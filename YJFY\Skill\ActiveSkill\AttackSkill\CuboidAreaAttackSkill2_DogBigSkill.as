package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class CuboidAreaAttackSkill2_DogBigSkill extends CuboidAreaAttackSkill2
   {
      
      protected var m_releaseSkillFrameLabel:String;
      
      protected var m_attackReachFrameLabel:String;
      
      protected var m_skillShowEndFrameLabel:String;
      
      protected var m_skillEndFrameLabel:String;
      
      protected var m_disappearBodyShowDefId:String;
      
      protected var m_appearBodyShowDefId:String;
      
      protected var m_frontSkillShowDefId:String;
      
      protected var m_groundSkillShowDefId:String;
      
      protected var m_frontSkillShowPlay:AnimationShowPlayLogicShell;
      
      protected var m_frontSkillShowPlayListener:AnimationPlayFrameLabelListener;
      
      protected var m_groundSkillShowPlay:AnimationShowPlayLogicShell;
      
      public function CuboidAreaAttackSkill2_DogBigSkill()
      {
         super();
         m_frontSkillShowPlay = new AnimationShowPlayLogicShell();
         m_groundSkillShowPlay = new AnimationShowPlayLogicShell();
         m_frontSkillShowPlayListener = new AnimationPlayFrameLabelListener();
         m_frontSkillShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_frontSkillShowPlay.addFrameLabelListener(m_frontSkillShowPlayListener);
         m_isOutWorldTime = true;
      }
      
      override public function clear() : void
      {
         m_releaseSkillFrameLabel = null;
         m_attackReachFrameLabel = null;
         m_skillShowEndFrameLabel = null;
         m_skillEndFrameLabel = null;
         m_disappearBodyShowDefId = null;
         m_appearBodyShowDefId = null;
         m_frontSkillShowDefId = null;
         m_groundSkillShowDefId = null;
         ClearUtil.clearObject(m_frontSkillShowPlay);
         m_frontSkillShowPlay = null;
         ClearUtil.clearObject(m_frontSkillShowPlayListener);
         m_frontSkillShowPlayListener = null;
         ClearUtil.clearObject(m_groundSkillShowPlay);
         m_groundSkillShowPlay = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_releaseSkillFrameLabel = String(param1.@releaseSkillFrameLabel);
         m_attackReachFrameLabel = String(param1.@attackReachFrameLabel);
         m_skillShowEndFrameLabel = String(param1.@skillShowEndFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_disappearBodyShowDefId = String(param1.@disappearBodyShowDefId);
         m_appearBodyShowDefId = String(param1.@appearBodyShowDefId);
         m_frontSkillShowDefId = String(param1.@frontSkillShowDefId);
         m_groundSkillShowDefId = String(param1.@groundSkillShowDefId);
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_disappearBodyShowDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      protected function releaseSkill3() : void
      {
         m_world.stopWorldTime();
         if(m_frontSkillShowPlay.getShow() == null)
         {
            m_frontSkillShowPlay.setShow(m_owner.getAnimationByDefId(m_frontSkillShowDefId),true);
         }
         (m_frontSkillShowPlay.getShow() as DisplayObject).x = m_world.getCamera().getScreenX();
         (m_frontSkillShowPlay.getShow() as DisplayObject).y = m_world.getCamera().getScreenY();
         m_frontSkillShowPlay.gotoAndPlay("1");
         m_world.addAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
         if(m_groundSkillShowDefId)
         {
            if(m_groundSkillShowPlay.getShow() == null)
            {
               m_groundSkillShowPlay.setShow(m_owner.getAnimationByDefId(m_groundSkillShowDefId),true);
            }
            (m_groundSkillShowPlay.getShow() as DisplayObject).x = m_world.getCamera().getScreenX();
            (m_groundSkillShowPlay.getShow() as DisplayObject).y = m_world.getCamera().getScreenY();
            m_groundSkillShowPlay.gotoAndPlay("1");
            m_world.addAnimationOfOutWorldTimeInGround(m_groundSkillShowPlay);
         }
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_releaseSkillFrameLabel:
               releaseSkill3();
               break;
            case m_attackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_skillShowEndFrameLabel:
               m_world.continueWorldTime();
               removeShows();
               m_owner.changeAnimationShow(m_appearBodyShowDefId);
               m_owner.currentAnimationGotoAndPlay("1");
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
      
      private function removeShows() : void
      {
         if(m_world.checkIn(m_frontSkillShowPlay))
         {
            m_world.removeAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
         }
         if(m_world.checkIn(m_groundSkillShowPlay))
         {
            m_world.removeAnimationOfOutWorldTimeInGround(m_groundSkillShowPlay);
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         removeShows();
      }
   }
}

