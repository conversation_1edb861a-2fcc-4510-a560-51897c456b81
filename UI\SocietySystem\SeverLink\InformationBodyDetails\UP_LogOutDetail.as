package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_LogOutDetail extends InformationBodyDetail
   {
      
      private var m_uid:Number;
      
      private var m_idx:int;
      
      public function UP_LogOutDetail()
      {
         super();
         m_informationBodyId = 3002;
      }
      
      public function initData(param1:Number, param2:int) : void
      {
         m_uid = param1;
         m_idx = param2;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         return _loc1_;
      }
   }
}

