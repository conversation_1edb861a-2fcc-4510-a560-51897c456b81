package UI.AutomaticPetPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.EntityShowContainer;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class AutomaticPetInforPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_petNameText:TextField;
      
      private var m_expBar:CMSXChangeBarLogicShell;
      
      private var m_hpBar:CMSXChangeBarLogicShell;
      
      private var m_mpBar:CMSXChangeBarLogicShell;
      
      private var m_levelNumShow:MultiPlaceNumLogicShell;
      
      private var m_scoreShow:MultiPlaceNumLogicShell2;
      
      private var m_pingJieShow:MovieClipPlayLogicShell;
      
      private var m_otherInforBtn:ButtonLogicShell2;
      
      private var m_showAnimationContianer:EntityShowContainer;
      
      private var m_automaticPetOtherInforPanel:AutomaticPetOtherInforPanel;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_showContainer:MovieClip;
      
      public function AutomaticPetInforPanel()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         m_petNameText = null;
         ClearUtil.clearObject(m_expBar);
         m_expBar = null;
         ClearUtil.clearObject(m_hpBar);
         m_hpBar = null;
         ClearUtil.clearObject(m_mpBar);
         m_mpBar = null;
         ClearUtil.clearObject(m_levelNumShow);
         m_levelNumShow = null;
         ClearUtil.clearObject(m_scoreShow);
         m_scoreShow = null;
         ClearUtil.clearObject(m_pingJieShow);
         m_pingJieShow = null;
         ClearUtil.clearObject(m_otherInforBtn);
         m_otherInforBtn = null;
         ClearUtil.clearObject(m_showAnimationContianer);
         m_showAnimationContianer = null;
         ClearUtil.clearObject(m_automaticPetOtherInforPanel);
         m_automaticPetOtherInforPanel = null;
         m_automaticPetVO = null;
         m_showContainer = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("AutoPetInforPanel") as MovieClip;
         addChild(m_show);
         m_showMC.setShow(m_show);
         initShow2();
      }
      
      public function setAutomaticPetVO(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         if(m_automaticPetVO)
         {
            m_automaticPetVO.resetSkinShow();
         }
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO)
         {
            initHavePetFrame();
            m_petNameText.text = m_automaticPetVO.getName();
            m_expBar.change(m_automaticPetVO.getCurrentExp() / m_automaticPetVO.getUpLevelTotalExp());
            m_expBar.setDataShow(m_automaticPetVO.getCurrentExp() + "/" + m_automaticPetVO.getUpLevelTotalExp());
            m_hpBar.change(m_automaticPetVO.getCurrentHp() / m_automaticPetVO.getTotalHp());
            m_hpBar.setDataShow(m_automaticPetVO.getCurrentHp() + "/" + m_automaticPetVO.getTotalHp());
            m_mpBar.change(m_automaticPetVO.getCurrentMp() / m_automaticPetVO.getTotalMp());
            m_mpBar.setDataShow(m_automaticPetVO.getCurrentMp() + "/" + m_automaticPetVO.getTotalMp());
            m_levelNumShow.showNum(m_automaticPetVO.getLevel());
            m_scoreShow.showNum(m_automaticPetVO.getAutomaticPetScore());
            m_pingJieShow.gotoAndStop(m_automaticPetVO.getPingJieVO().getId());
            m_showAnimationContianer.refreshAutomaticPetShow(m_automaticPetVO);
            if(m_automaticPetOtherInforPanel)
            {
               m_automaticPetOtherInforPanel.setAutomaticPetVO(m_automaticPetVO);
            }
         }
         else
         {
            initNotPetFrame();
            closeAutomaticPetOtherInforPanel();
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_otherInforBtn === _loc2_)
         {
            openAutomaticPetOtherInforPanel();
         }
      }
      
      private function openAutomaticPetOtherInforPanel() : void
      {
         if(m_automaticPetOtherInforPanel == null)
         {
            m_automaticPetOtherInforPanel = new AutomaticPetOtherInforPanel();
            m_automaticPetOtherInforPanel.x = 420;
            m_automaticPetOtherInforPanel.init(this);
         }
         m_automaticPetOtherInforPanel.setAutomaticPetVO(m_automaticPetVO);
         m_automaticPetOtherInforPanel.x = this.x + 375;
         m_automaticPetOtherInforPanel.y = this.y + 20;
         if(parent is MyControlPanel)
         {
            (parent as MyControlPanel).myPackage.addDragTarget(m_automaticPetOtherInforPanel);
         }
         if(parent is MyControlPanel)
         {
            parent.addChildAt(m_automaticPetOtherInforPanel,parent.getChildIndex((parent as MyControlPanel).topLayer));
         }
         else
         {
            parent.addChildAt(m_automaticPetOtherInforPanel,parent.numChildren);
         }
      }
      
      public function closeAutomaticPetOtherInforPanel() : void
      {
         ClearUtil.clearObject(m_automaticPetOtherInforPanel);
         m_automaticPetOtherInforPanel = null;
      }
      
      private function initNotPetFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("2");
      }
      
      private function initHavePetFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("1");
         m_petNameText = m_show["petNameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_petNameText);
         m_expBar = new CMSXChangeBarLogicShell();
         m_expBar.setShow(m_show["expBar"]);
         m_hpBar = new CMSXChangeBarLogicShell();
         m_hpBar.setShow(m_show["hpBar"]);
         m_mpBar = new CMSXChangeBarLogicShell();
         m_mpBar.setShow(m_show["mpBar"]);
         m_levelNumShow = new MultiPlaceNumLogicShell();
         m_levelNumShow.setShow(m_show["levelNumShow"]);
         m_scoreShow = new MultiPlaceNumLogicShell2();
         m_pingJieShow = new MovieClipPlayLogicShell();
         m_scoreShow.setShow(m_show["scoreShow"]);
         m_pingJieShow.setShow(m_show["pingJieShow"]);
         m_otherInforBtn = new ButtonLogicShell2();
         m_otherInforBtn.setTipString("点击查看更多妖将信息");
         m_otherInforBtn.setShow(m_show["otherInforBtn"]);
         m_showContainer = m_show["showContainer"];
         m_showAnimationContianer = new EntityShowContainer();
         m_showAnimationContianer.init();
         m_showContainer.addChild(m_showAnimationContianer.getShow());
      }
      
      private function frameClear() : void
      {
         m_petNameText = null;
         ClearUtil.clearObject(m_expBar);
         m_expBar = null;
         ClearUtil.clearObject(m_hpBar);
         m_hpBar = null;
         ClearUtil.clearObject(m_mpBar);
         m_mpBar = null;
         ClearUtil.clearObject(m_levelNumShow);
         m_levelNumShow = null;
         ClearUtil.clearObject(m_scoreShow);
         m_scoreShow = null;
         ClearUtil.clearObject(m_pingJieShow);
         m_pingJieShow = null;
         ClearUtil.clearObject(m_otherInforBtn);
         m_otherInforBtn = null;
         m_showContainer = null;
         ClearUtil.clearObject(m_showAnimationContianer);
         m_showAnimationContianer = null;
      }
   }
}

