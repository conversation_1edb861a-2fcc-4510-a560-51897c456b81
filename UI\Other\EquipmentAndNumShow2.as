package UI.Other
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipmentAndNumShow2 extends EquipmentAndNumShow
   {
      
      private var m_slashText:TextField;
      
      private var m_currentNumText:TextField;
      
      public function EquipmentAndNumShow2()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_slashText = null;
         m_currentNumText = null;
      }
      
      override public function setShow(param1:MovieClip) : void
      {
         super.setShow(param1);
         m_slashText = m_show["slashText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_slashText);
         m_slashText.text = "";
         m_slashText.selectable = false;
         m_slashText.mouseEnabled = false;
         m_currentNumText = m_show["currentNumText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_currentNumText);
         m_currentNumText.selectable = false;
         m_currentNumText.mouseEnabled = false;
         m_currentNumText.text = "";
      }
      
      public function getCurrentNumText() : TextField
      {
         return m_currentNumText;
      }
      
      public function getSlashText() : TextField
      {
         return m_slashText;
      }
      
      public function changeCurrentNumTextColor(param1:uint) : void
      {
         var _loc2_:TextFormat = m_currentNumText.defaultTextFormat;
         _loc2_.color = param1;
         m_currentNumText.defaultTextFormat = _loc2_;
         m_currentNumText.text = m_currentNumText.text;
      }
   }
}

