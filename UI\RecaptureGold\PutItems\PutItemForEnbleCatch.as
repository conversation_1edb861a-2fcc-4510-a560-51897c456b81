package UI.RecaptureGold.PutItems
{
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.RecaptureGold.Hook.Hook;
   import UI.RecaptureGold.Parent.ICatchItem;
   import UI.RecaptureGold.Parent.ICatchTarget;
   import UI.RecaptureGold.Parent.IThief;
   import UI.RecaptureGold.Parent.PutItem;
   import UI.RecaptureGold.RecaptureGoldFunction;
   import UI.SoundManager.SoundManager;
   import flash.display.DisplayObject;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import flash.utils.getQualifiedClassName;
   
   public class PutItemForEnbleCatch extends PutItem
   {
      
      public function PutItemForEnbleCatch()
      {
         super();
      }
      
      public function catchFunForEnbleCatchItem(param1:XML, param2:Hook, param3:ICatchTarget, param4:SoundManager) : Number
      {
         var _loc5_:XML = param1.TargetData.item.(@classNameForPut == getQualifiedClassName(this))[0];
         var _loc11_:Vector.<Number> = MyFunction.getInstance().excreteStringToNumber(_loc5_.@weight);
         var _loc10_:Number = _loc11_[0] + Math.round(Math.random() * (_loc11_[1] - _loc11_[0]));
         var _loc8_:String = String(_loc5_.@classNameForCatch);
         var _loc7_:Class = MyFunction2.returnClassByClassName(_loc8_);
         param2.hookItem = new _loc7_() as ICatchItem;
         var _loc6_:Point = new Point(param3.x,param3.y);
         var _loc9_:String = String(_loc5_.@soundForCatch);
         var _loc12_:SoundTransform = RecaptureGoldFunction.getInstance().getSpatialSoundTransform(1,_loc6_,(param3 as DisplayObject).parent);
         param4.play(_loc9_,0,0,_loc12_);
         var _loc13_:IThief = param3.getIthief();
         if(_loc13_)
         {
            _loc13_.playAnimationAfterCatch(param1,(param3 as DisplayObject).parent,param3);
         }
         param3.clear();
         (param3 as DisplayObject).parent.removeChild(param3 as DisplayObject);
         return _loc10_;
      }
   }
}

