package UI.SignPanel
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.PointTicketBuyBox;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class BuQianBox extends PointTicketBuyBox
   {
      
      public var ticketText:TextField;
      
      public var dayTxt:TextField;
      
      private var _signXML:XML;
      
      private var _afterFun:Function;
      
      private var _afterFunParams:Array;
      
      private var _weiQianNum:int;
      
      public function BuQianBox(param1:XML, param2:int, param3:Function, param4:Array)
      {
         _signXML = param1;
         _afterFun = param3;
         _afterFunParams = param4;
         _weiQianNum = param2;
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         dayTxt = null;
         ticketText = null;
         _signXML = null;
         _afterFun = null;
         ClearUtil.nullArr(_afterFunParams,false,false,false);
         _afterFunParams = null;
      }
      
      override protected function init() : void
      {
         dayTxt.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.embedFonts = true;
         dayTxt.embedFonts = true;
         dayTxt.text = _weiQianNum.toString();
         var _loc1_:Vector.<int> = MyFunction.getInstance().excreteString(_signXML.BuQianTicket[0].@ticketPrices);
         ticketText.text = String(_loc1_[Math.min(_weiQianNum,_loc1_.length - 1)]);
         super.init();
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var prices:Vector.<int>;
         var ticketIds:Vector.<String>;
         var price:int;
         var ticketId:String;
         var dataObj:Object;
         var afterFun:Function;
         var afterFunParams:Array;
         var e:UIBtnEvent = param1;
         var myParent:SignPanel = parent as SignPanel;
         var oldNum:int = SignData.getInstance().currentSignNum;
         SignData.getInstance().currentSignNum = 1;
         if(e.target == _sureBtn)
         {
            prices = MyFunction.getInstance().excreteString(_signXML.BuQianTicket[0].@ticketPrices);
            ticketIds = MyFunction.getInstance().excreteStringToString(_signXML.BuQianTicket[0].@ticketIds);
            price = prices[Math.min(_weiQianNum,prices.length - 1)];
            ticketId = ticketIds[Math.min(_weiQianNum,ticketIds.length - 1)];
            dataObj = {};
            dataObj["propId"] = ticketId;
            dataObj["count"] = 1;
            dataObj["price"] = price;
            dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
            dataObj["tag"] = "补签";
            afterFun = _afterFun;
            afterFunParams = _afterFunParams;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
            {
               if(param1["propId"] != ticketId)
               {
                  myParent.showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               if(oldNum < SignData.getInstance().maxSignNum)
               {
                  SignData.getInstance().currentSignNum = Math.min(oldNum + _weiQianNum + 1,SignData.getInstance().maxSignNum);
               }
               else
               {
                  SignData.getInstance().currentSignNum = Math.min(_weiQianNum + 1,SignData.getInstance().maxSignNum);
               }
               if(Boolean(afterFun))
               {
                  afterFun.apply(null,afterFunParams);
               }
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },myParent.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         else if(Boolean(_afterFun))
         {
            _afterFun.apply(null,_afterFunParams);
         }
         super.clickBtn(e);
      }
   }
}

