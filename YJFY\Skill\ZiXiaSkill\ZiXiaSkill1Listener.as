package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Entity.IEntity;
   import YJFY.Skill.DragonSkills.MoYuYingBulletEntity;
   
   public class ZiXiaSkill1Listener implements IZiXiaSkill1Listener
   {
      
      public var pushEntityFun:Function;
      
      public function ZiXiaSkill1Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         pushEntityFun = null;
      }
      
      public function pushEntity(param1:Skill_ZiXiaSkill1, param2:MoYuYingBulletEntity, param3:IEntity) : void
      {
         if(Boolean(pushEntityFun))
         {
            pushEntityFun(param1,param2,param3);
         }
      }
   }
}

