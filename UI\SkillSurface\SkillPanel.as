package UI.SkillSurface
{
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.SwitchBtn.ActiveSkillBtn;
   import UI.Button.SwitchBtn.PassiveSkillBtn;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Skills.SkillVO;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class SkillPanel extends MySprite
   {
      
      public var activeSkillBtn:ActiveSkillBtn;
      
      public var passiveSkillBtn:PassiveSkillBtn;
      
      public var moneyText:TextField;
      
      private var _pageBtnGroup:PageBtnGroup;
      
      private var _passiveSkillVOs:Vector.<SkillVO>;
      
      private var _activeSkillVOs:Vector.<SkillVO>;
      
      private var _activeAndPassive:Boolean;
      
      private var _player:Player;
      
      private const SPACING:Number = 75;
      
      private const BASE_NUM:Number = 7;
      
      public function SkillPanel()
      {
         super();
         initSkillPanel();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function refreshSkills(param1:Player) : void
      {
         _player = param1;
         _activeSkillVOs = _player.playerVO.skillVOs;
         _passiveSkillVOs = null;
         setSkillPanelPage(_pageBtnGroup.pageNum);
         arrangeSkills((_pageBtnGroup.pageNum - 1) * 4);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         if(activeSkillBtn)
         {
            activeSkillBtn.clear();
         }
         activeSkillBtn = null;
         if(passiveSkillBtn)
         {
            passiveSkillBtn.clear();
         }
         passiveSkillBtn = null;
         if(moneyText)
         {
            moneyText.removeEventListener("rollOver",rollOver,false);
            moneyText.removeEventListener("rollOut",rollOut,false);
         }
         moneyText = null;
         if(_pageBtnGroup)
         {
            _pageBtnGroup.clear();
         }
         _pageBtnGroup = null;
         _passiveSkillVOs = null;
         _activeSkillVOs = null;
         _player = null;
      }
      
      protected function initSkillPanel() : void
      {
         _passiveSkillVOs = new Vector.<SkillVO>();
         _activeSkillVOs = new Vector.<SkillVO>();
         _pageBtnGroup = new PageBtnGroup();
         _pageBtnGroup.x = 650;
         _pageBtnGroup.y = 425;
         addChild(_pageBtnGroup);
         activeSkillBtn.init(false);
         passiveSkillBtn.init(true);
         activeSkillBtn.visible = false;
         passiveSkillBtn.visible = false;
         _activeAndPassive = true;
         setSkillPanelPage(1);
         moneyText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16776960);
         moneyText.embedFonts = true;
         if(moneyText)
         {
            moneyText.addEventListener("rollOver",rollOver,false,0,true);
            moneyText.addEventListener("rollOut",rollOut,false,0,true);
         }
      }
      
      protected function setSkillPanelPage(param1:int) : void
      {
         if(!_activeAndPassive)
         {
            if(!_passiveSkillVOs.length)
            {
               _pageBtnGroup.initPageNumber(1,1);
               return;
            }
            if(_passiveSkillVOs.length % 4)
            {
               _pageBtnGroup.initPageNumber(param1,int(_passiveSkillVOs.length / 4) + 1);
            }
            else
            {
               _pageBtnGroup.initPageNumber(param1,_passiveSkillVOs.length / 4);
            }
         }
         else
         {
            if(!_activeSkillVOs.length)
            {
               _pageBtnGroup.initPageNumber(1,1);
               return;
            }
            if(_activeSkillVOs.length % 4)
            {
               _pageBtnGroup.initPageNumber(param1,int(_activeSkillVOs.length / 4) + 1);
            }
            else
            {
               _pageBtnGroup.initPageNumber(param1,_activeSkillVOs.length / 4);
            }
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("switchToActiveSkill",switchToActiveSkill,true,0,true);
         addEventListener("switchToPassiveSkill",switchToPassiveSkill,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("switchToActiveSkill",switchToActiveSkill,true);
         removeEventListener("switchToPassiveSkill",switchToPassiveSkill,true);
      }
      
      private function rollOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":{"target":"moneyText"}}));
      }
      
      private function rollOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      protected function pageUp(param1:UIBtnEvent) : void
      {
         arrangeSkills((_pageBtnGroup.pageNum - 1) * 4);
      }
      
      protected function pageDown(param1:UIBtnEvent) : void
      {
         arrangeSkills((_pageBtnGroup.pageNum - 1) * 4);
      }
      
      protected function switchToActiveSkill(param1:UIBtnEvent) : void
      {
         passiveSkillBtn.gotoTwoFrame();
         _activeAndPassive = true;
         setSkillPanelPage(1);
         arrangeSkills((_pageBtnGroup.pageNum - 1) * 4);
      }
      
      protected function switchToPassiveSkill(param1:UIBtnEvent) : void
      {
         activeSkillBtn.gotoTwoFrame();
         _activeAndPassive = false;
         setSkillPanelPage(1);
         arrangeSkills((_pageBtnGroup.pageNum - 1) * 4);
      }
      
      protected function arrangeSkills(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc7_:DisplayObject = null;
         var _loc8_:* = 0;
         var _loc2_:SkillColumn = null;
         var _loc6_:* = 0;
         var _loc5_:SkillColumn = null;
         var _loc3_:int = param1 + 4;
         while(numChildren > 7)
         {
            _loc7_ = getChildAt(7);
            removeChild(_loc7_);
            if(_loc7_ is SkillColumn)
            {
               (_loc7_ as SkillColumn).clear();
            }
         }
         if(!_activeAndPassive)
         {
            _loc4_ = int(_passiveSkillVOs.length);
            _loc8_ = param1;
            while(_loc8_ < _loc3_ && _loc8_ < _loc4_)
            {
               if(_passiveSkillVOs[_loc8_] != null)
               {
                  _loc2_ = new SkillColumn(_passiveSkillVOs[_loc8_],_player,_loc8_);
                  _loc2_.x = 10.5;
                  _loc2_.y = 116 + 75 * (_loc8_ - param1);
                  addChild(_loc2_);
                  _loc2_ = null;
               }
               _loc8_++;
            }
         }
         else
         {
            _loc4_ = int(_activeSkillVOs.length);
            _loc6_ = param1;
            while(_loc6_ < _loc3_ && _loc6_ < _loc4_)
            {
               if(_activeSkillVOs[_loc6_] != null)
               {
                  _loc5_ = new SkillColumn(_activeSkillVOs[_loc6_],_player,_loc6_);
                  _loc5_.x = 10.5;
                  _loc5_.y = 116 + 75 * (_loc6_ - param1);
                  addChild(_loc5_);
                  _loc5_ = null;
               }
               _loc6_++;
            }
         }
      }
   }
}

