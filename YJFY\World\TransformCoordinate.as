package YJFY.World
{
   public class TransformCoordinate
   {
      
      private var m_transformAngleByYOfScreen:Number;
      
      private var sinTheta:Number;
      
      private var cosTheta:Number;
      
      private var sinAlpha:Number;
      
      private var cosAlpha:Number;
      
      private var m_zChange:Number;
      
      public function TransformCoordinate()
      {
         super();
         m_transformAngleByYOfScreen = 2.0943951023931953;
         sinTheta = Math.sin(m_transformAngleByYOfScreen);
         cosTheta = Math.cos(m_transformAngleByYOfScreen);
         sinAlpha = 0;
         cosAlpha = 1;
      }
      
      public function setZChange(param1:Number) : void
      {
         m_zChange = param1;
      }
      
      public function worldCoordToScreenCoord(param1:Number, param2:Number, param3:Number) : Coordinate
      {
         var _loc5_:* = param1;
         var _loc4_:Number = cosTheta * param2 - sinTheta * param3;
         var _loc6_:Number = sinTheta * param2 + cosTheta * param3 + m_zChange;
         return new Coordinate(_loc5_,_loc4_,_loc6_);
      }
      
      public function worldCoordToScreenCoord2(param1:Coordinate, param2:Coordinate) : void
      {
         var _loc4_:Number = param1.getX();
         var _loc3_:Number = cosTheta * param1.getY() - sinTheta * param1.getZ();
         var _loc5_:Number = sinTheta * param1.getY() + cosTheta * param1.getZ() + m_zChange;
         param2.setTo(_loc4_,_loc3_,_loc5_);
      }
      
      public function screenCoordToWorldCoord(param1:Number, param2:Number, param3:Number) : Coordinate
      {
         return new Coordinate(param1,cosTheta * param2 + sinTheta * param3 - sinTheta * m_zChange,cosTheta * param3 - sinTheta * param2 - cosTheta * m_zChange);
      }
      
      public function screenCoordToWorldCoord2(param1:Coordinate, param2:Coordinate) : void
      {
         param2.setTo(param1.getX(),cosTheta * param1.getY() + sinTheta * param1.getZ() - sinTheta * m_zChange,cosTheta * param1.getZ() - sinTheta * param1.getY() - cosTheta * m_zChange);
      }
      
      public function screenToWorld(param1:Number, param2:Number, param3:Number = 0) : Coordinate
      {
         if(param3)
         {
            throw new Error("该算法目前只支持worldZ = 0");
         }
         var _loc4_:Number = param2 / cosTheta;
         var _loc5_:* = param1;
         return new Coordinate(_loc5_,_loc4_,param3);
      }
      
      public function screenToWorld2(param1:Number, param2:Number, param3:Number = 0) : Coordinate
      {
         return new Coordinate(param1,param3,(-param2 - param3 * cosTheta) / sinTheta);
      }
      
      public function screenToWorld22(param1:Number, param2:Number, param3:Coordinate, param4:Number = 0) : void
      {
         param3.setTo(param1,param4,(-param2 - param4 * cosTheta) / sinTheta);
      }
      
      public function getMaxScreenY(param1:Number, param2:Number) : Number
      {
         return cosTheta * param1 - sinTheta * param2;
      }
      
      public function getMinScreenY(param1:Number, param2:Number) : Number
      {
         return cosTheta * param1 - sinTheta * param2;
      }
      
      public function getMaxScreenZ(param1:Number, param2:Number) : Number
      {
         return sinTheta * param1 + cosTheta * param2 + m_zChange;
      }
      
      public function getMinScreenZ(param1:Number, param2:Number) : Number
      {
         return sinTheta * param1 + cosTheta * param2 + m_zChange;
      }
   }
}

