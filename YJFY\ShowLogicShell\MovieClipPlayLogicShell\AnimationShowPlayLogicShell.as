package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class AnimationShowPlayLogicShell
   {
      
      protected var m_show:Object;
      
      private var m_isStop:Boolean;
      
      private var m_isExRender:Boolean;
      
      private var m_playLabel:String;
      
      private var _startPlayListeners:Vector.<IStartPlayListener>;
      
      private var _nextStopListeners:Vector.<INextStopListener>;
      
      private var _frameLabelListeners:Vector.<IFrameLabelListener>;
      
      private var _reachMaxFrameListeners:Vector.<IReachMaxFrameListener>;
      
      private var _enterFrameListeners:Vector.<IEnterFrameListener>;
      
      public var extra:Object;
      
      private var m_testTraceStr:String;
      
      public function AnimationShowPlayLogicShell()
      {
         super();
      }
      
      public function setShow(param1:Object, param2:Boolean = false) : void
      {
         if(m_isExRender == false && Boolean(m_show))
         {
            m_show.removeEventListener("enterFrame",render,false);
         }
         m_isExRender = param2;
         this.m_show = param1;
         this.m_show.stop();
         m_isStop = true;
         if(m_isExRender == false)
         {
            m_show.addEventListener("enterFrame",render,false,0,true);
         }
      }
      
      public function getShow() : Object
      {
         return m_show;
      }
      
      public function getDisplayShow() : DisplayObject
      {
         if(m_show == null)
         {
            return null;
         }
         if(!(m_show is DisplayObject))
         {
            throw new Error("show is not displayObject");
         }
         return m_show as DisplayObject;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            if(m_isExRender == false)
            {
               m_show.removeEventListener("enterFrame",render,false);
            }
         }
         ClearUtil.nullArr(_startPlayListeners,false,false,false);
         _startPlayListeners = null;
         ClearUtil.nullArr(_frameLabelListeners,false,false,false);
         _frameLabelListeners = null;
         ClearUtil.nullArr(_nextStopListeners,false,false,false);
         _nextStopListeners = null;
         ClearUtil.nullArr(_reachMaxFrameListeners,false,false,false);
         _reachMaxFrameListeners = null;
         ClearUtil.nullArr(_enterFrameListeners,false,false,false);
         _enterFrameListeners = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         extra = null;
      }
      
      public function addEnterFrameListener(param1:IEnterFrameListener) : void
      {
         if(_enterFrameListeners == null)
         {
            _enterFrameListeners = new Vector.<IEnterFrameListener>();
         }
         ListenerUtil.addListener(_enterFrameListeners,param1);
      }
      
      public function removeEnterFrameListener(param1:IEnterFrameListener) : void
      {
         if(_enterFrameListeners == null)
         {
            return;
         }
         ListenerUtil.removeListener(_enterFrameListeners,param1);
      }
      
      public function addNextStopListener(param1:INextStopListener) : void
      {
         if(_nextStopListeners == null)
         {
            _nextStopListeners = new Vector.<INextStopListener>();
         }
         _nextStopListeners.push(param1);
      }
      
      public function removeNextStopListener(param1:INextStopListener) : void
      {
         if(_nextStopListeners == null)
         {
            return;
         }
         var _loc2_:int = int(_nextStopListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            _nextStopListeners.splice(_loc2_,1);
            _loc2_ = int(_nextStopListeners.indexOf(param1));
         }
      }
      
      public function addFrameLabelListener(param1:IFrameLabelListener) : void
      {
         if(_frameLabelListeners == null)
         {
            _frameLabelListeners = new Vector.<IFrameLabelListener>();
         }
         _frameLabelListeners.push(param1);
      }
      
      public function removeFrameLabelListener(param1:IFrameLabelListener) : void
      {
         if(_frameLabelListeners == null)
         {
            return;
         }
         var _loc2_:int = int(_frameLabelListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            _frameLabelListeners.splice(_loc2_,1);
            _loc2_ = int(_frameLabelListeners.indexOf(param1));
         }
      }
      
      public function addReachMaxFrameListener(param1:IReachMaxFrameListener) : void
      {
         if(_reachMaxFrameListeners == null)
         {
            _reachMaxFrameListeners = new Vector.<IReachMaxFrameListener>();
         }
         _reachMaxFrameListeners.push(param1);
      }
      
      public function removeReachMaxFrameListener(param1:IReachMaxFrameListener) : void
      {
         if(_reachMaxFrameListeners == null)
         {
            return;
         }
         var _loc2_:int = int(_reachMaxFrameListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            _reachMaxFrameListeners.splice(_loc2_,1);
            _loc2_ = int(_reachMaxFrameListeners.indexOf(param1));
         }
      }
      
      public function addStartPlayListener(param1:IStartPlayListener) : void
      {
         if(_startPlayListeners == null)
         {
            _startPlayListeners = new Vector.<IStartPlayListener>();
         }
         _startPlayListeners.push(param1);
      }
      
      public function removeStartPlayListener(param1:IStartPlayListener) : void
      {
         if(_startPlayListeners == null)
         {
            return;
         }
         var _loc2_:int = int(_startPlayListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            _startPlayListeners.splice(_loc2_,1);
            _loc2_ = int(_startPlayListeners.indexOf(param1));
         }
      }
      
      public function render(param1:Event = null) : void
      {
         var _loc4_:Array = null;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(m_isStop)
         {
            return;
         }
         if(m_show == null)
         {
            return;
         }
         if(m_show.currentFrame == m_show.totalFrames)
         {
            reachMaxFrame();
            gotoAndPlay(m_playLabel);
         }
         else
         {
            m_show.nextFrame();
         }
         if(m_show.currentFrameLabel)
         {
            _loc4_ = m_show.currentFrameLabel.split("^");
            _loc3_ = _loc4_ ? _loc4_.length : 0;
            _loc2_ = 0;
            while(_loc2_ < _loc3_)
            {
               if(_loc4_[_loc2_] == "stop")
               {
                  m_isStop = true;
                  nextStop();
                  break;
               }
               _loc2_++;
            }
            if(m_show)
            {
               reachFrameLabel(m_show.currentFrameLabel);
            }
         }
         enterFrame();
      }
      
      protected function startPlay() : void
      {
         var _loc3_:int = 0;
         if(_startPlayListeners == null)
         {
            return;
         }
         var _loc1_:Vector.<IStartPlayListener> = _startPlayListeners.slice(0);
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].startPlay();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc1_,false,false,false);
      }
      
      protected function reachFrameLabel(param1:String) : void
      {
         var _loc4_:int = 0;
         if(_frameLabelListeners == null)
         {
            return;
         }
         if(Boolean(param1) == false)
         {
            return;
         }
         var _loc2_:Vector.<IFrameLabelListener> = _frameLabelListeners.slice(0);
         var _loc3_:int = _loc2_ ? _loc2_.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].reachFrameLabel(param1);
               _loc2_[_loc4_].reachFrameLabel2(this,param1);
            }
            _loc4_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      protected function nextStop() : void
      {
         var _loc3_:int = 0;
         if(_nextStopListeners == null)
         {
            return;
         }
         var _loc2_:Vector.<INextStopListener> = _nextStopListeners.slice(0);
         var _loc1_:int = _loc2_ ? _loc2_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_loc2_[_loc3_])
            {
               _loc2_[_loc3_].stop();
               _loc2_[_loc3_].stop2(this);
            }
            _loc3_++;
         }
         ClearUtil.nullArr(_loc2_,false,false,false);
      }
      
      protected function reachMaxFrame() : void
      {
         var _loc3_:int = 0;
         if(_reachMaxFrameListeners == null)
         {
            return;
         }
         if(m_show == null)
         {
            return;
         }
         if(m_show.currentFrame != m_show.totalFrames)
         {
            return;
         }
         var _loc1_:Vector.<IReachMaxFrameListener> = _reachMaxFrameListeners.slice(0);
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].reachMaxFrame(this);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         if(_loc1_)
         {
            _loc1_.length = 0;
         }
         _loc1_ = null;
      }
      
      protected function enterFrame() : void
      {
         var _loc3_:int = 0;
         if(_enterFrameListeners == null)
         {
            return;
         }
         var _loc1_:Vector.<IEnterFrameListener> = _enterFrameListeners.slice(0);
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].enterFrame(this,m_show.currentFrame);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         if(_loc1_)
         {
            _loc1_.length = 0;
         }
         _loc1_ = null;
      }
      
      public function gotoAndPlay(param1:String) : void
      {
         if(!m_show)
         {
            return;
         }
         m_playLabel = param1;
         m_isStop = false;
         var _loc2_:uint = uint(m_show.currentFrame);
         m_show.gotoAndStop(param1);
         reachFrameLabel(m_show.currentFrameLabel);
         reachMaxFrame();
         startPlay();
         jugeIsStop();
         if(_loc2_ != m_show.currentFrame)
         {
            enterFrame();
         }
      }
      
      public function gotoAndStop(param1:String) : void
      {
         var _loc2_:uint = uint(m_show.currentFrame);
         try
         {
            m_show.gotoAndStop(param1);
            reachFrameLabel(param1);
            reachMaxFrame();
         }
         catch(e:Error)
         {
            m_show.gotoAndStop(1);
            if(m_show.currentFrameLabel)
            {
               reachFrameLabel(m_show.currentFrameLabel);
            }
            reachMaxFrame();
         }
         m_isStop = true;
         if(_loc2_ != m_show.currentFrame)
         {
            enterFrame();
         }
      }
      
      public function stop() : void
      {
         m_isStop = true;
      }
      
      public function play() : void
      {
         m_isStop = false;
      }
      
      public function getCurrentFrame() : int
      {
         return m_show.currentFrame;
      }
      
      public function getTotalFrame() : int
      {
         return m_show.totalFrames;
      }
      
      public function setTestTraceStr(param1:String) : void
      {
         m_testTraceStr = param1;
      }
      
      private function jugeIsStop() : void
      {
         var _loc3_:Array = null;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(m_show.currentFrameLabel)
         {
            _loc3_ = m_show.currentFrameLabel.split("^");
            _loc2_ = _loc3_ ? _loc3_.length : 0;
            _loc1_ = 0;
            while(_loc1_ < _loc2_)
            {
               if(_loc3_[_loc1_] == "stop")
               {
                  m_isStop = true;
                  nextStop();
                  break;
               }
               _loc1_++;
            }
         }
      }
   }
}

