package YJFY.Lottery
{
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class LotteryPanel extends MySprite
   {
      
      private const m_const_boxNum:uint = 12;
      
      private const m_const_boxShineInterval:uint = 200;
      
      private const m_const_packageX:Number = 488;
      
      private const m_const_packageY:Number = 38;
      
      private const m_const_flySpeed:Number = 0.5;
      
      private var m_show:MovieClip;
      
      private var m_boxes:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_startLotteryBtn:ButtonLogicShell;
      
      private var m_returnCityBtn:ButtonLogicShell;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_equipmentX:Number = 0;
      
      private var m_equipmentY:Number = 0;
      
      private var m_speedX:Number;
      
      private var m_speedY:Number;
      
      private var m_isFlyOfEquipment:Boolean;
      
      private var m_flyEquipment:Equipment;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_lotteryData:LotteryData;
      
      private var m_lottery:Lottery;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_isStartLottery:Boolean;
      
      private var m_lotteryTargetBox:AnimationShowPlayLogicShell;
      
      private var m_remainNumForShine:uint;
      
      private var m_currentShineBox:AnimationShowPlayLogicShell;
      
      private var m_lastShineBox:AnimationShowPlayLogicShell;
      
      private var m_nextChangeShineTime:Number;
      
      private var m_isEndLottery:Boolean;
      
      private var m_isReady:Boolean;
      
      public function LotteryPanel()
      {
         super();
         m_boxes = new Vector.<AnimationShowPlayLogicShell>();
         m_startLotteryBtn = new ButtonLogicShell();
         m_returnCityBtn = new ButtonLogicShell();
         m_equipmentVOs = new Vector.<EquipmentVO>();
         m_equipments = new Vector.<Equipment>();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_boxes);
         m_boxes = null;
         ClearUtil.clearObject(m_startLotteryBtn);
         m_startLotteryBtn = null;
         ClearUtil.clearObject(m_returnCityBtn);
         m_returnCityBtn = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_flyEquipment = null;
         m_myLoader = null;
         m_lotteryData = null;
         m_lottery = null;
         m_enterFrameTime = null;
         m_lotteryTargetBox = null;
         m_currentShineBox = null;
         m_lastShineBox = null;
         super.clear();
      }
      
      public function init(param1:YJFYLoader, param2:LotteryData, param3:Lottery, param4:EnterFrameTime) : void
      {
         m_myLoader = param1;
         m_lotteryData = param2;
         m_lottery = param3;
         m_enterFrameTime = param4;
         m_myLoader.getClass("NewGameFolder/BossMode/UISource.swf","LotteryUI",getShowSuccess,getFail);
         if(m_myLoader.getProgressShow() is LoadUI2)
         {
            (m_myLoader.getProgressShow() as LoadUI2).tranToTransparentcy();
         }
         m_myLoader.load();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_isFlyOfEquipment)
         {
            renderFly(param1);
         }
         if(m_isStartLottery)
         {
            renderLotteryShine(param1);
         }
      }
      
      public function startLottery(param1:LotteryDataOne) : void
      {
         var _loc3_:int = 0;
         if(m_isReady == false)
         {
            throw new Error("还未准备好");
         }
         var _loc2_:int = int(m_boxes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_boxes[_loc3_].extra == param1)
            {
               m_lotteryTargetBox = m_boxes[_loc3_];
               getFristBoxForShine(_loc3_);
               m_lastShineBox = null;
               m_isStartLottery = true;
               swapShineBox();
               m_nextChangeShineTime = m_enterFrameTime.getGameTimeForThisInit() + 200;
            }
            _loc3_++;
         }
      }
      
      public function getIsReady() : Boolean
      {
         return m_isReady;
      }
      
      public function getStartLotteryBtn() : ButtonLogicShell
      {
         return m_startLotteryBtn;
      }
      
      public function getReturnCityBtn() : ButtonLogicShell
      {
         return m_returnCityBtn;
      }
      
      private function renderLotteryShine(param1:EnterFrameTime) : void
      {
         if(m_isStartLottery == false || m_remainNumForShine <= 0)
         {
            return;
         }
         if(m_nextChangeShineTime > param1.getGameTimeForThisInit())
         {
            return;
         }
         m_lastShineBox = m_currentShineBox;
         var _loc2_:int = int(m_boxes.indexOf(m_currentShineBox));
         if(_loc2_ == -1)
         {
            throw new Error("出错了");
         }
         if(_loc2_ < m_boxes.length - 1)
         {
            m_currentShineBox = m_boxes[_loc2_ + 1];
         }
         else
         {
            m_currentShineBox = m_boxes[0];
         }
         swapShineBox();
         --m_remainNumForShine;
         m_nextChangeShineTime = m_enterFrameTime.getGameTimeForThisInit() + 200;
         if(m_remainNumForShine <= 0)
         {
            endLotteryShine();
         }
      }
      
      private function endLotteryShine() : void
      {
         m_isStartLottery = false;
         m_lastShineBox = m_currentShineBox;
         m_currentShineBox = m_lotteryTargetBox;
         swapShineBox();
         var _loc1_:int = int(m_boxes.indexOf(m_lotteryTargetBox));
         if(_loc1_ == -1)
         {
            throw new Error("出错了");
         }
         flyEquipment(m_equipments[_loc1_]);
      }
      
      private function getFristBoxForShine(param1:int) : void
      {
         var _loc2_:int = Math.random() * 12;
         m_remainNumForShine = 12 + _loc2_;
         m_currentShineBox = m_boxes[(param1 - _loc2_ + 12) % 12];
      }
      
      private function swapShineBox() : void
      {
         if(m_lastShineBox)
         {
            m_lastShineBox.gotoAndStop("1");
         }
         if(m_currentShineBox)
         {
            m_currentShineBox.gotoAndStop("2");
         }
      }
      
      private function flyEquipment(param1:Equipment) : void
      {
         var _loc2_:Point = param1.localToGlobal(new Point(param1.x,param1.y));
         m_equipmentX = _loc2_.x;
         m_equipmentY = _loc2_.y;
         m_flyEquipment = param1;
         m_show.addChild(param1);
         var _loc3_:Number = Math.sqrt(Math.pow(m_equipmentX - 488,2) + Math.pow(m_equipmentY - 38,2));
         m_speedX = 0.5 * (488 - m_equipmentX) / _loc3_;
         m_speedY = 0.5 * (38 - m_equipmentY) / _loc3_;
         m_isFlyOfEquipment = true;
      }
      
      private function renderFly(param1:EnterFrameTime) : void
      {
         var _loc4_:Number = Math.abs(488 - m_equipmentX);
         var _loc3_:Number = Math.abs(38 - m_equipmentY);
         var _loc2_:Number = m_speedX * param1.getAddTimeOneFrame();
         var _loc5_:Number = m_speedY * param1.getAddTimeOneFrame();
         if(_loc4_ == 0 && _loc3_ == 0)
         {
            return;
         }
         if(_loc4_ < Math.abs(_loc2_) && _loc3_ < Math.abs(_loc5_))
         {
            m_equipmentX = 488;
            m_equipmentY = 38;
            m_flyEquipment.x = m_equipmentX;
            m_flyEquipment.y = m_equipmentY;
            flyEnd();
         }
         else
         {
            m_equipmentX += _loc2_;
            m_equipmentY += _loc5_;
            m_flyEquipment.x = m_equipmentX;
            m_flyEquipment.y = m_equipmentY;
         }
      }
      
      private function flyEnd() : void
      {
         if(m_flyEquipment.parent == m_show)
         {
            m_show.removeChild(m_flyEquipment);
         }
      }
      
      private function initShow() : void
      {
         var _loc1_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:DisplayObject = null;
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc3_:int = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = m_show.getChildAt(_loc5_);
            if(_loc4_.name.substr(0,"box".length) == "box")
            {
               _loc1_++;
            }
            _loc5_++;
         }
         if(_loc1_ != 12)
         {
            throw new Error("box num != 12");
         }
         ClearUtil.clearObject(m_boxes);
         m_boxes.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc2_ = new AnimationShowPlayLogicShell();
            _loc2_.setShow(m_show["box_" + (_loc5_ + 1)]);
            m_boxes.push(_loc2_);
            _loc5_++;
         }
         m_startLotteryBtn.setShow(m_show["startLotteryBtn"]);
         m_returnCityBtn.setShow(m_show["backToCityBtn_PassLevel"]);
         initShow2();
      }
      
      private function initShow2() : void
      {
         var _loc8_:int = 0;
         var _loc1_:EquipmentVO = null;
         var _loc5_:Equipment = null;
         var _loc4_:AnimationShowPlayLogicShell = null;
         var _loc6_:LotteryDataOne = null;
         var _loc2_:MovieClip = null;
         var _loc7_:TextField = null;
         ClearUtil.clearObject(m_equipmentVOs);
         m_equipmentVOs.length = 0;
         ClearUtil.clearObject(m_equipments);
         m_equipments.length = 0;
         var _loc3_:int = m_lotteryData.getLotteryDataOneNum();
         _loc8_ = 0;
         while(_loc8_ < _loc3_)
         {
            _loc6_ = m_lotteryData.getLotteryDataOneByIndex(_loc8_);
            _loc1_ = XMLSingle.getEquipmentVOByID(uint(_loc6_.getEquipmentId()),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
            m_equipmentVOs.push(_loc1_);
            _loc5_ = MyFunction2.sheatheEquipmentShell(_loc1_);
            _loc5_.addEventListener("rollOver",onOver,false,0,true);
            _loc5_.addEventListener("rollOut",onOut,false,0,true);
            m_equipments.push(_loc5_);
            _loc4_ = m_boxes[_loc8_];
            if(_loc4_ == null)
            {
               throw new Error("box == null");
            }
            _loc4_.extra = _loc6_;
            _loc2_ = _loc4_.getShow()["item"] as MovieClip;
            _loc2_.addChild(_loc5_);
            _loc7_ = _loc4_.getShow()["num"] as TextField;
            _loc7_.text = _loc6_.getEquipmentNum().toString();
            _loc8_++;
         }
         m_isReady = true;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_startLotteryBtn:
               if(m_isEndLottery == false)
               {
                  if(m_lottery.lottery())
                  {
                     m_isEndLottery = true;
                  }
               }
               break;
            case m_returnCityBtn:
               Part1.getInstance().continueGame();
               Part1.getInstance().returnCity();
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         m_show = new param1.resultClass();
         addChild(m_show);
         initShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_lottery.closeLotteryPanel();
         GamingUI.getInstance().showMessageTip("加载资源失败");
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

