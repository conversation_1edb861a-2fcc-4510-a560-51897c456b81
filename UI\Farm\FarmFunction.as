package UI.Farm
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Farm.Land.Land;
   import UI.Farm.Land.LandVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class FarmFunction
   {
      
      private static var _instance:FarmFunction = null;
      
      public function FarmFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : FarmFunction
      {
         if(!_instance)
         {
            _instance = new FarmFunction();
         }
         return _instance;
      }
      
      public function setLandStateByLand(param1:Land) : void
      {
         var _loc2_:XML = null;
         if(!param1 || !param1.landDrive || !param1.landDrive.landVO)
         {
            return;
         }
         if(!param1.landDrive.landVO.id)
         {
            param1.addLandUpPartShow(MyFunction2.returnShowByClassName("GrassInUndevelopedLand"));
            return;
         }
         param1.addLandPartShow(param1.landDrive.landVO.id,XMLSingle.getInstance().farmXML);
         switch(param1.landDrive.landVO.state)
         {
            case 0:
               param1.addLandUpPartShow(null);
               break;
            case 1:
            case 2:
            case 3:
               _loc2_ = XMLSingle.getInstance().equipmentXML.item.(@id == param1.landDrive.landVO.equipmentIDInLand)[0];
               param1.addLandUpPartShow(MyFunction2.returnShowByClassName(String(XMLSingle.getInstance().equipmentXML.item.(@id == param1.landDrive.landVO.equipmentIDInLand)[0].growData.(@state == param1.landDrive.landData.plantState)[0].@className)));
               break;
            case 4:
               param1.addLandPartShow(-1,null);
               param1.addLandUpPartShow(null);
               break;
            default:
               throw new Error("土地状态类型错误！");
         }
      }
      
      public function getHarvestEquipmentsAndSunValue(param1:int, param2:Land, param3:XML, param4:XML) : Array
      {
         var isSuccessPutInPackage:Boolean;
         var newEquipmentVOs:Vector.<EquipmentVO>;
         var num2:int;
         var id:int = param1;
         var land:Land = param2;
         var eqXML:XML = param3;
         var farmXML:XML = param4;
         var landXML:XML = farmXML.land.(@id == land.landDrive.landVO.id)[0];
         var impactValueToHarvest:Number = Number(landXML.@impactValueToHarvest);
         var xml:XML = eqXML.item.(@id == id)[0];
         var harvestMinNum:int = int(xml.@harvestMinNum);
         var harvestMaxNum:int = int(xml.@harvestMaxNum);
         var getSunMinNum:int = int(xml.@getSunMinNum);
         var getSunMaxNum:int = int(xml.@getSunMaxNum);
         var num:int = Math.round((harvestMinNum + Math.floor(Math.random() * (harvestMaxNum - harvestMinNum + 1))) * impactValueToHarvest);
         var newEquipmentVO:EquipmentVO = XMLSingle.getEquipment(id,eqXML) as StackEquipmentVO;
         (newEquipmentVO as StackEquipmentVO).num = num;
         newEquipmentVOs = new Vector.<EquipmentVO>();
         newEquipmentVOs.push(newEquipmentVO);
         MyFunction2.addEquipmentVOs(newEquipmentVOs,GamingUI.getInstance().player1,function():void
         {
            isSuccessPutInPackage = false;
         },function():void
         {
            isSuccessPutInPackage = true;
         },[],[]);
         num2 = getSunMinNum + Math.floor(Math.random() * (getSunMaxNum - getSunMinNum + 1));
         return [isSuccessPutInPackage,num,newEquipmentVO.name,num2];
      }
      
      public function countLandStateRemainTime(param1:LandVO, param2:String, param3:XML, param4:XML) : Object
      {
         var _loc6_:int = 0;
         var _loc9_:int = 0;
         var _loc16_:int = 0;
         var _loc11_:Object = null;
         var _loc10_:XML = null;
         var _loc5_:XML = null;
         var _loc13_:int = 0;
         var _loc17_:XMLList = null;
         var _loc7_:Number = NaN;
         var _loc8_:String = null;
         var _loc15_:Number = NaN;
         var _loc12_:int = 0;
         var _loc14_:int = 0;
         switch(param1.state)
         {
            case 0:
               _loc11_ = {"remainTime":0};
               break;
            case 1:
               _loc6_ = Math.floor(new TimeUtil().timeInterval(param1.date,param2) * 3600);
               _loc5_ = param3.item.(@id == param1.equipmentIDInLand)[0];
               _loc17_ = _loc5_.growData;
               _loc10_ = param4.land.(@id == param1.id)[0];
               _loc7_ = Number(_loc10_.@impactValueToGeneration);
               _loc9_ = 0;
               _loc16_ = _loc17_.length() - 1;
               _loc9_ = 0;
               while(_loc9_ < _loc16_)
               {
                  if(Number(_loc17_[_loc9_].@time) * 60 * _loc7_ > _loc6_)
                  {
                     break;
                  }
                  _loc9_++;
               }
               _loc8_ = String(_loc17_[_loc9_ - 1].@state);
               if(_loc8_ != "maturityState")
               {
                  _loc13_ = Number(_loc17_[_loc9_].@time) * 60 - _loc6_;
                  _loc11_ = {
                     "remainTime":_loc13_,
                     "plantState":_loc8_
                  };
               }
               else
               {
                  _loc13_ = 0;
                  param1.state = 2;
                  param1.date = "";
                  _loc11_ = {
                     "remainTime":_loc13_,
                     "plantState":_loc8_,
                     "changeLandState":param1.state
                  };
               }
               break;
            case 2:
               _loc11_ = {
                  "remainTime":0,
                  "plantState":"maturityState"
               };
               break;
            case 3:
               _loc11_ = {
                  "remainTime":0,
                  "plantState":"witherState"
               };
               break;
            case 4:
               _loc6_ = Math.floor(new TimeUtil().timeInterval(param1.date,param2) * 3600);
               _loc10_ = param4.land.(@id == param1.id)[0];
               _loc5_ = param3.item.(@id == param1.equipmentIDInLand)[0];
               _loc15_ = 1 - GamingUI.getInstance().player1.vipVO.accLandRecoverValue;
               _loc12_ = Math.round(int(_loc5_.@lossLandValue) / int(_loc10_.@recoverability) * GamingUI.getInstance().player1.vipVO.accLandRecoverValue) * 60;
               _loc14_ = Math.round(int(_loc5_.@lossLandValue) / int(_loc10_.@recoverability) * _loc15_) * 60;
               _loc13_ = _loc14_ - _loc6_;
               _loc11_ = {
                  "remainTime":_loc13_,
                  "vipAccLandRecoverTime":_loc12_
               };
               if(_loc13_ <= 0)
               {
                  param1.date = "";
                  param1.equipmentIDInLand = 0;
                  param1.state = 0;
                  _loc13_ = 0;
                  _loc11_ = {
                     "remainTime":_loc13_,
                     "changeLandState":param1.state,
                     "vipAccLandRecoverTime":_loc12_
                  };
               }
               break;
            default:
               throw new Error("土地状态类型错误！");
         }
         return _loc11_;
      }
   }
}

