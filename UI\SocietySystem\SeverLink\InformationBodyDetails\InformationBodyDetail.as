package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class InformationBodyDetail
   {
      
      protected var m_informationBodyId:int;
      
      public function InformationBodyDetail()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function getThisByteArray() : ByteArray
      {
         return null;
      }
      
      public function initFromByteArray(param1:ByteArray) : void
      {
      }
      
      public function getInformationBodyId() : int
      {
         return m_informationBodyId;
      }
   }
}

