package YJFY.ShowLogicShell.Util
{
   import YJFY.ShowLogicShell.TipShowLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.InteractiveObject;
   import flash.display.Sprite;
   
   public class InitTipShowsInShow
   {
      
      private var m_tipMsgs:Vector.<Sprite>;
      
      private var m_tipShows:Vector.<TipShowLogicShell>;
      
      private var m_show:Sprite;
      
      private var m_tipParent:DisplayObjectContainer;
      
      public function InitTipShowsInShow()
      {
         super();
         m_tipMsgs = new Vector.<Sprite>();
         m_tipShows = new Vector.<TipShowLogicShell>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_tipMsgs);
         m_tipMsgs = null;
         ClearUtil.clearObject(m_tipShows);
         m_tipShows = null;
         m_show = null;
         m_tipParent = null;
      }
      
      public function init(param1:Sprite, param2:DisplayObjectContainer) : void
      {
         var _loc9_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:String = null;
         var _loc3_:DisplayObject = null;
         var _loc6_:TipShowLogicShell = null;
         m_show = param1;
         m_tipParent = param2;
         ClearUtil.clearObject(m_tipMsgs);
         m_tipMsgs.length = 0;
         ClearUtil.clearObject(m_tipShows);
         m_tipShows.length = 0;
         var _loc5_:uint = uint(param1.numChildren);
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc3_ = param1.getChildAt(_loc9_);
            if(_loc3_.name.substr(0,"tipShow".length) == "tipShow")
            {
               _loc6_ = new TipShowLogicShell();
               _loc6_.setShow(_loc3_ as InteractiveObject);
               m_tipShows.push(_loc6_);
            }
            else if(_loc3_.name.substr(0,"tipMsg".length) == "tipMsg")
            {
               m_tipMsgs.push(_loc3_ as Sprite);
            }
            else if(_loc3_ is DisplayObjectContainer)
            {
               searchTipShows(_loc3_ as DisplayObjectContainer);
            }
            _loc9_++;
         }
         _loc5_ = m_tipShows.length;
         var _loc7_:int = int(m_tipMsgs.length);
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc4_ = m_tipShows[_loc9_].getShow().name;
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(m_tipMsgs[_loc8_].name.substr("tipMsg_".length,m_tipMsgs[_loc8_].name.length) == _loc4_)
               {
                  m_tipShows[_loc9_].init1(m_tipMsgs[_loc8_],m_tipParent);
                  break;
               }
               _loc8_++;
            }
            _loc9_++;
         }
         _loc8_ = 0;
         while(_loc8_ < _loc7_)
         {
            if(m_tipMsgs[_loc8_].parent)
            {
               m_tipMsgs[_loc8_].parent.removeChild(m_tipMsgs[_loc8_]);
            }
            _loc8_++;
         }
      }
      
      private function searchTipShows(param1:DisplayObjectContainer) : void
      {
         var _loc5_:int = 0;
         var _loc2_:DisplayObject = null;
         var _loc4_:TipShowLogicShell = null;
         var _loc3_:uint = uint(param1.numChildren);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_ = param1.getChildAt(_loc5_);
            if(_loc2_.name.substr(0,"tipShow".length) == "tipShow")
            {
               _loc4_ = new TipShowLogicShell();
               _loc4_.setShow(_loc2_ as InteractiveObject);
               m_tipShows.push(_loc4_);
            }
            else if(_loc2_ is DisplayObjectContainer)
            {
               searchTipShows(_loc2_ as DisplayObjectContainer);
            }
            _loc5_++;
         }
      }
   }
}

