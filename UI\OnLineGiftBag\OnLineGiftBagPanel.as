package UI.OnLineGiftBag
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.GameEvent;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class OnLineGiftBagPanel extends MySprite
   {
      
      private var _show:MovieClip;
      
      private var _giftBagSmallPanels:Vector.<OnLineGiftBagSmallPanel>;
      
      private var _quitBtn:ButtonLogicShell2;
      
      private var _currentActiveIndex:int;
      
      private var _wantLoadSources:Array = ["onLineGiftBag"];
      
      private var _onLineGiftBagData:OnLineGiftBagData;
      
      public var gamingUI:GamingUI;
      
      private var m_timeUtil:TimeUtil;
      
      public function OnLineGiftBagPanel()
      {
         super();
         m_timeUtil = new TimeUtil();
      }
      
      override public function clear() : void
      {
         _show.removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         super.clear();
         _show = null;
         ClearUtil.nullArr(_giftBagSmallPanels);
         _giftBagSmallPanels = null;
         ClearUtil.clearObject(_quitBtn);
         _quitBtn = null;
         _onLineGiftBagData = null;
         gamingUI = null;
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         ClearUtil.clearObject(m_timeUtil);
         m_timeUtil = null;
      }
      
      public function init(param1:OnLineGiftBagData) : void
      {
         var onLineGiftBagPanel:OnLineGiftBagPanel;
         var loadFinishListener:LoadFinishListener1;
         var onLineGiftBagData:OnLineGiftBagData = param1;
         _onLineGiftBagData = onLineGiftBagData;
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         onLineGiftBagPanel = this;
         loadFinishListener = new LoadFinishListener1(function():void
         {
            var _loc5_:int = 0;
            var _loc2_:OnLineGiftBagSmallPanel = null;
            var _loc3_:* = undefined;
            _onLineGiftBagData.onLineGiftBagXML = getGiftBagXml();
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("OnLineGiftBagPanel") as MovieClip;
            }
            addChildAt(_show,0);
            _quitBtn = new ButtonLogicShell2();
            _quitBtn.setShow(_show["quitBtn"]);
            _quitBtn.setTipString("点击退出");
            _giftBagSmallPanels = new Vector.<OnLineGiftBagSmallPanel>();
            _loc5_ = 0;
            while(_loc5_ < 4)
            {
               _loc2_ = new OnLineGiftBagSmallPanel();
               _loc2_.setOnLineGiftBagPanel(onLineGiftBagPanel);
               _loc2_.setShow(_show["giftBag" + (_loc5_ + 1)],_loc5_);
               _giftBagSmallPanels.push(_loc2_);
               _loc5_++;
            }
            var _loc4_:XMLList = _onLineGiftBagData.onLineGiftBagXML.giftBag;
            var _loc1_:int = int(_loc4_.length());
            _loc5_ = 0;
            while(_loc5_ < _loc1_)
            {
               _loc3_ = XMLSingle.getEquipmentVOs(_loc4_[_loc5_],XMLSingle.getInstance().equipmentXML,true);
               if(_loc5_ < _giftBagSmallPanels.length)
               {
                  _giftBagSmallPanels[_loc5_].setquipmentVOs(_loc3_);
               }
               if(String(_loc4_[_loc5_].@id) == _onLineGiftBagData.currentGiftBagId)
               {
                  _currentActiveIndex = _loc5_;
               }
               _loc5_++;
            }
            setActiveShow();
         },null);
         gamingUI.loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      public function init2(param1:OnLineGiftBagData, param2:MovieClip) : void
      {
         var _loc8_:int = 0;
         var _loc5_:OnLineGiftBagSmallPanel = null;
         var _loc6_:* = undefined;
         _show = param2;
         _onLineGiftBagData = param1;
         _show.addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         var _loc3_:* = this;
         _onLineGiftBagData.onLineGiftBagXML = getGiftBagXml();
         _quitBtn = new ButtonLogicShell2();
         _giftBagSmallPanels = new Vector.<OnLineGiftBagSmallPanel>();
         _loc8_ = 0;
         while(_loc8_ < 4)
         {
            _loc5_ = new OnLineGiftBagSmallPanel();
            _loc5_.setOnLineGiftBagPanel(_loc3_);
            _loc5_.setShow(_show["giftBag" + (_loc8_ + 1)],_loc8_);
            _giftBagSmallPanels.push(_loc5_);
            _loc8_++;
         }
         var _loc7_:XMLList = _onLineGiftBagData.onLineGiftBagXML.giftBag;
         var _loc4_:int = int(_loc7_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc6_ = XMLSingle.getEquipmentVOs(_loc7_[_loc8_],XMLSingle.getInstance().equipmentXML,true);
            if(_loc8_ < _giftBagSmallPanels.length)
            {
               _giftBagSmallPanels[_loc8_].setquipmentVOs(_loc6_);
            }
            if(String(_loc7_[_loc8_].@id) == _onLineGiftBagData.currentGiftBagId)
            {
               _currentActiveIndex = _loc8_;
            }
            _loc8_++;
         }
         setActiveShow();
      }
      
      public function render() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = _giftBagSmallPanels ? _giftBagSmallPanels.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _giftBagSmallPanels[_loc2_].setRemainTime(_onLineGiftBagData.remainTime);
            _loc2_++;
         }
      }
      
      private function getGiftBagXml() : XML
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < XMLSingle.getInstance().onLineGiftBagXML.levelGift.length())
         {
            if(XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc1_].@minLevel <= GamingUI.getInstance().player1.playerVO.level && XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc1_].@maxLevel >= GamingUI.getInstance().player1.playerVO.level)
            {
               return XMLSingle.getInstance().onLineGiftBagXML.levelGift[_loc1_];
            }
            _loc1_++;
         }
         return null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var giftBagXML:XML;
         var maxMinInterval:uint;
         var e:ButtonEvent = param1;
         var length:int = int(_giftBagSmallPanels.length);
         var i:int = 0;
         while(i < length)
         {
            if(_giftBagSmallPanels[i].getGitBagBtn() == e.button)
            {
               giftBagXML = getGiftBagXml().giftBag[0];
               maxMinInterval = uint(giftBagXML.@needTime) * ProgramStartData.getInstance().getOneThousand();
               if(_onLineGiftBagData.addGetRecycleNumTime != 0)
               {
                  if(gamingUI.getEnterFrameTime().getOnLineTimeForThisInit() - _onLineGiftBagData.addGetRecycleNumTime < maxMinInterval)
                  {
                     showWarningBox("时间出错了",0);
                     return;
                  }
               }
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  if(_onLineGiftBagData.exportSaveRemainTime() > 0)
                  {
                     showWarningBox("时间不足",0);
                     return;
                  }
                  _giftBagSmallPanels[i].getGiftBag();
               },showWarningBox,true);
               break;
            }
            i++;
         }
         var _loc3_:* = e.button;
         if(_quitBtn === _loc3_)
         {
            gamingUI.closeOnLineGiftBagPanel();
         }
      }
      
      public function successGetGiftBag(param1:OnLineGiftBagSmallPanel) : void
      {
         var _loc2_:int = int(_giftBagSmallPanels.indexOf(param1));
         if(_loc2_ == -1)
         {
            throw new Error();
         }
         gamingUI.addMainLineTaskGoalGameEventStr("getOnLineGiftBag_" + (_loc2_ + 1));
         changeGiftBagIdAndRemainTime();
         setActiveShow();
         _onLineGiftBagData.addGetRecycleNumTime = gamingUI.getEnterFrameTime().getOnLineTimeForThisInit();
         var _loc3_:SaveTaskInfo = new SaveTaskInfo();
         _loc3_.type = "4399";
         _loc3_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc3_);
         MyFunction2.saveGame();
         showWarningBox("获取成功！",0);
         GameEvent.eventDispacher.dispatchEvent(new GameEvent("smallonlinetip"));
      }
      
      private function setActiveShow() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = int(_giftBagSmallPanels.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _giftBagSmallPanels[_loc2_].unActivePanel();
            _loc2_++;
         }
         _giftBagSmallPanels[_currentActiveIndex].activePanel();
      }
      
      private function changeGiftBagIdAndRemainTime() : void
      {
         var _loc1_:XMLList = _onLineGiftBagData.onLineGiftBagXML.giftBag;
         if(_currentActiveIndex < _loc1_.length() - 1)
         {
            _currentActiveIndex++;
         }
         else
         {
            _currentActiveIndex = 0;
            _onLineGiftBagData.getRecyleNum += 1;
         }
         _onLineGiftBagData.currentGiftBagId = String(_loc1_[_currentActiveIndex].@id);
         _onLineGiftBagData.remainTime = int(_loc1_[_currentActiveIndex].@needTime) * ProgramStartData.getInstance().getOneThousand();
         _onLineGiftBagData.remainTime_vertify = _onLineGiftBagData.remainTime;
         _onLineGiftBagData.servertime_vertify = gamingUI.getNewestTimeStrFromSever();
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         gamingUI.showMessageTip(param1);
      }
   }
}

