package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_ChangeAnnouncement extends InformationBodyDetail
   {
      
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_societyId:int;
      
      private var m_announcementLen:int;
      
      private var m_announcement:String;
      
      public function UP_ChangeAnnouncement()
      {
         super();
         m_informationBodyId = 3031;
      }
      
      public function initData(param1:Number, param2:int, param3:int, param4:String) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_societyId = param3;
         m_announcement = param4;
         m_announcementLen = new InformationBodyDetailUtil().getStringLength(m_announcement);
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_societyId);
         _loc1_.writeInt(m_announcementLen);
         if(m_announcementLen)
         {
            _loc1_.writeUTFBytes(m_announcement);
         }
         return _loc1_;
      }
      
      public function getAnnouncement() : String
      {
         return m_announcement;
      }
   }
}

