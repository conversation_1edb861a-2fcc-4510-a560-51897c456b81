package YJFY.PKMode2.PKUI
{
   import UI.EnterFrameTime;
   import UI.ExternalUI.AutomaticPetPanel;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Players.PlayerListener;
   import YJFY.GameSystemPanel.GameSystemPanelListener;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PKLogic.PKWorld;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class PKingInforPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_player1Panel:PKPlayerPanel;
      
      private var m_player2Panel:PKPlayerPanel;
      
      private var m_automaticPetPanel1:AutomaticPetPanel;
      
      private var m_automaticPetPanel2:AutomaticPetPanel;
      
      private var m_playerListener:PlayerListener;
      
      private var m_systemBtn:ButtonLogicShell;
      
      private var m_gameSystemPanelListener:GameSystemPanelListener;
      
      private var m_animationMC:MovieClipPlayLogicShell;
      
      private var m_stopListener:StopListener;
      
      private var m_existPKShow:Sprite;
      
      private var m_existPKSureBtn:ButtonLogicShell;
      
      private var m_existPKCancelBtn:ButtonLogicShell;
      
      private var m_startPKAnimation:AnimationShowPlayLogicShell;
      
      private var m_winAnimation:AnimationShowPlayLogicShell;
      
      private var m_failAnimation:AnimationShowPlayLogicShell;
      
      private var m_backBtn:ButtonLogicShell;
      
      private var m_isShowAutomaticPetPanel:Boolean;
      
      private var m_player1:Player;
      
      private var m_player2:Player;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_stopFun:Function;
      
      private var m_pk:PK2;
      
      private var m_pkWorld:PKWorld;
      
      public function PKingInforPanel()
      {
         super();
         m_playerListener = new PlayerListener();
         m_playerListener.changeDataFun = playerChangeData;
         m_showMC = new MovieClipPlayLogicShell();
         m_animationMC = new MovieClipPlayLogicShell();
         m_stopListener = new StopListener();
         m_stopListener.stop2Fun = showStop;
         m_existPKCancelBtn = new ButtonLogicShell();
         m_existPKSureBtn = new ButtonLogicShell();
         m_gameSystemPanelListener = new GameSystemPanelListener();
         m_gameSystemPanelListener.existPKFun = existPK;
         addEventListener("clickButton",clickButton,true,0,true);
         m_isShowAutomaticPetPanel = true;
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(GamingUI.getInstance().getVersionControl());
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         if(m_player1)
         {
            m_player1.removePlayerListener(m_playerListener);
         }
         if(m_player2)
         {
            m_player2.removePlayerListener(m_playerListener);
         }
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_player1Panel);
         m_player1Panel = null;
         ClearUtil.clearObject(m_player2Panel);
         m_player2Panel = null;
         ClearUtil.clearObject(m_automaticPetPanel1);
         m_automaticPetPanel1 = null;
         ClearUtil.clearObject(m_automaticPetPanel2);
         m_automaticPetPanel2 = null;
         ClearUtil.clearObject(m_playerListener);
         m_playerListener = null;
         ClearUtil.clearObject(m_animationMC);
         m_animationMC = null;
         ClearUtil.clearObject(m_stopListener);
         m_stopListener = null;
         ClearUtil.clearObject(m_systemBtn);
         m_systemBtn = null;
         ClearUtil.clearObject(m_gameSystemPanelListener);
         m_gameSystemPanelListener = null;
         ClearUtil.clearObject(m_existPKCancelBtn);
         m_existPKCancelBtn = null;
         ClearUtil.clearObject(m_existPKSureBtn);
         m_existPKSureBtn = null;
         m_existPKShow = null;
         ClearUtil.clearObject(m_startPKAnimation);
         m_startPKAnimation = null;
         ClearUtil.clearObject(m_winAnimation);
         m_winAnimation = null;
         ClearUtil.clearObject(m_failAnimation);
         m_failAnimation = null;
         ClearUtil.clearObject(m_backBtn);
         m_backBtn = null;
         m_player1 = null;
         m_player2 = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_stopFun = null;
         m_pk = null;
         m_pkWorld = null;
         super.clear();
      }
      
      public function init(param1:YJFYLoader, param2:PK2, param3:PKWorld) : void
      {
         m_pk = param2;
         m_pkWorld = param3;
         if(m_show == null)
         {
            m_myLoader.getClass("NewGameFolder/PKMode2/PKSource.swf","PKingInforUI",getShowSuccess,null);
            m_myLoader.load();
         }
         else
         {
            initShow();
         }
      }
      
      public function setPlayers(param1:Player, param2:Player) : void
      {
         if(m_player1)
         {
            m_player1.removePlayerListener(m_playerListener);
         }
         if(m_player2)
         {
            m_player2.removePlayerListener(m_playerListener);
         }
         m_player1 = param1;
         m_player2 = param2;
         if(m_player1)
         {
            m_player1.addPlayerListener(m_playerListener);
         }
         if(m_player2)
         {
            m_player2.addPlayerListener(m_playerListener);
         }
         initShow2();
      }
      
      public function setIsShowAutomaticPetPanel(param1:Boolean) : void
      {
         m_isShowAutomaticPetPanel = param1;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_player1Panel)
         {
            m_player1Panel.render(param1);
         }
         if(m_player2Panel)
         {
            m_player2Panel.render(param1);
         }
         if(Boolean(m_automaticPetPanel1) && m_automaticPetPanel1.getShow().parent)
         {
            m_automaticPetPanel1.render(param1);
         }
         if(Boolean(m_automaticPetPanel2) && m_automaticPetPanel2.getShow().parent)
         {
            m_automaticPetPanel2.render(param1);
         }
      }
      
      public function playStartAnimation(param1:Function) : void
      {
         m_stopFun = param1;
         initGameStartFrame();
         m_startPKAnimation.gotoAndPlay("1");
      }
      
      public function playGameEndAnimation(param1:Boolean, param2:Function) : void
      {
         m_stopFun = param2;
         initGameEndFrame();
         if(param1)
         {
            m_winAnimation.gotoAndPlay("1");
            (m_failAnimation.getShow() as DisplayObject).visible = false;
         }
         else
         {
            m_failAnimation.gotoAndPlay("1");
            (m_winAnimation.getShow() as DisplayObject).visible = false;
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
      }
      
      private function initShow() : void
      {
         m_showMC.setShow(m_show);
         m_animationMC.setShow(m_show["animation"]);
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_player1 == null || m_show == null)
         {
            return;
         }
         if(m_player2)
         {
            m_showMC.gotoAndStop("two");
         }
         ClearUtil.clearObject(m_player1Panel);
         m_player1Panel = null;
         m_player1Panel = new PKPlayerPanel();
         m_player1Panel.setShow(m_show["playerPanel1"]);
         m_player1Panel.setPlayer(m_player1);
         ClearUtil.clearObject(m_automaticPetPanel1);
         m_automaticPetPanel1 = null;
         m_automaticPetPanel1 = new AutomaticPetPanel();
         m_automaticPetPanel1.setShow(m_show["automaticPetPanel1"]);
         automaticPetPanelShowOrNot(m_player1,m_automaticPetPanel1);
         m_existPKShow = m_show["existPKPanel"];
         m_existPKCancelBtn.setShow(m_existPKShow["cancelBtn"]);
         m_existPKSureBtn.setShow(m_existPKShow["yesBtn"]);
         hideExistPKShow();
         ClearUtil.clearObject(m_player2Panel);
         m_player2Panel = null;
         ClearUtil.clearObject(m_automaticPetPanel2);
         m_automaticPetPanel2 = null;
         if(m_player2)
         {
            m_player2Panel = new PKPlayerPanel();
            m_player2Panel.setShow(m_show["playerPanel2"]);
            m_player2Panel.setPlayer(m_player2);
            m_automaticPetPanel2 = new AutomaticPetPanel();
            m_automaticPetPanel2.setShow(m_show["automaticPetPanel2"]);
            automaticPetPanelShowOrNot(m_player2,m_automaticPetPanel2);
         }
      }
      
      private function playerChangeData(param1:Player) : void
      {
         if(param1 == m_player1)
         {
            automaticPetPanelShowOrNot(m_player1,m_automaticPetPanel1);
         }
         else
         {
            automaticPetPanelShowOrNot(m_player2,m_automaticPetPanel2);
         }
      }
      
      private function automaticPetPanelShowOrNot(param1:Player, param2:AutomaticPetPanel) : void
      {
         if(m_isShowAutomaticPetPanel && param1.playerVO.automaticPetVO)
         {
            m_show.addChild(param2.getShow());
            param2.setAutomaticPetVO(param1.playerVO.automaticPetVO);
         }
         else
         {
            param2.setAutomaticPetVO(null);
            if(param2.getShow().parent)
            {
               param2.getShow().parent.removeChild(param2.getShow());
            }
         }
      }
      
      private function frameClear() : void
      {
         ClearUtil.clearObject(m_startPKAnimation);
         m_startPKAnimation = null;
         ClearUtil.clearObject(m_winAnimation);
         m_winAnimation = null;
         ClearUtil.clearObject(m_failAnimation);
         m_failAnimation = null;
         ClearUtil.clearObject(m_backBtn);
         m_backBtn = null;
         ClearUtil.clearObject(m_systemBtn);
         m_systemBtn = null;
      }
      
      private function initGameStartFrame() : void
      {
         frameClear();
         m_animationMC.gotoAndStop("gameStart");
         m_startPKAnimation = new AnimationShowPlayLogicShell();
         m_startPKAnimation.addNextStopListener(m_stopListener);
         m_startPKAnimation.setShow(m_animationMC.getShow()["startPKAnimation"]);
      }
      
      private function initGameEndFrame() : void
      {
         frameClear();
         m_animationMC.gotoAndStop("gameEnd");
         m_winAnimation = new AnimationShowPlayLogicShell();
         m_failAnimation = new AnimationShowPlayLogicShell();
         m_winAnimation.addNextStopListener(m_stopListener);
         m_failAnimation.addNextStopListener(m_stopListener);
         m_winAnimation.setShow(m_animationMC.getShow()["winAnimation"]);
         m_failAnimation.setShow(m_animationMC.getShow()["failAnimation"]);
      }
      
      private function initNormalFrame() : void
      {
         frameClear();
         m_animationMC.gotoAndStop("normal");
         m_systemBtn = new ButtonLogicShell();
         m_systemBtn.setShow(m_animationMC.getShow()["systemBtn"]);
      }
      
      private function showStop(param1:AnimationShowPlayLogicShell) : void
      {
         switch(param1)
         {
            case m_startPKAnimation:
               if(Boolean(m_stopFun))
               {
                  m_stopFun();
               }
               initNormalFrame();
               break;
            case m_winAnimation:
            case m_failAnimation:
               if(Boolean(m_stopFun))
               {
                  m_stopFun();
               }
               m_backBtn = new ButtonLogicShell();
               m_backBtn.setShow(param1.getShow()["yesBtn"]);
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_backBtn:
               m_pk.closePKWorld();
               break;
            case m_systemBtn:
               Part1.getInstance().openSystemPanel();
               Part1.getInstance().getGameSystemPanel().show3();
               Part1.getInstance().getGameSystemPanel().addGameSystemPanelListener(m_gameSystemPanelListener);
               break;
            case m_existPKCancelBtn:
               hideExistPKShow();
               Part1.getInstance().continueGame();
               break;
            case m_existPKSureBtn:
               hideExistPKShow();
               Part1.getInstance().continueGame();
               m_pkWorld.forceEnd();
         }
      }
      
      private function existPK() : void
      {
         Part1.getInstance().getGameSystemPanel().removeGameSystemPanelListener(m_gameSystemPanelListener);
         Part1.getInstance().closeSystemPanel();
         Part1.getInstance().stopGame();
         showExistPKShow();
      }
      
      private function showExistPKShow() : void
      {
         m_existPKShow.visible = true;
      }
      
      private function hideExistPKShow() : void
      {
         m_existPKShow.visible = false;
      }
   }
}

