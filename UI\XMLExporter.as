package UI
{
   import flash.external.ExternalInterface;
   import flash.net.FileReference;
   import flash.utils.ByteArray;
   
   public class XMLExporter extends MySprite
   {
      public function XMLExporter()
      {
         super();
      }
      
      // 导出所有XML数据到控制台
      public function exportAllXMLToConsole():void
      {
         var xmlSingle:XMLSingle = XMLSingle.getInstance();
         
         trace("=== 开始导出所有XML数据 ===");
         
         // 导出装备XML
         if(xmlSingle.subEquipmentXML)
         {
            trace("=== EQUIPMENT XML ===");
            trace(xmlSingle.subEquipmentXML.toXMLString());
            trace("=== END EQUIPMENT XML ===");
         }
         
         // 导出文本XML
         if(xmlSingle.textXML)
         {
            trace("=== TEXT XML ===");
            trace(xmlSingle.textXML.toXMLString());
            trace("=== END TEXT XML ===");
         }
         
         // 导出技能XML
         if(xmlSingle.skillXML)
         {
            trace("=== SKILL XML ===");
            trace(xmlSingle.skillXML.toXMLString());
            trace("=== END SKILL XML ===");
         }
         
         // 导出任务XML
         if(xmlSingle.taskXML)
         {
            trace("=== TASK XML ===");
            trace(xmlSingle.taskXML.toXMLString());
            trace("=== END TASK XML ===");
         }
         
         trace("=== XML数据导出完成 ===");
      }
      
      // 导出特定的装备数据
      public function exportEquipmentData():void
      {
         var xmlSingle:XMLSingle = XMLSingle.getInstance();
         
         if(xmlSingle.subEquipmentXML)
         {
            var equipmentXML:XML = xmlSingle.subEquipmentXML;
            var items:XMLList = equipmentXML..item;
            
            trace("=== 装备物品列表 ===");
            for each(var item:XML in items)
            {
               var id:String = item.@id;
               var name:String = item.@name;
               var description:String = item.@description;
               var price:String = item.@price;
               
               trace("ID: " + id + " | 名称: " + name + " | 描述: " + description + " | 价格: " + price);
            }
            trace("=== 装备列表结束 ===");
         }
      }
      
      // 通过ExternalInterface暴露给JavaScript调用
      public function setupExternalInterface():void
      {
         if(ExternalInterface.available)
         {
            ExternalInterface.addCallback("exportXMLData", exportAllXMLToConsole);
            ExternalInterface.addCallback("exportEquipmentData", exportEquipmentData);
         }
      }
      
      // 保存XML到本地文件
      public function saveXMLToFile(xmlData:String, fileName:String):void
      {
         var fileRef:FileReference = new FileReference();
         var bytes:ByteArray = new ByteArray();
         bytes.writeUTFBytes(xmlData);
         fileRef.save(bytes, fileName);
      }
   }
}
