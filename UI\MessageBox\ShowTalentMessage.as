package UI.MessageBox
{
   import UI.Pets.Talents.Talent;
   
   public class ShowTalentMessage
   {
      
      public function ShowTalentMessage()
      {
         super();
      }
      
      public function showMessage(param1:Talent, param2:MessageBox) : void
      {
         if(param1)
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText(param1.talentVO.description,15);
         }
      }
   }
}

