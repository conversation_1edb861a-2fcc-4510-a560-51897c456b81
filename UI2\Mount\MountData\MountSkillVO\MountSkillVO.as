package UI2.Mount.MountData.MountSkillVO
{
   import UI.DataManagerParent;
   import UI.EnterFrameTime;
   import flash.system.ApplicationDomain;
   import flash.utils.getQualifiedClassName;
   
   public class MountSkillVO extends DataManagerParent
   {
      
      protected var m_skillId:String;
      
      protected var m_name:String;
      
      protected var m_description:String;
      
      protected var m_level:uint;
      
      protected var m_maxLevel:uint;
      
      protected var m_iconClassName:String;
      
      protected var m_iconSwfPath:String;
      
      private var m_isHaveCommonIconShowData:Boolean;
      
      protected var m_skillsXML:XML;
      
      public function MountSkillVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_skillId = null;
         m_name = null;
         m_description = null;
         m_iconClassName = null;
         m_iconSwfPath = null;
         m_skillsXML = null;
         super.clear();
      }
      
      public function initFromXML(param1:String, param2:uint, param3:XML) : void
      {
         m_skillsXML = param3;
         this.skillId = param1;
         this.level = param2;
         var _loc4_:XML = param3.skill.(@skillId == this.skillId)[0];
         name = String(_loc4_.@name);
         description = String(_loc4_.@description);
         iconSwfPath = String(_loc4_.@iconSwfPath);
         iconClassName = String(_loc4_.@iconClassName);
         if(Boolean(iconSwfPath) && Boolean(iconClassName))
         {
            m_isHaveCommonIconShowData = true;
         }
         else
         {
            m_isHaveCommonIconShowData = false;
         }
         this.maxLevel = _loc4_.levelData.length();
         var _loc5_:XML = _loc4_.levelData.(@level == param2)[0];
         if(_loc5_ == null)
         {
            throw new Error("skill levelData xml is null");
         }
         initSkillLevelData(_loc5_);
      }
      
      public function copy(param1:MountSkillVO) : void
      {
         this.skillId = param1.skillId;
         this.level = param1.level;
         initFromXML(skillId,level,param1.m_skillsXML);
      }
      
      public function clone() : MountSkillVO
      {
         var _loc2_:String = getQualifiedClassName(this);
         var _loc1_:Class = ApplicationDomain.currentDomain.getDefinition(_loc2_) as Class;
         var _loc3_:MountSkillVO = new _loc1_();
         _loc3_.copy(this);
         return _loc3_;
      }
      
      public function changeLevel(param1:uint) : void
      {
         level = param1;
         initFromXML(this.skillId,level,m_skillsXML);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
      }
      
      public function getId() : String
      {
         return skillId;
      }
      
      public function getName() : String
      {
         return name;
      }
      
      public function getLevel() : uint
      {
         return level;
      }
      
      public function getMaxLevel() : uint
      {
         return maxLevel;
      }
      
      public function getDescription() : String
      {
         return description;
      }
      
      public function getIconClassName() : String
      {
         return iconClassName;
      }
      
      public function getIconSwfPath() : String
      {
         return iconSwfPath;
      }
      
      protected function initSkillLevelData(param1:XML) : void
      {
         if(m_isHaveCommonIconShowData == false)
         {
            this.iconSwfPath = String(param1.data.(@att == "iconSwfPath")[0].@value);
            this.iconClassName = String(param1.data.(@att == "iconClassName")[0].@value);
         }
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.skillId = m_skillId;
         _antiwear.level = m_level;
      }
      
      protected function get skillId() : String
      {
         return _antiwear.skillId;
      }
      
      protected function set skillId(param1:String) : void
      {
         _antiwear.skillId = param1;
      }
      
      protected function get name() : String
      {
         return m_name;
      }
      
      protected function set name(param1:String) : void
      {
         m_name = param1;
      }
      
      protected function get description() : String
      {
         return m_description;
      }
      
      protected function set description(param1:String) : void
      {
         m_description = param1;
      }
      
      protected function get level() : uint
      {
         return _antiwear.level;
      }
      
      protected function set level(param1:uint) : void
      {
         _antiwear.level = param1;
      }
      
      protected function get maxLevel() : uint
      {
         return m_maxLevel;
      }
      
      protected function set maxLevel(param1:uint) : void
      {
         m_maxLevel = param1;
      }
      
      protected function get iconClassName() : String
      {
         return m_iconClassName;
      }
      
      protected function set iconClassName(param1:String) : void
      {
         m_iconClassName = param1;
      }
      
      protected function get iconSwfPath() : String
      {
         return m_iconSwfPath;
      }
      
      protected function set iconSwfPath(param1:String) : void
      {
         m_iconSwfPath = param1;
      }
   }
}

