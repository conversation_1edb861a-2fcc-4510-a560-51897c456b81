package YJFY.XydzjsData.AISkillVO
{
   import YJFY.XydzjsData.IAttackSkillVOForAttackDataCalculate;
   
   public class AIAttackSkillVO extends AIActiveSkillVO implements IAttackSkillVOForAttackDataCalculate
   {
      
      private var m_skillHurtMulti:Number;
      
      public function AIAttackSkillVO()
      {
         super();
      }
      
      override public function initFromXML(param1:XML) : void
      {
         super.initFromXML(param1);
         m_skillHurtMulti = Number(param1.@hurtMulti);
         if(m_skillHurtMulti == 0)
         {
            m_skillHurtMulti = 1;
         }
      }
      
      public function getSkillHurtMulti() : Number
      {
         return m_skillHurtMulti;
      }
      
      public function getSkillHurtAdd() : uint
      {
         return 0;
      }
   }
}

