package YJFY.StartGamePanel
{
   import UI.LastSaveData;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.VersionControl;
   import YJFY.API_4399.LogAPI.LogAPI;
   import YJFY.API_4399.LogAPI.LogAPIListener;
   import YJFY.API_4399.LogAPI.LoginReturnData;
   import YJFY.API_4399.SaveAPI.SaveAPI;
   import YJFY.API_4399.SaveAPI.SaveAPIListener2;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.ChoicePlayerPanel.ChoicePlayerPanel;
   import YJFY.ChoicePlayerPanel.ChoicePlayerPanelListener;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.GameData;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Other.PlayerTypeDataInSaveXML;
   import YJFY.Other.SaveXMLFunction;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.SoundManager.SoundManager2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.World.SoundData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class StartGamePanel extends MySprite
   {
      
      private const m_const_enterStep_frameLabel:String = "enterStep^stop^";
      
      private const m_const_startToMainStep_frameLabel:String = "startToMainStep";
      
      private const m_const_mainStep_frameLabel:String = "mainStep^stop^";
      
      private const m_const_newGameStep_framLabel:String = "newGameStep^stop^";
      
      private const m_const_newGameStepEnd_frameLabel:String = "newGameStepEnd^stop^";
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_startGamePanelListeners:Vector.<IStartGamePanelListener>;
      
      private var m_choicePlayerPanel:ChoicePlayerPanel;
      
      private var m_choicePlayerPanelListener:ChoicePlayerPanelListener;
      
      private var m_logAPIListener:LogAPIListener;
      
      private var m_saveApiListener:SaveAPIListener2;
      
      private var m_show:MovieClip;
      
      private var m_showMC:AnimationShowPlayLogicShell;
      
      private var m_showMCFrameLabelListener:AnimationPlayFrameLabelListener;
      
      private var m_mengBan:Sprite;
      
      private var m_versionTxt:TextField;
      
      private var m_enterGameBtn:ButtonLogicShell;
      
      private var m_newSaveBtn:ButtonLogicShell;
      
      private var m_getSaveListBtn:ButtonLogicShell;
      
      private var m_aboutUsBtn:ButtonLogicShell;
      
      private var m_upDateBtn:ButtonLogicShell;
      
      private var m_gotoForumBtn:ButtonLogicShell;
      
      private var m_onePlayerBtn:ButtonLogicShell;
      
      private var m_twoPlayerBtn:ButtonLogicShell;
      
      private var m_returnBtn:ButtonLogicShell;
      
      private var m_aboutUsPanel:MovieClipPlayLogicShell;
      
      private var m_aboutUsPanelQuitBtn:ButtonLogicShell;
      
      private var m_updatePanel:MovieClipPlayLogicShell;
      
      private var m_updatePanelQuitBtn:ButtonLogicShell;
      
      private var m_updateMoreBtn:ButtonLogicShell;
      
      private var m_showSaveFileDatasPanel:AbleDragSpriteLogicShell;
      
      private var m_showSaveFileDatasCols:Vector.<ButtonLogicShell2>;
      
      private var m_showSaveFileDatasPanelQuitBtn:ButtonLogicShell;
      
      private var m_startGameSoundData:SoundData;
      
      private var m_soundManager:SoundManager2;
      
      private var m_appealWarningShow:MovieClip;
      
      private var m_appealWarningSureBtn:ButtonLogicShell;
      
      private var m_appealWarningCancelBtn:ButtonLogicShell;
      
      private var m_coverSaveWarningShow:MovieClip;
      
      private var m_coverSaveWarningSureBtn:ButtonLogicShell;
      
      private var m_coverSaveWarningCancelBtn:ButtonLogicShell;
      
      private var m_coverSaveDataIndex:uint;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_saveFileDatas:Vector.<SaveFileData>;
      
      private var m_logAPI:LogAPI;
      
      private var m_saveAPI:SaveAPI;
      
      private var m_versionControl:VersionControl;
      
      private var m_currentSelectBtn:IButton;
      
      private var m_player1Str:String;
      
      private var m_player2Str:String;
      
      public function StartGamePanel()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         m_myLoader = new YJFYLoader();
         m_startGamePanelListeners = new Vector.<IStartGamePanelListener>();
         m_logAPIListener = new LogAPIListener();
         m_logAPIListener.logInSuccessFun = loginSuccess;
         m_logAPIListener.logOutSuccessFun = logOutSuccess;
         m_saveApiListener = new SaveAPIListener2();
         m_saveApiListener.getSaveFileListSuccessFun = getSaveFileListSuccess;
         m_saveApiListener.getSaveFileSuccessFun = getSaveFileSuccess;
         m_saveApiListener.netGetErrorFun = netGetError;
         m_choicePlayerPanelListener = new ChoicePlayerPanelListener();
         m_choicePlayerPanelListener.choicePlayerCompleteFun = choicePlayerComplete;
         m_choicePlayerPanelListener.quitFun = quitChoicePlayerComplete;
         m_showMCFrameLabelListener = new AnimationPlayFrameLabelListener();
         m_showMCFrameLabelListener.reachFrameLabelFun = showMCReachFrameLabel;
         addEventListener("clickButton",clickButton,true,0,true);
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         if(m_logAPI)
         {
            m_logAPI.removeLogAPIListener(m_logAPIListener);
         }
         if(m_saveAPI)
         {
            m_saveAPI.removeSaveAPIListener(m_saveApiListener);
         }
         removeEventListener("clickButton",clickButton,true);
         if(Boolean(m_startGameSoundData) && m_soundManager)
         {
            m_soundManager.stop(m_startGameSoundData.getName());
         }
         clearAboutUsPanel();
         clearFrameShow();
         clearSaveFileDatasPanel();
         clearUpdatePanel();
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.nullArr(m_startGamePanelListeners,false,false,false);
         m_startGamePanelListeners = null;
         ClearUtil.clearObject(m_choicePlayerPanel);
         m_choicePlayerPanel = null;
         ClearUtil.clearObject(m_choicePlayerPanelListener);
         m_choicePlayerPanelListener = null;
         ClearUtil.clearObject(m_saveApiListener);
         m_saveApiListener = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_showMCFrameLabelListener);
         m_showMCFrameLabelListener = null;
         ClearUtil.clearObject(m_mengBan);
         m_mengBan = null;
         ClearUtil.clearObject(m_enterGameBtn);
         m_enterGameBtn = null;
         ClearUtil.clearObject(m_onePlayerBtn);
         m_onePlayerBtn = null;
         ClearUtil.clearObject(m_twoPlayerBtn);
         m_twoPlayerBtn = null;
         ClearUtil.clearObject(m_newSaveBtn);
         m_newSaveBtn = null;
         ClearUtil.clearObject(m_newSaveBtn);
         m_newSaveBtn = null;
         ClearUtil.clearObject(m_getSaveListBtn);
         m_getSaveListBtn = null;
         ClearUtil.clearObject(m_aboutUsBtn);
         m_aboutUsBtn = null;
         ClearUtil.clearObject(m_gotoForumBtn);
         m_gotoForumBtn = null;
         m_versionTxt = null;
         ClearUtil.clearObject(m_aboutUsPanel);
         m_aboutUsPanel = null;
         ClearUtil.clearObject(m_aboutUsPanelQuitBtn);
         m_aboutUsPanelQuitBtn = null;
         ClearUtil.clearObject(m_updatePanel);
         m_updatePanel = null;
         ClearUtil.clearObject(m_updatePanelQuitBtn);
         m_updatePanelQuitBtn = null;
         ClearUtil.clearObject(m_updateMoreBtn);
         m_updateMoreBtn = null;
         ClearUtil.clearObject(m_showSaveFileDatasPanel);
         m_showSaveFileDatasPanel = null;
         ClearUtil.clearObject(m_showSaveFileDatasCols);
         m_showSaveFileDatasCols = null;
         ClearUtil.clearObject(m_showSaveFileDatasPanelQuitBtn);
         m_showSaveFileDatasPanelQuitBtn = null;
         ClearUtil.clearObject(m_startGameSoundData);
         m_startGameSoundData = null;
         ClearUtil.clearObject(m_soundManager);
         m_soundManager = null;
         ClearUtil.clearObject(m_appealWarningShow);
         m_appealWarningShow = null;
         ClearUtil.clearObject(m_appealWarningSureBtn);
         m_appealWarningSureBtn = null;
         ClearUtil.clearObject(m_appealWarningCancelBtn);
         m_appealWarningCancelBtn = null;
         ClearUtil.clearObject(m_coverSaveWarningShow);
         m_coverSaveWarningShow = null;
         ClearUtil.clearObject(m_coverSaveWarningSureBtn);
         m_coverSaveWarningSureBtn = null;
         ClearUtil.clearObject(m_coverSaveWarningCancelBtn);
         m_coverSaveWarningCancelBtn = null;
         m_font = null;
         ClearUtil.clearObject(m_saveFileDatas);
         m_saveFileDatas = null;
         m_logAPI = null;
         m_saveAPI = null;
         m_versionControl = null;
         m_currentSelectBtn = null;
         m_player1Str = null;
         m_player2Str = null;
         super.clear();
      }
      
      public function setLogAPI(param1:LogAPI) : void
      {
         if(m_logAPI)
         {
            m_logAPI.removeLogAPIListener(m_logAPIListener);
         }
         m_logAPI = param1;
         m_logAPI.addLogAPIListener(m_logAPIListener);
      }
      
      public function setSaveAPI(param1:SaveAPI) : void
      {
         if(m_saveAPI)
         {
            m_saveAPI.removeSaveAPIListener(m_saveApiListener);
         }
         m_saveAPI = param1;
         m_saveAPI.addSaveAPIListener(m_saveApiListener);
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
         m_myLoader.setVersionControl(m_versionControl);
      }
      
      public function init() : void
      {
         Part1.getInstance().showGameWaitShow();
         m_loadUI = Part1.getInstance().getLoadUI();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.getClass("NewGameFolder/GameStartPanel.swf","GameStartUI",getShowSuccess,getShowFail,null,null,null,null,false);
         m_myLoader.load();
         m_startGameSoundData = new SoundData("startGameMusic","startGameMusic","NewGameFolder/GameStartPanel.swf","StartMusic");
         m_soundManager = new SoundManager2();
         m_soundManager.setMyLoader(m_myLoader);
         m_soundManager.addSound2(m_startGameSoundData.getName(),m_startGameSoundData.getSwfPath(),m_startGameSoundData.getClassName());
      }
      
      public function addStartGamePanelListener(param1:IStartGamePanelListener) : void
      {
         ListenerUtil.addListener(m_startGamePanelListeners,param1);
      }
      
      public function removeStartGamePanelListener(param1:IStartGamePanelListener) : void
      {
         ListenerUtil.removeListener(m_startGamePanelListeners,param1);
      }
      
      private function initShow() : void
      {
         m_showMC = new AnimationShowPlayLogicShell();
         m_showMC.addFrameLabelListener(m_showMCFrameLabelListener);
         m_showMC.setShow(m_show);
         m_mengBan = m_show["mengBan"];
         if(m_logAPI.getLogInfo(null))
         {
            initMainStepShow();
         }
         else
         {
            initEnterFrameShow();
         }
         m_versionTxt = m_show["versionTxt"];
         m_versionTxt.text = Part1.getInstance().getVersionControl().getShowVersionStr();
         hideMengBan();
      }
      
      private function initEnterFrameShow() : void
      {
         clearFrameShow();
         m_showMC.gotoAndStop("enterStep^stop^");
         m_enterGameBtn = new ButtonLogicShell();
         m_enterGameBtn.setShow(m_show["enterGameBtn"]);
      }
      
      private function initMainStepShow() : void
      {
         clearFrameShow();
         if(m_show.currentFrameLabel == "enterStep^stop^")
         {
            m_soundManager.play2(m_startGameSoundData.getName(),m_startGameSoundData.getSwfPath(),m_startGameSoundData.getClassName(),0,-1);
         }
         m_showMC.gotoAndPlay((m_show.currentFrame + 1).toString());
      }
      
      private function initNewGameStepShow() : void
      {
         clearFrameShow();
         m_showMC.gotoAndPlay((m_show.currentFrame + 1).toString());
      }
      
      private function initMainStepShow2() : void
      {
         m_newSaveBtn = new ButtonLogicShell();
         m_getSaveListBtn = new ButtonLogicShell();
         m_aboutUsBtn = new ButtonLogicShell();
         m_upDateBtn = new ButtonLogicShell();
         m_gotoForumBtn = new ButtonLogicShell();
         m_newSaveBtn.setShow(m_show["newSaveBtn"]);
         m_getSaveListBtn.setShow(m_show["getSaveListBtn"]);
         m_aboutUsBtn.setShow(m_show["aboutUsBtn"]);
         m_upDateBtn.setShow(m_show["updataBtn"]);
         m_gotoForumBtn.setShow(m_show["forumBtn"]);
      }
      
      private function initNewGameStepShow2() : void
      {
         m_onePlayerBtn = new ButtonLogicShell();
         m_twoPlayerBtn = new ButtonLogicShell();
         m_returnBtn = new ButtonLogicShell();
         m_onePlayerBtn.setShow(m_show["playerBtn_1"]);
         m_twoPlayerBtn.setShow(m_show["playerBtn_2"]);
         m_returnBtn.setShow(m_show["returnFromNewGameBtn"]);
      }
      
      private function clearFrameShow() : void
      {
         ClearUtil.clearObject(m_enterGameBtn);
         m_enterGameBtn = null;
         ClearUtil.clearObject(m_onePlayerBtn);
         m_onePlayerBtn = null;
         ClearUtil.clearObject(m_twoPlayerBtn);
         m_twoPlayerBtn = null;
         ClearUtil.clearObject(m_returnBtn);
         m_returnBtn = null;
         ClearUtil.clearObject(m_newSaveBtn);
         m_newSaveBtn = null;
         ClearUtil.clearObject(m_getSaveListBtn);
         m_getSaveListBtn = null;
         ClearUtil.clearObject(m_aboutUsBtn);
         m_aboutUsBtn = null;
         ClearUtil.clearObject(m_upDateBtn);
         m_upDateBtn = null;
         ClearUtil.clearObject(m_gotoForumBtn);
         m_gotoForumBtn = null;
      }
      
      private function showMCReachFrameLabel(param1:String) : void
      {
         switch(param1)
         {
            case "mainStep^stop^":
               initMainStepShow2();
               break;
            case "newGameStepEnd^stop^":
               m_showMC.gotoAndPlay("startToMainStep");
               m_mengBan = m_show["mengBan"];
               hideMengBan();
               break;
            case "newGameStep^stop^":
               initNewGameStepShow2();
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:String = null;
         var _loc5_:int = 0;
         var _loc4_:SaveFileData = null;
         var _loc6_:* = param1.button;
         loop1:
         switch(_loc6_)
         {
            case m_enterGameBtn:
               m_logAPI.logIn();
               break;
            case m_newSaveBtn:
               initNewGameStepShow();
               break;
            case m_returnBtn:
               initMainStepShow();
               break;
            case m_onePlayerBtn:
            case m_twoPlayerBtn:
               m_currentSelectBtn = param1.button;
               m_choicePlayerPanel = new ChoicePlayerPanel();
               m_choicePlayerPanel.addChoicePlayerPanelListener(m_choicePlayerPanelListener);
               m_show.addChild(m_choicePlayerPanel);
               switch(param1.button)
               {
                  case m_onePlayerBtn:
                     m_choicePlayerPanel.setChoicePlayerNumToOne();
                     break;
                  case m_twoPlayerBtn:
                     m_choicePlayerPanel.setChoicePlayerNumToTwo();
               }
               m_choicePlayerPanel.init();
               break;
            case m_getSaveListBtn:
               m_currentSelectBtn = param1.button;
               showMengBan();
               m_saveAPI.getList();
               break;
            case m_aboutUsBtn:
               m_currentSelectBtn = param1.button;
               showMengBan();
               m_myLoader.getClass("NewGameFolder/GameStartPanel.swf","AboutUsUI",getShowSuccess,getShowFail,null,null,null,null,false);
               m_myLoader.load();
               break;
            case m_upDateBtn:
               openUpdatePanel();
               break;
            default:
               if((m_aboutUsPanel ? m_aboutUsPanelQuitBtn : null) !== _loc6_)
               {
                  if((m_updatePanel ? m_updatePanelQuitBtn : null) !== _loc6_)
                  {
                     switch(_loc6_)
                     {
                        case m_showSaveFileDatasPanel ? m_showSaveFileDatasPanelQuitBtn : null:
                           clearSaveFileDatasPanel();
                           break loop1;
                        case m_appealWarningSureBtn:
                        case m_appealWarningCancelBtn:
                           break;
                        case m_coverSaveWarningCancelBtn:
                        case m_coverSaveWarningSureBtn:
                           if(param1.button == m_coverSaveWarningSureBtn)
                           {
                              startNewGame();
                           }
                           closeCoverSaveWarningShow();
                           break loop1;
                        case m_gotoForumBtn:
                           navigateToURL(new URLRequest("http://my.4399.com/forums-mtag-tagid-81260.html"),"_blank");
                           break loop1;
                        case m_updateMoreBtn:
                           _loc2_ = String(Part1.getInstance().getConfigXML().@updateMoreUrl);
                           if(_loc2_)
                           {
                              navigateToURL(new URLRequest(_loc2_),"_blank");
                           }
                     }
                     if(param1.button == m_appealWarningSureBtn)
                     {
                        navigateToURL(new URLRequest("http://app.my.4399.com/r.php?app=feedback"),"_blank");
                     }
                     closeAppealWarningShow();
                     break;
                  }
                  clearUpdatePanel();
                  break;
               }
               clearAboutUsPanel();
               break;
         }
         var _loc3_:int = m_showSaveFileDatasCols ? m_showSaveFileDatasCols.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(param1.button == m_showSaveFileDatasCols[_loc5_])
            {
               _loc4_ = m_showSaveFileDatasCols[_loc5_].extraData as SaveFileData;
               if(m_choicePlayerPanel)
               {
                  m_coverSaveDataIndex = _loc5_ + 1;
                  if(_loc4_)
                  {
                     showCoverSaveWarningShow();
                  }
                  else
                  {
                     startNewGame();
                  }
                  return;
               }
               if(_loc4_)
               {
                  if(_loc4_.status == 2)
                  {
                     return;
                  }
                  if(_loc4_.status == 1)
                  {
                     showAppealWarningShow();
                     return;
                  }
                  showMengBan();
                  m_saveAPI.getData(false,_loc5_ + 1);
               }
               break;
            }
            _loc5_++;
         }
      }
      
      private function startNewGame() : void
      {
         var _loc1_:SaveFileData = new SaveFileData();
         _loc1_.dateAndTime = new TimeUtil().getTimeStr();
         _loc1_.index = m_coverSaveDataIndex;
         _loc1_.status = 0;
         _loc1_.title = "";
         GameData.getInstance().isNewGame = true;
         GameData.getInstance().setSaveFileData(_loc1_);
         startGame(m_player1Str,m_player2Str,null,m_coverSaveDataIndex,true);
      }
      
      private function showCoverSaveWarningShow() : void
      {
         if(m_coverSaveWarningShow == null)
         {
            m_coverSaveWarningShow = MyFunction2.returnShowByClassName("OverWriteSaveTip") as MovieClip;
            addChild(m_coverSaveWarningShow);
            m_coverSaveWarningSureBtn = new ButtonLogicShell();
            m_coverSaveWarningSureBtn.setShow(m_coverSaveWarningShow["yesBtn"]);
            m_coverSaveWarningCancelBtn = new ButtonLogicShell();
            m_coverSaveWarningCancelBtn.setShow(m_coverSaveWarningShow["noBtn"]);
         }
      }
      
      private function closeCoverSaveWarningShow() : void
      {
         ClearUtil.clearObject(m_coverSaveWarningShow);
         m_coverSaveWarningShow = null;
         ClearUtil.clearObject(m_coverSaveWarningSureBtn);
         m_coverSaveWarningSureBtn = null;
         ClearUtil.clearObject(m_coverSaveWarningCancelBtn);
         m_coverSaveWarningCancelBtn = null;
      }
      
      private function showAppealWarningShow() : void
      {
         if(m_appealWarningShow == null)
         {
            m_appealWarningShow = MyFunction2.returnShowByClassName("TitleACTip") as MovieClip;
            addChild(m_appealWarningShow);
            m_appealWarningSureBtn = new ButtonLogicShell();
            m_appealWarningSureBtn.setShow(m_appealWarningShow["yesBtn"]);
            m_appealWarningCancelBtn = new ButtonLogicShell();
            m_appealWarningCancelBtn.setShow(m_appealWarningShow["noBtn"]);
         }
      }
      
      private function closeAppealWarningShow() : void
      {
         ClearUtil.clearObject(m_appealWarningShow);
         m_appealWarningShow = null;
         ClearUtil.clearObject(m_appealWarningSureBtn);
         m_appealWarningSureBtn = null;
         ClearUtil.clearObject(m_appealWarningCancelBtn);
         m_appealWarningCancelBtn = null;
      }
      
      private function loginSuccess(param1:LoginReturnData, param2:LogAPI) : void
      {
         if(param1 == null)
         {
            throw new Error("返回的loginReturnData 为null");
         }
         GameData.getInstance().setLoginReturnData(param1);
         initMainStepShow();
         openUpdatePanel();
      }
      
      private function logOutSuccess(param1:LogAPI) : void
      {
      }
      
      private function getSaveFileListSuccess(param1:Vector.<SaveFileData>) : void
      {
         hideMengBan();
         m_saveFileDatas = param1;
         m_myLoader.getClass("NewGameFolder/GameStartPanel.swf","SaveGameUIChoose",getShowSuccess,getShowFail,null,null,null,null,false);
         m_myLoader.load();
      }
      
      private function getSaveFileSuccess(param1:SaveFileData) : void
      {
         GameData.getInstance().setSaveFileData(param1);
         hideMengBan();
         var _loc2_:PlayerTypeDataInSaveXML = SaveXMLFunction.getPlayerTypeDataFromSaveXML(param1.saveXML);
         var _loc3_:LastSaveData = new LastSaveData();
         _loc3_.initData(param1.saveXML.Data[0].@uid,param1.saveXML.Data[0].@idx,param1.saveXML.Data[0].@ver);
         GameData.getInstance().setLastSaveData(_loc3_);
         startGame(_loc2_.getPlayer1Type(),_loc2_.getPlayer2Type(),param1.saveXML,param1.index,false);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
         _loc3_ = null;
      }
      
      private function netGetError() : void
      {
         hideMengBan();
      }
      
      private function choicePlayerComplete(param1:ChoicePlayerPanel, param2:String, param3:String) : void
      {
         m_player1Str = param2;
         m_player2Str = param3;
         showMengBan();
         m_saveAPI.getList();
      }
      
      private function showMengBan() : void
      {
         addChild(m_mengBan);
      }
      
      private function hideMengBan() : void
      {
         if(Boolean(m_mengBan) && m_mengBan.parent)
         {
            m_mengBan.parent.removeChild(m_mengBan);
         }
      }
      
      private function quitChoicePlayerComplete(param1:ChoicePlayerPanel) : void
      {
         ClearUtil.clearObject(m_choicePlayerPanel);
         m_choicePlayerPanel = null;
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         hideMengBan();
         var _loc2_:Class = param1.resultClass;
         switch(param1.wantClassName)
         {
            case "GameStartUI":
               m_show = new _loc2_();
               addChild(m_show);
               initShow();
               Part1.getInstance().hideGameWaitShow();
               break;
            case "AboutUsUI":
               initAboutUsPanel(_loc2_);
               break;
            case "UpdataUI":
               initUpdatePanel(_loc2_);
               break;
            case "SaveGameUIChoose":
               initSaveFileDatasPanel(_loc2_);
         }
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         hideMengBan();
      }
      
      private function initAboutUsPanel(param1:Class) : void
      {
         if(m_aboutUsPanel == null)
         {
            m_aboutUsPanel = new MovieClipPlayLogicShell();
            m_aboutUsPanel.setShow(new param1());
         }
         m_show.addChild(m_aboutUsPanel.getShow());
         m_aboutUsPanelQuitBtn = new ButtonLogicShell();
         m_aboutUsPanelQuitBtn.setShow(m_aboutUsPanel.getShow()["aboutUsBackBtn"]);
      }
      
      private function clearAboutUsPanel() : void
      {
         if(m_aboutUsPanel)
         {
            ClearUtil.clearObject(m_aboutUsPanel.getShow());
         }
         ClearUtil.clearObject(m_aboutUsPanel);
         m_aboutUsPanel = null;
         ClearUtil.clearObject(m_aboutUsPanelQuitBtn);
         m_aboutUsPanelQuitBtn = null;
      }
      
      private function openUpdatePanel() : void
      {
         showMengBan();
         m_myLoader.getClass("NewGameFolder/GameStartPanel.swf","UpdataUI",getShowSuccess,getShowFail,null,null,null,null,false);
         m_myLoader.load();
      }
      
      private function initUpdatePanel(param1:Class) : void
      {
         if(m_updatePanel == null)
         {
            m_updatePanel = new MovieClipPlayLogicShell();
            m_updatePanel.setShow(new param1());
         }
         m_show.addChild(m_updatePanel.getShow());
         m_updatePanelQuitBtn = new ButtonLogicShell();
         m_updatePanelQuitBtn.setShow(m_updatePanel.getShow()["quitBtn"]);
         m_updateMoreBtn = new ButtonLogicShell();
         m_updateMoreBtn.setShow(m_updatePanel.getShow()["UpdataMoreInfoBtn"]);
      }
      
      private function clearUpdatePanel() : void
      {
         if(m_updatePanel)
         {
            ClearUtil.clearObject(m_updatePanel.getShow());
         }
         ClearUtil.clearObject(m_updatePanel);
         m_updatePanel = null;
         ClearUtil.clearObject(m_updatePanelQuitBtn);
         m_updatePanelQuitBtn = null;
         ClearUtil.clearObject(m_updateMoreBtn);
         m_updateMoreBtn = null;
      }
      
      private function initSaveFileDatasPanel(param1:Class) : void
      {
         var _loc3_:ButtonLogicShell2 = null;
         var _loc2_:String = null;
         var _loc4_:int = 0;
         m_showSaveFileDatasPanel = new AbleDragSpriteLogicShell();
         m_showSaveFileDatasPanel.setShow(new param1() as Sprite);
         m_showSaveFileDatasPanelQuitBtn = new ButtonLogicShell();
         m_showSaveFileDatasPanelQuitBtn.setShow(m_showSaveFileDatasPanel.getShow()["saveGameBackBtn"]);
         m_show.addChild(m_showSaveFileDatasPanel.getShow());
         m_showSaveFileDatasCols = new Vector.<ButtonLogicShell2>();
         _loc4_ = 1;
         while(_loc4_ < 7)
         {
            _loc3_ = new ButtonLogicShell2();
            _loc3_.setShow(m_showSaveFileDatasPanel.getShow()["save_" + _loc4_]);
            MyFunction2.changeTextFieldFont(m_font.fontName,_loc3_.getShow()["numText"]);
            _loc3_.getShow()["numText"].text = _loc4_;
            _loc2_ = m_saveFileDatas[_loc4_] && m_saveFileDatas[_loc4_].title ? m_saveFileDatas[_loc4_].title : "";
            if(m_saveFileDatas[_loc4_])
            {
               if(m_saveFileDatas[_loc4_].status == 1)
               {
                  _loc2_ += "\n（临时封）";
               }
               else if(m_saveFileDatas[_loc4_].status == 2)
               {
                  _loc2_ += "\n（永久封）";
               }
            }
            _loc2_ += "\n";
            _loc2_ += m_saveFileDatas[_loc4_] && m_saveFileDatas[_loc4_].dateAndTime ? m_saveFileDatas[_loc4_].dateAndTime : "";
            MyFunction2.changeTextFieldFont(m_font.fontName,_loc3_.getShow()["infoText"]);
            _loc3_.getShow()["infoText"].text = _loc2_;
            _loc3_.extraData = m_saveFileDatas[_loc4_];
            m_showSaveFileDatasCols.push(_loc3_);
            _loc4_++;
         }
      }
      
      private function clearSaveFileDatasPanel() : void
      {
         ClearUtil.clearObject(m_showSaveFileDatasPanelQuitBtn);
         m_showSaveFileDatasPanelQuitBtn = null;
         ClearUtil.nullArr(m_showSaveFileDatasCols);
         m_showSaveFileDatasCols = null;
         if(m_showSaveFileDatasPanel)
         {
            ClearUtil.clearObject(m_showSaveFileDatasPanel.getShow());
         }
         ClearUtil.clearObject(m_showSaveFileDatasPanel);
         m_showSaveFileDatasPanel = null;
      }
      
      private function startGame(param1:String, param2:String, param3:XML, param4:int, param5:Boolean) : void
      {
         var _loc8_:int = 0;
         var _loc6_:Vector.<IStartGamePanelListener> = m_startGamePanelListeners.slice(0);
         var _loc7_:int = int(_loc6_.length);
         _loc8_ = 0;
         while(_loc8_ < _loc7_)
         {
            _loc6_[_loc8_].startGame(this,param1,param2,param3,param4,param5);
            _loc6_[_loc8_] = null;
            _loc8_++;
         }
         _loc6_.length = 0;
         _loc6_ = null;
      }
   }
}

