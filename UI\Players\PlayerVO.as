package UI.Players
{
   import UI.DanMedicinePanel.DanMedicineFunction;
   import UI.DetectionClass.DetectionClass;
   import UI.EquipmentMakeAndUpgrade.EqMagicVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.HatchPanel.HatchVO;
   import UI.InitUI;
   import UI.MainLineTask.TaskDetectors.IPlayerVOForTaskDetector_Player;
   import UI.MyFunction2;
   import UI.Pets.Pet;
   import UI.Players.ImplicitProPlayer.ImplicitProPlayer;
   import UI.ShiTu.IXiuLianTargetVO;
   import UI.ShiTu.TuDiVO;
   import UI.ShiTu.XiuLianContent;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountVO;
   import UI2.Mount.MountData.MountsVO;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.Utils.ClearUtil;
   import flash.utils.Dictionary;
   
   public class PlayerVO implements IXiuLianTargetVO, IPlayerVOForTaskDetector_Player
   {
      
      public static const PLAYER_ONE:String = "playerOne";
      
      public static const PLAYER_TWO:String = "playerTwo";
      
      public var name:String;
      
      public var pet:Pet;
      
      public var player:Player;
      
      public var playerType:String;
      
      public var playerUid:String;
      
      public var isShowFashionShow:Boolean = true;
      
      public var playerID:String;
      
      private var _isInvincible:Boolean;
      
      private var _isVampire:Boolean;
      
      private var _endInvincibleTime:Number;
      
      private var _endVampireTime:Number;
      
      private var _medal:EquipmentVO;
      
      private var _inforEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _attackDanMedicineEquipmentVOGrid:Vector.<Vector.<EquipmentVO>>;
      
      private var _defenceDanMedicineEquipmentVOGrid:Vector.<Vector.<EquipmentVO>>;
      
      private var _packageEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _storageEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _storageEquipmentXml:XML;
      
      private var _medalEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _skillVOs:Vector.<SkillVO>;
      
      private var _xiuLianContents:Vector.<XiuLianContent>;
      
      private var _xiuLianContentsByContent:Dictionary;
      
      private var _implicitProPlayers:Object;
      
      private var _experiencePercent:Number;
      
      private var _bloodPercent:Number;
      
      private var _magicPercent:Number;
      
      private var _extraAddExperienceRate:int;
      
      private var _extraAddMoneyRate:int;
      
      private var _nuQiAllValue:int;
      
      private var _nuQiCurrentValue:int;
      
      private var _tuDiVO:TuDiVO;
      
      private var _hatchVO:HatchVO;
      
      private var _eqMagicVO:EqMagicVO;
      
      private var _shiFuVO:ShiFuVO;
      
      private var _automaticPetVO:AutomaticPetVO;
      
      private var _automaticBackPetVO:AutomaticPetVO;
      
      private var _mountsVO:MountsVO;
      
      private var _mountVO:MountVO;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      private var _level:int;
      
      private var _money:int;
      
      private var _criticalRate:int;
      
      private var _baseCriticalRate:int;
      
      private var _xiuLianCriticalRate:int;
      
      private var _maxMagic:int;
      
      private var _baseMaxMagic:int;
      
      private var _eqScore:Number = 0;
      
      private var _baseBloodVolume:int;
      
      private var _petAddBloodVolume:int;
      
      private var _suitAddBloodVolume:int;
      
      private var _xiuLianBloodVolume:int;
      
      private var _otherBloodVolume:int;
      
      private var _addBloodRate:Number = 0;
      
      private var _addDownDef:Number = 0;
      
      private var _addAvgShanbi:Number = 0;
      
      private var _addAvgShanbi2:Number = 0;
      
      private var _addAvgBaoji:Number = 0;
      
      private var _addAvgBaoji2:Number = 0;
      
      private var _addChangeRp:Number = 0;
      
      private var _increaseMingzhong:Number = 0;
      
      private var _increaseMingzhong2:Number = 0;
      
      private var _addSbRate:Number = 0;
      
      private var _addBjRate:Number = 0;
      
      private var _addMzRate:Number = 0;
      
      private var _addGjRate:Number = 0;
      
      private var _addFbRate:Number = 0;
      
      private var _addDoubleExp:Number = 0;
      
      private var _addAvgMagic:Number = 0;
      
      private var _addHitPet:Number = 0;
      
      private var _addDownHp:Number = 0;
      
      private var _addDwon:Boolean = false;
      
      private var _bloodVolume:int;
      
      private var _experienceVolume:int;
      
      private var _renPin1:int;
      
      private var _otherRenPin:int;
      
      private var _renPinDan:int;
      
      private var _renPin_foreverFashionAdd:int;
      
      private var _addRenPinRate:Number = 0;
      
      private var _addRate:Number = 0;
      
      private var _addHit:Number = 0;
      
      private var _blood_FashionAdd:int;
      
      private var _magic_FashionAdd:int;
      
      private var _regmp_FashionAdd:int;
      
      private var _reghp_FashionAdd:int;
      
      private var _attack_FashionAdd:int;
      
      private var _defence_FashionAdd:int;
      
      private var _hit_FashionAdd:Number;
      
      private var _addMingzhongPet:Number;
      
      private var _dodge_FashionAdd:Number;
      
      private var _criticalRate_FashionAdd:int;
      
      private var _viotValue_FashionAdd:Number;
      
      private var _offensiveValue_FashionAdd:int;
      
      private var _renPin_MagicAdd:int;
      
      private var _blood_MagicAdd:int;
      
      private var _magic_MagicAdd:int;
      
      private var _regmp_MagicAdd:int;
      
      private var _reghp_MagicAdd:int;
      
      private var _attack_MagicAdd:int;
      
      private var _defence_MagicAdd:int;
      
      private var _hit_MagicAdd:Number;
      
      private var _dodge_MagicAdd:Number;
      
      private var _dodge_PetPassiveAdd:Number;
      
      private var _criticalRate_MagicAdd:int;
      
      private var _viotValue_MagicAdd:Number;
      
      private var _offensiveValue_MagicAdd:int;
      
      private var _regMp:int;
      
      private var _baseRegMp:int;
      
      private var _otherRegMp:int;
      
      private var _baseRegHp:int;
      
      private var _otherRegHp:int;
      
      private var _regHp:int;
      
      private var _baseHit:Number;
      
      private var _xiuLianHit:Number;
      
      private var _hit:Number;
      
      private var _otherHit:Number;
      
      private var _baseDodge:Number;
      
      private var _otherDodge:Number;
      
      private var _xiuLianDodge:Number;
      
      private var _dodge:Number;
      
      private var _baseOffensiveValue:int;
      
      private var _otherOffensiveValue:int;
      
      private var _offensiveValue:int;
      
      private var _baseRiotValue:Number;
      
      private var _xiuLianRiotValue:Number;
      
      private var _riotValue:Number;
      
      private var _PetPressSiveRegHp:Number;
      
      private var _petLowHp:Number;
      
      private var _PetPressSiveKuangBao:Number;
      
      private var _PetPressSiveXueNu:Number;
      
      private var _petLowXueNu:Number;
      
      private var _baseAttack:int;
      
      private var _weaponAttack:int;
      
      private var _suitAttack:int;
      
      private var _petAddAttack:int;
      
      private var _forceDanAttack:int;
      
      private var _xiuLianAttack:int;
      
      private var _otherAttack:int;
      
      private var _attack:int;
      
      private var _baseDefence:int;
      
      private var _clothesDefence:int;
      
      private var _suitDefence:int;
      
      private var _petAddDefence:int;
      
      private var _xiuLianDefence:int;
      
      private var _otherDefence:int;
      
      private var _defence:int;
      
      private var _otherRiotValue:Number;
      
      private var _otherCriticalRate:int;
      
      private var _otherMaxMagic:int;
      
      private var _addMagicRate:Number = 0;
      
      public function PlayerVO()
      {
         super();
         init();
         _implicitProPlayers = {};
         _antiwear.storageEquipmentXml = _storageEquipmentXml;
      }
      
      public function set(param1:String, param2:String) : void
      {
         _antiwear[param1] = param2;
      }
      
      public function set2(param1:String, param2:Number) : void
      {
         try
         {
            _antiwear[param1] = param2;
         }
         catch(error:Error)
         {
         }
      }
      
      public function get(param1:String) : String
      {
         var _loc2_:String = null;
         try
         {
            _loc2_ = _antiwear[param1];
         }
         catch(e:Error)
         {
            _loc2_ = "";
         }
         return _loc2_ ? _loc2_ : "";
      }
      
      public function get2(param1:String) : Number
      {
         var _loc2_:Number = NaN;
         try
         {
            _loc2_ = Number(_antiwear[param1]);
         }
         catch(e:Error)
         {
            _loc2_ = 0;
         }
         return _loc2_;
      }
      
      public function getImplicitProPlayer(param1:String) : ImplicitProPlayer
      {
         return _implicitProPlayers[param1];
      }
      
      public function isHavePet() : Boolean
      {
         if(Boolean(pet) && pet.petEquipmentVO)
         {
            return true;
         }
         return false;
      }
      
      public function addImplicitProPlayer(param1:ImplicitProPlayer) : void
      {
         if(_implicitProPlayers[param1.getId()])
         {
            throw new Error();
         }
         param1.judgeAndProPlayer(this);
         _implicitProPlayers[param1.getId()] = param1;
      }
      
      public function clearImplicitProPlayer() : void
      {
         for each(var _loc1_ in _implicitProPlayers)
         {
            _loc1_.recoverPlayer(this);
         }
         ClearUtil.clearObject(_implicitProPlayers);
         _implicitProPlayers = {};
      }
      
      public function get vertigoValue() : Number
      {
         return get2("automaticPetVertigo");
      }
      
      public function get crushedValue() : Number
      {
         return get2("automaticPetCrushed");
      }
      
      public function get invincibleCD() : Number
      {
         return get2("automaticPetInvincible");
      }
      
      public function get vampireCD() : Number
      {
         return get2("automaticPetVampireCD");
      }
      
      public function get vampireDuration() : Number
      {
         return get2("automaticPetVampireDuration");
      }
      
      public function get vampireBloodLimit() : Number
      {
         return get2("automaticPetVampireBloodLimit");
      }
      
      public function get vampirePercent() : Number
      {
         return get2("automaticPetVampirePercent");
      }
      
      public function get rebornOdds() : Number
      {
         return get2("automaticPetRebornOdds");
      }
      
      public function get rebornPercent() : Number
      {
         return get2("automaticPetRebornPercent");
      }
      
      public function get rebornCD() : Number
      {
         return get2("automaticPetRebornCD");
      }
      
      public function get riotValue() : Number
      {
         return (baseRiotValue + xiuLianRiotValue + Number(get("riot_gemAdd")) * (AntiwearNumber.nums["consts_1"] / AntiwearNumber.nums["consts_100"]) + otherRiotValue + viotValue_FashionAdd + viotValue_MagicAdd + Number(get("riot_petAdd2")) + Number(get("riot_mountAdd1"))) * (AntiwearNumber.nums["consts_1"] + addFbRate) - Number(get("sub_fangbao"));
      }
      
      public function get attack() : int
      {
         var _loc2_:Number = baseAttack + weaponAttack + suitAttack + danMedicineAttack + petAddAttack + forceDanAttack + xiuLianAttack + mountsAddAttack + otherAttack + attack_FashionAdd + attack_MagicAdd + int(get("attack_petAdd2")) + int(get("attack_gemAdd")) + int(get("attack_suitAdd")) + get2("attack_mountAdd1");
         var _loc1_:Number = Number(bloodPercent < petLowXueNu / AntiwearNumber.nums["consts_100"] ? int(_loc2_ * (PetPressSiveXueNu / AntiwearNumber.nums["consts_100"])) : AntiwearNumber.nums["consts_0"]);
         _loc2_ += _loc1_;
         _loc2_ *= AntiwearNumber.nums["consts_1"] + addGjRate;
         return _loc2_ + addAvgMagic;
      }
      
      public function get defence() : int
      {
         var _loc1_:Number = (renPin1 + renPinDan + otherRenPin + renPin_foreverFashionAdd + renPin_MagicAdd + int(get("renPin_petAdd2")) + int(get("renPin_gemAdd"))) * (AntiwearNumber.nums["consts_1"] + addRenPinRate);
         return (baseDefence + clothesDefence + suitDefence + danMedicineDefence + petAddDefence + xiuLianDefence + mountsAddDefence + otherDefence + defence_FashionAdd + defence_MagicAdd + int(get("defence_petAdd2")) + int(get("defence_gemAdd")) + int(get("defence_suitAdd")) + get2("defence_mountAdd1")) * (addDownDef > 0 ? AntiwearNumber.nums["consts_1"] - addDownDef : AntiwearNumber.nums["consts_1"]) + (addChangeRp > 0 ? _loc1_ : AntiwearNumber.nums["consts_0"]) - Number(get("sub_def"));
      }
      
      public function get bloodVolume() : int
      {
         return (baseBloodVolume + petAddBloodVolume + suitAddBloodVolume + xiuLianBloodVolume + mountsAddBloodVolume + otherBloodVolume + blood_FashionAdd + blood_MagicAdd + int(get("blood_petAdd2")) + int(get("blood_gemAdd")) + int(get("blood_suitAdd")) + int(get("blood_PKUp")) + get2("blood_mountAdd1")) * (AntiwearNumber.nums["consts_1"] + addBloodRate);
      }
      
      public function getPetAddTargetBloodVolume() : int
      {
         return bloodVolume - mountsAddBloodVolume;
      }
      
      public function get maxMagic() : int
      {
         return (baseMaxMagic + otherMaxMagic + magic_FashionAdd + magic_MagicAdd + int(get("magic_petAdd2")) + int(get("magic_gemAdd"))) * (AntiwearNumber.nums["consts_1"] + addMagicRate);
      }
      
      public function getPetAddTargetMaxMagic() : int
      {
         return maxMagic;
      }
      
      public function get regHp() : Number
      {
         return baseRegHp + reghp_FashionAdd + otherRegHp + reghp_MagicAdd + Number(get("reBlood_petAdd2")) + get2("regHp_mountAdd1") + PetPressSiveRegHp;
      }
      
      public function get regMp() : Number
      {
         return baseRegMp + regmp_FashionAdd + otherRegMp + regmp_MagicAdd + Number(get("reMagic_petAdd2")) + get2("regMp_mountAdd1");
      }
      
      public function get offensiveValue() : int
      {
         return baseOffensiveValue + otherOffensiveValue + offensiveValue_FashionAdd + offensiveValue_MagicAdd;
      }
      
      public function get dodge() : Number
      {
         var _loc1_:Number = NaN;
         if(addAvgShanbi / 100 * (addAvgShanbi2 > 0 ? defence / addAvgShanbi2 : 0) > 0.1)
         {
            _loc1_ = AntiwearNumber.nums["consts_10"] / AntiwearNumber.nums["consts_100"];
         }
         else
         {
            _loc1_ = addAvgShanbi / AntiwearNumber.nums["consts_100"] * (addAvgShanbi2 > 0 ? defence / addAvgShanbi2 : AntiwearNumber.nums["consts_0"]);
         }
         return (baseDodge + xiuLianDodge + otherDodge + dodge_FashionAdd + dodge_MagicAdd + dodge_PetPassiveAdd + Number(get("dodge_petAdd2")) + Number(get("dodge_gemAdd")) * (AntiwearNumber.nums["consts_1"] / AntiwearNumber.nums["consts_100"]) + Number(get("dodge_mountAdd1"))) * (AntiwearNumber.nums["consts_1"] + addSbRate) + _loc1_ - Number(get("sub_shanbi"));
      }
      
      public function get hit() : Number
      {
         return (baseHit + xiuLianHit + otherHit + hit_FashionAdd + hit_MagicAdd + Number(get("hit_petAdd2")) + Number(get("hit_gemAdd")) * (AntiwearNumber.nums["consts_1"] / AntiwearNumber.nums["consts_100"]) + get2("hit_mountAdd1")) * (AntiwearNumber.nums["consts_1"] + addMzRate) + addHit * (AntiwearNumber.nums["consts_1"] / AntiwearNumber.nums["consts_100"]) + (addHitPet > 0 ? addHitPet * (AntiwearNumber.nums["consts_1"] / AntiwearNumber.nums["consts_100"]) : AntiwearNumber.nums["consts_0"]) + Number(get("add_mingzhong")) + addMingzhongPet;
      }
      
      public function get criticalRate() : int
      {
         return (baseCriticalRate + xiuLianCriticalRate + otherCriticalRate + criticalRate_FashionAdd + addRate + Number(get("criticalRate_gemAdd")) + criticalRate_MagicAdd + int(get("criticalRate_petAdd2")) + get2("criticalRate_mountAdd1") + PetPressSiveKuangBao) * (AntiwearNumber.nums["consts_1"] + addBjRate) + (addAvgBaoji2 > 0 ? attack / addAvgBaoji2 : AntiwearNumber.nums["consts_0"]) * addAvgBaoji;
      }
      
      public function get renPin() : int
      {
         return (renPin1 + renPinDan + otherRenPin + renPin_foreverFashionAdd + renPin_MagicAdd + int(get("renPin_petAdd2")) + int(get("renPin_gemAdd"))) * (AntiwearNumber.nums["consts_1"] + addRenPinRate) * (addChangeRp > 0 ? AntiwearNumber.nums["consts_0"] : AntiwearNumber.nums["consts_1"]);
      }
      
      public function get nuQiAllValue() : int
      {
         return _antiwear.nuQiAllValue;
      }
      
      public function set nuQiAllValue(param1:int) : void
      {
         _antiwear.nuQiAllValue = param1;
      }
      
      public function get nuQiCurrentValue() : int
      {
         return _antiwear.nuQiCurrentValue;
      }
      
      public function set nuQiCurrentValue(param1:int) : void
      {
         if(param1 < 0)
         {
            param1 = 0;
         }
         else if(param1 > nuQiAllValue)
         {
            param1 = nuQiAllValue;
         }
         _antiwear.nuQiCurrentValue = param1;
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.level = _level;
         _antiwear.money = _money;
         _antiwear.criticalRate = _criticalRate;
         _antiwear.xiuLianCriticalRate = _xiuLianCriticalRate;
         _antiwear.otherCriticalRate = _otherCriticalRate;
         _antiwear.baseCriticalRate = _baseCriticalRate;
         _antiwear.maxMagic = _maxMagic;
         _antiwear.otherMaxMagic = _otherMaxMagic;
         _antiwear.baseMaxMagic = _baseMaxMagic;
         _antiwear.addMagicRate = _addMagicRate;
         _antiwear.eqScore = _eqScore = 0;
         _antiwear.baseBloodVolume = _baseBloodVolume;
         _antiwear.petAddBloodVolume = _petAddBloodVolume;
         _antiwear.suitAddBloodVolume = _suitAddBloodVolume;
         _antiwear.xiuLianBloodVolume = _xiuLianBloodVolume;
         _antiwear.otherBloodVolume = _otherBloodVolume;
         _antiwear.addBloodRate = _addBloodRate;
         _antiwear.addDownDef = _addDownDef;
         _antiwear.addAvgShanbi = _addAvgShanbi;
         _antiwear.addAvgShanbi2 = _addAvgShanbi2;
         _antiwear.addAvgBaoji = _addAvgBaoji;
         _antiwear.addAvgBaoji2 = _addAvgBaoji2;
         _antiwear.bloodVolume = _bloodVolume;
         _antiwear.addFbRate = _addFbRate;
         _antiwear.addDoubleExp = _addDoubleExp;
         _antiwear.addAvgMagic = _addAvgMagic;
         _antiwear.addHitPet = _addHitPet;
         _antiwear.addDownHp = _addDownHp;
         _antiwear.addDown = _addDwon;
         _antiwear.addSbRate = _addSbRate;
         _antiwear.addChangeRp = _addChangeRp;
         _antiwear.increaseMingzhong = _increaseMingzhong;
         _antiwear.increaseMingzhong2 = _increaseMingzhong2;
         _antiwear.addBjRate = _addBjRate;
         _antiwear.addMzRate = _addMzRate;
         _antiwear.addGjRate = _addGjRate;
         _antiwear.experienceVolume = _experienceVolume;
         _antiwear.renPin1 = _renPin1;
         _antiwear.otherRenPin = _otherRenPin;
         _antiwear.addRenPinRate = _addRenPinRate;
         _antiwear.renPinDan = _renPinDan;
         _antiwear.renPin_foreverFashionAdd = _renPin_foreverFashionAdd;
         _antiwear.addRate = _addRate;
         _antiwear.addHit = _addHit;
         _antiwear.blood_FashionAdd = _blood_FashionAdd;
         _antiwear.magic_FashionAdd = _magic_FashionAdd;
         _antiwear.regmp_FashionAdd = _regmp_FashionAdd;
         _antiwear.reghp_FashionAdd = _reghp_FashionAdd;
         _antiwear.attack_FashionAdd = _attack_FashionAdd;
         _antiwear.defence_FashionAdd = _defence_FashionAdd;
         _antiwear.hit_FashionAdd = _hit_FashionAdd;
         _antiwear.addMingzhongPet = _addMingzhongPet;
         _antiwear.dodge_FashionAdd = _dodge_FashionAdd;
         _antiwear.criticalRate_FashionAdd = _criticalRate_FashionAdd;
         _antiwear.viotValue_FashionAdd = _viotValue_FashionAdd;
         _antiwear.offensiveValue_FashionAdd = _offensiveValue_FashionAdd;
         _antiwear.renPin_MagicAdd = _renPin_MagicAdd;
         _antiwear.blood_MagicAdd = _blood_MagicAdd;
         _antiwear.magic_MagicAdd = _magic_MagicAdd;
         _antiwear.regmp_MagicAdd = _regmp_MagicAdd;
         _antiwear.reghp_MagicAdd = _reghp_MagicAdd;
         _antiwear.attack_MagicAdd = _attack_MagicAdd;
         _antiwear.defence_MagicAdd = _defence_MagicAdd;
         _antiwear.hit_MagicAdd = _hit_MagicAdd;
         _antiwear.dodge_MagicAdd = _dodge_MagicAdd;
         _antiwear.dodge_PetPassiveAdd = _dodge_PetPassiveAdd;
         _antiwear.criticalRate_MagicAdd = _criticalRate_MagicAdd;
         _antiwear.viotValue_MagicAdd = _viotValue_MagicAdd;
         _antiwear.offensiveValue_MagicAdd = _offensiveValue_MagicAdd;
         _antiwear.experiencePercent = _experiencePercent;
         _antiwear.bloodPercent = _bloodPercent;
         _antiwear.magicPercent = _magicPercent;
         _antiwear.isInvincible = _isInvincible;
         _antiwear.isVampire = _isVampire;
         _antiwear.endInvincibleTime = _endInvincibleTime;
         _antiwear.endVampireTime = _endVampireTime;
         _antiwear.extraAddExperienceRate = _extraAddExperienceRate;
         _antiwear.extraAddMoneyRate = _extraAddMoneyRate;
         _antiwear.rebornCDClearTime = 0;
         _antiwear.regMp = _regMp;
         _antiwear.baseRegMp = _baseRegMp;
         _antiwear.otherRegMp = _otherRegMp;
         _antiwear.baseRegHp = _baseRegHp;
         _antiwear.otherRegHp = _otherRegHp;
         _antiwear.regHp = _regHp;
         _antiwear.PetPressSiveRegHp = _PetPressSiveRegHp = 0;
         _antiwear.petLowHp = _petLowHp = 0;
         _antiwear.PetPressSiveKuangBao = _PetPressSiveKuangBao = 0;
         _antiwear.PetPressSiveXueNu = _PetPressSiveXueNu = 0;
         _antiwear.petLowXueNu = _petLowXueNu = 0;
         _antiwear.baseHit = _baseHit = 0;
         _antiwear.xiuLianHit = _xiuLianHit = 0;
         _antiwear.otherHit = _otherHit = 0;
         _antiwear.hit = _hit = 0;
         _antiwear.baseDodge = _baseDodge = 0;
         _antiwear.otherDodge = _otherDodge = 0;
         _antiwear.xiuLianDodge = _xiuLianDodge = 0;
         _antiwear.dodge = _dodge = 0;
         _antiwear.baseOffensiveValue = _baseOffensiveValue;
         _antiwear.otherOffensiveValue = _otherOffensiveValue;
         _antiwear.offensiveValue = _offensiveValue;
         _antiwear.baseRiotValue = _baseRiotValue = 0;
         _antiwear.xiuLianRiotValue = _xiuLianRiotValue = 0;
         _antiwear.otherRiotValue = _otherRiotValue = 0;
         _antiwear.riotValue = _riotValue;
         _antiwear.baseAttack = _baseAttack;
         _antiwear.weaponAttack = _weaponAttack;
         _antiwear.suitAttack = _suitAttack;
         _antiwear.petAddAttack = _petAddAttack;
         _antiwear.forceDanAttack = _forceDanAttack;
         _antiwear.xiuLianAttack = _xiuLianAttack;
         _antiwear.otherAttack = _otherAttack;
         _antiwear.attack = _attack;
         _antiwear.baseDefence = _baseDefence;
         _antiwear.clothesDefence = _clothesDefence;
         _antiwear.suitDefence = _suitDefence;
         _antiwear.petAddDefence = _petAddDefence;
         _antiwear.xiuLianDefence = _xiuLianDefence;
         _antiwear.otherDefence = _otherDefence;
         _antiwear.defence = _defence;
         _antiwear.nuQiAllValue = _nuQiAllValue;
         _antiwear.nuQiCurrentValue = _nuQiCurrentValue;
      }
      
      public function get baseAttack() : int
      {
         return _antiwear.baseAttack;
      }
      
      public function set baseAttack(param1:int) : void
      {
         _antiwear.baseAttack = param1;
      }
      
      public function get weaponAttack() : int
      {
         return _antiwear.weaponAttack;
      }
      
      public function set weaponAttack(param1:int) : void
      {
         _antiwear.weaponAttack = param1;
      }
      
      public function get suitAttack() : int
      {
         return _antiwear.suitAttack;
      }
      
      public function set suitAttack(param1:int) : void
      {
         _antiwear.suitAttack = param1;
      }
      
      public function get danMedicineAttack() : int
      {
         return DanMedicineFunction.getInstance().calculationTotalValue(_attackDanMedicineEquipmentVOGrid);
      }
      
      public function get petAddAttack() : int
      {
         return _antiwear.petAddAttack;
      }
      
      public function set petAddAttack(param1:int) : void
      {
         _antiwear.petAddAttack = param1;
      }
      
      public function get forceDanAttack() : int
      {
         return _antiwear.forceDanAttack;
      }
      
      public function set forceDanAttack(param1:int) : void
      {
         _antiwear.forceDanAttack = param1;
      }
      
      public function get xiuLianAttack() : int
      {
         return _antiwear.xiuLianAttack;
      }
      
      public function set xiuLianAttack(param1:int) : void
      {
         _antiwear.xiuLianAttack = param1;
      }
      
      public function get mountsAddAttack() : int
      {
         return _mountsVO ? _mountsVO.getTotalAddAttackOfMountVOs() : 0;
      }
      
      public function get otherAttack() : int
      {
         return _antiwear.otherAttack;
      }
      
      public function set otherAttack(param1:int) : void
      {
         _antiwear.otherAttack = param1;
      }
      
      public function get baseDefence() : int
      {
         return _antiwear.baseDefence;
      }
      
      public function set baseDefence(param1:int) : void
      {
         _antiwear.baseDefence = param1;
      }
      
      public function get clothesDefence() : int
      {
         return _antiwear.clothesDefence;
      }
      
      public function set clothesDefence(param1:int) : void
      {
         _antiwear.clothesDefence = param1;
      }
      
      public function get suitDefence() : int
      {
         return _antiwear.suitDefence;
      }
      
      public function set suitDefence(param1:int) : void
      {
         _antiwear.suitDefence = param1;
      }
      
      public function get danMedicineDefence() : int
      {
         return DanMedicineFunction.getInstance().calculationTotalValue(_defenceDanMedicineEquipmentVOGrid);
      }
      
      public function get petAddDefence() : int
      {
         return _antiwear.petAddDefence;
      }
      
      public function set petAddDefence(param1:int) : void
      {
         _antiwear.petAddDefence = param1;
      }
      
      public function get xiuLianDefence() : int
      {
         return _antiwear.xiuLianDefence;
      }
      
      public function set xiuLianDefence(param1:int) : void
      {
         _antiwear.xiuLianDefence = param1;
      }
      
      public function get mountsAddDefence() : int
      {
         return _mountsVO ? _mountsVO.getTotalAddDefenceOfMountVOs() : 0;
      }
      
      public function get otherDefence() : int
      {
         return _antiwear.otherDefence;
      }
      
      public function set otherDefence(param1:int) : void
      {
         _antiwear.otherDefence = param1;
      }
      
      public function get xiuLianRiotValue() : Number
      {
         return _antiwear.xiuLianRiotValue;
      }
      
      public function set xiuLianRiotValue(param1:Number) : void
      {
         _antiwear.xiuLianRiotValue = param1;
      }
      
      public function get otherRiotValue() : Number
      {
         return _antiwear.otherRiotValue;
      }
      
      public function set otherRiotValue(param1:Number) : void
      {
         _antiwear.otherRiotValue = param1;
      }
      
      public function get baseRiotValue() : Number
      {
         return _antiwear.baseRiotValue;
      }
      
      public function set baseRiotValue(param1:Number) : void
      {
         _antiwear.baseRiotValue = param1;
      }
      
      public function get baseOffensiveValue() : int
      {
         return _antiwear.baseOffensiveValue;
      }
      
      public function set baseOffensiveValue(param1:int) : void
      {
         _antiwear.baseOffensiveValue = param1;
      }
      
      public function get otherOffensiveValue() : int
      {
         return _antiwear.otherOffensiveValue;
      }
      
      public function set otherOffensiveValue(param1:int) : void
      {
         _antiwear.otherOffensiveValue = param1;
      }
      
      public function get baseDodge() : Number
      {
         return _antiwear.baseDodge;
      }
      
      public function set baseDodge(param1:Number) : void
      {
         _antiwear.baseDodge = param1;
      }
      
      public function get otherDodge() : Number
      {
         return _antiwear.otherDodge;
      }
      
      public function set otherDodge(param1:Number) : void
      {
         _antiwear.otherDodge = param1;
      }
      
      public function get xiuLianDodge() : Number
      {
         return _antiwear.xiuLianDodge;
      }
      
      public function set xiuLianDodge(param1:Number) : void
      {
         _antiwear.xiuLianDodge = param1;
      }
      
      public function get xiuLianHit() : Number
      {
         return _antiwear.xiuLianHit;
      }
      
      public function set xiuLianHit(param1:Number) : void
      {
         _antiwear.xiuLianHit = param1;
      }
      
      public function get otherHit() : Number
      {
         return _antiwear.otherHit;
      }
      
      public function set otherHit(param1:Number) : void
      {
         _antiwear.otherHit = param1;
      }
      
      public function get baseHit() : Number
      {
         return _antiwear.baseHit;
      }
      
      public function set baseHit(param1:Number) : void
      {
         _antiwear.baseHit = param1;
      }
      
      public function get baseRegHp() : Number
      {
         return _antiwear.baseRegHp;
      }
      
      public function set baseRegHp(param1:Number) : void
      {
         _antiwear.baseRegHp = param1;
      }
      
      public function get otherRegHp() : Number
      {
         return _antiwear.otherRegHp;
      }
      
      public function set otherRegHp(param1:Number) : void
      {
         _antiwear.otherRegHp = param1;
      }
      
      public function get PetPressSiveRegHp() : Number
      {
         return bloodPercent < petLowHp / AntiwearNumber.nums["consts_100"] ? int(bloodVolume * _antiwear.PetPressSiveRegHp / AntiwearNumber.nums["consts_100"]) : AntiwearNumber.nums["consts_0"];
      }
      
      public function set PetPressSiveRegHp(param1:Number) : void
      {
         _antiwear.PetPressSiveRegHp = param1;
      }
      
      public function get PetPressSiveKuangBao() : Number
      {
         return AntiwearNumber.nums["consts_100"] - int(bloodPercent * AntiwearNumber.nums["consts_100"]) < _antiwear.PetPressSiveKuangBao ? AntiwearNumber.nums["consts_100"] - int(bloodPercent * AntiwearNumber.nums["consts_100"]) : _antiwear.PetPressSiveKuangBao;
      }
      
      public function getKuangbaomax() : Number
      {
         return _antiwear.PetPressSiveKuangBao;
      }
      
      public function set PetPressSiveKuangBao(param1:Number) : void
      {
         _antiwear.PetPressSiveKuangBao = param1;
      }
      
      public function get PetPressSiveXueNu() : Number
      {
         return _antiwear.PetPressSiveXueNu;
      }
      
      public function set PetPressSiveXueNu(param1:Number) : void
      {
         _antiwear.PetPressSiveXueNu = param1;
      }
      
      public function get petLowXueNu() : Number
      {
         return _antiwear.petLowXueNu;
      }
      
      public function set petLowXueNu(param1:Number) : void
      {
         _antiwear.petLowXueNu = param1;
      }
      
      public function get petLowHp() : Number
      {
         return _antiwear.petLowHp;
      }
      
      public function set petLowHp(param1:Number) : void
      {
         _antiwear.petLowHp = param1;
      }
      
      public function get baseRegMp() : Number
      {
         return _antiwear.baseRegMp;
      }
      
      public function set baseRegMp(param1:Number) : void
      {
         _antiwear.baseRegMp = param1;
      }
      
      public function get otherRegMp() : Number
      {
         return _antiwear.otherRegMp;
      }
      
      public function set otherRegMp(param1:Number) : void
      {
         _antiwear.otherRegMp = param1;
      }
      
      public function get level() : int
      {
         return _antiwear.level;
      }
      
      public function set level(param1:int) : void
      {
         _antiwear.level = param1;
         if(player)
         {
            player.changeData();
         }
      }
      
      public function get money() : int
      {
         return _antiwear.money;
      }
      
      public function set money(param1:int) : void
      {
         if(param1 <= XMLSingle.getInstance().maxMoney)
         {
            _antiwear.money = param1;
         }
         else if(param1 <= XMLSingle.getInstance().maxMoney + XMLSingle.getInstance().maxDxValue_Money)
         {
            _antiwear.money = XMLSingle.getInstance().maxMoney;
         }
      }
      
      public function get otherCriticalRate() : int
      {
         return _antiwear.otherCriticalRate;
      }
      
      public function set otherCriticalRate(param1:int) : void
      {
         _antiwear.otherCriticalRate = param1;
      }
      
      public function get baseCriticalRate() : int
      {
         return _antiwear.baseCriticalRate;
      }
      
      public function set baseCriticalRate(param1:int) : void
      {
         _antiwear.baseCriticalRate = param1;
      }
      
      public function get xiuLianCriticalRate() : int
      {
         return _antiwear.xiuLianCriticalRate;
      }
      
      public function set xiuLianCriticalRate(param1:int) : void
      {
         _antiwear.xiuLianCriticalRate = param1;
      }
      
      public function get baseMaxMagic() : int
      {
         return _antiwear.baseMaxMagic;
      }
      
      public function set baseMaxMagic(param1:int) : void
      {
         _antiwear.baseMaxMagic = param1;
      }
      
      public function get otherMaxMagic() : int
      {
         return _antiwear.otherMaxMagic;
      }
      
      public function set otherMaxMagic(param1:int) : void
      {
         _antiwear.otherMaxMagic = param1;
      }
      
      public function get addMagicRate() : Number
      {
         return _antiwear.addMagicRate;
      }
      
      public function set addMagicRate(param1:Number) : void
      {
         _antiwear.addMagicRate = param1;
      }
      
      public function get eqScore() : Number
      {
         return _antiwear.eqScore;
      }
      
      public function set eqScore(param1:Number) : void
      {
         if(param1 < 0)
         {
            _antiwear.eqScore = 0;
         }
         else if(param1 > 4.99)
         {
            _antiwear.eqScore = 4.99;
         }
         else
         {
            _antiwear.eqScore = param1;
         }
      }
      
      public function get baseBloodVolume() : int
      {
         return _antiwear.baseBloodVolume;
      }
      
      public function set baseBloodVolume(param1:int) : void
      {
         _antiwear.baseBloodVolume = param1;
      }
      
      public function get petAddBloodVolume() : int
      {
         return _antiwear.petAddBloodVolume;
      }
      
      public function set petAddBloodVolume(param1:int) : void
      {
         _antiwear.petAddBloodVolume = param1;
      }
      
      public function get suitAddBloodVolume() : int
      {
         return _antiwear.suitAddBloodVolume;
      }
      
      public function set suitAddBloodVolume(param1:int) : void
      {
         _antiwear.suitAddBloodVolume = param1;
      }
      
      public function get xiuLianBloodVolume() : int
      {
         return _antiwear.xiuLianBloodVolume;
      }
      
      public function set xiuLianBloodVolume(param1:int) : void
      {
         _antiwear.xiuLianBloodVolume = param1;
      }
      
      public function get mountsAddBloodVolume() : int
      {
         if(!_mountsVO)
         {
         }
         return _mountsVO ? _mountsVO.getTotalAddHpOfMountVOs() : 0;
      }
      
      public function get otherBloodVolume() : int
      {
         return _antiwear.otherBloodVolume;
      }
      
      public function set otherBloodVolume(param1:int) : void
      {
         _antiwear.otherBloodVolume = param1;
      }
      
      public function get addBloodRate() : Number
      {
         return _antiwear.addBloodRate;
      }
      
      public function set addBloodRate(param1:Number) : void
      {
         _antiwear.addBloodRate = param1;
      }
      
      public function get addFbRate() : Number
      {
         return _antiwear.addFbRate;
      }
      
      public function set addFbRate(param1:Number) : void
      {
         _antiwear.addFbRate = param1;
      }
      
      public function get addSbRate() : Number
      {
         return _antiwear.addSbRate;
      }
      
      public function set addSbRate(param1:Number) : void
      {
         _antiwear.addSbRate = param1;
      }
      
      public function get addBjRate() : Number
      {
         return _antiwear.addBjRate;
      }
      
      public function set addBjRate(param1:Number) : void
      {
         _antiwear.addBjRate = param1;
      }
      
      public function get addMzRate() : Number
      {
         return _antiwear.addMzRate;
      }
      
      public function set addMzRate(param1:Number) : void
      {
         _antiwear.addMzRate = param1;
      }
      
      public function get addGjRate() : Number
      {
         return _antiwear.addGjRate;
      }
      
      public function set addGjRate(param1:Number) : void
      {
         _antiwear.addGjRate = param1;
      }
      
      public function get addDoubleExp() : Number
      {
         return _antiwear.addDoubleExp;
      }
      
      public function set addDoubleExp(param1:Number) : void
      {
         _antiwear.addDoubleExp = param1;
      }
      
      public function get addAvgMagic() : Number
      {
         return _antiwear.addAvgMagic;
      }
      
      public function set addAvgMagic(param1:Number) : void
      {
         _antiwear.addAvgMagic = param1;
      }
      
      public function get addHitPet() : Number
      {
         return _antiwear.addHitPet;
      }
      
      public function set addHitPet(param1:Number) : void
      {
         _antiwear.addHitPet = param1;
      }
      
      public function get addAvgShanbi() : Number
      {
         return _antiwear.addAvgShanbi;
      }
      
      public function set addAvgShanbi(param1:Number) : void
      {
         _antiwear.addAvgShanbi = param1;
      }
      
      public function get addAvgShanbi2() : Number
      {
         return _antiwear.addAvgShanbi2;
      }
      
      public function set addAvgShanbi2(param1:Number) : void
      {
         _antiwear.addAvgShanbi2 = param1;
      }
      
      public function get addDownHp() : Number
      {
         return _antiwear.addDownHp;
      }
      
      public function set addDownHp(param1:Number) : void
      {
         _antiwear.addDownHp = param1;
      }
      
      public function get addDownDef() : Number
      {
         return _antiwear.addDownDef;
      }
      
      public function set addDownDef(param1:Number) : void
      {
         _antiwear.addDownDef = param1;
      }
      
      public function get addAvgBaoji() : Number
      {
         return _antiwear.addAvgBaoji;
      }
      
      public function set addAvgBaoji(param1:Number) : void
      {
         _antiwear.addAvgBaoji = param1;
      }
      
      public function get addAvgBaoji2() : Number
      {
         return _antiwear.addAvgBaoji2;
      }
      
      public function set addAvgBaoji2(param1:Number) : void
      {
         _antiwear.addAvgBaoji2 = param1;
      }
      
      public function get addChangeRp() : Number
      {
         return _antiwear.addChangeRp;
      }
      
      public function set addChangeRp(param1:Number) : void
      {
         _antiwear.addChangeRp = param1;
      }
      
      public function get increaseMingzhong() : Number
      {
         return _antiwear.increaseMingzhong;
      }
      
      public function set increaseMingzhong(param1:Number) : void
      {
         _antiwear.increaseMingzhong = param1;
      }
      
      public function get increaseMingzhong2() : Number
      {
         return _antiwear.increaseMingzhong2;
      }
      
      public function set increaseMingzhong2(param1:Number) : void
      {
         _antiwear.increaseMingzhong2 = param1;
      }
      
      public function getAntiwear() : Antiwear
      {
         return _antiwear;
      }
      
      public function get addDown() : Boolean
      {
         return _antiwear.addDown;
      }
      
      public function set addDown(param1:Boolean) : void
      {
         _antiwear.addDown = param1;
      }
      
      public function get experienceVolume() : int
      {
         return _antiwear.experienceVolume;
      }
      
      public function set experienceVolume(param1:int) : void
      {
         _antiwear.experienceVolume = param1;
      }
      
      public function get renPin1() : int
      {
         return _antiwear.renPin1;
      }
      
      public function set renPin1(param1:int) : void
      {
         _antiwear.renPin1 = param1;
      }
      
      public function get otherRenPin() : int
      {
         return _antiwear.otherRenPin;
      }
      
      public function set otherRenPin(param1:int) : void
      {
         _antiwear.otherRenPin = param1;
      }
      
      public function get renPinDan() : int
      {
         return _antiwear.renPinDan;
      }
      
      public function set renPinDan(param1:int) : void
      {
         _antiwear.renPinDan = param1;
      }
      
      public function get addRenPinRate() : Number
      {
         return _antiwear.addRenPinRate;
      }
      
      public function set addRenPinRate(param1:Number) : void
      {
         _antiwear.addRenPinRate = param1;
      }
      
      public function get addRate() : Number
      {
         return _antiwear.addRate;
      }
      
      public function set addRate(param1:Number) : void
      {
         _antiwear.addRate = param1;
      }
      
      public function get addHit() : Number
      {
         return _antiwear.addHit;
      }
      
      public function set addHit(param1:Number) : void
      {
         _antiwear.addHit = param1;
      }
      
      public function get renPin_foreverFashionAdd() : int
      {
         return _antiwear.renPin_foreverFashionAdd;
      }
      
      public function set renPin_foreverFashionAdd(param1:int) : void
      {
         _antiwear.renPin_foreverFashionAdd = param1;
      }
      
      public function get blood_FashionAdd() : int
      {
         return _antiwear.blood_FashionAdd;
      }
      
      public function set blood_FashionAdd(param1:int) : void
      {
         _antiwear.blood_FashionAdd = param1;
      }
      
      public function get magic_FashionAdd() : int
      {
         return _antiwear.magic_FashionAdd;
      }
      
      public function set magic_FashionAdd(param1:int) : void
      {
         _antiwear.magic_FashionAdd = param1;
      }
      
      public function get regmp_FashionAdd() : int
      {
         return _antiwear.regmp_FashionAdd;
      }
      
      public function set regmp_FashionAdd(param1:int) : void
      {
         _antiwear.regmp_FashionAdd = param1;
      }
      
      public function get reghp_FashionAdd() : int
      {
         return _antiwear.reghp_FashionAdd;
      }
      
      public function set reghp_FashionAdd(param1:int) : void
      {
         _antiwear.reghp_FashionAdd = param1;
      }
      
      public function get attack_FashionAdd() : int
      {
         return _antiwear.attack_FashionAdd;
      }
      
      public function set attack_FashionAdd(param1:int) : void
      {
         _antiwear.attack_FashionAdd = param1;
      }
      
      public function get defence_FashionAdd() : int
      {
         return _antiwear.defence_FashionAdd;
      }
      
      public function set defence_FashionAdd(param1:int) : void
      {
         _antiwear.defence_FashionAdd = param1;
      }
      
      public function get hit_FashionAdd() : Number
      {
         return _antiwear.hit_FashionAdd;
      }
      
      public function set hit_FashionAdd(param1:Number) : void
      {
         _antiwear.hit_FashionAdd = param1;
      }
      
      public function get addMingzhongPet() : Number
      {
         return _antiwear.addMingzhongPet;
      }
      
      public function set addMingzhongPet(param1:Number) : void
      {
         _antiwear.addMingzhongPet = param1;
      }
      
      public function get dodge_FashionAdd() : Number
      {
         return _antiwear.dodge_FashionAdd;
      }
      
      public function set dodge_FashionAdd(param1:Number) : void
      {
         _antiwear.dodge_FashionAdd = param1;
      }
      
      public function get criticalRate_FashionAdd() : int
      {
         return _antiwear.criticalRate_FashionAdd;
      }
      
      public function set criticalRate_FashionAdd(param1:int) : void
      {
         _antiwear.criticalRate_FashionAdd = param1;
      }
      
      public function get viotValue_FashionAdd() : Number
      {
         return _antiwear.viotValue_FashionAdd;
      }
      
      public function set viotValue_FashionAdd(param1:Number) : void
      {
         _antiwear.viotValue_FashionAdd = param1;
      }
      
      public function get offensiveValue_FashionAdd() : int
      {
         return _antiwear.offensiveValue_FashionAdd;
      }
      
      public function set offensiveValue_FashionAdd(param1:int) : void
      {
         _antiwear.offensiveValue_FashionAdd = param1;
      }
      
      public function clear_FashionAdd() : void
      {
         blood_FashionAdd = 0;
         magic_FashionAdd = 0;
         regmp_FashionAdd = 0;
         reghp_FashionAdd = 0;
         attack_FashionAdd = 0;
         defence_FashionAdd = 0;
         hit_FashionAdd = 0;
         dodge_FashionAdd = 0;
         criticalRate_FashionAdd = 0;
         viotValue_FashionAdd = 0;
         offensiveValue_FashionAdd = 0;
      }
      
      public function get renPin_MagicAdd() : int
      {
         return _antiwear.renPin_MagicAdd;
      }
      
      public function set renPin_MagicAdd(param1:int) : void
      {
         _antiwear.renPin_MagicAdd = param1;
      }
      
      public function get blood_MagicAdd() : int
      {
         return _antiwear.blood_MagicAdd;
      }
      
      public function set blood_MagicAdd(param1:int) : void
      {
         _antiwear.blood_MagicAdd = param1;
      }
      
      public function get magic_MagicAdd() : int
      {
         return _antiwear.magic_MagicAdd;
      }
      
      public function set magic_MagicAdd(param1:int) : void
      {
         _antiwear.magic_MagicAdd = param1;
      }
      
      public function get regmp_MagicAdd() : int
      {
         return _antiwear.regmp_MagicAdd;
      }
      
      public function set regmp_MagicAdd(param1:int) : void
      {
         _antiwear.regmp_MagicAdd = param1;
      }
      
      public function get reghp_MagicAdd() : int
      {
         return _antiwear.reghp_MagicAdd;
      }
      
      public function set reghp_MagicAdd(param1:int) : void
      {
         _antiwear.reghp_MagicAdd = param1;
      }
      
      public function get attack_MagicAdd() : int
      {
         return _antiwear.attack_MagicAdd;
      }
      
      public function set attack_MagicAdd(param1:int) : void
      {
         _antiwear.attack_MagicAdd = param1;
      }
      
      public function get defence_MagicAdd() : int
      {
         return _antiwear.defence_MagicAdd;
      }
      
      public function set defence_MagicAdd(param1:int) : void
      {
         _antiwear.defence_MagicAdd = param1;
      }
      
      public function get hit_MagicAdd() : Number
      {
         return _antiwear.hit_MagicAdd;
      }
      
      public function set hit_MagicAdd(param1:Number) : void
      {
         _antiwear.hit_MagicAdd = param1;
      }
      
      public function get dodge_MagicAdd() : Number
      {
         return _antiwear.dodge_MagicAdd;
      }
      
      public function set dodge_MagicAdd(param1:Number) : void
      {
         _antiwear.dodge_MagicAdd = param1;
      }
      
      public function get dodge_PetPassiveAdd() : Number
      {
         return _antiwear.dodge_PetPassiveAdd;
      }
      
      public function set dodge_PetPassiveAdd(param1:Number) : void
      {
         _antiwear.dodge_PetPassiveAdd = param1;
      }
      
      public function get criticalRate_MagicAdd() : int
      {
         return _antiwear.criticalRate_MagicAdd;
      }
      
      public function set criticalRate_MagicAdd(param1:int) : void
      {
         _antiwear.criticalRate_MagicAdd = param1;
      }
      
      public function get viotValue_MagicAdd() : Number
      {
         return _antiwear.viotValue_MagicAdd;
      }
      
      public function set viotValue_MagicAdd(param1:Number) : void
      {
         _antiwear.viotValue_MagicAdd = param1;
      }
      
      public function get offensiveValue_MagicAdd() : int
      {
         return _antiwear.offensiveValue_MagicAdd;
      }
      
      public function set offensiveValue_MagicAdd(param1:int) : void
      {
         _antiwear.offensiveValue_MagicAdd = param1;
      }
      
      public function clear_MagicAdd() : void
      {
         renPin_MagicAdd = 0;
         blood_MagicAdd = 0;
         magic_MagicAdd = 0;
         regmp_MagicAdd = 0;
         reghp_MagicAdd = 0;
         attack_MagicAdd = 0;
         defence_MagicAdd = 0;
         hit_MagicAdd = 0;
         dodge_MagicAdd = 0;
         dodge_PetPassiveAdd = 0;
         addMingzhongPet = 0;
         criticalRate_MagicAdd = 0;
         viotValue_MagicAdd = 0;
         offensiveValue_MagicAdd = 0;
         petLowHp = 0;
         PetPressSiveRegHp = 0;
         PetPressSiveKuangBao = 0;
         PetPressSiveXueNu = 0;
         petLowXueNu = 0;
      }
      
      public function get isInvincible() : Boolean
      {
         return _antiwear.isInvincible;
      }
      
      public function set isInvincible(param1:Boolean) : void
      {
         _antiwear.isInvincible = param1;
         player.changeData();
      }
      
      public function get isVampire() : Boolean
      {
         return _antiwear.isVampire;
      }
      
      public function set isVampire(param1:Boolean) : void
      {
         _antiwear.isVampire = param1;
         player.changeData();
      }
      
      public function get endInvincibleTime() : Number
      {
         return _antiwear.endInvincibleTime;
      }
      
      public function set endInvincibleTime(param1:Number) : void
      {
         _antiwear.endInvincibleTime = param1;
      }
      
      public function get endVampireTime() : Number
      {
         return _antiwear.endVampireTime;
      }
      
      public function set endVampireTime(param1:Number) : void
      {
         _antiwear.endVampireTime = param1;
      }
      
      public function get RebornCDClearTime() : Number
      {
         return _antiwear.rebornCDClearTime;
      }
      
      public function set RebornCDClearTime(param1:Number) : void
      {
         _antiwear.rebornCDClearTime = param1;
      }
      
      public function get extraAddExperienceRate() : int
      {
         return _antiwear.extraAddExperienceRate;
      }
      
      public function set extraAddExperienceRate(param1:int) : void
      {
         _antiwear.extraAddExperienceRate = param1;
      }
      
      public function get extraAddMoneyRate() : int
      {
         return _antiwear.extraAddMoneyRate;
      }
      
      public function set extraAddMoneyRate(param1:int) : void
      {
         _antiwear.extraAddMoneyRate = param1;
      }
      
      public function get experiencePercent() : Number
      {
         return _antiwear.experiencePercent;
      }
      
      public function set experiencePercent(param1:Number) : void
      {
         if(param1 < 0)
         {
            _antiwear.experiencePercent = 0;
         }
         else if(param1 > 1)
         {
            _antiwear.experiencePercent = 1;
         }
         else
         {
            _antiwear.experiencePercent = param1;
         }
      }
      
      public function get storageEquipmentXml() : XML
      {
         return _antiwear.storageEquipmentXml;
      }
      
      public function set storageEquipmentXml(param1:XML) : void
      {
         _antiwear.storageEquipmentXml = param1;
      }
      
      public function get bloodPercent() : Number
      {
         return _antiwear.bloodPercent;
      }
      
      public function set bloodPercent(param1:Number) : void
      {
         if(param1 < 0)
         {
            _antiwear.bloodPercent = 0;
         }
         else if(param1 > 1)
         {
            _antiwear.bloodPercent = 1;
         }
         else
         {
            _antiwear.bloodPercent = param1;
         }
      }
      
      public function getCurrentMagic() : Number
      {
         return magicPercent * maxMagic;
      }
      
      public function addMagic(param1:uint) : void
      {
         magicPercent = (magicPercent * maxMagic + param1) / maxMagic;
      }
      
      public function decMagic(param1:uint) : void
      {
         magicPercent = (magicPercent * maxMagic - param1) / maxMagic;
      }
      
      public function get magicPercent() : Number
      {
         return _antiwear.magicPercent;
      }
      
      public function set magicPercent(param1:Number) : void
      {
         if(param1 < 0)
         {
            _antiwear.magicPercent = 0;
         }
         else if(param1 > 1)
         {
            _antiwear.magicPercent = 1;
         }
         else
         {
            _antiwear.magicPercent = param1;
         }
      }
      
      public function get medal() : EquipmentVO
      {
         return _medal;
      }
      
      public function set medal(param1:EquipmentVO) : void
      {
         _medal = param1;
      }
      
      public function get inforEquipmentVOs() : Vector.<EquipmentVO>
      {
         return _inforEquipmentVOs;
      }
      
      public function set inforEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _inforEquipmentVOs = param1;
      }
      
      public function get attackDanMedicineEquipmentVOGrid() : Vector.<Vector.<EquipmentVO>>
      {
         return _attackDanMedicineEquipmentVOGrid;
      }
      
      public function set attackDanMedicineEquipmentVOGrid(param1:Vector.<Vector.<EquipmentVO>>) : void
      {
         _attackDanMedicineEquipmentVOGrid = param1;
      }
      
      public function get defenceDanMedicineEquipmentVOGrid() : Vector.<Vector.<EquipmentVO>>
      {
         return _defenceDanMedicineEquipmentVOGrid;
      }
      
      public function set defenceDanMedicineEquipmentVOGrid(param1:Vector.<Vector.<EquipmentVO>>) : void
      {
         _defenceDanMedicineEquipmentVOGrid = param1;
      }
      
      public function get packageEquipmentVOs() : Vector.<EquipmentVO>
      {
         return _packageEquipmentVOs;
      }
      
      public function set packageEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _packageEquipmentVOs = param1;
      }
      
      public function get storageEquipmentVOs() : Vector.<EquipmentVO>
      {
         if(storageEquipmentXml && !_storageEquipmentVOs)
         {
            if(!GamingUI.getInstance().getNewestTimeStrFromSever())
            {
               _storageEquipmentVOs = InitUI.getInstance().getEquipmentVOs(storageEquipmentXml,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,"");
            }
            else
            {
               _storageEquipmentVOs = InitUI.getInstance().getEquipmentVOs(storageEquipmentXml,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
               DetectionClass.getInstance().detectionEquipmentVos(_storageEquipmentVOs);
            }
            storageEquipmentXml = null;
         }
         return _storageEquipmentVOs;
      }
      
      public function set storageEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _storageEquipmentVOs = param1;
      }
      
      public function get medalEquipmentVOs() : Vector.<EquipmentVO>
      {
         return _medalEquipmentVOs;
      }
      
      public function set medalEquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _medalEquipmentVOs = param1;
      }
      
      public function get skillVOs() : Vector.<SkillVO>
      {
         return _skillVOs;
      }
      
      public function set skillVOs(param1:Vector.<SkillVO>) : void
      {
         _skillVOs = param1;
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
         player = null;
         medal = null;
         if(_inforEquipmentVOs)
         {
            for each(var _loc5_ in _inforEquipmentVOs)
            {
               _loc5_ = null;
            }
         }
         if(_packageEquipmentVOs)
         {
            for each(var _loc2_ in _packageEquipmentVOs)
            {
               _loc2_ = null;
            }
         }
         if(_storageEquipmentVOs)
         {
            for each(var _loc1_ in _storageEquipmentVOs)
            {
               _loc1_ = null;
            }
         }
         if(_medalEquipmentVOs)
         {
            for each(var _loc4_ in _medalEquipmentVOs)
            {
               _loc4_ = null;
            }
         }
         if(_skillVOs)
         {
            for each(var _loc3_ in _skillVOs)
            {
               _skillVOs = null;
            }
         }
         _inforEquipmentVOs = null;
         _packageEquipmentVOs = null;
         _storageEquipmentVOs = null;
         _medalEquipmentVOs = null;
         _skillVOs = null;
         ClearUtil.clearObject(_implicitProPlayers);
         _implicitProPlayers = null;
         ClearUtil.clearObject(_tuDiVO);
         _tuDiVO = null;
         ClearUtil.clearObject(_hatchVO);
         _hatchVO = null;
         ClearUtil.clearObject(_eqMagicVO);
         _eqMagicVO = null;
         ClearUtil.clearObject(_shiFuVO);
         _shiFuVO = null;
         if(_automaticPetVO)
         {
            _automaticPetVO.setPlayerVO(null);
         }
         _automaticPetVO = null;
         _automaticBackPetVO = null;
         _mountsVO = null;
         if(_mountVO)
         {
            _mountVO.setPlayerVO(null);
         }
         _mountVO = null;
      }
      
      public function get tuDiVO() : TuDiVO
      {
         return _tuDiVO;
      }
      
      public function set tuDiVO(param1:TuDiVO) : void
      {
         if(_tuDiVO)
         {
            _tuDiVO.playerVO = null;
         }
         _tuDiVO = param1;
         if(_tuDiVO)
         {
            _tuDiVO.playerVO = this;
         }
      }
      
      public function get automaticPetVO() : AutomaticPetVO
      {
         return _automaticPetVO;
      }
      
      public function get automaticBackPetVO() : AutomaticPetVO
      {
         return _automaticBackPetVO;
      }
      
      public function set automaticBackPetVO(param1:AutomaticPetVO) : void
      {
         _automaticBackPetVO = param1;
      }
      
      public function set automaticPetVO(param1:AutomaticPetVO) : void
      {
         if(_automaticPetVO == param1 && _automaticPetVO.partnerUid == param1.partnerUid)
         {
            return;
         }
         if(_automaticPetVO)
         {
            _automaticPetVO.setPlayerVO(null);
         }
         _automaticPetVO = param1;
         if(_automaticPetVO)
         {
            _automaticPetVO.setPlayerVO(this);
         }
         if(_automaticPetVO && _automaticPetVO.partnerUid && GamingUI.getInstance().getAutomaticPetsData())
         {
            _automaticBackPetVO = GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(_automaticPetVO.partnerUid,_automaticPetVO.partnerName);
            if(_automaticBackPetVO)
            {
               _automaticBackPetVO.setPlayerVO(null);
            }
         }
         else
         {
            _automaticBackPetVO = null;
         }
         player.changeData();
      }
      
      public function get mountVO() : MountVO
      {
         return _mountVO;
      }
      
      public function set mountVO(param1:MountVO) : void
      {
         if(_mountVO == param1)
         {
            return;
         }
         if(_mountVO)
         {
            _mountVO.setPlayerVO(null);
         }
         _mountVO = param1;
         if(_mountVO)
         {
            _mountVO.setPlayerVO(this);
         }
         player.changeData();
      }
      
      public function setMountsVO(param1:MountsVO) : void
      {
         _mountsVO = param1;
      }
      
      public function get shiFuVO() : ShiFuVO
      {
         return _shiFuVO;
      }
      
      public function set shiFuVO(param1:ShiFuVO) : void
      {
         _shiFuVO = param1;
      }
      
      public function get hatchVO() : HatchVO
      {
         return _hatchVO;
      }
      
      public function set hatchVO(param1:HatchVO) : void
      {
         if(_hatchVO)
         {
            _hatchVO.playerVO = null;
         }
         _hatchVO = param1;
         if(_hatchVO)
         {
            _hatchVO.playerVO = this;
         }
      }
      
      public function get eqMagicVO() : EqMagicVO
      {
         return _eqMagicVO;
      }
      
      public function set eqMagicVO(param1:EqMagicVO) : void
      {
         if(_eqMagicVO)
         {
            _eqMagicVO.playerVO = null;
         }
         _eqMagicVO = param1;
         if(_eqMagicVO)
         {
            _eqMagicVO.playerVO = this;
         }
      }
      
      public function get xiuLianContents() : Vector.<XiuLianContent>
      {
         return _xiuLianContents;
      }
      
      public function getXiuLianContentByContent(param1:String) : XiuLianContent
      {
         if(_xiuLianContentsByContent == null)
         {
            return null;
         }
         return _xiuLianContentsByContent[param1];
      }
      
      public function addXiuLianContent(param1:XiuLianContent) : void
      {
         if(Boolean(_xiuLianContents) && _xiuLianContentsByContent[param1.content])
         {
            throw new Error();
         }
         if(_xiuLianContents == null)
         {
            _xiuLianContents = new Vector.<XiuLianContent>();
         }
         if(_xiuLianContentsByContent == null)
         {
            _xiuLianContentsByContent = new Dictionary();
         }
         _xiuLianContents.push(param1);
         _xiuLianContentsByContent[param1.content] = param1;
         param1.xiuLianTargetVO = this;
      }
      
      public function getHaveEquipmentNumInPackage(param1:EquipmentVO) : int
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:Vector.<EquipmentVO> = packageEquipmentVOs;
         var _loc3_:int = int(_loc2_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(_loc2_[_loc5_])
            {
               if(_loc2_[_loc5_].id == param1.id)
               {
                  if(_loc2_[_loc5_] is StackEquipmentVO)
                  {
                     _loc4_ += (_loc2_[_loc5_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc4_++;
                  }
               }
            }
            _loc5_++;
         }
         return _loc4_;
      }
   }
}

