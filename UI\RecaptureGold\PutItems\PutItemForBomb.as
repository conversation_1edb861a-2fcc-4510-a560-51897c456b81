package UI.RecaptureGold.PutItems
{
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.RecaptureGold.Parent.ICatchTarget;
   import UI.RecaptureGold.Parent.IPutItem;
   import UI.RecaptureGold.Parent.IThief;
   import UI.RecaptureGold.Parent.PutItem;
   import UI.SoundManager.SoundManager;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Point;
   import flash.utils.getQualifiedClassName;
   
   public class PutItemForBomb extends PutItem
   {
      
      public function PutItemForBomb()
      {
         super();
      }
      
      public function playExplodeAnimation(param1:XML, param2:DisplayObjectContainer, param3:ICatchTarget) : MovieClip
      {
         var _loc7_:String = getQualifiedClassName(this);
         var _loc5_:XML = param1.TargetData.item.(@classNameForPut == _loc7_)[0];
         var _loc4_:Class = MyFunction2.returnClassByClassName(_loc5_.@animationNameForExplode);
         var _loc6_:MovieClip = new _loc4_();
         var _loc8_:Point = param2.globalToLocal((param3 as DisplayObject).localToGlobal(new Point(this.x,this.y)));
         _loc6_.x = _loc8_.x;
         _loc6_.y = _loc8_.y;
         param2.addChild(_loc6_);
         return _loc6_;
      }
      
      public function catchFunForBombItem(param1:XML, param2:DisplayObjectContainer, param3:Sprite, param4:ICatchTarget, param5:Vector.<ICatchTarget>, param6:SoundManager, param7:Function) : void
      {
         var thisPoint:Point;
         var soundStr:String;
         var mythief:IThief;
         var recaptureGoldGameXML:XML = param1;
         var map:DisplayObjectContainer = param2;
         var recaptureGoldGame:Sprite = param3;
         var catchTarget:ICatchTarget = param4;
         var wanCatchTargets:Vector.<ICatchTarget> = param5;
         var soundManager:SoundManager = param6;
         var shakeFun:Function = param7;
         var playerOverfun:* = function(param1:Event):void
         {
            param1.target.removeEventListener("playerOver",playerOverfun,false);
         };
         var shake:* = function(param1:Event):void
         {
            var _loc7_:int = 0;
            var _loc4_:int = 0;
            var _loc2_:Number = NaN;
            var _loc5_:IThief = null;
            var _loc3_:IPutItem = null;
            var _loc6_:Point = null;
            param1.target.removeEventListener("explodeShake",shake,false);
            if(Boolean(shakeFun))
            {
               shakeFun();
            }
            if(mythief)
            {
               mythief.playAnimationAfterExplodeDie(recaptureGoldGameXML,map,catchTarget);
               (catchTarget as DisplayObjectContainer).removeChild(mythief as DisplayObject);
            }
            catchTarget.clear();
            if((catchTarget as DisplayObject).parent)
            {
               (catchTarget as DisplayObject).parent.removeChild(catchTarget as DisplayObject);
            }
            _loc7_ = 0;
            while(_loc7_ < wanCatchTargets.length)
            {
               _loc3_ = wanCatchTargets[_loc7_].getItem();
               _loc6_ = map.globalToLocal((wanCatchTargets[_loc7_] as DisplayObject).localToGlobal(new Point(_loc3_.x,_loc3_.y)));
               _loc2_ = Math.sqrt(Math.pow(thisPoint.x - _loc6_.x,2) + Math.pow(thisPoint.y - _loc6_.y,2));
               trace(_loc2_);
               if(_loc2_ <= explodeRadius)
               {
                  _loc5_ = wanCatchTargets[_loc7_].getIthief();
                  if(_loc5_)
                  {
                     _loc5_.playAnimationAfterExplodeDie(recaptureGoldGameXML,map,wanCatchTargets[_loc7_]);
                     (wanCatchTargets[_loc7_] as MovieClip).stop();
                     (wanCatchTargets[_loc7_] as DisplayObjectContainer).removeChild(_loc5_ as DisplayObject);
                  }
                  if(_loc3_ is PutItemForBomb)
                  {
                     (_loc3_ as PutItemForBomb).catchFunForBombItem(recaptureGoldGameXML,map,recaptureGoldGame,wanCatchTargets[_loc7_],wanCatchTargets,soundManager,shakeFun);
                  }
                  wanCatchTargets[_loc7_].startDropOut();
                  wanCatchTargets.splice(_loc7_,1);
                  _loc7_--;
               }
               _loc7_++;
            }
         };
         var bomItemClassNameForPut:String = getQualifiedClassName(this);
         var itemXML:XML = recaptureGoldGameXML.TargetData.item.(@classNameForPut == bomItemClassNameForPut)[0];
         var explodeRadiuss:Vector.<Number> = MyFunction.getInstance().excreteStringToNumber(itemXML.@explodeRadius);
         var explodeRadius:Number = explodeRadiuss[0] + Math.round(Math.random() * (explodeRadiuss[1] - explodeRadiuss[0]));
         var animationForExplode:MovieClip = this.playExplodeAnimation(recaptureGoldGameXML,map,catchTarget);
         (catchTarget as DisplayObjectContainer).removeChild(this);
         animationForExplode.addEventListener("playerOver",playerOverfun,false);
         animationForExplode.addEventListener("explodeShake",shake,false);
         thisPoint = map.globalToLocal((catchTarget as DisplayObjectContainer).localToGlobal(new Point(this.x,this.y)));
         soundStr = String(itemXML.@soundForExplode);
         soundManager.play(soundStr,0,0,soundTransform);
         mythief = catchTarget.getIthief();
      }
   }
}

