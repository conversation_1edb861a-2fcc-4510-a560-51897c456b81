package YJFY.LevelMode5
{
   import UI.GamingUI;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Part1;
   import YJFY.Skill.ActiveSkill.ActiveSkill;
   import YJFY.World.World;
   
   public class EndDancerBossSkillAI
   {
      
      private var m_endDancer:EndDancerBoss;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      private var m_nCdTime_1:int = 0;
      
      private var m_nCdSec_1:int = 0;
      
      private var m_bSkill_1:Boolean = false;
      
      private var m_nCdTime_2:int = 0;
      
      private var m_nCdSec_2:int = 0;
      
      private var m_bSkill_2_1:Boolean = false;
      
      private var m_bSkill_2_2:<PERSON>olean = false;
      
      private var m_bSkill_2_1End:Boolean = false;
      
      private var m_nCdTime_3:int = 0;
      
      private var m_nCdSec_3:int = 0;
      
      private var m_bSkill_3:Boolean = false;
      
      private var m_bSkill_4:Boolean = false;
      
      private var m_bIsFirstCD:Boolean = true;
      
      private var m_nEntityNum:int;
      
      private var m_id:String;
      
      private var m_px:Number;
      
      private var m_py:Number;
      
      public function EndDancerBossSkillAI()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function init(param1:IAnimalEntity, param2:World, param3:EndDancerBoss) : void
      {
         m_endDancer = param3;
         m_owner = param1;
         m_world = param2;
         m_bIsFirstCD = true;
      }
      
      public function render() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_world.isStop())
         {
            return;
         }
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            refreshTime_1();
            refreshTime_2();
            refreshTime_3();
         }
         showSkill_4();
         showSkill_3();
         showSkill_1();
         showSkill_2_1();
         showSkill_2_2();
      }
      
      private function showSkill_4() : void
      {
         if(m_owner.isInSkill() == false && m_owner.isInDie() == false && m_world.isStop() == false && m_bSkill_4)
         {
            m_bSkill_4 = false;
            m_endDancer.setIsInvincible(true);
            (m_owner.getSkillById("skill_4") as ActiveSkill).startSkill(m_world);
         }
      }
      
      private function refreshTime_3() : void
      {
         m_nCdTime_3++;
         if(m_nCdTime_3 >= 50)
         {
            m_nCdTime_3 = 0;
            m_nCdSec_3++;
            if(m_nCdSec_3 >= 2 && m_bIsFirstCD == true)
            {
               m_bIsFirstCD = false;
               m_nCdSec_3 = 0;
               m_bSkill_3 = true;
            }
            if(m_bIsFirstCD == false && m_nCdSec_3 >= m_endDancer.skill3CD)
            {
               m_nCdSec_3 = 0;
               m_bSkill_3 = true;
            }
         }
      }
      
      private function showSkill_3() : void
      {
         if(m_owner.isInSkill() == false && m_owner.isInDie() == false && m_world.isStop() == false && m_bSkill_3 && m_bSkill_2_2 == false && EndDancerBoss.m_bIsCallAnemy == false)
         {
            m_bSkill_3 = false;
            (m_owner.getSkillById("skill_3") as ActiveSkill).startSkill(m_world);
         }
      }
      
      private function refreshTime_2() : void
      {
         m_nCdTime_2++;
         if(m_nCdTime_2 >= 50)
         {
            m_nCdTime_2 = 0;
            m_nCdSec_2++;
            if(m_nCdSec_2 >= m_endDancer.skill2CD)
            {
               m_nCdSec_2 = 0;
               m_bSkill_2_1 = true;
            }
         }
      }
      
      private function showSkill_2_1() : void
      {
         if(m_owner.isInSkill() == false && m_owner.isInDie() == false && m_world.isStop() == false && m_bSkill_2_1End == false && m_bSkill_2_1)
         {
            m_bSkill_2_1 = false;
            m_bSkill_2_1End = true;
            m_endDancer.setIsInvincible(true);
            (m_owner.getSkillById("skill_2_1") as ActiveSkill).startSkill(m_world);
         }
      }
      
      private function showSkill_2_2() : void
      {
         if(m_owner.isInSkill() == false && m_owner.isInDie() == false && m_world.isStop() == false && m_bSkill_2_2)
         {
            m_bSkill_2_2 = false;
            m_bSkill_2_1End = false;
            m_endDancer.setIsInvincible(true);
            (m_owner.getSkillById("skill_2_2") as ActiveSkill).startSkill(m_world);
            loadPosition();
         }
      }
      
      private function refreshTime_1() : void
      {
         m_nCdTime_1++;
         if(m_nCdTime_1 >= 50)
         {
            m_nCdTime_1 = 0;
            m_nCdSec_1++;
            if(m_nCdSec_1 >= m_endDancer.skill1CD)
            {
               m_nCdSec_1 = 0;
               m_bSkill_1 = true;
            }
         }
      }
      
      private function showSkill_1() : void
      {
         if(m_owner.isInSkill() == false && m_owner.isInDie() == false && m_world.isStop() == false && m_bSkill_1 && m_bSkill_2_2 == false)
         {
            m_bSkill_1 = false;
            m_endDancer.setIsInvincible(true);
            (m_owner.getSkillById("skill_1") as ActiveSkill).startSkill(m_world);
            loadPosition();
         }
      }
      
      private function loadPosition() : void
      {
         if(isTwo())
         {
            if(isAliveOne())
            {
               loadOnePos();
            }
            else if(isAliveTwo())
            {
               loadTwoPos();
            }
         }
         else if(isAliveOne())
         {
            loadOnePos();
         }
         m_owner.setNewPosition(m_px,m_py,m_owner.getZ());
      }
      
      public function isTwo() : Boolean
      {
         if(GamingUI.getInstance().player2 && GamingUI.getInstance().player2.playerVO)
         {
            return true;
         }
         return false;
      }
      
      private function isAliveOne() : Boolean
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            if(Part1.getInstance().getLevelEndDancerWorld().getPlayer1() && !Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().isInDie())
            {
               return true;
            }
         }
         return false;
      }
      
      private function isAliveTwo() : Boolean
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            if(Part1.getInstance().getLevelEndDancerWorld().getPlayer2() && !Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().isInDie())
            {
               return true;
            }
         }
         return false;
      }
      
      private function loadOnePos() : void
      {
         m_px = Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().getX();
         m_py = Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().getY();
      }
      
      private function loadTwoPos() : void
      {
         m_px = Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().getX();
         m_py = Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().getY();
      }
      
      public function resetPos() : void
      {
         m_nCdTime_1 = 0;
         m_nCdSec_1 = 0;
         m_bSkill_1 = false;
         m_nCdTime_2 = 0;
         m_nCdSec_2 = 0;
         m_bSkill_2_1 = false;
         m_bSkill_2_1End = false;
         m_nCdTime_3 = 0;
         m_nCdSec_3 = 0;
         m_bSkill_3 = false;
      }
      
      public function setSkill3CD() : void
      {
         m_nCdSec_3 = m_endDancer.skill3CD;
      }
      
      public function setSkill_2_2(param1:Boolean) : void
      {
         m_bSkill_2_2 = param1;
      }
      
      public function setSkill_4(param1:Boolean) : void
      {
         m_bSkill_4 = param1;
      }
   }
}

