package YJFY.XydzjsLogic
{
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IEntity;
   import YJFY.Other.ValueAnimationsManager;
   import YJFY.Utils.ClearUtil;
   
   public class ValueAnimationsManagerXydzjs
   {
      
      private var m_normalAttackHurtValueShowClassName:String = "NormalAttackValueAnimation1";
      
      private var m_criticalAttackHurtValueShowClassName:String = "CriticalAttackValueAnimation1";
      
      private var m_shanBiShowClassName:String = "ShanBiAnimation1";
      
      private var m_normalAttackValueAnimationsManager:ValueAnimationsManager;
      
      private var m_criticalAttackValueAnimationsManager:ValueAnimationsManager;
      
      private var m_shanBiValueAnimationsManager:ValueAnimationsManager;
      
      public function ValueAnimationsManagerXydzjs()
      {
         super();
         m_normalAttackValueAnimationsManager = new ValueAnimationsManager();
         m_criticalAttackValueAnimationsManager = new ValueAnimationsManager();
         m_shanBiValueAnimationsManager = new ValueAnimationsManager();
         m_normalAttackValueAnimationsManager.setValueAnimationsClassName(m_normalAttackHurtValueShowClassName);
         m_criticalAttackValueAnimationsManager.setValueAnimationsClassName(m_criticalAttackHurtValueShowClassName);
         m_shanBiValueAnimationsManager.setValueAnimationsClassName(m_shanBiShowClassName);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_normalAttackValueAnimationsManager);
         m_normalAttackValueAnimationsManager = null;
         ClearUtil.clearObject(m_criticalAttackValueAnimationsManager);
         m_criticalAttackValueAnimationsManager = null;
         ClearUtil.clearObject(m_shanBiValueAnimationsManager);
         m_shanBiValueAnimationsManager = null;
         m_normalAttackHurtValueShowClassName = null;
         m_criticalAttackHurtValueShowClassName = null;
         m_shanBiShowClassName = null;
      }
      
      public function getValueAnimationsNum() : uint
      {
         return m_normalAttackValueAnimationsManager.getValueAnimationsNum() + m_criticalAttackValueAnimationsManager.getValueAnimationsNum() + m_shanBiValueAnimationsManager.getValueAnimationsNum();
      }
      
      public function render() : void
      {
         m_normalAttackValueAnimationsManager.render();
         m_criticalAttackValueAnimationsManager.render();
         m_shanBiValueAnimationsManager.render();
      }
      
      public function addValueAnimationToTarget(param1:IEntity, param2:AttackData) : void
      {
         if(param2.getIsBeDodge())
         {
            m_shanBiValueAnimationsManager.addValueAnimationToTarget(param1.getShow(),0 - param1.getBodyXRange() / 2,0 - param1.getBodyZRange(),param2.getHurt());
         }
         else if(param2.getIsCritical())
         {
            m_criticalAttackValueAnimationsManager.addValueAnimationToTarget(param1.getShow(),0 - param1.getBodyXRange() / 2,0 - param1.getBodyZRange(),param2.getHurt());
         }
         else
         {
            m_normalAttackValueAnimationsManager.addValueAnimationToTarget(param1.getShow(),0 - param1.getBodyXRange() / 2,0 - param1.getBodyZRange(),param2.getHurt());
         }
      }
   }
}

