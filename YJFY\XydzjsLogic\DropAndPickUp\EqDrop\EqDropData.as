package YJFY.XydzjsLogic.DropAndPickUp.EqDrop
{
   import YJFY.Utils.ClearUtil;
   
   public class EqDropData
   {
      
      private var m_noDropProWeigth:Number;
      
      private var m_noDropUpProWeigth:Number;
      
      private var m_oneEqDropDatas:Vector.<OneEqDropData>;
      
      private var m_dropNumData:DropNumData;
      
      public function EqDropData()
      {
         super();
         m_noDropProWeigth = 0;
         m_oneEqDropDatas = new Vector.<OneEqDropData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_oneEqDropDatas);
         m_oneEqDropDatas = null;
         ClearUtil.clearObject(m_dropNumData);
         m_dropNumData = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc7_:OneEqDropData = null;
         var _loc4_:Number = NaN;
         m_noDropProWeigth = Number(param1.@noDropProWeight);
         if(param1.hasOwnProperty("dropNumData"))
         {
            m_dropNumData = new DropNumData();
            m_dropNumData.initByXML(param1.dropNumData[0]);
         }
         var _loc6_:XMLList = param1.item;
         _loc5_ = int(_loc6_.length());
         var _loc3_:Number = m_noDropProWeigth;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc3_ += Number(_loc6_[_loc9_].@proWeight);
            _loc9_++;
         }
         m_noDropUpProWeigth = m_noDropProWeigth / _loc3_;
         var _loc2_:Number = m_noDropUpProWeigth;
         var _loc8_:* = m_noDropUpProWeigth;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc4_ = Number(_loc6_[_loc9_].@proWeight);
            _loc2_ = _loc8_ + _loc4_ / _loc3_;
            _loc7_ = new OneEqDropData(_loc6_[_loc9_].@dropClassName,_loc4_,_loc2_,_loc8_);
            m_oneEqDropDatas.push(_loc7_);
            _loc8_ = _loc2_;
            _loc9_++;
         }
      }
      
      public function getOneRandomEq() : String
      {
         var _loc3_:int = 0;
         var _loc1_:Number = Math.random();
         if(_loc1_ <= m_noDropUpProWeigth)
         {
            return null;
         }
         var _loc2_:int = int(m_oneEqDropDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_ <= m_oneEqDropDatas[_loc3_].getUpPro())
            {
               return m_oneEqDropDatas[_loc3_].getDropClassName();
            }
            _loc3_++;
         }
         throw new Error("概率计算出错了");
      }
      
      public function getOneRandomNum(param1:RandomNumReturn, param2:Number = 0) : void
      {
         if(m_dropNumData == null)
         {
            param1.num = 1;
            param1.isRenPingBaoFa = false;
            return;
         }
         return m_dropNumData.getOneRandomDropNum(param1,param2);
      }
   }
}

