package YJFY.Skill.RabbitSkills
{
   import YJFY.Entity.IEntity;
   
   public class RabbitSkill4Listener implements IRabbitSkill4Listener
   {
      
      public var pushEntityFun:Function;
      
      public function RabbitSkill4Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         pushEntityFun = null;
      }
      
      public function pushEntity(param1:Skill_RabbitSkill4, param2:IEntity) : void
      {
         if(<PERSON><PERSON>an(pushEntityFun))
         {
            pushEntityFun(param1,param2);
         }
      }
   }
}

