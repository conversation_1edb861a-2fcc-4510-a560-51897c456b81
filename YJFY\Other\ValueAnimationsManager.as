package YJFY.Other
{
   import UI.MyFunction2;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   
   public class ValueAnimationsManager
   {
      
      private var m_useValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_wasteValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_valueAnimationsPool:ObjectsPool;
      
      private var m_needRemoveValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_valueAnimationStopListener:StopListener;
      
      private var m_valueAnimationsClassName:String;
      
      public function ValueAnimationsManager()
      {
         super();
         m_useValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_needRemoveValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_valueAnimationsPool = new ObjectsPool(m_useValueAnimations,m_wasteValueAnimations,createValueAnimation,null);
         m_valueAnimationStopListener = new StopListener();
         m_valueAnimationStopListener.stop2Fun = valueAnimationEnd;
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = m_useValueAnimations ? m_useValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_useValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_useValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_useValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_useValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_useValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_useValueAnimations[_loc2_]);
            m_useValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_useValueAnimations.length = 0;
         m_useValueAnimations = null;
         _loc1_ = m_wasteValueAnimations ? m_wasteValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_wasteValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_wasteValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_wasteValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_wasteValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_wasteValueAnimations[_loc2_]);
            m_wasteValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_wasteValueAnimations.length = 0;
         m_wasteValueAnimations = null;
         ClearUtil.clearObject(m_valueAnimationsPool);
         m_valueAnimationsPool = null;
         ClearUtil.clearObject(m_valueAnimationStopListener);
         m_valueAnimationStopListener = null;
         ClearUtil.clearObject(m_needRemoveValueAnimations);
         m_needRemoveValueAnimations = null;
      }
      
      public function setValueAnimationsClassName(param1:String) : void
      {
         m_valueAnimationsClassName = param1;
      }
      
      public function getValueAnimationsNum() : uint
      {
         return m_useValueAnimations.length;
      }
      
      public function addValueAnimationToTarget(param1:DisplayObjectContainer, param2:Number, param3:Number, param4:Number, param5:uint = 1) : void
      {
         var _loc6_:AnimationShowPlayLogicShell = null;
         _loc6_ = getOneValueAnimation();
         _loc6_.getDisplayShow().x = param2;
         _loc6_.getDisplayShow().y = param3;
         param1.addChild(_loc6_.getDisplayShow());
         if(_loc6_.extra)
         {
            (_loc6_.extra as MultiPlaceNumLogicShell2).showNum(param4,param5);
         }
         _loc6_.gotoAndPlay("1");
      }
      
      public function render() : void
      {
         renderValueAnimations();
      }
      
      private function getOneValueAnimation() : AnimationShowPlayLogicShell
      {
         return m_valueAnimationsPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
      }
      
      private function renderValueAnimations() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = m_useValueAnimations ? m_useValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_useValueAnimations[_loc2_].render();
            _loc2_++;
         }
         _loc1_ = int(m_needRemoveValueAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_valueAnimationsPool.wasteOneObj(m_needRemoveValueAnimations[_loc2_]);
            _loc2_++;
         }
         m_needRemoveValueAnimations.length = 0;
      }
      
      private function createValueAnimation() : AnimationShowPlayLogicShell
      {
         var _loc2_:MultiPlaceNumLogicShell2 = null;
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_valueAnimationStopListener);
         _loc1_.setShow(MyFunction2.returnShowByClassName(m_valueAnimationsClassName),true);
         if(_loc1_.getShow()["clip"].hasOwnProperty("num"))
         {
            _loc2_ = new MultiPlaceNumLogicShell2();
            _loc1_.extra = _loc2_;
            _loc2_.setShow(_loc1_.getShow()["clip"]["num"]);
         }
         return _loc1_;
      }
      
      private function valueAnimationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         if((param1.getShow() as DisplayObject).parent)
         {
            (param1.getShow() as DisplayObject).parent.removeChild(param1.getShow() as DisplayObject);
         }
         m_needRemoveValueAnimations.push(param1);
      }
   }
}

