package UI.EquipmentMakeAndUpgrade.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class MakeBtn extends Btn
   {
      
      public function MakeBtn()
      {
         super();
         setTipString("开始装备打造");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickMakeEquipmentBtn"));
      }
   }
}

