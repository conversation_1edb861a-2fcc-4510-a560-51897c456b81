package UI.Utils
{
   import UI.CheatData.CheatData;
   import UI.DetectionClass.DetectionClass;
   import UI.GamingUI;
   import UI.MyFunction2;
   import YJFY.API_4399.SaveAPI.SaveAPIListener;
   import YJFY.GameData;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.Part1;
   
   public class SaveGame
   {
      
      public function SaveGame()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function saveGame(param1:XML, param2:int, param3:Function, param4:Function, param5:String) : void
      {
         var saveAPIListener:SaveAPIListener;
         var saveXML:XML = param1;
         var idx:int = param2;
         var successFunction:Function = param3;
         var failFunction:Function = param4;
         var title:String = param5;
         AntiwearNumber.checkIsError();
         if(CheatData.getInstance().getCheatDataStrNum() > 0)
         {
            MyFunction2.doIsCheat();
         }
         if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.playerVO.medalEquipmentVOs.length > 11)
         {
            MyFunction2.doIsCheat();
         }
         if(Part1.getInstance().isCheat)
         {
            GamingUI.getInstance().showMessageTip("存档失败");
            return;
         }
         if(DetectionClass.getInstance().detectionVersion())
         {
            GamingUI.getInstance().showMessageTip("存档失败(请清除缓存并在最新版本游玩)");
            return;
         }
         if(DetectionClass.getInstance().detectionUidAndIdx())
         {
            GamingUI.getInstance().showMessageTip("存档失败");
            return;
         }
         saveAPIListener = new SaveAPIListener();
         saveAPIListener.setSaveFileFailFun = function():void
         {
            if(Boolean(failFunction))
            {
               failFunction();
            }
         };
         saveAPIListener.setSaveFileSuccessFun = function():void
         {
            if(Boolean(successFunction))
            {
               successFunction();
            }
         };
         saveAPIListener.addTarget = GamingUI.getInstance().getAPI4399().saveAPI;
         GamingUI.getInstance().getAPI4399().saveAPI.addSaveAPIListener(saveAPIListener);
         GamingUI.getInstance().getAPI4399().saveAPI.saveData(title,saveXML,idx);
         GameData.getInstance().getSaveFileData().saveXML = saveXML;
      }
   }
}

