package UI.AutomaticPetPanel
{
   import GM_UI.GMData;
   import UI.AnalogServiceHoldFunction;
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.EntityShowContainer;
   import YJFY.GameData;
   import YJFY.Loader.IProgressShow;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.CheckBox.CheckBoxLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class InforShowPanel
   {
      
      private var m_petNameText:TextField;
      
      private var m_chuaZhanAndRestBtnShowMC:MovieClipPlayLogicShell;
      
      private var m_choosePetBtns:MovieClipPlayLogicShell;
      
      private var m_choosePetBtn:ButtonLogicShell2;
      
      private var m_choosePet1Btn:ButtonLogicShell2;
      
      private var m_choosePet2Btn:ButtonLogicShell2;
      
      private var m_restPetBtn:ButtonLogicShell2;
      
      private var m_selectSkinBtn:ButtonLogicShell2;
      
      private var m_buySkinBtn:ButtonLogicShell2;
      
      private var m_expBar:CMSXChangeBarLogicShell;
      
      private var m_hpBar:CMSXChangeBarLogicShell;
      
      private var m_mpBar:CMSXChangeBarLogicShell;
      
      private var m_levelNumShow:MultiPlaceNumLogicShell;
      
      private var m_scoreShow:MultiPlaceNumLogicShell2;
      
      private var m_pingJieShow:MovieClipPlayLogicShell;
      
      private var m_hpText:TextField;
      
      private var m_mpText:TextField;
      
      private var m_attackText:TextField;
      
      private var m_defenceText:TextField;
      
      private var m_hitRateText:TextField;
      
      private var m_dogdeRateText:TextField;
      
      private var m_criticalRateText:TextField;
      
      private var m_decriticalRateText:TextField;
      
      private var m_activeSkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_passiveSkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_auxiliarySkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_activeSkillUpgradeBtns:Vector.<ButtonLogicShell2>;
      
      private var m_passiveSkillUpgradeBtns:Vector.<ButtonLogicShell2>;
      
      private var m_auxiliarySkillUpgradeBtns:Vector.<ButtonLogicShell2>;
      
      private var m_resetSkillsBtn:ButtonLogicShell2;
      
      private var m_preSkinBtn:ButtonLogicShell2;
      
      private var m_nextSkinBtn:ButtonLogicShell2;
      
      private var m_SkinShowCkb:CheckBoxLogicShell;
      
      private var m_getNewPassiveSkillBtns:Vector.<ButtonLogicShell2>;
      
      private var m_showAnimationContianer:EntityShowContainer;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_automaticPetSkillOperationPanel:AuomaticPetSkillOperationPanel;
      
      private var m_show:MovieClip;
      
      private var m_showContainer:MovieClip;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_closebtn:MovieClip;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetPanel:AutomaticPetPanel;
      
      public function InforShowPanel()
      {
         super();
         m_chuaZhanAndRestBtnShowMC = new MovieClipPlayLogicShell();
         m_levelNumShow = new MultiPlaceNumLogicShell();
         m_scoreShow = new MultiPlaceNumLogicShell2();
         m_pingJieShow = new MovieClipPlayLogicShell();
         m_expBar = new CMSXChangeBarLogicShell();
         m_hpBar = new CMSXChangeBarLogicShell();
         m_mpBar = new CMSXChangeBarLogicShell();
         m_activeSkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         m_passiveSkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         m_auxiliarySkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         m_activeSkillUpgradeBtns = new Vector.<ButtonLogicShell2>();
         m_passiveSkillUpgradeBtns = new Vector.<ButtonLogicShell2>();
         m_auxiliarySkillUpgradeBtns = new Vector.<ButtonLogicShell2>();
         m_getNewPassiveSkillBtns = new Vector.<ButtonLogicShell2>();
         m_resetSkillsBtn = new ButtonLogicShell2();
         m_preSkinBtn = new ButtonLogicShell2();
         m_nextSkinBtn = new ButtonLogicShell2();
         m_SkinShowCkb = new CheckBoxLogicShell();
         m_showAnimationContianer = new EntityShowContainer();
         m_showAnimationContianer.init();
         m_font = new FangZhengKaTongJianTi();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_petNameText = null;
         ClearUtil.clearObject(m_chuaZhanAndRestBtnShowMC);
         m_chuaZhanAndRestBtnShowMC = null;
         ClearUtil.clearObject(m_choosePetBtns);
         m_choosePetBtns = null;
         ClearUtil.clearObject(m_choosePetBtn);
         m_choosePetBtn = null;
         ClearUtil.clearObject(m_choosePet1Btn);
         m_choosePet1Btn = null;
         ClearUtil.clearObject(m_choosePet2Btn);
         m_choosePet2Btn = null;
         ClearUtil.clearObject(m_restPetBtn);
         m_restPetBtn = null;
         ClearUtil.clearObject(m_selectSkinBtn);
         m_selectSkinBtn = null;
         ClearUtil.clearObject(m_buySkinBtn);
         m_buySkinBtn = null;
         ClearUtil.clearObject(m_font);
         m_font = null;
         ClearUtil.clearObject(m_expBar);
         m_expBar = null;
         ClearUtil.clearObject(m_hpBar);
         m_hpBar = null;
         ClearUtil.clearObject(m_mpBar);
         m_mpBar = null;
         ClearUtil.clearObject(m_levelNumShow);
         m_levelNumShow = null;
         ClearUtil.clearObject(m_scoreShow);
         m_scoreShow = null;
         ClearUtil.clearObject(m_pingJieShow);
         m_pingJieShow = null;
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         m_closebtn = null;
         ClearUtil.clearObject(m_activeSkillCells);
         m_activeSkillCells = null;
         ClearUtil.clearObject(m_passiveSkillCells);
         m_passiveSkillCells = null;
         ClearUtil.clearObject(m_auxiliarySkillCells);
         m_auxiliarySkillCells = null;
         ClearUtil.clearObject(m_showAnimationContianer);
         m_showAnimationContianer = null;
         ClearUtil.clearObject(m_activeSkillUpgradeBtns);
         m_activeSkillUpgradeBtns = null;
         ClearUtil.clearObject(m_passiveSkillUpgradeBtns);
         m_passiveSkillUpgradeBtns = null;
         ClearUtil.clearObject(m_auxiliarySkillUpgradeBtns);
         m_auxiliarySkillUpgradeBtns = null;
         ClearUtil.clearObject(m_getNewPassiveSkillBtns);
         m_getNewPassiveSkillBtns = null;
         ClearUtil.clearObject(m_resetSkillsBtn);
         m_resetSkillsBtn = null;
         ClearUtil.clearObject(m_preSkinBtn);
         m_preSkinBtn = null;
         ClearUtil.clearObject(m_nextSkinBtn);
         m_nextSkinBtn = null;
         ClearUtil.clearObject(m_SkinShowCkb);
         m_SkinShowCkb = null;
         ClearUtil.clearObject(m_automaticPetSkillOperationPanel);
         m_automaticPetSkillOperationPanel = null;
         m_show = null;
         m_showContainer = null;
         m_versionControl = null;
         m_loadUI = null;
         m_automaticPetVO = null;
         m_automaticPetPanel = null;
      }
      
      public function setShow(param1:MovieClip, param2:IVersionControl, param3:IProgressShow, param4:AutomaticPetPanel) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_versionControl = param2;
         m_automaticPetPanel = param4;
         m_loadUI = param3;
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         if(m_automaticPetVO)
         {
            m_automaticPetVO.resetSkinShow();
         }
         initShow2();
      }
      
      public function getAutomaticPetPanel() : AutomaticPetPanel
      {
         return m_automaticPetPanel;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_activeSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_activeSkillCells[_loc3_].render(param1);
            _loc3_++;
         }
         _loc2_ = int(m_passiveSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_passiveSkillCells[_loc3_].render(param1);
            _loc3_++;
         }
         _loc2_ = int(m_auxiliarySkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_auxiliarySkillCells[_loc3_].render(param1);
            _loc3_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_petNameText.text = m_automaticPetVO.getShowName();
         m_showContainer.addChild(m_showAnimationContianer.getShow());
         m_showAnimationContianer.refreshAutomaticPetShow(m_automaticPetVO);
         if(m_automaticPetVO.getCurrentSkin() != m_automaticPetVO.getCurrentShowSKin())
         {
            if(!m_automaticPetVO.getCurrentShowSKin() || m_automaticPetVO.getCurrentShowSKin().isActivity)
            {
               initSelectSkinBtnShowFrame();
            }
            else
            {
               initBuySkinBtnShowFrame();
            }
         }
         else if(m_automaticPetVO.getPlayerVO())
         {
            initRestBtnShowFrame();
         }
         else
         {
            initChuZhanBtnShowFrame();
         }
         m_expBar.change(m_automaticPetVO.getCurrentExp() / m_automaticPetVO.getUpLevelTotalExp());
         m_expBar.setDataShow(m_automaticPetVO.getCurrentExp() + "/" + m_automaticPetVO.getUpLevelTotalExp());
         m_hpBar.change(m_automaticPetVO.getCurrentHp() / m_automaticPetVO.getTotalHp());
         m_hpBar.setDataShow(m_automaticPetVO.getCurrentHp() + "/" + m_automaticPetVO.getTotalHp());
         m_mpBar.change(m_automaticPetVO.getCurrentMp() / m_automaticPetVO.getTotalMp());
         m_mpBar.setDataShow(m_automaticPetVO.getCurrentMp() + "/" + m_automaticPetVO.getTotalMp());
         m_levelNumShow.showNum(m_automaticPetVO.getLevel());
         m_pingJieShow.gotoAndStop(m_automaticPetVO.getPingJieVO().getId());
         m_scoreShow.showNum(m_automaticPetVO.getAutomaticPetScore());
         m_hpText.text = m_automaticPetVO.getTotalHp().toString();
         m_mpText.text = m_automaticPetVO.getTotalMp().toString();
         m_attackText.text = m_automaticPetVO.getAttack().toString();
         m_defenceText.text = m_automaticPetVO.getDefence().toString();
         m_hitRateText.text = (m_automaticPetVO.getHitRate() * 100).toFixed(2) + "%";
         m_dogdeRateText.text = (m_automaticPetVO.getDogdeRate() * 100).toFixed(2) + "%";
         m_criticalRateText.text = (m_automaticPetVO.getCriticalRate() * 100).toFixed(2) + "%";
         m_decriticalRateText.text = (m_automaticPetVO.getDeCriticalRate() * 100).toFixed(2) + "%";
         var _loc2_:int = int(m_automaticPetVO.getActiveSkillVONum());
         var _loc1_:int = int(m_activeSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_activeSkillCells[_loc3_].setData(null,null);
            m_activeSkillUpgradeBtns[_loc3_].getShow().visible = false;
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_activeSkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getActiveSkillVOByIndex(_loc3_));
            if(m_automaticPetVO.getActiveSkillVOByIndex(_loc3_).getLevel() >= m_automaticPetVO.getActiveSkillVOByIndex(_loc3_).getMaxLevel())
            {
               m_activeSkillUpgradeBtns[_loc3_].getShow().visible = false;
            }
            else
            {
               m_activeSkillUpgradeBtns[_loc3_].getShow().visible = true;
            }
            _loc3_++;
         }
         _loc2_ = int(m_automaticPetVO.getAuxiliarySkillVONum());
         _loc1_ = int(m_auxiliarySkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_auxiliarySkillCells[_loc3_].setData(null,null);
            m_auxiliarySkillUpgradeBtns[_loc3_].getShow().visible = false;
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_auxiliarySkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getAuxiliarySkillVOByIndex(_loc3_));
            if(m_automaticPetVO.getAuxiliarySkillVOByIndex(_loc3_).getLevel() >= m_automaticPetVO.getAuxiliarySkillVOByIndex(_loc3_).getMaxLevel())
            {
               m_auxiliarySkillUpgradeBtns[_loc3_].getShow().visible = false;
            }
            else
            {
               m_auxiliarySkillUpgradeBtns[_loc3_].getShow().visible = true;
            }
            _loc3_++;
         }
         _loc2_ = int(m_automaticPetVO.getPassiveSkillVONum());
         _loc1_ = int(m_passiveSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_passiveSkillCells[_loc3_].setData(null,null);
            m_passiveSkillUpgradeBtns[_loc3_].getShow().visible = false;
            m_getNewPassiveSkillBtns[_loc3_].getShow().visible = true;
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_passiveSkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getPassiveSkillVOByIndex(_loc3_) as AutomaticPetSkillVO);
            m_getNewPassiveSkillBtns[_loc3_].getShow().visible = false;
            if(m_automaticPetVO.getPassiveSkillVOByIndex(_loc3_).getLevel() >= m_automaticPetVO.getPassiveSkillVOByIndex(_loc3_).getMaxLevel())
            {
               m_passiveSkillUpgradeBtns[_loc3_].getShow().visible = false;
            }
            else
            {
               m_passiveSkillUpgradeBtns[_loc3_].getShow().visible = true;
            }
            _loc3_++;
         }
         if(_loc2_)
         {
            m_resetSkillsBtn.getShow().visible = true;
         }
         else
         {
            m_resetSkillsBtn.getShow().visible = false;
         }
         if(m_automaticPetVO.getActivitionSkins().length > 0)
         {
            m_SkinShowCkb.getShow().visible = true;
            if(m_automaticPetVO.getCurrentShowSKin())
            {
               m_SkinShowCkb.isCheck = true;
            }
            else
            {
               m_SkinShowCkb.isCheck = false;
            }
         }
         else
         {
            m_SkinShowCkb.getShow().visible = false;
         }
         if(m_automaticPetVO.partnerUid)
         {
            m_nextSkinBtn.getShow().visible = true;
            m_preSkinBtn.getShow().visible = true;
         }
         else
         {
            m_nextSkinBtn.getShow().visible = false;
            m_preSkinBtn.getShow().visible = false;
         }
      }
      
      private function initShow() : void
      {
         var _loc12_:int = 0;
         var _loc6_:int = 0;
         var _loc11_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc9_:AutomaticPetSkillCellLogic = null;
         var _loc10_:ButtonLogicShell2 = null;
         m_petNameText = m_show["petNameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_petNameText);
         m_showContainer = m_show["showContainer"];
         m_closebtn = m_show.parent["quitBtn"] as MovieClip;
         m_chuaZhanAndRestBtnShowMC.setShow(m_show["chuZhanAndRestBtn"]);
         m_expBar.setShow(m_show["expBar"]);
         m_hpBar.setShow(m_show["hpBar"]);
         m_mpBar.setShow(m_show["mpBar"]);
         m_levelNumShow.setShow(m_show["levelNumShow"]);
         m_scoreShow.setShow(m_show["scoreShow"]);
         m_pingJieShow.setShow(m_show["pingJieShow"]);
         if(GMData.getInstance().isGMApplication)
         {
            m_closebtn.addEventListener("mouseDown",closeEvent);
         }
         m_hpText = m_show["hpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hpText);
         m_mpText = m_show["mpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_mpText);
         m_attackText = m_show["attackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_attackText);
         m_defenceText = m_show["defenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_defenceText);
         m_hitRateText = m_show["hitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hitRateText);
         m_dogdeRateText = m_show["dogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_dogdeRateText);
         m_criticalRateText = m_show["criticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_criticalRateText);
         m_decriticalRateText = m_show["decriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_decriticalRateText);
         var _loc8_:int = m_show.numChildren;
         _loc12_ = 0;
         while(_loc12_ < _loc8_)
         {
            _loc1_ = m_show.getChildAt(_loc12_);
            if(_loc1_.name.substr(0,"activeSkillContainer_".length) == "activeSkillContainer_")
            {
               _loc6_++;
            }
            else if(_loc1_.name.substr(0,"passiveSkillContainer_".length) == "passiveSkillContainer_")
            {
               _loc11_++;
            }
            else if(_loc1_.name.substr(0,"auxiliarySkillContainer_".length) == "auxiliarySkillContainer_")
            {
               _loc3_++;
            }
            else if(_loc1_.name.substr(0,"aUpgradeBtn_".length) == "aUpgradeBtn_")
            {
               _loc5_++;
            }
            else if(_loc1_.name.substr(0,"pUpgradeBtn_".length) == "pUpgradeBtn_")
            {
               _loc4_++;
            }
            else if(_loc1_.name.substr(0,"auUpgradeBtn_".length) == "auUpgradeBtn_")
            {
               _loc7_++;
            }
            else if(_loc1_.name.substr(0,"getNewSkillBtn_".length) == "getNewSkillBtn_")
            {
               _loc2_++;
            }
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc6_)
         {
            _loc9_ = new AutomaticPetSkillCellLogic();
            _loc9_.setLoadUI(m_loadUI);
            _loc9_.setVersionControl(m_versionControl);
            _loc9_.setShow(m_show["activeSkillContainer_" + (_loc12_ + 1)],m_show["timeShow" + (_loc12_ + 1)]);
            m_activeSkillCells.push(_loc9_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc11_)
         {
            _loc9_ = new AutomaticPetSkillCellLogic();
            _loc9_.setLoadUI(m_loadUI);
            _loc9_.setVersionControl(m_versionControl);
            _loc9_.setShow(m_show["passiveSkillContainer_" + (_loc12_ + 1)],null);
            m_passiveSkillCells.push(_loc9_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc3_)
         {
            _loc9_ = new AutomaticPetSkillCellLogic();
            _loc9_.setLoadUI(m_loadUI);
            _loc9_.setVersionControl(m_versionControl);
            _loc9_.setShow(m_show["auxiliarySkillContainer_" + (_loc12_ + 1)],null);
            m_auxiliarySkillCells.push(_loc9_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc5_)
         {
            _loc10_ = new ButtonLogicShell2();
            _loc10_.setShow(m_show["aUpgradeBtn_" + (_loc12_ + 1)]);
            _loc10_.setTipString("点击升级该技能");
            m_activeSkillUpgradeBtns.push(_loc10_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc4_)
         {
            _loc10_ = new ButtonLogicShell2();
            _loc10_.setShow(m_show["pUpgradeBtn_" + (_loc12_ + 1)]);
            _loc10_.setTipString("点击升级该技能");
            m_passiveSkillUpgradeBtns.push(_loc10_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc7_)
         {
            _loc10_ = new ButtonLogicShell2();
            _loc10_.setShow(m_show["auUpgradeBtn_" + (_loc12_ + 1)]);
            _loc10_.setTipString("点击升级该技能");
            m_auxiliarySkillUpgradeBtns.push(_loc10_);
            _loc12_++;
         }
         _loc12_ = 0;
         while(_loc12_ < _loc2_)
         {
            _loc10_ = new ButtonLogicShell2();
            _loc10_.setShow(m_show["getNewSkillBtn_" + (_loc12_ + 1)]);
            _loc10_.setTipString("点击领悟新技能");
            m_getNewPassiveSkillBtns.push(_loc10_);
            _loc12_++;
         }
         m_resetSkillsBtn.setShow(m_show["resetSkillsBtn"]);
         m_resetSkillsBtn.setTipString("点击洗炼妖将被动技能");
         m_nextSkinBtn.setShow(m_show["btnNextSkin"]);
         m_nextSkinBtn.setTipString("点击切换妖将");
         m_preSkinBtn.setShow(m_show["btnPreSkin"]);
         m_preSkinBtn.setTipString("点击切换妖将");
         m_SkinShowCkb.setShow(m_show["ckbSkinShow"]);
      }
      
      private function closeEvent(param1:MouseEvent) : void
      {
         this.m_automaticPetPanel.closePanel();
         clear();
      }
      
      private function clearChuZhanAndRestBtnShowFrame() : void
      {
         clearChoosePetBtnFrame();
         ClearUtil.clearObject(m_choosePetBtns);
         m_choosePetBtns = null;
         ClearUtil.clearObject(m_restPetBtn);
         m_restPetBtn = null;
         ClearUtil.clearObject(m_selectSkinBtn);
         m_selectSkinBtn = null;
         ClearUtil.clearObject(m_buySkinBtn);
         m_buySkinBtn = null;
      }
      
      private function initSelectSkinBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("selectSkin");
         m_selectSkinBtn = new ButtonLogicShell2();
         m_selectSkinBtn.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["btnSelectSkin"]);
      }
      
      private function initBuySkinBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("buySkin");
         m_buySkinBtn = new ButtonLogicShell2();
         m_buySkinBtn.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["btnBuySkin"]);
      }
      
      private function initRestBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("rest");
         m_restPetBtn = new ButtonLogicShell2();
         m_restPetBtn.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["restBtn"]);
      }
      
      private function initChuZhanBtnShowFrame() : void
      {
         clearChuZhanAndRestBtnShowFrame();
         m_chuaZhanAndRestBtnShowMC.gotoAndStop("chuZhanBtns");
         m_choosePetBtns = new MovieClipPlayLogicShell();
         m_choosePetBtns.setShow(m_chuaZhanAndRestBtnShowMC.getShow()["chuZhanBtns"]);
         if(GamingUI.getInstance().player2)
         {
            initChoosePetBtnTwoFrame();
         }
         else
         {
            initChoosePetBtnOneFrame();
         }
      }
      
      private function clearChoosePetBtnFrame() : void
      {
         ClearUtil.clearObject(m_choosePetBtn);
         m_choosePetBtn = null;
         ClearUtil.clearObject(m_choosePet1Btn);
         m_choosePet1Btn = null;
         ClearUtil.clearObject(m_choosePet2Btn);
         m_choosePet2Btn = null;
      }
      
      private function initChoosePetBtnOneFrame() : void
      {
         clearChoosePetBtnFrame();
         m_choosePetBtns.gotoAndStop("1");
         m_choosePetBtn = new ButtonLogicShell2();
         m_choosePetBtn.setShow(m_choosePetBtns.getShow()["chuZhanBtn"]);
      }
      
      private function initChoosePetBtnTwoFrame() : void
      {
         clearChoosePetBtnFrame();
         m_choosePetBtns.gotoAndStop("2");
         m_choosePet1Btn = new ButtonLogicShell2();
         m_choosePet1Btn.setShow(m_choosePetBtns.getShow()["chuZhanBtn_1"]);
         m_choosePet2Btn = new ButtonLogicShell2();
         m_choosePet2Btn.setShow(m_choosePetBtns.getShow()["chuZhanBtn_2"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         switch(param1.button)
         {
            case m_choosePetBtn:
            case m_choosePet1Btn:
               if(m_automaticPetVO.partnerUid && GamingUI.getInstance().getAutomaticPetsData().getIndexOfAutomaticPetVOEnable(m_automaticPetVO) == -1 || GamingUI.getInstance().player1.playerVO.automaticPetVO && GamingUI.getInstance().player1.playerVO.automaticPetVO.partnerUid && GamingUI.getInstance().player1.playerVO.automaticPetVO.partnerUid == m_automaticPetVO.partnerUid)
               {
                  _loc2_ = GamingUI.getInstance().getAutomaticPetsData().getIndexOfAutomaticPetVOEnable(GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName));
                  GamingUI.getInstance().getAutomaticPetsData().replaceDoubleVosByUid(_loc2_,m_automaticPetVO);
               }
               if(GamingUI.getInstance().player2 && GamingUI.getInstance().player2.playerVO && GamingUI.getInstance().player2.playerVO.automaticPetVO && GamingUI.getInstance().player2.playerVO.automaticPetVO.partnerUid && GamingUI.getInstance().player2.playerVO.automaticPetVO.partnerUid == m_automaticPetVO.partnerUid)
               {
                  GamingUI.getInstance().player2.playerVO.automaticPetVO = null;
               }
               GamingUI.getInstance().player1.playerVO.automaticPetVO = m_automaticPetVO;
               m_automaticPetPanel.refreshShow(m_automaticPetVO);
               m_automaticPetPanel.showWarningBox("切换至出战状态！",0);
               break;
            case m_choosePet2Btn:
               if(m_automaticPetVO.partnerUid && GamingUI.getInstance().getAutomaticPetsData().getIndexOfAutomaticPetVOEnable(m_automaticPetVO) == -1 || GamingUI.getInstance().player2.playerVO.automaticPetVO && GamingUI.getInstance().player2.playerVO.automaticPetVO.partnerUid && GamingUI.getInstance().player2.playerVO.automaticPetVO.partnerUid == m_automaticPetVO.partnerUid)
               {
                  _loc2_ = GamingUI.getInstance().getAutomaticPetsData().getIndexOfAutomaticPetVOEnable(GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName));
                  GamingUI.getInstance().getAutomaticPetsData().replaceDoubleVosByUid(_loc2_,m_automaticPetVO);
               }
               if(GamingUI.getInstance().player1 && GamingUI.getInstance().player1.playerVO && GamingUI.getInstance().player1.playerVO.automaticPetVO && GamingUI.getInstance().player1.playerVO.automaticPetVO.partnerUid && GamingUI.getInstance().player1.playerVO.automaticPetVO.partnerUid == m_automaticPetVO.partnerUid)
               {
                  GamingUI.getInstance().player1.playerVO.automaticPetVO = null;
               }
               GamingUI.getInstance().player2.playerVO.automaticPetVO = m_automaticPetVO;
               m_automaticPetPanel.refreshShow(m_automaticPetVO);
               m_automaticPetPanel.showWarningBox("切换至出战状态！",0);
               break;
            case m_restPetBtn:
               m_automaticPetVO.getPlayerVO().automaticPetVO = null;
               m_automaticPetPanel.refreshShow(m_automaticPetVO);
               m_automaticPetPanel.showWarningBox("切换至休息状态！",0);
               break;
            case m_resetSkillsBtn:
               openAutomaticSkillResetSkillsPanel();
               return;
            case m_nextSkinBtn:
            case m_preSkinBtn:
               setData(GamingUI.getInstance().getAutomaticPetsData().getBackPetVoByUid(m_automaticPetVO.partnerUid,m_automaticPetVO.partnerName));
               return;
            case m_SkinShowCkb:
               if(m_SkinShowCkb.isCheck)
               {
                  m_automaticPetVO.changeNextShowSkin();
               }
               else
               {
                  m_automaticPetVO.changeShowNoSkin();
               }
               initShow2();
               return;
            case m_selectSkinBtn:
               m_automaticPetVO.useShowSkin();
               if(m_automaticPetVO.partnerName)
               {
                  setData(m_automaticPetVO);
               }
               m_automaticPetPanel.refreshShow(m_automaticPetVO);
               if(m_automaticPetVO.getPlayerVO())
               {
                  m_automaticPetVO.getPlayerVO().player.changeData();
               }
               return;
            case m_buySkinBtn:
               m_automaticPetPanel.showWarningBox("是否花费" + m_automaticPetVO.getCurrentShowSKin().ticketPrice + "点券购买" + m_automaticPetVO.getCurrentShowSKin().name + "皮肤",1 | 2,{
                  "type":"buyAutomaticPetSkin",
                  "okFunction":buy2
               });
               return;
         }
         var _loc3_:int = int(m_activeSkillUpgradeBtns.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_activeSkillUpgradeBtns[_loc4_] == param1.button)
            {
               openAutomaticSkillUpgradePanel(m_activeSkillCells[_loc4_].getAutomaticPetSkillVO());
               return;
            }
            _loc4_++;
         }
         _loc3_ = int(m_auxiliarySkillUpgradeBtns.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_auxiliarySkillUpgradeBtns[_loc4_] == param1.button)
            {
               openAutomaticSkillUpgradePanel(m_auxiliarySkillCells[_loc4_].getAutomaticPetSkillVO());
               return;
            }
            _loc4_++;
         }
         _loc3_ = int(m_passiveSkillUpgradeBtns.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_passiveSkillUpgradeBtns[_loc4_] == param1.button)
            {
               openAutomaticSkillUpgradePanel(m_passiveSkillCells[_loc4_].getAutomaticPetSkillVO());
               return;
            }
            _loc4_++;
         }
         _loc3_ = int(m_getNewPassiveSkillBtns.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_getNewPassiveSkillBtns[_loc4_] == param1.button)
            {
               openAutomaticSkillGetNewSkillPanel();
               return;
            }
            _loc4_++;
         }
      }
      
      private function buy2() : void
      {
         var price:uint;
         var ticketId:String;
         var dataObj:Object;
         if(m_automaticPetVO == null || !m_automaticPetVO.getCurrentShowSKin() || m_automaticPetVO.getCurrentShowSKin().isActivity)
         {
            return;
         }
         price = uint(m_automaticPetVO.getCurrentShowSKin().ticketPrice);
         ticketId = String(m_automaticPetVO.getCurrentShowSKin().ticketPriceId);
         dataObj = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买皮肤";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_automaticPetPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_automaticPetVO.getCurrentShowSKin().isActivity = true;
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            m_automaticPetVO.useShowSkin();
            setData(m_automaticPetVO);
            m_automaticPetPanel.refreshShow(m_automaticPetVO);
            if(m_automaticPetVO.getPlayerVO())
            {
               m_automaticPetVO.getPlayerVO().player.changeData();
            }
         },m_automaticPetPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function openAutomaticSkillUpgradePanel(param1:AutomaticPetSkillVO) : void
      {
         var _loc3_:Point = null;
         var _loc2_:Point = null;
         if(m_automaticPetSkillOperationPanel == null)
         {
            m_automaticPetSkillOperationPanel = new AuomaticPetSkillOperationPanel();
            m_automaticPetSkillOperationPanel.init(m_versionControl,m_loadUI,this,m_automaticPetPanel.getAutomaticPetDataXML());
            _loc3_ = new Point((m_show.stage.stageWidth - m_automaticPetSkillOperationPanel.width) / 2,(m_show.stage.stageHeight - m_automaticPetSkillOperationPanel.height) / 2);
            _loc2_ = m_show.globalToLocal(_loc3_);
            m_automaticPetSkillOperationPanel.x = _loc2_.x;
            m_automaticPetSkillOperationPanel.y = _loc2_.y;
         }
         m_automaticPetSkillOperationPanel.setUpgradeData(param1,m_automaticPetVO);
         m_show.addChild(m_automaticPetSkillOperationPanel);
      }
      
      private function openAutomaticSkillGetNewSkillPanel() : void
      {
         var _loc2_:Point = null;
         var _loc1_:Point = null;
         if(m_automaticPetSkillOperationPanel == null)
         {
            m_automaticPetSkillOperationPanel = new AuomaticPetSkillOperationPanel();
            m_automaticPetSkillOperationPanel.init(m_versionControl,m_loadUI,this,m_automaticPetPanel.getAutomaticPetDataXML());
            _loc2_ = new Point((m_show.stage.stageWidth - m_automaticPetSkillOperationPanel.width) / 2,(m_show.stage.stageHeight - m_automaticPetSkillOperationPanel.height) / 2);
            _loc1_ = m_show.globalToLocal(_loc2_);
            m_automaticPetSkillOperationPanel.x = _loc1_.x;
            m_automaticPetSkillOperationPanel.y = _loc1_.y;
         }
         m_automaticPetSkillOperationPanel.setGetNewSkillData(m_automaticPetVO);
         m_show.addChild(m_automaticPetSkillOperationPanel);
      }
      
      private function openAutomaticSkillResetSkillsPanel() : void
      {
         var _loc2_:Point = null;
         var _loc1_:Point = null;
         if(m_automaticPetSkillOperationPanel == null)
         {
            m_automaticPetSkillOperationPanel = new AuomaticPetSkillOperationPanel();
            m_automaticPetSkillOperationPanel.init(m_versionControl,m_loadUI,this,m_automaticPetPanel.getAutomaticPetDataXML());
            _loc2_ = new Point((m_show.stage.stageWidth - m_automaticPetSkillOperationPanel.width) / 2,(m_show.stage.stageHeight - m_automaticPetSkillOperationPanel.height) / 2);
            _loc1_ = m_show.globalToLocal(_loc2_);
            m_automaticPetSkillOperationPanel.x = _loc1_.x;
            m_automaticPetSkillOperationPanel.y = _loc1_.y;
         }
         m_automaticPetSkillOperationPanel.setResetSkillsData(m_automaticPetVO);
         m_show.addChild(m_automaticPetSkillOperationPanel);
      }
      
      public function closeAutomaticSkillOperationPanel() : void
      {
         ClearUtil.clearObject(m_automaticPetSkillOperationPanel);
         m_automaticPetSkillOperationPanel = null;
      }
   }
}

