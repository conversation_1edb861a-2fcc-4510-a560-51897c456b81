package YJFY.LevelMode5
{
   import UI.Event.NewLevelEvent;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIAbleRunInHurt;
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIRun;
   import YJFY.EntityAI.AILogic.IJugdeIsFoe;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEvent;
   import YJFY.LevelMode2.ILevelMode2Enemy;
   import YJFY.Skill.ISkill;
   import YJFY.Skill.Skill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.AIAttackVO;
   import YJFY.XydzjsLogic.JudgeIsFoe;
   
   public class CallNormalDancer extends EnemyXydzjs implements ILevelMode2Enemy, IHaveActiveSkillForAIAbleRunInHurt, IHaveActiveSkillForAIRun, IJugdeIsFoe
   {
      
      public var m_enemyAI:CallNormalDancerAI;
      
      public var m_enemyAttackVO:AIAttackVO;
      
      private var m_beNumAttack:int = 0;
      
      private var m_beAttackNum:int = 0;
      
      private var m_lastHp:uint;
      
      private var m_world:World;
      
      public function CallNormalDancer()
      {
         super();
         m_enemyAI = new CallNormalDancerAI();
         m_enemyAttackVO = new AIAttackVO();
      }
      
      override public function clear() : void
      {
         if(m_enemyAI)
         {
            ClearUtil.clearObject(m_enemyAI);
         }
         m_enemyAI = null;
         if(m_enemyAttackVO)
         {
            ClearUtil.clearObject(m_enemyAttackVO);
         }
         m_enemyAttackVO = null;
         super.clear();
      }
      
      override public function render() : void
      {
         super.render();
         if(m_world == null)
         {
            return;
         }
         if(m_animalEntity.isInDie() == false && m_animalEntity.getWorld() && m_animalEntity.getWorld().isStop() == false)
         {
            m_enemyAI.render();
         }
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         if(param1.hasOwnProperty("backattack"))
         {
            m_beAttackNum = int(param1.backattack);
         }
         m_enemyAttackVO.initByXML(param1.enemyAttackData[0]);
         m_lastHp = getTotalHp();
      }
      
      public function getActiveSkillNumOfAbleRunInHurt() : uint
      {
         return 0;
      }
      
      public function getActiveSkillOfAbleRunInHurtByIndex(param1:uint) : Skill
      {
         return null;
      }
      
      public function getActiveSkillNumOfRun() : uint
      {
         return 0;
      }
      
      public function getActiveSkillOfRunByIndex(param1:uint) : Skill
      {
         return null;
      }
      
      public function getUnableAttackMinInterval() : uint
      {
         return m_enemyAttackVO.getUnableAttackMinInterval();
      }
      
      public function getUnableAttackMaxInterval() : uint
      {
         return m_enemyAttackVO.getUnableAttackMaxInterval();
      }
      
      override public function isFoe(param1:IEntity) : Boolean
      {
         super.isFoe(param1);
         return m_isFoe || JudgeIsFoe.enemyJudgeEntityIsFoe(param1);
      }
      
      public function getRebuildAllFoeInterval() : uint
      {
         return m_enemyAttackVO.getRebuildAllFoeInterval();
      }
      
      public function getHpShowFrameLabel() : String
      {
         return null;
      }
      
      override public function isAbleRunSkill(param1:Skill) : Boolean
      {
         return false;
      }
      
      override public function runSkill(param1:Skill) : void
      {
      }
      
      override protected function addToWorld(param1:IEntity, param2:World) : void
      {
         super.addToWorld(param1,param2);
         m_enemyAI.init(m_animalEntity,param2,this);
         m_world = param2;
      }
      
      override protected function beAttack(param1:IEntity, param2:AttackData, param3:ISkill, param4:IEntity) : void
      {
         NormalDancer.m_isStop = false;
         super.beAttack(param1,param2,param3,param4);
         decCurrentHp(param2.getHurt());
         if(param1.getExtra() is AutoAttackPetXydzjs || param1.getExtra() is PetXydzjs)
         {
            GameEvent.eventDispacher.dispatchEvent(new NewLevelEvent("timedown3"));
         }
         if(param4.getX() < param1.getX())
         {
            if(param4.getShowDirection() == -1 && (param3 == null && !(param1 is TimeLimitEntity)))
            {
               m_beNumAttack++;
               if(m_beNumAttack >= m_beAttackNum)
               {
                  decCurrentHp(999999999);
               }
            }
         }
         else if(param4.getShowDirection() == 1 && (param3 == null && !(param1 is TimeLimitEntity)))
         {
            m_beNumAttack++;
            if(m_beNumAttack >= m_beAttackNum)
            {
               decCurrentHp(999999999);
            }
         }
         if(m_enemyVO.getCurrentBlood() <= 0)
         {
            m_animalEntity.die();
         }
      }
      
      override protected function entityAnimationPlayReachFrameLabel(param1:String) : void
      {
         super.entityAnimationPlayReachFrameLabel(param1);
      }
      
      override public function isInvincible() : Boolean
      {
         if(super.isInvincible())
         {
            return true;
         }
         return false;
      }
      
      public function getLastHp() : uint
      {
         return m_lastHp;
      }
      
      public function setLastHp(param1:uint) : void
      {
         m_lastHp = param1;
      }
   }
}

