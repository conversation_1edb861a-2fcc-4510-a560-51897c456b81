package UI.MiragePanel
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PopResetPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_myloader:YJFYLoader;
      
      private var m_btnOK:ButtonLogicShell2;
      
      private var m_btnNO:ButtonLogicShell2;
      
      private var m_txtMoney:TextField;
      
      public function PopResetPanel()
      {
         super();
         loadRes();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_btnOK);
         m_btnOK = null;
         ClearUtil.clearObject(m_btnNO);
         m_btnNO = null;
         super.clear();
      }
      
      public function loadRes() : void
      {
         addEventListener("clickButton",clickButton,true,0,true);
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myloader.getClass("UISprite2/popresetpanel.swf","popmiragePanel",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         this.addChild(m_show);
         initShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function initShow() : void
      {
         m_show.mouseChildren = true;
         m_show.mouseEnabled = true;
         m_btnOK = new ButtonLogicShell2();
         m_btnOK.setShow(m_show["btnOK"]);
         m_btnOK.getShow().mouseEnabled = true;
         m_btnNO = new ButtonLogicShell2();
         m_btnNO.setShow(m_show["btnNo"]);
         m_btnNO.getShow().mouseEnabled = true;
         m_txtMoney = m_show["moneytxt"] as TextField;
         showPanel();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_btnOK:
               buy2();
               break;
            case m_btnNO:
               hide();
         }
      }
      
      private function buy2() : void
      {
         var price:uint = uint(int(XMLSingle.getInstance().dataXML.MirageNum.@ticketPrice));
         var ticketId:String = String(XMLSingle.getInstance().dataXML.MirageNum.@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买幻化次数";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               throw new Error("购买物品id前后端不相同！");
            }
            MirageData.getInstance().currentMirageNum = 0;
            hide();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      public function hide() : void
      {
         this.x = 10000;
         this.y = 10000;
         this.visible = false;
      }
      
      public function showPanel() : void
      {
         if(m_txtMoney)
         {
            m_txtMoney.text = String(XMLSingle.getInstance().dataXML.MirageNum.@ticketPrice);
         }
         this.x = 500;
         this.y = 300;
         this.mouseChildren = true;
         this.mouseEnabled = true;
         this.visible = true;
      }
   }
}

