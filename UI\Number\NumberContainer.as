package UI.Number
{
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IUINumber;
   import flash.display.DisplayObject;
   
   public class NumberContainer extends MySprite
   {
      
      private var _singleDigit:IUINumber;
      
      private var _tensDigit:IUINumber;
      
      private var _hundredDigit:IUINumber;
      
      public function NumberContainer()
      {
         super();
      }
      
      public function initNumber(param1:int, param2:String, param3:Number) : void
      {
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_singleDigit)
         {
            _singleDigit.clear();
         }
         _singleDigit = null;
         if(_tensDigit)
         {
            _tensDigit.clear();
         }
         _tensDigit = null;
         if(_hundredDigit)
         {
            _hundredDigit.clear();
         }
         _hundredDigit = null;
         if(param1 < 0)
         {
            trace("级数不能小于等于零");
            return;
         }
         if(param1 >= 0 && param1 <= 9)
         {
            _singleDigit = addOneNumber(param1,param3 / 2,0,param2);
         }
         else if(param1 > 9 && param1 <= 99)
         {
            _loc6_ = param1 / 10;
            _tensDigit = addOneNumber(_loc6_,0,0,param2);
            _loc7_ = param1 % 10;
            _singleDigit = addOneNumber(_loc7_,param3,0,param2);
         }
         else if(param1 > 99 && param1 <= 999)
         {
            _loc5_ = param1 / 100;
            _loc4_ = param1 - _loc5_ * 100;
            _loc6_ = _loc4_ / 10;
            _loc7_ = _loc4_ % 10;
            _hundredDigit = addOneNumber(_loc5_,-0.5 * param3,0,param2);
            _tensDigit = addOneNumber(_loc6_,0.5 * param3,0,param2);
            _singleDigit = addOneNumber(_loc7_,param3 * 1.5,0,param2);
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_singleDigit)
         {
            _singleDigit.clear();
         }
         _singleDigit = null;
         if(_tensDigit)
         {
            _tensDigit.clear();
         }
         _tensDigit = null;
         if(_hundredDigit)
         {
            _hundredDigit.clear();
         }
         _hundredDigit = null;
      }
      
      private function addOneNumber(param1:int, param2:Number = 0, param3:Number = 0, param4:String = "") : IUINumber
      {
         var _loc5_:IUINumber = null;
         if(param1 < 0 || param1 > 9)
         {
            return null;
         }
         switch(param1)
         {
            case 0:
               _loc5_ = new Zero(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 1:
               _loc5_ = new One(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 2:
               _loc5_ = new Two(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 3:
               _loc5_ = new Three(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 4:
               _loc5_ = new Four(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 5:
               _loc5_ = new Five(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 6:
               _loc5_ = new Six(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 7:
               _loc5_ = new Seven(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 8:
               _loc5_ = new Eight(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
               break;
            case 9:
               _loc5_ = new Nine(param4);
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_ as DisplayObject);
         }
         return _loc5_;
      }
   }
}

