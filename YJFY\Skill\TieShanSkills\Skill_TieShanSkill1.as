package YJFY.Skill.TieShanSkills
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   
   public class Skill_TieShanSkill1 extends CuboidAreaAttackSkill_OneSkillShow implements IPushSkill
   {
      
      protected var m_skillMoveSpeed:int;
      
      private var m_isAttackEdEntities:Vector.<IEntity>;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_isPushEntity:Boolean;
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_skillDirectionX:int;
      
      protected var m_pushForce:P3DVector3D;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_pushForce2:P3DVector3D;
      
      public function Skill_TieShanSkill1()
      {
         super();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
         m_isAttackEdEntities = new Vector.<IEntity>();
         m_pushForce = new P3DVector3D();
         m_pushForce2 = new P3DVector3D();
         m_pushForce.setTo(10000,0,0);
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = pushEntity;
      }
      
      override public function clear() : void
      {
         m_skillEndFrameLabel = null;
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         ClearUtil.nullArr(m_isAttackEdEntities,false,false,false);
         m_isAttackEdEntities = null;
         ClearUtil.clearObject(m_pushForce);
         m_pushForce = null;
         ClearUtil.clearObject(m_pushForce2);
         m_pushForce2 = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillMoveSpeed = int(param1.@skillMoveSpeed);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
      }
      
      override public function isAbleRun() : Boolean
      {
         return super.isAbleRun();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return false;
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         renderPushEntity();
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.forceSetDirection(m_owner.getShowDirection(),0);
         m_owner.setMoveSpeed(m_skillMoveSpeed);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_isAttackEdEntities.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_isAttackEdEntities[_loc3_] == param1)
            {
               return;
            }
            _loc3_++;
         }
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && param1 is IAnimalEntity)
         {
            m_isAttackEdEntities.push(param1);
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         ClearUtil.nullArr(m_isAttackEdEntities,false,false,false);
         m_isAttackEdEntities.length = 0;
         m_owner.getBody().setVelocity2(0,0,0);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_skillEndFrameLabel === _loc2_)
         {
            endSkill1();
         }
      }
      
      override protected function ownerSetDirection(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
         if(m_isRun)
         {
            if(m_owner)
            {
               m_owner.forceSetDirection(m_skillDirectionX,0);
            }
         }
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      private function renderPushEntity() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      private function pushEntity(param1:IEntity) : void
      {
         pushEntity2(param1);
         if(m_isPushEntity)
         {
            m_pushForce2.multi2(m_owner.getShowDirection() * param1.getBody().getMass(),m_pushForce);
            param1.applyForce(m_pushForce2);
         }
         attackSuccess(param1);
      }
      
      private function pushEntity2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
   }
}

