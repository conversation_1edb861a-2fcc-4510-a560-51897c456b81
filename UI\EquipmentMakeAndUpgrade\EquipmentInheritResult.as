package UI.EquipmentMakeAndUpgrade
{
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MessageBox.MessageBoxFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipmentInheritResult extends MySprite
   {
      
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["OpenEquipMagic"];
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_myMagicPanel:EquipMagicInherit;
      
      private var _newContainer:Sprite;
      
      private var _newEquipment:Equipment;
      
      private var _sayTexts:Vector.<TextField> = new Vector.<TextField>();
      
      public function EquipmentInheritResult()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_myMagicPanel = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         if(_sayTexts)
         {
            _loc1_ = int(_sayTexts.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _sayTexts[_loc2_] = null;
               _loc2_++;
            }
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_myMagicPanel.closeUseContractSuccessInforPanel();
         }
      }
      
      public function init(param1:EquipMagicInherit, param2:Boolean) : void
      {
         var myEquipmentMagicCreat:EquipMagicInherit = param1;
         var bSuccess:Boolean = param2;
         m_myMagicPanel = myEquipmentMagicCreat;
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("ShowCreateResultPanel") as MovieClip;
            }
            addChild(_show);
            _showMC = new MovieClipPlayLogicShell();
            _showMC.setShow(_show);
            if(bSuccess)
            {
               _showMC.gotoAndStop("successResult");
            }
            else
            {
               _showMC.gotoAndStop("failResult");
            }
            initShow();
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function initShow() : void
      {
         m_quitBtn.setShow(_show["quitBtn"]);
         _newContainer = _show["newContainer"];
         _sayTexts.push(_show["attr1"]);
         _sayTexts.push(_show["attr2"]);
         _sayTexts.push(_show["attr3"]);
         _sayTexts.push(_show["attr4"]);
         _sayTexts.push(_show["attr5"]);
         this.x = 240.35;
         this.y = -52;
      }
      
      public function AddShowReultEquipment(param1:Equipment, param2:Boolean, param3:Boolean) : void
      {
         var _loc13_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:String = null;
         var _loc14_:Array = null;
         var _loc4_:EquipmentVO = null;
         var _loc5_:* = undefined;
         var _loc7_:* = undefined;
         var _loc11_:WeaponEquipmentVO = null;
         var _loc6_:PreciousEquipmentVO = null;
         var _loc12_:NecklaceEquipmentVO = null;
         var _loc15_:GourdEquipmentVO = null;
         var _loc9_:ClothesEquipmentVO = null;
         if(param2 == false)
         {
            if(param3)
            {
               _sayTexts[1].text = "受“圣器护佑”保护，装备的附魔属性没有消失";
            }
            else
            {
               _sayTexts[1].text = "很遗憾，装备的附魔属性消失了";
            }
         }
         if(param1)
         {
            _newEquipment = param1.clone();
            _newEquipment.addEventListener("rollOver",onOver,false,0,true);
            _newEquipment.addEventListener("rollOut",onOut,false,0,true);
            _newEquipment.equipmentVO = param1.equipmentVO;
            (_newEquipment as DisplayObject).scaleX = (param1 as DisplayObject).scaleY = 1.1;
            _newContainer.addChild(_newEquipment as DisplayObject);
            if(param2 == true)
            {
               switch((_loc4_ = param1.equipmentVO).equipmentType)
               {
                  case "weapon":
                     _loc11_ = _loc4_ as WeaponEquipmentVO;
                     _loc5_ = _loc11_.addPlayerSaveAttr;
                     _loc7_ = _loc11_.addPlayerSaveAttrVals;
                     _loc13_ = int(_loc5_.length);
                     _loc8_ = 0;
                     while(_loc8_ < _loc13_)
                     {
                        _loc14_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc5_[_loc8_],_loc7_[_loc8_]);
                        _loc10_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc14_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc14_[1],16);
                        _sayTexts[_loc8_].text = _loc14_[0] + "：" + _loc14_[1];
                        _loc14_[0] = null;
                        _loc14_[1] = null;
                        _loc14_ = null;
                        _loc8_++;
                     }
                     break;
                  case "precious":
                     _loc6_ = _loc4_ as PreciousEquipmentVO;
                     _loc5_ = _loc6_.addPlayerSaveAttr;
                     _loc7_ = _loc6_.addPlayerSaveAttrVals;
                     _loc13_ = int(_loc5_.length);
                     _loc8_ = 0;
                     while(_loc8_ < _loc13_)
                     {
                        _loc14_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc5_[_loc8_],_loc7_[_loc8_]);
                        _loc10_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc14_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc14_[1],16);
                        _sayTexts[_loc8_].text = _loc14_[0] + "：" + _loc14_[1];
                        _loc14_[0] = null;
                        _loc14_[1] = null;
                        _loc14_ = null;
                        _loc8_++;
                     }
                     break;
                  case "necklace":
                     _loc12_ = _loc4_ as NecklaceEquipmentVO;
                     _loc5_ = _loc12_.addPlayerSaveAttr;
                     _loc7_ = _loc12_.addPlayerSaveAttrVals;
                     _loc13_ = int(_loc5_.length);
                     _loc8_ = 0;
                     while(_loc8_ < _loc13_)
                     {
                        _loc14_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc5_[_loc8_],_loc7_[_loc8_]);
                        _loc10_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc14_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc14_[1],16);
                        _sayTexts[_loc8_].text = _loc14_[0] + "：" + _loc14_[1];
                        _loc14_[0] = null;
                        _loc14_[1] = null;
                        _loc14_ = null;
                        _loc8_++;
                     }
                     break;
                  case "gourd":
                     _loc15_ = _loc4_ as GourdEquipmentVO;
                     _loc5_ = _loc15_.addPlayerSaveAttr;
                     _loc7_ = _loc15_.addPlayerSaveAttrVals;
                     _loc13_ = int(_loc5_.length);
                     _loc8_ = 0;
                     while(_loc8_ < _loc13_)
                     {
                        _loc14_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc5_[_loc8_],_loc7_[_loc8_]);
                        _loc10_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc14_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc14_[1],16);
                        _sayTexts[_loc8_].text = _loc14_[0] + "：" + _loc14_[1];
                        _loc14_[0] = null;
                        _loc14_[1] = null;
                        _loc14_ = null;
                        _loc8_++;
                     }
                     break;
                  case "clothes":
                     _loc9_ = _loc4_ as ClothesEquipmentVO;
                     _loc5_ = _loc9_.addPlayerSaveAttr;
                     _loc7_ = _loc9_.addPlayerSaveAttrVals;
                     _loc13_ = int(_loc5_.length);
                     _loc8_ = 0;
                     while(_loc8_ < _loc13_)
                     {
                        _loc14_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(_loc5_[_loc8_],_loc7_[_loc8_]);
                        _loc10_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc14_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc14_[1],16);
                        _sayTexts[_loc8_].text = _loc14_[0] + "：" + _loc14_[1];
                        _loc14_[0] = null;
                        _loc14_[1] = null;
                        _loc14_ = null;
                        _loc8_++;
                     }
               }
            }
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

