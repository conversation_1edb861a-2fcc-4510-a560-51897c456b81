package UI.ShiTu
{
   import UI.DataManagerParent;
   import UI.Players.PlayerVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class XiuLianContent extends DataManagerParent
   {
      
      public static const SHI_FU_OWNER:String = "ShiFu";
      
      public static const TU_DI_OWNER:String = "TuDi";
      
      public var name:String;
      
      public var name2:String;
      
      public var description:String;
      
      public var content:String;
      
      public var owner:String;
      
      public var xiuLianTargetVO:IXiuLianTargetVO;
      
      private var _level:int;
      
      private var _id:String;
      
      private var _needXiuLianValue:int;
      
      private var _currentXiuLianValue:int;
      
      public var addAttributes:Vector.<String> = new Vector.<String>();
      
      public var addAttributeValueObjects:Vector.<PromoteValueObject> = new Vector.<PromoteValueObject>();
      
      public function XiuLianContent()
      {
         super();
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         ClearUtil.nullArr(addAttributes);
         addAttributes = null;
         ClearUtil.nullArr(addAttributeValueObjects);
         addAttributeValueObjects = null;
         xiuLianTargetVO = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.level = _level;
         _antiwear.id = _id;
         _antiwear.needXiuLianValue = _needXiuLianValue;
         _antiwear.currentXiuLianValue = _currentXiuLianValue;
      }
      
      public function get level() : int
      {
         return _antiwear.level;
      }
      
      public function set level(param1:int) : void
      {
         _antiwear.level = param1;
      }
      
      public function get id() : String
      {
         return _antiwear.id;
      }
      
      public function set id(param1:String) : void
      {
         _antiwear.id = param1;
      }
      
      public function get needXiuLianValue() : int
      {
         return _antiwear.needXiuLianValue;
      }
      
      public function set needXiuLianValue(param1:int) : void
      {
         _antiwear.needXiuLianValue = param1;
      }
      
      public function get currentXiuLianValue() : int
      {
         return _antiwear.currentXiuLianValue;
      }
      
      public function set currentXiuLianValue(param1:int) : void
      {
         _antiwear.currentXiuLianValue = param1;
      }
      
      public function get maxLevel() : int
      {
         var _loc1_:PlayerVO = null;
         if(xiuLianTargetVO is PlayerVO)
         {
            _loc1_ = xiuLianTargetVO as PlayerVO;
         }
         else
         {
            _loc1_ = (xiuLianTargetVO as TuDiVO).playerVO;
         }
         return int(XMLSingle.getInstance().xiuLianContentXML[owner][0].LevelData[0].XiuLianContent.(@content == content)[0].maxLevel.(@minLevel <= _loc1_.level && @maxLevel >= _loc1_.level)[0].@value);
      }
      
      public function getId2() : String
      {
         var _loc4_:int = 0;
         var _loc1_:Array = id.split("_");
         var _loc3_:int = _loc1_.length - 1;
         var _loc2_:String = "";
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ += _loc1_[_loc4_];
            if(_loc4_ < _loc3_ - 1)
            {
               _loc2_ += "_";
            }
            _loc4_++;
         }
         ClearUtil.clearObject(_loc1_);
         _loc1_ = null;
         trace("xiu lian content id2:",_loc2_);
         return _loc2_;
      }
   }
}

