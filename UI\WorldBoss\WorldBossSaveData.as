package UI.WorldBoss
{
   import UI.DataManagerParent;
   import UI.Players.VipVO;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class WorldBossSaveData extends DataManagerParent
   {
      
      private static var m_instance:WorldBossSaveData;
      
      private var m_savePowerValue:Number;
      
      private var m_savePowerDate:String = "";
      
      private var m_bossHurtDatas:Vector.<BossHurtData>;
      
      private var m_proAttackPercent:Number = 0;
      
      private var m_buyNum:int;
      
      private var m_lastFightBossId:String;
      
      private var m_dataTime:String;
      
      private var m_worldBossDataXML:XML;
      
      public function WorldBossSaveData()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : WorldBossSaveData
      {
         if(m_instance == null)
         {
            m_instance = new WorldBossSaveData();
         }
         return m_instance;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.savePowerValue = m_savePowerValue;
         _antiwear.savePowerDate = m_savePowerDate;
         _antiwear.proAttackPercent = m_proAttackPercent;
         _antiwear.buyNum = m_buyNum;
         _antiwear.lastFightBossId = m_lastFightBossId;
         _antiwear.dataTime = m_dataTime;
      }
      
      private function get savePowerValue() : Number
      {
         return _antiwear.savePowerValue;
      }
      
      private function set savePowerValue(param1:Number) : void
      {
         _antiwear.savePowerValue = param1;
      }
      
      private function get savePowerDate() : String
      {
         return _antiwear.savePowerDate;
      }
      
      private function set savePowerDate(param1:String) : void
      {
         _antiwear.savePowerDate = param1;
      }
      
      private function get proAttackPercent() : Number
      {
         return _antiwear.proAttackPercent;
      }
      
      private function set proAttackPercent(param1:Number) : void
      {
         _antiwear.proAttackPercent = param1;
      }
      
      private function get buyNum() : int
      {
         return _antiwear.buyNum;
      }
      
      private function set buyNum(param1:int) : void
      {
         _antiwear.buyNum = param1;
      }
      
      private function get lastFightBossId() : String
      {
         return _antiwear.lastFightBossId;
      }
      
      private function set lastFightBossId(param1:String) : void
      {
         _antiwear.lastFightBossId = param1;
      }
      
      private function get dataTime() : String
      {
         return _antiwear.dataTime;
      }
      
      private function set dataTime(param1:String) : void
      {
         _antiwear.dataTime = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_bossHurtDatas);
         m_bossHurtDatas = null;
         m_instance = null;
         m_worldBossDataXML = null;
         m_instance = null;
      }
      
      public function initFromSaveXML(param1:XML, param2:String, param3:XML) : void
      {
         var _loc6_:XMLList = null;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:BossHurtData = null;
         m_worldBossDataXML = param3;
         if(param1.hasOwnProperty("WorldBoss"))
         {
            savePowerValue = Number(param1.WorldBoss[0].@p);
            savePowerDate = String(param1.WorldBoss[0].@pT);
            proAttackPercent = Number(param1.WorldBoss[0].@proA);
            buyNum = int(param1.WorldBoss[0].@buyNum);
            lastFightBossId = String(param1.WorldBoss[0].@bossId);
            dataTime = String(param1.WorldBoss[0].@dataTime);
            if(Boolean(savePowerDate) == false)
            {
               savePowerDate = param2;
            }
            _loc6_ = param1.WorldBoss[0].boss;
            _loc5_ = int(_loc6_ ? _loc6_.length() : 0);
            m_bossHurtDatas = new Vector.<BossHurtData>();
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc4_ = new BossHurtData();
               _loc4_.initFromSaveXML(_loc6_[_loc7_]);
               m_bossHurtDatas.push(_loc4_);
               _loc7_++;
            }
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc4_:int = 0;
         var _loc3_:XML = null;
         var _loc2_:XML = <WorldBoss />;
         _loc2_.@p = Math.round(savePowerValue);
         _loc2_.@pT = savePowerDate;
         if(proAttackPercent > 0)
         {
            _loc2_.@proA = proAttackPercent.toFixed(4);
         }
         if(buyNum > 0)
         {
            _loc2_.@buyNum = buyNum;
         }
         if(lastFightBossId)
         {
            _loc2_.@bossId = lastFightBossId;
         }
         if(dataTime)
         {
            _loc2_.@dataTime = dataTime;
         }
         var _loc1_:int = m_bossHurtDatas ? m_bossHurtDatas.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = m_bossHurtDatas[_loc4_].exportXML();
            _loc2_.appendChild(_loc3_);
            _loc4_++;
         }
         return _loc2_;
      }
      
      public function isResetData(param1:String, param2:int, param3:String) : void
      {
         if(Boolean(dataTime) == false)
         {
            dataTime = param1;
            ZeroedBossHurt(param1);
            return;
         }
         var _loc4_:int = Math.floor(new TimeUtil().timeInterval(param3,dataTime) / param2);
         var _loc5_:int = Math.floor(new TimeUtil().timeInterval(param3,param1) / param2);
         if(_loc4_ != _loc5_)
         {
            ZeroedBossHurt(param1);
         }
      }
      
      private function ZeroedBossHurt(param1:String) : void
      {
         ClearUtil.nullArr(m_bossHurtDatas);
         m_bossHurtDatas = null;
         dataTime = param1;
         m_lastFightBossId = "";
      }
      
      public function getGameData(param1:VipVO, param2:String, param3:WorldBossData) : void
      {
         param3.initFromSaveData(savePowerValue,savePowerDate,param1,param2,m_worldBossDataXML,this);
      }
      
      public function reSetPowerValueSaveData(param1:WorldBossData, param2:String) : void
      {
         savePowerValue = param1.getPowerValue();
         savePowerDate = param2;
      }
      
      public function reSetLastFightBossId(param1:String) : void
      {
         this.lastFightBossId = param1;
      }
      
      public function addPromoteAttackPercent(param1:Number) : void
      {
         proAttackPercent += param1;
         buyNum++;
      }
      
      public function getBuyNum() : int
      {
         return buyNum;
      }
      
      public function getLastFightBossId() : String
      {
         return lastFightBossId;
      }
      
      public function setZeroPromoteAttackPercent() : void
      {
         proAttackPercent = 0;
         buyNum = 0;
      }
      
      public function getProAttackPercent() : Number
      {
         return proAttackPercent;
      }
      
      public function getBossHurtDataNum() : int
      {
         return m_bossHurtDatas ? m_bossHurtDatas.length : 0;
      }
      
      public function getBossHurtDataByIndex(param1:int) : BossHurtData
      {
         if(m_bossHurtDatas == null || param1 < 0 || param1 >= m_bossHurtDatas.length)
         {
            return null;
         }
         return m_bossHurtDatas[param1];
      }
      
      public function getBossHurtDataByBossId(param1:String) : BossHurtData
      {
         var _loc4_:int = 0;
         var _loc2_:BossHurtData = null;
         var _loc3_:int = getBossHurtDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = getBossHurtDataByIndex(_loc4_);
            if(Boolean(_loc2_) && _loc2_.getBossId() == param1)
            {
               return _loc2_;
            }
            _loc4_++;
         }
         return null;
      }
      
      public function addBossHurtData(param1:BossHurtData) : void
      {
         if(m_bossHurtDatas == null)
         {
            m_bossHurtDatas = new Vector.<BossHurtData>();
         }
         m_bossHurtDatas.push(param1);
      }
   }
}

