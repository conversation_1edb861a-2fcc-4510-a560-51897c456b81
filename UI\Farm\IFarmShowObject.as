package UI.Farm
{
   import UI.UIInterface.OldInterface.ISprite;
   
   public interface IFarmShowObject extends ISprite
   {
      
      function get farmShowObjectVO() : FarmShowObjectVO;
      
      function set farmShowObjectVO(param1:FarmShowObjectVO) : void;
      
      function get hI() : int;
      
      function sethv(param1:int, param2:int) : void;
      
      function get vJ() : int;
      
      function set framShowState(param1:int) : void;
      
      function destory() : void;
      
      function clear() : void;
   }
}

