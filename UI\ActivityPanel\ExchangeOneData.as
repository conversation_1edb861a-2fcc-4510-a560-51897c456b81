package UI.ActivityPanel
{
   import UI.Other.NeedEquipmentData;
   import UI.Players.PlayerVO;
   import YJFY.Utils.ClearUtil;
   
   public class ExchangeOneData
   {
      
      private var m_needEquipmentDatas:Vector.<NeedEquipmentData>;
      
      private var m_targetEquipmentDatas:Vector.<EquipmentData>;
      
      public function ExchangeOneData()
      {
         super();
         m_needEquipmentDatas = new Vector.<NeedEquipmentData>();
         m_targetEquipmentDatas = new Vector.<EquipmentData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_needEquipmentDatas);
         m_needEquipmentDatas = null;
         ClearUtil.clearObject(m_targetEquipmentDatas);
         m_targetEquipmentDatas = null;
      }
      
      public function initByXML(param1:XML, param2:String) : void
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:NeedEquipmentData = null;
         var _loc3_:EquipmentData = null;
         var _loc4_:XMLList = param1.needEquipment;
         _loc5_ = int(_loc4_ ? _loc4_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc6_ = new NeedEquipmentData(_loc4_[_loc7_].@id,_loc4_[_loc7_].@num,param2);
            m_needEquipmentDatas.push(_loc6_);
            _loc7_++;
         }
         _loc4_ = param1.targetEquipment;
         _loc5_ = int(_loc4_ ? _loc4_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc3_ = new EquipmentData(_loc4_[_loc7_].@id,_loc4_[_loc7_].@num,param2);
            m_targetEquipmentDatas.push(_loc3_);
            _loc7_++;
         }
      }
      
      public function getNeedEquipmentDataNum() : int
      {
         return m_needEquipmentDatas.length;
      }
      
      public function getNeedEquipmentDataByIndex(param1:int) : NeedEquipmentData
      {
         return m_needEquipmentDatas[param1];
      }
      
      public function getTargetEquipmentDataNum() : int
      {
         return m_targetEquipmentDatas.length;
      }
      
      public function getTargetEquipmentDataByIndex(param1:int) : EquipmentData
      {
         return m_targetEquipmentDatas[param1];
      }
      
      public function refreshNeedEqHaveNumInPlayer(param1:PlayerVO, param2:PlayerVO) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(m_needEquipmentDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            m_needEquipmentDatas[_loc4_].setCurrentNum(0);
            m_needEquipmentDatas[_loc4_].setCurrentNum(m_needEquipmentDatas[_loc4_].getCurrentNum() + param1.getHaveEquipmentNumInPackage(m_needEquipmentDatas[_loc4_].getEquipmentVO()));
            if(param2)
            {
               m_needEquipmentDatas[_loc4_].setCurrentNum(m_needEquipmentDatas[_loc4_].getCurrentNum() + param2.getHaveEquipmentNumInPackage(m_needEquipmentDatas[_loc4_].getEquipmentVO()));
            }
            _loc4_++;
         }
      }
      
      public function getIsAbleExChange() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_needEquipmentDatas.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_needEquipmentDatas[_loc2_].getCurrentNum() < m_needEquipmentDatas[_loc2_].getNum())
            {
               return false;
            }
            _loc2_++;
         }
         return true;
      }
   }
}

