package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   import YJFY.Utils.ShowUtil;
   import flash.display.Sprite;
   import flash.text.Font;
   import flash.text.TextField;
   
   public class CMSXChangeBarLogicShell
   {
      
      protected var _type:String;
      
      protected var _show:Sprite;
      
      protected var _dataText:TextField;
      
      protected var _mask:Sprite;
      
      protected var _upBar:Sprite;
      
      protected var _font:Font;
      
      public function CMSXChangeBarLogicShell()
      {
         super();
      }
      
      public function setFont(param1:Font) : void
      {
         _font = param1;
      }
      
      public function setShow(param1:Sprite, param2:Boolean = true) : void
      {
         var _loc3_:ShowUtil = null;
         _show = param1;
         _dataText = param1["dataText"];
         if(_dataText)
         {
            _dataText.mouseEnabled = false;
         }
         _mask = param1["mask"];
         if(_mask == null)
         {
            _mask = param1["barMask"];
         }
         _upBar = param1["up"];
         if(_upBar == null)
         {
            _upBar = param1["upBar"];
         }
         if(_upBar)
         {
            _upBar.mask = _mask;
         }
         _type = "horizontal";
         if(_dataText != null && param2)
         {
            _loc3_ = new ShowUtil();
            _loc3_.changeTextFieldFont(_font.fontName,_dataText);
         }
      }
      
      public function getShow() : Sprite
      {
         return _show;
      }
      
      public function clear() : void
      {
         if(_upBar)
         {
            _upBar.mask = null;
         }
         _show = null;
         _dataText = null;
         _mask = null;
         _upBar = null;
         _font = null;
      }
      
      public function setDataShow(param1:String) : void
      {
         if(_dataText)
         {
            _dataText.text = param1;
         }
      }
      
      public function change(param1:Number) : void
      {
         if(param1 < 0 || param1 > 1)
         {
            throw new Error("值不能小于0，或大于1!");
         }
         var _loc2_:String = _type;
         if("horizontal" !== _loc2_)
         {
            _mask.scaleY = param1;
         }
         else
         {
            _mask.scaleX = param1;
         }
      }
      
      public function setType(param1:CMSXChangeBarLogicShellType) : void
      {
         if(param1)
         {
            _type = param1.type;
         }
      }
   }
}

