package YJFY.Other
{
   public class SaveXMLFunction
   {
      
      public function SaveXMLFunction()
      {
         super();
      }
      
      public static function getPlayerTypeDataFromSaveXML(param1:XML) : PlayerTypeDataInSaveXML
      {
         var _loc4_:String = null;
         var _loc2_:XMLList = XML(param1.Data[0]).children();
         var _loc3_:String = XML(_loc2_[0]).localName() as String;
         if(_loc2_.length() > 1)
         {
            _loc4_ = XML(_loc2_[1]).localName() as String;
         }
         return new PlayerTypeDataInSaveXML(getPlayerStr(_loc3_),getPlayerStr(_loc4_));
      }
      
      private static function getPlayerStr(param1:String) : String
      {
         switch(param1)
         {
            case "Monkey":
               return "SunWuKong";
            case "Dragon":
               return "BaiLongMa";
            case "ErLangShen":
               return "ErLangShen";
            case "ChangE":
               return "Chang<PERSON>";
            case "<PERSON>":
               return "<PERSON>";
            case "Tie<PERSON><PERSON>":
               return "Tie<PERSON>han";
            case "Houyi":
               return "Houyi";
            case "ZiXia":
               return "ZiXia";
            default:
               return "";
         }
      }
   }
}

