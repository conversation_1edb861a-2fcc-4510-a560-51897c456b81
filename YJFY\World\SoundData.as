package YJFY.World
{
   public class SoundData
   {
      
      private var m_id:String;
      
      private var m_name:String;
      
      private var m_swfPath:String;
      
      private var m_className:String;
      
      public function SoundData(param1:String, param2:String, param3:String, param4:String)
      {
         super();
         m_id = param1;
         m_name = param2;
         m_swfPath = param3;
         m_className = param4;
      }
      
      public function clear() : void
      {
         m_id = null;
         m_name = null;
         m_swfPath = null;
         m_className = null;
      }
      
      public function getId() : String
      {
         return m_id;
      }
      
      public function getName() : String
      {
         return m_name;
      }
      
      public function getSwfPath() : String
      {
         return m_swfPath;
      }
      
      public function getClassName() : String
      {
         return m_className;
      }
   }
}

