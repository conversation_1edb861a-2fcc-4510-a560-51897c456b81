package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.GhostEntity.GhostEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.IEntityContainer;
   import YJFY.Entity.IFriend;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.BitmapDatasEntityAnimationDefinition;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.MovieClipToBitmapDatas.DataOfBitmapData;
   import YJFY.World.CoordinateFunction;
   import YJFY.World.World;
   import YJFY.geom.Area3D.Cuboid;
   
   public class TimeLimitEntity_MonkeySkill3 extends TimeLimitEntity implements IFriend
   {
      
      private var m_pushCuboidArea:Cuboid;
      
      private var m_pushCuboidAreaToWorld:Cuboid;
      
      private var m_createGhostEntityTime:Number;
      
      private var m_createGhostEngtityInterval:int;
      
      private var m_ghostEntityAlpha:Number;
      
      private var m_ghostEntityDurationTime:int;
      
      private var m_createdGhostEntitys:Vector.<GhostEntity>;
      
      private var m_isAblePushEntity:Boolean;
      
      private var m_pushEntityListener:TimeLimitEntity_MonkeySkill3Listener;
      
      public function TimeLimitEntity_MonkeySkill3()
      {
         super();
         m_pushCuboidArea = new Cuboid();
         m_pushCuboidAreaToWorld = new Cuboid();
         m_createdGhostEntitys = new Vector.<GhostEntity>();
      }
      
      override protected function fullyClear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         ClearUtil.clearObject(m_pushCuboidArea);
         m_pushCuboidArea = null;
         ClearUtil.clearObject(m_pushCuboidAreaToWorld);
         m_pushCuboidAreaToWorld = null;
         if(m_createdGhostEntitys)
         {
            _loc1_ = int(m_createdGhostEntitys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_createdGhostEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_createdGhostEntitys[_loc2_]);
               m_createdGhostEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_createdGhostEntitys.length = 0;
            m_createdGhostEntitys = null;
         }
         m_pushEntityListener = null;
         super.fullyClear();
      }
      
      override public function setParent(param1:IEntityContainer, param2:World) : void
      {
         super.setParent(param1,param2);
         if(m_world)
         {
            m_createGhostEntityTime = m_world.getWorldTime() + m_createGhostEngtityInterval;
         }
      }
      
      public function setIsAblePushEntity(param1:Boolean) : void
      {
         m_isAblePushEntity = param1;
      }
      
      public function setPushEntityListener(param1:TimeLimitEntity_MonkeySkill3Listener) : void
      {
         m_pushEntityListener = param1;
      }
      
      override protected function initOtherData() : void
      {
         super.initOtherData();
         m_pushCuboidArea.setTo(m_xml.pushRange[0].@x,m_xml.pushRange[0].@y,m_xml.pushRange[0].@z,m_xml.pushRange[0].@xRange,m_xml.pushRange[0].@yRange,m_xml.pushRange[0].@zRange);
         m_createGhostEngtityInterval = int(m_xml.otherData[0].@createGhostEngtityInterval);
         m_ghostEntityAlpha = Number(m_xml.otherData[0].@ghostEntityAlpha);
         m_ghostEntityDurationTime = int(m_xml.otherData[0].@ghostEntityDurationTime);
      }
      
      override public function render(param1:World) : void
      {
         if(m_createGhostEntityTime < param1.getWorldTime())
         {
            createGhostEntity();
            m_createGhostEntityTime = param1.getWorldTime() + m_createGhostEngtityInterval;
         }
         refreshWorldPushCuboid();
         pushEnemys();
         super.render(param1);
      }
      
      private function pushEnemys() : void
      {
         var _loc3_:int = 0;
         var _loc2_:IEntity = null;
         var _loc1_:int = int(m_world.getAllEntityNum());
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = m_world.getAllEnttiyByIndex(_loc3_);
            if(_loc2_ != this)
            {
               if(m_world.cuboidRangeIsContainsEntity(m_pushCuboidAreaToWorld,_loc2_))
               {
                  pushEntity2(this,_loc2_);
                  if(m_isAblePushEntity)
                  {
                     if(_loc2_ is AnimalEntity)
                     {
                        (_loc2_ as AnimalEntity).setShowDirection(-getShowDirection());
                        (_loc2_ as AnimalEntity).setNewPosition(getX() + _loc2_.getBodyXRange() / 2 * getShowDirection(),_loc2_.getY(),_loc2_.getZ());
                     }
                  }
               }
            }
            _loc3_++;
         }
      }
      
      private function pushEntity2(param1:TimeLimitEntity_MonkeySkill3, param2:IEntity) : void
      {
         if(m_pushEntityListener)
         {
            m_pushEntityListener.pushEntity(param1,param2);
         }
      }
      
      private function refreshWorldPushCuboid() : void
      {
         CoordinateFunction.cuboidRangeTranToWorld2(getShowDirection(),getX(),getY(),getZ(),m_pushCuboidArea,m_pushCuboidAreaToWorld);
      }
      
      private function createGhostEntity() : void
      {
         var _loc3_:DataOfBitmapData = null;
         var _loc2_:String = null;
         var _loc1_:GhostEntity_MonkeySkill3 = null;
         if((m_currentAnimation.getShow() as AnimationShow).getDefinition() is BitmapDatasEntityAnimationDefinition)
         {
            _loc3_ = ((m_currentAnimation.getShow() as AnimationShow).getDefinition() as BitmapDatasEntityAnimationDefinition).getDataOfBitmapData((m_currentAnimation.getShow() as AnimationShow).currentFrame - 1);
            _loc2_ = String(m_xml["die"][0].@defId);
            _loc1_ = new GhostEntity_MonkeySkill3();
            _loc1_.setDisappearAnimationDef((m_allAnimationsObj[_loc2_] as AnimationShow).getDefinition());
            _loc1_.setDisappearTime(m_ghostEntityDurationTime);
            _loc1_.init(_loc3_.getBitmapData(),_loc3_.getBitmapDataXOffset(),_loc3_.getBitmapDataYOffset(),getX(),getY(),getZ(),m_ghostEntityAlpha);
            m_world.addEntity(_loc1_);
            m_createdGhostEntitys.push(_loc1_);
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(param1.substr(0,"attackReach".length) == "attackReach")
         {
            attackReach();
         }
      }
   }
}

