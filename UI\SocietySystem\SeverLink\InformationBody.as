package UI.SocietySystem.SeverLink
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.InformationBodyDetail;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.InformationBodyDetailFactory;
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class InformationBody
   {
      
      public static const DATA_BYTE_LEN_FOR_INFORBODY_LEN:int = 4;
      
      public static const DATA_BYTE_LEN_FOR_INFOR_ID:int = 4;
      
      public var id_informationBody:int;
      
      protected var m_informationBodyDetail:InformationBodyDetail;
      
      public function InformationBody()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_informationBodyDetail);
         m_informationBodyDetail = null;
      }
      
      public function setDetailByByteArray(param1:ByteArray) : void
      {
         var _loc2_:InformationBodyDetailFactory = new InformationBodyDetailFactory();
         m_informationBodyDetail = _loc2_.createByIdAndByteArray(id_informationBody,param1);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
         if(m_informationBodyDetail.getInformationBodyId() == 0 || id_informationBody == 0 || m_informationBodyDetail.getInformationBodyId() != id_informationBody)
         {
            throw new Error("informationBodyId error!body id:" + id_informationBody + "bodyDetail id:" + m_informationBodyDetail.getInformationBodyId());
         }
      }
      
      public function setDetail(param1:InformationBodyDetail) : void
      {
         if(param1.getInformationBodyId() == 0 || id_informationBody == 0 || param1.getInformationBodyId() != id_informationBody)
         {
            throw new Error("informationBodyId error!body id:" + id_informationBody + "  bodyDetail id:" + param1.getInformationBodyId());
         }
         m_informationBodyDetail = param1;
      }
      
      public function getThisByteArray() : ByteArray
      {
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.endian = "littleEndian";
         var _loc1_:ByteArray = m_informationBodyDetail ? m_informationBodyDetail.getThisByteArray() : null;
         var _loc3_:int = _loc1_ ? _loc1_.length : 0;
         _loc2_.writeInt(_loc3_ + 4 + 4);
         _loc2_.writeInt(id_informationBody);
         if(_loc1_)
         {
            _loc1_.position = 0;
            _loc2_.writeBytes(_loc1_);
         }
         _loc2_.position = 0;
         return _loc2_;
      }
      
      public function getDetail() : InformationBodyDetail
      {
         return m_informationBodyDetail;
      }
   }
}

