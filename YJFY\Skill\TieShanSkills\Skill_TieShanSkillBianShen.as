package YJFY.Skill.TieShanSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.World.World;
   
   public class Skill_TieShanSkillBianShen extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      public function Skill_TieShanSkillBianShen()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         (m_owner.getExtra() as Tie<PERSON>han).removeSkillBuff();
         releaseSkill2(param1);
         return true;
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         changeShow();
      }
      
      private function changeShow() : void
      {
         (this.m_owner.getExtra() as <PERSON><PERSON><PERSON><PERSON>).changeBianShenState();
      }
   }
}

