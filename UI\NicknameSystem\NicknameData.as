package UI.NicknameSystem
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   
   public class NicknameData extends DataManagerParent
   {
      
      public static const WHITE_NICKNAME:String = "whiteNickname";
      
      public static const RED_NICKNAME:String = "redNickname";
      
      private static var _instance:NicknameData = null;
      
      public var nicknameSaveData:Vector.<NicknameSaveData>;
      
      public var myNicknameRankListId:uint;
      
      private var m_myDataInNicknameRankList:Object;
      
      public var freeRankListId:int;
      
      public var payRankListId:int;
      
      public function NicknameData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error();
      }
      
      public static function getInstance() : NicknameData
      {
         if(!_instance)
         {
            _instance = new NicknameData();
         }
         return _instance;
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         if(nicknameSaveData)
         {
            _loc2_ = int(nicknameSaveData.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(nicknameSaveData[_loc3_])
               {
                  nicknameSaveData[_loc3_] = null;
               }
               _loc3_++;
            }
            nicknameSaveData = null;
         }
         for(var _loc1_ in m_myDataInNicknameRankList)
         {
            m_myDataInNicknameRankList[_loc1_] = null;
         }
         m_myDataInNicknameRankList = null;
         _instance = null;
      }
      
      public function set myDataInNicknameRankList(param1:Object) : void
      {
         m_myDataInNicknameRankList = param1;
         if(Boolean(m_myDataInNicknameRankList) && Boolean(m_myDataInNicknameRankList.extra) && GamingUI.getInstance().getSocietySystem())
         {
            GamingUI.getInstance().getSocietySystem().updatePlayerData(2);
         }
      }
      
      public function get myDataInNicknameRankList() : Object
      {
         return m_myDataInNicknameRankList;
      }
   }
}

