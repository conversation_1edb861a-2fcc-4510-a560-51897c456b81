package YJFY.Other
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.World.World;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   
   public class MedalManager
   {
      
      private const m_const_animation1FrameLabel:String = "a1";
      
      private const m_const_animation2FrameLabel:String = "a2";
      
      private const m_const_animation1StopFrameLabel:String = "a1^stop^";
      
      private const m_const_playA1IntervalMin:uint = 5000;
      
      private const m_const_playA1IntervalMax:uint = 10000;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_replaceMC_Medal:MovieClip;
      
      private var m_medalPlay:AnimationShowPlayLogicShell;
      
      private var m_medalPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_medalMc:MovieClip;
      
      private var m_medalWordMc:MovieClip;
      
      private var m_playA1AnimationTime:Number = 0;
      
      private var m_currentMedalIsDynamic:Boolean;
      
      private var m_versionControl:IVersionControl;
      
      public function MedalManager()
      {
         super();
         m_myLoader = new YJFYLoader();
         m_medalPlay = new AnimationShowPlayLogicShell();
         m_medalPlayListener = new AnimationPlayFrameLabelListener();
         m_medalPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_medalPlay.addFrameLabelListener(m_medalPlayListener);
         m_currentMedalIsDynamic = true;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_replaceMC_Medal);
         m_replaceMC_Medal = null;
         ClearUtil.clearObject(m_medalPlay);
         m_medalPlay = null;
         ClearUtil.clearObject(m_medalMc);
         m_medalMc = null;
         ClearUtil.clearObject(m_medalWordMc);
         m_medalWordMc = null;
         ClearUtil.clearObject(m_medalPlayListener);
         m_medalPlayListener = null;
         m_versionControl = null;
      }
      
      public function init(param1:IVersionControl) : void
      {
         m_versionControl = param1;
         m_myLoader.setVersionControl(m_versionControl);
         m_replaceMC_Medal = MyFunction2.returnShowByClassName("ReplaceMC_Medal") as MovieClip;
         m_medalPlay.setShow(m_replaceMC_Medal);
         m_medalPlay.gotoAndPlay("1");
      }
      
      public function changeShow(param1:String) : void
      {
         var _loc2_:XML = XMLSingle.getInstance().dataXML.MedalInitData[0].data.(@eqClassName == param1)[0];
         m_myLoader.getClass(_loc2_.dynamic[0].@swfPath,_loc2_.dynamic[0].@className,getMedalShowSuccess,getFail);
         m_myLoader.getClass(_loc2_.static[0].@swfPath,_loc2_.static[0].@className,getMedalWordShowSuccess,getFail);
         m_myLoader.load();
      }
      
      public function swapToDynamic() : void
      {
         if(m_currentMedalIsDynamic)
         {
            return;
         }
         m_currentMedalIsDynamic = true;
         if(m_medalMc)
         {
            m_medalPlay.setShow(m_medalMc);
            m_medalPlay.gotoAndPlay("a1");
         }
      }
      
      public function swapToStatic() : void
      {
         if(m_currentMedalIsDynamic == false)
         {
            return;
         }
         m_currentMedalIsDynamic = false;
         if(m_medalWordMc)
         {
            m_medalPlay.setShow(m_medalWordMc);
            m_medalPlay.gotoAndPlay("1");
         }
      }
      
      public function render(param1:World) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(m_currentMedalIsDynamic && m_playA1AnimationTime < param1.getWorldTime() && m_medalMc)
         {
            m_medalPlay.gotoAndPlay("a1");
            m_playA1AnimationTime = param1.getWorldTime() + 5000 + Math.random() * (10000 - 5000);
         }
      }
      
      public function getMedalPlay() : AnimationShowPlayLogicShell
      {
         return m_medalPlay;
      }
      
      private function getMedalShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc4_:DisplayObjectContainer = null;
         var _loc2_:int = 0;
         ClearUtil.clearObject(m_medalMc);
         m_medalMc = null;
         var _loc3_:Class = param1.resultClass;
         m_medalMc = new _loc3_();
         if(m_currentMedalIsDynamic)
         {
            if(m_medalPlay.getDisplayShow())
            {
               _loc4_ = m_medalPlay.getDisplayShow().parent;
               if(_loc4_)
               {
                  _loc2_ = _loc4_.getChildIndex(m_medalPlay.getDisplayShow());
               }
            }
            m_medalPlay.setShow(m_medalMc);
            if(_loc4_)
            {
               _loc4_.addChildAt(m_medalPlay.getDisplayShow(),_loc2_);
            }
            ClearUtil.clearObject(m_replaceMC_Medal);
            m_replaceMC_Medal = null;
            m_medalPlay.gotoAndPlay("a1");
         }
      }
      
      private function getMedalWordShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc4_:DisplayObjectContainer = null;
         var _loc2_:int = 0;
         ClearUtil.clearObject(m_medalWordMc);
         m_medalWordMc = null;
         var _loc3_:Class = param1.resultClass;
         m_medalWordMc = new _loc3_();
         if(m_currentMedalIsDynamic == false)
         {
            if(m_medalPlay.getDisplayShow())
            {
               _loc4_ = m_medalPlay.getDisplayShow().parent;
               if(_loc4_)
               {
                  _loc2_ = _loc4_.getChildIndex(m_medalPlay.getDisplayShow());
               }
            }
            m_medalPlay.setShow(m_medalWordMc);
            if(_loc4_)
            {
               _loc4_.addChildAt(m_medalPlay.getDisplayShow(),_loc2_);
            }
            ClearUtil.clearObject(m_replaceMC_Medal);
            m_replaceMC_Medal = null;
            m_medalPlay.gotoAndPlay("1");
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("勋章显示加载失败");
         trace("勋章显示加载失败",param1.swfPath,param1.wantClassName);
      }
      
      private function reachFrameLabel(param1:String) : void
      {
         if(param1 == "a1^stop^")
         {
            m_medalPlay.gotoAndPlay("a2");
         }
      }
   }
}

