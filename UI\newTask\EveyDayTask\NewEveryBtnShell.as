package UI.newTask.EveyDayTask
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MainLineTask.ChoiceGetRewardPlayerPanel;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.Task.TaskFunction;
   import UI.Task.TaskVO.MTaskVO;
   import UI.WarningBox.WarningBoxSingle;
   import UI.newTask.NewTaskPanel;
   import YJFY.GameEvent;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   
   public class NewEveryBtnShell
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_neweverydaypanel:NewEveryDayPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_DoneBtn:ButtonLogicShell2;
      
      private var m_GetBtn:ButtonLogicShell2;
      
      private var m_GoBtn:ButtonLogicShell2;
      
      private var m_data:MTaskVO;
      
      private var m_choiceGetRewardPlayerPanel:ChoiceGetRewardPlayerPanel;
      
      public function NewEveryBtnShell()
      {
         super();
      }
      
      public function clear() : void
      {
         hide();
         if(m_DoneBtn)
         {
            ClearUtil.clearObject(m_DoneBtn);
         }
         m_DoneBtn = null;
         if(m_GetBtn)
         {
            ClearUtil.clearObject(m_GetBtn);
         }
         m_GetBtn = null;
         if(m_GoBtn)
         {
            ClearUtil.clearObject(m_GoBtn);
         }
         m_GoBtn = null;
         ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
         m_choiceGetRewardPlayerPanel = null;
         m_mc.removeEventListener("clickButton",clickButton,true);
         if(m_choiceGetRewardPlayerPanel)
         {
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewEveryDayPanel) : void
      {
         m_newtaskpanel = param1;
         m_neweverydaypanel = param3;
         m_show = param2;
         m_mc = m_show["btnshell_2"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_DoneBtn = new ButtonLogicShell2();
         m_GetBtn = new ButtonLogicShell2();
         m_GoBtn = new ButtonLogicShell2();
         m_DoneBtn.setShow(m_mc["donebtn"]);
         m_GetBtn.setShow(m_mc["getbtn"]);
         m_GoBtn.setShow(m_mc["gobtn"]);
         m_DoneBtn.getShow().visible = false;
         m_GetBtn.getShow().visible = false;
         m_GoBtn.getShow().visible = false;
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function show() : void
      {
      }
      
      public function hide() : void
      {
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChild(WarningBoxSingle.getInstance());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_GetBtn:
               if(GamingUI.getInstance().player2)
               {
                  if(m_choiceGetRewardPlayerPanel == null)
                  {
                     m_choiceGetRewardPlayerPanel = new ChoiceGetRewardPlayerPanel();
                  }
                  m_choiceGetRewardPlayerPanel.x = -200;
                  m_choiceGetRewardPlayerPanel.y = -300;
                  m_mc.addChild(m_choiceGetRewardPlayerPanel);
               }
               else
               {
                  getTaskReward(GamingUI.getInstance().player1);
               }
               break;
            case m_GoBtn:
               if(m_data.gotoInfo)
               {
                  if(m_data.gotoInfo.type == "2" || m_data.gotoInfo.type == "3" || m_data.gotoInfo.type == "4" || m_data.gotoInfo.type == "5" || m_data.gotoInfo.type == "6" || m_data.gotoInfo.type == "7" || m_data.gotoInfo.type == "8" || m_data.gotoInfo.type == "9" || m_data.gotoInfo.type == "10")
                  {
                     if("NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(LevelModeSaveData.getInstance().getMapLevel() >= 16)
                        {
                           m_newtaskpanel.CloseUI(null);
                           Part1.getInstance().closeCityMap();
                           Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("关卡未开启");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(GamingUI.getInstance().player1.playerVO.level >= 30)
                        {
                           m_newtaskpanel.CloseUI(null);
                           Part1.getInstance().closeCityMap();
                           Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap4.xml" == m_data.gotoInfo.xmlpath)
                     {
                        if(GamingUI.getInstance().player1.playerVO.level >= 60)
                        {
                           if(LevelModeSaveData.getInstance().getGodLevel() >= int(m_data.gotoInfo.gotobtnname.substr("levelMaptBtn".length,m_data.gotoInfo.gotobtnname.length)))
                           {
                              m_newtaskpanel.CloseUI(null);
                              Part1.getInstance().closeCityMap();
                              Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                           }
                           else
                           {
                              GamingUI.getInstance().showMessageTip("关卡未开启");
                           }
                        }
                        else
                        {
                           GamingUI.getInstance().showMessageTip("60级之后才能挑战");
                        }
                     }
                     else if("NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" == m_data.gotoInfo.xmlpath)
                     {
                        m_newtaskpanel.CloseUI(null);
                        Part1.getInstance().closeCityMap();
                        Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                     }
                  }
                  else if(m_data.gotoInfo.type == "1")
                  {
                     if(GamingUI.getInstance().player1.playerVO.level >= 30)
                     {
                        m_newtaskpanel.CloseUI(null);
                        GamingUI.getInstance().openPreciousView();
                     }
                     else
                     {
                        GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                     }
                  }
               }
               break;
            case m_DoneBtn:
         }
         if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn1)
         {
            getTaskReward(GamingUI.getInstance().player1);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
         else if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn2)
         {
            getTaskReward(GamingUI.getInstance().player2);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
      }
      
      private function getTaskReward(param1:Player) : void
      {
         var _loc2_:SaveTaskInfo = null;
         m_data.state = 1;
         if(TaskFunction.getInstance().dealWithTaskReward(m_data,param1))
         {
            m_data.isGotReward = 1;
            m_neweverydaypanel.refreshScript(m_data);
            m_neweverydaypanel.refreshlist();
            showWarningBox("您完成任务， 奖励领取成功！！",0);
            _loc2_ = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
            GamingUI.getInstance().addMainLineTaskGoalGameEventStr("completeEveryDayTask");
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("refreshtip"));
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("hidetip"));
         }
         else
         {
            showWarningBox("背包已满！！",0);
         }
      }
      
      public function refreshScript(param1:MTaskVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         m_data = param1;
         allhide();
         if(m_data)
         {
            if(m_data.isGotReward)
            {
               showDone();
            }
            else
            {
               _loc3_ = 0;
               _loc2_ = 0;
               _loc4_ = 0;
               _loc4_ = 0;
               while(_loc4_ < m_data.currentTaskGoalVO_nums.length)
               {
                  _loc3_ += m_data.currentTaskGoalVO_nums[_loc4_];
                  _loc4_++;
               }
               _loc4_ = 0;
               while(_loc4_ < m_data.taskGoalVO_nums.length)
               {
                  _loc2_ += m_data.taskGoalVO_nums[_loc4_];
                  _loc4_++;
               }
               if(_loc3_ >= _loc2_)
               {
                  showGet();
               }
               else
               {
                  showGO();
               }
            }
         }
      }
      
      private function allhide() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showDone() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showGet() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = true;
            m_GetBtn.unLock();
         }
      }
      
      private function showGO() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = true;
            m_GoBtn.unLock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
   }
}

