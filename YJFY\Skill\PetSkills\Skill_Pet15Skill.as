package YJFY.Skill.PetSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.EntityListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.RealityEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_EveryTargetAddShow;
   import YJFY.Skill.IStunSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_Pet15Skill extends CuboidAreaAttackSkill2_EveryTargetAddShow implements IStunSkill
   {
      
      private const m_const_changeStartId:String = "changeStart";
      
      private const m_const_changeEndId:String = "changeEnd";
      
      private const m_const_changeState1Id:String = "changeState1";
      
      private const m_const_changeState2Id:String = "changeState2";
      
      private const m_const_changeTargetFrameLabel:String = "changeTarget";
      
      private const m_const_disappearFrameLabel:String = "disappear";
      
      private const m_const_appearFrameLabel:String = "appear";
      
      private const m_const_enterStateFrameLabel:String = "enterState^stop^";
      
      private const m_const_changeEndFrameLabel:String = "changeEnd^stop^";
      
      private var m_changeStartDefinitionData:AnimationDefinitionData;
      
      private var m_changeEndDefinitionData:AnimationDefinitionData;
      
      private var m_changeState1DefinitionData:AnimationDefinitionData;
      
      private var m_changeState2DefinitionData:AnimationDefinitionData;
      
      private var m_changeStateDefinitionDatas:Vector.<AnimationDefinitionData>;
      
      protected var m_effectShowsAddtoTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_wasteEffectShowsAddToTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_effectShowsAddtoTargetPool:ObjectsPool;
      
      protected var m_effectShowReachFrameLabelListener:AnimationPlayFrameLabelListener;
      
      protected var m_animalEntitys_addEffect:Vector.<AnimalEntity>;
      
      protected var m_entitysInHide:Vector.<IEntity>;
      
      protected var m_entityInHideListener:EntityListener;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      public function Skill_Pet15Skill()
      {
         super();
         m_effectShowsAddtoTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteEffectShowsAddToTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_animalEntitys_addEffect = new Vector.<AnimalEntity>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.changeStateFun = entityChangeState;
         m_effectShowsAddtoTargetPool = new ObjectsPool(m_effectShowsAddtoTarget,m_wasteEffectShowsAddToTarget,createEffectShowAddToTarget,null);
         m_effectShowReachFrameLabelListener = new AnimationPlayFrameLabelListener();
         m_effectShowReachFrameLabelListener.reachFrameLabelFun2 = effectShowReachFrameLabel;
         m_changeStateDefinitionDatas = new Vector.<AnimationDefinitionData>();
         m_entitysInHide = new Vector.<IEntity>();
         m_entityInHideListener = new EntityListener();
         m_entityInHideListener.preShowBodyShowFun = preShowBodyOfEntityInHide;
      }
      
      override public function clear() : void
      {
         clear2();
         ClearUtil.clearObject(m_changeStartDefinitionData);
         m_changeStartDefinitionData = null;
         ClearUtil.clearObject(m_changeEndDefinitionData);
         m_changeEndDefinitionData = null;
         ClearUtil.clearObject(m_changeState1DefinitionData);
         m_changeState1DefinitionData = null;
         ClearUtil.clearObject(m_changeStateDefinitionDatas);
         m_changeStateDefinitionDatas = null;
         ClearUtil.clearObject(m_effectShowsAddtoTarget);
         m_effectShowsAddtoTarget = null;
         ClearUtil.clearObject(m_wasteEffectShowsAddToTarget);
         m_wasteEffectShowsAddToTarget = null;
         ClearUtil.clearObject(m_effectShowsAddtoTargetPool);
         m_effectShowsAddtoTargetPool = null;
         ClearUtil.clearObject(m_effectShowReachFrameLabelListener);
         m_effectShowReachFrameLabelListener = null;
         ClearUtil.clearObject(m_animalEntitys_addEffect);
         m_animalEntitys_addEffect = null;
         ClearUtil.nullArr(m_entitysInHide,false,false,false);
         m_entitysInHide = null;
         ClearUtil.clearObject(m_entityInHideListener);
         m_entityInHideListener = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_changeStartDefinitionData = new AnimationDefinitionData();
         m_changeStartDefinitionData.initByXML(param1.animationDefinition.(@id == "changeStart")[0]);
         m_changeEndDefinitionData = new AnimationDefinitionData();
         m_changeEndDefinitionData.initByXML(param1.animationDefinition.(@id == "changeEnd")[0]);
         m_changeState1DefinitionData = new AnimationDefinitionData();
         m_changeState1DefinitionData.initByXML(param1.animationDefinition.(@id == "changeState1")[0]);
         m_changeState2DefinitionData = new AnimationDefinitionData();
         m_changeState2DefinitionData.initByXML(param1.animationDefinition.(@id == "changeState2")[0]);
         m_changeStateDefinitionDatas.push(m_changeState1DefinitionData);
         m_changeStateDefinitionDatas.push(m_changeState2DefinitionData);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override protected function everyEntityAddShowPlayReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         super.everyEntityAddShowPlayReachFrame(param1,param2);
         var _loc3_:* = param2;
         if("changeTarget" === _loc3_)
         {
            changeTarget(param1.extra as IEntity);
         }
      }
      
      protected function effectShowReachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         switch(param2)
         {
            case "disappear":
               disappearTarget(param1.extra as IEntity);
               break;
            case "enterState^stop^":
               enterState(param1);
               break;
            case "appear":
               appeartTarget(param1.extra as IEntity);
               break;
            case "changeEnd^stop^":
               changeEnd(param1);
         }
      }
      
      protected function effectShowOfPreAddToTarget(param1:AnimationShowPlayLogicShell) : void
      {
         (param1.getShow() as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_changeStartDefinitionData));
      }
      
      protected function changeTarget(param1:IEntity) : void
      {
         if(m_world == null)
         {
            return;
         }
         if(param1.getIsShowBody() == false || param1 is IAnimalEntity && (param1 as IAnimalEntity).isInDie())
         {
            return;
         }
         var _loc2_:AnimationShowPlayLogicShell = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
         effectShowOfPreAddToTarget(_loc2_);
         _loc2_.extra = param1;
         _loc2_.addFrameLabelListener(m_effectShowReachFrameLabelListener);
         (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
         m_animalEntitys_addEffect.push(param1);
         _loc2_.gotoAndPlay("start");
         param1.addOtherAniamtion(_loc2_);
         _loc2_.getDisplayShow().scaleX = param1.getShowDirection();
      }
      
      protected function createEffectShowAddToTarget() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_changeStartDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      private function disappearTarget(param1:IEntity) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1.getIsShowBody() == false || param1 is IAnimalEntity && (param1 as IAnimalEntity).isInHurt() == false)
         {
            _loc2_ = int(m_effectShowsAddtoTarget.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(m_effectShowsAddtoTarget[_loc3_].extra == param1)
               {
                  changeEnd(m_effectShowsAddtoTarget[_loc3_]);
                  _loc3_--;
                  _loc2_--;
               }
               _loc3_++;
            }
            return;
         }
         param1.hideBodyShow();
         addOneEntityInHide(param1);
      }
      
      private function enterState(param1:AnimationShowPlayLogicShell) : void
      {
         if(m_world == null)
         {
            return;
         }
         var _loc2_:int = Math.random() * 2;
         (param1.getShow() as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_changeStateDefinitionDatas[_loc2_]));
         param1.gotoAndPlay("1");
      }
      
      private function entityChangeState(param1:AnimalEntity) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1.isInHurt() == false)
         {
            _loc2_ = int(m_effectShowsAddtoTarget.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(m_effectShowsAddtoTarget[_loc3_].extra == param1)
               {
                  endState(m_effectShowsAddtoTarget[_loc3_]);
               }
               _loc3_++;
            }
         }
      }
      
      private function endState(param1:AnimationShowPlayLogicShell) : void
      {
         if(m_world == null)
         {
            return;
         }
         if((param1.getShow() as AnimationShow).getDefinition().getId() == m_changeEndDefinitionData.getId())
         {
            return;
         }
         (param1.getShow() as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_changeEndDefinitionData));
         param1.gotoAndPlay("1");
      }
      
      private function appeartTarget(param1:IEntity) : void
      {
         decOneEntityInHide(param1);
         param1.showBodyShow();
      }
      
      private function changeEnd(param1:AnimationShowPlayLogicShell) : void
      {
         (param1.extra as IEntity).removeOtherAnimation(param1);
         param1.removeFrameLabelListener(m_effectShowReachFrameLabelListener);
         m_effectShowsAddtoTargetPool.wasteOneObj(param1);
         var _loc2_:int = int(m_animalEntitys_addEffect.indexOf(param1.extra as AnimalEntity));
         m_animalEntitys_addEffect.splice(_loc2_,1);
         _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1.extra as AnimalEntity));
         if(_loc2_ == -1)
         {
            (param1.extra as IAnimalEntity).removeAnimalEntityListener(m_animalEntityListener);
         }
      }
      
      private function clear2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_effectShowsAddtoTarget[_loc2_].extra)
            {
               (m_effectShowsAddtoTarget[_loc2_].extra as IEntity).removeOtherAnimation(m_effectShowsAddtoTarget[_loc2_]);
            }
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_].getShow());
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_]);
            m_effectShowsAddtoTarget[_loc2_] = null;
            _loc2_++;
         }
         m_effectShowsAddtoTarget.length = 0;
         _loc1_ = m_animalEntitys_addEffect ? m_animalEntitys_addEffect.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_animalEntitys_addEffect[_loc2_].removeAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect[_loc2_] = null;
            _loc2_++;
         }
         m_animalEntitys_addEffect.length = 0;
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
      }
      
      private function addOneEntityInHide(param1:IEntity) : void
      {
         if(!(param1 is RealityEntity))
         {
            throw new Error("entiy 必须是Entity， 因为要侦听entity事件，并根据情况设置setIsAbleShowBodyShow2来是否真的能够显示body");
         }
         if(param1 is RealityEntity)
         {
            (param1 as RealityEntity).addEntityListener(m_entityInHideListener);
         }
         m_entitysInHide.push(param1);
      }
      
      private function decOneEntityInHide(param1:IEntity) : void
      {
         if(!(param1 is RealityEntity))
         {
            throw new Error("entiy 必须是Entity， 因为要侦听entity事件，并根据情况设置setIsAbleShowBodyShow2来是否真的能够显示body");
         }
         var _loc2_:int = int(m_entitysInHide.indexOf(param1));
         if(param1 is RealityEntity)
         {
            (param1 as RealityEntity).removeEntityListener(m_entityInHideListener);
         }
         m_entitysInHide.splice(_loc2_,1);
      }
      
      private function preShowBodyOfEntityInHide(param1:IEntity) : void
      {
         (param1 as RealityEntity).setIsAbleShowBodyShow2(false);
      }
   }
}

