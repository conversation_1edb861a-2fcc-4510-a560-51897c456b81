package UI.EquipmentDetailShow
{
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Pets.Talents.Talent;
   import UI.Skills.Skill;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class PetDetailShow extends EquipmentDetailShow
   {
      
      private var m_scrollSprite:ScrollSpriteLogicShell;
      
      private var m_dataLayer:Sprite;
      
      private var m_dataLayerMC:MovieClipPlayLogicShell;
      
      private var m_petLevelTxt:TextField;
      
      private var m_experienceBar:CMSXChangeBarLogicShell;
      
      private var m_essenceBar:CMSXChangeBarLogicShell;
      
      private var m_talentSprite:Sprite;
      
      private var m_talentNameText:TextField;
      
      private var m_activeSkillCell:Sprite;
      
      private var m_passiveSkillCells:Vector.<Sprite>;
      
      private var m_awakePassiveSkillCell:Sprite;
      
      private var m_talent:Talent;
      
      private var m_activeSkill:Skill;
      
      private var m_passiveSkills:Vector.<Skill>;
      
      public function PetDetailShow()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_scrollSprite);
         m_scrollSprite = null;
         m_dataLayer = null;
         ClearUtil.clearObject(m_dataLayer);
         m_dataLayer = null;
         m_petLevelTxt = null;
         ClearUtil.clearObject(m_experienceBar);
         m_experienceBar = null;
         ClearUtil.clearObject(m_essenceBar);
         m_essenceBar = null;
         m_talentSprite = null;
         m_talentNameText = null;
         m_activeSkillCell = null;
         ClearUtil.nullArr(m_passiveSkillCells);
         m_passiveSkillCells = null;
         m_awakePassiveSkillCell = null;
         ClearUtil.clearObject(m_talent);
         m_talent = null;
         ClearUtil.clearObject(m_activeSkill);
         m_activeSkill = null;
         ClearUtil.nullArr(m_passiveSkills);
         m_passiveSkills = null;
      }
      
      override protected function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:Skill = null;
         var _loc3_:Skill = null;
         super.initShow();
         var _loc1_:PetEquipmentVO = m_equipmentVO as PetEquipmentVO;
         m_showMC.gotoAndStop("pet");
         m_scrollSprite = new ScrollSpriteLogicShell();
         m_scrollSprite.setShow(m_show["petDetailScrollSprite"]);
         m_dataLayer = m_scrollSprite.getDataLayer();
         m_dataLayerMC = new MovieClipPlayLogicShell();
         m_dataLayerMC.setShow(m_dataLayer as MovieClip);
         if(_loc1_ is AdvancePetEquipmentVO)
         {
            m_dataLayerMC.gotoAndStop("advancePet");
            m_awakePassiveSkillCell = m_dataLayer["awakePassiveSkillCell"];
         }
         else
         {
            m_dataLayerMC.gotoAndStop("pet");
         }
         m_scrollSprite.refresh();
         m_eqCell = m_dataLayer["eqCell"];
         m_eqNameText = m_dataLayer["eqNameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_eqNameText);
         m_petLevelTxt = m_dataLayer["petLevelTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_petLevelTxt);
         m_experienceBar = new CMSXChangeBarLogicShell();
         m_experienceBar.setShow(m_dataLayer["experienceBar"]);
         m_essenceBar = new CMSXChangeBarLogicShell();
         m_essenceBar.setShow(m_dataLayer["essenceBar"]);
         m_talentSprite = m_dataLayer["talentCell"];
         m_talentNameText = m_dataLayer["talentNameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_talentNameText);
         m_activeSkillCell = m_dataLayer["activeSkillCell"];
         m_passiveSkillCells = new Vector.<Sprite>();
         _loc5_ = 0;
         while(_loc5_ < 5)
         {
            m_passiveSkillCells.push(m_dataLayer["passiveSkillCell" + (_loc5_ + 1)]);
            _loc5_++;
         }
         m_petLevelTxt.text = _loc1_.petLevel.toString();
         m_experienceBar.change(_loc1_.experiencePercent);
         m_experienceBar.setDataShow(Math.round(_loc1_.experiencePercent * _loc1_.experienceVolume) + "/" + _loc1_.experienceVolume);
         m_essenceBar.change(_loc1_.essentialPercent);
         m_essenceBar.setDataShow(Math.round(_loc1_.essentialPercent * _loc1_.essentialVolume) + "/" + _loc1_.essentialVolume);
         m_talent = new Talent(_loc1_.talentVO);
         m_talentSprite.addChild(m_talent);
         m_talentNameText.text = _loc1_.talentVO.name;
         m_activeSkill = new Skill(_loc1_.activeSkillVO);
         m_activeSkill.addEventListener("rollOver",onOverSkill,false,0,true);
         m_activeSkill.addEventListener("rollOut",onOutSkill,false,0,true);
         m_activeSkillCell.addChild(m_activeSkill);
         _loc2_ = _loc1_.passiveSkillVOs ? _loc1_.passiveSkillVOs.length : 0;
         m_passiveSkills = new Vector.<Skill>();
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = new Skill(_loc1_.passiveSkillVOs[_loc5_]);
            _loc4_.addEventListener("rollOver",onOverSkill,false,0,true);
            _loc4_.addEventListener("rollOut",onOutSkill,false,0,true);
            m_passiveSkills.push(_loc4_);
            m_passiveSkillCells[_loc5_].addChild(_loc4_);
            _loc5_++;
         }
         if(_loc1_ is AdvancePetEquipmentVO)
         {
            _loc3_ = new Skill((_loc1_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs[0]);
            _loc3_.addEventListener("rollOver",onOverSkill,false,0,true);
            _loc3_.addEventListener("rollOut",onOutSkill,false,0,true);
            m_awakePassiveSkillCell.addChild(_loc3_);
         }
      }
      
      private function onOverSkill(param1:Event) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOutSkill(param1:Event) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

