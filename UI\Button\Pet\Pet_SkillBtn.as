package UI.Button.Pet
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Pet_SkillBtn extends SwitchBtn
   {
      
      public function Pet_SkillBtn()
      {
         super();
         setTipString("宠物技能");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToPetSkill"));
      }
   }
}

