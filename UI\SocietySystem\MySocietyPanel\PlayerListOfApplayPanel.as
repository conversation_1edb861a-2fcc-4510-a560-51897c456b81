package UI.SocietySystem.MySocietyPanel
{
   import UI.Event.UIBtnEvent;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerListOfApply;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.PlayerDataOfApply;
   import UI.SocietySystem.SocietySystem;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class PlayerListOfApplayPanel extends MySprite
   {
      
      private var m_columeNumOfOnePage:int;
      
      private var m_clear:ClearHelper;
      
      private var m_show:MovieClip;
      
      private var m_showDrag:AbleDragSpriteLogicShell;
      
      private var m_columesForPlayerOfApply:Vector.<ColumeForPlayerOfApply>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_playerDatas:PlayerDatas;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_playerListOfApply:DOWN_PlayerListOfApply;
      
      private var m_societySystem:SocietySystem;
      
      public function PlayerListOfApplayPanel()
      {
         super();
         m_clear = new ClearHelper();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showDrag);
         m_showDrag = null;
         ClearUtil.nullArr(m_columesForPlayerOfApply);
         m_columesForPlayerOfApply = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_playerDatas);
         m_playerDatas = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.nullArr(m_players);
         m_players = null;
         ClearUtil.clearObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         m_myLoader = null;
         m_playerListOfApply = null;
         m_societySystem = null;
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setPlayerListOfApply(param1:DOWN_PlayerListOfApply) : void
      {
         m_playerListOfApply = param1;
         if(m_show)
         {
            initShow2();
         }
      }
      
      public function init() : void
      {
         initShow();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow() : void
      {
         m_myLoader.getClass("MySocietyPanel.swf","PlayerListOfApplyPanel",getShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc6_:int = 0;
         var _loc5_:DisplayObject = null;
         var _loc2_:ColumeForPlayerOfApply = null;
         var _loc3_:Class = param1.resultClass;
         m_show = new _loc3_();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         addChild(m_show);
         m_showDrag = new AbleDragSpriteLogicShell();
         m_showDrag.setShow(m_show);
         if(stage)
         {
            m_show.x = (stage.stageWidth - m_show.width) / 2;
            m_show.y = (stage.stageHeight - m_show.height) / 2;
         }
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         var _loc4_:int = m_show.numChildren;
         m_columeNumOfOnePage = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = m_show.getChildAt(_loc6_);
            if(_loc5_.name.substr(0,"colume".length) == "colume")
            {
               m_columeNumOfOnePage++;
            }
            _loc6_++;
         }
         m_columesForPlayerOfApply = new Vector.<ColumeForPlayerOfApply>();
         _loc6_ = 0;
         while(_loc6_ < m_columeNumOfOnePage)
         {
            _loc2_ = new ColumeForPlayerOfApply();
            _loc2_.setShow(m_show["colume" + (_loc6_ + 1)]);
            m_columesForPlayerOfApply.push(_loc2_);
            _loc2_.getShow().visible = false;
            _loc6_++;
         }
         if(m_playerListOfApply)
         {
            initShow2();
         }
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
      }
      
      private function initShow2() : void
      {
         if(m_playerListOfApply == null)
         {
            return;
         }
         setPageBtn(1,m_playerListOfApply.getPlayerNumOfApply());
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOfOnePage);
      }
      
      private function setPageBtn(param1:int, param2:int) : void
      {
         if(param2 == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(param2 % m_columeNumOfOnePage == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,param2 / m_columeNumOfOnePage);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(param2 / m_columeNumOfOnePage) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc7_:* = 0;
         var _loc5_:int = 0;
         var _loc2_:ColumeForPlayerOfApply = null;
         var _loc3_:PlayerDataOfApply = null;
         var _loc6_:int = param1 + m_columeNumOfOnePage;
         var _loc4_:int = m_playerListOfApply.getPlayerNumOfApply();
         _loc7_ = param1;
         while(_loc7_ < _loc6_ && _loc7_ < _loc4_)
         {
            _loc2_ = m_columesForPlayerOfApply[_loc5_];
            _loc2_.getShow().visible = true;
            _loc3_ = m_playerListOfApply.getPlayerDataOfApplyByIndex(_loc7_);
            _loc2_.setPlayerDataOfApply(_loc3_);
            _loc5_++;
            _loc7_++;
         }
         while(_loc5_ < m_columeNumOfOnePage)
         {
            m_columesForPlayerOfApply[_loc5_].getShow().visible = false;
            _loc5_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc3_:PlayerDataOfApply = null;
         var _loc2_:PlayerDataOfApply = null;
         var _loc5_:* = param1.button;
         if(m_pageBtnGroup !== _loc5_)
         {
            _loc4_ = 0;
            while(_loc4_ < m_columeNumOfOnePage)
            {
               if(m_columesForPlayerOfApply[_loc4_].getLookUpBtn() == param1.button)
               {
                  if(m_columesForPlayerOfApply[_loc4_].getPlayerDataOfApply())
                  {
                     if(m_playerDatas == null)
                     {
                        m_playerDatas = new PlayerDatas();
                        m_getPlayerDataListener = new GetPlayerDataListener();
                        m_getPlayerDataListener.getPlayerDataFun = getPlayerData;
                        m_playerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
                        m_playerDatas.init();
                     }
                     _loc2_ = m_columesForPlayerOfApply[_loc4_].getPlayerDataOfApply();
                     m_playerDatas.getPlayerData(_loc2_.getUid_applicant().toString(),_loc2_.getIdx_applicant());
                  }
                  return;
               }
               if(m_columesForPlayerOfApply[_loc4_].getAgreeBtn() == param1.button)
               {
                  _loc3_ = m_columesForPlayerOfApply[_loc4_].getPlayerDataOfApply();
                  m_societySystem.agreeOrRefusePlayerJoin(true,_loc3_.getUid_applicant(),_loc3_.getIdx_applicant());
                  m_playerListOfApply.spliceOnePlayerDataOfApply(_loc4_);
                  setPageBtn(1,m_playerListOfApply.getPlayerNumOfApply());
                  arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOfOnePage);
                  return;
               }
               if(m_columesForPlayerOfApply[_loc4_].getRefuseBtn() == param1.button)
               {
                  _loc3_ = m_columesForPlayerOfApply[_loc4_].getPlayerDataOfApply();
                  m_societySystem.agreeOrRefusePlayerJoin(false,_loc3_.getUid_applicant(),_loc3_.getIdx_applicant());
                  m_playerListOfApply.spliceOnePlayerDataOfApply(_loc4_);
                  setPageBtn(1,m_playerListOfApply.getPlayerNumOfApply());
                  arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOfOnePage);
                  return;
               }
               _loc4_++;
            }
            return;
         }
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOfOnePage);
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      private function getPlayerData(param1:InitPlayersData) : void
      {
         openPlayerShowPanel(param1.player1,param1.player2,param1.nickNameData);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         addChild(m_otherPlayerShowPanel);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
   }
}

