package YJFY.XydzjsData
{
   public class EnemyVO
   {
      
      private var m_totalHp:uint;
      
      private var m_attack:uint;
      
      private var m_expOfDieThisEnemy:uint;
      
      private var m_defence:uint;
      
      private var m_dogdeRate:Number;
      
      private var m_criticalRate:Number;
      
      private var m_criticalMuti:Number;
      
      private var m_deCriticalRate:Number;
      
      private var m_hitRate:Number;
      
      private var m_totalMp:uint;
      
      private var m_regMpPerS:Number;
      
      private var m_regHpPerS:Number;
      
      private var m_currentMp:uint;
      
      private var m_currentHp:uint;
      
      public function EnemyVO()
      {
         super();
         m_regHpPerS = 0;
         m_regMpPerS = 0;
      }
      
      public function clear() : void
      {
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc4_:int = 0;
         var _loc2_:XMLList = param1.data;
         var _loc3_:int = int(_loc2_.length());
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            this[String(_loc2_[_loc4_].@att)] = String(_loc2_[_loc4_].@value);
            _loc4_++;
         }
         m_currentHp = m_totalHp;
         m_currentMp = m_totalMp;
      }
      
      public function reSetValue(param1:int, param2:int, param3:int, param4:int, param5:Number, param6:Number, param7:Number, param8:Number, param9:Number) : void
      {
         totalHp = param1;
         attack = param2;
         expOfDieThisEnemy = param3;
         defence = param4;
         dogdeRate = param5;
         criticalRate = param6;
         criticalMuti = param7;
         deCriticalRate = param8;
         hitRate = param9;
         m_currentHp = m_totalHp;
         m_currentMp = m_totalMp;
      }
      
      public function reSetAttack(param1:int) : void
      {
         attack = param1;
      }
      
      public function getCurrentBlood() : uint
      {
         return m_currentHp;
      }
      
      public function addHp(param1:uint) : void
      {
         m_currentHp = Math.min(m_currentHp + param1,m_totalHp);
      }
      
      public function decHp(param1:uint) : void
      {
         m_currentHp = Math.max(m_currentHp - param1,0);
      }
      
      public function getDefence() : uint
      {
         return m_defence;
      }
      
      public function setDefence(param1:uint) : void
      {
         m_defence = param1;
      }
      
      public function getDeCriticalRate() : Number
      {
         return m_deCriticalRate;
      }
      
      public function setDeCriticalRate(param1:Number) : void
      {
         m_deCriticalRate = param1;
      }
      
      public function getDogdeRate() : Number
      {
         return m_dogdeRate;
      }
      
      public function getTotalHp() : uint
      {
         return m_totalHp;
      }
      
      public function getAttack() : uint
      {
         return m_attack;
      }
      
      public function setAttack(param1:uint) : void
      {
         m_attack = param1;
      }
      
      public function getExpOfDieThisEnemy() : uint
      {
         return m_expOfDieThisEnemy;
      }
      
      public function getCriticalRate() : Number
      {
         return m_criticalRate;
      }
      
      public function setCriticalRate(param1:uint) : void
      {
         m_criticalRate = param1;
      }
      
      public function getCriticalMuti() : Number
      {
         return m_criticalMuti;
      }
      
      public function getHitRate() : Number
      {
         return m_hitRate;
      }
      
      public function setHitRate(param1:uint) : void
      {
         m_hitRate = param1;
      }
      
      public function addMp(param1:uint) : void
      {
         m_currentMp = Math.min(m_currentMp + param1,m_totalMp);
      }
      
      public function decMp(param1:uint) : void
      {
         m_currentMp = Math.max(m_currentMp - param1,0);
      }
      
      public function getCurrentMp() : uint
      {
         return m_currentMp;
      }
      
      public function getTotalMp() : uint
      {
         return m_totalMp;
      }
      
      public function getRegHpPerS() : Number
      {
         return m_regHpPerS;
      }
      
      public function getRegMpPerS() : Number
      {
         return m_regMpPerS;
      }
      
      private function set totalHp(param1:uint) : void
      {
         m_totalHp = param1;
      }
      
      private function set attack(param1:uint) : void
      {
         m_attack = param1;
      }
      
      private function set expOfDieThisEnemy(param1:uint) : void
      {
         m_expOfDieThisEnemy = param1;
      }
      
      private function set defence(param1:uint) : void
      {
         m_defence = param1;
      }
      
      private function set dogdeRate(param1:Number) : void
      {
         m_dogdeRate = param1;
      }
      
      private function set deCriticalRate(param1:Number) : void
      {
         m_deCriticalRate = param1;
      }
      
      private function set hitRate(param1:Number) : void
      {
         m_hitRate = param1;
      }
      
      private function set criticalRate(param1:Number) : void
      {
         m_criticalRate = param1;
      }
      
      private function set criticalMuti(param1:Number) : void
      {
         m_criticalMuti = param1;
      }
      
      private function set totalMp(param1:uint) : void
      {
         m_totalMp = param1;
      }
      
      private function set regHpPerS(param1:Number) : void
      {
         m_regHpPerS = param1;
      }
      
      private function set regMpPerS(param1:Number) : void
      {
         m_regMpPerS = param1;
      }
   }
}

