package YJFY.ShowLogicShell.SwitchBtn
{
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SwitchBtnLogicShell implements IButton
   {
      
      protected var m_show:Sprite;
      
      public var data:Object;
      
      private var _SwitchBtns:Array;
      
      protected var _isActive:Boolean;
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      protected var m_clickEnble:Boolean;
      
      public function SwitchBtnLogicShell()
      {
         super();
         m_clickEnble = true;
      }
      
      public static function setDefaultActivateBtnFromBtns(param1:Array) : SwitchBtnLogicShell
      {
         if(param1 == null)
         {
            throw new Error();
         }
         SwitchBtnLogicShell(param1[0]).turnActivate();
         SwitchBtnLogicShell(param1[0]).getShow().dispatchEvent(new ButtonEvent("clickButton",param1[0]));
         return param1[0];
      }
      
      public function setShow(param1:Sprite) : void
      {
         ClearUtil.nullObject(data);
         data = null;
         m_show = param1;
         m_show.buttonMode = true;
         MovieClip(param1).gotoAndStop(2);
         _isActive = false;
         m_show.addEventListener("click",onClick,false,0,true);
         m_show.addEventListener("rollOver",onOver,false,0,true);
         m_show.addEventListener("rollOut",onOut,false,0,true);
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("click",onClick,false);
         }
         if(m_show)
         {
            m_show.removeEventListener("rollOut",onOut,false);
         }
         if(m_show)
         {
            m_show.removeEventListener("rollOver",onOver,false);
         }
         m_show = null;
         data = null;
         ClearUtil.nullArr(_SwitchBtns,false,false,false);
         _SwitchBtns = null;
      }
      
      public function setClickEnble(param1:Boolean = true) : void
      {
         m_clickEnble = param1;
      }
      
      public function setTipString(param1:String) : void
      {
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               createSmallToolTip();
            }
         }
      }
      
      protected function createSmallToolTip() : void
      {
         m_smallToolTip = new SmallToolTip();
         m_smallToolTip.setTipStr(m_tipString);
         m_smallToolTip.init();
      }
      
      protected function onClick(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(_isActive && param1)
         {
            return;
         }
         if(m_clickEnble == false)
         {
            return;
         }
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         turnActivate();
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
      
      public function turnActiveAndDispatchEvent() : void
      {
         onClick(null);
      }
      
      public function turnActivate() : void
      {
         var _loc2_:int = 0;
         MovieClip(m_show).gotoAndStop(1);
         _isActive = true;
         var _loc1_:int = _SwitchBtns ? _SwitchBtns.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_SwitchBtns[_loc2_] != this)
            {
               SwitchBtnLogicShell(_SwitchBtns[_loc2_]).turnInActivate();
            }
            _loc2_++;
         }
      }
      
      public function get isActive() : Boolean
      {
         return _isActive;
      }
      
      public function turnUnActivate() : void
      {
         turnInActivate();
      }
      
      protected function turnInActivate() : void
      {
         MovieClip(m_show).gotoAndStop(2);
         _isActive = false;
      }
      
      public function set switchBtns(param1:Array) : void
      {
         _SwitchBtns = param1;
      }
      
      protected function onOut(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(_isActive)
         {
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop(2);
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
         m_show.dispatchEvent(new ButtonEvent("onOutButton",this));
      }
      
      protected function onOver(param1:MouseEvent) : void
      {
         if(m_smallToolTip)
         {
            if(m_show.stage)
            {
               m_show.stage.addChild(m_smallToolTip);
               if(m_show.stage.mouseX + 10 + m_smallToolTip.width > m_show.stage.stageWidth)
               {
                  m_smallToolTip.x = m_show.stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = m_show.stage.mouseX + 10;
               }
               if(m_show.stage.mouseY + 10 + m_smallToolTip.height > m_show.stage.stageHeight)
               {
                  m_smallToolTip.y = m_show.stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = m_show.stage.mouseY + 10;
               }
            }
         }
         if(_isActive)
         {
            return;
         }
         try
         {
            if(MovieClip(m_show).totalFrames >= 3)
            {
               MovieClip(m_show).gotoAndStop(3);
            }
            else
            {
               MovieClip(m_show).gotoAndStop(1);
            }
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
         m_show.dispatchEvent(new ButtonEvent("onOverButton",this));
      }
   }
}

