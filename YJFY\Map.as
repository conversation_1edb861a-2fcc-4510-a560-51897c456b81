package YJFY
{
   import UI.EnterFrameTime;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameWorldLogic.IHavePlayersWorld;
   import YJFY.GameWorldLogic.WorldCameraPosition;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   
   public class Map extends MapBase implements IHavePlayersWorld
   {
      
      protected var m_worldCameraPositionTransfer:WorldCameraPosition;
      
      protected var m_player1:Player;
      
      protected var m_player2:Player;
      
      protected var m_player1InitPosition:Coordinate;
      
      protected var m_player2InitPosition:Coordinate;
      
      public function Map()
      {
         super();
         m_player1InitPosition = new Coordinate();
         m_player2InitPosition = new Coordinate();
         m_worldCameraPositionTransfer = new WorldCameraPosition();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_player1);
         m_player1 = null;
         ClearUtil.clearObject(m_player2);
         m_player2 = null;
         ClearUtil.clearObject(m_player1InitPosition);
         m_player1InitPosition = null;
         ClearUtil.clearObject(m_player2InitPosition);
         m_player2InitPosition = null;
         ClearUtil.clearObject(m_worldCameraPositionTransfer);
         m_worldCameraPositionTransfer = null;
         super.clear();
      }
      
      override public function init() : void
      {
         super.init();
         m_worldCameraPositionTransfer.init(this);
      }
      
      public function setPlayer1InitPosition(param1:Number, param2:Number, param3:Number) : void
      {
         m_player1InitPosition.setTo(param1,param2,param3);
         if(m_player1)
         {
            m_player1.getAnimalEntity().setNewPosition(m_player1InitPosition.getX(),m_player1InitPosition.getY(),m_player1InitPosition.getZ());
         }
      }
      
      public function setPlayer2InitPosition(param1:Number, param2:Number, param3:Number) : void
      {
         m_player2InitPosition.setTo(param1,param2,param3);
         if(m_player2)
         {
            m_player2.getAnimalEntity().setNewPosition(m_player2InitPosition.getX(),m_player2InitPosition.getY(),m_player2InitPosition.getZ());
         }
      }
      
      override public function render(param1:EnterFrameTime) : void
      {
         super.render(param1);
         if(m_player1)
         {
            m_player1.render();
         }
         if(m_player2)
         {
            m_player2.render();
         }
         m_worldCameraPositionTransfer.render(param1);
      }
      
      public function getPlayer1() : Player
      {
         return m_player1;
      }
      
      public function getPlayer2() : Player
      {
         return m_player2;
      }
      
      public function getWorld() : World
      {
         return m_world;
      }
   }
}

