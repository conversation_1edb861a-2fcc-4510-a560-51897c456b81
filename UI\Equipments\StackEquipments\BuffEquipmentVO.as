package UI.Equipments.StackEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.UIInterface.IBuffEquipmentVO;
   import UI.UIInterface.IOneValueEquipmentVO;
   
   public class BuffEquipmentVO extends StackEquipmentVO implements IOneValueEquipmentVO, IBuffEquipmentVO
   {
      
      private var _value:String;
      
      public function BuffEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.value = _value;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:BuffEquipmentVO = new BuffEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as BuffEquipmentVO).value = this.value;
      }
      
      public function get value() : String
      {
         return _antiwear.value;
      }
      
      public function set value(param1:String) : void
      {
         _antiwear.value = param1;
      }
   }
}

