package YJFY.LevelMode5
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAI.AILogic.IsOneEffectiveTarget;
   import YJFY.EntityAI.AutomationAI.SearchAttackTargetAI;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   
   public class CallNormalDancerAttackAI
   {
      
      private var m_isAbleAttack2:Boolean;
      
      private var m_isAbleAttack:Boolean;
      
      private var m_nextAbleAttackTime:Number;
      
      private var m_isOneEffectiveTarget:IsOneEffectiveTarget;
      
      private var m_unableAttackMinInterval:uint;
      
      private var m_unableAttackMaxInterval:uint;
      
      private var m_currentAttackIndex:uint;
      
      private var m_i:int;
      
      private var m_num:int;
      
      public var m_isSay:Boolean = false;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      private var m_coordinate:Coordinate;
      
      private var m_searchAttackTargetAI:SearchAttackTargetAI;
      
      private var m_tX:Number;
      
      private var m_tY:Number;
      
      public function CallNormalDancerAttackAI()
      {
         super();
         m_isAbleAttack = true;
         m_isOneEffectiveTarget = new IsOneEffectiveTarget();
         m_isAbleAttack2 = true;
         m_coordinate = new Coordinate();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_isOneEffectiveTarget);
         m_isOneEffectiveTarget = null;
         ClearUtil.clearObject(m_coordinate);
         m_coordinate = null;
         m_owner = null;
         m_world = null;
      }
      
      public function init(param1:IEntity, param2:World, param3:uint, param4:uint, param5:SearchAttackTargetAI) : void
      {
         m_unableAttackMaxInterval = param4;
         m_unableAttackMinInterval = param3;
         m_owner = param1 as IAnimalEntity;
         m_world = param2;
         m_searchAttackTargetAI = param5;
      }
      
      public function render() : void
      {
         if(m_owner.isInSkill())
         {
            return;
         }
         if(m_isAbleAttack2 == false)
         {
            return;
         }
         if(m_isOneEffectiveTarget.isOneEffectiveTarget(m_searchAttackTargetAI.getAttackTarget()) == false)
         {
            return;
         }
         if(m_isAbleAttack == false && m_nextAbleAttackTime < m_world.getWorldTime())
         {
            m_isAbleAttack = true;
         }
         if(m_isAbleAttack == false)
         {
            return;
         }
         if(m_searchAttackTargetAI.getSaveFoeEntitys().getCuboidIsContainAtLeastOneFoe(m_owner.getAttackRangeToWorld(),m_world))
         {
            if(m_owner.isAbleToIdle())
            {
               m_owner.idle();
            }
            if(m_owner.isAbleToAttack())
            {
               attack();
               m_isAbleAttack = false;
               m_nextAbleAttackTime = m_world.getWorldTime() + m_unableAttackMinInterval + Math.random() * (m_unableAttackMaxInterval - m_unableAttackMinInterval);
            }
         }
      }
      
      public function setIsAbleAttack(param1:Boolean) : void
      {
         m_isAbleAttack2 = param1;
      }
      
      public function getIsAbleAttack() : Boolean
      {
         return m_isAbleAttack && m_isAbleAttack2;
      }
      
      public function getWantAttackIndex() : uint
      {
         return m_currentAttackIndex;
      }
      
      private function attack() : void
      {
         if(m_owner.isInSkill())
         {
            return;
         }
         var _loc1_:uint = m_owner.getMaxAttackNum();
         if(_loc1_)
         {
            m_owner.setAttackShowIndex(m_currentAttackIndex + 1);
         }
         m_owner.attack();
         if(_loc1_)
         {
            ++m_currentAttackIndex;
            if(m_currentAttackIndex >= _loc1_)
            {
               m_currentAttackIndex = 0;
            }
         }
      }
   }
}

