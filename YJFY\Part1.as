package YJFY
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.BuffData;
   import UI.CheatData.CheatData;
   import UI.CollectTimePanel.CollectTimeSaveData;
   import UI.DetectionClass.DetectionClass;
   import UI.EmbedXMLLoad;
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.ExchangeEquipment.ExEPlayerData;
   import UI.ExchangeGiftBag.ExchangeGiftData;
   import UI.Farm.CoordGrid.SceneCoordGrids;
   import UI.Farm.FarmData;
   import UI.Farm.MouseManager.MouseManager;
   import UI.GamingUI;
   import UI.KeyManager.KeyManager;
   import UI.MainGameCall;
   import UI.MiragePanel.MirageData;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.NicknameSystem.NicknameData;
   import UI.NicknameSystem.NicknameRankListFunction;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import UI.PKUI.PKFunction;
   import UI.PKUI.PlayerDataForPK;
   import UI.Protect.ProtectData;
   import UI.RankListFunction;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RecaptureGold.RecaptureGoldFunction;
   import UI.RefineFactory.RefineFactoryData;
   import UI.Shop.ShopData;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.SignPanel.SignData;
   import UI.Task.TaskGoalManager;
   import UI.Task.TasksManager;
   import UI.UnAbleBuyEquipmentData;
   import UI.Utils.LocalSave;
   import UI.Utils.SaveGame;
   import UI.VersionControl;
   import UI.WorldBoss.WorldBossSaveData;
   import UI.XMLSingle;
   import UI.XiangMoLevelPanel.XiangMoLevelSaveData;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.NewRank.RankPK;
   import UI2.ProgramStartData.ProgramStartData;
   import UI2.broadcast.BroadcastFunction;
   import UI2.broadcast.GetDataFunction;
   import UI2.broadcast.TipPanel;
   import UI2.firstPay.FirstPayData;
   import UI2.newSign.NewSignData;
   import YJFY.API_4399.API_4399;
   import YJFY.API_4399.LogAPI.LogAPI;
   import YJFY.API_4399.LogAPI.LogAPIListener;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.API_4399.SaveAPI.SaveAPIListener2;
   import YJFY.BossMode.BossClairWorld;
   import YJFY.BossMode.BossUIAndData.BossSaveData;
   import YJFY.BossMode.BossWorld;
   import YJFY.BossMode.PetBossUIAndData.PetBossSaveData;
   import YJFY.CityMapMode.CityMap;
   import YJFY.EndlessMode.EndlessData;
   import YJFY.EndlessMode.EndlessLevelData;
   import YJFY.EndlessMode.EndlessManage;
   import YJFY.EndlessMode.EndlessWorld;
   import YJFY.EntityAnimation.AnimationDefinitionManager;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.AntiwearNumber;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.GameSystemPanel.GameSystemPanel;
   import YJFY.LevelMode1.Data.ILevelMode1RouteData;
   import YJFY.LevelMode1.DreamLand.DreamLandSaveData;
   import YJFY.LevelMode1.DreamLand.LevelcustomLogic_dreamLand;
   import YJFY.LevelMode1.DreamLand.RouteMapOfDreamLand;
   import YJFY.LevelMode1.IRouteMap;
   import YJFY.LevelMode1.Level;
   import YJFY.LevelMode1.LevelCustomLogic1;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.LevelMode1.RouteMap;
   import YJFY.LevelMode2.LevelWorld;
   import YJFY.LevelMode3.LevelNewData;
   import YJFY.LevelMode3.LevelNewWorld;
   import YJFY.LevelMode4.LevelAnheiWorld;
   import YJFY.LevelMode5.LevelEndDancerWorld;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.IPK_test;
   import YJFY.PKMode.PK;
   import YJFY.PKMode.PKData.MyPKSaveData;
   import YJFY.PKMode.PKLogic.PKWorld;
   import YJFY.PKMode2.PK2;
   import YJFY.SoundManager.SoundManager2;
   import YJFY.StartGamePanel.StartGamePanel;
   import YJFY.StartGamePanel.StartGamePanelListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.XydzjsLogic.XydzjsMainLineLogic;
   import YJFY.optimize.SaveNewFileData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import deng.fzip.FZipLoading;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.system.System;
   import flash.text.Font;
   import flash.utils.Timer;
   import net.hires.debug.Stats;
   
   public class Part1 extends MySprite
   {
      
      private static var m_instance:Part1;
      
      private var m_version:String = "1880";
      
      private const m_const_renderInterval:uint = 20;
      
      private var m_loadUILayer:MySprite;
      
      private var m_gameWaitShowLayer:MySprite;
      
      private var m_gameContentLayer:MySprite;
      
      private var m_cheatGameShowLayer:MySprite;
      
      private var m_keyManager:KeyManager;
      
      private var m_soundManager_global:SoundManager2;
      
      private var m_api4399:API_4399;
      
      private var m_logAPIListener:LogAPIListener;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:LoadUI2;
      
      private var m_gameWaitShow:MovieClip;
      
      private var m_cheatGamePanel:CheatGamePanel;
      
      public var m_enterFrameTime:EnterFrameTime;
      
      public var m_nowTime:Number;
      
      private var m_versionControl:VersionControl;
      
      private var m_storeStateListener:SaveAPIListener2;
      
      private var m_startGamePanel:StartGamePanel;
      
      private var m_startGamePanelListener:StartGamePanelListener;
      
      private var m_gameSystemPanel:GameSystemPanel;
      
      public var m_part2:Part2;
      
      private var m_cityMap:CityMap;
      
      private var m_routeMap:IRouteMap;
      
      private var m_level:Level;
      
      public var m_level2:LevelWorld;
      
      public var m_newLevel:LevelNewWorld;
      
      public var m_anyeLevel:LevelAnheiWorld;
      
      public var m_endDancerLevel:LevelEndDancerWorld;
      
      private var m_bossWorld:BossWorld;
      
      private var m_bossNewWorld:BossClairWorld;
      
      private var m_pk:IPK_test;
      
      private var m_rankworld:IPK_test;
      
      private var m_pkMap:PKWorld;
      
      private var m_renderTimer:Timer;
      
      private var m_configXML:XML;
      
      private var m_xydzjsMainLineLogic:XydzjsMainLineLogic;
      
      private var m_xydzjsSelfAnimationDefManager:AnimationDefinitionManager;
      
      private var m_loadPart:LoadPart;
      
      private var m_player1CurrentPosition:Coordinate;
      
      private var m_currentCityMapXMLPath:String;
      
      private var m_serviceHold:*;
      
      private var m_gameIsStop:Boolean;
      
      private var m_const_saveMaxLimitTime:uint = 6000;
      
      private var m_frameRate:Number;
      
      private var m_isFirst:Boolean;
      
      private var m_showNewYearTimer:Timer;
      
      private var m_isFive:Boolean = false;
      
      private var m_delayTime:uint;
      
      public var isCheat:Boolean = false;
      
      private var _temSaveXml:XML;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      private var _tippanel:TipPanel;
      
      private var m_test:Number;
      
      private var m_bSaving:Boolean = false;
      
      private var isHaveData:Boolean = false;
      
      private var m_bInResult:Boolean = false;
      
      private var m_bTimeOut:Boolean = false;
      
      private var m_currSaveInfo:SaveTaskInfo;
      
      private var m_nSaveIndex:int;
      
      private var m_xmlSave:XML;
      
      public var xmlData:XML;
      
      private var _firstSaveLasttime:int;
      
      private var m_enbedxmlload:EmbedXMLLoad;
      
      private var m_loadConfigCompleteFun:Function;
      
      private var _lastTime:int;
      
      private var _frame:int;
      
      private var m_payAPI:PayAPI;
      
      private var m_payAPIListener:PayAPIListener;
      
      private var remLottoryInfo:Array;
      
      public function Part1()
      {
         super();
         if(m_instance == null)
         {
            m_test = System.privateMemory;
            m_instance = this;
            mouseChildren = true;
            mouseEnabled = true;
            m_player1CurrentPosition = new Coordinate();
            m_gameContentLayer = new MySprite();
            m_gameContentLayer.mouseChildren = true;
            m_gameContentLayer.mouseEnabled = true;
            addChild(m_gameContentLayer);
            if(_tippanel == null)
            {
               _tippanel = new TipPanel(250,40,"alltip");
               addChild(_tippanel);
               _tippanel.hide();
            }
            m_gameWaitShowLayer = new MySprite();
            m_gameWaitShowLayer.mouseChildren = true;
            m_gameWaitShowLayer.mouseEnabled = true;
            addChild(m_gameWaitShowLayer);
            m_loadUILayer = new MySprite();
            m_loadUILayer.mouseChildren = true;
            m_loadUILayer.mouseEnabled = true;
            addChild(m_loadUILayer);
            m_cheatGameShowLayer = new MySprite();
            m_cheatGameShowLayer.mouseChildren = true;
            m_cheatGameShowLayer.mouseEnabled = true;
            addChild(m_cheatGameShowLayer);
            m_logAPIListener = new LogAPIListener();
            m_logAPIListener.logOutSuccessFun = logOutSuccess;
            AntiwearNumber.setErrFun(MyFunction2.doIsCheat);
            AntiwearNumber.nums;
            _binaryEn = new binaryEncrypt();
            _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
            temSaveXml = null;
            trace("构造 = ",System.privateMemory - m_test);
            return;
         }
         throw new Error("实例已经存在了");
      }
      
      public static function getInstance() : Part1
      {
         if(m_instance == null)
         {
            m_instance = new Part1();
         }
         return m_instance;
      }
      
      override public function clear() : void
      {
         clear3();
         if(m_configXML)
         {
            System.disposeXML(m_configXML);
         }
         m_configXML = null;
         ClearUtil.clearObject(m_versionControl);
         m_versionControl = null;
         ClearUtil.clearObject(m_loadUILayer);
         m_loadUILayer = null;
         ClearUtil.clearObject(m_cheatGameShowLayer);
         m_cheatGameShowLayer = null;
         m_serviceHold = null;
         ClearUtil.clearObject(m_player1CurrentPosition);
         m_player1CurrentPosition = null;
         ClearUtil.clearObject(m_gameWaitShowLayer);
         m_gameWaitShowLayer = null;
         ClearUtil.clearObject(m_gameContentLayer);
         m_gameContentLayer = null;
         m_instance = null;
         ClearUtil.clearObject(m_logAPIListener);
         m_logAPIListener = null;
         ClearUtil.clearObject(m_cheatGamePanel);
         m_cheatGamePanel = null;
         ClearUtil.clearObject(m_storeStateListener);
         m_storeStateListener = null;
         m_loadConfigCompleteFun = null;
         ClearUtil.clearObject(_tippanel);
         _tippanel = null;
         super.clear();
      }
      
      public function reStartGame() : void
      {
         m_bSaving = false;
         isHaveData = false;
         m_bInResult = false;
         m_bTimeOut = false;
         EndlessLevelData.clear();
         clear3();
         init2();
      }
      
      private function clear2() : void
      {
         stage.removeEventListener("deactivate",deActive,false);
         m_version = null;
         ClearUtil.clearObject(m_enbedxmlload);
         m_enbedxmlload = null;
         ClearUtil.clearObject(m_keyManager);
         m_keyManager = null;
         ClearUtil.clearObject(m_soundManager_global);
         m_soundManager_global = null;
         ClearUtil.clearObject(m_api4399);
         m_api4399 = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_loadUI);
         m_loadUI = null;
         ClearUtil.clearObject(m_gameWaitShow);
         m_gameWaitShow = null;
         ClearUtil.clearObject(m_enterFrameTime);
         m_enterFrameTime = null;
         ClearUtil.clearObject(m_startGamePanel);
         m_startGamePanel = null;
         ClearUtil.clearObject(m_startGamePanelListener);
         m_startGamePanelListener = null;
         ClearUtil.clearObject(m_gameSystemPanel);
         m_gameSystemPanel = null;
         ClearUtil.clearObject(m_part2);
         m_part2 = null;
         ClearUtil.clearObject(m_cityMap);
         m_cityMap = null;
         ClearUtil.clearObject(m_routeMap);
         m_routeMap = null;
         ClearUtil.clearObject(m_level);
         m_level = null;
         ClearUtil.clearObject(m_level2);
         m_level2 = null;
         ClearUtil.clearObject(m_bossWorld);
         m_bossWorld = null;
         ClearUtil.clearObject(m_bossNewWorld);
         m_bossNewWorld = null;
         ClearUtil.clearObject(m_pk);
         m_pk = null;
         if(m_renderTimer)
         {
            m_renderTimer.stop();
         }
         m_renderTimer = null;
         _tippanel.hide();
         if(m_showNewYearTimer)
         {
            m_showNewYearTimer.stop();
            m_showNewYearTimer.removeEventListener("timer",ToShowNewYear);
         }
         m_showNewYearTimer = null;
         ClearUtil.clearObject(m_loadPart);
         m_loadPart = null;
         m_currentCityMapXMLPath = null;
         ClearUtil.clearObject(m_xydzjsMainLineLogic);
         m_xydzjsMainLineLogic = null;
         ClearUtil.clearObject(m_xydzjsSelfAnimationDefManager);
         m_xydzjsSelfAnimationDefManager = null;
      }
      
      private function clear3() : void
      {
         isCheat = false;
         ClearUtil.clearObject(BuffData.getInstance());
         ClearUtil.clearObject(CheatData.getInstance());
         ClearUtil.clearObject(CollectTimeSaveData.getInstance());
         ClearUtil.clearObject(ExchangeGiftData.getInstance());
         ClearUtil.clearObject(SceneCoordGrids.getInstance());
         ClearUtil.clearObject(MouseManager.getInstance());
         ClearUtil.clearObject(FarmData.getInstance());
         ClearUtil.clearObject(MirageData.getInstance());
         ClearUtil.clearObject(NicknameData.getInstance());
         ClearUtil.clearObject(OnLineGiftBagData.getInstance());
         ClearUtil.clearObject(PlayerDataForPK.getInstance());
         ClearUtil.clearObject(ProtectData.getInstance());
         ClearUtil.clearObject(RecaptureGoldData.getInstance());
         ClearUtil.clearObject(RefineFactoryData.getInstance());
         ClearUtil.clearObject(ShopData.getInstance());
         ClearUtil.clearObject(CurrentTicketPointManager.getInstance());
         ClearUtil.clearObject(SignData.getInstance());
         ClearUtil.clearObject(TaskGoalManager.getInstance());
         ClearUtil.clearObject(TasksManager.getInstance());
         ClearUtil.clearObject(WorldBossSaveData.getInstance());
         ClearUtil.clearObject(AnalogServiceHoldFunction.getInstance());
         ClearUtil.clearObject(GamingUI.getInstance());
         ClearUtil.clearObject(UnAbleBuyEquipmentData.getInstance());
         ClearUtil.clearObject(BossSaveData.getInstance());
         ClearUtil.clearObject(PetBossSaveData.getInstance());
         ClearUtil.clearObject(LevelModeSaveData.getInstance());
         ClearUtil.clearObject(MyPKSaveData.getInstance());
         ClearUtil.clearObject(XiangMoLevelSaveData.getInstance());
         ClearUtil.clearObject(FirstPayData.getInstance());
         ClearUtil.clearObject(NewSignData.getInstance());
         ClearUtil.clearObject(GameData.getInstance());
         ClearUtil.clearObject(LoadPart.getInstance());
         ClearUtil.clearObject(NicknameRankListFunction.getInstance());
         ClearUtil.clearObject(PKFunction.getInstance());
         ClearUtil.clearObject(RecaptureGoldFunction.getInstance());
         ClearUtil.clearObject(RankListFunction.getInstance());
         ClearUtil.clearObject(APIDataAnalyze.getInstance());
         ClearUtil.clearObject(DreamLandSaveData.getInstance());
         ClearUtil.clearObject(ExEPlayerData.getInstance());
         XMLSingle.getInstance().clear();
         XMLSingle.getInstance().clear2();
         clear2();
      }
      
      public function setServiceHold(param1:*) : void
      {
         m_serviceHold = param1;
      }
      
      public function getLoadUI() : LoadUI2
      {
         return m_loadUI;
      }
      
      public function getConfigXML() : XML
      {
         return m_configXML;
      }
      
      public function init(param1:Function) : void
      {
         m_loadConfigCompleteFun = param1;
         loadConfigXML(m_loadConfigCompleteFun);
         stage.quality = "high";
         addEventListener("click",clickTrace,true,0,true);
      }
      
      private function clickTrace(param1:MouseEvent) : void
      {
         var _loc2_:DisplayObject = param1.target as DisplayObject;
         while(Boolean(_loc2_) && _loc2_ != stage)
         {
            _loc2_ = _loc2_.parent;
         }
      }
      
      public function init2() : void
      {
         m_renderTimer = new Timer(20);
         m_renderTimer.addEventListener("timer",render,false,0,true);
         m_renderTimer.start();
         m_api4399 = new API_4399();
         m_api4399.setServiceHold(m_serviceHold);
         m_api4399.setVersionControl(m_versionControl);
         m_api4399.init(this.stage);
         m_api4399.saveAPI.setShareObjName("xydzjs2");
         m_api4399.saveAPI.setLocalSave(new LocalSave());
         m_api4399.logAPI.addLogAPIListener(m_logAPIListener);
         m_enterFrameTime = new EnterFrameTime();
         m_enterFrameTime.init(new Date().getTime());
         m_myLoader = new YJFYLoader();
         m_loadUI = new LoadUI2(m_versionControl,m_myLoader);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.setProgressShow(m_loadUI);
         m_loadUILayer.addChild(m_loadUI);
         m_loadPart = LoadPart.getInstance();
         m_loadPart.init(m_configXML,m_versionControl,m_loadUI);
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            init2_1();
         }
         else
         {
            loadZipXML(m_loadUI);
         }
      }
      
      private function init2_1() : void
      {
         var _loc1_:Stats = null;
         loadFontAndOther();
         m_soundManager_global = new SoundManager2();
         m_soundManager_global.setMyLoader(m_myLoader);
         m_xydzjsMainLineLogic = new XydzjsMainLineLogic();
         m_xydzjsSelfAnimationDefManager = new AnimationDefinitionManager();
         m_xydzjsSelfAnimationDefManager.setLoader(m_myLoader);
         ProgramStartData.getInstance();
         m_enbedxmlload = new EmbedXMLLoad();
         m_enbedxmlload.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_enbedxmlload.setMyLoader(m_myLoader);
         m_enbedxmlload.initXML();
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            _loc1_ = new Stats();
            this.stage.addChild(_loc1_);
         }
         if(_tippanel)
         {
            _tippanel.initTip();
         }
      }
      
      public function getMyLoader() : YJFYLoader
      {
         return m_myLoader;
      }
      
      public function doInit3() : void
      {
         var _loc1_:SaveTaskInfo = null;
         hideGameWaitShow();
         stage.addEventListener("deactivate",deActive,false,0,true);
         m_player1CurrentPosition.setTo(2500,300,0);
         if(m_isFirst)
         {
            _loc1_ = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame();
         }
         openCityMap("NewGameFolder/CityMap1.xml",null);
         GamingUI.getInstance().enterToWeekPay();
      }
      
      private function init3() : void
      {
         doInit3();
      }
      
      private function onGetLotteryInfoHandler(param1:Event) : void
      {
         var _loc2_:URLLoader = param1.currentTarget as URLLoader;
         _loc2_.removeEventListener("complete",onGetLotteryInfoHandler);
         remLottoryInfo = MyJSON.decode(_loc2_.data);
         m_payAPIListener = new PayAPIListener();
         m_payAPIListener.getRechargedMoneyErrorFun = getRechargedMoneyError;
         m_payAPIListener.getRechargedMoneySuccessFun = getRechargedMoneySuccess;
         m_payAPI = GamingUI.getInstance().getAPI4399().payAPI;
         m_payAPI.addPayAPIListener(m_payAPIListener);
         m_payAPI.getTotalRechargeFun(null);
      }
      
      public function setTipXY(param1:Number, param2:Number) : void
      {
         if(_tippanel)
         {
            _tippanel.setPosXY(param1,param2);
         }
      }
      
      public function hideTipForPet() : void
      {
         if(_tippanel)
         {
            BroadcastFunction.getInstance().zanting = true;
            _tippanel.hide();
         }
      }
      
      public function showTipForPet() : void
      {
         if(_tippanel)
         {
            BroadcastFunction.getInstance().zanting = false;
            _tippanel.showVisible();
         }
      }
      
      private function getRechargedMoneySuccess(param1:int) : void
      {
         var _loc2_:EquipmentVO = null;
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_payAPIListener.clear();
         m_payAPI = null;
         var _loc3_:int = 0;
         if(GamingUI.getInstance().player1)
         {
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player1.playerVO.packageEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player1.playerVO.inforEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player1.playerVO.storageEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player1.playerVO.storageEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player1.playerVO.storageEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
         }
         if(GamingUI.getInstance().player2)
         {
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player2.playerVO.packageEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player2.playerVO.inforEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < GamingUI.getInstance().player2.playerVO.storageEquipmentVOs.length)
            {
               _loc2_ = GamingUI.getInstance().player2.playerVO.storageEquipmentVOs[_loc3_];
               if(_loc2_ && checkError(_loc2_,param1))
               {
                  GamingUI.getInstance().player2.playerVO.storageEquipmentVOs[_loc3_] = null;
               }
               _loc3_++;
            }
         }
         remLottoryInfo = null;
         doInit3();
      }
      
      private function checkError(param1:EquipmentVO, param2:int) : Boolean
      {
         var _loc3_:Array = null;
         var _loc4_:* = null;
         if(param2 < 3346)
         {
            _loc3_ = [10704014,10704114,10704214,10704314,10704414,10704514,10704614,10704714,10704814,10704914,10104014,10104114,10104214,10104314,10104414,10104514,10104614,10104714,10104814,10104914,10702014,10702114,10702214,10702314,10702414,10702514,10702614,10702714,10702814,10702914,10102014,10102114,10102214,10102314,10102414,10102514,10102614,10102714,10102814,10102914,10706014,10706114,10706214,10706314,10706414,10706514,10706614,10706714,10706814,10706914,10106014,10106114,10106214,10106314,10106414,10106514,10106614,10106714,10106814,10106914,10701014,10701114,10701214,10701314,10701414,10701514,10701614,10701714,10701814,10701914,10101014,10101114,10101214,10101314,10101414,10101514,10101614,10101714,10101814,10101914,10705014,10705114,10705214,10705314,10705414,10705514,10705614,10705714,10705814,10705914,10105014,10105114,10105214,10105314,10105414,10105514,10105614,10105714,10105814,10105914,10707014,10707114,10707214,10707314,10707414,10707514,10707614,10707714,10707814,10707914,10107014
            ,10107114,10107214,10107314,10107414,10107514,10107614,10107714,10107814,10107914];
            if(_loc3_.indexOf(param1.id) != -1)
            {
               return true;
            }
         }
         if(param2 < 700)
         {
            _loc3_ = [10200014,10200114,10200214,10200314,10200414,10200514,10200614,10200714,10200814,10200914];
            if(_loc3_.indexOf(param1.id) != -1)
            {
               for each(_loc4_ in remLottoryInfo)
               {
                  if(_loc4_.itemid == 10200014)
                  {
                     return false;
                  }
               }
               return true;
            }
         }
         if(param2 < 700)
         {
            _loc3_ = [10300014,10300114,10300214,10300314,10300414,10300514,10300614,10300714,10300814,10300914];
            if(_loc3_.indexOf(param1.id) != -1)
            {
               for each(_loc4_ in remLottoryInfo)
               {
                  if(_loc4_.itemid == 10300014)
                  {
                     return false;
                  }
               }
               return true;
            }
         }
         return false;
      }
      
      private function getRechargedMoneyError() : void
      {
      }
      
      private function ToShowNewYear(param1:Event) : void
      {
         removeEventListener("openSvActivityEvent",ToShowNewYear);
      }
      
      private function loadZipXML(param1:LoadUI2) : void
      {
         var _loc2_:FZipLoading = new FZipLoading();
         _loc2_.initLoaders(param1,init2_1,m_versionControl);
         _loc2_.load();
      }
      
      private function loadFontAndOther() : void
      {
         m_myLoader.getClass("NewGameFolder/FirstEnterSource.swf","GameWait",getGameWaitShowSuccess,getFail);
         m_myLoader.getClass("FontUI.swf","UI.MyFont.FangZhengKaTongJianTi",getFontSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getGameWaitShowSuccess(param1:YJFYLoaderData) : void
      {
         m_gameWaitShow = new param1.resultClass();
      }
      
      private function getZipSuccess(param1:YJFYLoaderData) : void
      {
         trace("zip加载成功");
      }
      
      private function getFontSuccess(param1:YJFYLoaderData) : void
      {
         Font.registerFont(param1.resultClass);
         openStartGamePanel();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function render(param1:Event) : void
      {
         if(m_enbedxmlload == null || m_enbedxmlload.getIsAllLoad() == false)
         {
            return;
         }
         if(m_enterFrameTime == null)
         {
            return;
         }
         var _loc2_:Number = Number(new Date().getTime());
         m_enterFrameTime.render(_loc2_);
         if(m_part2)
         {
            GamingUI.getInstance().render(m_enterFrameTime);
         }
         if(m_gameSystemPanel)
         {
            m_gameSystemPanel.render(m_enterFrameTime);
         }
         if(m_xydzjsSelfAnimationDefManager)
         {
            m_xydzjsSelfAnimationDefManager.render();
         }
         if(m_bSaving && isHaveData == false && m_bInResult == false)
         {
            saveGameData();
         }
         if(m_bTimeOut)
         {
            sendTime();
         }
         if(m_gameIsStop)
         {
            return;
         }
         if(m_cityMap)
         {
            m_cityMap.render(m_enterFrameTime);
            if(m_cityMap)
            {
               m_cityMap.showRender();
            }
         }
         if(m_level)
         {
            if(!(m_level.m_gameIsEnd && m_level.m_bWin == false))
            {
               m_level.render(m_enterFrameTime);
               m_level.showRender();
            }
         }
         if(m_level2)
         {
            m_level2.render(m_enterFrameTime);
            m_level2.showRender();
         }
         if(m_bossWorld)
         {
            m_bossWorld.render(m_enterFrameTime);
            m_bossWorld.showRender();
         }
         if(m_bossNewWorld)
         {
            m_bossNewWorld.render(m_enterFrameTime);
            m_bossNewWorld.showRender();
         }
         if(m_pk)
         {
            m_pk.render(m_enterFrameTime);
         }
         if(m_rankworld)
         {
            m_rankworld.render(m_enterFrameTime);
         }
         if(m_newLevel)
         {
            m_newLevel.render(m_enterFrameTime);
            m_newLevel.showRender();
         }
         if(m_anyeLevel)
         {
            m_anyeLevel.render(m_enterFrameTime);
            m_anyeLevel.showRender();
         }
         if(m_endDancerLevel)
         {
            m_endDancerLevel.render(m_enterFrameTime);
            m_endDancerLevel.showRender();
         }
         BroadcastFunction.getInstance().render(m_enterFrameTime);
         if(m_routeMap)
         {
            if(m_routeMap is RouteMap)
            {
               (m_routeMap as RouteMap).render(m_enterFrameTime);
            }
         }
      }
      
      public function stopGame() : void
      {
         m_gameIsStop = true;
         if(m_enterFrameTime)
         {
            m_enterFrameTime.stopGame(new Date().getTime());
         }
         if(m_part2)
         {
            GamingUI.getInstance().stopGame();
         }
         Part1.getInstance().setTipXY(250,10);
      }
      
      public function continueGame() : void
      {
         m_gameIsStop = false;
         if(m_enterFrameTime)
         {
            m_enterFrameTime.continueGame(new Date().getTime());
         }
         if(m_part2)
         {
            GamingUI.getInstance().continueGame();
         }
         stage.focus = this;
         Part1.getInstance().setTipXY(250,150);
      }
      
      private function completelyStopGame() : void
      {
         stopGame();
      }
      
      private function completelyContinueGame() : void
      {
         continueGame();
      }
      
      public function getGameIsStop() : Boolean
      {
         return m_gameIsStop;
      }
      
      public function turnAllSoundOn() : void
      {
         SoundMixer.soundTransform = new SoundTransform(1,SoundMixer.soundTransform.pan);
      }
      
      public function turnAllSoundOff() : void
      {
         SoundMixer.soundTransform = new SoundTransform(0,SoundMixer.soundTransform.pan);
      }
      
      public function showGameWaitShow() : void
      {
         if(m_gameWaitShow && m_gameWaitShowLayer.contains(m_gameWaitShow) == false)
         {
            m_gameWaitShowLayer.addChild(m_gameWaitShow);
         }
      }
      
      public function hideGameWaitShow() : void
      {
         if(Boolean(m_gameWaitShow) && m_gameWaitShowLayer.contains(m_gameWaitShow))
         {
            m_gameWaitShowLayer.removeChild(m_gameWaitShow);
         }
      }
      
      private function loadConfigXML(param1:Function) : void
      {
         var configXMLURL:String;
         var completeFun:Function = param1;
         var failHandler:* = function(param1:Event):void
         {
         };
         var complete:* = function(param1:Event):void
         {
            param1.currentTarget.removeEventListener("complete",complete,false);
            param1.currentTarget.removeEventListener("securityError",failHandler,false);
            param1.currentTarget.removeEventListener("ioError",failHandler,false);
            m_configXML = new XML((param1.currentTarget as URLLoader).data);
            m_versionControl = new VersionControl();
            completeFun();
         };
         var loader:URLLoader = new URLLoader();
         loader.addEventListener("securityError",failHandler,false);
         loader.addEventListener("ioError",failHandler,false);
         loader.addEventListener("complete",complete,false);
         configXMLURL = "config" + m_version + ".xml?v=" + Math.random() * 999999;
         loader.load(new URLRequest(configXMLURL));
         trace("[XML]",configXMLURL);
      }
      
      private function openStartGamePanel() : void
      {
         m_startGamePanel = new StartGamePanel();
         m_gameContentLayer.addChild(m_startGamePanel);
         m_startGamePanel.setLogAPI(m_api4399.logAPI);
         m_startGamePanel.setSaveAPI(m_api4399.saveAPI);
         m_startGamePanel.setVersionControl(m_versionControl);
         m_startGamePanel.init();
         m_startGamePanelListener = new StartGamePanelListener();
         m_startGamePanel.addStartGamePanelListener(m_startGamePanelListener);
         m_startGamePanelListener.startGameFun = startGame;
      }
      
      public function getApi4399() : API_4399
      {
         return m_api4399;
      }
      
      public function getXydzjMainLineLogic() : XydzjsMainLineLogic
      {
         return m_xydzjsMainLineLogic;
      }
      
      public function getXydzjSelfAnimationManager() : AnimationDefinitionManager
      {
         return m_xydzjsSelfAnimationDefManager;
      }
      
      private function closeStartGamePanel() : void
      {
         ClearUtil.clearObject(m_startGamePanel);
         m_startGamePanel = null;
         ClearUtil.clearObject(m_startGamePanelListener);
         m_startGamePanelListener = null;
      }
      
      public function openSystemPanel() : void
      {
         m_gameSystemPanel = new GameSystemPanel();
         m_gameSystemPanel.setPart1(this);
         m_gameSystemPanel.init();
         addChild(m_gameSystemPanel);
         if(!m_cityMap)
         {
            m_gameSystemPanel.show2();
         }
         completelyStopGame();
      }
      
      public function closeSystemPanel() : void
      {
         ClearUtil.clearObject(m_gameSystemPanel);
         m_gameSystemPanel = null;
         completelyContinueGame();
      }
      
      public function getGameSystemPanel() : GameSystemPanel
      {
         return m_gameSystemPanel;
      }
      
      private function initPart2(param1:String, param2:String, param3:XML, param4:Function) : void
      {
         if(m_part2 == null)
         {
            showGameWaitShow();
            m_part2 = new Part2();
            m_gameContentLayer.addChild(m_part2);
            m_part2.setVersionControl(m_versionControl);
            m_part2.setMyLoader(m_myLoader);
            m_part2.init(param1,param2,param3,param4);
         }
      }
      
      public function openCityMap(param1:String, param2:Coordinate) : void
      {
         Part1.getInstance().setTipXY(250,150);
         if(param2)
         {
            m_player1CurrentPosition.copy(param2);
         }
         if(param1)
         {
            m_currentCityMapXMLPath = param1;
         }
         m_cityMap = new CityMap();
         m_gameContentLayer.addChild(m_cityMap);
         m_cityMap.setEnterFrameTime(m_enterFrameTime);
         m_cityMap.setVersionControl(m_versionControl);
         m_cityMap.setLoadUI(m_loadUI);
         m_cityMap.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_cityMap.setPlayer1InitPosition(m_player1CurrentPosition.getX(),m_player1CurrentPosition.getY(),m_player1CurrentPosition.getZ());
         m_cityMap.setPlayer2InitPosition(m_player1CurrentPosition.getX() + 100,m_player1CurrentPosition.getY() + 50,m_player1CurrentPosition.getZ());
         m_cityMap.init();
         m_cityMap.initByXMLPath(m_currentCityMapXMLPath);
         if(m_part2.parent)
         {
            m_part2.parent.removeChild(m_part2);
         }
         m_cityMap.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToNormalState();
         stage.focus = this;
      }
      
      public function closeCityMap() : void
      {
         if(Boolean(m_cityMap) && m_cityMap.parent)
         {
            m_cityMap.parent.removeChild(m_cityMap);
         }
         if(m_cityMap)
         {
            m_cityMap.removeUIShow(m_part2);
         }
         ClearUtil.clearObject(m_cityMap);
         m_cityMap = null;
         Part1.getInstance().setTipXY(250,10);
      }
      
      private function loadStartGameSource(param1:Function) : void
      {
         LoadPart.getInstance().loadOnePart1(param1,"startGameEnterSources");
      }
      
      public function openRouteMap(param1:String, param2:String, param3:String) : void
      {
         m_routeMap = new RouteMap();
         var _loc4_:RouteMap = m_routeMap as RouteMap;
         _loc4_.setLoadUI(m_loadUI);
         m_loadUI.tranToOpacity();
         _loc4_.setVersionControl(m_versionControl);
         _loc4_.setLoader(m_myLoader);
         _loc4_.setYJFY(this);
         _loc4_.init(param1,param2,param3);
         m_gameContentLayer.addChild(_loc4_);
         stage.focus = this;
      }
      
      public function openRouteMapByTask(param1:String, param2:String, param3:String, param4:String) : void
      {
         m_routeMap = new RouteMap();
         var _loc5_:RouteMap = m_routeMap as RouteMap;
         _loc5_.setLoadUI(m_loadUI);
         m_loadUI.tranToOpacity();
         _loc5_.setVersionControl(m_versionControl);
         _loc5_.setLoader(m_myLoader);
         _loc5_.setGoto(true);
         _loc5_.setGotoName(param4);
         _loc5_.setYJFY(this);
         _loc5_.init(param1,param2,param3);
         m_gameContentLayer.addChild(_loc5_);
         stage.focus = this;
      }
      
      public function openEndless() : void
      {
         Part1.getInstance().setTipXY(250,150);
         EndlessManage.getInstance().IsEndlessMode = true;
         m_level2 = new EndlessWorld();
         m_gameContentLayer.addChild(m_level2);
         m_level2.setVersionControl(m_versionControl);
         m_level2.setLoadUI(m_loadUI);
         m_level2.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level2.setEnterFrameTime(m_enterFrameTime);
         m_level2.setPlayer1InitPosition(300,200,0);
         m_level2.setPlayer2InitPosition(250,250,0);
         m_level2.init();
         var _loc3_:EndlessData = EndlessLevelData.getInstance().getFuBenDataByIndex(EndlessManage.getInstance().EndlessLevel,EndlessManage.getInstance().remPlayerMode);
         if(_loc3_.waves == 0)
         {
            _loc3_.addWaves();
         }
         var _loc1_:uint = EndlessManage.getInstance().remJumpWaves == 0 ? _loc3_.waves : EndlessManage.getInstance().remJumpWaves;
         var _loc2_:String = _loc3_.getLevelsPathId(_loc1_);
         m_level2.initByXMLPath(_loc2_);
         m_level2.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openRouteMap_dreamLand() : void
      {
         var _loc1_:String = "Resources/RouteMapOfDL/RouteMapOfDL.swf";
         var _loc3_:String = "RouteMapOfDReamLand";
         var _loc4_:String = "NewGameFolder/GuardingTangSengLevelMode/routeMap_dreamLand.xml";
         m_routeMap = new RouteMapOfDreamLand();
         var _loc2_:RouteMapOfDreamLand = m_routeMap as RouteMapOfDreamLand;
         m_gameContentLayer.addChild(_loc2_);
         _loc2_.setLoadUI(m_loadUI);
         m_loadUI.tranToOpacity();
         _loc2_.setVersionControl(m_versionControl);
         _loc2_.setLoader(m_myLoader);
         _loc2_.setPart1(this);
         _loc2_.init(_loc1_,_loc3_,_loc4_);
         stage.focus = this;
      }
      
      public function closeRouteMap() : void
      {
         ClearUtil.clearObject(m_routeMap);
         m_routeMap = null;
      }
      
      public function openBossWorld(param1:String) : void
      {
         closeCityMap();
         m_bossWorld = new BossWorld();
         m_gameContentLayer.addChild(m_bossWorld);
         m_bossWorld.setVersionControl(m_versionControl);
         m_bossWorld.setLoadUI(m_loadUI);
         m_bossWorld.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_bossWorld.setEnterFrameTime(m_enterFrameTime);
         m_bossWorld.setPlayer1InitPosition(300,200,0);
         m_bossWorld.setPlayer2InitPosition(250,250,0);
         m_bossWorld.init();
         m_bossWorld.initByXMLPath(param1);
         m_bossWorld.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openBossNewWorld(param1:String) : void
      {
         closeCityMap();
         m_bossNewWorld = new BossClairWorld();
         m_gameContentLayer.addChild(m_bossNewWorld);
         m_bossNewWorld.setVersionControl(m_versionControl);
         m_bossNewWorld.setLoadUI(m_loadUI);
         m_bossNewWorld.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_bossNewWorld.setEnterFrameTime(m_enterFrameTime);
         m_bossNewWorld.setPlayer1InitPosition(300,200,0);
         m_bossNewWorld.setPlayer2InitPosition(250,250,0);
         m_bossNewWorld.init();
         m_bossNewWorld.initByXMLPath(param1);
         m_bossNewWorld.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevel1(param1:String, param2:String, param3:ILevelMode1RouteData) : void
      {
         m_level = new Level();
         m_gameContentLayer.addChild(m_level);
         m_level.setVersionControl(m_versionControl);
         m_level.setLoadUI(m_loadUI);
         m_level.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level.setEnterFrameTime(m_enterFrameTime);
         m_level.setPlayer1InitPosition(300,200,0);
         m_level.setPlayer2InitPosition(250,250,0);
         m_level.init();
         m_level.setLevelCustomLogic(new LevelCustomLogic1());
         m_level.setLevelRouteData(param2,param3);
         m_level.initByXMLPath(param1);
         m_level.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevel1_2(param1:XML, param2:String, param3:ILevelMode1RouteData, param4:String = null) : void
      {
         m_level = new Level();
         m_gameContentLayer.addChild(m_level);
         m_level.setVersionControl(m_versionControl);
         m_level.setLoadUI(m_loadUI);
         m_level.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level.setEnterFrameTime(m_enterFrameTime);
         m_level.setPlayer1InitPosition(300,200,0);
         m_level.setPlayer2InitPosition(250,250,0);
         m_level.init();
         m_level.setLevelCustomLogic(new LevelCustomLogic1());
         m_level.setLevelRouteData(param2,param3);
         m_level.setCurMapXmlPath(param4);
         m_level.initByXML(param1);
         m_level.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevel_dreamLand(param1:String, param2:String, param3:ILevelMode1RouteData) : void
      {
         m_level = new Level();
         m_gameContentLayer.addChild(m_level);
         m_level.setVersionControl(m_versionControl);
         m_level.setLoadUI(m_loadUI);
         m_level.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level.setEnterFrameTime(m_enterFrameTime);
         m_level.setPlayer1InitPosition(300,200,0);
         m_level.setPlayer2InitPosition(250,250,0);
         m_level.init();
         m_level.setLevelCustomLogic(new LevelcustomLogic_dreamLand());
         m_level.setLevelRouteData(param2,param3);
         m_level.initByXMLPath(param1);
         m_level.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevel_dreamLand_2(param1:XML, param2:String, param3:ILevelMode1RouteData) : void
      {
         m_level = new Level();
         m_gameContentLayer.addChild(m_level);
         m_level.setVersionControl(m_versionControl);
         m_level.setLoadUI(m_loadUI);
         m_level.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level.setEnterFrameTime(m_enterFrameTime);
         m_level.setPlayer1InitPosition(300,200,0);
         m_level.setPlayer2InitPosition(250,250,0);
         m_level.init();
         m_level.setLevelCustomLogic(new LevelcustomLogic_dreamLand());
         m_level.setLevelRouteData(param2,param3);
         m_level.initByXML(param1);
         m_level.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevel2(param1:String) : void
      {
         Part1.getInstance().setTipXY(250,150);
         m_level2 = new LevelWorld();
         m_gameContentLayer.addChild(m_level2);
         m_level2.setVersionControl(m_versionControl);
         m_level2.setLoadUI(m_loadUI);
         m_level2.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_level2.setEnterFrameTime(m_enterFrameTime);
         m_level2.setPlayer1InitPosition(300,200,0);
         m_level2.setPlayer2InitPosition(250,250,0);
         m_level2.init();
         m_level2.initByXMLPath(param1);
         m_level2.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openAnyeLevel(param1:String) : void
      {
         Part1.getInstance().setTipXY(250,150);
         LevelNewData.getInstance().setPath(param1);
         m_anyeLevel = new LevelAnheiWorld();
         m_gameContentLayer.addChild(m_anyeLevel);
         m_anyeLevel.setVersionControl(m_versionControl);
         m_anyeLevel.setLoadUI(m_loadUI);
         m_anyeLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_anyeLevel.setEnterFrameTime(m_enterFrameTime);
         m_anyeLevel.setPlayer1InitPosition(300,200,0);
         m_anyeLevel.setPlayer2InitPosition(250,250,0);
         m_anyeLevel.init();
         m_anyeLevel.initByXMLPath(param1);
         m_anyeLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openAnyeLevel_2(param1:String) : void
      {
         m_anyeLevel = new LevelAnheiWorld();
         m_gameContentLayer.addChild(m_anyeLevel);
         m_anyeLevel.setVersionControl(m_versionControl);
         m_anyeLevel.setLoadUI(m_loadUI);
         m_anyeLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_anyeLevel.setEnterFrameTime(m_enterFrameTime);
         m_anyeLevel.setPlayer1InitPosition(300,200,0);
         m_anyeLevel.setPlayer2InitPosition(250,250,0);
         m_anyeLevel.init();
         m_anyeLevel.initByXMLPath(param1);
         m_anyeLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
         GamingUI.getInstance().addHp(GamingUI.getInstance().player1.playerVO.bloodVolume,GamingUI.getInstance().player1);
         if(Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO))
         {
            GamingUI.getInstance().addHp(GamingUI.getInstance().player2.playerVO.bloodVolume,GamingUI.getInstance().player2);
         }
         GamingUI.getInstance().getAutomaticPetsData().recoverAllAutomaticPetsHp();
      }
      
      public function openLevelNew(param1:String) : void
      {
         Part1.getInstance().setTipXY(250,150);
         LevelNewData.getInstance().setPath(param1);
         m_newLevel = new LevelNewWorld();
         m_gameContentLayer.addChild(m_newLevel);
         m_newLevel.setVersionControl(m_versionControl);
         m_newLevel.setLoadUI(m_loadUI);
         m_newLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_newLevel.setEnterFrameTime(m_enterFrameTime);
         m_newLevel.setPlayer1InitPosition(300,200,0);
         m_newLevel.setPlayer2InitPosition(250,250,0);
         m_newLevel.init();
         m_newLevel.initByXMLPath(param1);
         m_newLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openLevelNew_2(param1:String) : void
      {
         m_newLevel = new LevelNewWorld();
         m_gameContentLayer.addChild(m_newLevel);
         m_newLevel.setVersionControl(m_versionControl);
         m_newLevel.setLoadUI(m_loadUI);
         m_newLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_newLevel.setEnterFrameTime(m_enterFrameTime);
         m_newLevel.setPlayer1InitPosition(300,200,0);
         m_newLevel.setPlayer2InitPosition(250,250,0);
         m_newLevel.init();
         m_newLevel.initByXMLPath(param1);
         m_newLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
         GamingUI.getInstance().addHp(GamingUI.getInstance().player1.playerVO.bloodVolume,GamingUI.getInstance().player1);
         if(Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO))
         {
            GamingUI.getInstance().addHp(GamingUI.getInstance().player2.playerVO.bloodVolume,GamingUI.getInstance().player2);
         }
         GamingUI.getInstance().getAutomaticPetsData().recoverAllAutomaticPetsHp();
      }
      
      public function openEndDancerLevel(param1:String) : void
      {
         Part1.getInstance().setTipXY(250,150);
         LevelNewData.getInstance().setPath(param1);
         m_endDancerLevel = new LevelEndDancerWorld();
         m_gameContentLayer.addChild(m_endDancerLevel);
         m_endDancerLevel.setVersionControl(m_versionControl);
         m_endDancerLevel.setLoadUI(m_loadUI);
         m_endDancerLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_endDancerLevel.setEnterFrameTime(m_enterFrameTime);
         m_endDancerLevel.setPlayer1InitPosition(300,200,0);
         m_endDancerLevel.setPlayer2InitPosition(250,250,0);
         m_endDancerLevel.init();
         m_endDancerLevel.initByXMLPath(param1);
         m_endDancerLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
      }
      
      public function openEndDancerLevel_2(param1:String) : void
      {
         m_endDancerLevel = new LevelEndDancerWorld();
         m_gameContentLayer.addChild(m_endDancerLevel);
         m_endDancerLevel.setVersionControl(m_versionControl);
         m_endDancerLevel.setLoadUI(m_loadUI);
         m_endDancerLevel.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         m_endDancerLevel.setEnterFrameTime(m_enterFrameTime);
         m_endDancerLevel.setPlayer1InitPosition(300,200,0);
         m_endDancerLevel.setPlayer2InitPosition(250,250,0);
         m_endDancerLevel.init();
         m_endDancerLevel.initByXMLPath(param1);
         m_endDancerLevel.addUIShow(m_part2);
         MainGameCall.getInstance().uiTranToFightState();
         stage.focus = this;
         GamingUI.getInstance().addHp(GamingUI.getInstance().player1.playerVO.bloodVolume,GamingUI.getInstance().player1);
         if(Boolean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO))
         {
            GamingUI.getInstance().addHp(GamingUI.getInstance().player2.playerVO.bloodVolume,GamingUI.getInstance().player2);
         }
         GamingUI.getInstance().getAutomaticPetsData().recoverAllAutomaticPetsHp();
      }
      
      public function openPK2(param1:String) : void
      {
         closeCityMap();
         ClearUtil.clearObject(m_pk);
         m_pk = new PK2();
         var _loc2_:PK2 = m_pk as PK2;
         _loc2_.setLoadUI(m_loadUI);
         _loc2_.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         _loc2_.setVersionControl(m_versionControl);
         _loc2_.setEnterFrameTime(m_enterFrameTime);
         _loc2_.init(param1);
         m_gameContentLayer.addChild(_loc2_.getShow());
      }
      
      public function openPK(param1:String) : void
      {
         GetDataFunction.getInstance().bIsPK = true;
         Part1.getInstance().setTipXY(250,10);
         closeCityMap();
         ClearUtil.clearObject(m_pk);
         m_pk = new PK();
         var _loc2_:PK = m_pk as PK;
         _loc2_.setLoadUI(m_loadUI);
         _loc2_.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         _loc2_.setVersionControl(m_versionControl);
         _loc2_.setEnterFrameTime(m_enterFrameTime);
         _loc2_.init(param1);
         m_gameContentLayer.addChild(_loc2_.getShow());
      }
      
      public function closePK() : void
      {
         hideGameWaitShow();
         ClearUtil.clearObject(m_pk);
         m_pk = null;
         Part1.getInstance().setTipXY(250,150);
         GetDataFunction.getInstance().bIsPK = false;
      }
      
      public function openNewRank() : void
      {
         Part1.getInstance().showGameWaitShow();
         closeCityMap();
         ClearUtil.clearObject(m_rankworld);
         m_rankworld = new RankPK();
         var _loc1_:RankPK = m_rankworld as RankPK;
         m_loadUI.tranToOpacity();
         _loc1_.setLoadUI(m_loadUI);
         _loc1_.setMyLoader(m_myLoader);
         m_loadUI.tranToOpacity();
         _loc1_.setVersionControl(m_versionControl);
         _loc1_.setEnterFrameTime(m_enterFrameTime);
         _loc1_.init("");
         m_gameContentLayer.addChild(_loc1_.getShow());
      }
      
      public function closeNewRank() : void
      {
         hideGameWaitShow();
         ClearUtil.clearObject(m_rankworld);
         m_rankworld = null;
      }
      
      public function ClearAllLevel() : void
      {
         EndlessManage.disposeAll();
         closeLevel();
         closeLevel2();
         closePK();
         closeBossWorld();
         closeBossNewWorld();
         closeNewRank();
         closeNewLevel();
      }
      
      public function returnCity() : void
      {
         Part1.getInstance().setTipXY(250,150);
         EndlessManage.disposeAll();
         GamingUI.getInstance().closePreciousView();
         closeLevel();
         closeAnyeLevel();
         closeEndDancerLevel();
         closeLevel2();
         closeNewLevel();
         closePK();
         closeBossWorld();
         closeBossNewWorld();
         closeNewRank();
         closeRouteMap();
         if(m_currentCityMapXMLPath)
         {
            openCityMap(m_currentCityMapXMLPath,null);
         }
         else
         {
            openCityMap("NewGameFolder/CityMap1.xml",null);
         }
         continueGame();
      }
      
      public function openCheatGamePanel() : void
      {
         if(m_cheatGamePanel == null)
         {
            m_cheatGamePanel = new CheatGamePanel();
            m_cheatGamePanel.init();
         }
         m_cheatGameShowLayer.addChild(m_cheatGamePanel);
         completelyStopGame();
      }
      
      public function saveGame() : void
      {
         if(m_storeStateListener == null)
         {
            m_storeStateListener = new SaveAPIListener2();
         }
         else
         {
            saveGame2();
         }
         m_storeStateListener.multipleErrorFun = function():void
         {
            GamingUI.getInstance().lockGamingUI("游戏多开了！");
            GamingUI.getInstance().getAPI4399().saveAPI.removeSaveAPIListener(m_storeStateListener);
            ClearUtil.clearObject(m_storeStateListener);
            m_storeStateListener = null;
         };
         m_storeStateListener.noInMultipStateFun = function():void
         {
            GamingUI.getInstance().getAPI4399().saveAPI.removeSaveAPIListener(m_storeStateListener);
            ClearUtil.clearObject(m_storeStateListener);
            m_storeStateListener = null;
            saveGame2();
         };
         GamingUI.getInstance().getAPI4399().saveAPI.addSaveAPIListener(m_storeStateListener);
         GamingUI.getInstance().getAPI4399().saveAPI.getStoreState();
      }
      
      public function saveGame2() : void
      {
         if(m_bSaving)
         {
            return;
         }
         DetectionClass.getInstance().detectionAllPlayer1(GamingUI.getInstance().player1,GamingUI.getInstance().player2,GamingUI.getInstance().publicStorageEquipmentVOs);
         m_currSaveInfo = SaveTaskList.getInstance().getData();
         if(m_currSaveInfo)
         {
            if(m_currSaveInfo.isHaveData)
            {
               m_bSaving = true;
               isHaveData = m_currSaveInfo.isHaveData;
               GamingUI.getInstance().showMessageTip("开始存档...");
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  var timeStr:String = param1;
                  new SaveGame().saveGame(m_currSaveInfo.data,m_currSaveInfo.saveIndex,function():void
                  {
                     GamingUI.getInstance().showMessageTip("存档成功");
                     if(m_currSaveInfo && m_currSaveInfo.successFunc)
                     {
                        m_currSaveInfo.successFunc();
                     }
                     SaveTaskList.getInstance().desData();
                     m_currSaveInfo = null;
                     m_bSaving = false;
                     isHaveData = false;
                     if(SaveTaskList.getInstance().getSaveCount() > 0)
                     {
                        saveGame2();
                     }
                  },function():void
                  {
                     GamingUI.getInstance().showMessageTip("存档失败");
                     if(m_currSaveInfo && m_currSaveInfo.failFunc)
                     {
                        m_currSaveInfo.failFunc();
                     }
                     SaveTaskList.getInstance().desData();
                     m_currSaveInfo = null;
                     m_bSaving = false;
                     isHaveData = false;
                     if(SaveTaskList.getInstance().getSaveCount() > 0)
                     {
                        saveGame2();
                     }
                  },m_currSaveInfo.title);
                  GameData.getInstance().setLastSaveTime(m_enterFrameTime.getOnLineTimeForThisInit());
               },null,false);
               m_nSaveIndex = 0;
            }
            else
            {
               isHaveData = false;
               m_bSaving = true;
               GamingUI.getInstance().showMessageTip("开始存档...");
               m_xmlSave = <root />;
               Part1.getInstance().xmlData = <Data></Data>;
               GamingUI.getInstance().initListSaveXML();
               m_xmlSave.appendChild(xmlData);
               m_nSaveIndex = 0;
            }
         }
         else
         {
            isHaveData = false;
            m_bSaving = false;
         }
      }
      
      private function saveGameData() : void
      {
         if(m_bInResult)
         {
            return;
         }
         if(m_nSaveIndex >= SaveNewFileData.getInstance().getTotalNum())
         {
            m_bInResult = true;
            saveResult();
            return;
         }
         SaveNewFileData.getInstance().doByIndex(m_nSaveIndex);
         m_nSaveIndex++;
      }
      
      private function sendTime() : void
      {
         if(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - _firstSaveLasttime >= m_const_saveMaxLimitTime)
         {
            GamingUI.getInstance().showMessageTip("存档超时,请重新存档!");
            if(m_gameSystemPanel)
            {
               GamingUI.getInstance().unLockGamingUI();
            }
            SaveTaskList.getInstance().desData();
            m_currSaveInfo = null;
            m_bInResult = false;
            m_bSaving = false;
            isHaveData = false;
            m_bTimeOut = false;
            if(SaveTaskList.getInstance().getSaveCount() > 0)
            {
               saveGame2();
            }
         }
      }
      
      private function saveResult() : void
      {
         GamingUI.getInstance().addAutomaticSave(m_xmlSave);
         _firstSaveLasttime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
         m_bTimeOut = true;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var timeStr:String = param1;
            new SaveGame().saveGame(m_xmlSave,GameData.getInstance().getSaveFileData().index,function():void
            {
               if(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - _firstSaveLasttime >= m_const_saveMaxLimitTime)
               {
                  return;
               }
               m_bTimeOut = false;
               GamingUI.getInstance().showMessageTip("存档成功");
               if(m_currSaveInfo && m_currSaveInfo.successFunc)
               {
                  m_currSaveInfo.successFunc();
               }
               SaveTaskList.getInstance().desData();
               m_currSaveInfo = null;
               m_bInResult = false;
               m_bSaving = false;
               isHaveData = false;
               if(SaveTaskList.getInstance().getSaveCount() > 0)
               {
                  saveGame2();
               }
            },function():void
            {
               if(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - _firstSaveLasttime >= m_const_saveMaxLimitTime)
               {
                  return;
               }
               m_bTimeOut = false;
               GamingUI.getInstance().showMessageTip("存档失败");
               if(m_currSaveInfo && m_currSaveInfo.failFunc)
               {
                  m_currSaveInfo.failFunc();
               }
               SaveTaskList.getInstance().desData();
               m_currSaveInfo = null;
               m_bInResult = false;
               m_bSaving = false;
               isHaveData = false;
               if(SaveTaskList.getInstance().getSaveCount() > 0)
               {
                  saveGame2();
               }
            },GamingUI.getInstance().createNewTitle());
            GameData.getInstance().setLastSaveTime(m_enterFrameTime.getOnLineTimeForThisInit());
         },null,false);
      }
      
      public function closeLevel() : void
      {
         if(m_level)
         {
            m_level.removeUIShow(m_part2);
            ClearUtil.clearObject(m_level);
            m_level = null;
         }
      }
      
      public function closeLevel2() : void
      {
         if(m_level2)
         {
            m_level2.removeUIShow(m_part2);
            ClearUtil.clearObject(m_level2);
            m_level2 = null;
         }
         Part1.getInstance().setTipXY(250,10);
      }
      
      public function closeNewLevel() : void
      {
         if(m_newLevel)
         {
            m_newLevel.removeUIShow(m_part2);
            m_gameContentLayer.removeChild(m_newLevel);
            ClearUtil.clearObject(m_newLevel);
            m_newLevel = null;
         }
         Part1.getInstance().setTipXY(250,10);
      }
      
      public function closeAnyeLevel() : void
      {
         if(m_anyeLevel)
         {
            m_anyeLevel.removeUIShow(m_part2);
            m_gameContentLayer.removeChild(m_anyeLevel);
            ClearUtil.clearObject(m_anyeLevel);
            m_anyeLevel = null;
         }
      }
      
      public function closeEndDancerLevel() : void
      {
         if(m_endDancerLevel)
         {
            m_endDancerLevel.removeUIShow(m_part2);
            m_gameContentLayer.removeChild(m_endDancerLevel);
            ClearUtil.clearObject(m_endDancerLevel);
            m_endDancerLevel = null;
         }
      }
      
      private function closeBossWorld() : void
      {
         if(m_bossWorld)
         {
            m_bossWorld.removeUIShow(m_part2);
            ClearUtil.clearObject(m_bossWorld);
            m_bossWorld = null;
         }
      }
      
      private function closeBossNewWorld() : void
      {
         if(m_bossNewWorld)
         {
            m_bossNewWorld.removeUIShow(m_part2);
            ClearUtil.clearObject(m_bossNewWorld);
            m_bossNewWorld = null;
         }
      }
      
      private function startGame(param1:StartGamePanel, param2:String, param3:String, param4:XML, param5:int, param6:Boolean) : void
      {
         var startGamePanel:StartGamePanel = param1;
         var player1Str:String = param2;
         var player2Str:String = param3;
         var saveXML:XML = param4;
         var saveIndex:int = param5;
         var isFirst:Boolean = param6;
         m_isFirst = isFirst;
         closeStartGamePanel();
         loadStartGameSource(function():void
         {
            MyFunction.getInstance();
            initPart2(player1Str,player2Str,saveXML,init3);
         });
      }
      
      private function deActive(param1:Event) : void
      {
      }
      
      public function resetInitPlayerInCityMap() : void
      {
         GamingUI.getInstance().resetPlayerType();
         m_cityMap.resetInitPlayer();
      }
      
      public function getVersionControl() : VersionControl
      {
         return m_versionControl;
      }
      
      public function getVersion() : uint
      {
         return int(m_version);
      }
      
      public function getLevel() : Level
      {
         return m_level;
      }
      
      public function getBossWorld() : BossWorld
      {
         return m_bossWorld;
      }
      
      public function getPart2() : Sprite
      {
         return m_part2;
      }
      
      public function getSoundManager() : SoundManager2
      {
         return m_soundManager_global;
      }
      
      public function getEnterFrameTime() : EnterFrameTime
      {
         return m_enterFrameTime;
      }
      
      public function savePlayer1CurrentPosition(param1:Number, param2:Number, param3:Number) : void
      {
         m_player1CurrentPosition.setTo(param1,param2,param3);
      }
      
      public function getIsFive() : Boolean
      {
         return m_isFive;
      }
      
      public function setIsFive(param1:Boolean) : void
      {
         m_isFive = param1;
      }
      
      private function logOutSuccess(param1:LogAPI) : void
      {
         reStartGame();
      }
      
      public function get temSaveXml() : XML
      {
         return _antiwear._temSaveXml;
      }
      
      public function set temSaveXml(param1:XML) : void
      {
         _antiwear._temSaveXml = param1;
      }
      
      public function getLevelEndDancerWorld() : LevelEndDancerWorld
      {
         return m_endDancerLevel;
      }
      
      public function getIsInLevel() : Boolean
      {
         if(m_level || m_newLevel || m_level2 || m_bossWorld || m_bossNewWorld || m_anyeLevel || m_endDancerLevel)
         {
            return true;
         }
         return false;
      }
      
      public function getLevelIsGameEnd() : Boolean
      {
         if(m_level)
         {
            return m_level.m_gameIsEnd;
         }
         if(m_level2)
         {
            return m_level2.m_gameIsEnd;
         }
         if(m_bossWorld)
         {
            return m_bossWorld.getIsGameEnd();
         }
         if(m_bossNewWorld)
         {
            return m_bossNewWorld.getIsGameEnd();
         }
         return false;
      }
      
      public function getIsInCityMap() : Boolean
      {
         if(m_cityMap)
         {
            return true;
         }
         return false;
      }
   }
}

