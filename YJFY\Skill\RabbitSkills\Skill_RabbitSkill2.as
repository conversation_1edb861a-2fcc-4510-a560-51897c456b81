package YJFY.Skill.RabbitSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.World.World;
   
   public class Skill_RabbitSkill2 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      public function Skill_RabbitSkill2()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

