package UI.RecaptureGold.RewardPanel
{
   import UI.Buff.BuffData;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.ForceDanEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.RecaptureGold.RewardPanel.Btn.GetRewardBtn;
   import UI.XMLSingle;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class RewardColume extends MySprite
   {
      
      public static const NAME:String = "name";
      
      public static const DESCRIPTION:String = "description";
      
      public static const MEDAL_ID:String = "medalID";
      
      public var nameText:TextField;
      
      public var descriptionText:TextField;
      
      public var getAwardBtn:GetRewardBtn;
      
      private var _dataObject:Object;
      
      private var _isActive:Boolean = false;
      
      private var _medalEquipmentVO:EquipmentVO;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function RewardColume(param1:Object, param2:String, param3:XML)
      {
         super();
         _dataObject = param1;
         init(param1,param2,param3);
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc3_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc3_ = getChildAt(0);
            removeChildAt(0);
            if(_loc3_.hasOwnProperty("clear"))
            {
               _loc3_["clear"]();
            }
         }
         nameText = null;
         descriptionText = null;
         if(getAwardBtn)
         {
            getAwardBtn.clear();
         }
         getAwardBtn = null;
         if(_medalEquipmentVO)
         {
            _medalEquipmentVO.clear();
         }
         _medalEquipmentVO = null;
         for(var _loc1_ in _dataObject)
         {
            _dataObject[_loc1_] = null;
         }
         _dataObject = null;
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function setActivate(param1:Boolean) : void
      {
         _antiwear.isActive = param1;
         if(param1)
         {
            getAwardBtn.mouseChildren = true;
            getAwardBtn.mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(getAwardBtn,0);
         }
         else
         {
            getAwardBtn.mouseChildren = false;
            getAwardBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(getAwardBtn,-100);
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickGetRewardBtn",getAward,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickGetRewardBtn",getAward,true);
      }
      
      private function getAward(param1:Event) : void
      {
         var _loc10_:int = 0;
         var _loc5_:int = 0;
         var _loc9_:* = undefined;
         var _loc3_:EquipmentVO = null;
         var _loc8_:* = undefined;
         if(!_antiwear.isActive)
         {
            return;
         }
         var _loc4_:Player = GamingUI.getInstance().player1;
         var _loc6_:Player = GamingUI.getInstance().player2;
         var _loc2_:Vector.<EquipmentVO> = GamingUI.getInstance().publicStorageEquipmentVOs;
         var _loc7_:Boolean = false;
         if(_loc6_)
         {
            _loc9_ = MyFunction2.concatEquipmentVOVector(_loc4_.playerVO.inforEquipmentVOs,_loc4_.playerVO.packageEquipmentVOs,_loc4_.playerVO.storageEquipmentVOs,_loc6_.playerVO.inforEquipmentVOs,_loc6_.playerVO.packageEquipmentVOs,_loc6_.playerVO.storageEquipmentVOs,_loc4_.playerVO.medalEquipmentVOs,_loc6_.playerVO.medalEquipmentVOs,_loc2_);
         }
         else
         {
            _loc9_ = MyFunction2.concatEquipmentVOVector(_loc4_.playerVO.inforEquipmentVOs,_loc4_.playerVO.packageEquipmentVOs,_loc4_.playerVO.storageEquipmentVOs,_loc4_.playerVO.medalEquipmentVOs,_loc2_);
         }
         if(BuffData.getInstance().isHaveTheFieldBuff("forceDanBuff","buffDrives"))
         {
            _loc7_ = true;
         }
         _loc5_ = int(_loc9_.length);
         _loc10_ = 0;
         while(_loc10_ < _loc5_)
         {
            if(Boolean(_loc9_[_loc10_]) && (_loc9_[_loc10_].id == _dataObject["medalID"] || _loc9_[_loc10_] is ForceDanEquipmentVO))
            {
               _loc7_ = true;
               break;
            }
            _loc10_++;
         }
         if(!_loc7_)
         {
            _loc3_ = XMLSingle.getEquipmentVOByID(_dataObject["medalID"],XMLSingle.getInstance().equipmentXML,null,true);
            _loc8_ = new Vector.<EquipmentVO>();
            _loc8_.push(_loc3_);
            MyFunction2.addEquipmentVOs(_loc8_,_loc4_,(parent as RewardPanel).showWarningBox,(parent as RewardPanel).showWarningBox,["背包已满！",0],["获取成功！",0],2);
            _loc5_ = int(_loc8_.length);
            _loc10_ = 0;
            while(_loc10_ < _loc5_)
            {
               if(_loc8_[_loc10_])
               {
                  _loc8_[_loc10_] = null;
               }
               _loc10_++;
            }
            _loc8_ = null;
         }
         else
         {
            (parent as RewardPanel).showWarningBox("你已经领取过了，目前暂不能再次领取！",0);
         }
      }
      
      private function init(param1:Object, param2:String, param3:XML) : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.isActive = _isActive;
         var _loc5_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         nameText.defaultTextFormat = new TextFormat(_loc5_.fontName,18,16777215);
         descriptionText.defaultTextFormat = new TextFormat(_loc5_.fontName,15,16777215);
         nameText.selectable = false;
         descriptionText.selectable = false;
         nameText.embedFonts = true;
         descriptionText.embedFonts = true;
         descriptionText.multiline = true;
         descriptionText.wordWrap = true;
         nameText.text = param1.name;
         descriptionText.text = param1.description;
         _medalEquipmentVO = XMLSingle.getEquipmentVOByID(param1["medalID"],param3,param2);
         var _loc4_:Equipment = MyFunction2.sheatheEquipmentShell(_medalEquipmentVO);
         _loc4_.x = 30;
         _loc4_.y = 42;
         _loc4_.addEventListener("rollOver",equipmentInfor,false,0,true);
         _loc4_.addEventListener("rollOut",equipmentInfor,false,0,true);
         addChild(_loc4_);
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

