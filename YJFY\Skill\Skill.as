package YJFY.Skill
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.EntityListener;
   import YJFY.Entity.IEntity;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill implements ISkill
   {
      
      protected var m_id:String;
      
      protected var m_owner:AnimalEntity;
      
      protected var m_entityListener:EntityListener;
      
      protected var m_animalEntityFrameLabelListener:AnimationPlayFrameLabelListener;
      
      protected var m_world:World;
      
      protected var m_myLoader:YJFYLoader;
      
      protected var m_skillXML:XML;
      
      protected var m_skillListeners:Vector.<ISkillListener>;
      
      protected var m_isOutWorldTime:Boolean;
      
      protected var m_extra:Object;
      
      public function Skill()
      {
         super();
         m_skillListeners = new Vector.<ISkillListener>();
         m_animalEntityFrameLabelListener = new AnimationPlayFrameLabelListener();
         m_animalEntityFrameLabelListener.reachFrameLabelFun = reachFrameLabel;
         m_entityListener = new EntityListener();
         m_entityListener.setDirectionFun = ownerSetDirection;
      }
      
      public function clear() : void
      {
         if(m_owner)
         {
            m_owner.removeEntityListener(m_entityListener);
         }
         m_id = null;
         m_owner = null;
         ClearUtil.clearObject(m_animalEntityFrameLabelListener);
         m_animalEntityFrameLabelListener = null;
         ClearUtil.clearObject(m_entityListener);
         m_entityListener = null;
         m_world = null;
         m_myLoader = null;
         m_skillXML = null;
         ClearUtil.nullArr(m_skillListeners,false,false,false);
         m_skillListeners = null;
         m_extra = null;
      }
      
      public function setOwner(param1:AnimalEntity) : void
      {
         if(m_owner)
         {
            m_owner.removeEntityListener(m_entityListener);
         }
         m_owner = param1;
         if(m_owner)
         {
            m_owner.addEntityListener(m_entityListener);
            m_owner.addAnimationFrameLabelListener(m_animalEntityFrameLabelListener);
         }
      }
      
      public function setWorld(param1:World) : void
      {
         m_world = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function initByXML(param1:XML) : void
      {
         m_skillXML = param1;
         m_id = String(param1.@id);
      }
      
      public function render(param1:World) : void
      {
      }
      
      public function showRender() : void
      {
      }
      
      public function addSkillListener(param1:ISkillListener) : void
      {
         m_skillListeners.push(param1);
      }
      
      public function removeSkillListener(param1:ISkillListener) : void
      {
         var _loc2_:int = int(m_skillListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_skillListeners.splice(_loc2_,1);
            _loc2_ = int(m_skillListeners.indexOf(param1));
         }
      }
      
      public function getId() : String
      {
         return m_id;
      }
      
      public function getExtra() : Object
      {
         return m_extra;
      }
      
      public function setExtra(param1:Object) : void
      {
         m_extra = param1;
      }
      
      protected function reachFrameLabel(param1:String) : void
      {
      }
      
      protected function ownerSetDirection(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
      }
   }
}

