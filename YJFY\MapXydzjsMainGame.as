package YJFY
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.Players.Player;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.MainGamePartPlayerAndPet.MainGameEntity;
   import YJFY.GameEntity.MainGamePartPlayerAndPet.MainGamePlayer;
   import YJFY.GameEntity.PlayerAndPet.BloadInfo;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dog;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dragon;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Fox;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Houyi;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Monkey;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Rabbit;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.GameEntity.XydzjsPlayerAndPet.ZiXia;
   import YJFY.GameWorldLogic.GameKeyControlLogic_Xydzjs;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   
   public class MapXydzjsMainGame extends Map
   {
      
      protected var m_player1_mainGameEntity:MainGameEntity;
      
      protected var m_player2_mainGameEntity:MainGameEntity;
      
      protected var m_gameKeyControlLogic:GameKeyControlLogic_Xydzjs;
      
      public var vectDownHp:Vector.<BloadInfo>;
      
      public var vectDownHp2:Vector.<BloadInfo>;
      
      public function MapXydzjsMainGame()
      {
         super();
         m_player1_mainGameEntity = new MainGamePlayer();
         m_player2_mainGameEntity = new MainGamePlayer();
         vectDownHp = new Vector.<BloadInfo>();
         vectDownHp2 = new Vector.<BloadInfo>();
         m_gameKeyControlLogic = new GameKeyControlLogic_Xydzjs();
      }
      
      override public function clear() : void
      {
         if(m_player1 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.getAntiwear() && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.get("add_mingzhong"))
         {
            (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.set("add_mingzhong","0");
         }
         if(GamingUI.getInstance().player2 && m_player2 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.getAntiwear() && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.get("add_mingzhong"))
         {
            (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.set("add_mingzhong","0");
         }
         if(m_player1 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.getAntiwear())
         {
            (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.addDown = false;
         }
         if(m_player2 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.getAntiwear())
         {
            (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.addDown = false;
         }
         ClearUtil.clearObject(vectDownHp);
         vectDownHp = null;
         ClearUtil.clearObject(vectDownHp2);
         vectDownHp2 = null;
         ClearUtil.clearObject(m_player1_mainGameEntity);
         m_player1_mainGameEntity = null;
         ClearUtil.clearObject(m_player2_mainGameEntity);
         m_player2_mainGameEntity = null;
         ClearUtil.clearObject(m_gameKeyControlLogic);
         m_gameKeyControlLogic = null;
         super.clear();
      }
      
      override public function init() : void
      {
         super.init();
         m_gameKeyControlLogic.init(m_keyManager,this);
      }
      
      override public function render(param1:EnterFrameTime) : void
      {
         super.render(param1);
         m_player1_mainGameEntity.render();
         m_player2_mainGameEntity.render();
      }
      
      override protected function beAttack(param1:IEntity, param2:AttackData, param3:ISkill, param4:IEntity) : void
      {
         super.beAttack(param1,param2,param3,param4);
         checkHp();
      }
      
      private function checkHp() : void
      {
         var _loc1_:int = 0;
         if(m_player1 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong > 0 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2 > 0)
         {
            _loc1_ = 0;
            while(_loc1_ < vectDownHp.length)
            {
               if((m_player1 as PlayerXydzjs).getCurrentHp() >= vectDownHp[_loc1_].minhp && (m_player1 as PlayerXydzjs).getCurrentHp() < vectDownHp[_loc1_].maxhp && vectDownHp[_loc1_].bShow == false)
               {
                  vectDownHp[_loc1_].bShow = true;
                  (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.set("add_mingzhong",String(vectDownHp[_loc1_].downhp));
               }
               _loc1_++;
            }
         }
         if(GamingUI.getInstance().player2)
         {
            if(m_player2 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong > 0 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2 > 0)
            {
               _loc1_ = 0;
               while(_loc1_ < vectDownHp.length)
               {
                  if((m_player1 as PlayerXydzjs).getCurrentHp() >= vectDownHp[_loc1_].minhp && (m_player1 as PlayerXydzjs).getCurrentHp() < vectDownHp[_loc1_].maxhp && vectDownHp[_loc1_].bShow == false)
                  {
                     vectDownHp[_loc1_].bShow = true;
                     (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.set("add_mingzhong",String(vectDownHp2[_loc1_].downhp));
                  }
                  _loc1_++;
               }
            }
         }
      }
      
      override protected function mapInitSuccess() : void
      {
         super.mapInitSuccess();
         initPlayerOne(GamingUI.getInstance().player1);
         if(GamingUI.getInstance().player2)
         {
            initPlayerOne(GamingUI.getInstance().player2);
         }
      }
      
      public function resetInitPlayer() : void
      {
         ClearUtil.clearObject(m_player1);
         m_player1 = null;
         ClearUtil.clearObject(m_player2);
         m_player2 = null;
         initPlayerOne(GamingUI.getInstance().player1);
         if(GamingUI.getInstance().player2)
         {
            initPlayerOne(GamingUI.getInstance().player2);
         }
      }
      
      private function initPlayerOne(param1:UI.Players.Player) : void
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               m_myLoader.getXML("NewGameFolder/Players/Monkey.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "BaiLongMa":
               m_myLoader.getXML("NewGameFolder/Players/Dragon.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ErLangShen":
               m_myLoader.getXML("NewGameFolder/Players/Dog.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ChangE":
               m_myLoader.getXML("NewGameFolder/Players/Rabbit.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Fox":
               m_myLoader.getXML("NewGameFolder/Players/Fox.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "TieShan":
               m_myLoader.getXML("NewGameFolder/Players/TieShan.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Houyi":
               m_myLoader.getXML("NewGameFolder/Players/Houyi.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ZiXia":
               m_myLoader.getXML("NewGameFolder/Players/ZiXia.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            default:
               throw new Error();
         }
         m_myLoader.load();
      }
      
      private function getPlayerXMLSuccess(param1:YJFYLoaderData, param2:UI.Players.Player) : void
      {
         var _loc3_:YJFY.GameEntity.PlayerAndPet.Player = null;
         if(param1.xmlPath == "NewGameFolder/Players/Monkey.xml")
         {
            _loc3_ = new Monkey();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dragon.xml")
         {
            _loc3_ = new Dragon();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dog.xml")
         {
            _loc3_ = new Dog();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Rabbit.xml")
         {
            _loc3_ = new Rabbit();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Fox.xml")
         {
            _loc3_ = new Fox();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/TieShan.xml")
         {
            _loc3_ = new TieShan();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Houyi.xml")
         {
            _loc3_ = new Houyi();
         }
         else
         {
            if(param1.xmlPath != "NewGameFolder/Players/ZiXia.xml")
            {
               throw new Error();
            }
            _loc3_ = new ZiXia();
         }
         _loc3_.setMyLoader(m_myLoader);
         _loc3_.setEnterFrameTime(m_enterFrameTime);
         _loc3_.setAnimationDefinitionManager(Part1.getInstance().getXydzjSelfAnimationManager());
         _loc3_.setSoundMananger(m_soundManager);
         _loc3_.initByXML(param1.resultXML);
         _loc3_.getAnimalEntity().addEntityListener(m_entityListener);
         _loc3_.getAnimalEntity().addAnimalEntityListener(m_animalEntityListener);
         if(param2 == GamingUI.getInstance().player2)
         {
            m_player2 = _loc3_;
            m_player2.getAnimalEntity().setNewPosition(m_player2InitPosition.getX(),m_player2InitPosition.getY(),m_player2InitPosition.getZ());
            (m_player2 as PlayerXydzjs).setUiPlayer(GamingUI.getInstance().player2);
            m_player2_mainGameEntity.setEntity(m_player2);
            initCompletePlayer(m_player2);
            initBlood(2);
         }
         else
         {
            m_player1 = _loc3_;
            m_player1.getAnimalEntity().setNewPosition(m_player1InitPosition.getX(),m_player1InitPosition.getY(),m_player1InitPosition.getZ());
            (m_player1 as PlayerXydzjs).setUiPlayer(GamingUI.getInstance().player1);
            m_player1_mainGameEntity.setEntity(m_player1);
            initCompletePlayer(m_player1);
            initBlood(1);
         }
         m_world.addEntity(_loc3_.getAnimalEntity());
         m_worldCameraPositionTransfer.setCameraPosition();
      }
      
      protected function initCompletePlayer(param1:YJFY.GameEntity.PlayerAndPet.Player) : void
      {
         if(Boolean(m_player1) && (GamingUI.getInstance().player2 == null || Boolean(m_player2)))
         {
            initCompletePlayers();
         }
      }
      
      public function initBlood(param1:int) : void
      {
         var _loc4_:Number = NaN;
         var _loc3_:int = 0;
         var _loc2_:BloadInfo = null;
         var _loc5_:int = 0;
         vectDownHp.length = 0;
         vectDownHp2.length = 0;
         if(param1 == 1)
         {
            if(m_player1 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong > 0 && (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2 > 0)
            {
               _loc4_ = (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.bloodVolume * (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong * 0.01;
               _loc3_ = (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.bloodVolume / _loc4_;
               _loc5_ = 1;
               while(_loc5_ < _loc3_ - 1)
               {
                  _loc2_ = new BloadInfo();
                  _loc2_.minhp = _loc4_ * _loc5_;
                  _loc2_.maxhp = _loc4_ * (_loc5_ + 1);
                  _loc2_.bShow = false;
                  _loc2_.downhp = (_loc3_ - _loc5_) * (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2;
                  vectDownHp.push(_loc2_);
                  _loc5_++;
               }
            }
            if(m_player1)
            {
               (m_player1 as PlayerXydzjs).getUiPlayer().playerVO.addDown = true;
            }
         }
         else
         {
            if(m_player2 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong > 0 && (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2 > 0)
            {
               _loc4_ = (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.bloodVolume * (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong * 0.01;
               _loc3_ = (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.bloodVolume / _loc4_;
               _loc5_ = 1;
               while(_loc5_ < _loc3_ - 1)
               {
                  _loc2_ = new BloadInfo();
                  _loc2_.minhp = _loc4_ * _loc5_;
                  _loc2_.maxhp = _loc4_ * (_loc5_ + 1);
                  _loc2_.bShow = false;
                  _loc2_.downhp = (_loc3_ - _loc5_) * (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.increaseMingzhong2;
                  vectDownHp2.push(_loc2_);
                  _loc5_++;
               }
            }
            if(m_player2)
            {
               (m_player2 as PlayerXydzjs).getUiPlayer().playerVO.addDown = true;
            }
         }
      }
      
      protected function initCompletePlayers() : void
      {
      }
      
      protected function initAllGameModeSharedSource() : void
      {
      }
      
      public function getGameKeyControlLogic() : GameKeyControlLogic_Xydzjs
      {
         return m_gameKeyControlLogic;
      }
   }
}

