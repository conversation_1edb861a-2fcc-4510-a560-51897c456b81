package YJFY
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.filters.DropShadowFilter;
   import flash.text.*;
   import flash.utils.Timer;
   
   public class MiddleWord extends MovieClip
   {
      
      private var _root:Object;
      
      private var size_arr:Array;
      
      public function MiddleWord(param1:Object, param2:String, param3:Number = 0, param4:Number = 0, param5:Number = 0, param6:Number = 0, param7:Number = 3, param8:int = 100)
      {
         var tf2:TextField;
         var format:TextFormat;
         var myDropFilter:DropShadowFilter;
         var myFilters2:Array;
         var tt:Timer;
         var outRoot:Object = param1;
         var txt:String = param2;
         var wX:Number = param3;
         var wY:Number = param4;
         var tX:Number = param5;
         var tY:Number = param6;
         var wTime:Number = param7;
         var money:int = param8;
         var removeWord:* = function(param1:Event = null):void
         {
            TweenLite.to(tf2,wTime / 2,{
               "x":tX,
               "y":tY,
               "alpha":0,
               "ease":Back.easeIn,
               "onComplete":endFunc
            });
         };
         var endFunc:* = function():void
         {
            _root.removeChild(tf2);
         };
         size_arr = new Array(101);
         super();
         size_arr[1] = 15;
         size_arr[5] = 18;
         size_arr[10] = 21;
         size_arr[20] = 24;
         size_arr[50] = 27;
         size_arr[100] = 50;
         _root = outRoot;
         tf2 = new TextField();
         tf2.mouseEnabled = false;
         tf2.type = "dynamic";
         format = new TextFormat();
         format.font = "黑体";
         format.color = 16711680;
         format.size = size_arr[money];
         format.bold = true;
         tf2.defaultTextFormat = format;
         tf2.htmlText = txt;
         tf2.width = tf2.textWidth + 10;
         tf2.height = tf2.textHeight + 10;
         tf2.x = wX = _root.stageWidth / 2 - tf2.width / 2 + 25;
         tf2.y = wY = _root.stageHeight / 2 - tf2.height / 2;
         tX = wX;
         tY = wY + 2 * tf2.height;
         myDropFilter = new DropShadowFilter(1,45,0,1,1,1,100,1,false,false);
         myDropFilter.blurX = myDropFilter.blurY = 10;
         myDropFilter.strength = 10;
         myFilters2 = new Array(myDropFilter);
         tf2.filters = myFilters2;
         _root.addChild(tf2);
         TweenLite.to(tf2,wTime / 2,{
            "x":tX,
            "y":wY - 2 * tf2.height,
            "alpha":1,
            "ease":Back.easeOut
         });
         tt = new Timer(wTime / 2,1);
         tt.addEventListener("timer",removeWord);
         tt.start();
      }
   }
}

