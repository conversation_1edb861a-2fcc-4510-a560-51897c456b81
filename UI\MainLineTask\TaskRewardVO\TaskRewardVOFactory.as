package UI.MainLineTask.TaskRewardVO
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class TaskRewardVOFactory
   {
      
      private var _classes:Array;
      
      public function TaskRewardVOFactory()
      {
         super();
         _classes = [TaskRewardVO,EquipmentRewardVO,ExperienceRewardVO,MoneyRewardVO,ChoiceEquipmentRewardVO];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_classes);
         _classes = null;
      }
      
      public function createTaskRewardVOByClassName(param1:String) : TaskRewardVO
      {
         var _loc3_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc3_();
      }
      
      public function createTaskRewardVOByXML(param1:XML) : TaskRewardVO
      {
         var _loc3_:String = String(param1.@className);
         var _loc2_:TaskRewardVO = createTaskRewardVOByClassName(_loc3_);
         _loc2_.initByXML(param1);
         return _loc2_;
      }
      
      public function createTaskRewardVOsByXML(param1:XML) : Vector.<TaskRewardVO>
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:TaskRewardVO = null;
         var _loc5_:XMLList = param1.taskReward;
         _loc4_ = int(_loc5_ ? _loc5_.length() : 0);
         var _loc3_:Vector.<TaskRewardVO> = new Vector.<TaskRewardVO>();
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc2_ = createTaskRewardVOByXML(_loc5_[_loc6_]);
            _loc3_.push(_loc2_);
            _loc6_++;
         }
         return _loc3_;
      }
   }
}

