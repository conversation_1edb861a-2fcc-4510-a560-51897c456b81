package YJFY.Utils
{
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ShowUtil
   {
      
      public function ShowUtil()
      {
         super();
      }
      
      public function changeTextFieldFont(param1:String, param2:TextField, param3:Boolean = true) : void
      {
         if(param1 == null)
         {
            param3 = false;
         }
         if(param2 == null)
         {
            return;
         }
         var _loc4_:TextFormat = param2.defaultTextFormat;
         _loc4_.font = param1;
         param2.defaultTextFormat = _loc4_;
         param2.embedFonts = param3;
      }
   }
}

