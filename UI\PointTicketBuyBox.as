package UI
{
   import UI.Button.SureAndCancelBtn;
   import UI.ConsumptionGuide.Button.GuideRechargeBtn;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.ShopWall.CurrentTicketPointManager;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class PointTicketBuyBox extends MySprite
   {
      
      protected var _sureBtn:SureAndCancelBtn;
      
      protected var _cancelBtn:SureAndCancelBtn;
      
      protected var _rechargeBtn:GuideRechargeBtn;
      
      protected var _pointTicketText:TextField;
      
      protected var _mengBan:Sprite;
      
      public function PointTicketBuyBox()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _mengBan = null;
         if(_sureBtn)
         {
            _sureBtn.clear();
         }
         _sureBtn = null;
         if(_cancelBtn)
         {
            _cancelBtn.clear();
         }
         _cancelBtn = null;
         if(_rechargeBtn)
         {
            _rechargeBtn.clear();
         }
         _rechargeBtn = null;
         _pointTicketText = null;
      }
      
      protected function init() : void
      {
         _mengBan = new Sprite();
         _mengBan.graphics.lineStyle(1,16777215,0);
         _mengBan.graphics.beginFill(16777215,0);
         _mengBan.graphics.drawRect(0,0,width,height);
         _mengBan.graphics.endFill();
         _mengBan.x = 0;
         _mengBan.y = 0;
         addChild(_mengBan);
         _sureBtn = new SureAndCancelBtn();
         _cancelBtn = new SureAndCancelBtn();
         _sureBtn.x = 220;
         _cancelBtn.x = 300;
         _sureBtn.y = 50;
         _cancelBtn.y = 50;
         addChild(_sureBtn);
         addChild(_cancelBtn);
         _sureBtn.showText("确定");
         _cancelBtn.showText("取消");
         _mengBan.buttonMode = true;
         _rechargeBtn = new GuideRechargeBtn();
         _rechargeBtn.x = 140;
         _rechargeBtn.y = _sureBtn.y - 5;
         addChild(_rechargeBtn);
         _pointTicketText = new TextField();
         _pointTicketText.x = 10;
         _pointTicketText.y = _rechargeBtn.y + 10;
         _pointTicketText.selectable = false;
         _pointTicketText.embedFonts = true;
         addChild(_pointTicketText);
         _pointTicketText.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'12\' color=\'#ff0000\' > 现有点券: </font>" + "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#ffffff\'>" + CurrentTicketPointManager.getInstance().getCurrentTicketPoint().toString() + "</font>";
         _pointTicketText.width = _pointTicketText.textWidth + 10;
         _pointTicketText.height = _pointTicketText.textHeight + 5;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickSureAndCancelBtn",clickBtn,true,0,true);
         _mengBan.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _mengBan.addEventListener("mouseUp",mouseUpBox,false,0,true);
         _rechargeBtn.addEventListener("click",recharge,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("clickSureAndCancelBtn",clickBtn,true);
         removeEventListener("removedFromStage",removeFromStage,false);
         _mengBan.removeEventListener("mouseDown",mouseDownBox,false);
         _mengBan.removeEventListener("mouseUp",mouseUpBox,false);
         _rechargeBtn.removeEventListener("click",recharge,false);
      }
      
      protected function recharge(param1:MouseEvent) : void
      {
         var _loc2_:* = null;
         AnalogServiceHoldFunction.getInstance().payMoney_As3();
         parent.removeChild(this);
         clear();
      }
      
      protected function clickBtn(param1:UIBtnEvent) : void
      {
         parent.removeChild(this);
         clear();
      }
      
      protected function mouseDownBox(param1:MouseEvent) : void
      {
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      protected function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      protected function test(param1:Event) : void
      {
         var _loc2_:Point = localToGlobal(new Point(mouseX,mouseY));
         if(_loc2_.x > stage.stageWidth)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            x -= 10;
         }
         if(_loc2_.x < 0)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            x += 10;
         }
         if(_loc2_.y > stage.stageHeight)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            y -= 10;
         }
         if(_loc2_.y < 0)
         {
            _mengBan.dispatchEvent(new MouseEvent("mouseUp"));
            y += 10;
         }
      }
   }
}

