package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   import flash.events.IEventDispatcher;
   
   public interface IAnimationShow extends IEventDispatcher
   {
      
      function get currentFrame() : int;
      
      function get currentFrameLabel() : String;
      
      function get totalFrames() : int;
      
      function gotoAndPlay(param1:Object, param2:String = null) : void;
      
      function gotoAndStop(param1:Object, param2:String = null) : void;
      
      function nextFrame() : void;
      
      function stop() : void;
   }
}

