package YJFY.Skill.DogSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneBullet;
   import YJFY.World.World;
   
   public class Skill_DogSkill4 extends CuboidAreaAttackSkill_OneBullet
   {
      
      public function Skill_DogSkill4()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

