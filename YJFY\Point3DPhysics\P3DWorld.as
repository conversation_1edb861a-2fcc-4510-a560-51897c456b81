package YJFY.Point3DPhysics
{
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.ClearUtil;
   
   public class P3DWorld
   {
      
      private var m_allBodys:Vector.<P3DBody>;
      
      private var m_gravity:P3DVector3D;
      
      private var m_doSleep:Boolean;
      
      private var m_bodyBeforePositionInRender:P3DVector3D;
      
      private var m_bodyBeforeVelocityInRender:P3DVector3D;
      
      private var m_groundContactListeners:Vector.<IGroundContactListener>;
      
      private var m_i:int;
      
      private var m_length:int;
      
      private var m_middleVec:P3DVector3D;
      
      private var m_upUnitVec:P3DVector3D;
      
      private var m_middleGroundContactListeners:Vector.<IGroundContactListener>;
      
      public function P3DWorld(param1:P3DVector3D, param2:Boolean)
      {
         super();
         m_allBodys = new Vector.<P3DBody>();
         m_gravity = new P3DVector3D();
         m_gravity.copy(param1);
         m_doSleep = param2;
         m_bodyBeforePositionInRender = new P3DVector3D();
         m_bodyBeforeVelocityInRender = new P3DVector3D();
         m_groundContactListeners = new Vector.<IGroundContactListener>();
         m_middleVec = new P3DVector3D();
         m_upUnitVec = new P3DVector3D(0,0,1);
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_allBodys,false,false,false);
         m_allBodys = null;
         ClearUtil.clearObject(m_gravity);
         m_gravity = null;
         ClearUtil.clearObject(m_bodyBeforePositionInRender);
         m_bodyBeforePositionInRender = null;
         ClearUtil.clearObject(m_bodyBeforeVelocityInRender);
         m_bodyBeforeVelocityInRender = null;
         ClearUtil.nullArr(m_groundContactListeners,false,false,false);
         m_groundContactListeners = null;
         ClearUtil.clearObject(m_middleVec);
         m_middleVec = null;
         ClearUtil.clearObject(m_upUnitVec);
         m_upUnitVec = null;
      }
      
      public function render(param1:IP3DTime) : void
      {
         var _loc3_:P3DBody = null;
         var _loc5_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc2_:GroundContact = null;
         if(m_doSleep)
         {
            return;
         }
         m_length = m_allBodys.length;
         m_i = 0;
         while(m_i < m_length)
         {
            _loc3_ = m_allBodys[m_i];
            m_bodyBeforePositionInRender.copy(_loc3_.getPosition());
            m_bodyBeforeVelocityInRender.copy(_loc3_.getVelocity());
            if(_loc3_.getIsOutGravity() == false)
            {
               _loc3_.applyForce(m_gravity.multi(_loc3_.getMass()));
            }
            if(m_bodyBeforePositionInRender.getZ() < 1 && (_loc3_.getVelocityX() != 0 || _loc3_.getVelocityY() != 0))
            {
               if(_loc3_.getFriction() == 1)
               {
                  m_middleVec.setTo(0,0,0);
               }
               else if(_loc3_.getFriction() == 0)
               {
                  m_middleVec.setTo(_loc3_.getVelocityX(),_loc3_.getVelocityY(),0);
               }
               else
               {
                  m_middleVec.setTo(_loc3_.getVelocityX(),_loc3_.getVelocityY(),0);
                  m_middleVec.normalize();
                  m_middleVec.negate();
                  m_middleVec.multi2(m_gravity.getLength() * _loc3_.getFriction() * param1.getAddTimeOneFrame() / 1000,m_middleVec);
                  if(_loc3_.getVelocityX() > 0)
                  {
                     _loc5_ = Math.max(_loc3_.getVelocityX() + m_middleVec.getX(),0);
                  }
                  else
                  {
                     _loc5_ = Math.min(_loc3_.getVelocityX() + m_middleVec.getX(),0);
                  }
                  if(_loc3_.getVelocityY() > 0)
                  {
                     _loc4_ = Math.max(_loc3_.getVelocityY() + m_middleVec.getY(),0);
                  }
                  else
                  {
                     _loc4_ = Math.min(_loc3_.getVelocityY() + m_middleVec.getY(),0);
                  }
                  m_middleVec.setTo(_loc5_,_loc4_,_loc3_.getVelocityZ());
               }
               m_bodyBeforeVelocityInRender.copy(m_middleVec);
            }
            _loc3_.render(param1);
            if(m_bodyBeforePositionInRender.getZ() >= 0 && _loc3_.getPositionZ() < 0)
            {
               m_middleVec.setTo(_loc3_.getPositionX(),_loc3_.getPositionY(),0);
               _loc3_.setNewPosition(m_middleVec);
               _loc3_.setVelocity2(m_bodyBeforeVelocityInRender.getX(),m_bodyBeforeVelocityInRender.getY(),-m_bodyBeforeVelocityInRender.getZ() * _loc3_.getRestitution());
               _loc2_ = new GroundContact(_loc3_,m_middleVec,m_upUnitVec,(_loc3_.getVelocityZ() - m_bodyBeforeVelocityInRender.getZ()) * _loc3_.getMass());
               occurGroundContact(_loc2_);
            }
            ++m_i;
         }
      }
      
      public function addBody(param1:P3DBody) : void
      {
         m_allBodys.push(param1);
         param1.setWorld(this);
      }
      
      public function removeBody(param1:P3DBody) : void
      {
         var _loc2_:int = int(m_allBodys.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_allBodys.splice(_loc2_,1);
            param1.setWorld(null);
            _loc2_ = int(m_allBodys.indexOf(param1));
         }
      }
      
      public function setGravity(param1:P3DVector3D) : void
      {
         m_gravity.copy(param1);
      }
      
      public function doSleep(param1:Boolean) : void
      {
         m_doSleep = param1;
      }
      
      public function getBodyNum() : int
      {
         return m_allBodys.length;
      }
      
      public function getBodyByIndex(param1:int) : P3DBody
      {
         return m_allBodys[param1];
      }
      
      public function addGroundContactListener(param1:IGroundContactListener) : void
      {
         m_groundContactListeners.push(param1);
      }
      
      public function removeGroundContactListener(param1:IGroundContactListener) : void
      {
         var _loc2_:int = int(m_groundContactListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_groundContactListeners.splice(_loc2_,1);
            _loc2_ = int(m_groundContactListeners.indexOf(param1));
         }
      }
      
      private function occurGroundContact(param1:GroundContact) : void
      {
         var _loc4_:int = 0;
         var _loc2_:Vector.<IGroundContactListener> = m_groundContactListeners.slice(0);
         var _loc3_:int = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_].contact(param1);
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_ = null;
      }
   }
}

