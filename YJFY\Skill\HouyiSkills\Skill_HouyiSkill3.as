package YJFY.Skill.HouyiSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.geom.Area3D.Cuboid;
   
   public class Skill_HouyiSkill3 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private var m_attackReach_1:String = "skillAttackReach1";
      
      private var m_attackReach_2:String = "skillAttackReach2";
      
      private var m_attackReach_3:String = "skillAttackReach3";
      
      private var m_attackReach_4:String = "skillAttackReach4";
      
      private var m_attackReach_5:String = "skillAttackReach5";
      
      protected var m_cuboidRange1:Cuboid;
      
      protected var m_cuboidRange2:Cuboid;
      
      protected var m_cuboidRange3:Cuboid;
      
      protected var m_cuboidRange4:Cuboid;
      
      protected var m_cuboidRange5:Cuboid;
      
      public function Skill_HouyiSkill3()
      {
         super();
         m_cuboidRange1 = new Cuboid();
         m_cuboidRange2 = new Cuboid();
         m_cuboidRange3 = new Cuboid();
         m_cuboidRange4 = new Cuboid();
         m_cuboidRange5 = new Cuboid();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_cuboidRange1);
         m_cuboidRange1 = null;
         ClearUtil.clearObject(m_cuboidRange2);
         m_cuboidRange2 = null;
         ClearUtil.clearObject(m_cuboidRange3);
         m_cuboidRange3 = null;
         ClearUtil.clearObject(m_cuboidRange4);
         m_cuboidRange4 = null;
         ClearUtil.clearObject(m_cuboidRange5);
         m_cuboidRange5 = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_cuboidRange1.setTo(param1.attackCuboid1[0].@x,param1.attackCuboid1[0].@y,param1.attackCuboid1[0].@z,param1.attackCuboid1[0].@xRange,param1.attackCuboid1[0].@yRange,param1.attackCuboid1[0].@zRange);
         m_cuboidRange2.setTo(param1.attackCuboid2[0].@x,param1.attackCuboid2[0].@y,param1.attackCuboid2[0].@z,param1.attackCuboid2[0].@xRange,param1.attackCuboid2[0].@yRange,param1.attackCuboid2[0].@zRange);
         m_cuboidRange3.setTo(param1.attackCuboid3[0].@x,param1.attackCuboid3[0].@y,param1.attackCuboid3[0].@z,param1.attackCuboid3[0].@xRange,param1.attackCuboid3[0].@yRange,param1.attackCuboid3[0].@zRange);
         m_cuboidRange4.setTo(param1.attackCuboid4[0].@x,param1.attackCuboid4[0].@y,param1.attackCuboid4[0].@z,param1.attackCuboid4[0].@xRange,param1.attackCuboid4[0].@yRange,param1.attackCuboid4[0].@zRange);
         m_cuboidRange5.setTo(param1.attackCuboid5[0].@x,param1.attackCuboid5[0].@y,param1.attackCuboid5[0].@z,param1.attackCuboid5[0].@xRange,param1.attackCuboid5[0].@yRange,param1.attackCuboid5[0].@zRange);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function reachFrameLabel2(param1:String) : void
      {
         switch(param1)
         {
            case m_releaseFrameLabel:
               m_owner.currentAnimationStop();
               playSkillAnimation();
               break;
            case m_releaseFrameLabel2:
               m_owner.currentAnimationPlay();
               break;
            case m_attackReach_1:
               m_cuboidRange.copy(m_cuboidRange1);
               attackReach(m_world);
               break;
            case m_attackReach_2:
               m_cuboidRange.copy(m_cuboidRange2);
               attackReach(m_world);
               break;
            case m_attackReach_3:
               m_cuboidRange.copy(m_cuboidRange3);
               attackReach(m_world);
               break;
            case m_attackReach_4:
               m_cuboidRange.copy(m_cuboidRange4);
               attackReach(m_world);
               break;
            case m_attackReach_5:
               m_cuboidRange.copy(m_cuboidRange5);
               attackReach(m_world);
               break;
            case m_skillEndEndFrameLabel:
               endSkill2();
         }
      }
   }
}

