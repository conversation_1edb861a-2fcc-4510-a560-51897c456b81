package YJFY.Utils
{
   public class CloseSubUI
   {
      
      private var m_closeFun:Function;
      
      public function CloseSubUI()
      {
         super();
      }
      
      public function clear() : void
      {
         m_closeFun = null;
      }
      
      public function init(param1:Function) : void
      {
         m_closeFun = param1;
      }
      
      public function close() : void
      {
         if(<PERSON><PERSON>an(m_closeFun))
         {
            m_closeFun();
         }
      }
   }
}

