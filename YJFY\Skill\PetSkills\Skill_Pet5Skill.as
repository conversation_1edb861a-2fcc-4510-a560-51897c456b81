package YJFY.Skill.PetSkills
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.EntityLogic.StopAndContinueEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_EveryTargetAddShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   
   public class Skill_Pet5Skill extends CuboidAreaAttackSkill2_EveryTargetAddShow
   {
      
      private var m_fronzeFrameLabel:String;
      
      private var m_fronzeShowId:String;
      
      private var m_fronzeShowDefinitionData:AnimationDefinitionData;
      
      protected var m_usefronzeShowEntitys:Vector.<AnimationEntityLogicShell>;
      
      protected var m_wastefronzeShowEntitys:Vector.<AnimationEntityLogicShell>;
      
      protected var m_fronzeShowEntitysPool:ObjectsPool;
      
      private var m_stopAndContinueEntity:StopAndContinueEntity;
      
      private var m_getAllEnityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_isAddFronzeShow:Boolean = true;
      
      private var m_pet5SkillListeners:Vector.<IPet5SkillListener>;
      
      private var m_fronzeEnemeys:Vector.<IEntity>;
      
      public function Skill_Pet5Skill()
      {
         super();
         m_usefronzeShowEntitys = new Vector.<AnimationEntityLogicShell>();
         m_wastefronzeShowEntitys = new Vector.<AnimationEntityLogicShell>();
         m_fronzeShowEntitysPool = new ObjectsPool(m_usefronzeShowEntitys,m_wastefronzeShowEntitys,createFronzeShow,null);
         m_fronzeEnemeys = new Vector.<IEntity>();
         m_pet5SkillListeners = new Vector.<IPet5SkillListener>();
         m_stopAndContinueEntity = new StopAndContinueEntity();
         m_getAllEnityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEnityAndRunFun.fun = addFronzeShow;
      }
      
      override public function clear() : void
      {
         clearWasteFronzeShowEntitys();
         clearUseFronzeShowEntitys();
         m_fronzeFrameLabel = null;
         m_fronzeShowId = null;
         ClearUtil.clearObject(m_fronzeShowDefinitionData);
         m_fronzeShowDefinitionData = null;
         ClearUtil.clearObject(m_usefronzeShowEntitys);
         m_usefronzeShowEntitys = null;
         ClearUtil.clearObject(m_wastefronzeShowEntitys);
         m_wastefronzeShowEntitys = null;
         ClearUtil.clearObject(m_fronzeShowEntitysPool);
         m_fronzeShowEntitysPool = null;
         ClearUtil.nullArr(m_pet5SkillListeners,false,false,false);
         m_pet5SkillListeners = null;
         ClearUtil.nullArr(m_fronzeEnemeys,false,false,false);
         m_fronzeEnemeys = null;
         ClearUtil.clearObject(m_stopAndContinueEntity);
         m_stopAndContinueEntity = null;
         ClearUtil.clearObject(m_getAllEnityAndRunFun);
         m_getAllEnityAndRunFun = null;
         super.clear();
      }
      
      public function addPet5SkillListener(param1:IPet5SkillListener) : void
      {
         m_pet5SkillListeners.push(param1);
      }
      
      public function removePet5SkillListener(param1:IPet5SkillListener) : void
      {
         var _loc2_:int = int(m_pet5SkillListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_pet5SkillListeners.splice(_loc2_,1);
            _loc2_ = int(m_pet5SkillListeners.indexOf(param1));
         }
      }
      
      public function setIsAddFronzeShow(param1:Boolean) : void
      {
         m_isAddFronzeShow = param1;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_fronzeFrameLabel = String(param1.@fronzeFrameLabel);
         m_fronzeShowId = String(param1.@fronzeShowId);
         m_fronzeShowDefinitionData = new AnimationDefinitionData();
         m_fronzeShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_fronzeShowId)[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function endSkill2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = int(m_fronzeEnemeys.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_fronzeEnemeys[_loc2_].showBodyShow();
            m_stopAndContinueEntity.continueEntity(m_fronzeEnemeys[_loc2_]);
            _loc2_++;
         }
         m_fronzeEnemeys.length = 0;
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         if(m_isRun == false)
         {
            return;
         }
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case m_fronzeFrameLabel:
               addAllFronzeShow();
               break;
            case m_bodyAttackReachFrameLabel:
               removeAllFronzeShow();
         }
      }
      
      private function removeAllFronzeShow() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_usefronzeShowEntitys.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_usefronzeShowEntitys[_loc2_].getExtra())
            {
               (m_usefronzeShowEntitys[_loc2_].getExtra() as IEntity).showBodyShow();
               m_usefronzeShowEntitys[_loc2_].setExtra(null);
            }
            m_world.removeEntity(m_usefronzeShowEntitys[_loc2_]);
            m_fronzeShowEntitysPool.wasteOneObj(m_usefronzeShowEntitys[_loc2_]);
            _loc2_--;
            _loc1_--;
            _loc2_++;
         }
      }
      
      protected function addAllFronzeShow() : void
      {
         getCuboidRangeToWorld();
         m_getAllEnityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      private function addFronzeShow(param1:IEntity) : void
      {
         addFronzeShow2(param1);
         if(m_isAddFronzeShow == false)
         {
            return;
         }
         m_fronzeEnemeys.push(param1);
         m_stopAndContinueEntity.stopEntity(param1);
         var _loc2_:AnimationEntityLogicShell = m_fronzeShowEntitysPool.getOneOrCreateOneObj() as AnimationEntityLogicShell;
         _loc2_.init(param1.getX(),param1.getY() - 10,0);
         _loc2_.setExtra(param1);
         m_world.addEntity(_loc2_);
         _loc2_.getAniamtionShowPlay().gotoAndPlay("1");
      }
      
      private function addFronzeShow2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<IPet5SkillListener> = m_pet5SkillListeners.slice(0);
         var _loc2_:int = int(_loc3_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_[_loc4_].addFronzeShow(this,param1);
            _loc3_[_loc4_] = null;
            _loc4_++;
         }
         _loc3_.length = 0;
         _loc3_ = null;
      }
      
      protected function createFronzeShow() : AnimationEntityLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_fronzeShowDefinitionData));
         var _loc1_:AnimationEntityLogicShell = new AnimationEntityLogicShell();
         _loc1_.setShow(_loc2_);
         return _loc1_;
      }
      
      protected function clearWasteFronzeShowEntitys() : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         var _loc1_:int = m_wastefronzeShowEntitys ? m_wastefronzeShowEntitys.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_wastefronzeShowEntitys[0];
            m_wastefronzeShowEntitys.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_.getAnimationShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_wastefronzeShowEntitys = null;
      }
      
      protected function clearUseFronzeShowEntitys() : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         var _loc1_:int = m_usefronzeShowEntitys ? m_usefronzeShowEntitys.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_usefronzeShowEntitys[0];
            m_usefronzeShowEntitys.splice(0,1);
            _loc1_--;
            _loc2_.getAniamtionShowPlay().extra = null;
            if(m_world)
            {
               m_world.removeEntity(_loc2_);
            }
            ClearUtil.clearObject(_loc2_.getAnimationShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_usefronzeShowEntitys = null;
      }
   }
}

