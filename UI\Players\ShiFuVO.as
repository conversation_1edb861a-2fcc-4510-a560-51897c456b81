package UI.Players
{
   import UI.DataManagerParent;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class ShiFuVO extends DataManagerParent
   {
      
      private var m_usedXiuLianNum:uint;
      
      private var m_xiuLianTime:String;
      
      private var m_timeUtil:TimeUtil;
      
      public function ShiFuVO()
      {
         super();
         m_timeUtil = new TimeUtil();
         usedXiuLianNum = 0;
         xiuLianTime = "";
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_timeUtil);
         m_timeUtil = null;
         super.clear();
      }
      
      public function initBySaveXML(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         usedXiuLianNum = uint(param1.@xLUsedNum);
         xiuLianTime = String(param1.@xLDate);
      }
      
      public function exportSaveXML(param1:XML) : void
      {
         param1.@xLUsedNum = usedXiuLianNum;
         param1.@xLDate = xiuLianTime;
      }
      
      public function updateData(param1:String) : void
      {
         if(m_timeUtil.newDateIsNewDay(xiuLianTime,param1))
         {
            xiuLianTime = param1;
            usedXiuLianNum = 0;
         }
      }
      
      public function getUsedXiuLianNum() : uint
      {
         return usedXiuLianNum;
      }
      
      public function addOneUsedXiuLianNum() : void
      {
         usedXiuLianNum += ProgramStartData.getInstance().get_one();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.usedXiuLianNum = m_usedXiuLianNum;
         _antiwear.xiuLianTime = m_xiuLianTime;
      }
      
      private function get usedXiuLianNum() : uint
      {
         return _antiwear.usedXiuLianNum;
      }
      
      private function set usedXiuLianNum(param1:uint) : void
      {
         _antiwear.usedXiuLianNum = param1;
      }
      
      private function get xiuLianTime() : String
      {
         return _antiwear.xiuLianTime;
      }
      
      private function set xiuLianTime(param1:String) : void
      {
         _antiwear.xiuLianTime = param1;
      }
   }
}

