package YJFY.Skill
{
   import YJFY.BossMode.Boss1.<PERSON><PERSON>a<PERSON>hao1;
   import YJFY.BossMode.Boss1.<PERSON><PERSON>aZhao2;
   import YJFY.BossMode.Boss1.BossDaZhao3;
   import YJFY.BossMode.Boss1.Boss<PERSON>aZhao4;
   import YJFY.BossMode.Boss1.BossDecSpeedDrop;
   import YJFY.BossMode.Boss1.BossGenSuiTarget;
   import YJFY.BossMode.Boss1.BossNaZhaKunBang;
   import YJFY.BossMode.Boss1.BossPushUpSkill;
   import YJFY.BossMode.Boss1.BossThorSkill;
   import YJFY.BossMode.Boss1.Skill_BossBWCZhengPing;
   import YJFY.BossMode.Boss1.Skill_BossBackSupper;
   import YJFY.BossMode.Boss1.Skill_BossChangeSupper;
   import YJFY.BossMode.Boss1.Skill_BossRecoverHpSkill;
   import YJFY.BossMode.Boss1.Skill_BossSkill1;
   import YJFY.BossMode.Boss1.Skill_BossTeleport;
   import YJFY.BossMode.Boss1.Skill_BossThrowWeapon;
   import YJFY.BossMode.Boss1.Skill_PushSkill;
   import YJFY.LevelMode1.Level39.Skill2_Castellan;
   import YJFY.LevelMode2.Levels1.Skill_AllResurgence;
   import YJFY.LevelMode2.Levels1.Skill_DisabilityDragonSkill1;
   import YJFY.LevelMode2.Levels1.Skill_DisabilityDragonSkill2;
   import YJFY.LevelMode2.Levels1.Skill_DragonKingSkill1;
   import YJFY.LevelMode2.Levels1.Skill_DragonKingSkill2;
   import YJFY.LevelMode2.Levels1.Skill_JiaoRenSkill;
   import YJFY.Skill.BossSkills.Skill_BossInvincibleSkill;
   import YJFY.Skill.BossSkills.Skill_NanTaSkill;
   import YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao;
   import YJFY.Skill.DogSkills.Skill_DogSkill1;
   import YJFY.Skill.DogSkills.Skill_DogSkill2;
   import YJFY.Skill.DogSkills.Skill_DogSkill3;
   import YJFY.Skill.DogSkills.Skill_DogSkill4;
   import YJFY.Skill.DogSkills.Skill_DogSkill5;
   import YJFY.Skill.DragonSkills.Skill_DragonSkill1;
   import YJFY.Skill.DragonSkills.Skill_DragonSkill2;
   import YJFY.Skill.DragonSkills.Skill_DragonSkill3;
   import YJFY.Skill.DragonSkills.Skill_DragonSkill4;
   import YJFY.Skill.DragonSkills.Skill_DragonSkill5;
   import YJFY.Skill.FoxSkills.Skill_FoxSkill1;
   import YJFY.Skill.FoxSkills.Skill_FoxSkill2;
   import YJFY.Skill.FoxSkills.Skill_FoxSkill3;
   import YJFY.Skill.FoxSkills.Skill_FoxSkill4;
   import YJFY.Skill.FoxSkills.Skill_FoxSkill5;
   import YJFY.Skill.HouyiSkills.Skill_HouyiSkill1;
   import YJFY.Skill.HouyiSkills.Skill_HouyiSkill2;
   import YJFY.Skill.HouyiSkills.Skill_HouyiSkill3;
   import YJFY.Skill.HouyiSkills.Skill_HouyiSkill4;
   import YJFY.Skill.HouyiSkills.Skill_HouyiSkill5;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill1;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill2;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill3;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill4;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill5;
   import YJFY.Skill.PetSkills.Skill_Pet0Skill;
   import YJFY.Skill.PetSkills.Skill_Pet10Skill;
   import YJFY.Skill.PetSkills.Skill_Pet11Skill;
   import YJFY.Skill.PetSkills.Skill_Pet12Skill;
   import YJFY.Skill.PetSkills.Skill_Pet13Skill;
   import YJFY.Skill.PetSkills.Skill_Pet14Skill;
   import YJFY.Skill.PetSkills.Skill_Pet15Skill;
   import YJFY.Skill.PetSkills.Skill_Pet16Skill;
   import YJFY.Skill.PetSkills.Skill_Pet17Skill;
   import YJFY.Skill.PetSkills.Skill_Pet18Skill;
   import YJFY.Skill.PetSkills.Skill_Pet19Skill;
   import YJFY.Skill.PetSkills.Skill_Pet1Skill;
   import YJFY.Skill.PetSkills.Skill_Pet20NoQuanPingSkill;
   import YJFY.Skill.PetSkills.Skill_Pet20Skill;
   import YJFY.Skill.PetSkills.Skill_Pet21Skill;
   import YJFY.Skill.PetSkills.Skill_Pet22Skill;
   import YJFY.Skill.PetSkills.Skill_Pet23Skill;
   import YJFY.Skill.PetSkills.Skill_Pet2Skill;
   import YJFY.Skill.PetSkills.Skill_Pet3Skill;
   import YJFY.Skill.PetSkills.Skill_Pet4Skill;
   import YJFY.Skill.PetSkills.Skill_Pet5Skill;
   import YJFY.Skill.PetSkills.Skill_Pet6Skill;
   import YJFY.Skill.PetSkills.Skill_Pet7Skill;
   import YJFY.Skill.PetSkills.Skill_Pet8Skill;
   import YJFY.Skill.PetSkills.Skill_Pet9Skill;
   import YJFY.Skill.RabbitSkills.Skill_RabbitSkill1;
   import YJFY.Skill.RabbitSkills.Skill_RabbitSkill2;
   import YJFY.Skill.RabbitSkills.Skill_RabbitSkill3;
   import YJFY.Skill.RabbitSkills.Skill_RabbitSkill4;
   import YJFY.Skill.RabbitSkills.Skill_RabbitSkill5;
   import YJFY.Skill.TieShanSkills.Skill_TieShanDaZhao;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkill1;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkillBianShen;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkillFeng;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkillFeng2;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkillHuo1;
   import YJFY.Skill.TieShanSkills.Skill_TieShanSkillHuo2;
   import YJFY.Skill.ZiXiaSkill.Skill_ZiXiaSkill1;
   import YJFY.Skill.ZiXiaSkill.Skill_ZiXiaSkill2;
   import YJFY.Skill.ZiXiaSkill.Skill_ZiXiaSkill3;
   import YJFY.Skill.ZiXiaSkill.Skill_ZiXiaSkill4;
   import YJFY.Skill.ZiXiaSkill.Skill_ZiXiaSkill5;
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class SkillFactory
   {
      
      private var m_classes:Array;
      
      public function SkillFactory()
      {
         super();
         m_classes = [Skill_MonkeySkill1,Skill_MonkeySkill2,Skill_MonkeySkill3,Skill_MonkeySkill4,Skill_MonkeySkill5,Skill_DragonSkill1,Skill_DragonSkill2,Skill_DragonSkill3,Skill_DragonSkill4,Skill_DragonSkill5,Skill_DogSkill1,Skill_DogSkill2,Skill_DogSkill3,Skill_DogSkill4,Skill_DogSkill5,Skill_RabbitSkill1,Skill_RabbitSkill2,Skill_RabbitSkill3,Skill_RabbitSkill4,Skill_RabbitSkill5,Skill_FoxSkill1,Skill_FoxSkill2,Skill_FoxSkill3,Skill_FoxSkill4,Skill_FoxSkill5,Skill_TieShanSkill1,Skill_TieShanSkillBianShen,Skill_TieShanSkillFeng,Skill_TieShanSkillHuo1,Skill_TieShanSkillFeng2,Skill_TieShanSkillHuo2,Skill_TieShanDaZhao,Skill_Pet0Skill,Skill_Pet1Skill,Skill_Pet2Skill,Skill_Pet3Skill,Skill_Pet4Skill,Skill_Pet5Skill,Skill_Pet6Skill,Skill_Pet7Skill,Skill_Pet8Skill,Skill_Pet9Skill,Skill_Pet10Skill,Skill_Pet11Skill,Skill_Pet12Skill,Skill_Pet13Skill,Skill_Pet14Skill,Skill_Pet15Skill,Skill_Pet16Skill,Skill_Pet17Skill,Skill_Pet18Skill,Skill_Pet19Skill,Skill_Pet20Skill,Skill_Pet21Skill,Skill_BossInvincibleSkill
         ,Skill_Pet20NoQuanPingSkill,Skill_Pet22Skill,Skill_Pet23Skill,BossThorSkill,BossPushUpSkill,Skill_BossSkill1,Skill_BossTeleport,BossGenSuiTarget,Skill_BossChangeSupper,Skill_BossBackSupper,BossDaZhao1,BossDaZhao2,BossDaZhao3,BossDecSpeedDrop,BossNaZhaKunBang,BossDaZhao4,Skill_BossRecoverHpSkill,Skill_NanTaSkill,Skill_ZengZhangDaZhao,Skill_PushSkill,Skill_DragonKingSkill1,Skill_DragonKingSkill2,Skill_AllResurgence,Skill_JiaoRenSkill,Skill_DisabilityDragonSkill2,Skill_BossThrowWeapon,Skill_BossBWCZhengPing,Skill_DisabilityDragonSkill1,Skill_HouyiSkill1,Skill_HouyiSkill2,Skill_HouyiSkill3,Skill_HouyiSkill4,Skill_HouyiSkill5,Skill_ZiXiaSkill1,Skill_ZiXiaSkill2,Skill_ZiXiaSkill3,Skill_ZiXiaSkill4,Skill_ZiXiaSkill5,Skill2_Castellan];
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_classes);
         m_classes = null;
      }
      
      public function createByClassName(param1:String) : Skill
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
   }
}

