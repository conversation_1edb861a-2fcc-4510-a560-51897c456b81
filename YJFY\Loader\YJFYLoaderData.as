package YJFY.Loader
{
   import YJFY.Utils.ClearUtil;
   import com.greensock.loading.core.LoaderCore;
   
   public class YJFYLoaderData
   {
      
      private var _loadType:String;
      
      private var _swfPath:String;
      
      private var _xmlPath:String;
      
      private var _wantClassName:String;
      
      private var _resultXML:XML;
      
      private var _resultClass:Class;
      
      private var _getSuccessFun:Function;
      
      private var _getSuccessFunParams:Array;
      
      private var _getFailFun:Function;
      
      private var _getFailFunParams:Array;
      
      private var _loaderCore:LoaderCore;
      
      private var _progressShow:IProgressShow;
      
      private var _isAutoDispose:Boolean = false;
      
      private var _extra:Object;
      
      public function YJFYLoaderData()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(_resultXML);
         _resultXML = null;
         ClearUtil.clearObject(_resultClass);
         _resultClass = null;
         _getSuccessFun = null;
         ClearUtil.nullArr(_getFailFunParams,false,false,false);
         _getFailFunParams = null;
         _getFailFun = null;
         ClearUtil.nullArr(_getSuccessFunParams,false,false,false);
         _getSuccessFunParams = null;
         _loaderCore = null;
         _progressShow = null;
         _isAutoDispose = false;
         ClearUtil.nullObject(_extra);
         _extra = null;
      }
      
      public function get loadType() : String
      {
         return _loadType;
      }
      
      public function set loadType(param1:String) : void
      {
         _loadType = param1;
      }
      
      public function get xmlPath() : String
      {
         return _xmlPath;
      }
      
      public function set xmlPath(param1:String) : void
      {
         _xmlPath = param1;
      }
      
      public function get swfPath() : String
      {
         return _swfPath;
      }
      
      public function set swfPath(param1:String) : void
      {
         _swfPath = param1;
      }
      
      public function get wantClassName() : String
      {
         return _wantClassName;
      }
      
      public function set wantClassName(param1:String) : void
      {
         _wantClassName = param1;
      }
      
      public function get resultXML() : XML
      {
         return _resultXML;
      }
      
      public function set resultXML(param1:XML) : void
      {
         _resultXML = param1;
      }
      
      public function get resultClass() : Class
      {
         return _resultClass;
      }
      
      public function set resultClass(param1:Class) : void
      {
         _resultClass = param1;
      }
      
      public function get getSuccessFun() : Function
      {
         return _getSuccessFun;
      }
      
      public function set getSuccessFun(param1:Function) : void
      {
         _getSuccessFun = param1;
      }
      
      public function get getSuccessFunParams() : Array
      {
         return _getSuccessFunParams;
      }
      
      public function set getSuccessFunParams(param1:Array) : void
      {
         _getSuccessFunParams = param1;
      }
      
      public function get getFailFun() : Function
      {
         return _getFailFun;
      }
      
      public function set getFailFun(param1:Function) : void
      {
         _getFailFun = param1;
      }
      
      public function get getFailFunParams() : Array
      {
         return _getFailFunParams;
      }
      
      public function set getFailFunParams(param1:Array) : void
      {
         _getFailFunParams = param1;
      }
      
      public function get loaderCore() : LoaderCore
      {
         return _loaderCore;
      }
      
      public function set loaderCore(param1:LoaderCore) : void
      {
         _loaderCore = param1;
      }
      
      public function get progressShow() : IProgressShow
      {
         return _progressShow;
      }
      
      public function set progressShow(param1:IProgressShow) : void
      {
         _progressShow = param1;
      }
      
      public function get extra() : Object
      {
         return _extra;
      }
      
      public function get isAutoDispose() : Boolean
      {
         return _isAutoDispose;
      }
      
      public function set isAutoDispose(param1:Boolean) : void
      {
         _isAutoDispose = param1;
      }
      
      public function set extra(param1:Object) : void
      {
         _extra = param1;
      }
   }
}

