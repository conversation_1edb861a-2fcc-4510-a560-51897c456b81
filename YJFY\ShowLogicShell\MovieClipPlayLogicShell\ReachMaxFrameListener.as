package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   public class ReachMaxFrameListener implements IReachMaxFrameListener
   {
      
      public var reachMaxFrameFun:Function;
      
      public function ReachMaxFrameListener()
      {
         super();
      }
      
      public function clear() : void
      {
         reachMaxFrameFun = null;
      }
      
      public function reachMaxFrame(param1:AnimationShowPlayLogicShell) : void
      {
         if(Boolean(reachMaxFrameFun))
         {
            reachMaxFrameFun(param1);
         }
      }
   }
}

