package UI.WorldBoss.Boss.AbleAttackedBoss
{
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.BossUpgradeListener;
   
   public class AttackUpgradeBoss extends AbleAttackedBoss
   {
      
      public function AttackUpgradeBoss()
      {
         super();
      }
      
      override protected function runAfterAttack(param1:AnimationData) : void
      {
         var _loc2_:BossUpgradeListener = null;
         super.runAfterAttack(param1);
         if(m_level < m_maxLevel)
         {
            setLevel(m_level + 1);
            _loc2_ = new BossUpgradeListener();
            _loc2_.animationData = param1;
            _loc2_.level = m_level;
            _loc2_.mc = m_mc;
            param1.addPlayEndListener(_loc2_);
         }
      }
   }
}

