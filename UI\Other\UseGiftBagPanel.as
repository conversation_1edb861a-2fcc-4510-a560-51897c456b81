package UI.Other
{
   import UI.AbleDragSprite;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class UseGiftBagPanel extends AbleDragSprite
   {
      
      private var _show:MovieClip;
      
      private var _getBtn:ButtonLogicShell2;
      
      private var _quitBtn:ButtonLogicShell2;
      
      private var _funAfterChange:Function;
      
      private var _funParamsAfterChange:Array;
      
      private var _funAfterQuit:Function;
      
      private var _funParamsAfterQuit:Array;
      
      private var _giftBagEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _player:Player;
      
      private var _equipmentContainer:Sprite;
      
      private var _wantLoadSources:Array = ["useGiftBagPanel"];
      
      public function UseGiftBagPanel()
      {
         super();
      }
      
      override public function clear() : void
      {
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(_equipmentContainer);
         _equipmentContainer = null;
         _show = null;
         if(_getBtn)
         {
            _getBtn.clear();
         }
         _getBtn = null;
         if(_quitBtn)
         {
            _quitBtn.clear();
         }
         _quitBtn = null;
         _funAfterChange = null;
         ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
         _funParamsAfterChange = null;
         _funAfterQuit = null;
         ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
         _funParamsAfterQuit = null;
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         _player = null;
         ClearUtil.nullArr(_giftBagEquipmentVOs);
         _giftBagEquipmentVOs = null;
      }
      
      public function init2(param1:XML, param2:Player, param3:Function, param4:Array, param5:Function, param6:Array) : void
      {
         var loadFinishListener:LoadFinishListener1;
         var giftBagXML:XML = param1;
         var player:Player = param2;
         var funAfterChange:Function = param3;
         var funParamsAfterChange:Array = param4;
         var funAfterQuit:Function = param5;
         var funParamsAfterQuit:Array = param6;
         ClearUtil.nullArr(_giftBagEquipmentVOs);
         _giftBagEquipmentVOs = XMLSingle.getEquipmentVOs(giftBagXML,XMLSingle.getInstance().equipmentXML,true);
         _player = player;
         _funAfterChange = null;
         ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
         _funAfterQuit = null;
         ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
         _funAfterChange = funAfterChange;
         _funParamsAfterChange = funParamsAfterChange;
         _funAfterQuit = funAfterQuit;
         _funParamsAfterQuit = funParamsAfterQuit;
         loadFinishListener = new LoadFinishListener1(function():void
         {
            if(_show == null)
            {
               _show = MyFunction2.returnShowByClassName("UseGiftBagPanel") as MovieClip;
            }
            if(_show.parent == null)
            {
               addChild(_show);
            }
            if(stage)
            {
               x = (stage.stageWidth - _show.width) / 2;
               y = (stage.stageHeight - _show.height) / 2;
            }
            if(_getBtn == null)
            {
               _getBtn = new ButtonLogicShell2();
               _getBtn.setShow(_show["getBtn"]);
               _getBtn.setTipString("点击获取");
            }
            if(_quitBtn == null)
            {
               _quitBtn = new ButtonLogicShell2();
               _quitBtn.setShow(_show["quitBtn3"]);
               _quitBtn.setTipString("点击关闭");
            }
            arrangeGiftBag();
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      public function get giftBagEquipments() : Vector.<EquipmentVO>
      {
         return _giftBagEquipmentVOs;
      }
      
      private function arrangeGiftBag() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Equipment = null;
         if(_equipmentContainer == null)
         {
            _equipmentContainer = new Sprite();
         }
         if(_equipmentContainer.parent == null)
         {
            addChild(_equipmentContainer);
         }
         var _loc1_:int = _giftBagEquipmentVOs ? _giftBagEquipmentVOs.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(_giftBagEquipmentVOs[_loc3_]);
            _loc2_.x = 22 + _loc3_ % 8 * 50;
            _loc2_.y = 22 + int(_loc3_ / 8) * 50;
            _loc2_.addEventListener("rollOver",onOver,false,0,true);
            _loc2_.addEventListener("rollOut",onOut,false,0,true);
            _equipmentContainer.addChild(_loc2_);
            _loc3_++;
         }
         _equipmentContainer.x = (width - _equipmentContainer.width) / 2;
         _equipmentContainer.y = (height - _equipmentContainer.height) / 2;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _getBtn:
            case _quitBtn:
               switch(param1.button)
               {
                  case _getBtn:
                     if(Boolean(_funAfterChange))
                     {
                        _funAfterChange.apply(null,_funParamsAfterChange);
                     }
                     _funAfterChange = null;
                     ClearUtil.nullArr(_funParamsAfterChange,false,false,false);
                     break;
                  case _quitBtn:
                     if(Boolean(_funAfterQuit))
                     {
                        _funAfterQuit.apply(null,_funParamsAfterQuit);
                     }
                     _funParamsAfterQuit = null;
                     ClearUtil.nullArr(_funParamsAfterQuit,false,false,false);
               }
               if(parent)
               {
                  parent.removeChild(this);
               }
               clear();
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

