package UI.Event
{
   import flash.events.Event;
   
   public class NewLevelEvent extends Event
   {
      
      public static const TIMEDOWN:String = "timedown";
      
      public static const GUANYIN:String = "guanyin";
      
      public static const RESTART:String = "restart";
      
      public static const ADDENEMY:String = "addenemy";
      
      public static const REFRESH:String = "refresh";
      
      public static const ADDBYTIDENG:String = "addtideng";
      
      public static const HIDEHP:String = "hidehp";
      
      public static const SHOWHP:String = "showhp";
      
      public static const TIMEDOWN2:String = "timedown2";
      
      public static const RESTART2:String = "restart2";
      
      public static const TIMEDOWN3:String = "timedown3";
      
      public static const RESTART3:String = "restart3";
      
      public static const ADDENEMY3:String = "addenemy3";
      
      public var data:*;
      
      public function NewLevelEvent(param1:String, param2:* = null, param3:Boolean = false, param4:Boolean = false)
      {
         this.data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new NewLevelEvent(type,data,bubbles,cancelable);
      }
   }
}

