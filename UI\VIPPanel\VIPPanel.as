package UI.VIPPanel
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.QuitBtn;
   import UI.EquipmentCellBackground;
   import UI.Equipments.Equipment;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Number.NumberContainer;
   import UI.Players.VipVO;
   import UI.Privilege.Privilege;
   import UI.TextTrace.traceText;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class VIPPanel extends MySprite
   {
      
      public var getGiftBtn:GetGiftBtn;
      
      public var rechargeVipBtn:RechargeVIPBtn;
      
      public var quitBtn:QuitBtn;
      
      private var _vipVO:VipVO;
      
      private var _nextVipVO:VipVO;
      
      private var _vipMaxLevel:int;
      
      private var _newDataGetGift:String;
      
      private var _vipProgressBarGrounp:VIPProgressBarGroup;
      
      private var _vipNumContainer:NumberContainer;
      
      private var _VIPOrCommonBitmap:Bitmap;
      
      public function VIPPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function refreshVIPPanel(param1:VipVO) : void
      {
         var _loc3_:DisplayObject = null;
         var _loc2_:int = 0;
         _vipVO = param1;
         if(_vipVO.vipLevel < _vipMaxLevel)
         {
            _nextVipVO = XMLSingle.getVipVOByLevel(param1.vipLevel + 1);
         }
         else
         {
            _nextVipVO = null;
         }
         if(getChildByName(_vipNumContainer.name))
         {
            removeChild(_vipNumContainer);
         }
         if(param1.vipLevel)
         {
            _vipNumContainer.initNumber(param1.vipLevel,"vipLevel",10);
            addChild(_vipNumContainer);
            _VIPOrCommonBitmap.bitmapData = new VIPBitmapData();
            _VIPOrCommonBitmap.x = 180;
            _VIPOrCommonBitmap.y = 110;
         }
         else
         {
            _VIPOrCommonBitmap.bitmapData = new CommonPlayerBitmapData();
            _VIPOrCommonBitmap.x = 200;
            _VIPOrCommonBitmap.y = 150;
         }
         MyFunction2.getServerTimeFunction(dealWithData,showWarningBox,true);
         _vipProgressBarGrounp.setVIPProgress(setVIPProgressNum());
         _loc2_ = 0;
         while(_loc2_ < numChildren)
         {
            if(getChildAt(_loc2_) is EquipmentCellBackground || getChildAt(_loc2_) is Equipment || getChildAt(_loc2_) is Privilege)
            {
               if(getChildAt(_loc2_) is Equipment)
               {
                  getChildAt(_loc2_).removeEventListener("rollOver",equipmentInfor,false);
                  getChildAt(_loc2_).removeEventListener("rollOut",equipmentInfor,false);
               }
               _loc3_ = getChildAt(_loc2_);
               if(!(_loc3_ is Equipment) && !(_loc3_ is Privilege) && _loc3_.hasOwnProperty("clear"))
               {
                  _loc3_["clear"]();
               }
               removeChildAt(_loc2_);
               _loc2_--;
            }
            _loc2_++;
         }
         arrangePrivilege(_vipVO,55,348,10);
         arrangePrivilege(_nextVipVO,495,348,10);
         arrangeGifts(_vipVO,75,480);
         arrangeGifts(_nextVipVO,520,480);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(!(_loc1_ is Equipment) && !(_loc1_ is Privilege) && _loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(getGiftBtn)
         {
            getGiftBtn.clear();
         }
         getGiftBtn = null;
         if(rechargeVipBtn)
         {
            rechargeVipBtn.clear();
         }
         rechargeVipBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _vipVO = null;
         if(_nextVipVO)
         {
            _nextVipVO.clear();
         }
         _nextVipVO = null;
         _newDataGetGift = null;
         if(_vipProgressBarGrounp)
         {
            _vipProgressBarGrounp.clear();
         }
         _vipProgressBarGrounp.clear();
         _vipProgressBarGrounp = null;
         if(_vipNumContainer)
         {
            _vipNumContainer.clear();
         }
         _vipNumContainer = null;
         if(_VIPOrCommonBitmap)
         {
            if(_VIPOrCommonBitmap.bitmapData)
            {
               _VIPOrCommonBitmap.bitmapData.dispose();
            }
            _VIPOrCommonBitmap.bitmapData = null;
         }
         _VIPOrCommonBitmap = null;
      }
      
      private function dealWithData(param1:String) : void
      {
         traceText("服务器时间",param1);
         _newDataGetGift = MyFunction.getInstance().splitTimeString(param1);
         if(Boolean(param1) && Boolean(param1.length) && Boolean(getGiftBtn) && _newDataGetGift != _vipVO.oldDateGetGift && _vipVO.vipLevel != 0)
         {
            getGiftBtn.mouseChildren = true;
            getGiftBtn.mouseEnabled = true;
            MyFunction.getInstance().changeSaturation(getGiftBtn,0);
         }
         else if(getGiftBtn)
         {
            getGiftBtn.mouseChildren = false;
            getGiftBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(getGiftBtn,-100);
         }
      }
      
      private function setVIPProgressNum() : Number
      {
         var _loc1_:int = _vipVO.vipLevel;
         if(_loc1_ < _vipMaxLevel)
         {
            return _loc1_ + (_vipVO.totalPointTicketValue - XMLSingle.getPointTicketValueByVipLevel(_loc1_)) / (XMLSingle.getPointTicketValueByVipLevel(_loc1_ + 1) - XMLSingle.getPointTicketValueByVipLevel(_loc1_));
         }
         return _loc1_;
      }
      
      private function arrangePrivilege(param1:VipVO, param2:Number, param3:Number, param4:int) : void
      {
         var _loc5_:TextField = null;
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:Privilege = null;
         if(param1)
         {
            if(param1.vipLevel == _vipMaxLevel)
            {
               _loc5_ = new TextField();
               _loc5_.selectable = false;
               _loc5_.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16711680);
               _loc5_.embedFonts = true;
               _loc5_.text = "待开放！";
               _loc5_.x = param2;
               _loc5_.y = param3;
               addChild(_loc5_);
               return;
            }
            _loc8_ = 0;
            _loc7_ = int(param1.privilegeVOs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(param1.privilegeVOs[_loc8_])
               {
                  _loc6_ = new Privilege(param1.privilegeVOs[_loc8_]);
                  _loc6_.x = param2 + (_loc6_.width + 5) * (_loc8_ % param4);
                  _loc6_.y = param3 + (_loc6_.height + 5) * (int(_loc8_ / param4));
                  addChild(_loc6_);
               }
               _loc8_++;
            }
         }
      }
      
      private function arrangeGifts(param1:VipVO, param2:Number, param3:Number) : void
      {
         var _loc4_:TextField = null;
         var _loc6_:int = 0;
         var _loc7_:Equipment = null;
         var _loc8_:int = 0;
         var _loc5_:EquipmentCellBackground = null;
         if(param1)
         {
            if(param1.vipLevel == _vipMaxLevel)
            {
               _loc4_ = new TextField();
               _loc4_.selectable = false;
               _loc4_.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16711680);
               _loc4_.embedFonts = true;
               _loc4_.text = "待开放！";
               _loc4_.x = param2 - 20;
               _loc4_.y = param3 - 10;
               addChild(_loc4_);
               return;
            }
            _loc6_ = int(param1.giftBagVOs.length);
            _loc8_ = 0;
            while(_loc8_ < _loc6_)
            {
               _loc5_ = new EquipmentCellBackground();
               _loc5_.x = param2 + 50 * _loc8_;
               _loc5_.y = param3;
               addChild(_loc5_);
               _loc7_ = MyFunction2.sheatheEquipmentShell(param1.giftBagVOs[_loc8_]);
               _loc7_.x = _loc5_.x;
               _loc7_.y = _loc5_.y;
               _loc7_.addEventListener("rollOver",equipmentInfor,false,0,true);
               _loc7_.addEventListener("rollOut",equipmentInfor,false,0,true);
               addChild(_loc7_);
               _loc8_++;
            }
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function init() : void
      {
         _vipMaxLevel = XMLSingle.getVipMaxLevel();
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _vipProgressBarGrounp = new VIPProgressBarGroup();
         _vipProgressBarGrounp.init(0.01,0.02,770,_vipMaxLevel);
         _vipProgressBarGrounp.x = 85;
         _vipProgressBarGrounp.y = 230;
         addChild(_vipProgressBarGrounp);
         _vipNumContainer = new NumberContainer();
         _vipNumContainer.x = 295;
         _vipNumContainer.y = 140;
         _VIPOrCommonBitmap = new Bitmap();
         addChild(_VIPOrCommonBitmap);
         getGiftBtn.mouseChildren = false;
         getGiftBtn.mouseEnabled = false;
         MyFunction.getInstance().changeSaturation(getGiftBtn,-100);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickGetGiftBtn",getGift,true,0,true);
         addEventListener("clickviprechargeBtn",goRecharge,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickGetGiftBtn",getGift,true);
         removeEventListener("clickviprechargeBtn",goRecharge,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function getGift(param1:UIBtnEvent) : void
      {
         if(_vipVO.oldDateGetGift != _newDataGetGift)
         {
            dispatchEvent(new UIPassiveEvent("addGifts",{
               "successFunction":getedGift,
               "successFunParams":[_newDataGetGift],
               "failFunction":showWarningBox,
               "failFunParams":["背包空间不足，放不下礼包！",0],
               "giftBags":GamingUI.getInstance().player1.vipVO.giftBagVOs
            }));
         }
      }
      
      private function goRecharge(param1:UIBtnEvent) : void
      {
         AnalogServiceHoldFunction.getInstance().payMoney_As3();
      }
      
      private function getedGift(param1:String) : void
      {
         _vipVO.oldDateGetGift = param1;
         if(getGiftBtn)
         {
            getGiftBtn.mouseChildren = false;
            getGiftBtn.mouseEnabled = false;
            MyFunction.getInstance().changeSaturation(getGiftBtn,-100);
            showWarningBox("礼包领取成功！",0);
         }
      }
      
      private function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

