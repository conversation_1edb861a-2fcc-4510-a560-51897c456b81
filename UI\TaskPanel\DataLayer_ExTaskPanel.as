package UI.TaskPanel
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.filters.DropShadowFilter;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class DataLayer_ExTaskPanel extends MySprite
   {
      
      public var titleText1:TextField;
      
      public var titleText2:TextField;
      
      public var taskDescriptionText:TextField;
      
      public var resultText:TextField;
      
      private var _equipmentLayer:Sprite;
      
      public function DataLayer_ExTaskPanel()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         _equipmentLayer = null;
         titleText1 = null;
         titleText2 = null;
         taskDescriptionText = null;
         resultText = null;
      }
      
      public function refreshDataLayer(param1:String, param2:String, param3:Vector.<EquipmentVO>, param4:Vector.<TextField>) : void
      {
         var _loc9_:DisplayObject = null;
         var _loc6_:Equipment = null;
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         if(param1 == "")
         {
            titleText1.text = "";
         }
         else
         {
            titleText1.text = "任务描述";
         }
         taskDescriptionText.text = param1;
         taskDescriptionText.x = titleText1.x;
         taskDescriptionText.y = titleText1.y + titleText1.height;
         taskDescriptionText.height = taskDescriptionText.textHeight + 5;
         resultText.text = param2;
         resultText.x = taskDescriptionText.x;
         resultText.y = taskDescriptionText.y + taskDescriptionText.height;
         resultText.height = resultText.textHeight + 5;
         titleText2.y = resultText.y + resultText.height;
         if((!Boolean(param3) || !param3.length) && (!Boolean(param4) || !param4.length))
         {
            titleText2.text = "";
         }
         else
         {
            titleText2.text = "任务奖励";
         }
         while(numChildren > 4)
         {
            _loc9_ = getChildAt(4);
            if(_loc9_.hasOwnProperty("clear"))
            {
               _loc9_["clear"]();
            }
            removeChildAt(4);
         }
         var _loc8_:int = 0;
         if(param3)
         {
            _loc5_ = int(param3.length);
            _loc8_ = 0;
            while(_loc8_ < _loc5_)
            {
               _loc6_ = MyFunction2.sheatheEquipmentShell(param3[_loc8_]);
               _loc6_.filters = [new DropShadowFilter(5,45,0,1,5,5,1)];
               _loc6_.x = 100 + _loc8_ * _loc6_.width;
               _loc6_.y = titleText2.y + titleText2.height + _loc6_.height / 2;
               _loc6_.addEventListener("rollOver",equipmentInfor,false,0,true);
               _loc6_.addEventListener("rollOut",equipmentInfor,false,0,true);
               addChild(_loc6_);
               _loc8_++;
            }
         }
         if(param4)
         {
            _loc5_ = int(param4.length);
            _loc8_ = 0;
            while(_loc8_ < _loc5_)
            {
               _loc7_ = int(param3.length);
               if(!_loc8_ && _loc7_)
               {
                  param4[_loc8_].x = titleText1.x;
                  param4[_loc8_].y = _loc6_.y + _loc6_.height + _loc8_ * param4[_loc8_].height;
                  addChild(param4[_loc8_] as TextField);
               }
               if(_loc8_)
               {
                  param4[_loc8_].x = param4[_loc8_ - 1].x;
                  param4[_loc8_].y = param4[_loc8_ - 1].y + param4[_loc8_ - 1].height;
                  addChild(param4[_loc8_] as TextField);
               }
               _loc8_++;
            }
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function init() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         titleText1.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText2.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText1.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText2.filters = [new GlowFilter(0,1,2,2,10,3)];
         taskDescriptionText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16766337);
         taskDescriptionText.filters = [new GlowFilter(0,1,2,2,10,3)];
         resultText.defaultTextFormat = new TextFormat(_loc1_.fontName,18,52224);
         resultText.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText1.embedFonts = true;
         titleText2.embedFonts = true;
         taskDescriptionText.embedFonts = true;
         resultText.embedFonts = true;
         titleText1.selectable = false;
         titleText2.selectable = false;
         taskDescriptionText.selectable = false;
         resultText.selectable = false;
         taskDescriptionText.mouseWheelEnabled = false;
         resultText.mouseWheelEnabled = false;
         taskDescriptionText.width = this.width - 25;
         resultText.width = this.width - 25;
         _equipmentLayer = new Sprite();
         addChild(_equipmentLayer);
      }
   }
}

