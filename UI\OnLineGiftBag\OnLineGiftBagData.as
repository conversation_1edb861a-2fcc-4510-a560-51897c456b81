package UI.OnLineGiftBag
{
   import UI.DataManagerParent;
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class OnLineGiftBagData extends DataManagerParent
   {
      
      private static var _instance:OnLineGiftBagData;
      
      private var _remainTime:int;
      
      private var _currentGiftBagId:String;
      
      private var _addGetRecycleNumTime:Number;
      
      private var _getRecyleNum:uint;
      
      private var _dayTimeStr:String;
      
      private var _lastGameTime:Number;
      
      private var _onLineGiftBagXML:XML;
      
      private var m_remainTime_vertify:uint;
      
      private var m_servertime_vertify:String;
      
      private var m_timeUtil:TimeUtil;
      
      public function OnLineGiftBagData()
      {
         super();
         if(_instance == null)
         {
            _dayTimeStr = "";
            m_timeUtil = new TimeUtil();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : OnLineGiftBagData
      {
         if(_instance == null)
         {
            _instance = new OnLineGiftBagData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         currentGiftBagId = null;
         _instance = null;
         ClearUtil.clearObject(m_timeUtil);
         m_timeUtil = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.remainTime = _remainTime;
         _antiwear.currentGiftBagId = _currentGiftBagId;
         _antiwear.dayTimeStr = _dayTimeStr;
         _antiwear.getRecyleNum = _getRecyleNum;
         _antiwear.onLineGiftBagXML = _onLineGiftBagXML;
         _antiwear.addGetRecycleNumTime = _addGetRecycleNumTime = 0;
         _antiwear.remainTime_vertify = m_remainTime_vertify;
         _antiwear.servertime_vertify = m_servertime_vertify;
      }
      
      public function getDayTimeStr() : String
      {
         return _antiwear.dayTimeStr;
      }
      
      public function setDayTimeStr(param1:String) : void
      {
         _antiwear.dayTimeStr = param1;
      }
      
      public function get remainTime() : int
      {
         return _antiwear.remainTime;
      }
      
      public function set remainTime(param1:int) : void
      {
         if(param1 < 0)
         {
            param1 = 0;
         }
         _antiwear.remainTime = param1;
      }
      
      public function get currentGiftBagId() : String
      {
         return _antiwear.currentGiftBagId;
      }
      
      public function set currentGiftBagId(param1:String) : void
      {
         _antiwear.currentGiftBagId = param1;
      }
      
      public function get getRecyleNum() : uint
      {
         return _antiwear.getRecyleNum;
      }
      
      public function set getRecyleNum(param1:uint) : void
      {
         _antiwear.getRecyleNum = param1;
      }
      
      public function get addGetRecycleNumTime() : Number
      {
         return _antiwear.addGetRecycleNumTime;
      }
      
      public function set addGetRecycleNumTime(param1:Number) : void
      {
         _antiwear.addGetRecycleNumTime = param1;
      }
      
      public function get onLineGiftBagXML() : XML
      {
         return _antiwear.onLineGiftBagXML;
      }
      
      public function set onLineGiftBagXML(param1:XML) : void
      {
         _antiwear.onLineGiftBagXML = param1;
      }
      
      public function get remainTime_vertify() : uint
      {
         return _antiwear.remainTime_vertify;
      }
      
      public function set remainTime_vertify(param1:uint) : void
      {
         _antiwear.remainTime_vertify = param1;
      }
      
      public function get servertime_vertify() : String
      {
         return _antiwear.servertime_vertify;
      }
      
      public function set servertime_vertify(param1:String) : void
      {
         _antiwear.servertime_vertify = param1;
      }
      
      public function exportSaveRemainTime() : uint
      {
         return Math.max(0,remainTime_vertify - m_timeUtil.timeInterval(servertime_vertify,GamingUI.getInstance().getNewestTimeStrFromSever()) * ProgramStartData.getInstance().getSixty() * ProgramStartData.getInstance().getSixty() * ProgramStartData.getInstance().getOneThousand());
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(isNaN(_lastGameTime))
         {
            _lastGameTime = param1.getOnLineTimeForThisInit();
         }
         var _loc2_:Number = param1.getOnLineTimeForThisInit() - _lastGameTime;
         remainTime -= _loc2_;
         _lastGameTime = param1.getOnLineTimeForThisInit();
      }
   }
}

