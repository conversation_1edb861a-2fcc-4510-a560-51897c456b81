package UI.Number
{
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IUINumber;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.system.ApplicationDomain;
   
   public class UINumber extends MySprite implements IUINumber
   {
      
      private var _id:int;
      
      private var _showBitmap:Bitmap;
      
      public function UINumber(param1:String)
      {
         super();
         setshow(param1);
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_showBitmap)
         {
            if(_showBitmap.bitmapData)
            {
               _showBitmap.bitmapData.dispose();
            }
            _showBitmap.bitmapData = null;
         }
         _showBitmap = null;
      }
      
      public function get id() : int
      {
         return _id;
      }
      
      public function set id(param1:int) : void
      {
         _id = param1;
      }
      
      protected function setshow(param1:String) : void
      {
         var _loc2_:BitmapData = null;
         var _loc3_:Class = MyFunction2.returnClassByClassName(param1);
         if(!_loc3_ && ApplicationDomain.currentDomain.hasDefinition(param1))
         {
            _loc3_ = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         }
         if(_loc3_)
         {
            _loc2_ = new _loc3_();
            _showBitmap = new Bitmap(_loc2_);
            while(numChildren > 0)
            {
               removeChildAt(numChildren - 1);
            }
            addChild(_showBitmap);
         }
      }
   }
}

