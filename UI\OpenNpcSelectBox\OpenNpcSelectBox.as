package UI.OpenNpcSelectBox
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class OpenNpcSelectBox extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell;
      
      private var m_wantLoadSource:Array = ["openNpcSelectBox"];
      
      private var m_sayText:TextField;
      
      private var m_ruKouBtns:Vector.<ButtonLogicShell2>;
      
      private var m_skipBtns:Vector.<ButtonLogicShell2>;
      
      private var m_backBtn:ButtonLogicShell2;
      
      private var m_openNpcListeners:Vector.<IOpenNpcListener>;
      
      private var m_option:String;
      
      private var m_openTargetData:Object;
      
      private var m_skipTargetData:Object;
      
      private var m_backTargetData:Object;
      
      public function OpenNpcSelectBox(param1:String)
      {
         super();
         m_openTargetData = {};
         m_openTargetData["RankNpc"] = ["pkRankOne","PKRankTwo","worldBoss","pkMode2_1","endless"];
         m_openTargetData["PKNpc"] = ["pkOne","pkTwo"];
         m_openTargetData["petNpc"] = ["hatch","mirage","advance"];
         m_openTargetData["taskNpc"] = ["task","mainLineTask"];
         m_openTargetData["societySystemNpc1"] = ["createSocietyPanel","joinSocietyPanel"];
         m_openTargetData["societySystemNpc2"] = ["mySocietyPanel","societyListPanel"];
         m_openTargetData["resetNpc"] = ["resetPlayer1","resetPlayer2"];
         m_openTargetData["equipMagicNpc"] = ["equipmentCreatMagic","equipmentInheritMagic"];
         m_openTargetData["onePlayerToTwo"] = ["resetPlayer1","onePlayerToTwo"];
         m_skipTargetData = {};
         m_backTargetData = {};
         m_option = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         if(m_wantLoadSource)
         {
            GamingUI.getInstance().loadQueue.unLoad(m_wantLoadSource);
         }
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.nullArr(m_wantLoadSource);
         m_wantLoadSource = null;
         m_sayText = null;
         ClearUtil.nullArr(m_openNpcListeners);
         m_openNpcListeners = null;
         ClearUtil.nullObject(m_openTargetData);
         m_openTargetData = null;
         ClearUtil.nullObject(m_skipTargetData);
         m_skipTargetData = null;
         ClearUtil.nullObject(m_backTargetData);
         m_backTargetData = null;
         ClearUtil.nullArr(m_ruKouBtns);
         m_ruKouBtns = null;
         ClearUtil.nullArr(m_skipBtns);
         m_skipBtns = null;
         m_backBtn = null;
         super.clear();
      }
      
      protected function init() : void
      {
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            var _loc2_:String = m_option;
            if(m_show == null)
            {
               m_show = MyFunction2.returnShowByClassName("NpcSelectBox") as MovieClip;
            }
            addChild(m_show);
            m_showMC = new MovieClipPlayLogicShell();
            m_showMC.setShow(m_show);
            m_showMC.gotoAndStop(_loc2_);
            m_sayText = m_show["sayText"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_sayText);
            m_sayText.mouseEnabled = false;
            m_sayText.selectable = false;
            m_quitBtn = new ButtonLogicShell();
            m_quitBtn.setShow(m_show["quitBtn"]);
            m_quitBtn.setTipString("点击关闭");
            var _loc1_:int = int(XMLSingle.getInstance().textXML[_loc2_].item.length());
            m_sayText.text = XMLSingle.getInstance().textXML[_loc2_].item[Math.floor(_loc1_ * Math.random())];
            initTexts();
         },null);
         GamingUI.getInstance().loadQueue.load(m_wantLoadSource,loadFinishListener);
      }
      
      public function addOpenNpcListener(param1:IOpenNpcListener) : void
      {
         if(m_openNpcListeners == null)
         {
            m_openNpcListeners = new Vector.<IOpenNpcListener>();
         }
         m_openNpcListeners.push(param1);
      }
      
      public function removeOpenNpcListener(param1:IOpenNpcListener) : void
      {
         if(m_openNpcListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_openNpcListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_openNpcListeners.splice(_loc2_,1);
            _loc2_ = int(m_openNpcListeners.indexOf(param1));
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",onClick,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",onClick,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      protected function onClick(param1:Event) : void
      {
      }
      
      protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:String = null;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:String = null;
         switch(param1.button)
         {
            case m_quitBtn:
               clear();
               Part1.getInstance().continueGame();
               break;
            case m_backBtn:
               _loc3_ = m_backTargetData[m_option];
               clearTexts();
               m_showMC.gotoAndStop(_loc3_);
               m_option = _loc3_;
               initTexts();
         }
         var _loc4_:int = m_ruKouBtns ? m_ruKouBtns.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(m_ruKouBtns[_loc7_] == param1.button)
            {
               _loc5_ = m_openNpcListeners ? m_openNpcListeners.length : 0;
               _loc6_ = 0;
               while(_loc6_ < _loc5_)
               {
                  m_openNpcListeners[_loc6_].openNpc(m_openTargetData[m_option][_loc7_]);
                  _loc6_++;
               }
               clear();
               return;
            }
            _loc7_++;
         }
         _loc4_ = m_skipBtns ? m_skipBtns.length : 0;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(m_skipBtns[_loc7_] == param1.button)
            {
               _loc2_ = m_skipTargetData[m_option][_loc7_];
               clearTexts();
               m_showMC.gotoAndStop(_loc2_);
               m_option = _loc2_;
               initTexts();
               break;
            }
            _loc7_++;
         }
      }
      
      protected function clearTexts() : void
      {
         ClearUtil.nullArr(m_ruKouBtns);
         m_ruKouBtns = null;
         ClearUtil.nullArr(m_skipBtns);
         m_skipBtns = null;
         m_backBtn = null;
      }
      
      protected function initTexts() : void
      {
         var _loc4_:int = 0;
         var _loc3_:* = null;
         var _loc1_:ButtonLogicShell2 = null;
         var _loc2_:int = m_show.numChildren;
         m_ruKouBtns = new Vector.<ButtonLogicShell2>();
         _loc4_ = 0;
         while(true)
         {
            _loc4_++;
            if(!m_show["btn" + _loc4_])
            {
               break;
            }
            _loc1_ = new ButtonLogicShell2();
            _loc1_.setShow(m_show["btn" + _loc4_]);
            m_ruKouBtns.push(_loc1_);
         }
         m_skipBtns = new Vector.<ButtonLogicShell2>();
         _loc4_ = 0;
         while(true)
         {
            _loc4_++;
            if(!m_show["skipBtn" + _loc4_])
            {
               break;
            }
            _loc1_ = new ButtonLogicShell2();
            _loc1_.setShow(m_show["skipBtn" + _loc4_]);
            m_skipBtns.push(_loc1_);
         }
         if(m_show["backBtn"])
         {
            m_backBtn = new ButtonLogicShell2();
            m_backBtn.setShow(m_show["backBtn"]);
         }
      }
      
      public function getOption() : String
      {
         return m_option;
      }
   }
}

