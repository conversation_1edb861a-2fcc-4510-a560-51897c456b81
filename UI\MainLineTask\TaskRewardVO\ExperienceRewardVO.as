package UI.MainLineTask.TaskRewardVO
{
   public class ExperienceRewardVO extends TaskRewardVO
   {
      
      private var _experience:int;
      
      public function ExperienceRewardVO()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.experience = _experience;
      }
      
      public function getExperience() : int
      {
         return _antiwear.experience;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         _antiwear.experience = int(param1.@value);
      }
   }
}

