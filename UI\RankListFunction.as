package UI
{
   import UI.TextTrace.traceText;
   import YJFY.API_4399.RankListAPI.SubmitData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   import flash.utils.setTimeout;
   import unit4399.events.RankListEvent;
   
   public class RankListFunction
   {
      
      private static var _instance:RankListFunction = null;
      
      private var _getUserDataAfterDatas:Array;
      
      private var _getUserDataFailDatas:Array;
      
      private var _getOneDataByNameAfterDatas:Array;
      
      private var _getOneDataByNameFailDatas:Array;
      
      private var _getRankListDataAfterDatas:Array;
      
      private var _getRankListDataFailDatas:Array;
      
      private var _submitScoreAfterDatas:Array;
      
      private var _submitScoreFailDatas:Array;
      
      private var _requestNum_1:int;
      
      private var _requestNum_2:int;
      
      private var _requestNum_3:int;
      
      private var _requestNum_4:int;
      
      private var _requestNum_5:int;
      
      public function RankListFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            Part1.getInstance().stage.addEventListener("rankListError",onRankListErrorHandler);
            Part1.getInstance().stage.addEventListener("rankListSuccess",onRankListSuccessHander);
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : RankListFunction
      {
         if(!_instance)
         {
            _instance = new RankListFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         Part1.getInstance().stage.removeEventListener("rankListError",onRankListErrorHandler);
         Part1.getInstance().stage.removeEventListener("rankListSuccess",onRankListSuccessHander);
         _instance = null;
      }
      
      public function getUserData(param1:String, param2:uint, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         if(Boolean(param3))
         {
            param3.apply(null,param5);
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "加载数据中...";
         if(!_getUserDataAfterDatas)
         {
            _getUserDataAfterDatas = [];
         }
         _getUserDataAfterDatas.push([param4,param6,param1,param2]);
         if(!_getUserDataFailDatas)
         {
            _getUserDataFailDatas = [];
         }
         _getUserDataFailDatas.push([param7,param8]);
         GamingUI.getInstance().getAPI4399().rankListAPI.getSaveData(param1,param2);
         _requestNum_5 = 1;
      }
      
      public function getOneRankListDataByUserName(param1:uint, param2:String, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         if(Boolean(param3))
         {
            param3.apply(null,param5);
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "加载数据中...";
         if(!_getOneDataByNameAfterDatas)
         {
            _getOneDataByNameAfterDatas = [];
         }
         _getOneDataByNameAfterDatas.push([param4,param6,param1,param2]);
         if(!_getOneDataByNameFailDatas)
         {
            _getOneDataByNameFailDatas = [];
         }
         _getOneDataByNameFailDatas.push([param7,param8]);
         GamingUI.getInstance().getAPI4399().rankListAPI.getOneRankInfo(param1,param2);
         _requestNum_1 = 1;
      }
      
      public function getRankListData(param1:uint, param2:int, param3:int, param4:Function, param5:Function, param6:Array, param7:Array, param8:Function, param9:Array) : void
      {
         if(Boolean(param4))
         {
            param4.apply(null,param6);
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "加载数据中...";
         if(!_getRankListDataAfterDatas)
         {
            _getRankListDataAfterDatas = [];
         }
         _getRankListDataAfterDatas.push([param5,param7,param1,param2,param3]);
         if(!_getRankListDataFailDatas)
         {
            _getRankListDataFailDatas = [];
         }
         _getRankListDataFailDatas.push([param8,param9]);
         GamingUI.getInstance().getAPI4399().rankListAPI.getRankListsData(param1,param3,param2);
         _requestNum_4 = 1;
      }
      
      public function submitScoreToRankLists(param1:uint, param2:Array, param3:Function, param4:Function, param5:Array, param6:Array, param7:Function, param8:Array) : void
      {
         var _loc12_:int = 0;
         var _loc9_:SubmitData = null;
         if(Boolean(param3))
         {
            param3.apply(null,param5);
         }
         if(!Boolean(param8))
         {
            param8 = [];
         }
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "加载数据中...";
         if(!_submitScoreAfterDatas)
         {
            _submitScoreAfterDatas = [];
         }
         _submitScoreAfterDatas.push([param4,param6,param1,param2]);
         if(!_submitScoreFailDatas)
         {
            _submitScoreFailDatas = [];
         }
         _submitScoreFailDatas.push([param7,param8]);
         var _loc10_:Vector.<SubmitData> = new Vector.<SubmitData>();
         var _loc11_:int = param2 ? param2.length : 0;
         _loc12_ = 0;
         while(_loc12_ < _loc11_)
         {
            _loc9_ = new SubmitData(param2[_loc12_].rId,param2[_loc12_].score,param2[_loc12_].extra);
            _loc10_.push(_loc9_);
            _loc12_++;
         }
         GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(param1,_loc10_);
         _requestNum_3 = 1;
      }
      
      public function onRankListErrorHandler(param1:RankListEvent) : void
      {
         var _loc11_:int = 0;
         var _loc10_:int = 0;
         var _loc6_:int = 0;
         var _loc9_:int = 0;
         var _loc4_:* = undefined;
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:SubmitData = null;
         var _loc8_:Object = param1.data;
         var _loc5_:String = "apiFlag:" + _loc8_.apiName + "   errorCode:" + _loc8_.code + "   message:" + _loc8_.message + "\n";
         traceText(_loc5_);
         switch(_loc8_.apiName)
         {
            case "1":
               if(_requestNum_1 < 5)
               {
                  if(_getOneDataByNameAfterDatas)
                  {
                     _loc6_ = int(_getOneDataByNameAfterDatas.length);
                     _loc11_ = 0;
                     while(_loc11_ < _loc6_)
                     {
                        GamingUI.getInstance().getAPI4399().rankListAPI.getOneRankInfo(_getOneDataByNameAfterDatas[_loc11_][2],_getOneDataByNameAfterDatas[_loc11_][3]);
                        _loc11_++;
                     }
                     _requestNum_1++;
                     traceText("_requestNum_1:",_requestNum_1);
                  }
                  return;
               }
               if(_getOneDataByNameFailDatas)
               {
                  _loc6_ = int(_getOneDataByNameFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     if(_getOneDataByNameFailDatas[_loc11_][0])
                     {
                        _getOneDataByNameFailDatas[_loc11_][0].apply(null,_getOneDataByNameFailDatas[_loc11_][1]);
                     }
                     _getOneDataByNameFailDatas[_loc11_][0] = null;
                     if(_getOneDataByNameFailDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getOneDataByNameFailDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getOneDataByNameFailDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getOneDataByNameFailDatas[_loc11_][1] = null;
                     }
                     _getOneDataByNameFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getOneDataByNameFailDatas = null;
               }
               if(_getOneDataByNameAfterDatas)
               {
                  _loc6_ = int(_getOneDataByNameAfterDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     _getOneDataByNameAfterDatas[_loc11_][0] = null;
                     if(_getOneDataByNameAfterDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getOneDataByNameAfterDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getOneDataByNameAfterDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getOneDataByNameAfterDatas[_loc11_][1] = null;
                     }
                     _getOneDataByNameAfterDatas[_loc11_][2] = null;
                     _getOneDataByNameAfterDatas[_loc11_][3] = null;
                     _getOneDataByNameAfterDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getOneDataByNameAfterDatas = null;
               }
               break;
            case "2":
               break;
            case "3":
               if(_requestNum_3 < 5)
               {
                  if(_submitScoreAfterDatas)
                  {
                     _loc6_ = int(_submitScoreAfterDatas.length);
                     _loc11_ = 0;
                     while(_loc11_ < _loc6_)
                     {
                        _loc4_ = new Vector.<SubmitData>();
                        _loc7_ = int(_submitScoreAfterDatas[_loc11_][3] ? _submitScoreAfterDatas[_loc11_][3].length : 0);
                        _loc3_ = 0;
                        while(_loc3_ < _loc7_)
                        {
                           _loc2_ = new SubmitData(_submitScoreAfterDatas[_loc11_][3][_loc3_].rId,_submitScoreAfterDatas[_loc11_][3][_loc3_].score,_submitScoreAfterDatas[_loc11_][3][_loc3_].extra);
                           _loc4_.push(_loc2_);
                           _loc3_++;
                        }
                        GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(_submitScoreAfterDatas[_loc11_][2],_loc4_);
                        _loc11_++;
                     }
                     _requestNum_3++;
                     traceText("_requestNum_3:",_requestNum_3);
                  }
                  return;
               }
               if(_submitScoreFailDatas)
               {
                  _loc6_ = int(_submitScoreFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     if(_submitScoreFailDatas[_loc11_][0])
                     {
                        _submitScoreFailDatas[_loc11_][0].apply(null,_submitScoreFailDatas[_loc11_][1].concat("onRankListErrorHandler错误！"));
                     }
                     _submitScoreFailDatas[_loc11_][0] = null;
                     if(_submitScoreFailDatas[_loc11_][1])
                     {
                        _loc9_ = int(_submitScoreFailDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _submitScoreFailDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _submitScoreFailDatas[_loc11_][1] = null;
                     }
                     _submitScoreFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _submitScoreFailDatas = null;
               }
               if(_submitScoreAfterDatas)
               {
                  _loc6_ = int(_submitScoreAfterDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     _submitScoreAfterDatas[_loc11_][0] = null;
                     if(_submitScoreAfterDatas[_loc11_][1])
                     {
                        _loc9_ = int(_submitScoreAfterDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _submitScoreAfterDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _submitScoreAfterDatas[_loc11_][1] = null;
                     }
                     _submitScoreAfterDatas[_loc11_][2] = null;
                     _submitScoreAfterDatas[_loc11_][3] = null;
                     _submitScoreAfterDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _submitScoreAfterDatas = null;
               }
               break;
            case "4":
               if(_requestNum_4 < 5)
               {
                  if(_getRankListDataAfterDatas)
                  {
                     _loc6_ = int(_getRankListDataAfterDatas.length);
                     _loc11_ = 0;
                     while(_loc11_ < _loc6_)
                     {
                        setTimeout(GamingUI.getInstance().getAPI4399().rankListAPI.getRankListsData,100,_getRankListDataAfterDatas[_loc11_][2],_getRankListDataAfterDatas[_loc11_][4],_getRankListDataAfterDatas[_loc11_][3]);
                        _loc11_++;
                     }
                     _requestNum_4++;
                     traceText("_requestNum_4:",_requestNum_4);
                  }
                  return;
               }
               if(_getRankListDataFailDatas)
               {
                  _loc6_ = int(_getRankListDataFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     if(_getRankListDataFailDatas[_loc11_][0])
                     {
                        _getRankListDataFailDatas[_loc11_][0].apply(null,_getRankListDataFailDatas[_loc11_][1]);
                     }
                     _getRankListDataFailDatas[_loc11_][0] = null;
                     if(_getRankListDataFailDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getRankListDataFailDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getRankListDataFailDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getRankListDataFailDatas[_loc11_][1] = null;
                     }
                     _getRankListDataFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getRankListDataFailDatas = null;
               }
               if(_getRankListDataAfterDatas)
               {
                  _loc6_ = int(_getRankListDataAfterDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     _getRankListDataAfterDatas[_loc11_][0] = null;
                     if(_getRankListDataAfterDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getRankListDataAfterDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getRankListDataAfterDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getRankListDataAfterDatas[_loc11_][1] = null;
                     }
                     _getRankListDataAfterDatas[_loc11_][2] = null;
                     _getRankListDataAfterDatas[_loc11_][3] = null;
                     _getRankListDataAfterDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getRankListDataAfterDatas = null;
               }
               break;
            case "5":
               if(_requestNum_5 < 5)
               {
                  if(_getUserDataAfterDatas)
                  {
                     _loc6_ = int(_getUserDataAfterDatas.length);
                     _loc11_ = 0;
                     while(_loc11_ < _loc6_)
                     {
                        GamingUI.getInstance().getAPI4399().rankListAPI.getSaveData(_getUserDataAfterDatas[_loc11_][2],_getUserDataAfterDatas[_loc11_][3]);
                        _loc11_++;
                     }
                     _requestNum_5++;
                     traceText("_requestNum_5:",_requestNum_5);
                  }
                  return;
               }
               if(_getUserDataFailDatas)
               {
                  _loc6_ = int(_getUserDataFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     if(_getUserDataFailDatas[_loc11_][0])
                     {
                        _getUserDataFailDatas[_loc11_][0].apply(null,_getUserDataFailDatas[_loc11_][1]);
                     }
                     _getUserDataFailDatas[_loc11_][0] = null;
                     if(_getUserDataFailDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getUserDataFailDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getUserDataFailDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getUserDataFailDatas[_loc11_][1] = null;
                     }
                     _getUserDataFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getUserDataFailDatas = null;
               }
               if(_getUserDataAfterDatas)
               {
                  _loc6_ = int(_getUserDataAfterDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc6_)
                  {
                     _getUserDataAfterDatas[_loc11_][0] = null;
                     if(_getUserDataAfterDatas[_loc11_][1])
                     {
                        _loc9_ = int(_getUserDataAfterDatas[_loc11_][1].length);
                        _loc10_ = 0;
                        while(_loc10_ < _loc9_)
                        {
                           _getUserDataAfterDatas[_loc11_][1][_loc10_] = null;
                           _loc10_++;
                        }
                        _getUserDataAfterDatas[_loc11_][1] = null;
                     }
                     _getUserDataAfterDatas[_loc11_][2] = null;
                     _getUserDataAfterDatas[_loc11_][3] = null;
                     _getUserDataAfterDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getUserDataAfterDatas = null;
               }
               break;
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
      
      public function onRankListSuccessHander(param1:RankListEvent) : void
      {
         var _loc11_:int = 0;
         var _loc7_:int = 0;
         var _loc17_:int = 0;
         var _loc8_:int = 0;
         var _loc16_:Array = null;
         var _loc10_:Array = null;
         var _loc12_:Array = null;
         var _loc3_:String = null;
         var _loc14_:* = undefined;
         var _loc13_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:SubmitData = null;
         var _loc9_:Array = null;
         var _loc15_:ByteArray = null;
         var _loc2_:Array = null;
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         var _loc18_:Object = param1.data;
         var _loc5_:* = _loc18_.data;
         switch(_loc18_.apiName)
         {
            case "1":
               if(_getOneDataByNameAfterDatas)
               {
                  _loc16_ = _getOneDataByNameAfterDatas.slice(0);
                  ClearUtil.nullArr(_getOneDataByNameAfterDatas,false,false,false);
                  _getOneDataByNameAfterDatas = null;
                  _loc17_ = int(_loc16_.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc16_[_loc11_][0].apply(null,_loc16_[_loc11_][1] ? _loc16_[_loc11_][1].push(_loc5_) : [_loc5_]);
                     _loc16_[_loc11_][0] = null;
                     if(_loc16_[_loc11_][1])
                     {
                        _loc8_ = int(_loc16_[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _loc16_[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _loc16_[_loc11_][1] = null;
                     }
                     _loc16_[_loc11_][2] = null;
                     _loc16_[_loc11_][3] = null;
                     _loc16_[_loc11_] = null;
                     _loc11_++;
                  }
                  _loc16_ = null;
               }
               if(_getOneDataByNameFailDatas)
               {
                  _loc10_ = _getOneDataByNameFailDatas.slice(0);
                  ClearUtil.nullArr(_getOneDataByNameFailDatas,false,false,false);
                  _getOneDataByNameFailDatas = null;
                  _loc17_ = int(_loc10_.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc10_[_loc11_][0] = null;
                     if(_loc10_[_loc11_][1])
                     {
                        _loc8_ = int(_loc10_[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _loc10_[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _loc10_[_loc11_][1] = null;
                     }
                     _loc10_[_loc11_] = null;
                     _loc11_++;
                  }
                  _loc10_ = null;
               }
               break;
            case "2":
               break;
            case "4":
               trace(" flag 4 _getRankListDataAfterDatas:",_getRankListDataAfterDatas);
               if(_getRankListDataAfterDatas)
               {
                  _loc12_ = _getRankListDataAfterDatas.slice(0);
                  ClearUtil.nullArr(_getRankListDataAfterDatas,false,false,false);
                  _getRankListDataAfterDatas = null;
                  _loc17_ = int(_loc12_.length);
                  trace("getRankListDataAfterDatas length:",_loc17_);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     trace("getRankListDataAfterDatas[" + _loc11_ + "][0]:" + _loc12_[_loc11_][0]);
                     _loc12_[_loc11_][0].apply(null,_loc12_[_loc11_][1] ? _loc12_[_loc11_][1].push(_loc5_) : [_loc5_]);
                     _loc12_[_loc11_][0] = null;
                     if(_loc12_[_loc11_][1])
                     {
                        _loc8_ = int(_loc12_[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _loc12_[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _loc12_[_loc11_][1] = null;
                     }
                     _loc12_[_loc11_][2] = null;
                     _loc12_[_loc11_][3] = null;
                     _loc12_[_loc11_][4] = null;
                     _loc12_[_loc11_] = null;
                     _loc11_++;
                  }
                  _loc12_ = null;
               }
               if(_getRankListDataFailDatas)
               {
                  _loc17_ = int(_getRankListDataFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _getRankListDataFailDatas[_loc11_][0] = null;
                     if(_getRankListDataFailDatas[_loc11_][1])
                     {
                        _loc8_ = int(_getRankListDataFailDatas[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _getRankListDataFailDatas[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _getRankListDataFailDatas[_loc11_][1] = null;
                     }
                     _getRankListDataFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getRankListDataFailDatas = null;
               }
               break;
            case "3":
               if(_loc5_ == null || _loc5_.length == 0 || _loc5_[0].code != "10000" || _loc5_[0].curRank == null)
               {
                  if(_loc5_ == null)
                  {
                     _loc3_ = "提交返回data为null";
                  }
                  else if(_loc5_.length == 0)
                  {
                     _loc3_ = "提交返回data.length为0";
                  }
                  else if(_loc5_[0].code != "10000")
                  {
                     _loc3_ = "提交返回data[0].code != \"10000\"" + _loc5_[0].message;
                  }
                  else if(_loc5_[0].curRank == null)
                  {
                     _loc3_ = "提交返回data[0].curRank == null";
                  }
                  else
                  {
                     _loc3_ = "未知错误！";
                  }
                  if(_requestNum_3 < 5 && _submitScoreAfterDatas)
                  {
                     _loc14_ = new Vector.<SubmitData>();
                     _loc6_ = int(_submitScoreAfterDatas[3] ? _submitScoreAfterDatas[3].length : 0);
                     _loc13_ = 0;
                     while(_loc13_ < _loc6_)
                     {
                        _loc4_ = new SubmitData(_submitScoreAfterDatas[3][_loc13_].rId,_submitScoreAfterDatas[3][_loc13_].score,_submitScoreAfterDatas[3][_loc13_].extra);
                        _loc14_.push(_loc4_);
                        _loc13_++;
                     }
                     GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(_submitScoreAfterDatas[2],_loc14_);
                     _requestNum_3++;
                  }
                  else
                  {
                     if(_submitScoreFailDatas)
                     {
                        _loc17_ = int(_submitScoreFailDatas.length);
                        _loc11_ = 0;
                        while(_loc11_ < _loc17_)
                        {
                           if(_submitScoreFailDatas[_loc11_][0])
                           {
                              _submitScoreFailDatas[_loc11_][0].apply(null,_submitScoreFailDatas[_loc11_][1].concat(_loc3_));
                           }
                           _submitScoreFailDatas[_loc11_][0] = null;
                           if(_submitScoreFailDatas[_loc11_][1])
                           {
                              _loc8_ = int(_submitScoreFailDatas[_loc11_][1].length);
                              _loc7_ = 0;
                              while(_loc7_ < _loc8_)
                              {
                                 _submitScoreFailDatas[_loc11_][1][_loc7_] = null;
                                 _loc7_++;
                              }
                              _submitScoreFailDatas[_loc11_][1] = null;
                           }
                           _submitScoreFailDatas[_loc11_] = null;
                           _loc11_++;
                        }
                        _submitScoreFailDatas = null;
                     }
                     if(_submitScoreAfterDatas)
                     {
                        _loc17_ = int(_submitScoreAfterDatas.length);
                        _loc11_ = 0;
                        while(_loc11_ < _loc17_)
                        {
                           _submitScoreAfterDatas[_loc11_][0] = null;
                           if(_submitScoreAfterDatas[_loc11_][1])
                           {
                              _loc8_ = int(_submitScoreAfterDatas[_loc11_][1].length);
                              _loc7_ = 0;
                              while(_loc7_ < _loc8_)
                              {
                                 _submitScoreAfterDatas[_loc11_][1][_loc7_] = null;
                                 _loc7_++;
                              }
                              _submitScoreAfterDatas[_loc11_][1] = null;
                           }
                           _submitScoreAfterDatas[_loc11_][2] = null;
                           _submitScoreAfterDatas[_loc11_][3] = null;
                           _submitScoreAfterDatas[_loc11_] = null;
                           _loc11_++;
                        }
                        _submitScoreAfterDatas = null;
                     }
                  }
                  return;
               }
               if(_submitScoreAfterDatas)
               {
                  _loc9_ = _submitScoreAfterDatas.slice(0);
                  ClearUtil.nullArr(_submitScoreAfterDatas,false,false,false);
                  _submitScoreAfterDatas = null;
                  _loc17_ = int(_loc9_.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc9_[_loc11_][0].apply(null,_loc9_[_loc11_][1] ? _loc9_[_loc11_][1].push(_loc5_) : [_loc5_]);
                     _loc9_[_loc11_][0] = null;
                     if(_loc9_[_loc11_][1])
                     {
                        _loc8_ = int(_loc9_[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _loc9_[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _loc9_[_loc11_][1] = null;
                     }
                     _loc9_[_loc11_][2] = null;
                     _loc9_[_loc11_][3] = null;
                     _loc9_[_loc11_] = null;
                     _loc11_++;
                  }
                  _loc9_ = null;
               }
               if(_submitScoreFailDatas)
               {
                  _loc17_ = int(_submitScoreFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _submitScoreFailDatas[_loc11_][0] = null;
                     if(_submitScoreFailDatas[_loc11_][1])
                     {
                        _loc8_ = int(_submitScoreFailDatas[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _submitScoreFailDatas[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _submitScoreFailDatas[_loc11_][1] = null;
                     }
                     _submitScoreFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _submitScoreFailDatas = null;
               }
               break;
            case "5":
               if(_getUserDataAfterDatas)
               {
                  if(String(_loc5_.title).substr(0,3) == "V1_")
                  {
                     _loc5_.title = String(_loc5_.title).substring(3);
                     _loc15_ = Base64.decodeToByteArray(String(_loc5_.data));
                     _loc15_.uncompress();
                     _loc15_.position = 0;
                     _loc5_.data = _loc15_.readObject();
                  }
                  _loc2_ = _getUserDataAfterDatas.slice(0);
                  ClearUtil.nullArr(_getUserDataAfterDatas,false,false,false);
                  _getUserDataAfterDatas = null;
                  _loc17_ = int(_loc2_.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc2_[_loc11_][0].apply(null,_loc2_[_loc11_][1] ? _loc2_[_loc11_][1].push(_loc5_) : [_loc5_]);
                     _loc2_[_loc11_][0] = null;
                     if(_loc2_[_loc11_][1])
                     {
                        _loc8_ = int(_loc2_[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _loc2_[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _loc2_[_loc11_][1] = null;
                     }
                     _loc2_[_loc11_][2] = null;
                     _loc2_[_loc11_][3] = null;
                     _loc2_[_loc11_] = null;
                     _loc11_++;
                  }
                  _loc2_ = null;
               }
               if(_getUserDataFailDatas)
               {
                  _loc17_ = int(_getUserDataFailDatas.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _getUserDataFailDatas[_loc11_][0] = null;
                     if(_getUserDataFailDatas[_loc11_][1])
                     {
                        _loc8_ = int(_getUserDataFailDatas[_loc11_][1].length);
                        _loc7_ = 0;
                        while(_loc7_ < _loc8_)
                        {
                           _getUserDataFailDatas[_loc11_][1][_loc7_] = null;
                           _loc7_++;
                        }
                        _getUserDataFailDatas[_loc11_][1] = null;
                     }
                     _getUserDataFailDatas[_loc11_] = null;
                     _loc11_++;
                  }
                  _getUserDataFailDatas = null;
               }
         }
      }
   }
}

