package UI.PKUI
{
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class PKRankListColume extends MySwitchBtnLogicShell
   {
      
      private var m_rankText:TextField;
      
      private var m_playerNameText:TextField;
      
      private var m_winMatchNumText:TextField;
      
      private var m_rankIcon:MovieClipPlayLogicShell;
      
      public var m_rId:int = 0;
      
      private var _rankObject:Object;
      
      public function PKRankListColume()
      {
         super();
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         m_rankIcon = new MovieClipPlayLogicShell();
         m_rankIcon.setShow(m_show["placeIcon"]);
         m_rankText = m_show["rankText"];
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_rankText);
         m_rankText.mouseEnabled = false;
         m_playerNameText = m_show["playerNameText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_playerNameText);
         m_playerNameText.mouseEnabled = false;
         m_winMatchNumText = m_show["winMatchNumText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_winMatchNumText);
         m_winMatchNumText.mouseEnabled = false;
      }
      
      public function get rankObject() : Object
      {
         return _rankObject;
      }
      
      public function set rankObject(param1:Object) : void
      {
         _rankObject = param1;
         if(_rankObject.rank <= 3)
         {
            m_rankText.visible = false;
            m_rankIcon.getShow().visible = true;
            m_rankIcon.gotoAndStop("" + _rankObject.rank);
         }
         else
         {
            m_rankText.visible = true;
            m_rankIcon.getShow().visible = false;
            m_rankText.text = "  " + _rankObject.rank;
         }
         m_playerNameText.text = _rankObject.userName;
         if(m_rId == 1576 || m_rId == 1583 || m_rId == 1580 || m_rId == 1575)
         {
            m_winMatchNumText.text = String(Math.floor(_rankObject.score / 100000));
         }
         else
         {
            m_winMatchNumText.text = _rankObject.score.toString();
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_rankIcon);
         m_rankIcon = null;
         m_rankText = null;
         m_playerNameText = null;
         m_winMatchNumText = null;
         _rankObject = null;
      }
   }
}

