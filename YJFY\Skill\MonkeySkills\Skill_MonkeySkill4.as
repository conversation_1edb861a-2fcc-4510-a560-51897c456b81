package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IEntity;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_MonkeySkill4 extends CuboidAreaAttackSkill
   {
      
      private const m_const_groundEffectEndFrameLabel:String = "groundEffect^end^";
      
      private const m_const_skillEndFrameLabel:String = "skill^stop^";
      
      private const m_const_produceGroundEffectFrameLabel:String = "produceGroundEffect";
      
      private var m_skillBodyDefId:String;
      
      private var m_groundEffectDefId:String;
      
      private var m_groundEffectAnimation:AnimationShowPlayLogicShell;
      
      private var m_groundEffectAniamtionFrameLabelListener:AnimationPlayFrameLabelListener;
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      public function Skill_MonkeySkill4()
      {
         super();
         m_groundEffectAnimation = new AnimationShowPlayLogicShell();
         m_groundEffectAniamtionFrameLabelListener = new AnimationPlayFrameLabelListener();
         m_groundEffectAniamtionFrameLabelListener.reachFrameLabelFun = groundEffectReachFrameLabel;
         m_groundEffectAnimation.addFrameLabelListener(m_groundEffectAniamtionFrameLabelListener);
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         m_skillBodyDefId = null;
         m_groundEffectDefId = null;
         if(m_world)
         {
            m_world.removeAnimationInGround(m_groundEffectAnimation);
         }
         ClearUtil.clearObject(m_groundEffectAnimation);
         m_groundEffectAnimation = null;
         ClearUtil.clearObject(m_groundEffectAniamtionFrameLabelListener);
         m_groundEffectAniamtionFrameLabelListener = null;
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillBodyDefId = String(param1.@skillBodyDefId);
         m_groundEffectDefId = String(param1.@groundEffectDefId);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_skillBodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         m_groundEffectAnimation.setShow(m_owner.getAnimationByDefId(m_groundEffectDefId),true);
         (m_groundEffectAnimation.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         (m_groundEffectAnimation.getShow() as DisplayObject).x = m_owner.getScreenX();
         (m_groundEffectAnimation.getShow() as DisplayObject).y = m_owner.getScreenY();
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
      }
      
      override public function showRender() : void
      {
         super.showRender();
         m_groundEffectAnimation.render();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         switch(param1)
         {
            case "skill^stop^":
               endSkill2();
               break;
            case "produceGroundEffect":
               m_world.addAnimationInGround(m_groundEffectAnimation);
               m_groundEffectAnimation.gotoAndPlay("1");
               attackReach(m_world);
               m_world.shakeView(m_shakeViewDataOfPoints);
         }
      }
      
      private function groundEffectReachFrameLabel(param1:String) : void
      {
         var _loc2_:* = param1;
         if("groundEffect^end^" === _loc2_)
         {
            m_world.removeAnimationInGround(m_groundEffectAnimation);
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            param1.getBody().setVelocity(new P3DVector3D(0,0,200));
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

