package UI.Other
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.PointTicketBuyBox2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.Part1;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class QuickenChargeBox extends PointTicketBuyBox2
   {
      
      public var ticketText:TextField;
      
      public var timeFun:Function;
      
      public var activeFun:Function;
      
      public function QuickenChargeBox()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ticketText = null;
         timeFun = null;
         activeFun = null;
      }
      
      override protected function init() : void
      {
         super.init();
         ticketText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.embedFonts = true;
         ticketText.text = (_numBtnGroup.num * int(XMLSingle.getInstance().dataXML.Hatch.@hatchPrice)).toString();
         _numBtnGroup.x += 33;
         _pointTicketText.x += 33;
      }
      
      override protected function recharge(param1:MouseEvent) : void
      {
         var _loc2_:* = null;
         AnalogServiceHoldFunction.getInstance().payMoney_As3();
         try
         {
            activeFun();
         }
         catch(error:Error)
         {
            trace(error.message);
         }
         parent.removeChild(this);
         clear();
      }
      
      override protected function changeNum(param1:UIPassiveEvent) : void
      {
         ticketText.text = (_numBtnGroup.num * int(XMLSingle.getInstance().dataXML.Hatch.@hatchPrice)).toString();
      }
      
      public function setNum(param1:int) : void
      {
         _numBtnGroup.num = param1;
         ticketText.text = (_numBtnGroup.num * int(XMLSingle.getInstance().dataXML.Hatch.@hatchPrice)).toString();
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var price:int;
         var num:int;
         var tFun:Function;
         var e:UIBtnEvent = param1;
         if(e.target == _sureBtn)
         {
            try
            {
               price = _numBtnGroup.num * int(XMLSingle.getInstance().dataXML.Hatch.@hatchPrice);
               num = _numBtnGroup.num;
               tFun = timeFun;
               AnalogServiceHoldFunction.getInstance().buyByPointTicket(price,function(param1:Function):void
               {
                  var dec:Function = param1;
                  dec(price,function():void
                  {
                     tFun(num);
                  },Part1.getInstance().stage,GamingUI.getInstance().internalPanel.showWarningBox);
               },Part1.getInstance().stage,GamingUI.getInstance().internalPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
            }
            catch(error:Error)
            {
               trace(error.message);
            }
            try
            {
               activeFun();
            }
            catch(error:Error)
            {
               trace(error.message);
            }
            parent.removeChild(this);
            clear();
         }
         else
         {
            try
            {
               activeFun();
            }
            catch(error:Error)
            {
               trace(error.message);
            }
            parent.removeChild(this);
            clear();
         }
      }
   }
}

