package UI.WorldBoss.Utils
{
   import UI.WorldBoss.GetWeekNum;
   import UI.WorldBoss.WeekData;
   
   public class GetWorldBossData
   {
      
      public function GetWorldBossData()
      {
         super();
      }
      
      public function getData(param1:String, param2:XML, param3:Boolean, param4:WorldBossNeedSomeData, param5:int = 0) : void
      {
         param4.rankXML = param2.rank[param5];
         param4.effectiveTime = String(param4.rankXML.@effectiveTime);
         param4.cycleLength = Number(param4.rankXML.@reSetTimeLong);
         var _loc6_:WeekData = new WeekData();
         new GetWeekNum().getWeekNum(param4.effectiveTime,param1,param4.cycleLength,_loc6_);
         param4.timeInCycle = _loc6_.timeInCycle;
         param4.cycleNum = _loc6_.cycleNum;
         if(param3)
         {
            param4.rankId = int(param4.rankXML["rankId" + (param4.cycleNum % 2 + 1)]);
         }
         else
         {
            param4.rankId = int(param4.rankXML["rankId" + (param4.cycleNum % 2 == 0 ? 2 : 1)]);
         }
      }
      
      public function getBossXML(param1:String, param2:XML) : XML
      {
         if(Boolean(param1) == false)
         {
            return null;
         }
         return param2.Boss[0].boss.(@id == param1)[0];
      }
   }
}

