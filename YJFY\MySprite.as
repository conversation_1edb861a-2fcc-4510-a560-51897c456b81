package YJFY
{
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class MySprite extends Sprite
   {
      
      public function MySprite()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      public function clear() : void
      {
         ClearUtil.clearDisplayObjectInContainer(this);
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

