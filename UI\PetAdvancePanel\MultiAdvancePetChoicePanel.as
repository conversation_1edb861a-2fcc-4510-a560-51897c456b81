package UI.PetAdvancePanel
{
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.Pets.Pet;
   import UI.Utils.CreateAdvancePet.CreateAdvancePet;
   import UI.Utils.CreateAdvancePet.CreateAdvancePetReturn;
   import YJFY.EntityShowContainer;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MultiAdvancePetChoicePanel
   {
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_petNum:int;
      
      private var m_advancePets:Vector.<Pet>;
      
      private var m_advancePetShowContainers:Vector.<EntityShowContainer>;
      
      private var m_petDataXMLList:XMLList;
      
      private var m_choicePanels:Vector.<SwitchBtnLogicShell>;
      
      private var m_choicePanelGroup:SwitchBtnGroupLogicShell;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_show:MovieClip;
      
      private var m_choiceListener:IChoiceListener;
      
      public function MultiAdvancePetChoicePanel()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            m_show = null;
         }
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.nullArr(m_advancePets);
         m_advancePets = null;
         ClearUtil.nullArr(m_advancePetShowContainers);
         m_advancePetShowContainers = null;
         ClearUtil.clearObject(m_choicePanelGroup);
         m_choicePanelGroup = null;
         ClearUtil.clearObject(m_choicePanels);
         m_choicePanels = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         m_choiceListener = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         initShow();
      }
      
      public function setData(param1:XMLList, param2:Pet, param3:IChoiceListener) : void
      {
         var _loc5_:int = 0;
         var _loc4_:CreateAdvancePetReturn = null;
         m_petDataXMLList = param1;
         m_petNum = param1.length();
         m_choiceListener = param3;
         ClearUtil.nullArr(m_advancePets);
         ClearUtil.nullArr(m_advancePetShowContainers);
         m_advancePets = new Vector.<Pet>();
         m_advancePetShowContainers = new Vector.<EntityShowContainer>();
         _loc5_ = 0;
         while(_loc5_ < m_petNum)
         {
            _loc4_ = new CreateAdvancePet().createAdvancePet(param1[_loc5_].@newId,param2);
            m_advancePets.push(_loc4_.getAdvancePet());
            _loc4_.nullQuotes();
            ClearUtil.clearObject(_loc4_);
            _loc4_ = null;
            _loc5_++;
         }
         initShow();
      }
      
      private function initShow() : void
      {
         if(m_advancePets == null || m_advancePets.length == 0)
         {
            clearFrame();
            initFrame(1);
         }
         else
         {
            clearFrame();
            initFrame(m_petNum - 1);
         }
         initShow2();
      }
      
      private function initShow2() : void
      {
         var i:int;
         var length:int;
         var completeFun:* = function():void
         {
            var _loc1_:EntityShowContainer = new EntityShowContainer();
            _loc1_.init();
            _loc1_.refreshPetShow(m_advancePets[i].petEquipmentVO);
            m_advancePetShowContainers.push(_loc1_);
            _loc1_.getShow().scaleX = _loc1_.getShow().scaleY = 2;
            m_choicePanels[i].getShow()["container"].addChild(_loc1_.getShow());
            ClearUtil.nullObject(m_choicePanels[i].data);
            m_choicePanels[i].data = {};
            m_choicePanels[i].data["pet"] = m_advancePets[i];
            m_choicePanels[i].data["xml"] = m_petDataXMLList[i];
            i++;
            if(i < length)
            {
               completeFun();
            }
         };
         if(m_show == null || m_advancePets == null || m_advancePets.length == 0)
         {
            return;
         }
         length = int(m_advancePets.length);
         completeFun();
      }
      
      private function clearFrame() : void
      {
         ClearUtil.clearObject(m_choicePanelGroup);
         m_choicePanelGroup = null;
         ClearUtil.nullArr(m_choicePanels);
         m_choicePanels = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
      }
      
      private function initFrame(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:SwitchBtnLogicShell = null;
         m_showMC.gotoAndStop(param1 + "");
         m_choicePanelGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_choicePanels = new Vector.<SwitchBtnLogicShell>();
         _loc3_ = 0;
         while(_loc3_ < param1 + 1)
         {
            _loc2_ = new MySwitchBtnLogicShell();
            _loc2_.setShow(m_show["choicePanel" + (_loc3_ + 1)]);
            _loc2_.setTipString("点击选择超进化目标宠物");
            m_choicePanelGroup.addSwitchBtn(_loc2_);
            m_choicePanels.push(_loc2_);
            _loc3_++;
         }
         m_choicePanelGroup.addEnd();
         m_sureBtn = new ButtonLogicShell2();
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_sureBtn.setTipString("确定选择");
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:PetEquipmentVO = null;
         var _loc2_:Pet = null;
         var _loc4_:XML = null;
         var _loc5_:* = param1.button;
         if(m_sureBtn === _loc5_)
         {
            _loc3_ = (m_choicePanelGroup.currentActivateBtn().data["pet"] as Pet).petEquipmentVO;
            (m_choicePanelGroup.currentActivateBtn().data["pet"] as Pet).petEquipmentVO = null;
            _loc2_ = new Pet(_loc3_.clone() as PetEquipmentVO);
            (m_choicePanelGroup.currentActivateBtn().data["pet"] as Pet).petEquipmentVO = _loc3_;
            _loc4_ = m_choicePanelGroup.currentActivateBtn().data["xml"];
            if(m_choiceListener)
            {
               m_choiceListener.choice(_loc2_,_loc4_);
            }
         }
      }
   }
}

