package UI.Button.SwitchBtn
{
   import UI.Event.UIBtnEvent;
   
   public class ChangEBtn extends SwitchBtn
   {
      
      public var btnName:String;
      
      public function ChangEBtn()
      {
         super();
         setTipString("嫦娥");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToChangE"));
      }
   }
}

