package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class ClothesEquipmentVO extends AbleEquipmentVO
   {
      
      private var _defence:int;
      
      private var _minDefence:int;
      
      private var _maxDefence:int;
      
      private var _riot:Number;
      
      private var _minRiot:Number;
      
      private var _maxRiot:Number;
      
      public var addPlayerSaveAttr:Vector.<String> = new Vector.<String>();
      
      public var addPlayerSaveAttrVals:Vector.<Number> = new Vector.<Number>();
      
      public function ClothesEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.defence = _defence;
         _antiwear.maxDefence = _maxDefence;
         _antiwear.minDefence = _minDefence;
         _antiwear.riot = _riot;
         _antiwear.minRiot = _minRiot;
         _antiwear.maxRiot = _maxRiot;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:ClothesEquipmentVO = new ClothesEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as ClothesEquipmentVO).defence = this.defence;
         (param1 as ClothesEquipmentVO).minDefence = this.minDefence;
         (param1 as ClothesEquipmentVO).maxDefence = this.maxDefence;
         (param1 as ClothesEquipmentVO).riot = this.riot;
         (param1 as ClothesEquipmentVO).minRiot = this.minRiot;
         (param1 as ClothesEquipmentVO).maxRiot = this.maxRiot;
         _loc2_ = int(addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as ClothesEquipmentVO).addPlayerSaveAttr.push(addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as ClothesEquipmentVO).addPlayerSaveAttrVals.push(addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
      }
      
      public function get riot() : Number
      {
         return _antiwear.riot;
      }
      
      public function set riot(param1:Number) : void
      {
         _antiwear.riot = param1;
      }
      
      public function get minRiot() : Number
      {
         return _antiwear.minRiot;
      }
      
      public function set minRiot(param1:Number) : void
      {
         _antiwear.minRiot = param1;
      }
      
      public function get maxRiot() : Number
      {
         return _antiwear.maxRiot;
      }
      
      public function set maxRiot(param1:Number) : void
      {
         _antiwear.maxRiot = param1;
      }
      
      public function get defence() : int
      {
         return _antiwear.defence;
      }
      
      public function set defence(param1:int) : void
      {
         _antiwear.defence = param1;
      }
      
      public function get maxDefence() : int
      {
         return _antiwear.maxDefence;
      }
      
      public function set maxDefence(param1:int) : void
      {
         _antiwear.maxDefence = param1;
      }
      
      public function get minDefence() : int
      {
         return _antiwear.minDefence;
      }
      
      public function set minDefence(param1:int) : void
      {
         _antiwear.minDefence = param1;
      }
   }
}

