package UI2.TimeUtil
{
   import UI.DataManagerParent;
   import YJFY.Utils.TimeUtil.ITimePeriodOfOneDay;
   
   public class TimePeriodOfOneDay extends DataManagerParent implements ITimePeriodOfOneDay
   {
      
      private var m_startTime:String;
      
      private var m_endTime:String;
      
      public function TimePeriodOfOneDay()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_startTime = null;
         m_endTime = null;
         super.clear();
      }
      
      public function setStartTimeAndEndTime(param1:String, param2:String) : void
      {
         this.startTime = param1;
         this.endTime = param2;
      }
      
      public function getStartTime() : String
      {
         return startTime;
      }
      
      public function getEndTime() : String
      {
         return endTime;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startTime = m_startTime;
         _antiwear.endTime = m_endTime;
      }
      
      private function get startTime() : String
      {
         return _antiwear.startTime;
      }
      
      private function set startTime(param1:String) : void
      {
         _antiwear.startTime = param1;
      }
      
      private function get endTime() : String
      {
         return _antiwear.endTime;
      }
      
      private function set endTime(param1:String) : void
      {
         _antiwear.endTime = param1;
      }
   }
}

