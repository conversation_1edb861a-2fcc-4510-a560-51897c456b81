package YJFY.Utils.TimeUtil
{
   public class TimeUtil
   {
      
      public static const s_const_millisecondNumOfOneSecond:uint = 1000;
      
      public static const m_const_millisecondNumOfOneMinute:uint = 60000;
      
      public static const s_const_millisecondNumOfOneHour:uint = 3600000;
      
      public static var timeStr:String = "";
      
      private static var _instance:TimeUtil;
      
      public function TimeUtil()
      {
         super();
      }
      
      public static function getTimeUtil() : TimeUtil
      {
         if(!_instance)
         {
            _instance = new TimeUtil();
         }
         return _instance;
      }
      
      public function getTimeStr(param1:Date = null) : String
      {
         if(!param1)
         {
            param1 = new Date();
         }
         var _loc4_:String = "";
         _loc4_ = _loc4_ + (param1.getFullYear() + "-");
         var _loc6_:int = param1.getMonth() + 1;
         _loc4_ += String(int(_loc6_ / 10)) + String(int(_loc6_ % 10)) + "-";
         var _loc5_:int = int(param1.getDate());
         _loc4_ += String(int(_loc5_ / 10)) + String(int(_loc5_ % 10)) + " ";
         var _loc7_:int = int(param1.getHours());
         _loc4_ += String(int(_loc7_ / 10)) + String(int(_loc7_ % 10)) + ":";
         var _loc2_:int = int(param1.getMinutes());
         _loc4_ += String(int(_loc2_ / 10)) + String(int(_loc2_ % 10)) + ":";
         var _loc3_:int = int(param1.getSeconds());
         return _loc4_ + (String(int(_loc3_ / 10)) + String(int(_loc3_ % 10)));
      }
      
      public function timeInterval(param1:String, param2:String) : Number
      {
         var _loc4_:Number = Number(stringToDate(param1).getTime());
         var _loc3_:Number = Number(stringToDate(param2).getTime());
         return (_loc3_ - _loc4_) / 3600000;
      }
      
      public function timeIntervalBySecond(param1:String, param2:String) : Number
      {
         var _loc4_:Number = Number(stringToDate(param1).getTime());
         var _loc3_:Number = Number(stringToDate(param2).getTime());
         return (_loc3_ - _loc4_) / 1000;
      }
      
      public function stringToDate(param1:String) : Date
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         if(param1 !== "")
         {
            _loc7_ = int(param1.substr(0,4));
            _loc5_ = int(param1.substr(5,2));
            _loc3_ = int(param1.substr(8,2));
            _loc6_ = int(param1.substr(11,2));
            _loc2_ = int(param1.substr(14,2));
            _loc4_ = int(param1.substr(17,2));
            return new Date(_loc7_,_loc5_ - 1,_loc3_,_loc6_,_loc2_,_loc4_);
         }
         return new Date(1980,1,1,1,1,1);
      }
      
      public function getInTime(param1:String, param2:String, param3:String) : Boolean
      {
         var _loc5_:Date = stringToDate(param1);
         var _loc6_:Date = stringToDate(param2);
         var _loc4_:Date = stringToDate(param3);
         if(_loc4_.time >= _loc5_.time && _loc4_.time <= _loc6_.time)
         {
            return true;
         }
         return false;
      }
      
      public function isInTimeOfOneDay(param1:String, param2:String, param3:String) : Boolean
      {
         var _loc5_:int = int(param1.search(" "));
         var _loc7_:String = param1.substring(0,_loc5_);
         param2 = _loc7_ + " " + param2;
         param3 = _loc7_ + " " + param3;
         var _loc4_:Number = timeInterval(param2,param1);
         var _loc6_:Number = timeInterval(param1,param3);
         if(_loc4_ >= 0 && _loc6_ >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function isInTimeOfOneDay2(param1:String, param2:ITimePeriodsOfOneDay) : Boolean
      {
         var _loc5_:int = 0;
         var _loc3_:ITimePeriodOfOneDay = null;
         var _loc4_:int = int(param2.getNumOfTimePeriod());
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = param2.getTimePeriodByIndex(_loc5_);
            if(isInTimeOfOneDay(param1,_loc3_.getStartTime(),_loc3_.getEndTime()))
            {
               return true;
            }
            _loc5_++;
         }
         return false;
      }
      
      public function newDateIsNewDay(param1:String, param2:String) : Boolean
      {
         if(!Boolean(param1))
         {
            return true;
         }
         var _loc3_:Number = timeInterval(transformTimeToZero(param1),param2);
         if(_loc3_ > 24)
         {
            return true;
         }
         return false;
      }
      
      public function newTimeIsNewWeek(param1:String, param2:String) : Boolean
      {
         if(!Boolean(param1))
         {
            return true;
         }
         var _loc4_:String = "2014-06-02 00:00:00";
         var _loc3_:uint = Math.floor(timeInterval(_loc4_,param1) / 168);
         var _loc5_:uint = Math.floor(timeInterval(_loc4_,param2) / 168);
         if(_loc3_ != _loc5_)
         {
            return true;
         }
         return false;
      }
      
      public function getDistanceWeek(param1:String) : int
      {
         if(!param1)
         {
            param1 = timeStr;
         }
         var _loc2_:Date = stringToDate(param1);
         var _loc3_:int = _loc2_.day;
         if(_loc3_ == 0)
         {
            _loc3_ = 7;
         }
         return (7 - _loc3_) * 24 + (24 - _loc2_.hours);
      }
      
      public function getWeek(param1:String = null) : int
      {
         if(!param1)
         {
            param1 = timeStr;
         }
         var _loc2_:Date = stringToDate(param1);
         var _loc3_:int = _loc2_.day;
         if(_loc3_ == 0)
         {
            _loc3_ = 7;
         }
         return _loc3_;
      }
      
      public function getMonth(param1:String = null) : int
      {
         if(!param1)
         {
            param1 = timeStr;
         }
         var _loc3_:Date = stringToDate(param1);
         return int(_loc3_.month);
      }
      
      public function getFirstWeekDay(param1:String) : String
      {
         var _loc4_:String = transformTimeToZero(param1);
         var _loc3_:Date = stringToDate(_loc4_);
         var _loc5_:int = _loc3_.day;
         if(_loc5_ == 0)
         {
            _loc5_ = 7;
         }
         var _loc2_:Number = _loc3_.getTime() - (_loc5_ - 1) * 24 * 60 * 60 * 1000;
         _loc3_.setTime(_loc2_);
         return getTimeStr(_loc3_);
      }
      
      public function getDayNumBetweenTwoTime(param1:String, param2:String) : int
      {
         if(Boolean(param1) == false)
         {
            return -1;
         }
         var _loc3_:Number = timeInterval(transformTimeToZero(param1),param2);
         return int(_loc3_ / 24);
      }
      
      public function splitTimeString(param1:String = null) : String
      {
         if(!param1)
         {
            param1 = timeStr;
         }
         return param1.split(" ")[0];
      }
      
      public function transformTimeToZero(param1:String) : String
      {
         return splitTimeString(param1) + " 00:00:00";
      }
   }
}

