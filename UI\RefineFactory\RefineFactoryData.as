package UI.RefineFactory
{
   import UI.DataManagerParent;
   import UI.RefineFactory.LianDanFurnace.LianDanFurnaceVO;
   
   public class RefineFactoryData extends DataManagerParent
   {
      
      private static var _instance:RefineFactoryData = null;
      
      public var lianDanFurnace:LianDanFurnaceVO;
      
      private var _materialID:int;
      
      private var _materialNum:int;
      
      private var _refineTargetID:int;
      
      private var _refineTargetNum:int;
      
      private var _atOnceCompleteDate:String;
      
      private var _atOnceCompleteNum:int;
      
      public function RefineFactoryData()
      {
         if(!_instance)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : RefineFactoryData
      {
         if(!_instance)
         {
            _instance = new RefineFactoryData();
         }
         return _instance;
      }
      
      public function get materialID() : int
      {
         return _antiwear.materialID;
      }
      
      public function set materialID(param1:int) : void
      {
         _antiwear.materialID = param1;
      }
      
      public function get materialNum() : int
      {
         return _antiwear.materialNum;
      }
      
      public function set materialNum(param1:int) : void
      {
         _antiwear.materialNum = param1;
      }
      
      public function get refineTargetID() : int
      {
         return _antiwear.refineTargetID;
      }
      
      public function set refineTargetID(param1:int) : void
      {
         _antiwear.refineTargetID = param1;
      }
      
      public function get refineTargetNum() : int
      {
         return _antiwear.refineTargetNum;
      }
      
      public function set refineTargetNum(param1:int) : void
      {
         _antiwear.refineTargetNum = param1;
      }
      
      public function get atOnceCompleteNum() : int
      {
         return _antiwear.atOnceCompleteNum;
      }
      
      public function set atOnceCompleteNum(param1:int) : void
      {
         _antiwear.atOnceCompleteNum = param1;
      }
      
      public function get atOnceCompleteDate() : String
      {
         return _antiwear.atOnceCompleteDate;
      }
      
      public function set atOnceCompleteDate(param1:String) : void
      {
         _antiwear.atOnceCompleteDate = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.materialID = _materialID;
         _antiwear.materialNum = _materialNum;
         _antiwear.refineTargetID = _refineTargetID;
         _antiwear.atOnceCompleteDate = _atOnceCompleteDate;
         _antiwear.atOnceCompleteNum = _atOnceCompleteNum;
         _antiwear.refineTargetNum = _refineTargetNum;
      }
      
      override public function clear() : void
      {
         super.clear();
         lianDanFurnace = null;
         _instance = null;
      }
   }
}

