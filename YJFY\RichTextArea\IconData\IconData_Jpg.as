package YJFY.RichTextArea.IconData
{
   public class IconData_Jpg extends IconData
   {
      
      private var m_url:String;
      
      public function IconData_Jpg(param1:String, param2:String, param3:String)
      {
         super(param1,param2);
         m_url = param3;
         m_iconType = "jpg";
      }
      
      public function getUrl() : String
      {
         return m_url;
      }
   }
}

