package UI.RefineFactory.LianDanFurnace
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class LianDanFurnaceVO
   {
      
      public static const IDLE_STATE:int = 0;
      
      public static const REFINE_STATE:int = 1;
      
      public static const REFINE_COMPLETE_STATE:int = 2;
      
      private var _id:int;
      
      private var _state:int;
      
      private var _date:String;
      
      private var _remainTime:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function LianDanFurnaceVO()
      {
         super();
         init();
      }
      
      private function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.state = _state;
         _antiwear.date = _date;
         _antiwear.remainTime = _remainTime;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get state() : int
      {
         return _antiwear.state;
      }
      
      public function set state(param1:int) : void
      {
         _antiwear.state = param1;
      }
      
      public function get date() : String
      {
         return _antiwear.date;
      }
      
      public function set date(param1:String) : void
      {
         _antiwear.date = param1;
      }
      
      public function get remainTime() : int
      {
         return _antiwear.remainTime;
      }
      
      public function set remainTime(param1:int) : void
      {
         _antiwear.remainTime = param1;
      }
   }
}

