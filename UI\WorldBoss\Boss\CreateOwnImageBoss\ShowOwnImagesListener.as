package UI.WorldBoss.Boss.CreateOwnImageBoss
{
   import UI.WorldBoss.Boss.Boss;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IFrameLabelListener;
   import flash.display.Sprite;
   
   public class ShowOwnImagesListener implements IFrameLabelListener
   {
      
      public var fightStage:Sprite;
      
      public var mc:AnimationShowPlayLogicShell;
      
      public var createBosses:Vector.<Boss>;
      
      public function ShowOwnImagesListener()
      {
         super();
      }
      
      public function clear() : void
      {
         mc = null;
      }
      
      public function reachFrameLabel(param1:String) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 == "showImages")
         {
            if(!mc)
            {
               return;
            }
            _loc2_ = createBosses ? createBosses.length : 0;
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(createBosses[_loc3_].getPositionSprite())
               {
                  createBosses[_loc3_].getPositionSprite().addChild(createBosses[_loc3_]);
                  if(createBosses[_loc3_] is ImageBoss)
                  {
                     (createBosses[_loc3_] as ImageBoss).playStartShowAnimation(null);
                  }
               }
               _loc3_++;
            }
            mc.removeFrameLabelListener(this);
            clear();
         }
      }
      
      public function reachFrameLabel2(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
   }
}

