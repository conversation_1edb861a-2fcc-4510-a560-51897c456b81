package UI.DanMedicinePanel.Btn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class AttackDanSwitchBtn extends SwitchBtn
   {
      
      public function AttackDanSwitchBtn()
      {
         super();
         setTipString("攻击丹");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToAttackDan"));
      }
   }
}

