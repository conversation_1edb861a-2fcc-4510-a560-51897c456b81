package UI
{
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   
   public class EquipmentCellBackground extends MySprite implements IEquipmentCellBackground
   {
      
      private var _id:int;
      
      public function EquipmentCellBackground()
      {
         super();
      }
      
      public function get id() : int
      {
         return _id;
      }
      
      public function set id(param1:int) : void
      {
         _id = param1;
      }
   }
}

