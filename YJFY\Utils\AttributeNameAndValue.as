package YJFY.Utils
{
   public class AttributeNameAndValue
   {
      
      private var m_attributeName:String;
      
      private var m_attributeValue:String;
      
      public function AttributeNameAndValue(param1:String, param2:String)
      {
         super();
         m_attributeName = param1;
         m_attributeValue = param2;
      }
      
      public function getAttributeName() : String
      {
         return m_attributeName;
      }
      
      public function getAttributeValue() : String
      {
         return m_attributeValue;
      }
   }
}

