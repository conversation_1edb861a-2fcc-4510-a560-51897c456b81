package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerTheSocietyData;
   
   public class TEST_DOWN_PlayerTheSocietyData extends DOWN_PlayerTheSocietyData
   {
      
      public function TEST_DOWN_PlayerTheSocietyData()
      {
         super();
         m_informationBodyId = 3007;
      }
      
      public function initData(param1:int, param2:String, param3:int, param4:int, param5:int, param6:int, param7:int, param8:Number, param9:int, param10:String, param11:int, param12:String, param13:int) : void
      {
         m_societyId = param1;
         m_societyName = param2;
         m_personalTotalConValue = param3;
         m_personalReConValue = param4;
         m_societyLevel = param5;
         m_playerNumInSociety = param6;
         m_playerMaxNumInSociety = param7;
         m_uid_leader = param8;
         m_idx_leader = param9;
         m_name_leader = param10;
         m_societyTotalConValue = param11;
         m_announcementOfSociety = param12;
         m_dissolveSocietyRemainTime = param13;
      }
   }
}

