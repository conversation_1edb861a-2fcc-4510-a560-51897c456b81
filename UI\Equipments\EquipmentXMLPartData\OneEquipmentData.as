package UI.Equipments.EquipmentXMLPartData
{
   import UI.DataManagerParent;
   
   public class OneEquipmentData extends DataManagerParent
   {
      
      private var m_equipmentId:String;
      
      private var m_equipmentPrice:Number;
      
      private var m_equipmentPKPrice:Number;
      
      public function OneEquipmentData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_equipmentId = null;
         super.clear();
      }
      
      public function initDataByXML(param1:XML) : void
      {
         this.equipmentId = String(param1.@id);
         this.equipmentPrice = Number(param1.@price);
         this.equipmentPKPrice = Number(param1.@pkPrice);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.equipmentId = m_equipmentId;
         _antiwear.equipmentPrice = m_equipmentPrice;
         _antiwear.equipmentPKPrice = m_equipmentPKPrice;
      }
      
      public function getEquipmentId() : String
      {
         return equipmentId;
      }
      
      public function getEquipmentPrice() : Number
      {
         return equipmentPrice;
      }
      
      public function getEquipmentPKPrice() : Number
      {
         return equipmentPKPrice;
      }
      
      private function get equipmentId() : String
      {
         return _antiwear.equipmentId;
      }
      
      private function set equipmentId(param1:String) : void
      {
         _antiwear.equipmentId = param1;
      }
      
      private function get equipmentPrice() : Number
      {
         return _antiwear.equipmentPrice;
      }
      
      private function set equipmentPrice(param1:Number) : void
      {
         _antiwear.equipmentPrice = param1;
      }
      
      private function get equipmentPKPrice() : Number
      {
         return _antiwear.equipmentPKPrice;
      }
      
      private function set equipmentPKPrice(param1:Number) : void
      {
         _antiwear.equipmentPKPrice = param1;
      }
   }
}

