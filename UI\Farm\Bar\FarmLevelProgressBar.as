package UI.Farm.Bar
{
   import UI.ChangeBar;
   import UI.MyFont.FangZhengKaTongJianTi;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class FarmLevelProgressBar extends ChangeBar
   {
      
      public var upBar:FarmLevelProgressUpBar;
      
      public var progressMask:FarmLevelProgressBarMask;
      
      public var levelText:TextField;
      
      public function FarmLevelProgressBar()
      {
         super();
         _width = upBar.width;
         _heidth = upBar.height;
         levelText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16763904);
         levelText.filters = [new GlowFilter(16777215,1,5,5,1,3)];
         levelText.selectable = false;
         levelText.embedFonts = true;
      }
      
      public function changeProgress(param1:Number, param2:int, param3:int) : void
      {
         changebar(upBar,progressMask,param1,param2,true,15,0);
         levelText.text = param3.toString();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(upBar)
         {
            upBar.clear();
         }
         if(progressMask)
         {
            progressMask.clear();
         }
         upBar = null;
         progressMask = null;
         levelText = null;
      }
   }
}

