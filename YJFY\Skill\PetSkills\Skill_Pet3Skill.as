package YJFY.Skill.PetSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_EveryTargetAddShow2;
   import YJFY.World.World;
   
   public class Skill_Pet3Skill extends CuboidAreaAttackSkill_EveryTargetAddShow2
   {
      
      public function Skill_Pet3Skill()
      {
         super();
      }
      
      override public function startSkill(param1:World) : <PERSON><PERSON>an
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

