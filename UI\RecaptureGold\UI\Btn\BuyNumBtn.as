package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class BuyNumBtn extends Btn
   {
      
      public function BuyNumBtn()
      {
         super();
         setTipString("点击购买次数");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("buyRGNum"));
      }
   }
}

