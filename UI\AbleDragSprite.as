package UI
{
   import YJFY.Utils.ClearUtil;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class AbleDragSprite extends MySprite
   {
      
      public function AbleDragSprite()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
      }
      
      protected function init() : void
      {
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("mouseDown",mouseDownBox,false,0,true);
         addEventListener("mouseUp",mouseUpBox,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("mouseDown",mouseDownBox,false);
         removeEventListener("mouseUp",mouseUpBox,false);
      }
      
      protected function mouseDownBox(param1:MouseEvent) : void
      {
         if(parent)
         {
            if(parent is MyControlPanel)
            {
               parent.setChildIndex(this,parent.getChildIndex((parent as MyControlPanel).topLayer) - 1);
            }
            else
            {
               parent.setChildIndex(this,parent.numChildren - 1);
            }
         }
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      protected function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      protected function test(param1:Event) : void
      {
         var _loc2_:Point = null;
         if(stage)
         {
            _loc2_ = localToGlobal(new Point(mouseX,mouseY));
            if(_loc2_.x > stage.stageWidth)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x -= 10;
            }
            if(_loc2_.x < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               x += 10;
            }
            if(_loc2_.y > stage.stageHeight)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y -= 10;
            }
            if(_loc2_.y < 0)
            {
               dispatchEvent(new MouseEvent("mouseUp"));
               y += 10;
            }
         }
      }
   }
}

