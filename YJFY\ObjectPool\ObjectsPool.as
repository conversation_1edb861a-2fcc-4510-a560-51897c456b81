package YJFY.ObjectPool
{
   import YJFY.Utils.ClearUtil;
   
   public class ObjectsPool
   {
      
      private var m_usedObjs:*;
      
      private var m_wasteObjs:*;
      
      private var m_createFun:Function;
      
      private var m_createFunParams:Array;
      
      public function ObjectsPool(param1:*, param2:*, param3:Function, param4:Array)
      {
         super();
         m_usedObjs = param1;
         m_wasteObjs = param2;
         m_createFun = param3;
         m_createFunParams = param4;
      }
      
      public function clear() : void
      {
         m_usedObjs = null;
         m_wasteObjs = null;
         m_createFun = null;
         ClearUtil.nullArr(m_createFunParams,false,false,false);
         m_createFunParams = null;
      }
      
      public function getOneOrCreateOneObj() : Object
      {
         var _loc1_:Object = null;
         if(m_wasteObjs.length)
         {
            _loc1_ = m_wasteObjs[0];
            m_usedObjs.push(_loc1_);
            m_wasteObjs.splice(0,1);
         }
         else
         {
            _loc1_ = m_createFun.apply(null,m_createFunParams);
            m_usedObjs.push(_loc1_);
         }
         return _loc1_;
      }
      
      public function wasteOneObj(param1:Object) : void
      {
         var _loc2_:int = int(m_usedObjs.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_usedObjs.splice(_loc2_,1);
            _loc2_ = int(m_usedObjs.indexOf(param1));
         }
         m_wasteObjs.push(param1);
      }
      
      public function removeAll() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_usedObjs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_wasteObjs.push(m_usedObjs[_loc2_]);
            _loc2_++;
         }
         m_usedObjs.length = 0;
      }
      
      public function resetCreateFun(param1:Function, param2:Array) : void
      {
         m_createFun = param1;
         ClearUtil.nullArr(m_createFunParams,false,false,false);
         m_createFunParams = param2;
      }
   }
}

