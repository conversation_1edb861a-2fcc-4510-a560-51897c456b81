package YJFY.RichTextArea.IconData
{
   import YJFY.Utils.ClearUtil;
   
   public class IconData
   {
      
      protected var m_iconType:String;
      
      protected var m_iconStr:String;
      
      protected var m_iconName:String;
      
      protected var m_extraData:Object;
      
      public function IconData(param1:String, param2:String)
      {
         super();
         m_iconStr = param1;
         m_iconName = param2;
         m_extraData = {};
      }
      
      public function clear() : void
      {
         ClearUtil.nullObject(m_extraData,false);
         m_extraData = null;
      }
      
      public function getIconType() : String
      {
         return m_iconType;
      }
      
      public function getIconStr() : String
      {
         return m_iconStr;
      }
      
      public function getIconName() : String
      {
         return m_iconName;
      }
      
      public function getExtraData() : Object
      {
         return m_extraData;
      }
   }
}

