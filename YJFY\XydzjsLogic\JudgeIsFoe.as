package YJFY.XydzjsLogic
{
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.IFriendXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.XydzjsData.IEnemyXydzjs;
   
   public class JudgeIsFoe
   {
      
      public function JudgeIsFoe()
      {
         super();
      }
      
      public static function enemyJudgeEntityIsFoe(param1:IEntity) : Boolean
      {
         return param1.getExtra() is IFriendXydzjs && param1.getExtra() is PetXydzjs == false;
      }
      
      public static function friendJudgeEntityIsFoe(param1:IEntity) : Boolean
      {
         return param1.getExtra() is IEnemyXydzjs;
      }
   }
}

