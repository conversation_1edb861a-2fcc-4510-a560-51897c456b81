package YJFY.ShowLogicShell
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.INextStopListener;
   
   public class PlayEndListener implements INextStopListener
   {
      
      public var playEndFun:Function;
      
      public var playEnd2Fun:Function;
      
      public var mc:AnimationShowPlayLogicShell;
      
      public function PlayEndListener()
      {
         super();
      }
      
      public function clear() : void
      {
         mc = null;
         playEndFun = null;
         playEnd2Fun = null;
      }
      
      public function stop() : void
      {
         if(Boolean(playEndFun))
         {
            playEndFun();
         }
         mc.removeNextStopListener(this);
         clear();
      }
      
      public function stop2(param1:AnimationShowPlayLogicShell) : void
      {
         if(<PERSON><PERSON><PERSON>(playEnd2Fun))
         {
            playEnd2Fun();
         }
      }
   }
}

