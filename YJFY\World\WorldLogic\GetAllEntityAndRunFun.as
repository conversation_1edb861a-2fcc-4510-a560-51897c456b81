package YJFY.World.WorldLogic
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.IEntityContainer;
   import YJFY.World.World;
   import YJFY.geom.Area3D.Cuboid;
   
   public class GetAllEntityAndRunFun
   {
      
      public var fun:Function;
      
      public function GetAllEntityAndRunFun()
      {
         super();
      }
      
      public function clear() : void
      {
         fun = null;
      }
      
      public function getAllEntityAndRunFun2(param1:World, param2:IEntity, param3:Cuboid) : void
      {
         var _loc6_:int = 0;
         var _loc5_:IEntity = null;
         var _loc4_:int = int(param1.getAllEntityNum());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = param1.getAllEnttiyByIndex(_loc6_);
            if(_loc5_ != param2)
            {
               runFunInOneEntity(param1,_loc5_,param3);
            }
            _loc6_++;
         }
      }
      
      public function getAllEntityAndRunFun(param1:World, param2:IEntity, param3:Cuboid) : void
      {
         var _loc6_:int = 0;
         var _loc5_:IEntity = null;
         var _loc4_:int = int(param1.getAllEntityNum());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = param1.getAllEnttiyByIndex(_loc6_);
            if(_loc5_ != param2)
            {
               if(_loc5_ is IEntityContainer)
               {
                  getAllEntityAndRunFun_inEntityContainer(_loc5_ as IEntityContainer,param1,param2,param3);
               }
               else
               {
                  runFunInOneEntity(param1,_loc5_,param3);
               }
            }
            _loc6_++;
         }
      }
      
      public function getOneEntityAndRunFun(param1:World, param2:IEntity, param3:Cuboid) : void
      {
         var _loc6_:int = 0;
         var _loc5_:IEntity = null;
         var _loc4_:int = int(param1.getAllEntityNum());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = param1.getAllEnttiyByIndex(_loc6_);
            if(_loc5_ != param2)
            {
               if(_loc5_ is IEntityContainer)
               {
                  getOneEntityAndRunFun_inEntityContainer(_loc5_ as IEntityContainer,param1,param2,param3);
               }
               else
               {
                  runFunInOneEntity3(param1,_loc5_,param3);
               }
            }
            _loc6_++;
         }
      }
      
      private function getOneEntityAndRunFun_inEntityContainer(param1:IEntityContainer, param2:World, param3:IEntity, param4:Cuboid) : void
      {
         var _loc7_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = int(param1.getAllEntityNum());
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc6_ = param1.getAllEnttiyByIndex(_loc7_);
            if(_loc6_ != param3)
            {
               if(_loc6_ is IEntityContainer)
               {
                  getOneEntityAndRunFun_inEntityContainer(_loc6_ as IEntityContainer,param2,param3,param4);
               }
               else
               {
                  runFunInOneEntity3(param2,_loc6_,param4);
               }
            }
            _loc7_++;
         }
      }
      
      private function getAllEntityAndRunFun_inEntityContainer(param1:IEntityContainer, param2:World, param3:IEntity, param4:Cuboid) : void
      {
         var _loc7_:int = 0;
         var _loc6_:IEntity = null;
         var _loc5_:int = int(param1.getAllEntityNum());
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc6_ = param1.getAllEnttiyByIndex(_loc7_);
            if(_loc6_ != param3)
            {
               if(_loc6_ is IEntityContainer)
               {
                  getAllEntityAndRunFun_inEntityContainer(_loc6_ as IEntityContainer,param2,param3,param4);
               }
               else
               {
                  runFunInOneEntity(param2,_loc6_,param4);
               }
            }
            _loc7_++;
         }
      }
      
      private function runFunInOneEntity(param1:World, param2:IEntity, param3:Cuboid) : void
      {
         if(param1.cuboidRangeIsContainsEntity(param3,param2))
         {
            if(Boolean(fun))
            {
               fun(param2);
            }
         }
      }
      
      private function runFunInOneEntity3(param1:World, param2:IEntity, param3:Cuboid) : void
      {
         if(param1.cuboidRangeIsContainsEntity(param3,param2))
         {
            if(Boolean(fun))
            {
               fun(param2);
            }
         }
      }
      
      private function runFunInOneEntity2(param1:World, param2:IEntity, param3:Cuboid) : Boolean
      {
         if(param1.cuboidRangeIsContainsEntity(param3,param2))
         {
            if(Boolean(fun))
            {
               fun(param2);
            }
            return true;
         }
         return false;
      }
   }
}

