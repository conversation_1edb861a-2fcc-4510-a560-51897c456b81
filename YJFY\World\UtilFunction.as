package YJFY.World
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import flash.display.DisplayObjectContainer;
   
   public class UtilFunction
   {
      
      private var m_i:int;
      
      private var m_length:int;
      
      private var m_animation:AnimationShowPlayLogicShell;
      
      private var m_index:int;
      
      public function UtilFunction()
      {
         super();
      }
      
      public function clear() : void
      {
         m_animation = null;
      }
      
      public function addAnimationsFromWaitAdd(param1:Vector.<AnimationShowPlayLogicShell>, param2:Vector.<AnimationShowPlayLogicShell>, param3:DisplayObjectContainer) : void
      {
         while(param1.length)
         {
            m_animation = param1[0];
            addAnimationFromAnimations(m_animation,param2,param3);
            param1.splice(0,1);
         }
      }
      
      public function removeAnimationsFromWaitRemove(param1:Vector.<AnimationShowPlayLogicShell>, param2:Vector.<AnimationShowPlayLogicShell>, param3:DisplayObjectContainer) : void
      {
         while(param1.length)
         {
            m_animation = param1[0];
            removeAniamtionFromAnimations(m_animation,param2,param3);
            param1.splice(0,1);
         }
      }
      
      public function clearOtherAnimations(param1:Vector.<AnimationShowPlayLogicShell>, param2:DisplayObjectContainer) : void
      {
         if(Boolean(param1) == false)
         {
            return;
         }
         if(Boolean(param2) == false)
         {
            return;
         }
         m_length = param1.length;
         m_i = 0;
         while(m_i < m_length)
         {
            if(Boolean(param1[m_i].getDisplayShow()) && param1[m_i].getDisplayShow().parent == param2)
            {
               param2.removeChild(param1[m_i].getDisplayShow());
            }
            param1[m_i] = null;
            ++m_i;
         }
         param1.length = 0;
         param1 = null;
      }
      
      public function addAnimationFromAnimations(param1:AnimationShowPlayLogicShell, param2:Vector.<AnimationShowPlayLogicShell>, param3:DisplayObjectContainer) : void
      {
         m_index = param2.indexOf(param1);
         param3.addChild(param1.getDisplayShow());
         if(m_index != -1)
         {
            return;
         }
         param2.push(param1);
      }
      
      public function removeAniamtionFromAnimations(param1:AnimationShowPlayLogicShell, param2:Vector.<AnimationShowPlayLogicShell>, param3:DisplayObjectContainer) : void
      {
         m_index = param2.indexOf(param1);
         if(Boolean(param1.getDisplayShow()) && param1.getDisplayShow().parent == param3)
         {
            param3.removeChild(param1.getDisplayShow());
         }
         while(m_index != -1)
         {
            param2.splice(m_index,1);
            m_index = param2.indexOf(param1);
         }
      }
   }
}

