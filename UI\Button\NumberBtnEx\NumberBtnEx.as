package UI.Button.NumberBtnEx
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class NumberBtnEx extends Btn
   {
      
      public function NumberBtnEx()
      {
         super();
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickNumberBtnEx"));
      }
   }
}

