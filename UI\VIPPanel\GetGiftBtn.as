package UI.VIPPanel
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GetGiftBtn extends Btn
   {
      
      public function GetGiftBtn()
      {
         super();
         setTipString("点击获取VIP礼包");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGetGiftBtn"));
      }
   }
}

