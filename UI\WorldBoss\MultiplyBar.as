package UI.WorldBoss
{
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import YJFY.Utils.ShowUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class MultiplyBar extends CMSXChangeBarLogicShell
   {
      
      private var m_dataText2:TextField;
      
      private var m_ups:Vector.<Sprite>;
      
      public function MultiplyBar()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_dataText2 = null;
      }
      
      override public function setShow(param1:Sprite, param2:Boolean = true) : void
      {
         var _loc3_:ShowUtil = null;
         super.setShow(param1,param2);
         m_ups = new Vector.<Sprite>();
         var _loc5_:int = 1;
         var _loc4_:Sprite = _show["up" + _loc5_];
         while(_loc4_)
         {
            m_ups.push(_loc4_);
            _loc4_.visible = false;
            _loc5_++;
            _loc4_ = _show["up" + _loc5_];
         }
         _upBar = m_ups[0];
         _upBar.visible = true;
         if(_upBar)
         {
            _upBar.mask = _mask;
         }
         m_dataText2 = _show["dataText2"];
         m_dataText2.mouseEnabled = false;
         if(_dataText != null && param2)
         {
            _loc3_ = new ShowUtil();
            _loc3_.changeTextFieldFont(_font.fontName,m_dataText2);
         }
      }
      
      public function setDataShow2(param1:String) : void
      {
         m_dataText2.text = param1;
      }
      
      public function getColorNum() : int
      {
         return m_ups ? m_ups.length : 0;
      }
      
      public function setColorIndex(param1:int) : void
      {
         if(m_ups == null || param1 < 0 || param1 > m_ups.length - 1)
         {
            return;
         }
         _upBar.mask = null;
         _upBar.visible = false;
         _upBar = m_ups[param1];
         _upBar.mask = _mask;
         _upBar.visible = true;
      }
   }
}

