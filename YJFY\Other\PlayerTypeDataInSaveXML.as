package YJFY.Other
{
   public class PlayerTypeDataInSaveXML
   {
      
      private var m_player1Type:String;
      
      private var m_player2Type:String;
      
      public function PlayerTypeDataInSaveXML(param1:String, param2:String)
      {
         super();
         m_player1Type = param1;
         m_player2Type = param2;
      }
      
      public function clear() : void
      {
         m_player1Type = null;
         m_player2Type = null;
      }
      
      public function getPlayer1Type() : String
      {
         return m_player1Type;
      }
      
      public function getPlayer2Type() : String
      {
         return m_player2Type;
      }
   }
}

