package UI.RefineFactory
{
   import UI.Button.NumberBtn.NumberBtnGroup;
   import UI.Equipments.Equipment;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RefineFactory.Btn.PutAllBtn;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.DropShadowFilter;
   import flash.text.TextField;
   
   public class RefineDataArea extends MySprite
   {
      
      private var _refineTarget:Equipment;
      
      private var _material:Equipment;
      
      private var _numBtnGroup:NumberBtnGroup;
      
      private var _refineNumText:TextField;
      
      private var _putAllBtn:PutAllBtn;
      
      private var _materialMaxNum:int;
      
      private var _enblePutMaterialMaxNum:int;
      
      private var _showWarningBoxFun:Function;
      
      public function RefineDataArea()
      {
         super();
         init();
         oneStepShow();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_refineTarget)
         {
            _refineTarget.clear();
         }
         _refineTarget = null;
         if(_material)
         {
            _material.clear();
         }
         _material = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         _refineNumText = null;
         if(_putAllBtn)
         {
            _putAllBtn.clear();
         }
         _putAllBtn = null;
      }
      
      public function oneStepShow() : void
      {
         while(numChildren > 2)
         {
            removeChildAt(2);
         }
         if(_refineTarget)
         {
            _refineTarget.clear();
         }
         if(_material)
         {
            _material.clear();
         }
         _refineTarget = null;
         _material = null;
         _showWarningBoxFun = null;
      }
      
      public function drawRefineDataArea(param1:Equipment, param2:int, param3:int, param4:int, param5:Equipment, param6:Boolean, param7:XML, param8:Function) : void
      {
         var displayerObject:DisplayObject;
         var font:FangZhengKaTongJianTi;
         var targetNameTextField:TextField;
         var textField1:TextField;
         var textField2:TextField;
         var materialNumText:TextField;
         var refineRate:Number;
         var getMinNum:int;
         var getMaxNum:int;
         var material:Equipment = param1;
         var materialNum:int = param2;
         var materialMaxNum:int = param3;
         var enblePutMaterialMaxNum:int = param4;
         var refreshTarget:Equipment = param5;
         var isShowNumBtnGroup:Boolean = param6;
         var eqXML:XML = param7;
         var showWarningBoxFun:Function = param8;
         while(numChildren > 1)
         {
            displayerObject = getChildAt(1);
            removeChildAt(1);
            if(displayerObject.hasOwnProperty("clear") && displayerObject != _numBtnGroup)
            {
               displayerObject["clear"]();
            }
         }
         font = new FangZhengKaTongJianTi();
         targetNameTextField = new TextField();
         textField1 = new TextField();
         textField2 = new TextField();
         materialNumText = new TextField();
         MyFunction2.setTextFieldAttribute("",targetNameTextField,this,150,280,font.fontName,20,16762997);
         MyFunction2.setTextFieldAttribute("",textField1,this,70,88,font.fontName,18,16762997);
         MyFunction2.setTextFieldAttribute("",textField2,this,70,250,font.fontName,18,16762997);
         MyFunction2.setTextFieldAttribute("",materialNumText,this,160,130,font.fontName,20,16762997);
         targetNameTextField.text = refreshTarget.equipmentVO.name;
         targetNameTextField.width = targetNameTextField.textWidth + 5;
         targetNameTextField.height = targetNameTextField.textHeight + 5;
         textField1.text = "请放入" + material.equipmentVO.name + ":";
         textField1.width = textField1.textWidth + 5;
         textField1.height = textField1.textHeight + 5;
         textField2.text = "炼制结果:";
         textField2.width = textField2.textWidth + 5;
         textField2.height = textField2.textHeight + 5;
         material.x = 100;
         material.y = 140;
         (material as DisplayObject).filters = [new DropShadowFilter(5,45,0,1,5,5,1)];
         material.addEventListener("rollOver",onMouse,false,0,true);
         material.addEventListener("rollOut",onMouse,false,0,true);
         addChild(material as DisplayObject);
         materialNumText.text = materialNum.toString();
         materialNumText.width = materialNumText.textWidth + 5;
         materialNumText.height = materialNumText.textHeight + 5;
         refreshTarget.x = 100;
         refreshTarget.y = 300;
         (refreshTarget as DisplayObject).filters = [new DropShadowFilter(5,45,0,1,5,5,1)];
         refreshTarget.addEventListener("rollOver",onMouse,false,0,true);
         refreshTarget.addEventListener("rollOut",onMouse,false,0,true);
         addChild(refreshTarget as DisplayObject);
         _material = material;
         _refineTarget = refreshTarget;
         _showWarningBoxFun = showWarningBoxFun;
         if(isShowNumBtnGroup)
         {
            _numBtnGroup.minNum = 0;
            _materialMaxNum = materialMaxNum;
            _enblePutMaterialMaxNum = enblePutMaterialMaxNum;
            _numBtnGroup.setMaxNumAndFun(Math.min(materialMaxNum,enblePutMaterialMaxNum),function(param1:String, param2:Function):void
            {
               param2(param1,0);
            },[materialMaxNum <= enblePutMaterialMaxNum ? "背包中的" + _material.equipmentVO.name + "为" + materialMaxNum + "个, 没有更多了！" : "炼制丹药的个数不能超过当前背包空格子的数量。",showWarningBoxFun]);
            _numBtnGroup.num = materialNum;
            _numBtnGroup.x = 130;
            _numBtnGroup.y = 120;
            addChild(_numBtnGroup);
            materialNumText.visible = !isShowNumBtnGroup;
            _putAllBtn = new PutAllBtn();
            _putAllBtn.x = 200;
            _putAllBtn.y = 124;
            addChild(_putAllBtn);
         }
         RefineFactoryData.getInstance().materialID = material.equipmentVO.id;
         if(_numBtnGroup && isShowNumBtnGroup)
         {
            RefineFactoryData.getInstance().materialNum = _numBtnGroup.num;
         }
         RefineFactoryData.getInstance().refineTargetID = refreshTarget.equipmentVO.id;
         refineRate = RefineFactoryFunction.getRefineRate(eqXML,XMLSingle.getInstance().farmXML,RefineFactoryData.getInstance().lianDanFurnace.id,RefineFactoryData.getInstance().refineTargetID,RefineFactoryData.getInstance().materialID,RefineFactoryData.getInstance().materialNum);
         getMinNum = Math.floor(refineRate);
         getMaxNum = Math.ceil(refineRate);
         if(getMaxNum == getMinNum)
         {
            _refineNumText.text = "数量：" + getMinNum;
         }
         else
         {
            _refineNumText.text = "数量：" + Math.floor(refineRate) + "-" + Math.ceil(refineRate);
         }
         _refineNumText.width = _refineNumText.textWidth + 5;
         _refineNumText.height = _refineNumText.textHeight + 5;
      }
      
      private function init() : void
      {
         _refineNumText = new TextField();
         _numBtnGroup = new NumberBtnGroup();
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.setTextFieldAttribute("",_refineNumText,this,160,300,_loc1_.fontName,18);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("changeNum",changeMaterialNum,true,0,true);
         addEventListener("clickPutAllBtn",putAll,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("changeNum",changeMaterialNum,true);
         removeEventListener("clickPutAllBtn",putAll,true);
      }
      
      private function putAll(param1:UIBtnEvent) : void
      {
         _numBtnGroup.num = _numBtnGroup.maxNum;
         if(_materialMaxNum > _enblePutMaterialMaxNum && Boolean(_showWarningBoxFun))
         {
            _showWarningBoxFun("炼制丹药的个数不能超过当前背包空格子的数量。",0);
         }
         changeMaterialNum(null);
      }
      
      private function changeMaterialNum(param1:UIPassiveEvent) : void
      {
         RefineFactoryData.getInstance().materialNum = _numBtnGroup.num;
         var _loc3_:Number = RefineFactoryFunction.getRefineRate(XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML,RefineFactoryData.getInstance().lianDanFurnace.id,RefineFactoryData.getInstance().refineTargetID,RefineFactoryData.getInstance().materialID,RefineFactoryData.getInstance().materialNum);
         var _loc2_:int = Math.floor(_loc3_);
         var _loc4_:int = Math.ceil(_loc3_);
         if(_loc4_ == _loc2_)
         {
            _refineNumText.text = "数量：" + _loc2_;
         }
         else
         {
            _refineNumText.text = "数量：" + Math.floor(_loc3_) + "-" + Math.ceil(_loc3_);
         }
         _refineNumText.width = _refineNumText.textWidth + 5;
         _refineNumText.height = _refineNumText.textHeight + 5;
      }
      
      private function onMouse(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
               break;
            default:
               throw new Error();
         }
      }
   }
}

