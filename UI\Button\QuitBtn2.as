package UI.Button
{
   import UI.Event.UIBtnEvent;
   import flash.display.DisplayObject;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class QuitBtn2 extends Btn
   {
      
      public var quitBtn:SimpleButton;
      
      private var _susPensionSprite:DisplayObject;
      
      public function QuitBtn2()
      {
         super();
         setTipString("关闭");
      }
      
      override public function clear() : void
      {
         super.clear();
         quitBtn = null;
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickQuitBtn"));
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
   }
}

