package UI2.broadcast
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import YJFY.GameEvent;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class TipPanel extends MovieClip
   {
      
      private var m_myloader:YJFYLoader;
      
      private var m_show:MovieClip;
      
      public var isStart:Boolean = false;
      
      private var m_broadrewardpanel:BroadRewardPanel;
      
      private var m_broadactionpanel:BroadActionPanel;
      
      private var m_x:Number;
      
      private var m_y:Number;
      
      private var m_event:String;
      
      public function TipPanel(param1:Number, param2:Number, param3:String)
      {
         super();
         m_x = param1;
         m_y = param2;
         if(param3)
         {
            m_event = param3;
         }
         else
         {
            m_event = "showtip";
         }
         GameEvent.eventDispacher.addEventListener(m_event,callshowtip);
      }
      
      public function clear() : void
      {
         removeEventListener("enterFrame",render);
         GameEvent.eventDispacher.removeEventListener(m_event,callshowtip);
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_broadrewardpanel);
         m_broadrewardpanel = null;
         ClearUtil.clearObject(m_broadactionpanel);
         m_broadactionpanel = null;
      }
      
      public function render(param1:Event) : void
      {
         if(isStart)
         {
            if(m_broadactionpanel)
            {
               m_broadactionpanel.render(null);
            }
         }
      }
      
      public function initTip() : void
      {
         addEventListener("enterFrame",render);
         if(MyFunction2.returnShowByClassName("showtip") as MovieClip == null)
         {
            init();
         }
         else
         {
            initShow();
         }
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function hide() : void
      {
         this.visible = false;
         isStart = false;
      }
      
      public function hideVisible() : void
      {
         this.visible = false;
      }
      
      public function showVisible() : void
      {
         isStart = true;
      }
      
      public function callshowtip(param1:Event) : void
      {
         show();
      }
      
      public function show() : void
      {
         if(m_broadactionpanel)
         {
            m_broadactionpanel.setData(m_event);
         }
      }
      
      public function showbg() : void
      {
         this.visible = true;
         isStart = true;
      }
      
      public function setPosXY(param1:Number, param2:Number) : void
      {
         m_show.x = param1;
         m_show.y = param2;
      }
      
      private function initShow() : void
      {
         if(m_show)
         {
            return;
         }
         m_show = MyFunction2.returnShowByClassName("showtip") as MovieClip;
         m_show.x = m_x;
         m_show.y = m_y;
         addChild(m_show);
         if(m_broadrewardpanel == null)
         {
            m_broadrewardpanel = new BroadRewardPanel();
            m_broadrewardpanel.init(m_show,this);
         }
         if(m_broadactionpanel == null)
         {
            m_broadactionpanel = new BroadActionPanel();
            m_broadactionpanel.init(m_show,this);
         }
         var _loc1_:MovieClip = m_show["tipbg"] as MovieClip;
         if(_loc1_)
         {
            _loc1_.mouseEnabled = false;
            _loc1_.alpha = 0.65;
         }
      }
      
      private function init() : void
      {
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myloader.getClass("UISprite2/tipshow.swf","showtip",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         var _loc3_:MovieClip = new _loc2_();
         BroadDataManager.getInstance().isLoaded = true;
         initShow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      public function hideBtn() : void
      {
         if(m_broadrewardpanel)
         {
            m_broadrewardpanel.hideBtn();
         }
      }
      
      public function showBtn() : void
      {
         if(m_broadrewardpanel)
         {
            m_broadrewardpanel.showBtn();
         }
      }
      
      public function next() : void
      {
         if(m_broadactionpanel)
         {
            m_broadactionpanel.setData(m_event);
         }
      }
      
      public function setData(param1:BroadInfo) : void
      {
         if(m_broadrewardpanel)
         {
            m_broadrewardpanel.setData(param1);
         }
      }
      
      public function getEvent() : String
      {
         return m_event;
      }
   }
}

