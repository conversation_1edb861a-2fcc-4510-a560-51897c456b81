package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class GoodData extends DataManagerParent
   {
      
      private var m_equipmentVO:EquipmentVO;
      
      private var m_conValue:uint;
      
      private var m_maxNum:int;
      
      public function GoodData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_equipmentVO = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.conValue = m_conValue;
         _antiwear.maxNum = m_maxNum;
      }
      
      public function getEquipmentVO() : EquipmentVO
      {
         return m_equipmentVO;
      }
      
      public function setEquipmentVO(param1:EquipmentVO) : void
      {
         m_equipmentVO = param1;
      }
      
      public function getConValue() : uint
      {
         if(_antiwear.conValue == 0)
         {
            throw new Error("购买商品所需贡献值不能为0");
         }
         return _antiwear.conValue;
      }
      
      public function setConValue(param1:uint) : void
      {
         _antiwear.conValue = param1;
      }
      
      public function getMaxNum() : int
      {
         return _antiwear.maxNum;
      }
      
      public function setMaxNum(param1:int) : void
      {
         _antiwear.maxNum = param1;
      }
   }
}

