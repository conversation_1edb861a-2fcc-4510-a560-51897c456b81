package YJFY.StepAttackGame
{
   import YJFY.Utils.ClearUtil;
   
   public class StepAttackGameWorld
   {
      
      private var m_friends:Vector.<IEntity>;
      
      private var m_enemies:Vector.<IEntity>;
      
      private var m_currentAttackEntity:IEntity;
      
      private var m_currentBeAttackedEntities:Vector.<IEntity>;
      
      private var m_getNextStepActionEntities:IGetNextStepActionEntities;
      
      private var m_judgeIsEnd:IJudgeStepAttackGameIsEnd;
      
      private var m_fightNum:uint;
      
      private var m_extraData:Object;
      
      public function StepAttackGameWorld()
      {
         super();
         m_extraData = {};
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_friends,false,false,false);
         m_friends = null;
         ClearUtil.nullArr(m_enemies,false,false,false);
         m_enemies = null;
         m_currentAttackEntity = null;
         ClearUtil.nullArr(m_currentBeAttackedEntities,false,false,false);
         m_currentBeAttackedEntities = null;
         m_getNextStepActionEntities = null;
         m_judgeIsEnd = null;
         ClearUtil.nullObject(m_extraData);
         m_extraData = null;
      }
      
      public function addFriend(param1:IEntity) : void
      {
         if(m_friends == null)
         {
            m_friends = new Vector.<IEntity>();
         }
         if(param1)
         {
            param1.setWorld(this);
         }
         m_friends.push(param1);
      }
      
      public function removeFriend(param1:IEntity) : void
      {
         var _loc3_:* = null;
         if(m_friends == null)
         {
            return;
         }
         var _loc2_:int = int(m_friends.indexOf(param1));
         while(_loc2_ != -1)
         {
            param1 = m_friends.splice(_loc2_,1)[0];
            if(param1)
            {
               param1.setWorld(null);
            }
            _loc2_ = int(m_friends.indexOf(param1));
         }
      }
      
      public function addEnemy(param1:IEntity) : void
      {
         if(m_enemies == null)
         {
            m_enemies = new Vector.<IEntity>();
         }
         if(param1)
         {
            param1.setWorld(this);
         }
         m_enemies.push(param1);
      }
      
      public function removeEnemy(param1:IEntity) : void
      {
         if(m_enemies == null)
         {
            return;
         }
         var _loc2_:int = int(m_enemies.indexOf(param1));
         while(_loc2_ != -1)
         {
            param1 = m_enemies.splice(_loc2_,1)[0];
            if(param1)
            {
               param1.setWorld(null);
            }
            _loc2_ = int(m_enemies.indexOf(param1));
         }
      }
      
      private function removeEntity(param1:IEntity) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_enemies.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_enemies.splice(_loc2_,1);
            _loc2_ = int(m_enemies.indexOf(param1));
         }
         _loc2_ = int(m_friends.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_friends.splice(_loc2_,1);
            _loc2_ = int(m_friends.indexOf(param1));
         }
      }
      
      public function setGetNextStepActionEntities(param1:IGetNextStepActionEntities) : void
      {
         m_getNextStepActionEntities = param1;
      }
      
      public function getGetNextStepActionEnities() : IGetNextStepActionEntities
      {
         return m_getNextStepActionEntities;
      }
      
      public function setJudgeStepAttackGameIsEnd(param1:IJudgeStepAttackGameIsEnd) : void
      {
         m_judgeIsEnd = param1;
      }
      
      public function startAttack() : void
      {
         m_fightNum = 0;
         while(m_judgeIsEnd.judeIsEnd() == false)
         {
            m_fightNum++;
            stepAttack();
         }
      }
      
      public function getExtraData() : Object
      {
         return m_extraData;
      }
      
      protected function stepAttack() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:NextEntities = m_getNextStepActionEntities.getNextAttackAndBeAttackedEntities();
         m_currentAttackEntity = _loc3_.nextAttackEntity;
         m_currentBeAttackedEntities = _loc3_.nextBeAttackedEntities;
         _loc1_ = m_currentBeAttackedEntities ? m_currentBeAttackedEntities.length : 0;
         m_currentAttackEntity.attack(m_currentBeAttackedEntities);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_currentBeAttackedEntities[_loc2_].isDie())
            {
               m_currentBeAttackedEntities[_loc2_].Die();
               removeEntity(m_currentBeAttackedEntities[_loc2_]);
               m_currentBeAttackedEntities.splice(_loc2_,1);
               _loc2_--;
               _loc1_--;
            }
            _loc2_++;
         }
         _loc3_.clear();
      }
      
      public function getFriendNum() : int
      {
         return m_friends ? m_friends.length : 0;
      }
      
      public function getFriendByIndex(param1:int) : IEntity
      {
         if(m_friends == null || param1 < 0 || param1 >= m_friends.length)
         {
            return null;
         }
         return m_friends[param1];
      }
      
      public function getEnemyNum() : int
      {
         return m_enemies ? m_enemies.length : 0;
      }
      
      public function getEnemyByIndex(param1:int) : IEntity
      {
         if(m_enemies == null || param1 < 0 || param1 >= m_enemies.length)
         {
            return null;
         }
         return m_enemies[param1];
      }
      
      public function getCurrentAttackEntity() : IEntity
      {
         return m_currentAttackEntity;
      }
      
      public function getCurrentBeAttackedEntityNum() : int
      {
         return m_currentBeAttackedEntities ? m_currentBeAttackedEntities.length : 0;
      }
      
      public function getCurrentBeAttackedEntityByIndex(param1:int) : IEntity
      {
         if(m_currentBeAttackedEntities == null || param1 < 0 || param1 >= m_currentBeAttackedEntities.length)
         {
            return null;
         }
         return m_currentBeAttackedEntities[param1];
      }
      
      public function getFightNum() : uint
      {
         return m_fightNum;
      }
   }
}

