package YJFY.XydzjsData
{
   import YJFY.Entity.AttackData;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   
   public class AttackDataCalculate
   {
      
      public function AttackDataCalculate()
      {
         super();
      }
      
      public function caculateHurt(param1:IAttackDataCalculateVOXydzjs, param2:IAttackDataCalculateVOXydzjs, param3:AttackData) : void
      {
         var _loc7_:Boolean = false;
         var _loc5_:* = 0;
         var _loc8_:Boolean = false;
         var _loc4_:Number = Math.min(1,Math.max(0,param1.getCriticalRate() - param2.getDeCriticalRate()));
         var _loc9_:Number = Math.min(1,Math.max(0,param2.getDogdeRate() - param1.getHitRate()));
         var _loc6_:Number = Math.random();
         if(_loc6_ < _loc9_)
         {
            _loc8_ = true;
            _loc5_ = 0;
         }
         else
         {
            _loc6_ = Math.random();
            if(_loc6_ < _loc4_)
            {
               _loc5_ = Math.round(Math.max(1,param1.getAttack() - param2.getDefence())) * (1 + param1.getCriticalMulti());
               _loc7_ = true;
            }
            else
            {
               _loc5_ = Math.max(param1.getAttack() - param2.getDefence(),1);
            }
            if(param1 is PlayerXydzjs)
            {
               (param1 as PlayerXydzjs).getUiPlayer().playerVO.petLowXueNu;
            }
         }
         _loc5_ = Math.max(_loc5_ * (1 - param2.getPetPassiveSkillDecHurt() / 100),1);
         param3.resetData(_loc5_,_loc7_,_loc8_,param3.getHurtDuration());
      }
   }
}

