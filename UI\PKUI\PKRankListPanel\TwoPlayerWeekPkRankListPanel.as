package UI.PKUI.PKRankListPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.PKUI.PlayerDataForPK;
   import UI.PKUI.RankListPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class TwoPlayerWeekPkRankListPanel extends RankListPanel
   {
      
      private var m_btnTotal:ButtonLogicShell2 = new ButtonLogicShell2();
      
      private var m_btnMingren:ButtonLogicShell2 = new ButtonLogicShell2();
      
      private var m_remBtn:ButtonLogicShell2;
      
      public function TwoPlayerWeekPkRankListPanel()
      {
         super();
         _isTwoMode = true;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("TwoPKRankList") as MovieClip;
         addChild(m_show);
         m_btnTotal.setShow(m_show["curRankBtn"]);
         m_btnMingren.setShow(m_show["mingRenBtn"]);
         setBtnLock(m_btnTotal);
         super.init(param1,param2);
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_btnTotal);
         m_btnTotal = null;
         ClearUtil.clearObject(m_btnMingren);
         m_btnMingren = null;
      }
      
      override protected function clickButton(param1:ButtonEvent) : void
      {
         super.clickButton(param1);
         switch(param1.button)
         {
            case m_btnMingren:
               setBtnLock(m_btnMingren);
               PlayerDataForPK.getInstance().clearRankListTsData();
               quit();
               m_pageBtnGroup.initPageNumber(1,8);
               _rankId = 1575;
               initRankListData(PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer);
               m_myRankText.text = String(m_curRank);
               m_myScoreText.text = String(PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer);
               break;
            case m_btnTotal:
               setBtnLock(m_btnTotal);
               PlayerDataForPK.getInstance().clearRankListTsData();
               quit();
               m_pageBtnGroup.initPageNumber(1,8);
               _rankId = 1580;
               initRankListData(PlayerDataForPK.getInstance().winMatchForTwoPlayer);
               m_myRankText.text = String(m_curRank);
               m_myScoreText.text = String(PlayerDataForPK.getInstance().winMatchForTwoPlayer);
         }
      }
      
      private function setBtnLock(param1:ButtonLogicShell2) : void
      {
         param1.lock();
         if(m_remBtn)
         {
            m_remBtn.unLock();
         }
         m_remBtn = param1;
      }
   }
}

