package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   
   public class TEST_MemberDataInMemberList extends MemberDataInMemberList
   {
      
      public function TEST_MemberDataInMemberList()
      {
         super();
      }
      
      public function initData(param1:Number, param2:int, param3:String, param4:int, param5:int, param6:int, param7:int, param8:int) : void
      {
         m_uid_member = param1;
         m_idx_member = param2;
         m_name_member = param3;
         m_level_member = param4;
         m_isOnLine_member = param5;
         m_personalReConValue = param6;
         m_personalTotalConValue = param7;
         m_rankByPersonalTotalConValue = param8;
      }
   }
}

