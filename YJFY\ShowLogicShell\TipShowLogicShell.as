package YJFY.ShowLogicShell
{
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObjectContainer;
   import flash.display.InteractiveObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TipShowLogicShell
   {
      
      private var m_tipShow:Sprite;
      
      private var m_tipShowIsEx:Boolean;
      
      private var m_tipClass:Class;
      
      private var m_tipShowParent:DisplayObjectContainer;
      
      private var m_show:InteractiveObject;
      
      public function TipShowLogicShell()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("rollOver",onOver,false);
            m_show.removeEventListener("rollOut",onOut,false);
            m_show = null;
         }
         if(m_tipShowIsEx == false)
         {
            ClearUtil.clearObject(m_tipShow);
         }
         m_tipShow = null;
         m_tipClass = null;
         m_tipShowParent = null;
      }
      
      public function setShow(param1:InteractiveObject) : void
      {
         m_show = param1;
         m_show.addEventListener("rollOver",onOver,false,0,true);
         m_show.addEventListener("rollOut",onOut,false,0,true);
      }
      
      public function init1(param1:Sprite, param2:DisplayObjectContainer) : void
      {
         m_tipShow = param1;
         m_tipShowIsEx = true;
         m_tipShowParent = param2;
      }
      
      public function init2(param1:Class, param2:DisplayObjectContainer) : void
      {
         m_tipClass = param1;
         m_tipShowParent = param2;
      }
      
      public function getShow() : InteractiveObject
      {
         return m_show;
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         if(m_tipShow && m_tipShow.parent)
         {
            m_tipShow.parent.removeChild(m_tipShow);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         if(m_tipShow == null)
         {
            createTipShow();
         }
         if(m_tipShow == null)
         {
            return;
         }
         addToParent();
      }
      
      private function createTipShow() : void
      {
         if(m_tipClass == null)
         {
            return;
         }
         m_tipShow = new m_tipClass();
         m_tipShowIsEx = false;
      }
      
      private function addToParent() : void
      {
         var _loc1_:DisplayObjectContainer = null;
         if(m_show.stage == null)
         {
            return;
         }
         if(m_tipShowParent)
         {
            _loc1_ = m_tipShowParent;
         }
         else
         {
            _loc1_ = m_show.stage;
         }
         if(_loc1_)
         {
            _loc1_.addChild(m_tipShow);
            if(_loc1_.mouseX + 10 + m_tipShow.width > m_show.stage.stageWidth)
            {
               m_tipShow.x = _loc1_.mouseX - 10 - m_tipShow.width;
            }
            else
            {
               m_tipShow.x = _loc1_.mouseX + 10;
            }
            if(_loc1_.mouseY + 10 + m_tipShow.height > m_show.stage.stageHeight)
            {
               m_tipShow.y = _loc1_.mouseY - 10 - m_tipShow.height;
            }
            else
            {
               m_tipShow.y = _loc1_.mouseY + 10;
            }
         }
      }
   }
}

