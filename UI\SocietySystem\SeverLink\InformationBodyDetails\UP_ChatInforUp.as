package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_ChatInforUp extends InformationBodyDetail
   {
      
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_chatStrLength:int;
      
      private var m_chatStr:String;
      
      private var m_extraData:int;
      
      public function UP_ChatInforUp()
      {
         super();
         m_informationBodyId = 3042;
      }
      
      public function initData(param1:Number, param2:int, param3:String, param4:int) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_chatStr = param3;
         m_chatStrLength = new InformationBodyDetailUtil().getStringLength(m_chatStr);
         m_extraData = param4;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_chatStrLength);
         if(m_chatStrLength)
         {
            _loc1_.writeUTFBytes(m_chatStr);
         }
         _loc1_.writeInt(m_extraData);
         return _loc1_;
      }
      
      public function getChatStr() : String
      {
         return m_chatStr;
      }
      
      public function getExtraData() : int
      {
         return m_extraData;
      }
   }
}

