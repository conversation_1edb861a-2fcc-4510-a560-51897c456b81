package YJFY.ToolTip
{
   import YJFY.MySprite;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.filters.DropShadowFilter;
   import flash.filters.GlowFilter;
   import flash.text.Font;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class SmallToolTip extends MySprite
   {
      
      private var m_clear:ClearHelper;
      
      private var m_tipStr:String;
      
      private var m_text:TextField;
      
      private var m_fontSize:int;
      
      private var m_font:Font;
      
      public function SmallToolTip()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         filters = [new DropShadowFilter(3,45,0,1,2,2,1,1)];
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         super.clear();
         m_text = null;
         m_font = null;
      }
      
      public function setTipStr(param1:String) : void
      {
         m_tipStr = param1;
         if(m_text)
         {
            m_text.text = m_tipStr;
            m_text.width = m_text.textWidth + 5;
            m_text.height = m_text.textHeight + 5;
            drawBack();
         }
      }
      
      public function setFont(param1:Font) : void
      {
         var _loc2_:TextFormat = null;
         var _loc3_:String = null;
         m_font = param1;
         if(m_text)
         {
            if(m_font)
            {
               m_text.embedFonts = true;
            }
            _loc2_ = m_text.defaultTextFormat;
            _loc3_ = m_text.text;
            _loc2_.font = m_font.fontName;
            m_text.defaultTextFormat = _loc2_;
            m_text.text = _loc3_;
            m_text.width = m_text.textWidth + 5;
            m_text.height = m_text.textHeight + 5;
            drawBack();
         }
      }
      
      public function render(param1:Number, param2:Number) : void
      {
         if(m_text)
         {
            x = param1;
            y = param2;
         }
      }
      
      public function init() : void
      {
         var _loc1_:String = null;
         if(m_font)
         {
            _loc1_ = m_font.fontName;
         }
         m_fontSize = 15;
         m_text = new TextField();
         addChild(m_text);
         m_text.selectable = false;
         m_text.multiline = true;
         m_text.mouseEnabled = false;
         m_text.filters = [new GlowFilter(16777215,1,2,2,10,2)];
         m_text.defaultTextFormat = new TextFormat(_loc1_,m_fontSize,0);
         if(m_font)
         {
            m_text.embedFonts = true;
         }
         else
         {
            m_text.embedFonts = false;
         }
         if(m_tipStr)
         {
            m_text.text = m_tipStr;
         }
         m_text.width = m_text.textWidth + 5;
         m_text.height = m_text.textHeight + 5;
         drawBack();
      }
      
      private function drawBack() : void
      {
         graphics.clear();
         graphics.lineStyle(1,0,1);
         graphics.beginFill(16777215,1);
         graphics.drawRoundRect(0,0,m_text.width,m_text.textHeight + 3,10,10);
         graphics.endFill();
      }
   }
}

