package UI.Utils.LoadXML
{
   import UI.GamingUI;
   import YJFY.Utils.ClearUtil;
   import deng.fzip.FZipLoading;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.SecurityErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   
   public class MyLoadXML
   {
      
      private var m_xmlPath:String;
      
      private var m_xml:XML;
      
      private var m_loader:URLLoader;
      
      private var m_isLockScreen:Boolean;
      
      private var m_listeners:Vector.<ILoadXMLListener>;
      
      private var m_isAddShared:Boolean;
      
      public function MyLoadXML()
      {
         super();
      }
      
      public function clear() : void
      {
         m_xml = null;
         m_loader = null;
         ClearUtil.nullArr(m_listeners);
         m_listeners = null;
      }
      
      public function setXMLPath(param1:String) : void
      {
         m_xmlPath = param1;
      }
      
      public function getXMLPath() : String
      {
         return m_xmlPath;
      }
      
      public function getXML() : XML
      {
         return m_xml;
      }
      
      public function addLoadXMLListener(param1:ILoadXMLListener) : void
      {
         if(m_listeners == null)
         {
            m_listeners = new Vector.<ILoadXMLListener>();
         }
         m_listeners.push(param1);
      }
      
      public function removeLoadXMLListener(param1:ILoadXMLListener) : void
      {
         if(m_listeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_listeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_listeners.splice(_loc2_,1);
            _loc2_ = int(m_listeners.indexOf(param1));
         }
      }
      
      public function load(param1:Boolean, param2:Boolean = false) : void
      {
         var _loc3_:XML = null;
         var _loc4_:String = null;
         m_isLockScreen = param1;
         m_isAddShared = param2;
         if(Boolean(m_xmlPath) == false)
         {
            throw new Error("xml路径未赋正确值！");
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            _loc3_ = GamingUI.getInstance().getSharedXMLs().getXML("CodeXML/" + m_xmlPath);
         }
         else
         {
            _loc3_ = GamingUI.getInstance().getSharedXMLs().getXML("XML/" + m_xmlPath);
         }
         if(_loc3_)
         {
            m_xml = _loc3_;
            loadSuccess();
            return;
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            try
            {
               _loc4_ = MyBase64.getReallyDate(FZipLoading.dic[m_xmlPath]);
            }
            catch(e:Error)
            {
               throw new Error("配置表解密错误" + m_xmlPath,43999);
            }
            m_xml = new XML(_loc4_);
            GamingUI.getInstance().getSharedXMLs().addXML("CodeXML/" + m_xmlPath,m_xml);
            loadSuccess();
            return;
         }
         var _loc5_:String = GamingUI.getInstance().getVersionControl().getXMLVersionStr();
         if(m_loader == null)
         {
            m_loader = new URLLoader();
         }
         m_loader.addEventListener("securityError",securityErrorHandler,false);
         m_loader.addEventListener("ioError",ioError,false);
         m_loader.addEventListener("complete",complete,false);
         if(param1)
         {
            GamingUI.getInstance().mouseChildren = false;
            GamingUI.getInstance().mouseEnabled = false;
            if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
            {
               GamingUI.getInstance().manBan.text.text = "处理中...";
            }
         }
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            m_loader.load(new URLRequest("CodeXML" + _loc5_ + "/" + m_xmlPath + "?v=" + Math.random() * 999999));
         }
         else
         {
            m_loader.load(new URLRequest("XML" + _loc5_ + "/" + m_xmlPath + "?v=" + Math.random() * 999999));
         }
      }
      
      private function complete(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",complete,false);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler,false);
         param1.currentTarget.removeEventListener("ioError",ioError,false);
         var _loc2_:String = (param1.currentTarget as URLLoader).data;
         if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
         {
            try
            {
               _loc2_ = MyBase64.getReallyDate(_loc2_);
            }
            catch(e:Error)
            {
               throw new Error("配置表解密错误" + m_xmlPath,43999);
            }
         }
         var _loc3_:XML = new XML(_loc2_);
         if(m_isLockScreen)
         {
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
         m_xml = _loc3_;
         if(m_isAddShared)
         {
            if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
            {
               GamingUI.getInstance().getSharedXMLs().addXML("CodeXML/" + m_xmlPath,m_xml);
            }
            else
            {
               GamingUI.getInstance().getSharedXMLs().addXML("XML/" + m_xmlPath,m_xml);
            }
         }
         loadSuccess();
      }
      
      private function loadSuccess() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_listeners ? m_listeners.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_listeners[_loc2_])
            {
               m_listeners[_loc2_].loadSuccess(m_xml);
            }
            _loc2_++;
         }
         ClearUtil.nullArr(m_listeners,false,false,false);
         m_listeners = null;
      }
      
      private function securityErrorHandler(param1:SecurityErrorEvent) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",complete,false);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler,false);
         param1.currentTarget.removeEventListener("ioError",ioError,false);
         var _loc2_:int = m_listeners ? m_listeners.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_listeners[_loc3_])
            {
               m_listeners[_loc3_].errorHandler();
            }
            if(m_listeners[_loc3_])
            {
               m_listeners[_loc3_].securityError();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(m_listeners,false,false,false);
         m_listeners = null;
         if(m_isLockScreen)
         {
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
      
      private function ioError(param1:IOErrorEvent) : void
      {
         var _loc3_:int = 0;
         param1.currentTarget.removeEventListener("complete",complete,false);
         param1.currentTarget.removeEventListener("securityError",securityErrorHandler,false);
         param1.currentTarget.removeEventListener("ioError",ioError,false);
         var _loc2_:int = m_listeners ? m_listeners.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_listeners[_loc3_])
            {
               m_listeners[_loc3_].errorHandler();
            }
            if(m_listeners[_loc3_])
            {
               m_listeners[_loc3_].ioError();
            }
            _loc3_++;
         }
         ClearUtil.nullArr(m_listeners,false,false,false);
         m_listeners = null;
         if(m_isLockScreen)
         {
            GamingUI.getInstance().mouseChildren = true;
            GamingUI.getInstance().mouseEnabled = true;
         }
      }
   }
}

