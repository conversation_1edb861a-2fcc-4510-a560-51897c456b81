package UI.Button.SwitchBtn.ShopWallSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class ShopWall_XianShiGoodBtn extends SwitchBtn
   {
      
      public function ShopWall_XianShiGoodBtn()
      {
         super();
         setTipString("新品");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopWallSwitchBtn"));
      }
   }
}

