package UI.TaskPanel.Bar
{
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.TaskPanel.InTaskPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class NewYearTaskBar extends MySprite implements ITaskBar
   {
      
      private var _show:Sprite;
      
      private var _bar:CMSXChangeBarLogicShell;
      
      public function NewYearTaskBar()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(this);
         _show = null;
         if(_bar)
         {
            _bar.clear();
         }
         _bar = null;
      }
      
      public function setInTaskPanel(param1:InTaskPanel) : void
      {
      }
      
      public function change(param1:Number, param2:int = 0) : void
      {
         _bar.change(param1);
         _bar.setDataShow(Math.round(param1 * param2) + "/" + param2);
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("NiuYearBar") as Sprite;
         addChild(_show);
         _bar = new CMSXChangeBarLogicShell();
         _bar.setShow(_show);
      }
   }
}

