package UI.ConsumptionGuide.Button
{
   import UI.ConsumptionGuide.GuideUseEquipment;
   
   public class BuyPlayerBloodPotionGuideBtn extends GuideBtn
   {
      
      public function BuyPlayerBloodPotionGuideBtn(param1:int, param2:Function = null)
      {
         super(param1,param2);
         var _loc3_:GuideUseEquipment = new GuideUseEquipment();
         _loc3_.setShowWarningFun(_showWarningFun);
         _loc3_.setUseAfterFun(param2);
         _loc3_.addUseEqId("11000008");
         _loc3_.addUseEqId("11000017");
         setGuideEquipment(_loc3_);
      }
   }
}

