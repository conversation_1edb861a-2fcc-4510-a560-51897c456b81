package UI.TaskPanel
{
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.QuitBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ScrollBar;
   import UI.Task.TaskGoalVO;
   import UI.Task.TaskReward.TaskRewardVO_Equipment;
   import UI.Task.TaskReward.TaskRewardVO_Experience;
   import UI.Task.TaskReward.TaskRewardVO_LSHSHI;
   import UI.Task.TaskReward.TaskRewardVO_Money;
   import UI.Task.TaskReward.TaskRewardVO_ZHHJZH;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.EveryDayTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.TaskPanel.Button.AcceptBtn;
   import UI.TaskPanel.Button.ActivityTaskBtn;
   import UI.TaskPanel.Button.EveryDayTaskBtn;
   import UI.TaskPanel.Button.ResetTaskBtn;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ExTaskPanel extends MySprite
   {
      
      public static const TASK_NUM_ONE_PAGE:int = 5;
      
      public static const TASK_START_NUM:int = 1;
      
      public static const EVERY_DAY_TASK_STATE:int = 0;
      
      public static const ACTIVITY_TASK_STATE:int = 1;
      
      public var acceptBtn:AcceptBtn;
      
      public var dataLayer:DataLayer_ExTaskPanel;
      
      public var dataMask:Sprite;
      
      public var slider:Sprite;
      
      public var downControl:Sprite;
      
      public var upControl:Sprite;
      
      public var scroll_bg:Sprite;
      
      public var everyDayTaskBtn:EveryDayTaskBtn;
      
      public var activityTaskBtn:ActivityTaskBtn;
      
      public var quitBtn:QuitBtn;
      
      private const TASK_COLUME_START_X:int = 55;
      
      private const TASK_COLUME_START_Y:int = 169;
      
      private var _currentSelectedTaskIndex:int;
      
      private var _myScroll:ScrollBar;
      
      private var _pageBtnGroup:PageBtnGroup;
      
      private var _resetTaskBtn:ResetTaskBtn;
      
      private var _currentState:int;
      
      private var _currentTaskVOs:Vector.<MTaskVO>;
      
      public var m_bIsIn:Boolean = false;
      
      public function ExTaskPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function refreshTaskList() : void
      {
         setPanelPage(_pageBtnGroup.pageNum);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(acceptBtn)
         {
            acceptBtn.clear();
         }
         acceptBtn = null;
         if(dataLayer)
         {
            dataLayer.clear();
         }
         dataLayer = null;
         dataMask = null;
         slider = null;
         downControl = null;
         upControl = null;
         scroll_bg = null;
         _myScroll = null;
         if(_pageBtnGroup)
         {
            _pageBtnGroup.clear();
         }
         _pageBtnGroup = null;
         if(everyDayTaskBtn)
         {
            everyDayTaskBtn.clear();
         }
         everyDayTaskBtn = null;
         if(activityTaskBtn)
         {
            activityTaskBtn.clear();
         }
         activityTaskBtn = null;
         _currentTaskVOs = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_myScroll)
         {
            _myScroll.clear();
         }
         _myScroll = null;
         if(_resetTaskBtn)
         {
            _resetTaskBtn.clear();
         }
         _resetTaskBtn = null;
      }
      
      private function setPanelPage(param1:int) : void
      {
         var _loc2_:int = 0;
         _loc2_ = int(_currentTaskVOs.length);
         if(!_loc2_)
         {
            _pageBtnGroup.initPageNumber(1,1);
            return;
         }
         if(_loc2_ % 5)
         {
            _pageBtnGroup.initPageNumber(param1,int(_loc2_ / 5) + 1);
         }
         else
         {
            _pageBtnGroup.initPageNumber(param1,_loc2_ / 5);
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickTaskColume",refreshTaskData,true,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("clickAcceptTaskBtn",acceptTask,true,0,true);
         addEventListener("clickTaskColume",switchTaskColume,true,0,true);
         addEventListener("switchEveryDayTask",switchToEveryDayTask,true,0,true);
         addEventListener("switchToActivityTask",switchToActivityTask,true,0,true);
         addEventListener("clickResetTaskBtn",showResetTaskBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickTaskColume",refreshTaskData,true);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("clickAcceptTaskBtn",acceptTask,true);
         removeEventListener("clickTaskColume",switchTaskColume,true);
         removeEventListener("switchEveryDayTask",switchToEveryDayTask,true);
         removeEventListener("switchToActivityTask",switchToActivityTask,true);
         removeEventListener("clickResetTaskBtn",showResetTaskBox,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      private function showResetTaskBox(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         if(getChildByName("buyTask"))
         {
            return;
         }
         if(m_bIsIn == true)
         {
            return;
         }
         m_bIsIn = true;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc3_:String = MyFunction.getInstance().splitTimeString(param1);
            if(TasksManager.getInstance().resetDate != _loc3_)
            {
               TasksManager.getInstance().resetCount = 0;
               TasksManager.getInstance().resetDate = _loc3_;
            }
            m_bIsIn = false;
            var _loc2_:ResetCompletedEveryDayTaskBox = new ResetCompletedEveryDayTaskBox();
            _loc2_.x = (stage.stageWidth - _loc2_.width) / 2;
            _loc2_.y = (stage.stageHeight - _loc2_.height) / 2;
            _loc2_.name = "buyTask";
            addChild(_loc2_);
         },function():void
         {
            m_bIsIn = false;
            GamingUI.getInstance().showMessageTip("服务器时间加载失败");
         },true);
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function switchToEveryDayTask(param1:UIBtnEvent) : void
      {
         activityTaskBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(everyDayTaskBtn),getChildIndex(activityTaskBtn));
         setChildIndex(activityTaskBtn,_loc2_);
         currentState = 0;
         ableClickReset();
         setPanelPage(1);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function switchToActivityTask(param1:UIBtnEvent) : void
      {
         everyDayTaskBtn.gotoTwoFrame();
         var _loc2_:int = Math.min(getChildIndex(everyDayTaskBtn),getChildIndex(activityTaskBtn));
         setChildIndex(everyDayTaskBtn,_loc2_);
         currentState = 1;
         NoAbleClickReset();
         setPanelPage(1);
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      public function set currentState(param1:int) : void
      {
         switch(param1)
         {
            case 0:
               _currentState = 0;
               _currentTaskVOs = TasksManager.getInstance().taskVOs;
               break;
            case 1:
               _currentState = 1;
               _currentTaskVOs = TasksManager.getInstance().activityTaskVOs;
         }
      }
      
      private function switchTaskColume(param1:UIBtnEvent) : void
      {
         var _loc3_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc2_:int = numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = getChildAt(_loc4_);
            if(_loc3_ is ExTaskColume && _loc3_ != param1.target)
            {
               (_loc3_ as ExTaskColume).gotoTwoFrame();
            }
            _loc4_++;
         }
      }
      
      private function acceptTask(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         if(_currentTaskVOs.length)
         {
            switch(_currentState)
            {
               case 0:
                  if(TasksManager.getInstance().acceptedEveryDayTaskVOs.length >= XMLSingle.getInstance().everyDayTaskMaxNum)
                  {
                     showWarningBox("无法再接受更多任务了!",0);
                     return;
                  }
                  MyFunction2.getServerTimeFunction(function(param1:String):void
                  {
                     TasksManager.getInstance().acceptedEveryDayTaskVOs.push(_currentTaskVOs[_currentSelectedTaskIndex]);
                     dealWithDate(param1);
                  },showWarningBox,true);
                  break;
               case 1:
                  if(TasksManager.getInstance().acceptedActivityTaskVOs.length >= XMLSingle.getInstance().activityTaskMaxNum)
                  {
                     showWarningBox("无法再接受更多任务了!",0);
                     return;
                  }
                  MyFunction2.getServerTimeFunction(function(param1:String):void
                  {
                     TasksManager.getInstance().acceptedActivityTaskVOs.push(_currentTaskVOs[_currentSelectedTaskIndex]);
                     dealWithDate(param1);
                  },showWarningBox,true);
                  break;
            }
         }
      }
      
      private function dealWithDate(param1:String) : void
      {
         var _loc2_:String = MyFunction.getInstance().splitTimeString(param1);
         var _loc3_:String = _currentTaskVOs[_currentSelectedTaskIndex].name;
         switch(_currentTaskVOs[_currentSelectedTaskIndex].type)
         {
            case "everyDayTask":
               (_currentTaskVOs[_currentSelectedTaskIndex] as EveryDayTaskVO).receiveTaskDate = _loc2_;
               break;
            case "limitingTimeTask":
               break;
            case "accumulatedTask":
            case "limitingTimeAccumulatedTask":
               (_currentTaskVOs[_currentSelectedTaskIndex] as AccumulatedTaskVO).deteDate = _loc2_;
               break;
            default:
               throw new Error();
         }
         _currentTaskVOs.splice(_currentSelectedTaskIndex,1);
         showWarningBox("您成功接受了“" + _loc3_ + "”任务!!",0);
         refreshTaskList();
      }
      
      private function pageUp(param1:UIBtnEvent) : void
      {
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function pageDown(param1:UIBtnEvent) : void
      {
         arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
      }
      
      private function refreshTaskData(param1:UIBtnEvent = null) : void
      {
         var _loc4_:* = undefined;
         var _loc5_:TextField = null;
         var _loc8_:MTaskVO = null;
         var _loc3_:FangZhengKaTongJianTi = null;
         var _loc12_:* = undefined;
         var _loc9_:int = 0;
         var _loc14_:int = 0;
         var _loc7_:TaskRewardVO_Equipment = null;
         var _loc13_:TaskRewardVO_Experience = null;
         var _loc11_:TaskRewardVO_Money = null;
         var _loc10_:TaskRewardVO_ZHHJZH = null;
         var _loc2_:TaskRewardVO_LSHSHI = null;
         var _loc15_:String = "";
         var _loc16_:String = "";
         var _loc6_:Vector.<TextField> = new Vector.<TextField>();
         if(param1)
         {
            _loc8_ = param1.data;
         }
         else if(_currentTaskVOs.length)
         {
            try
            {
               _loc8_ = _currentTaskVOs[(_pageBtnGroup.pageNum - 1) * 5];
            }
            catch(e:Error)
            {
               if(_currentTaskVOs.length)
               {
                  _loc8_ = _currentTaskVOs[0];
               }
               else
               {
                  _loc8_ = null;
               }
               setPanelPage(_pageBtnGroup.pageNum - 1);
               arrangeTask((_pageBtnGroup.pageNum - 1) * 5);
            }
         }
         if(_loc8_)
         {
            _loc3_ = new FangZhengKaTongJianTi();
            _currentSelectedTaskIndex = _currentTaskVOs.indexOf(_loc8_);
            _loc16_ = _loc8_.description;
            _loc12_ = XMLSingle.getTaskGoals(_loc8_.taskGoalVO_ids,XMLSingle.getInstance().taskXML);
            _loc9_ = 0;
            _loc14_ = int(_loc12_.length);
            _loc9_ = 0;
            while(_loc9_ < _loc14_)
            {
               _loc15_ += "" + (_loc9_ + 1) + "." + _loc12_[_loc9_].name + "×" + _loc8_.taskGoalVO_nums[_loc9_] + " \n";
               _loc9_++;
            }
            _loc14_ = int(_loc8_.taskRewardVOs.length);
            _loc9_ = 0;
            while(_loc9_ < _loc14_)
            {
               switch(_loc8_.taskRewardVOs[_loc9_].type)
               {
                  case "equipmentReward":
                     _loc7_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Equipment;
                     _loc4_ = XMLSingle.getEquipmentVOsIDs(_loc7_.equipments_IDs,XMLSingle.getInstance().equipmentXML,_loc7_.equipments_Nums,_loc7_.isBinding);
                     break;
                  case "experienceReward":
                  case "moneyReward":
                  case "zhHJZHRward":
                  case "lSHSHRward":
                     _loc5_ = new TextField();
                     _loc5_.selectable = false;
                     _loc5_.defaultTextFormat = new TextFormat(_loc3_.fontName,20,16777215);
                     _loc5_.embedFonts = true;
                     _loc5_.wordWrap = true;
                     _loc5_.width = 475;
                     break;
                  default:
                     throw new Error("类型错误");
               }
               switch(_loc8_.taskRewardVOs[_loc9_].type)
               {
                  case "experienceReward":
                     _loc13_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Experience;
                     _loc5_.text = _loc13_.description + "×" + _loc13_.value;
                     break;
                  case "moneyReward":
                     _loc11_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Money;
                     _loc5_.text = _loc11_.description + "×" + _loc11_.value;
                     break;
                  case "zhHJZHRward":
                     _loc10_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_ZHHJZH;
                     _loc5_.text = _loc10_.description + "×" + _loc10_.value;
                     break;
                  case "lSHSHRward":
                     _loc2_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_LSHSHI;
                     _loc5_.text = _loc2_.description + "×" + _loc2_.value;
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               _loc5_.width = Math.min(_loc5_.textWidth + 5,475);
               _loc5_.height = _loc5_.textHeight + 5;
               _loc6_.push(_loc5_);
               _loc9_++;
            }
         }
         dataLayer.refreshDataLayer(_loc16_,_loc15_,_loc4_,_loc6_);
         _myScroll.refresh();
      }
      
      private function arrangeTask(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc7_:* = 0;
         var _loc3_:ExTaskColume = null;
         var _loc6_:DisplayObject = null;
         var _loc2_:int = param1 + 5;
         _loc7_ = 0;
         while(_loc7_ < numChildren)
         {
            _loc6_ = getChildAt(_loc7_);
            if(_loc6_ is ExTaskColume)
            {
               removeChildAt(_loc7_);
               (_loc6_ as ExTaskColume).clear();
               _loc7_--;
            }
            _loc7_++;
         }
         _loc4_ = int(_currentTaskVOs.length);
         _loc7_ = param1;
         while(_loc7_ < _loc2_ && _loc7_ < _loc4_)
         {
            _loc3_ = new ExTaskColume();
            _loc3_.x = 55;
            _loc3_.y = 169 + _loc3_.height * (_loc7_ - param1);
            _loc3_.TaskVO = _currentTaskVOs[_loc7_];
            addChild(_loc3_);
            _loc7_++;
         }
         var _loc5_:int = 0;
         _loc4_ = numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc6_ = getChildAt(_loc7_);
            if(_loc6_ is ExTaskColume)
            {
               if(!_loc5_)
               {
                  (_loc6_ as ExTaskColume).init(false);
               }
               else
               {
                  (_loc6_ as ExTaskColume).init(true);
               }
               _loc5_++;
            }
            _loc7_++;
         }
         refreshTaskData();
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function init() : void
      {
         _myScroll = new ScrollBar(dataLayer,dataMask,slider,scroll_bg);
         _myScroll.direction = "L";
         _myScroll.tween = 5;
         _myScroll.elastic = false;
         _myScroll.lineAbleClick = true;
         _myScroll.mouseWheel = true;
         _myScroll.UP = upControl;
         _myScroll.DOWN = downControl;
         _myScroll.stepNumber = 15;
         _myScroll.refresh();
         _resetTaskBtn = new ResetTaskBtn();
         _resetTaskBtn.x = 51.85;
         _resetTaskBtn.y = 476.35;
         addChild(_resetTaskBtn);
         currentState = 0;
         ableClickReset();
         _pageBtnGroup = new PageBtnGroup();
         setPanelPage(1);
         _pageBtnGroup.x = 170;
         _pageBtnGroup.y = 490;
         addChild(_pageBtnGroup);
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         everyDayTaskBtn.init(false);
         activityTaskBtn.init(true);
      }
      
      private function ableClickReset() : void
      {
         _resetTaskBtn.gotoAndStop("1");
         _resetTaskBtn.mouseEnabled = true;
         _resetTaskBtn.mouseChildren = true;
      }
      
      private function NoAbleClickReset() : void
      {
         _resetTaskBtn.gotoAndStop("3");
         _resetTaskBtn.mouseEnabled = false;
         _resetTaskBtn.mouseChildren = false;
      }
   }
}

