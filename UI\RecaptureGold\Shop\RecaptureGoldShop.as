package UI.RecaptureGold.Shop
{
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.Parent.IProp;
   import UI.RecaptureGold.PlayerPropVO;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import UI.RecaptureGold.RecaptureGoldMain;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class RecaptureGoldShop extends MySprite
   {
      
      private var _sayText:TextField;
      
      private var _currentGoldNumText:TextField;
      
      private var _propDescriptionText:TextField;
      
      private var _shopXML:XML;
      
      private var _recaptureGoldXML:XML;
      
      private var _recaptureGoldMain:RecaptureGoldMain;
      
      private var _playerPropVO:PlayerPropVO;
      
      public function RecaptureGoldShop(param1:XML, param2:RecaptureGoldMain, param3:PlayerPropVO)
      {
         super();
         _recaptureGoldXML = param1;
         _playerPropVO = param3;
         _recaptureGoldMain = param2;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         _sayText = null;
         _currentGoldNumText = null;
         _propDescriptionText = null;
         _shopXML = null;
         _recaptureGoldXML = null;
         _recaptureGoldMain = null;
         _playerPropVO = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("showPropDescription",showPropDescription,true,0,true);
         addEventListener("buyProp",buyProp,true,0,true);
         addEventListener("clickQuitGameBtn",clickQuitBtn,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("showPropDescription",showPropDescription,true);
         removeEventListener("buyProp",buyProp,true);
         removeEventListener("clickQuitGameBtn",clickQuitBtn,true);
      }
      
      private function clickQuitBtn(param1:Event) : void
      {
         var shop:RecaptureGoldShop;
         var e:Event = param1;
         if(_recaptureGoldMain)
         {
            shop = this;
            _recaptureGoldMain.showWarningBox("是否要放弃继续游戏， 上缴当前的黄金退出游戏？",1 | 2,{"okFunction":function():void
            {
               shop.dispatchEvent(new RecaptureGoldEvent("quitGame"));
            }});
         }
         else
         {
            dispatchEvent(new RecaptureGoldEvent("quitGame"));
         }
      }
      
      private function showPropDescription(param1:RecaptureGoldEvent) : void
      {
         _propDescriptionText.text = param1.data;
      }
      
      private function buyProp(param1:RecaptureGoldEvent) : void
      {
         var _loc2_:RecaptureGoldShopColume = null;
         if(RecaptureGoldData.getInstance().allGoldNum < param1.data.price)
         {
            _recaptureGoldMain.showWarningBox("银两不足！",0);
            return;
         }
         if(_playerPropVO.addProp(param1.data.prop,_recaptureGoldXML))
         {
            RecaptureGoldData.getInstance().allGoldNum = RecaptureGoldData.getInstance().allGoldNum - param1.data.price;
            _currentGoldNumText.text = RecaptureGoldData.getInstance().allGoldNum + "两";
            _loc2_ = param1.target as RecaptureGoldShopColume;
            removeChild(_loc2_);
            _loc2_.clear();
            _loc2_ = null;
            _propDescriptionText.text = "";
            _recaptureGoldMain.showWarningBox("购买成功！",0);
         }
         else
         {
            _recaptureGoldMain.showWarningBox("购买失败, 你已经有了3个炸弹！",0);
         }
      }
      
      private function init() : void
      {
         var show:DisplayObject = MyFunction2.returnShowByClassName("UI.RecaptureGold.Shop.RecaptureGoldShopShow");
         addChild(show);
         _sayText = show["sayText"];
         _currentGoldNumText = show["currentGoldNumText"];
         _propDescriptionText = show["propDescriptionText"];
         MyFunction2.loadXMLFunction("recaptureGoldShop",function(param1:XML):void
         {
            _shopXML = param1;
            _currentGoldNumText.text = RecaptureGoldData.getInstance().allGoldNum + "两";
            var _loc3_:XMLList = _shopXML.sayText[0].item;
            var _loc4_:int = int(_loc3_ ? _loc3_.length() : 0);
            var _loc2_:int = Math.random() * _loc4_;
            _sayText.text = String(_loc3_[_loc2_]);
            arrange();
         },null,true);
      }
      
      public function arrange() : void
      {
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:DisplayObject = null;
         var _loc2_:Number = NaN;
         var _loc1_:Number = NaN;
         var _loc3_:RecaptureGoldShopColume = null;
         _loc8_ = 0;
         while(_loc8_ < numChildren)
         {
            _loc7_ = getChildAt(_loc8_);
            if(_loc7_ is RecaptureGoldShopColume)
            {
               removeChildAt(_loc8_);
               _loc8_--;
               (_loc7_ as RecaptureGoldShopColume).clear();
            }
            _loc8_++;
         }
         var _loc5_:XMLList = _recaptureGoldXML.PropData[0].item;
         var _loc6_:int = 0;
         _loc4_ = int(_loc5_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc2_ = Number(_loc5_[_loc8_].@shopApearPro);
            _loc1_ = Math.random();
            if(_loc1_ <= _loc2_)
            {
               _loc3_ = new RecaptureGoldShopColume(_loc5_[_loc8_],MyFunction2.returnShowByClassName(_loc5_[_loc8_].@classNameForShow) as IProp);
               _loc3_.x = 150 + _loc6_ * 120;
               _loc3_.y = 230;
               addChild(_loc3_);
               _loc6_++;
            }
            _loc8_++;
         }
      }
   }
}

