package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.IEntity;
   
   public class TimeLimitEntity_MonkeySkill3Listener
   {
      
      public var pushEntityFun:Function;
      
      public function TimeLimitEntity_MonkeySkill3Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         pushEntityFun = null;
      }
      
      public function pushEntity(param1:TimeLimitEntity_MonkeySkill3, param2:IEntity) : void
      {
         if(<PERSON><PERSON><PERSON>(pushEntityFun))
         {
            pushEntityFun(param1,param2);
         }
      }
   }
}

