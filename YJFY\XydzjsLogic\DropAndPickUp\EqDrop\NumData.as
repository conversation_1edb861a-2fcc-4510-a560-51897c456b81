package YJFY.XydzjsLogic.DropAndPickUp.EqDrop
{
   import UI.DataManagerParent;
   
   public class NumData extends DataManagerParent
   {
      
      private var m_proWeight:Number;
      
      private var m_upPro:Number;
      
      private var m_downPro:Number;
      
      private var m_num:uint;
      
      public function NumData(param1:uint, param2:Number, param3:Number, param4:Number)
      {
         super();
         this.num = param1;
         this.proWeight = param2;
         this.upPro = param3;
         this.downPro = param4;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.proWeight = m_proWeight;
         _antiwear.upPro = m_upPro;
         _antiwear.downPro = m_downPro;
         _antiwear.num = m_num;
      }
      
      public function getNum() : uint
      {
         return num;
      }
      
      public function getProWeight() : Number
      {
         return proWeight;
      }
      
      public function getUpPro() : Number
      {
         return upPro;
      }
      
      public function getDownPro() : Number
      {
         return downPro;
      }
      
      private function set proWeight(param1:Number) : void
      {
         _antiwear.proWeight = param1;
      }
      
      private function get proWeight() : Number
      {
         return _antiwear.proWeight;
      }
      
      private function set upPro(param1:Number) : void
      {
         _antiwear.upPro = param1;
      }
      
      private function get upPro() : Number
      {
         return _antiwear.upPro;
      }
      
      private function set downPro(param1:Number) : void
      {
         _antiwear.downPro;
      }
      
      private function get downPro() : Number
      {
         return _antiwear.downPro;
      }
      
      private function set num(param1:uint) : void
      {
         _antiwear.num = param1;
      }
      
      private function get num() : uint
      {
         return _antiwear.num;
      }
   }
}

