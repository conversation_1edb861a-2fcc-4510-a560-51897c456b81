package YJFY.ShowLogicShell.SwitchBtn
{
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class SwitchBtnGroupLogicShell
   {
      
      private var _switchBtns:Array;
      
      private var _switchBtnLSClass:Class;
      
      private var _extraData:Object;
      
      public function SwitchBtnGroupLogicShell(param1:Class)
      {
         super();
         _switchBtnLSClass = param1;
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_switchBtns);
         _switchBtnLSClass = null;
         _extraData = null;
      }
      
      public function addSwitchBtnShow(param1:Sprite, param2:Object) : void
      {
         var _loc3_:SwitchBtnLogicShell = new _switchBtnLSClass();
         _loc3_.setShow(param1);
         _loc3_.data = param2;
         if(_switchBtns == null)
         {
            _switchBtns = [];
         }
         _switchBtns.push(_loc3_);
         _loc3_.switchBtns = _switchBtns;
      }
      
      public function addSwitchBtn(param1:SwitchBtnLogicShell) : void
      {
         if(_switchBtns == null)
         {
            _switchBtns = [];
         }
         _switchBtns.push(param1);
         param1.switchBtns = _switchBtns;
      }
      
      public function addEnd() : void
      {
         if(Boolean(_switchBtns) && _switchBtns.length)
         {
            if(_switchBtns[0] is HaveLockSwitchBtnLogicShell)
            {
               HaveLockSwitchBtnLogicShell.setDefaultActivateBtnFromBtns(_switchBtns);
            }
            else
            {
               SwitchBtnLogicShell.setDefaultActivateBtnFromBtns(_switchBtns);
            }
         }
      }
      
      public function getSwitchBtnNum() : int
      {
         return _switchBtns ? _switchBtns.length : 0;
      }
      
      public function getSwitchBtnByIndex(param1:int) : SwitchBtnLogicShell
      {
         if(_switchBtns == null || param1 > _switchBtns.length - 1 || param1 < 0)
         {
            return null;
         }
         return _switchBtns[param1];
      }
      
      public function currentActivateBtn() : SwitchBtnLogicShell
      {
         var _loc2_:int = 0;
         var _loc1_:int = _switchBtns ? _switchBtns.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(SwitchBtnLogicShell(_switchBtns[_loc2_]).isActive)
            {
               return _switchBtns[_loc2_];
            }
            _loc2_++;
         }
         return null;
      }
      
      public function currentActivateBtnIndex() : int
      {
         var _loc2_:int = 0;
         var _loc1_:int = _switchBtns ? _switchBtns.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(SwitchBtnLogicShell(_switchBtns[_loc2_]).isActive)
            {
               return _loc2_;
            }
            _loc2_++;
         }
         return -1;
      }
      
      public function setExtraData(param1:Object) : void
      {
         _extraData = param1;
      }
      
      public function getExtraData() : Object
      {
         return _extraData;
      }
   }
}

