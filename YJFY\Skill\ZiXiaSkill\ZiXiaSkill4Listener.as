package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Entity.IEntity;
   
   public class ZiXiaSkill4Listener implements IZiXiaSkill4Listener
   {
      
      public var createImageFun:Function;
      
      public function ZiXiaSkill4Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         createImageFun = null;
      }
      
      public function createImage(param1:Skill_ZiXiaSkill4, param2:IEntity) : void
      {
         if(Boolean(createImageFun))
         {
            createImageFun(param1,param2);
         }
      }
   }
}

