package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.Players.Player;
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   
   public class PKInforPanel
   {
      
      private const m_const_forumURL:String = "http://my.4399.com/forums-thread-tagid-81260-id-41428935.html";
      
      private var m_matchInforShow:MatchInforShow;
      
      private var m_historyShow:PKHistoryShow;
      
      private var m_gotoForumBtn:ButtonLogicShell2;
      
      private var m_pkType:String;
      
      private var m_show:MovieClip;
      
      private var m_pk:PK2;
      
      public function PKInforPanel()
      {
         super();
         m_matchInforShow = new MatchInforShow();
         m_historyShow = new PKHistoryShow();
         m_gotoForumBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_matchInforShow);
         m_matchInforShow = null;
         ClearUtil.clearObject(m_historyShow);
         m_historyShow = null;
         ClearUtil.clearObject(m_gotoForumBtn);
         m_gotoForumBtn = null;
         m_pkType = null;
         m_show = null;
         m_pk = null;
      }
      
      public function init(param1:MovieClip, param2:PK2, param3:String, param4:Player, param5:Player, param6:uint, param7:PKMode2VO) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true);
         }
         m_pk = param2;
         m_pkType = param3;
         initShow();
         m_matchInforShow.setMyPlayers(param4,param5,param6);
         m_historyShow.setPKTargetPlayerDatas(param7);
      }
      
      private function initShow() : void
      {
         m_matchInforShow.init(m_show["matchInforShow"],m_pk,m_pkType);
         m_historyShow.init(m_show["HitstoryShow"],m_pk);
         m_gotoForumBtn.setShow(m_show["gotoForumBtn"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_gotoForumBtn === _loc2_)
         {
            navigateToURL(new URLRequest("http://my.4399.com/forums-thread-tagid-81260-id-41428935.html"),"_blank");
         }
      }
   }
}

