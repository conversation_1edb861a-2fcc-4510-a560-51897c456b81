package YJFY.Skill.ZiXiaSkill
{
   import YJFY.ActivityMode.SantaClaus;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_ZiXiaSkill5 extends CuboidAreaAttackSkill2
   {
      
      protected var m_releaseSkillFrameLabel:String;
      
      protected var m_attackReachFrameLabel:String;
      
      protected var m_skillShowEndFrameLabel:String;
      
      protected var m_skillEndFrameLabel:String;
      
      protected var m_changePositionFrameLabel:String;
      
      protected var m_lightEndFrameLabel:String;
      
      protected var m_disappearBodyShowDefId:String;
      
      protected var m_appearBodyShowDefId:String;
      
      protected var m_frontSkillShowDefId:String;
      
      protected var m_lightSkillShowDefId:String;
      
      protected var m_frontSkillShowPlay:AnimationShowPlayLogicShell;
      
      protected var m_frontSkillShowPlayListener:AnimationPlayFrameLabelListener;
      
      protected var m_lightSkillShowPlay:AnimationShowPlayLogicShell;
      
      protected var m_lightSkillShowPlayListener:AnimationPlayFrameLabelListener;
      
      public function Skill_ZiXiaSkill5()
      {
         super();
         m_frontSkillShowPlay = new AnimationShowPlayLogicShell();
         m_frontSkillShowPlayListener = new AnimationPlayFrameLabelListener();
         m_frontSkillShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_frontSkillShowPlay.addFrameLabelListener(m_frontSkillShowPlayListener);
         m_lightSkillShowPlay = new AnimationShowPlayLogicShell();
         m_lightSkillShowPlayListener = new AnimationPlayFrameLabelListener();
         m_lightSkillShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_lightSkillShowPlay.addFrameLabelListener(m_lightSkillShowPlayListener);
         m_isOutWorldTime = true;
      }
      
      override public function clear() : void
      {
         m_releaseSkillFrameLabel = null;
         m_attackReachFrameLabel = null;
         m_skillShowEndFrameLabel = null;
         m_skillEndFrameLabel = null;
         m_changePositionFrameLabel = null;
         m_lightEndFrameLabel = null;
         m_disappearBodyShowDefId = null;
         m_appearBodyShowDefId = null;
         m_frontSkillShowDefId = null;
         m_lightSkillShowDefId = null;
         ClearUtil.clearObject(m_frontSkillShowPlay);
         m_frontSkillShowPlay = null;
         ClearUtil.clearObject(m_frontSkillShowPlayListener);
         m_frontSkillShowPlayListener = null;
         ClearUtil.clearObject(m_lightSkillShowPlay);
         m_lightSkillShowPlay = null;
         ClearUtil.clearObject(m_lightSkillShowPlayListener);
         m_lightSkillShowPlayListener = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_releaseSkillFrameLabel = String(param1.@releaseSkillFrameLabel);
         m_attackReachFrameLabel = String(param1.@attackReachFrameLabel);
         m_skillShowEndFrameLabel = String(param1.@skillShowEndFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_changePositionFrameLabel = String(param1.@changePositionFrameLabel);
         m_lightEndFrameLabel = String(param1.@ligthtEndFrameLabel);
         m_disappearBodyShowDefId = String(param1.@disappearBodyShowDefId);
         m_appearBodyShowDefId = String(param1.@appearBodyShowDefId);
         m_frontSkillShowDefId = String(param1.@frontSkillShowDefId);
         m_lightSkillShowDefId = String(param1.@lightSkillShowDefId);
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_disappearBodyShowDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      protected function releaseSkill3() : void
      {
         m_world.stopWorldTime();
         if(m_frontSkillShowPlay.getShow() == null)
         {
            m_frontSkillShowPlay.setShow(m_owner.getAnimationByDefId(m_frontSkillShowDefId),true);
         }
         (m_frontSkillShowPlay.getShow() as DisplayObject).x = m_world.getCamera().getScreenX();
         (m_frontSkillShowPlay.getShow() as DisplayObject).y = m_world.getCamera().getScreenY();
         m_frontSkillShowPlay.gotoAndPlay("1");
         m_world.addAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
      }
      
      private function releaseLight() : void
      {
         if(m_lightSkillShowDefId)
         {
            if(m_lightSkillShowPlay.getShow() == null)
            {
               m_lightSkillShowPlay.setShow(m_owner.getAnimationByDefId(m_lightSkillShowDefId),true);
            }
            (m_lightSkillShowPlay.getShow() as DisplayObject).x = m_world.getCamera().getScreenX();
            (m_lightSkillShowPlay.getShow() as DisplayObject).y = m_world.getCamera().getScreenY();
            m_lightSkillShowPlay.gotoAndPlay("1");
            m_world.addAnimationOfOutWorldTimeInFront(m_lightSkillShowPlay);
         }
      }
      
      private function returnTime() : void
      {
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_releaseSkillFrameLabel:
               releaseSkill3();
               break;
            case m_attackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_skillShowEndFrameLabel:
               removeShows();
               releaseLight();
               break;
            case m_changePositionFrameLabel:
               this.returnFoe();
               break;
            case m_lightEndFrameLabel:
               m_world.continueWorldTime();
               removeShows();
               m_owner.changeAnimationShow(m_appearBodyShowDefId);
               m_owner.currentAnimationGotoAndPlay("1");
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
      
      private function returnFoe() : void
      {
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         var _loc6_:IEntity = null;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc3_:IAnimalEntity = null;
         var _loc2_:int = 0;
         var _loc1_:Array = null;
         var _loc10_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc8_:Number = NaN;
         if(m_world)
         {
            _loc5_ = int(m_world.getAllEntityNum());
            _loc4_ = 0;
            _loc7_ = 0;
            _loc11_ = 0;
            while(_loc11_ < _loc5_)
            {
               _loc6_ = m_world.getAllEnttiyByIndex(_loc11_);
               if(!(_loc6_ is TangMonkEntity) && _loc6_ is AnimalEntity && !(_loc6_ as AnimalEntity).isInDie() && _loc6_.getExtra() is EnemyXydzjs && !(_loc6_.getExtra() is SantaClaus))
               {
                  _loc3_ = (_loc6_.getExtra() as EnemyXydzjs).getAnimalEntity();
                  _loc2_ = _loc3_.getTimeIndex();
                  _loc1_ = [];
                  _loc1_ = _loc3_.getPosition(_loc2_);
                  if(_loc1_ == null)
                  {
                     _loc1_ = _loc3_.getPosition(0);
                  }
                  if(_loc1_ == null)
                  {
                     _loc10_ = Number(_loc3_.getInitX());
                     _loc9_ = Number(_loc3_.getInitY());
                     _loc8_ = Number(_loc3_.getInitZ());
                  }
                  else
                  {
                     _loc10_ = Number(_loc1_[0]);
                     _loc9_ = Number(_loc1_[1]);
                     _loc8_ = Number(_loc1_[2]);
                  }
                  _loc3_.setNewPosition(_loc10_,_loc9_,_loc8_);
                  _loc3_.clearPosition();
                  _loc3_.setTimeIndex(0);
               }
               _loc11_++;
            }
         }
      }
      
      private function removeShows() : void
      {
         if(m_world.checkIn(m_frontSkillShowPlay))
         {
            m_world.removeAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
         }
         if(m_world.checkIn(m_lightSkillShowPlay))
         {
            m_world.removeAnimationOfOutWorldTimeInFront(m_lightSkillShowPlay);
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         removeShows();
      }
   }
}

