package UI.UIInterface.OldInterface
{
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.IEventDispatcher;
   import flash.geom.Rectangle;
   
   public interface ISprite extends IEventDispatcher
   {
      
      function get x() : Number;
      
      function set x(param1:Number) : void;
      
      function get y() : Number;
      
      function set y(param1:Number) : void;
      
      function get width() : Number;
      
      function set width(param1:Number) : void;
      
      function get height() : Number;
      
      function set height(param1:Number) : void;
      
      function get hitArea() : Sprite;
      
      function get visible() : Boolean;
      
      function set visible(param1:Boolean) : void;
      
      function getRect(param1:DisplayObject) : Rectangle;
      
      function get name() : String;
      
      function set name(param1:String) : void;
      
      function hitTestPoint(param1:Number, param2:Number, param3:Boolean = false) : Boolean;
   }
}

