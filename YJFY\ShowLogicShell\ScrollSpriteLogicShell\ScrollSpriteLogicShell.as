package YJFY.ShowLogicShell.ScrollSpriteLogicShell
{
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ScrollSpriteLogicShell implements IButton
   {
      
      private var _show:Sprite;
      
      protected var _dataLayer:Sprite;
      
      protected var _dataMask:Sprite;
      
      protected var _slider:Sprite;
      
      protected var _downControl:Sprite;
      
      protected var _upControl:Sprite;
      
      protected var _scroll_bg:Sprite;
      
      protected var _mouseResponseSprite:MySprite;
      
      protected var _myScroll:ScrollBar;
      
      protected var _direction:String = "L";
      
      public function ScrollSpriteLogicShell()
      {
         super();
      }
      
      public function clear() : void
      {
         _show.removeEventListener("click",onClick,false);
         _show.removeEventListener("click",onClick,true);
         ClearUtil.clearDisplayObjectInContainer(_dataLayer);
         _show = null;
         _dataLayer = null;
         _dataMask = null;
         _slider = null;
         _downControl = null;
         _upControl = null;
         _scroll_bg = null;
         if(_myScroll)
         {
            _myScroll.clear();
         }
         _myScroll = null;
      }
      
      public function setShow(param1:Sprite, param2:ScrollSpriteDirection = null) : void
      {
         _show = param1;
         _mouseResponseSprite = new MySprite();
         _show.addChildAt(_mouseResponseSprite,0);
         if(param2)
         {
            _direction = param2.getDirection();
         }
         _dataLayer = param1["dataLayer"];
         _dataMask = param1["dataMask"];
         _slider = param1["slider_" + _direction];
         _downControl = param1["downControl_" + _direction];
         _upControl = param1["upControl_" + _direction];
         _scroll_bg = param1["scrollBg_" + _direction];
         _show.addEventListener("click",onClick,false,0,true);
         _show.addEventListener("click",onClick,true,0,true);
         initMainLogic();
         drawMosueResponseSprite();
      }
      
      public function addChildToDataLayer(param1:DisplayObject) : void
      {
         _dataLayer.addChild(param1);
         _myScroll.refresh();
      }
      
      public function removeChildFromDataLayer(param1:DisplayObject) : void
      {
         _dataLayer.removeChild(param1);
         _myScroll.refresh();
      }
      
      public function refresh() : void
      {
         _myScroll.refresh();
      }
      
      public function reset() : void
      {
         _myScroll.reset();
      }
      
      public function setIsAutoHideBar(param1:Boolean) : void
      {
         _myScroll.isAutoHideBar = param1;
      }
      
      private function initMainLogic() : void
      {
         _myScroll = new ScrollBar(_dataLayer,_dataMask,_slider,_scroll_bg);
         _myScroll.direction = _direction;
         _myScroll.tween = 1;
         _myScroll.elastic = false;
         _myScroll.lineAbleClick = true;
         _myScroll.mouseWheel = true;
         _myScroll.UP = _upControl;
         _myScroll.DOWN = _downControl;
         _myScroll.stepNumber = 15;
         _myScroll.refresh();
      }
      
      private function drawMosueResponseSprite() : void
      {
         _mouseResponseSprite.x = _dataMask.x;
         _mouseResponseSprite.y = _dataMask.y;
         _mouseResponseSprite.graphics.clear();
         _mouseResponseSprite.graphics.beginFill(16777215,0.01);
         _mouseResponseSprite.graphics.drawRect(0,0,_dataMask.width,_dataMask.height);
         _mouseResponseSprite.graphics.endFill();
      }
      
      public function getShow() : Sprite
      {
         return _show;
      }
      
      public function getDataLayer() : Sprite
      {
         return _dataLayer;
      }
      
      public function openMouseWheel() : void
      {
         _myScroll.mouseWheel = true;
      }
      
      public function closeMouseWheel() : void
      {
         _myScroll.mouseWheel = false;
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         _show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
   }
}

