package UI.MainLineTask
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class MainLineTaskData extends DataManagerParent
   {
      
      public var currentPhase:String;
      
      public var currentPhaseTaskList:PhaseTaskList;
      
      public var phaseTaskLists:Vector.<PhaseTaskList>;
      
      public function MainLineTaskData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         currentPhaseTaskList = null;
         ClearUtil.nullArr(phaseTaskLists);
         phaseTaskLists = null;
      }
      
      public function initByXML(param1:XML, param2:XML, param3:XML, param4:XML) : void
      {
         var _loc13_:int = 0;
         var _loc16_:int = 0;
         var _loc8_:PhaseTaskList = null;
         var _loc14_:int = 0;
         var _loc6_:int = 0;
         var _loc15_:int = 0;
         var _loc11_:int = 0;
         var _loc10_:int = 0;
         var _loc12_:MainLineTaskVO = null;
         var _loc5_:String = null;
         var _loc7_:* = undefined;
         var _loc18_:XMLList = param2.tasks;
         _loc16_ = int(_loc18_ ? _loc18_.length() : 0);
         phaseTaskLists = new Vector.<PhaseTaskList>();
         _loc13_ = 0;
         while(_loc13_ < _loc16_)
         {
            _loc8_ = new PhaseTaskList();
            _loc8_.initByXML(_loc18_[_loc13_],param4,param3);
            phaseTaskLists.push(_loc8_);
            _loc13_++;
         }
         if(!param1.hasOwnProperty("MainLineTask"))
         {
            currentPhase = phaseTaskLists[0].phase;
            currentPhaseTaskList = phaseTaskLists[0];
            return;
         }
         var _loc9_:XML = param1.MainLineTask[0];
         currentPhase = String(_loc9_.@phase);
         _loc16_ = phaseTaskLists ? phaseTaskLists.length : 0;
         _loc13_ = 0;
         while(_loc13_ < _loc16_)
         {
            if(currentPhase == phaseTaskLists[_loc13_].phase)
            {
               currentPhaseTaskList = phaseTaskLists[_loc13_];
               _loc14_ = 0;
               while(_loc14_ < _loc13_)
               {
                  _loc15_ = int(phaseTaskLists[_loc14_].taskVOs.length);
                  _loc6_ = 0;
                  while(_loc6_ < _loc15_)
                  {
                     phaseTaskLists[_loc14_].taskVOs[_loc6_].isGotReward = true;
                     _loc6_++;
                  }
                  _loc14_++;
               }
               break;
            }
            _loc13_++;
         }
         _loc18_ = _loc9_.task;
         _loc16_ = int(_loc18_ ? _loc18_.length() : 0);
         var _loc17_:TaskGoalVOFactory = new TaskGoalVOFactory();
         _loc13_ = 0;
         while(_loc13_ < _loc16_)
         {
            _loc5_ = String(_loc18_[_loc13_].@id);
            _loc10_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
            _loc11_ = 0;
            while(_loc11_ < _loc10_)
            {
               _loc12_ = currentPhaseTaskList.taskVOs[_loc11_];
               if(_loc12_.id == _loc5_)
               {
                  _loc12_.isGotReward = Boolean(int(_loc18_[_loc13_].@isGotReward));
                  _loc7_ = _loc17_.createTaskGoalsByXML(_loc18_[_loc13_],param3);
                  _loc12_.addTaskGoalsToCurrentTaskGoalVOs(_loc7_);
                  ClearUtil.nullArr(_loc7_,false,false,false);
                  break;
               }
               _loc11_++;
            }
            _loc17_.clear();
            _loc13_++;
         }
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc3_:XML = null;
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:int = 0;
         var _loc8_:MainLineTaskVO = null;
         var _loc2_:XML = null;
         var _loc4_:XML = null;
         var _loc7_:TaskGoalVO_MainTask = null;
         _loc1_ = Boolean(currentPhaseTaskList) && Boolean(currentPhaseTaskList.taskVOs) ? currentPhaseTaskList.taskVOs.length : 0;
         _loc9_ = 0;
         while(_loc9_ < _loc1_)
         {
            _loc8_ = currentPhaseTaskList.taskVOs[_loc9_];
            if(_loc8_)
            {
               if(_loc3_ == null)
               {
                  _loc3_ = <MainLineTask></MainLineTask>;
                  _loc3_.@phase = currentPhase;
               }
            }
            if(_loc8_.isGotReward)
            {
               if(_loc3_ == null)
               {
                  _loc3_ = <MainLineTask></MainLineTask>;
                  _loc3_.@phase = currentPhase;
               }
               _loc2_ = <task />;
               _loc2_.@id = _loc8_.id;
               _loc2_.@isGotReward = "1";
               _loc3_.appendChild(_loc2_);
            }
            else if(_loc8_.getCurrentTaskGoalNum())
            {
               if(_loc3_ == null)
               {
                  _loc3_ = <MainLineTask></MainLineTask>;
                  _loc3_.@phase = currentPhase;
               }
               _loc2_ = <task />;
               _loc2_.@id = _loc8_.id;
               _loc5_ = _loc8_.getCurrentTaskGoalNum();
               _loc6_ = 0;
               while(_loc6_ < _loc5_)
               {
                  _loc7_ = _loc8_.getCurrentTaskGoalByIndex(_loc6_);
                  if(_loc7_)
                  {
                     _loc4_ = <taskGoal />;
                     _loc4_.@id = _loc7_.id;
                     _loc4_.@num = _loc7_.num;
                     _loc2_.appendChild(_loc4_);
                  }
                  _loc6_++;
               }
               _loc3_.appendChild(_loc2_);
            }
            _loc9_++;
         }
         return _loc3_;
      }
      
      public function addTaskGoalByGameEventStr(param1:String) : void
      {
         var _loc6_:int = 0;
         var _loc5_:Boolean = false;
         var _loc2_:TaskGoalVOFactory = new TaskGoalVOFactory();
         var _loc4_:TaskGoalVO_MainTask = _loc2_.createOneTaskGoalByGameEventStr(param1,XMLSingle.getInstance().mainLineTaskGoalsXML);
         _loc2_.clear();
         var _loc3_:int = Boolean(currentPhaseTaskList) && currentPhaseTaskList.taskVOs ? currentPhaseTaskList.taskVOs.length : 0;
         while(_loc4_ && _loc6_ < _loc3_)
         {
            _loc5_ = Boolean(currentPhaseTaskList.taskVOs[_loc6_].addNewTaskGoal(_loc4_));
            if(_loc5_)
            {
               _loc4_ = null;
            }
            _loc6_++;
         }
      }
      
      public function gotoNextTaskList() : Boolean
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:MainLineTaskVO = null;
         var _loc1_:int = 0;
         _loc3_ = int(currentPhaseTaskList.taskVOs.length);
         var _loc2_:Boolean = true;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = currentPhaseTaskList.taskVOs[_loc5_];
            if(_loc4_.isGotReward == false)
            {
               if(_loc2_)
               {
                  _loc2_ = false;
               }
               break;
            }
            _loc5_++;
         }
         if(_loc2_)
         {
            _loc1_ = int(phaseTaskLists.indexOf(currentPhaseTaskList));
            if(_loc1_ == -1)
            {
               throw new Error();
            }
            if(_loc1_ < phaseTaskLists.length - 1 && phaseTaskLists[_loc1_ + 1].needLevel <= GamingUI.getInstance().player1.playerVO.level)
            {
               currentPhaseTaskList = phaseTaskLists[_loc1_ + 1];
               currentPhase = currentPhaseTaskList.phase;
            }
            else
            {
               _loc2_ = false;
            }
         }
         return _loc2_;
      }
   }
}

