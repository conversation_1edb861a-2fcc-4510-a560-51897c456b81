package UI.InformationPanel.PlayerInformationPanels
{
   import UI.ConsumptionGuide.Button.BuyPlayerBloodPotionGuideBtn;
   import UI.ConsumptionGuide.Button.BuyPlayerExperiencePotionGuideBtn;
   import UI.ConsumptionGuide.Button.BuyPlayerMagicPotionGuideBtn;
   import UI.Event.UIBtnEvent;
   import UI.InformationPanel.PlayerInformationPanel;
   import UI.NicknameSystem.ChangeNicknamePanel;
   import UI.NicknameSystem.NicknameData;
   import UI.Players.Player;
   import UI.UICheckBox.CheckBox1;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   
   public class PlayerInformationPanel_Inner extends PlayerInformationPanel
   {
      
      private var _buyPlayerBloodPotionGuideBtn:BuyPlayerBloodPotionGuideBtn;
      
      private var _buyPlayerMagicPotionGuideBtn:BuyPlayerMagicPotionGuideBtn;
      
      private var _buyPlayerExperiencePotionGuideBtn:BuyPlayerExperiencePotionGuideBtn;
      
      private var _isShowGuideBtn:Boolean;
      
      private var _fashionCheckBox:CheckBox1;
      
      private var _isShowFashionCheckBox:Boolean;
      
      private var _changeNicknamePanel:ChangeNicknamePanel;
      
      public function PlayerInformationPanel_Inner()
      {
         super();
      }
      
      override public function setPlayerPanel(param1:Player) : void
      {
         _useEquipmentVOs = param1.playerVO.inforEquipmentVOs;
         isShowFashionCheckBox = true;
         super.setPlayerPanel(param1);
         usedEquipmentCell_1.isShowBtn = true;
         usedEquipmentCell_2.isShowBtn = true;
         usedEquipmentCell_3.isShowBtn = true;
         usedEquipmentCell_4.isShowBtn = true;
         usedEquipmentCell_5.isShowBtn = true;
         usedEquipmentCell_6.isShowBtn = true;
         nickname = NicknameData.getInstance().myDataInNicknameRankList;
      }
      
      override protected function initPlayerInformationPanel() : void
      {
         super.initPlayerInformationPanel();
         _buyPlayerBloodPotionGuideBtn = new BuyPlayerBloodPotionGuideBtn(11000017);
         _buyPlayerBloodPotionGuideBtn.x = 360;
         _buyPlayerBloodPotionGuideBtn.y = 318;
         addChild(_buyPlayerBloodPotionGuideBtn);
         _buyPlayerMagicPotionGuideBtn = new BuyPlayerMagicPotionGuideBtn(11000016);
         _buyPlayerMagicPotionGuideBtn.x = 360;
         _buyPlayerMagicPotionGuideBtn.y = 340;
         addChild(_buyPlayerMagicPotionGuideBtn);
         _buyPlayerExperiencePotionGuideBtn = new BuyPlayerExperiencePotionGuideBtn(11000004);
         _buyPlayerExperiencePotionGuideBtn.x = 360;
         _buyPlayerExperiencePotionGuideBtn.y = 362;
         addChild(_buyPlayerExperiencePotionGuideBtn);
      }
      
      public function set isShowFashionCheckBox(param1:Boolean) : void
      {
         _isShowFashionCheckBox = param1;
         if(_isShowFashionCheckBox && Boolean(_useEquipmentVOs) && _useEquipmentVOs[3])
         {
            if(!_fashionCheckBox)
            {
               _fashionCheckBox = new CheckBox1();
            }
            _fashionCheckBox.x = 345;
            _fashionCheckBox.y = 135;
            addChild(_fashionCheckBox);
         }
         else if(_fashionCheckBox)
         {
            if(getChildByName(_fashionCheckBox.name))
            {
               removeChild(_fashionCheckBox);
            }
            _fashionCheckBox.clear();
            _fashionCheckBox = null;
         }
      }
      
      override public function set nickname(param1:Object) : void
      {
         var _loc2_:String = null;
         if(param1)
         {
            if(param1.nicknameType == "redNickname")
            {
               _loc2_ = "#ff0000";
            }
            else
            {
               _loc2_ = "#ffffff";
            }
            nicknameText.htmlText = "<font color=\'" + _loc2_ + "\'  size=\'15\' >" + (param1.extra ? param1.extra : "") + "</font>";
         }
         else
         {
            nicknameText.htmlText = "<font color=\'#666666\' size=\'15\' >" + (NicknameData.getInstance().myNicknameRankListId ? "昵称获取失败, 点击刷新" : "点击此处更改昵称") + "</font>";
         }
      }
      
      override public function set currentPlayer(param1:Player) : void
      {
         super.currentPlayer = param1;
         if(_fashionCheckBox)
         {
            _fashionCheckBox.isCheck = _currentPlayer.playerVO.isShowFashionShow;
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_buyPlayerBloodPotionGuideBtn)
         {
            _buyPlayerBloodPotionGuideBtn.clear();
         }
         _buyPlayerBloodPotionGuideBtn = null;
         if(_buyPlayerMagicPotionGuideBtn)
         {
            _buyPlayerMagicPotionGuideBtn.clear();
         }
         _buyPlayerMagicPotionGuideBtn = null;
         if(_buyPlayerExperiencePotionGuideBtn)
         {
            _buyPlayerExperiencePotionGuideBtn.clear();
         }
         _buyPlayerExperiencePotionGuideBtn = null;
         if(_fashionCheckBox)
         {
            _fashionCheckBox.clear();
         }
         _fashionCheckBox = null;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         nicknameText.addEventListener("click",clickNameText,false,0,true);
         nicknameText.addEventListener("rollOver",onRoll,false,0,true);
         nicknameText.addEventListener("rollOut",onRoll,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         nicknameText.removeEventListener("click",clickNameText,false);
         nicknameText.removeEventListener("rollOver",onRoll,false);
         nicknameText.removeEventListener("rollOut",onRoll,false);
      }
      
      private function onRoll(param1:MouseEvent) : void
      {
         if(param1.type == "rollOver")
         {
            nicknameText.filters = [new GlowFilter(16761220,1,2,2,10,3)];
         }
         else
         {
            nicknameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         }
      }
      
      private function clickNameText(param1:MouseEvent) : void
      {
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         var _loc2_:* = e.currentTarget;
         if(_changeNicknamePanel === _loc2_)
         {
            if(_changeNicknamePanel)
            {
               _changeNicknamePanel.playRemovedAnimation(function():void
               {
                  if(_changeNicknamePanel)
                  {
                     if(_changeNicknamePanel.parent)
                     {
                        _changeNicknamePanel.parent.removeChild(_changeNicknamePanel);
                     }
                     _changeNicknamePanel.removeEventListener("clickQuitBtn",quit,true);
                     _changeNicknamePanel.clear();
                     _changeNicknamePanel = null;
                  }
               },[]);
            }
         }
      }
   }
}

