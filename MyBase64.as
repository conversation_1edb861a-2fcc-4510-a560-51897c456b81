package
{
   import flash.utils.ByteArray;
   
   public class MyBase64
   {
      
      private static const BASE64_CHARS:String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      
      public static const version:String = "1.0.0";
      
      private static const SaveSt:String = "1880";
      
      public function MyBase64()
      {
         super();
         throw new Error("Base64 class is static container only");
      }
      
      public static function encode(param1:String) : String
      {
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.writeUTFBytes(param1);
         return encodeByteArray(_loc2_);
      }
      
      public static function getFakeData(param1:Object) : String
      {
         var _loc7_:int = 0;
         var _loc2_:* = 0;
         var _loc5_:ByteArray = new ByteArray();
         _loc5_.writeObject(param1);
         _loc5_.position = 0;
         _loc5_.compress();
         _loc5_.position = 0;
         while(_loc5_.bytesAvailable >= 4)
         {
            _loc7_ = int(_loc5_.position);
            _loc2_ = _loc5_.readUnsignedInt();
            _loc2_ ^= 255;
            _loc5_.position = _loc7_;
            _loc5_.writeUnsignedInt(_loc2_);
         }
         var _loc6_:String = Base64.encodeByteArray(_loc5_);
         var _loc8_:int = (_loc6_.length - 1) * Math.random();
         var _loc4_:String = _loc6_.substring(0,_loc8_ + 1) + "1880" + _loc6_.substring(_loc8_ + 1,_loc6_.length);
         return Base64.encode(_loc4_);
      }
      
      public static function getReallyDate(param1:String) : String
      {
         var _loc7_:int = 0;
         var _loc2_:* = 0;
         var _loc4_:String = Base64.decode(param1);
         var _loc8_:int = int(_loc4_.indexOf("1880"));
         var _loc3_:String = _loc4_.substring(0,_loc8_) + _loc4_.substring(_loc8_ + "1880".length);
         var _loc6_:ByteArray = Base64.decodeToByteArray(_loc3_);
         _loc6_.position = 0;
         while(_loc6_.bytesAvailable >= 4)
         {
            _loc7_ = int(_loc6_.position);
            _loc2_ = _loc6_.readUnsignedInt();
            _loc2_ ^= 255;
            _loc6_.position = _loc7_;
            _loc6_.writeUnsignedInt(_loc2_);
         }
         _loc6_.position = 0;
         _loc6_.uncompress();
         _loc6_.position = 0;
         var _loc5_:Object = _loc6_.readObject();
         return String(_loc5_);
      }
      
      public static function encodeByteArray(param1:ByteArray) : String
      {
         var _loc2_:Array = null;
         var _loc7_:* = 0;
         var _loc3_:* = 0;
         var _loc5_:* = 0;
         var _loc4_:String = "";
         var _loc6_:Array = new Array(4);
         param1.position = 0;
         while(param1.bytesAvailable > 0)
         {
            _loc2_ = [];
            _loc7_ = 0;
            while(_loc7_ < 3 && param1.bytesAvailable > 0)
            {
               _loc2_[_loc7_] = param1.readUnsignedByte();
               _loc7_++;
            }
            _loc6_[0] = (_loc2_[0] & 0xFC) >> 2;
            _loc6_[1] = (_loc2_[0] & 3) << 4 | _loc2_[1] >> 4;
            _loc6_[2] = (_loc2_[1] & 0x0F) << 2 | _loc2_[2] >> 6;
            _loc6_[3] = _loc2_[2] & 0x3F;
            _loc3_ = _loc2_.length;
            while(_loc3_ < 3)
            {
               _loc6_[_loc3_ + 1] = 64;
               _loc3_++;
            }
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               _loc4_ += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc6_[_loc5_]);
               _loc5_++;
            }
         }
         return _loc4_;
      }
      
      public static function decode(param1:String) : String
      {
         var _loc2_:ByteArray = decodeToByteArray(param1);
         return _loc2_.readUTFBytes(_loc2_.length);
      }
      
      public static function decodeToByteArray(param1:String) : ByteArray
      {
         var _loc7_:* = 0;
         var _loc3_:* = 0;
         var _loc5_:* = 0;
         var _loc4_:ByteArray = new ByteArray();
         var _loc2_:Array = new Array(4);
         var _loc6_:Array = new Array(3);
         _loc7_ = 0;
         while(_loc7_ < param1.length)
         {
            _loc3_ = 0;
            while(_loc3_ < 4 && _loc7_ + _loc3_ < param1.length)
            {
               _loc2_[_loc3_] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc7_ + _loc3_));
               _loc3_++;
            }
            _loc6_[0] = (_loc2_[0] << 2) + ((_loc2_[1] & 0x30) >> 4);
            _loc6_[1] = ((_loc2_[1] & 0x0F) << 4) + ((_loc2_[2] & 0x3C) >> 2);
            _loc6_[2] = ((_loc2_[2] & 3) << 6) + _loc2_[3];
            _loc5_ = 0;
            while(_loc5_ < _loc6_.length)
            {
               if(_loc2_[_loc5_ + 1] == 64)
               {
                  break;
               }
               _loc4_.writeByte(_loc6_[_loc5_]);
               _loc5_++;
            }
            _loc7_ += 4;
         }
         _loc4_.position = 0;
         return _loc4_;
      }
   }
}

