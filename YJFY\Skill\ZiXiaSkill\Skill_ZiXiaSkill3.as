package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_ZiXiaSkill3 extends CuboidAreaAttackSkill_OneSkillShow
   {
      
      private var m_skillStartFrameLabel:String;
      
      private var m_skillAttackReachFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      private var m_bIsHaveShake:Boolean;
      
      public function Skill_ZiXiaSkill3()
      {
         super();
         m_isAttackReachWhenRelease = false;
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         m_bIsHaveShake = false;
         releaseSkill2(param1);
         return true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillStartFrameLabel = String(param1.@skillStartFrameLabel);
         m_skillAttackReachFrameLabel = String(param1.@skillAttackReachFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_skillAttackReachFrameLabel:
               attackReach(m_world);
               if(!m_bIsHaveShake)
               {
                  m_world.shakeView(m_shakeViewDataOfPoints);
                  m_bIsHaveShake = true;
               }
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
   }
}

