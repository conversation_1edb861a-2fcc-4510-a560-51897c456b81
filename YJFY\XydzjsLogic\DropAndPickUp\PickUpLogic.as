package YJFY.XydzjsLogic.DropAndPickUp
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import YJFY.Entity.ShowEntity.DropEqEntity;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.Other.ValueAnimationsManager;
   import YJFY.SoundManager.SoundManager2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.SoundData;
   import YJFY.World.World;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class PickUpLogic
   {
      
      private const m_const_detectionDropEqAndPickUpInterval:uint = 250;
      
      private const m_const_otherDrop_moPing:String = "Item_MpUp";
      
      private const m_const_otherDrop_yuanBao:String = "Item_MoneyUp";
      
      private const m_const_otherDrop_yuanBao_10000:String = "Item_MoneyUp_10000";
      
      private const m_const_otherDrop_yuanBao_50000:String = "Item_MoneyUp_50000";
      
      private const m_const_otherDrop_yuanBao_100000:String = "Item_MoneyUp_100000";
      
      private const m_const_otherDrop_taoZi:String = "Item_HpUp";
      
      private const m_const_otherDrop_ZHH_1:String = "Item_ZhaoHuan_1";
      
      private const m_const_otherDrop_StN_10:String = "Item_StrengthenNum_10";
      
      private var m_data:PickUpData;
      
      private var m_pickUpSoundData:SoundData;
      
      private var m_pickUpReturnData:PickUpReturnData;
      
      private var m_dropEqEntitys:Vector.<DropEqEntity>;
      
      private var m_flyEqEntitys:Vector.<DropEqEntity>;
      
      private var m_packageViewCoordinate:Coordinate;
      
      private var m_flyTargetCoordinate:Coordinate;
      
      private var m_nextDetectionTime:Number;
      
      private var m_addMpValueAnimationsManager:ValueAnimationsManager;
      
      private var m_addHpValueAnimationsManager:ValueAnimationsManager;
      
      private var m_addMoneyValueAnimationsManager:ValueAnimationsManager;
      
      private var m_addZHHValueAnimationManager:ValueAnimationsManager;
      
      private var m_addStNValueAnimationManager:ValueAnimationsManager;
      
      private var m_dropEqPoint:Point;
      
      private var m_soundManager:SoundManager2;
      
      private var m_world:World;
      
      private var m_player1:Player;
      
      private var m_player2:Player;
      
      private var m_valueAnimationContainer:DisplayObjectContainer;
      
      public function PickUpLogic()
      {
         super();
         m_dropEqEntitys = new Vector.<DropEqEntity>();
         m_flyEqEntitys = new Vector.<DropEqEntity>();
         m_flyTargetCoordinate = new Coordinate();
         m_packageViewCoordinate = new Coordinate(488,38,0);
         m_nextDetectionTime = 0;
         m_pickUpReturnData = new PickUpReturnData();
         m_pickUpSoundData = new SoundData("pickUpSound","pickUpSound","NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf","PickSound");
         m_addHpValueAnimationsManager = new ValueAnimationsManager();
         m_addHpValueAnimationsManager.setValueAnimationsClassName("AddHpValueAnimation1");
         m_addMpValueAnimationsManager = new ValueAnimationsManager();
         m_addMpValueAnimationsManager.setValueAnimationsClassName("AddMpValueAnimation1");
         m_addMoneyValueAnimationsManager = new ValueAnimationsManager();
         m_addMoneyValueAnimationsManager.setValueAnimationsClassName("AddMoneyValueAnimation1");
         m_addZHHValueAnimationManager = new ValueAnimationsManager();
         m_addZHHValueAnimationManager.setValueAnimationsClassName("AddZHHAnimation1");
         m_addStNValueAnimationManager = new ValueAnimationsManager();
         m_addStNValueAnimationManager.setValueAnimationsClassName("AddSNAnimation1");
         m_dropEqPoint = new Point();
         m_data = new PickUpData();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_dropEqEntitys);
         m_dropEqEntitys = null;
         ClearUtil.clearObject(m_flyEqEntitys);
         m_flyEqEntitys = null;
         ClearUtil.clearObject(m_packageViewCoordinate);
         m_packageViewCoordinate = null;
         ClearUtil.clearObject(m_flyTargetCoordinate);
         m_flyTargetCoordinate = null;
         ClearUtil.clearObject(m_pickUpReturnData);
         m_pickUpReturnData = null;
         ClearUtil.clearObject(m_pickUpSoundData);
         m_pickUpSoundData = null;
         ClearUtil.clearObject(m_data);
         m_data = null;
         ClearUtil.clearObject(m_addHpValueAnimationsManager);
         m_addHpValueAnimationsManager = null;
         ClearUtil.clearObject(m_addMpValueAnimationsManager);
         m_addMpValueAnimationsManager = null;
         ClearUtil.clearObject(m_addMoneyValueAnimationsManager);
         m_addMoneyValueAnimationsManager = null;
         ClearUtil.clearObject(m_addZHHValueAnimationManager);
         m_addZHHValueAnimationManager = null;
         ClearUtil.clearObject(m_addStNValueAnimationManager);
         m_addStNValueAnimationManager = null;
         m_dropEqPoint = null;
         m_soundManager = null;
         m_world = null;
         m_player1 = null;
         m_player2 = null;
         m_valueAnimationContainer = null;
      }
      
      public function init(param1:World, param2:Player, param3:Player, param4:SoundManager2, param5:DisplayObjectContainer) : void
      {
         m_soundManager = param4;
         m_world = param1;
         m_player1 = param2;
         m_player2 = param3;
         m_valueAnimationContainer = param5;
         m_soundManager.addSound2(m_pickUpSoundData.getName(),m_pickUpSoundData.getSwfPath(),m_pickUpSoundData.getClassName());
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_nextDetectionTime < param1.getGameTimeForThisInit())
         {
            detectionDropEqAndPickUp();
            m_nextDetectionTime = param1.getGameTimeForThisInit() + 250;
         }
         m_addHpValueAnimationsManager.render();
         m_addMpValueAnimationsManager.render();
         m_addMoneyValueAnimationsManager.render();
         m_addZHHValueAnimationManager.render();
         m_addStNValueAnimationManager.render();
      }
      
      public function createOneDropEqAddAddToWorld(param1:String, param2:Number, param3:Number, param4:Number) : void
      {
         if(Boolean(param1) == false)
         {
            return;
         }
         var _loc6_:Sprite = MyFunction2.returnShowByClassName(param1) as Sprite;
         var _loc5_:DropEqEntity = new DropEqEntity();
         param2 = Math.max(m_world.getWorldArea().getMinX(),Math.min(m_world.getWorldArea().getMaxX(),param2));
         _loc5_.setShow(_loc6_);
         _loc5_.setNewPosition2(0,0,m_world.getTransformCoordinate());
         _loc5_.setEqClassName(param1);
         _loc5_.setNewPosition(param2,param3,param4);
         m_world.addEntity(_loc5_);
         m_dropEqEntitys.push(_loc5_);
      }
      
      private function detectionDropEqAndPickUp() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = int(m_dropEqEntitys.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(detectionDropEqAndPickUp2(m_player1,m_dropEqEntitys[_loc2_]))
            {
               m_dropEqEntitys.splice(_loc2_,1);
               _loc2_--;
               _loc1_--;
            }
            else if(Boolean(m_player2) && detectionDropEqAndPickUp2(m_player2,m_dropEqEntitys[_loc2_]))
            {
               m_dropEqEntitys.splice(_loc2_,1);
               _loc2_--;
               _loc1_--;
            }
            _loc2_++;
         }
      }
      
      private function detectionDropEqAndPickUp2(param1:Player, param2:DropEqEntity) : Boolean
      {
         if(param2.getPickUpCuboidRangeToWorld().contains(param1.getAnimalEntity().getX(),param1.getAnimalEntity().getY(),param1.getAnimalEntity().getZ()))
         {
            pickUp(param2.getEqClassName(),param1 as PlayerXydzjs,m_pickUpReturnData,param2);
            if(m_pickUpReturnData.IsAblePickUp)
            {
               if(m_soundManager)
               {
                  m_soundManager.play2(m_pickUpSoundData.getName(),m_pickUpSoundData.getSwfPath(),m_pickUpSoundData.getClassName());
               }
               if(m_pickUpReturnData.IsDisappear == false)
               {
                  m_flyEqEntitys.push(param2);
                  removeDropEqEntityShadow(param2);
                  m_world.getWorldCoordinateFromViewCoordinate(m_packageViewCoordinate.getX(),m_packageViewCoordinate.getY(),0,m_flyTargetCoordinate);
                  param2.fly(m_flyTargetCoordinate);
               }
               else
               {
                  m_world.removeEntity(param2);
                  m_flyEqEntitys.push(param2);
               }
               return true;
            }
         }
         return false;
      }
      
      private function pickUp(param1:String, param2:PlayerXydzjs, param3:PickUpReturnData, param4:DropEqEntity) : void
      {
         var _loc6_:* = 0;
         var _loc8_:Point = null;
         var _loc7_:Point = null;
         var _loc5_:Point = null;
         var _loc10_:Point = null;
         var _loc9_:Point = null;
         param3.IsAblePickUp = false;
         param3.IsDisappear = false;
         if(Boolean(param1) == false)
         {
            return;
         }
         switch(param1)
         {
            case "Item_MpUp":
               _loc6_ = m_data.get_addMp_moPing();
               GamingUI.getInstance().addMp(_loc6_,param2.getUiPlayer());
               m_dropEqPoint.x = param4.getShow().x;
               m_dropEqPoint.y = param4.getShow().y;
               _loc8_ = param4.getShow().parent.localToGlobal(m_dropEqPoint);
               m_addMpValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc8_.x,_loc8_.y,_loc6_);
               param3.IsAblePickUp = true;
               param3.IsDisappear = true;
               break;
            case "Item_MoneyUp":
            case "Item_MoneyUp_10000":
            case "Item_MoneyUp_50000":
            case "Item_MoneyUp_100000":
               m_dropEqPoint.x = param4.getShow().x;
               m_dropEqPoint.y = param4.getShow().y;
               _loc7_ = param4.getShow().parent.localToGlobal(m_dropEqPoint);
               switch(param1)
               {
                  case "Item_MoneyUp":
                     if(param2.getUiPlayer().playerVO.addDoubleExp > 0)
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao() * param2.getUiPlayer().playerVO.addDoubleExp;
                     }
                     else
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao();
                     }
                     GamingUI.getInstance().addMoney(_loc6_,param2.getUiPlayer());
                     m_addMoneyValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc7_.x,_loc7_.y,_loc6_,1 + param2.getUiPlayer().playerVO.extraAddMoneyRate / 100);
                     break;
                  case "Item_MoneyUp_10000":
                     if(param2.getUiPlayer().playerVO.addDoubleExp > 0)
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_10000() * param2.getUiPlayer().playerVO.addDoubleExp;
                     }
                     else
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_10000();
                     }
                     GamingUI.getInstance().addMoney(_loc6_,param2.getUiPlayer());
                     m_addMoneyValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc7_.x,_loc7_.y,_loc6_,1 + param2.getUiPlayer().playerVO.extraAddMoneyRate / 100);
                     break;
                  case "Item_MoneyUp_50000":
                     if(param2.getUiPlayer().playerVO.addDoubleExp > 0)
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_50000() * param2.getUiPlayer().playerVO.addDoubleExp;
                     }
                     else
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_50000();
                     }
                     GamingUI.getInstance().addMoney(_loc6_,param2.getUiPlayer());
                     m_addMoneyValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc7_.x,_loc7_.y,_loc6_,1 + param2.getUiPlayer().playerVO.extraAddMoneyRate / 100);
                     break;
                  case "Item_MoneyUp_100000":
                     if(param2.getUiPlayer().playerVO.addDoubleExp > 0)
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_100000() * param2.getUiPlayer().playerVO.addDoubleExp;
                     }
                     else
                     {
                        _loc6_ = m_data.get_addMoney_yuanBao_100000();
                     }
                     GamingUI.getInstance().addMoney(_loc6_,param2.getUiPlayer());
                     m_addMoneyValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc7_.x,_loc7_.y,_loc6_,1 + param2.getUiPlayer().playerVO.extraAddMoneyRate / 100);
                     break;
                  default:
                     throw new Error("出错了");
               }
               param3.IsAblePickUp = true;
               param3.IsDisappear = true;
               break;
            case "Item_HpUp":
               _loc6_ = m_data.get_addHp_taoZi();
               GamingUI.getInstance().addHp(_loc6_,param2.getUiPlayer());
               m_dropEqPoint.x = param4.getShow().x;
               m_dropEqPoint.y = param4.getShow().y;
               _loc5_ = param4.getShow().parent.localToGlobal(m_dropEqPoint);
               m_addHpValueAnimationsManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc5_.x,_loc5_.y,_loc6_);
               param3.IsAblePickUp = true;
               param3.IsDisappear = true;
               break;
            case "Item_ZhaoHuan_1":
               _loc6_ = m_data.get_addZHH_1();
               GamingUI.getInstance().player1.getMountsVO().addGetMaterialPointNum(_loc6_);
               m_dropEqPoint.x = param4.getShow().x;
               m_dropEqPoint.y = param4.getShow().y;
               _loc10_ = param4.getShow().parent.localToGlobal(m_dropEqPoint);
               m_addZHHValueAnimationManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc10_.x,_loc10_.y,_loc6_);
               param3.IsAblePickUp = true;
               param3.IsDisappear = true;
               break;
            case "Item_StrengthenNum_10":
               _loc6_ = m_data.get_addStN_10();
               GamingUI.getInstance().player1.getMountsVO().addStrengthenPointNum(_loc6_);
               m_dropEqPoint.x = param4.getShow().x;
               m_dropEqPoint.y = param4.getShow().y;
               _loc9_ = param4.getShow().parent.localToGlobal(m_dropEqPoint);
               m_addStNValueAnimationManager.addValueAnimationToTarget(m_valueAnimationContainer,_loc9_.x,_loc9_.y,_loc6_);
               param3.IsAblePickUp = true;
               param3.IsDisappear = true;
               break;
            default:
               param3.IsAblePickUp = GamingUI.getInstance().addEqFromDropEq(param1,param2.getUiPlayer());
               param3.IsDisappear = false;
         }
      }
      
      private function removeDropEqEntityShadow(param1:DropEqEntity) : void
      {
         try
         {
            param1.getShowEx().removeChildAt(0);
         }
         catch(e:Error)
         {
            trace("移除掉落装备影子出错");
         }
      }
   }
}

