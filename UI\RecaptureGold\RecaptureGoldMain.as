package UI.RecaptureGold
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.Shop.RecaptureGoldShop;
   import UI.RecaptureGold.UI.AnimationPieces.AnimationPiece;
   import UI.RecaptureGold.UI.PassLevelFailShow;
   import UI.RecaptureGold.UI.RGoldBeginPanel;
   import UI.SoundManager.SoundManager;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class RecaptureGoldMain extends MySprite
   {
      
      private var _maskSprite:Sprite;
      
      private var _recaptureGoldXML:XML;
      
      private var _rGoldBeginPanel:RGoldBeginPanel;
      
      private var _rGoldGame:RecaptureGoldGame;
      
      private var _passLevelFailShow:PassLevelFailShow;
      
      private var _recaptureGoldShop:RecaptureGoldShop;
      
      private var _soundManager:SoundManager;
      
      public function RecaptureGoldMain()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         _maskSprite = null;
         _recaptureGoldXML = null;
         if(_rGoldBeginPanel)
         {
            _rGoldBeginPanel.clear();
         }
         _rGoldBeginPanel = null;
         if(_rGoldGame)
         {
            _rGoldGame.clear();
         }
         _rGoldGame = null;
         if(_passLevelFailShow)
         {
            _passLevelFailShow.clear();
         }
         _passLevelFailShow = null;
         if(_recaptureGoldShop)
         {
            _recaptureGoldShop.clear();
         }
         _recaptureGoldShop = null;
         if(_soundManager)
         {
            _soundManager.clear();
         }
         _soundManager = null;
         AnimationPiece.drawTarget = null;
         AnimationPiece.maskTarget = null;
         AnimationPiece.showTarget = null;
         _maskSprite = null;
      }
      
      public function stopGame() : void
      {
         if(_rGoldGame)
         {
            _rGoldGame.stopGame();
         }
      }
      
      public function showPassLevelFail() : void
      {
         submitScoreToRankList(_recaptureGoldXML);
         _passLevelFailShow = new PassLevelFailShow();
         addChild(_passLevelFailShow);
      }
      
      public function showPassLevel() : void
      {
         var playerOver:* = function(param1:Event):void
         {
            animtion.removeEventListener("playerOver",playerOver,false);
            showShop();
         };
         var animtion:MovieClip = MyFunction2.returnShowByClassName(_recaptureGoldXML.OtherData.GameUIAnimation.@passLevel) as MovieClip;
         animtion.addEventListener("playerOver",playerOver,false);
         addChild(animtion);
      }
      
      private function showShop() : void
      {
         if(_rGoldGame)
         {
            _recaptureGoldShop = new RecaptureGoldShop(_recaptureGoldXML,this,_rGoldGame.playerPropVO);
            addChild(_recaptureGoldShop);
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("startRecaptureGoldGame",startGame,true,0,true);
         addEventListener("nextLevel",nextLevel,true,0,true);
         addEventListener("returnMainPanel",returnMainPanel,true,0,true);
         addEventListener("quitGame",quitGame,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("startRecaptureGoldGame",startGame,true);
         removeEventListener("nextLevel",nextLevel,true);
         removeEventListener("returnMainPanel",returnMainPanel,true);
         removeEventListener("quitGame",quitGame,true);
         removeEventListener("warningBox",sureOrCancel,true);
      }
      
      private function quitGame(param1:RecaptureGoldEvent) : void
      {
         submitScoreToRankList(_recaptureGoldXML);
         if(!_rGoldBeginPanel)
         {
            _rGoldBeginPanel = new RGoldBeginPanel(_recaptureGoldXML);
         }
         if(!getChildByName(_rGoldBeginPanel.name))
         {
            addChild(_rGoldBeginPanel);
         }
         _rGoldBeginPanel.quitBtn.quitBtn.dispatchEvent(new MouseEvent("click"));
      }
      
      private function returnMainPanel(param1:Event) : void
      {
         var animtion:MovieClip;
         var e:Event = param1;
         var playerOver:* = function(param1:Event):void
         {
            animtion.removeEventListener("playerOver",playerOver,false);
            removeChild(_passLevelFailShow);
            _passLevelFailShow.clear();
            _passLevelFailShow = null;
            clearMaskAnimationPiece();
         };
         if(_rGoldGame)
         {
            _rGoldGame.clear();
         }
         _rGoldGame = null;
         if(!_rGoldBeginPanel)
         {
            _rGoldBeginPanel = new RGoldBeginPanel(_recaptureGoldXML);
         }
         addChild(_rGoldBeginPanel);
         AnimationPiece.showTarget = _rGoldBeginPanel;
         AnimationPiece.drawTarget = _passLevelFailShow;
         AnimationPiece.maskTarget = _maskSprite;
         animtion = MyFunction2.returnShowByClassName(_recaptureGoldXML.OtherData.GameUIAnimation.@returnMainPanel) as MovieClip;
         animtion.addEventListener("playerOver",playerOver,false);
         addChild(animtion);
      }
      
      private function nextLevel(param1:Event) : void
      {
         var _loc2_:Sprite = null;
         addChild(_rGoldGame);
         _loc2_ = _recaptureGoldShop;
         removeChild(_loc2_);
         _loc2_["clear"]();
         _recaptureGoldShop = null;
         _rGoldGame.nextLevel();
      }
      
      private function startGame(param1:RecaptureGoldEvent) : void
      {
         var e:RecaptureGoldEvent = param1;
         if(_rGoldGame)
         {
            return;
         }
         _rGoldGame = new RecaptureGoldGame(_soundManager);
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var animtion:MovieClip;
            var saveinfo:SaveTaskInfo;
            var timeStr:String = param1;
            var playerOver:* = function(param1:Event):void
            {
               animtion.removeEventListener("playerOver",playerOver,false);
               removeChild(_rGoldBeginPanel);
               _rGoldBeginPanel.clear();
               _rGoldBeginPanel = null;
               clearMaskAnimationPiece();
            };
            RecaptureGoldData.getInstance().rGNum++;
            addChild(_rGoldGame);
            _rGoldGame.init(_recaptureGoldXML);
            AnimationPiece.showTarget = _rGoldGame;
            AnimationPiece.drawTarget = _rGoldBeginPanel;
            AnimationPiece.maskTarget = _maskSprite;
            animtion = MyFunction2.returnShowByClassName(_recaptureGoldXML.OtherData.GameUIAnimation.@startGame) as MovieClip;
            animtion.addEventListener("playerOver",playerOver,false);
            addChild(animtion);
            saveinfo = new SaveTaskInfo();
            saveinfo.type = "4399";
            saveinfo.isHaveData = false;
            SaveTaskList.getInstance().addData(saveinfo);
            MyFunction2.saveGame2();
         },showWarningBox,true);
      }
      
      private function init() : void
      {
         MyFunction2.loadXMLFunction("recaptureGold",function(param1:XML):void
         {
            _recaptureGoldXML = param1;
            _rGoldBeginPanel = new RGoldBeginPanel(_recaptureGoldXML);
            addChild(_rGoldBeginPanel);
            _maskSprite = new Sprite();
            _soundManager = new SoundManager();
         },null,true);
      }
      
      private function clearMaskAnimationPiece() : void
      {
         AnimationPiece.showTarget.mask = null;
         AnimationPiece.drawTarget = null;
         AnimationPiece.showTarget = null;
         while(_maskSprite.numChildren > 0)
         {
            _maskSprite.removeChildAt(0);
         }
      }
      
      private function submitScoreToRankList(param1:XML) : void
      {
         var rId:int;
         var score:int;
         var extra:XML;
         var rankInfoAry:Array;
         var isExcecution:Boolean;
         var myThis:DisplayObject;
         var recaptureGoldXML:XML = param1;
         if(RecaptureGoldData.getInstance().allGoldNum == 0)
         {
            return;
         }
         rId = int(recaptureGoldXML.OtherData[0].RankList[0].@rId);
         score = RecaptureGoldData.getInstance().allGoldNum;
         extra = <Data></Data>;
         extra.appendChild(<CIN />);
         extra.CIN[0].@num = RecaptureGoldData.getInstance().catchItemNum;
         rankInfoAry = [{
            "rId":rId,
            "score":score,
            "extra":extra
         }];
         isExcecution = true;
         myThis = this;
         RecaptureGoldFunction.getInstance().getOneRankListDataByUserName(rId,GameData.getInstance().getLoginReturnData().getName(),null,function(param1:Object):void
         {
            var data:Object = param1;
            if(data)
            {
               if(data.score >= score)
               {
                  isExcecution = false;
                  if(myThis.stage)
                  {
                     showWarningBox("本次夺金数比排行榜上的底，不提交排行榜！",0);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("本次夺金数比排行榜上的底，不提交排行榜！");
                  }
               }
            }
            if(isExcecution)
            {
               RecaptureGoldFunction.getInstance().submitScoreToRankList(GameData.getInstance().getSaveFileData().index,rankInfoAry,null,function(param1:Array):void
               {
                  if(myThis.stage)
                  {
                     showWarningBox("夺金数提交成功！",0);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("夺金数提交成功！");
                  }
               },null,null,function(param1:String):void
               {
                  if(myThis.stage)
                  {
                     showWarningBox("夺金数提交失败！",0);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("夺金数提交失败！");
                  }
               },null);
            }
         },null,null,function(param1:String):void
         {
            if(myThis.stage)
            {
               showWarningBox("夺金数提交失败！",0);
            }
            else
            {
               GamingUI.getInstance().showMessageTip("夺金数提交失败！");
            }
         },null);
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         if(param1.data.detail == 1)
         {
            if((param1.data.task as Object).hasOwnProperty("okFunction"))
            {
               param1.data.task.okFunction.apply(null,param1.data.okFunParams);
            }
            for(var _loc2_ in param1.data.task)
            {
               param1.data.task[_loc2_] = null;
            }
            param1.data.task = null;
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
   }
}

