package UI.AutomaticPetPanel
{
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.MySprite;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   
   public class SkillShow extends MySprite
   {
      
      private var m_skillShowListeners:Vector.<ISkillShowListener>;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_iconSwfPath:String;
      
      private var m_iconClassName:String;
      
      private var m_extra:Object;
      
      private var m_show:MovieClip;
      
      public function SkillShow()
      {
         super();
         m_myLoader = new YJFYLoader();
         mouseChildren = true;
         mouseEnabled = true;
         m_skillShowListeners = new Vector.<ISkillShowListener>();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_loadUI = null;
         m_versionControl = null;
         m_iconClassName = null;
         m_iconSwfPath = null;
         m_extra = null;
         ClearUtil.nullArr(m_skillShowListeners,false,false,false);
         m_skillShowListeners = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         super.clear();
      }
      
      public function init(param1:IProgressShow, param2:IVersionControl, param3:String = null, param4:String = null) : void
      {
         m_loadUI = param1;
         m_versionControl = param2;
         m_iconSwfPath = param3;
         m_iconClassName = param4;
         loadShow();
      }
      
      public function resetShow(param1:String, param2:String) : void
      {
         if(m_iconSwfPath != param1 || m_iconClassName != param2)
         {
            m_iconSwfPath = param1;
            m_iconClassName = param2;
         }
         loadShow();
      }
      
      public function setExtra(param1:Object) : void
      {
         m_extra = param1;
      }
      
      public function getExtra() : Object
      {
         return m_extra;
      }
      
      public function addSkillShowListener(param1:ISkillShowListener) : void
      {
         ListenerUtil.addListener(m_skillShowListeners,param1);
      }
      
      public function removeSkillShowListener(param1:ISkillShowListener) : void
      {
         ListenerUtil.removeListener(m_skillShowListeners,param1);
      }
      
      private function loadShow() : void
      {
         if(Boolean(m_iconClassName) && Boolean(m_iconSwfPath))
         {
            m_myLoader.setProgressShow(m_loadUI);
            m_myLoader.setVersionControl(m_versionControl);
            m_myLoader.getClass(m_iconSwfPath,m_iconClassName,getShowSuccess,null);
            m_myLoader.load();
         }
         else
         {
            ClearUtil.clearDisplayObjectInContainer(this);
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         ClearUtil.clearDisplayObjectInContainer(this);
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         m_show.x = -m_show.width / 2;
         m_show.y = -m_show.height / 2;
         addChild(m_show);
         showSkillComplete();
      }
      
      private function showSkillComplete() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:Vector.<ISkillShowListener> = m_skillShowListeners.slice(0);
         _loc2_ = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].showSkillComplete(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
   }
}

