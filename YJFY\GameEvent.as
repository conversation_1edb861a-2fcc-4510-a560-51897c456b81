package YJFY
{
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   public class GameEvent extends Event
   {
      
      public static const RECHARGE_CALL_BACK:String = "rechargeCallBack";
      
      public static const TICKET_CHANGE:String = "ticketChange";
      
      public static const SUCCESS:String = "success";
      
      public static const FAIL:String = "fail";
      
      public static const HIDETIP:String = "hidetip";
      
      public static const REFRESHTIP:String = "refreshtip";
      
      public static const SHOWTIP:String = "showtip";
      
      public static const RUNDETECTOR:String = "rundetector";
      
      public static const CITYTOWER:String = "citytower";
      
      public static const ALLTIP:String = "alltip";
      
      public static const SMALLONLINETIP:String = "smallonlinetip";
      
      public static var eventDispacher:EventDispatcher = new EventDispatcher();
      
      public var data:Object;
      
      public function GameEvent(param1:String, param2:Object = null, param3:Boolean = false, param4:Boolean = false)
      {
         super(param1,param3,param4);
         this.data = param2;
      }
      
      public static function dispatchEventWith(param1:String, param2:Object = null, param3:Boolean = false, param4:Boolean = false) : void
      {
         eventDispacher.dispatchEvent(new GameEvent(param1,param2,param3,param4));
      }
   }
}

