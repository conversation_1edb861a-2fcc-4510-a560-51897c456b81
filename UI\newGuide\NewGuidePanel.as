package UI.newGuide
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.LimitingTimeTaskVO;
   import UI.Task.TaskVO.LimtingTimeAccumulatedTaskVO;
   import UI.XMLSingle;
   import UI.newGuide.GuideActivityTask.GuideActivityPanel;
   import UI.newGuide.GuideEveryDay.GuideEveryPanel;
   import UI.newGuide.GuideMainLine.GuideMainPanel;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import UI.newTask.NewActivityTask.MewActivityData;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import YJFY.GameEvent;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class NewGuidePanel
   {
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_gantan:MovieClip;
      
      private var m_dagou:MovieClip;
      
      private var m_show:MovieClip;
      
      private var m_tip:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_myloader:YJFYLoader;
      
      public var currentType:int = 0;
      
      public var currentName:String;
      
      private var m_mainTask:MySwitchBtnLogicShell;
      
      private var m_dayTask:MySwitchBtnLogicShell;
      
      private var m_activiyTask:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_mainNumTxt:TextField;
      
      private var m_dayNumTxt:TextField;
      
      private var m_activityNumTxt:TextField;
      
      private var m_guidemainpanel:GuideMainPanel;
      
      private var m_guideeverypanel:GuideEveryPanel;
      
      private var m_guideactivitypanel:GuideActivityPanel;
      
      private var m_delayTime:int;
      
      public function NewGuidePanel()
      {
         var _loc1_:SaveTaskInfo = null;
         super();
         GameEvent.eventDispacher.addEventListener("refreshtip",callrefreshTip);
         GameEvent.eventDispacher.addEventListener("hidetip",hideCall);
         GameEvent.eventDispacher.addEventListener("rundetector",runTaskDetectorsInTaskList);
         if(NewMainTaskData.getInstance().getIsComp() == false)
         {
            _loc1_ = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            MyFunction2.saveGame();
         }
      }
      
      public function clear() : void
      {
         GameEvent.eventDispacher.removeEventListener("refreshtip",callrefreshTip);
         GameEvent.eventDispacher.removeEventListener("hidetip",hideCall);
         GameEvent.eventDispacher.removeEventListener("rundetector",runTaskDetectorsInTaskList);
         m_show.removeEventListener("clickButton",clickButton,true);
         m_gantan.removeEventListener("click",callgantan);
         m_dagou.removeEventListener("click",calldagou);
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_guidemainpanel);
         m_guidemainpanel = null;
         ClearUtil.clearObject(m_guideeverypanel);
         m_guideeverypanel = null;
         ClearUtil.clearObject(m_guideactivitypanel);
         m_guideactivitypanel = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_mainTask);
         m_mainTask = null;
         ClearUtil.clearObject(m_dayTask);
         m_dayTask = null;
         ClearUtil.clearObject(m_activiyTask);
         m_activiyTask = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
      }
      
      public function hide() : void
      {
         m_show.visible = false;
         refreshState();
      }
      
      public function show() : void
      {
         m_show.visible = true;
         refreshState();
      }
      
      public function hideAll() : void
      {
         currentName = null;
         m_guidemainpanel.hide();
         m_guideeverypanel.hide();
         m_guideactivitypanel.hide();
      }
      
      private function runTaskDetectorsInTaskList(param1:Event) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:MainLineTaskVO = NewMainTaskData.getInstance().currentTask;
         if(Boolean(NewMainTaskData.getInstance().currentTask) && Boolean(NewMainTaskData.getInstance().currentTask.taskDetectors) && NewMainTaskData.getInstance().currentTask.taskDetectors.length)
         {
            _loc3_ = int(NewMainTaskData.getInstance().currentTask.taskDetectors.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               NewMainTaskData.getInstance().currentTask.taskDetectors[_loc4_].detect();
               _loc4_++;
            }
         }
      }
      
      public function openByIndex(param1:int, param2:String) : void
      {
         hideAll();
         if(param1 == 1)
         {
            m_guidemainpanel.show();
         }
         else if(param1 == 2)
         {
            m_guideeverypanel.show();
         }
         else if(param1 == 3)
         {
            m_guideactivitypanel.show();
         }
      }
      
      private function callgantan(param1:MouseEvent) : void
      {
         showCheck();
      }
      
      private function calldagou(param1:MouseEvent) : void
      {
         showCheck();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_mainTask:
               if(currentType != 1)
               {
                  currentType = 1;
                  openByIndex(1,null);
               }
               break;
            case m_dayTask:
               if(currentType != 2)
               {
                  currentType = 2;
                  openByIndex(2,null);
               }
               break;
            case m_activiyTask:
               if(currentType != 3)
               {
                  currentType = 3;
                  openByIndex(3,null);
               }
               break;
            case m_quitBtn:
               refreshState();
         }
      }
      
      public function showCheck() : void
      {
         runTaskDetectorsInTaskList(null);
         if(NewEveryDayData.getInstance().everyDayTaskVOs == null || NewEveryDayData.getInstance().everyDayTaskVOs.length <= 0)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               NewEveryDayData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
               MewActivityData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
               refreshState();
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
         else
         {
            showList();
         }
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         if(NewGuideData.getInstance().isShow)
         {
            loadUI();
            m_show.visible = true;
            initShow();
         }
         else
         {
            m_show.visible = false;
         }
      }
      
      private function hideCall(param1:Event) : void
      {
         if(NewGuideData.getInstance().isShow)
         {
            show();
         }
         else
         {
            hide();
         }
      }
      
      public function initShow() : void
      {
         var _loc2_:Array = null;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_guidemainpanel = new GuideMainPanel();
         m_guidemainpanel.init(this,m_show);
         m_guideeverypanel = new GuideEveryPanel();
         m_guideeverypanel.init(this,m_show);
         m_guideactivitypanel = new GuideActivityPanel();
         m_guideactivitypanel.init(this,m_show);
         m_tip = m_show["mctip"];
         m_gantan = m_show["btntip"];
         m_dagou = m_show["btnchecked"];
         m_gantan.addEventListener("click",callgantan);
         m_dagou.addEventListener("click",calldagou);
         var _loc1_:MovieClip = m_show["listpanel"];
         m_mc = _loc1_;
         m_mainTask = new MySwitchBtnLogicShell();
         m_mainTask.setShow(_loc1_["btnmainline"]);
         m_mainTask.setTipString("主线任务");
         m_dayTask = new MySwitchBtnLogicShell();
         m_dayTask.setShow(_loc1_["btneveryday"]);
         m_dayTask.setTipString("每日任务");
         m_activiyTask = new MySwitchBtnLogicShell();
         m_activiyTask.setShow(_loc1_["btnactivity"]);
         m_activiyTask.setTipString("活动任务");
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(_loc1_["btnClose"]);
         m_quitBtn.setTipString("收起");
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtnGroup.addSwitchBtn(m_mainTask);
         m_switchBtnGroup.addSwitchBtn(m_dayTask);
         m_switchBtnGroup.addSwitchBtn(m_activiyTask);
         m_switchBtnGroup.addEnd();
         m_mainNumTxt = (m_mainTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         m_dayNumTxt = (m_dayTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         m_activityNumTxt = (m_activiyTask.getShow()["mctip"] as MovieClip)["tipnumtxt"] as TextField;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_mainNumTxt,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_dayNumTxt,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_activityNumTxt,true);
         _loc2_ = [new GlowFilter(0,1,2,2,10,1,false,false)];
         m_mainNumTxt.filters = _loc2_;
         m_dayNumTxt.filters = _loc2_;
         m_activityNumTxt.filters = _loc2_;
         NewEveryDayData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,TimeUtil.getTimeUtil().getTimeStr());
         MewActivityData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,TimeUtil.getTimeUtil().getTimeStr());
         m_delayTime = setTimeout(calldelay,2000);
         callrefreshTip(null);
      }
      
      private function calldelay() : void
      {
         refreshState();
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
      }
      
      public function showGantan() : void
      {
         m_gantan.visible = true;
         m_dagou.visible = false;
         m_mc.visible = false;
         m_tip.visible = false;
         (m_tip["tipnumtxt"] as TextField).visible = false;
      }
      
      public function showDagou() : void
      {
         m_gantan.visible = false;
         m_dagou.visible = true;
         m_mc.visible = false;
         m_tip.visible = false;
         (m_tip["tipnumtxt"] as TextField).visible = false;
      }
      
      public function showList() : void
      {
         if(currentType == 1)
         {
            m_guidemainpanel.refreshlist();
         }
         else if(currentType == 2)
         {
            NewEveryDayData.getInstance().refreshData();
            m_guideeverypanel.refreshlist();
         }
         else if(currentType == 3)
         {
            MewActivityData.getInstance().refreshData();
            m_guideactivitypanel.refreshlist();
         }
         m_mc.visible = true;
         m_gantan.visible = false;
         m_dagou.visible = false;
         callrefreshTip(null);
         m_tip.visible = false;
         (m_tip["tipnumtxt"] as TextField).visible = false;
      }
      
      public function refreshState() : void
      {
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:Boolean = false;
         _loc7_ = 0;
         while(_loc7_ < NewMainTaskData.getInstance().phaseTaskLists.length)
         {
            _loc5_ = 0;
            while(_loc5_ < NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs.length)
            {
               if(NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].isWorking && NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].isGotReward == false)
               {
                  _loc2_ = 0;
                  _loc6_ = 0;
                  while(_loc6_ < NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].getCurrentTaskGoalNum())
                  {
                     _loc2_ += NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].getCurrentTaskGoalByIndex(_loc6_).num;
                     _loc6_++;
                  }
                  _loc1_ = 0;
                  _loc3_ = 0;
                  while(_loc3_ < NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].getNeedTaskGoalNum())
                  {
                     _loc1_ += NewMainTaskData.getInstance().phaseTaskLists[_loc7_].taskVOs[_loc5_].getNeedTaskGoalByIndex(_loc3_).num;
                     _loc3_++;
                  }
                  if(_loc2_ >= _loc1_ && _loc1_ != 0)
                  {
                     _loc4_ = true;
                     showDagou();
                     return;
                  }
               }
               _loc5_++;
            }
            _loc7_++;
         }
         NewEveryDayData.getInstance().refreshData();
         _loc7_ = 0;
         while(_loc7_ < NewEveryDayData.getInstance().everyDayTaskVOs.length)
         {
            if(NewEveryDayData.getInstance().everyDayTaskVOs[_loc7_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < NewEveryDayData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_ids.length)
               {
                  _loc2_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc7_].currentTaskGoalVO_nums[_loc5_];
                  _loc1_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_nums[_loc5_];
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc4_ = true;
                  showDagou();
                  return;
               }
            }
            _loc7_++;
         }
         MewActivityData.getInstance().refreshData();
         _loc7_ = 0;
         while(_loc7_ < MewActivityData.getInstance().everyDayTaskVOs.length)
         {
            if(MewActivityData.getInstance().everyDayTaskVOs[_loc7_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < MewActivityData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_ids.length)
               {
                  if(MewActivityData.getInstance().everyDayTaskVOs[_loc7_] is LimitingTimeTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_nums[_loc5_];
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc7_] is AccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc7_] as AccumulatedTaskVO).taskCount;
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc7_] is LimtingTimeAccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc7_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc7_] as LimtingTimeAccumulatedTaskVO).taskCount;
                  }
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc4_ = true;
                  showDagou();
                  return;
               }
            }
            _loc7_++;
         }
         if(_loc4_)
         {
            showDagou();
            return;
         }
         showGantan();
      }
      
      private function loadUI() : void
      {
         if(NewGuideData.getInstance().isLoad == false)
         {
            NewGuideData.getInstance().isLoad = true;
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               NewEveryDayData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
               MewActivityData.getInstance().initXML(XMLSingle.getInstance().everyDayTask,param1);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
         if(NewGuideData.getInstance().mc == null)
         {
            if(m_myloader == null)
            {
               m_myloader = new YJFYLoader();
            }
            m_myloader.getClass("ExShow.swf","listitem",getShowSuccess,getFail);
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         NewGuideData.getInstance().mc = new _loc2_();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
      }
      
      public function callrefreshTip(param1:Event) : void
      {
         callmain();
         calleveryday();
         callactivity();
      }
      
      private function callmain() : void
      {
         var _loc3_:MainLineTaskVO = null;
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:Boolean = false;
         _loc9_ = 0;
         while(_loc9_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs.length)
         {
            if(NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].isGotReward == false && NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].isWorking)
            {
               _loc3_ = NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_];
               _loc2_ = 0;
               _loc8_ = 0;
               while(_loc8_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getCurrentTaskGoalNum())
               {
                  _loc2_ += NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getCurrentTaskGoalByIndex(_loc8_).num;
                  _loc8_++;
               }
               _loc1_ = 0;
               _loc6_ = 0;
               while(_loc6_ < NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getNeedTaskGoalNum())
               {
                  _loc1_ += NewMainTaskData.getInstance().currentPhaseTaskList.taskVOs[_loc9_].getNeedTaskGoalByIndex(_loc6_).num;
                  _loc6_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc4_ = true;
                  _loc5_++;
               }
            }
            _loc9_++;
         }
         if(_loc4_)
         {
            if(m_mainTask && m_mainTask.getShow())
            {
               (m_mainTask.getShow()["mctip"] as MovieClip).visible = true;
               m_mainNumTxt.text = String(_loc5_);
            }
         }
         else if(m_mainTask && m_mainTask.getShow())
         {
            (m_mainTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
      
      private function calleveryday() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         NewEveryDayData.getInstance().refreshData();
         _loc6_ = 0;
         while(_loc6_ < NewEveryDayData.getInstance().everyDayTaskVOs.length)
         {
            if(NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_ids.length)
               {
                  _loc2_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                  _loc1_ += NewEveryDayData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_];
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc3_ = true;
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            if(m_dayTask && m_dayTask.getShow())
            {
               (m_dayTask.getShow()["mctip"] as MovieClip).visible = true;
               m_dayNumTxt.text = String(_loc4_);
            }
         }
         else if(m_dayTask && m_dayTask.getShow())
         {
            (m_dayTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
      
      private function callactivity() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         MewActivityData.getInstance().refreshData();
         _loc6_ = 0;
         while(_loc6_ < MewActivityData.getInstance().everyDayTaskVOs.length)
         {
            if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_].isGotReward != 1)
            {
               _loc2_ = 0;
               _loc1_ = 0;
               _loc5_ = 0;
               while(_loc5_ < MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_ids.length)
               {
                  if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is LimitingTimeTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_];
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is AccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc6_] as AccumulatedTaskVO).taskCount;
                  }
                  else if(MewActivityData.getInstance().everyDayTaskVOs[_loc6_] is LimtingTimeAccumulatedTaskVO)
                  {
                     _loc2_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].currentTaskGoalVO_nums[_loc5_];
                     _loc1_ += MewActivityData.getInstance().everyDayTaskVOs[_loc6_].taskGoalVO_nums[_loc5_] * (MewActivityData.getInstance().everyDayTaskVOs[_loc6_] as LimtingTimeAccumulatedTaskVO).taskCount;
                  }
                  _loc5_++;
               }
               if(_loc2_ >= _loc1_ && _loc1_ != 0)
               {
                  _loc3_ = true;
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         if(_loc3_)
         {
            if(m_activiyTask && m_activiyTask.getShow())
            {
               (m_activiyTask.getShow()["mctip"] as MovieClip).visible = true;
               m_activityNumTxt.text = String(_loc4_);
            }
         }
         else if(m_activiyTask && m_activiyTask.getShow())
         {
            (m_activiyTask.getShow()["mctip"] as MovieClip).visible = false;
         }
      }
   }
}

