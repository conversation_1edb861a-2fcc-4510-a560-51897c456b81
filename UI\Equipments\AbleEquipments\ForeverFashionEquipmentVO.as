package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class ForeverFashionEquipmentVO extends AbleEquipmentVO
   {
      
      public var addExtraAttrValue:Number = 0;
      
      public var addExtraAttrEx:String = "";
      
      public var addPlayerAttributes:Vector.<String> = new Vector.<String>();
      
      public var addPlayerAttributeValues:Vector.<Number> = new Vector.<Number>();
      
      public function ForeverFashionEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         addExtraAttrValue = 0;
         addExtraAttrEx = "";
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         var _loc2_:int = 0;
         if(addPlayerAttributes != null)
         {
            _loc1_ = int(addPlayerAttributes.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               addPlayerAttributes[_loc2_] = null;
               _loc2_++;
            }
            addPlayerAttributes = null;
         }
         if(addPlayerAttributeValues != null)
         {
            _loc1_ = int(addPlayerAttributeValues.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               addPlayerAttributeValues[_loc2_] = null;
               _loc2_++;
            }
            addPlayerAttributeValues = null;
         }
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:ForeverFashionEquipmentVO = new ForeverFashionEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         _loc2_ = int(addPlayerAttributes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as ForeverFashionEquipmentVO).addPlayerAttributes.push(addPlayerAttributes[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerAttributeValues.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as ForeverFashionEquipmentVO).addPlayerAttributeValues.push(addPlayerAttributeValues[_loc3_]);
            _loc3_++;
         }
         (param1 as ForeverFashionEquipmentVO).addExtraAttrValue = addExtraAttrValue;
         (param1 as ForeverFashionEquipmentVO).addExtraAttrEx = addExtraAttrEx;
      }
   }
}

