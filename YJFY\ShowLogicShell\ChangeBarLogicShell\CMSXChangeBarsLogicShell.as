package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class CMSXChangeBarsLogicShell
   {
      
      private var _show:Sprite;
      
      private var _cmsxChangeBars:Vector.<CMSXChangeBarLogicShell>;
      
      public function CMSXChangeBarsLogicShell()
      {
         super();
      }
      
      public function setShow(param1:Sprite) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:DisplayObject = null;
         var _loc2_:CMSXChangeBarLogicShell = null;
         _show = param1;
         _loc3_ = param1.numChildren;
         _cmsxChangeBars = new Vector.<CMSXChangeBarLogicShell>();
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = param1.getChildAt(_loc5_);
            _loc2_ = new CMSXChangeBarLogicShell();
            _loc2_.setShow(_loc4_ as Sprite);
            _cmsxChangeBars.push(_loc2_);
            _loc5_++;
         }
      }
      
      public function getShow() : Sprite
      {
         return _show;
      }
      
      public function clear() : void
      {
         _show = null;
         ClearUtil.nullArr(_cmsxChangeBars);
         _cmsxChangeBars = null;
      }
      
      public function change(param1:Number) : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         if(isNaN(param1))
         {
            param1 = 0;
         }
         if(param1 > _cmsxChangeBars.length)
         {
            throw new Error(param1 + "超出了可以的最大值：" + _cmsxChangeBars.length);
         }
         var _loc3_:int = param1;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _cmsxChangeBars[_loc5_].change(1);
            _loc5_++;
         }
         var _loc4_:Number = param1 - _loc3_;
         if(_loc3_ < _cmsxChangeBars.length)
         {
            _cmsxChangeBars[_loc3_].change(_loc4_);
            _loc5_ = _loc3_ + 1;
            while(_loc5_ < _cmsxChangeBars.length)
            {
               _cmsxChangeBars[_loc5_].change(0);
               _loc5_++;
            }
         }
      }
   }
}

