package UI.WorldBoss.GetNextStepActionEntities
{
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class GetNextStepActionEntitiesFactory
   {
      
      private var m_classes:Array;
      
      public function GetNextStepActionEntitiesFactory()
      {
         super();
         m_classes = [GetNextStepActionEntities1,GetNextStepActionEntites2];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_classes);
         m_classes = null;
      }
      
      public function createByClassName(param1:String) : IGetNextStepActionEntities
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
   }
}

