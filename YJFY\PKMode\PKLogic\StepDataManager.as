package YJFY.PKMode.PKLogic
{
   import YJFY.Utils.ClearUtil;
   
   public class StepDataManager
   {
      
      private var m_oneStepDatas:Vector.<OneStepData>;
      
      private var m_currentSteDataIndex:int;
      
      private var m_isAbleAddStepData:Boolean;
      
      private var m_isStartPKAnimation:Boolean;
      
      public function StepDataManager()
      {
         super();
         m_oneStepDatas = new Vector.<OneStepData>();
         m_currentSteDataIndex = -1;
         m_isAbleAddStepData = true;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_oneStepDatas);
         m_oneStepDatas = null;
      }
      
      public function addOneStepData(param1:OneStepData) : void
      {
         if(m_isAbleAddStepData == false)
         {
            throw new Error("现在不能再增加数据了");
         }
         m_oneStepDatas.push(param1);
      }
      
      public function startPKAnimation() : void
      {
         m_isAbleAddStepData = false;
         m_isStartPKAnimation = true;
      }
      
      public function getOneStepDataByOrder() : OneStepData
      {
         ++m_currentSteDataIndex;
         return m_oneStepDatas[m_currentSteDataIndex];
      }
      
      public function getIsEndAnimation() : Boolean
      {
         return m_currentSteDataIndex >= m_oneStepDatas.length - 1;
      }
   }
}

