package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_MonkeySkill3 extends AttackSkill
   {
      
      private const m_const_produceImageFrameLabel:String = "produceImage";
      
      private const m_const_skillEndFrameLabel:String = "blow^stop^";
      
      private var m_bodyDefId:String;
      
      private var m_imageDurationTime:int;
      
      private var m_imageMoveSpeed:uint;
      
      private var m_imageXML:XML;
      
      private var m_createdImageEntitys:Vector.<TimeLimitEntity>;
      
      private var m_isAblePushEntity:Boolean;
      
      private var m_monkeySkill3Listeners:Vector.<IMonkeySkill3Listener>;
      
      private var m_pushEntityListener:TimeLimitEntity_MonkeySkill3Listener;
      
      private var m_animalEntityListener:AnimalEntityListener;
      
      public function Skill_MonkeySkill3()
      {
         super();
         m_createdImageEntitys = new Vector.<TimeLimitEntity>();
         m_isAblePushEntity = true;
         m_monkeySkill3Listeners = new Vector.<IMonkeySkill3Listener>();
         m_pushEntityListener = new TimeLimitEntity_MonkeySkill3Listener();
         m_pushEntityListener.pushEntityFun = listeneredPushEntity;
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.attackSuccessFun = listeneredImageAttackSuccess;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         m_bodyDefId = null;
         m_imageXML = null;
         if(m_createdImageEntitys)
         {
            _loc1_ = int(m_createdImageEntitys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_createdImageEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_createdImageEntitys[_loc2_]);
               m_createdImageEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_createdImageEntitys.length = 0;
            m_createdImageEntitys = null;
         }
         ClearUtil.nullArr(m_monkeySkill3Listeners,false,false,false);
         m_monkeySkill3Listeners = null;
         ClearUtil.clearObject(m_pushEntityListener);
         m_pushEntityListener = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_imageXML = param1.image[0];
         m_imageDurationTime = int(param1.@imageDurationTime);
         m_imageMoveSpeed = int(param1.@imageMoveSpeed);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      public function setIsAblePushEntity(param1:Boolean) : void
      {
         m_isAblePushEntity = param1;
      }
      
      public function addMonkeySkill3Listener(param1:IMonkeySkill3Listener) : void
      {
         m_monkeySkill3Listeners.push(param1);
      }
      
      public function removeMonkeySkill3Listener(param1:IMonkeySkill3Listener) : void
      {
         var _loc2_:int = int(m_monkeySkill3Listeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_monkeySkill3Listeners.splice(_loc2_,1);
            _loc2_ = int(m_monkeySkill3Listeners.indexOf(param1));
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      protected function listeneredPushEntity(param1:TimeLimitEntity_MonkeySkill3, param2:IEntity) : void
      {
         var _loc5_:int = 0;
         var _loc4_:Vector.<IMonkeySkill3Listener> = m_monkeySkill3Listeners.slice(0);
         var _loc3_:int = int(_loc4_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_[_loc5_].pushEntity(this,param1,param2);
            _loc4_[_loc5_] = null;
            _loc5_++;
         }
         _loc4_.length = 0;
         _loc4_ = null;
         param1.setIsAblePushEntity(m_isAblePushEntity);
      }
      
      protected function listeneredImageAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as TimeLimitEntity).setAttackData(m_attackData);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         var _loc2_:TimeLimitEntity = null;
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case "produceImage":
               if(m_isRun != false)
               {
                  _loc2_ = new TimeLimitEntity_MonkeySkill3();
                  _loc2_.setOwner(m_owner);
                  _loc2_.setMyLoader(m_myLoader);
                  _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
                  _loc2_.setSoundManager(m_world.getSoundManager());
                  _loc2_.setDisappearTime(m_imageDurationTime,"^stop^");
                  _loc2_.initByXML(m_imageXML);
                  _loc2_.setNewPosition(m_owner.getX(),m_owner.getY() + 10,m_owner.getZ());
                  _loc2_.setDirection(m_owner.getShowDirection(),0);
                  _loc2_.attack();
                  _loc2_.setMoveSpeed(m_imageMoveSpeed);
                  m_world.addEntity(_loc2_);
                  m_createdImageEntitys.push(_loc2_);
                  _loc2_.addAnimalEntityListener(m_animalEntityListener);
                  (_loc2_ as TimeLimitEntity_MonkeySkill3).setPushEntityListener(m_pushEntityListener);
                  return;
               }
               break;
            case "blow^stop^":
               endSkill2();
         }
      }
   }
}

