package UI.Task.TaskReward
{
   import UI.MyFunction;
   
   public class TaskRewardVO_Equipment extends TaskRewardVO
   {
      
      private var _dataXML:XML;
      
      public function TaskRewardVO_Equipment()
      {
         super();
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         var _loc2_:int = 0;
         _dataXML = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.dataXML = _dataXML;
      }
      
      public function setDataXML(param1:XML) : void
      {
         _antiwear.dataXML = param1;
      }
      
      public function get equipments_IDs() : Vector.<int>
      {
         return MyFunction.getInstance().excreteString(String(_antiwear.dataXML.@ids));
      }
      
      public function get equipments_Nums() : Vector.<int>
      {
         return MyFunction.getInstance().excreteString(String(_antiwear.dataXML.@nums));
      }
      
      public function get isBinding() : Boolean
      {
         return String(_antiwear.dataXML.@binding) == "true";
      }
   }
}

