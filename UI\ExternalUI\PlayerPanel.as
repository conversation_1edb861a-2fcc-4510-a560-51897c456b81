package UI.ExternalUI
{
   import UI.EnterFrameTime;
   import UI.EquipEquipmentFuns.EquipEquipmentFunction;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.ExternalUI.SkillCells.UsedSkillCellLogicShell;
   import UI.GamingUI;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Other.CDAnimationShape;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import YJFY.EndlessMode.EndlessManage;
   import YJFY.ShowLogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShellType;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Stage;
   
   public class PlayerPanel
   {
      
      public var _show:MovieClip;
      
      private var _headShow:MovieClipPlayLogicShell;
      
      private var _bloodBar:CMSXChangeBarLogicShell;
      
      private var _magicBar:CMSXChangeBarLogicShell;
      
      private var _experienceBar:CMSXChangeBarLogicShell;
      
      private var _nuQiBarMc:MovieClipPlayLogicShell;
      
      private var _nuQiBar:CMSXChangeBarLogicShell;
      
      public var _eqContainer_1:MovieClipPlayLogicShell;
      
      public var _eqContainer_2:MovieClipPlayLogicShell;
      
      public var _potion1:MovieClipPlayLogicShell;
      
      public var _potion2:MovieClipPlayLogicShell;
      
      private var _potion1CdAnimation:CDAnimationShape;
      
      private var _potion2CdAnimation:CDAnimationShape;
      
      private var _skillCells:Vector.<UsedSkillCellLogicShell>;
      
      private var _levelShow:MultiPlaceNumLogicShell;
      
      private var _player:Player;
      
      private var _fastUseEqs:Vector.<String>;
      
      private var _preferFastUse_0:EquipmentVO;
      
      private var _preferFastUse_1:EquipmentVO;
      
      private var _isActive:Boolean;
      
      private var _isStop:Boolean;
      
      private var _lastTime:Number;
      
      private var _potion1Cd:int;
      
      private var _potion2Cd:int;
      
      private var m_ableFastUse:Boolean;
      
      public function PlayerPanel()
      {
         super();
         m_ableFastUse = true;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         _show = param1;
      }
      
      public function refresh(param1:int) : void
      {
         if(param1 & 0x80)
         {
            setBlood(_player.playerVO.bloodPercent,_player.playerVO.bloodVolume);
            setMagic(_player.playerVO.magicPercent,_player.playerVO.maxMagic);
            setExperience(_player.playerVO.experiencePercent,_player.playerVO.experienceVolume);
            setNuQi(_player.playerVO.nuQiCurrentValue,_player.playerVO.nuQiAllValue);
            setLevel(_player.playerVO.level);
         }
         if(param1 & 0x40)
         {
            addSkillVOs(player.playerVO.skillVOs);
            addPetSkill(_player.playerVO.pet);
         }
      }
      
      public function initPanel() : void
      {
         addToVector();
         _experienceBar = new CMSXChangeBarLogicShell();
         _experienceBar.setShow(_show["experienceBar"]);
         var _loc1_:CMSXChangeBarLogicShellType = new CMSXChangeBarLogicShellType();
         _loc1_.type = "vertical";
         _experienceBar.setType(_loc1_);
         _magicBar = new CMSXChangeBarLogicShell();
         _magicBar.setShow(_show["magicBar"]);
         _bloodBar = new CMSXChangeBarLogicShell();
         _bloodBar.setShow(_show["bloodBar"]);
         _nuQiBarMc = new MovieClipPlayLogicShell();
         _nuQiBarMc.setShow(_show["nuQiBar"]);
         _nuQiBar = new CMSXChangeBarLogicShell();
         _nuQiBar.setShow(_nuQiBarMc.getShow()["bar"]);
         _levelShow = new MultiPlaceNumLogicShell();
         _levelShow.setShow(_show["levelShow"]);
         _headShow = new MovieClipPlayLogicShell();
         _headShow.setShow(_show["headShow"]);
         _eqContainer_1 = new MovieClipPlayLogicShell();
         _eqContainer_1.setShow(_show["EqCell_1"]);
         _eqContainer_2 = new MovieClipPlayLogicShell();
         _eqContainer_2.setShow(_show["EqCell_2"]);
         _potion1 = new MovieClipPlayLogicShell();
         _potion1.setShow(_eqContainer_1.getShow()["potion"]);
         _potion2 = new MovieClipPlayLogicShell();
         _potion2.setShow(_eqContainer_2.getShow()["potion"]);
         _potion1CdAnimation = new CDAnimationShape();
         _potion1CdAnimation.y = _potion1.getShow().y + _potion1.getShow().height / 2;
         _potion1CdAnimation.x = _potion1.getShow().x + _potion1.getShow().width / 2;
         _potion1CdAnimation.init(_potion1.getShow().width,_potion1.getShow().height,6736930,0.8);
         _potion1CdAnimation.drawShape(2 * 3.141592653589793);
         _eqContainer_1.getShow().addChild(_potion1CdAnimation);
         _potion1.getShow().mask = _potion1CdAnimation;
         _potion2CdAnimation = new CDAnimationShape();
         _potion2CdAnimation.y = _potion2.getShow().y + _potion2.getShow().height / 2;
         _potion2CdAnimation.x = _potion2.getShow().x + _potion2.getShow().width / 2;
         _potion2CdAnimation.init(_potion2.getShow().width,_potion2.getShow().height,6736930,0.8);
         _potion2CdAnimation.drawShape(2 * 3.141592653589793);
         _eqContainer_2.getShow().addChild(_potion2CdAnimation);
         _potion2.getShow().mask = _potion2CdAnimation;
         isActive = true;
      }
      
      public function setHeadShow(param1:String) : void
      {
         _headShow.gotoAndStop(param1);
      }
      
      public function setBlood(param1:Number, param2:int = 0) : void
      {
         _bloodBar.change(param1);
         _bloodBar.setDataShow("" + Math.round(param1 * param2) + "/" + param2);
      }
      
      public function setMagic(param1:Number, param2:int = 0) : void
      {
         _magicBar.change(param1);
         _magicBar.setDataShow("" + Math.round(param1 * param2) + "/" + param2);
      }
      
      public function setNuQi(param1:Number, param2:int = 0) : void
      {
         if(param1 == param2)
         {
            _nuQiBarMc.gotoAndStop("full");
         }
         else
         {
            _nuQiBarMc.gotoAndStop("normal");
         }
         _nuQiBar.change(param1 / param2);
         _nuQiBar.setDataShow("" + param1 + "/" + param2);
      }
      
      public function setExperience(param1:Number, param2:int = 0) : void
      {
         _experienceBar.change(param1);
      }
      
      public function setLevel(param1:int) : void
      {
         _levelShow.showNum(param1);
      }
      
      public function addSkillVOs(param1:Vector.<SkillVO>) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         if(param1)
         {
            _loc2_ = _skillCells.length - 1;
            _loc3_ = int(param1.length);
            _loc5_ = 0;
            while(_loc5_ < _loc2_ && _loc5_ < _loc3_)
            {
               if(param1[_loc5_])
               {
                  if(param1[_loc5_].type == "playerActive")
                  {
                     _skillCells[_loc4_].removeSkillVO();
                     _skillCells[_loc4_].addSkillVO(param1[_loc5_]);
                     if(_player.playerVO.maxMagic * _player.playerVO.magicPercent >= (param1[_loc5_] as PlayerActiveSkillVO).manaCost)
                     {
                        (_skillCells[_loc4_] as UsedSkillCellLogicShell).setActive();
                     }
                     else
                     {
                        (_skillCells[_loc4_] as UsedSkillCellLogicShell).setNoActive();
                     }
                     (_skillCells[_loc4_] as UsedSkillCellLogicShell).setIsUntileUse(param1[_loc5_].isUntileUse);
                     _loc4_++;
                  }
               }
               else
               {
                  _skillCells[_loc4_].removeSkillVO();
                  (_skillCells[_loc4_] as UsedSkillCellLogicShell).setActive();
                  _loc4_++;
               }
               _loc5_++;
            }
         }
      }
      
      public function addPetSkill(param1:Pet) : void
      {
         var _loc2_:SkillVO = null;
         if(param1)
         {
            if(param1.petEquipmentVO)
            {
               if(param1.petEquipmentVO.activeSkillVO)
               {
                  _loc2_ = param1.petEquipmentVO.activeSkillVO;
                  _skillCells[5].removeSkillVO();
                  _skillCells[5].addSkillVO(_loc2_);
                  if(_player.playerVO.maxMagic * _player.playerVO.magicPercent >= (_loc2_ as PetActiveSkillVO).manaCost && param1.petEquipmentVO.essentialPercent > 0)
                  {
                     (_skillCells[5] as UsedSkillCellLogicShell).setActive();
                  }
                  else
                  {
                     (_skillCells[5] as UsedSkillCellLogicShell).setNoActive();
                  }
                  return;
               }
            }
         }
         _skillCells[5].removeSkillVO();
         (_skillCells[5] as UsedSkillCellLogicShell).setActive();
      }
      
      public function get skillCells() : Vector.<UsedSkillCellLogicShell>
      {
         return _skillCells;
      }
      
      public function get player() : Player
      {
         return _player;
      }
      
      public function set player(param1:Player) : void
      {
         var _loc3_:int = 0;
         _player = param1;
         setHeadShow(_player.playerVO.playerType);
         var _loc2_:int = int(_skillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _skillCells[_loc3_].setPlayerVO(_player.playerVO);
            _loc3_++;
         }
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_skillCells);
         _skillCells = null;
         _show = null;
         if(_headShow)
         {
            _headShow.clear();
         }
         _headShow = null;
         if(_bloodBar)
         {
            _bloodBar.clear();
         }
         _bloodBar = null;
         if(_magicBar)
         {
            _magicBar.clear();
         }
         _magicBar = null;
         if(_nuQiBarMc)
         {
            _nuQiBarMc.clear();
         }
         _nuQiBarMc = null;
         if(_nuQiBar)
         {
            _nuQiBar.clear();
         }
         _nuQiBar = null;
         if(_experienceBar)
         {
            _experienceBar.clear();
         }
         _experienceBar = null;
         if(_levelShow)
         {
            _levelShow.clear();
         }
         _levelShow = null;
         if(_eqContainer_1)
         {
            _eqContainer_1.clear();
         }
         _eqContainer_1 = null;
         if(_eqContainer_2)
         {
            _eqContainer_2.clear();
         }
         _eqContainer_2 = null;
         if(_potion1)
         {
            _potion1.clear();
         }
         if(_potion2)
         {
            _potion2.clear();
         }
         _potion1 = null;
         _potion2 = null;
         _player = null;
         ClearUtil.nullArr(_fastUseEqs);
         _fastUseEqs = null;
         _preferFastUse_0 = null;
         _preferFastUse_1 = null;
      }
      
      public function get isActive() : Boolean
      {
         return _isActive;
      }
      
      public function set isActive(param1:Boolean) : void
      {
         if(_isActive != param1)
         {
            _isActive = param1;
            if(param1)
            {
               MyFunction.getInstance().changeSaturation(_show,0);
            }
            else
            {
               MyFunction.getInstance().changeSaturation(_show,-100);
            }
         }
      }
      
      private function addToVector() : void
      {
         var _loc3_:int = 0;
         var _loc1_:UsedSkillCellLogicShell = null;
         _skillCells = new Vector.<UsedSkillCellLogicShell>();
         var _loc2_:int = 6;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ = new UsedSkillCellLogicShell();
            _loc1_.setShow(_show["skillCell_" + (_loc3_ + 1)],_show["timeShow" + (_loc3_ + 1)]);
            _skillCells.push(_loc1_);
            _loc3_++;
         }
      }
      
      public function refreshFastUseEq() : void
      {
         var _loc2_:int = 0;
         if(_preferFastUse_0)
         {
            _loc2_ = int(_player.playerVO.packageEquipmentVOs.indexOf(_preferFastUse_0));
            if(_loc2_ == -1)
            {
               _preferFastUse_0 = null;
            }
         }
         if(_preferFastUse_1)
         {
            _loc2_ = int(_player.playerVO.packageEquipmentVOs.indexOf(_preferFastUse_1));
            if(_loc2_ == -1)
            {
               _preferFastUse_1 = null;
            }
         }
         var _loc3_:XML = XMLSingle.getInstance().dataXML.FastUseEquip[0];
         var _loc1_:Vector.<String> = coutPlayerFastUseEquip(_player,_loc3_);
         ClearUtil.nullArr(_fastUseEqs);
         if(_preferFastUse_0)
         {
            _potion1.gotoAndStop("Id_" + _preferFastUse_0.id);
         }
         else if(_loc1_[0])
         {
            _potion1.gotoAndStop("Id_" + _loc1_[0]);
         }
         else
         {
            _potion1.gotoAndStop("noHave");
         }
         if(_preferFastUse_1)
         {
            _potion2.gotoAndStop("Id_" + _preferFastUse_1.id);
         }
         else if(_loc1_[1])
         {
            _potion2.gotoAndStop("Id_" + _loc1_[1]);
         }
         else
         {
            _potion2.gotoAndStop("noHave");
         }
         _fastUseEqs = _loc1_;
         if(EndlessManage.getInstance().IsEndlessMode)
         {
            DisplayFastUseEq();
         }
      }
      
      public function HideFastUseEq() : void
      {
         this["_eqContainer_1"].gotoAndStop(15);
         this["_eqContainer_2"].gotoAndStop(11);
      }
      
      private function DisplayFastUseEq() : void
      {
         this["_eqContainer_1"].gotoAndStop(1);
         this["_eqContainer_2"].gotoAndStop(1);
      }
      
      public function useFastUseEq(param1:int) : void
      {
         var packageEq:EquipmentVO;
         var fastUseEqId:String;
         var stage:Stage;
         var myThis:PlayerPanel;
         var equipmentFun:EquipEquipmentFunction;
         var equipEqListener:EquipEqListener;
         var value:int = param1;
         if(m_ableFastUse == false)
         {
            return;
         }
         if(value < 0)
         {
            return;
         }
         if(this["_potion" + (value + 1) + "Cd"] > 0)
         {
            return;
         }
         if(_fastUseEqs == null)
         {
            return;
         }
         if(_fastUseEqs.length < value + 1)
         {
            return;
         }
         packageEq = this["_preferFastUse_" + value];
         if(packageEq == null)
         {
            fastUseEqId = _fastUseEqs[value];
            if(!Boolean(fastUseEqId))
            {
               return;
            }
            packageEq = MyFunction2.getOneEquipmentVOById(fastUseEqId,_player.playerVO.packageEquipmentVOs);
         }
         if(packageEq)
         {
            stage = _show.stage;
            myThis = this;
            equipmentFun = new EquipEquipmentFunction();
            equipEqListener = new EquipEqListener();
            equipEqListener.actionAfterUseFun = function():void
            {
               decFastUseEq(fastUseEqId);
               myThis["_eqContainer_" + (value + 1)].gotoAndPlay("useStart");
               myThis["_potion" + (value + 1) + "Cd"] = 1000;
               m_ableFastUse = true;
            };
            equipEqListener.actionAfterUnableUseFun = function():void
            {
               myThis["_eqContainer_" + (value + 1)].gotoAndPlay("UnableUseStart",null,null,function():void
               {
                  myThis["_eqContainer_" + (value + 1)].gotoAndStop("normal");
               },null);
               m_ableFastUse = true;
            };
            m_ableFastUse = false;
            equipmentFun.EquipEquipmentVOAction(packageEq,_player,equipEqListener,GamingUI.getInstance().getNewestTimeStrFromSever());
         }
      }
      
      private function decFastUseEq(param1:String) : void
      {
         refreshFastUseEq();
      }
      
      private function coutPlayerFastUseEquip(param1:Player, param2:XML) : Vector.<String>
      {
         var _loc3_:String = null;
         var _loc4_:int = 0;
         var _loc8_:XMLList = null;
         var _loc11_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:Boolean = false;
         var _loc10_:Vector.<String> = new Vector.<String>();
         var _loc9_:XMLList = param2.children();
         var _loc5_:int = int(_loc9_.length());
         _loc4_ = 0;
         while(_loc4_ < _loc5_)
         {
            _loc8_ = _loc9_[_loc4_].item;
            _loc7_ = int(_loc8_.length());
            _loc11_ = 0;
            while(_loc11_ < _loc7_)
            {
               _loc6_ = false;
               _loc3_ = String(_loc8_[_loc11_].@id);
               _loc6_ = MyFunction2.IsHaveEqInEqsById(_loc3_,param1.playerVO.packageEquipmentVOs);
               if(_loc6_)
               {
                  break;
               }
               _loc11_++;
            }
            if(_loc6_)
            {
               _loc10_.push(_loc3_);
            }
            else
            {
               _loc10_.push(null);
            }
            _loc4_++;
         }
         return _loc10_;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc5_:int = 0;
         if(_isStop)
         {
            return;
         }
         if(isNaN(_lastTime))
         {
            _lastTime = param1.getGameTimeForThisInit();
         }
         var _loc2_:int = param1.getGameTimeForThisInit() - _lastTime;
         var _loc4_:int = _loc2_ / 1000;
         if(_potion1Cd > 0)
         {
            _potion1Cd = Math.max(0,_potion1Cd - _loc2_);
            _potion1CdAnimation.drawShape((1000 - _potion1Cd) / 1000 * 2 * 3.141592653589793);
         }
         if(_potion2Cd > 0)
         {
            _potion2Cd = Math.max(0,_potion2Cd - _loc2_);
            _potion2CdAnimation.drawShape((1000 - _potion2Cd) / 1000 * 2 * 3.141592653589793);
         }
         var _loc3_:int = int(_skillCells.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _skillCells[_loc5_].render(param1);
            _loc5_++;
         }
      }
      
      public function set preferFastUse_bloodEq(param1:EquipmentVO) : void
      {
         _preferFastUse_0 = param1;
      }
      
      public function set preferFastUse_magicEq(param1:EquipmentVO) : void
      {
         _preferFastUse_1 = param1;
      }
      
      public function stopGame() : void
      {
         _lastTime = NaN;
         _isStop = true;
      }
      
      public function continueGame() : void
      {
         _isStop = false;
         _lastTime = new Date().getTime();
      }
   }
}

import UI.EquipEquipmentFuns.IEquipEqListener;

class EquipEqListener implements IEquipEqListener
{
   
   public var showWarningBoxFun:Function;
   
   public var actionAfterUseFun:Function;
   
   public var actionAfterUnableUseFun:Function;
   
   private var _warningTextFontSize:int = 12;
   
   public function EquipEqListener()
   {
      super();
   }
   
   public function showWarning(param1:String, param2:int) : void
   {
      if(showWarningBoxFun)
      {
         showWarningBoxFun(param1,param2);
      }
   }
   
   public function setWarningTextFontSizse(param1:int) : void
   {
      _warningTextFontSize = param1;
   }
   
   public function getWarningTextFontSize() : int
   {
      return _warningTextFontSize;
   }
   
   public function actionAfterUse() : void
   {
      if(actionAfterUseFun)
      {
         actionAfterUseFun();
      }
   }
   
   public function actionAfterUnableUse() : void
   {
      if(actionAfterUnableUseFun)
      {
         actionAfterUnableUseFun();
      }
   }
}
