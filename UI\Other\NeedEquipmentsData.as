package UI.Other
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MyFunction;
   import UI.Players.PlayerVO;
   import YJFY.Utils.ClearUtil;
   
   public class NeedEquipmentsData
   {
      
      protected var m_needEquipmentDatas:Vector.<NeedEquipmentData>;
      
      public function NeedEquipmentsData()
      {
         super();
         m_needEquipmentDatas = new Vector.<NeedEquipmentData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_needEquipmentDatas);
         m_needEquipmentDatas = null;
      }
      
      public function initByXML(param1:XML, param2:String) : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:NeedEquipmentData = null;
         var _loc3_:XMLList = param1.children();
         _loc4_ = int(_loc3_ ? _loc3_.length() : 0);
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = new NeedEquipmentData(_loc3_[_loc6_].@id,_loc3_[_loc6_].@num,param2);
            m_needEquipmentDatas.push(_loc5_);
            _loc6_++;
         }
      }
      
      public function getNeedEquipmentDataNum() : int
      {
         return m_needEquipmentDatas.length;
      }
      
      public function getNeedEquipmentDataByIndex(param1:int) : NeedEquipmentData
      {
         return m_needEquipmentDatas[param1];
      }
      
      public function refreshNeedEqHaveNumInPlayer(param1:PlayerVO, param2:PlayerVO) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(m_needEquipmentDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            m_needEquipmentDatas[_loc4_].setCurrentNum(0);
            m_needEquipmentDatas[_loc4_].setCurrentNum(m_needEquipmentDatas[_loc4_].getCurrentNum() + param1.getHaveEquipmentNumInPackage(m_needEquipmentDatas[_loc4_].getEquipmentVO()));
            if(param2)
            {
               m_needEquipmentDatas[_loc4_].setCurrentNum(m_needEquipmentDatas[_loc4_].getCurrentNum() + param2.getHaveEquipmentNumInPackage(m_needEquipmentDatas[_loc4_].getEquipmentVO()));
            }
            _loc4_++;
         }
      }
      
      public function getIsEnough() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_needEquipmentDatas.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_needEquipmentDatas[_loc2_].getCurrentNum() < m_needEquipmentDatas[_loc2_].getNum())
            {
               return false;
            }
            _loc2_++;
         }
         return true;
      }
      
      public function minusNeedEquipmentsFromPlayer(param1:PlayerVO, param2:PlayerVO) : void
      {
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:* = undefined;
         if(getIsEnough() == false)
         {
            throw new Error("is not enough, but do minus.");
         }
         if(param1 == null)
         {
            throw new Error("playerVO1 don\'t equal to null.");
         }
         var _loc5_:int = int(m_needEquipmentDatas.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc4_ = param1.packageEquipmentVOs;
            _loc3_ = MyFunction.getInstance().minusEquipmentVOs(_loc4_,m_needEquipmentDatas[_loc6_].getNum(),m_needEquipmentDatas[_loc6_].getEquipmentVO().id);
            if(_loc3_)
            {
               if(param2 == null)
               {
                  throw new Error(" num of equiment in playerVO1 package is not enough to minus when playerVO2 equal to 0");
               }
               _loc4_ = param2.packageEquipmentVOs;
               MyFunction.getInstance().minusEquipmentVOs(_loc4_,_loc3_,m_needEquipmentDatas[_loc6_].getEquipmentVO().id);
            }
            _loc6_++;
         }
      }
   }
}

