package YJFY.Lottery
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.Players.Player;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.GetEquipmentVOsLogic.IEquipmentVOsData;
   import YJFY.Utils.ClearUtil;
   
   public class LotteryLogic
   {
      
      private var m_equipmentVOsData:EquipmentVOsData;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      public function LotteryLogic()
      {
         super();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_equipmentVOsData);
         m_equipmentVOsData = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
      }
      
      public function init1(param1:Vector.<LotteryDataOne>) : void
      {
         var _loc6_:int = 0;
         var _loc3_:Vector.<int> = new Vector.<int>();
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc4_:int = int(param1.length);
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_.push(param1[_loc6_].getEquipmentId());
            _loc2_.push(param1[_loc6_].getEquipmentNum());
            _loc6_++;
         }
         var _loc5_:Vector.<EquipmentVO> = XMLSingle.getEquipmentVOsIDs(_loc3_,XMLSingle.getInstance().equipmentXML,_loc2_,false,GamingUI.getInstance().getNewestTimeStrFromSever());
         ClearUtil.clearObject(m_equipmentVOsData);
         m_equipmentVOsData = new EquipmentVOsData();
         m_equipmentVOsData.setEquipmentVOs(_loc5_);
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
      
      public function getIsAbleAddEquipmentVOs(param1:Player) : Boolean
      {
         return m_getEquipmentVOsLogic.getIsAbleAddEquipmentVOs(m_equipmentVOsData,param1);
      }
      
      public function getEquipmentVOs(param1:Player) : Boolean
      {
         return m_getEquipmentVOsLogic.getEquipmentVOs(m_equipmentVOsData,param1);
      }
      
      public function getEquipmentVOsData() : IEquipmentVOsData
      {
         return m_equipmentVOsData;
      }
   }
}

