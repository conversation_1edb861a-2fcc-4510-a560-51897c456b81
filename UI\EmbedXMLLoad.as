package UI
{
   import UI2.medalPanel.MedalFunction;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   
   public class EmbedXMLLoad extends MySprite
   {
      
      private var _xmlURLs:Array = ["EmbedXMLs/mount/mounts1.xml","EmbedXMLs/mount/mountSkills.xml","EmbedXMLs/automaticPet/automaticPetOtherData.xml","EmbedXMLs/automaticPet/automaticPets1.xml","EmbedXMLs/automaticPet/automaticPets2.xml","EmbedXMLs/automaticPet/automaticPetSkills.xml","EmbedXMLs/equipment/buffEquipment.xml","EmbedXMLs/equipment/contract.xml","EmbedXMLs/equipment/danMedicine.xml","EmbedXMLs/equipment/dogClothes.xml","EmbedXMLs/equipment/dogWeapon.xml","EmbedXMLs/equipment/dragonClothes.xml","EmbedXMLs/equipment/dragonWeapon.xml","EmbedXMLs/equipment/egg.xml","EmbedXMLs/equipment/fashion.xml","EmbedXMLs/equipment/forceDan.xml","EmbedXMLs/equipment/foreverFashion.xml","EmbedXMLs/equipment/gourd.xml","EmbedXMLs/equipment/precious.xml","EmbedXMLs/equipment/grass.xml","EmbedXMLs/equipment/insetGem.xml","EmbedXMLs/equipment/material.xml","EmbedXMLs/equipment/medal.xml","EmbedXMLs/equipment/medal.xml","EmbedXMLs/equipment/monkeyClothes.xml","EmbedXMLs/equipment/monkeyWeapon.xml","EmbedXMLs/equipment/necklace.xml"
      ,"EmbedXMLs/equipment/pet.xml","EmbedXMLs/equipment/petSkillBook.xml","EmbedXMLs/equipment/pocket.xml","EmbedXMLs/equipment/potion.xml","EmbedXMLs/equipment/rabbitClothes.xml","EmbedXMLs/equipment/rabbitWeapon.xml","EmbedXMLs/equipment/scroll.xml","EmbedXMLs/equipment/foxClothes.xml","EmbedXMLs/equipment/foxWeapon.xml","EmbedXMLs/equipment/tieShanClothes.xml","EmbedXMLs/equipment/tieShanWeapon.xml","EmbedXMLs/equipment/dropEq.xml","EmbedXMLs/mainLineTask/mainLineTask.xml","EmbedXMLs/mainLineTask/mainLineTaskGoals.xml","EmbedXMLs/mainLineTask/mainLineTaskList.xml","EmbedXMLs/player/monkey.xml","EmbedXMLs/player/dragon.xml","EmbedXMLs/player/erLangShen.xml","EmbedXMLs/player/changE.xml","EmbedXMLs/player/fox.xml","EmbedXMLs/player/tieshan.xml","EmbedXMLs/smallAssistant/activeTaskList.xml","EmbedXMLs/smallAssistant/smallAssistant.xml","EmbedXMLs/tuDi/tuDiData.xml","EmbedXMLs/tuDi/tuDiSkill.xml","EmbedXMLs/tuDi/xiaoBa.xml","EmbedXMLs/tuDi/xiaoKong.xml","EmbedXMLs/buff.xml","EmbedXMLs/data.xml"
      ,"EmbedXMLs/farm.xml","EmbedXMLs/onLineGiftBag.xml","EmbedXMLs/privilege.xml","EmbedXMLs/Sign.xml","EmbedXMLs/skill.xml","EmbedXMLs/talent.xml","EmbedXMLs/task.xml","EmbedXMLs/text.xml","EmbedXMLs/vip.xml","EmbedXMLs/xiuLianContent.xml","EmbedXMLs/NewRank.xml","EmbedXMLs/EveryDayTask.xml","EmbedXMLs/mainLineTaskDescription.xml","EmbedXMLs/broadcast.xml","EmbedXMLs/player/houyi.xml","EmbedXMLs/equipment/houyiClothes.xml","EmbedXMLs/equipment/houyiWeapon.xml","EmbedXMLs/player/zixia.xml","EmbedXMLs/equipment/zixiaClothes.xml","EmbedXMLs/equipment/zixiaWeapon.xml","EmbedXMLs/tehui.xml","EmbedXMLs/weekPay.xml","EmbedXMLs/consumeractivity.xml","EmbedXMLs/buchang.xml","EmbedXMLs/wuyi.xml"];
      
      private var _xmls:Array = ["subMountsXML","mountSkillsXML","automaticPetOtherDataXML","subAutomaticPetsXML","subAutomaticPetsXML","automaticPetSkillsXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","allMedalsXml","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","mainLineTaskXML","mainLineTaskGoalsXML","mainLineTaskListXML","monkeyXML","dragonXML","erLangShenXML","changEXML","foxXML","tieShanXML","activeTaskListXML","smallAssistantXML","tuDiDataXML","tuDiSkillXML","xiaoBaXML","xiaoKongXML","buffXML","dataXML","farmXML"
      ,"onLineGiftBagXML","privilegeXML","signXML","skillXML","talentXML","taskXML","textXML","vipXML","xiuLianContentXML","NewRank","everyDayTask","mainLineTaskDescription","broadcast","houyiXML","subEquipmentXML","subEquipmentXML","ziXiaXML","subEquipmentXML","subEquipmentXML","tehui","weekpay","consumer","buchang","wuyi"];
      
      private var m_loadnum:int = 0;
      
      private var m_medalLoad:Boolean = false;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:VersionControl;
      
      private var m_length:int = 0;
      
      public function EmbedXMLLoad()
      {
         super();
         var _loc1_:int = int(_xmlURLs.length);
         _loc1_ = int(_xmls.length);
         m_length = _xmlURLs.length;
      }
      
      override public function clear() : void
      {
         m_versionControl = null;
         m_myLoader = null;
         ClearUtil.clearObject(_xmlURLs);
         _xmlURLs = null;
         ClearUtil.clearObject(_xmls);
         _xmls = null;
         super.clear();
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function getIsAllLoad() : Boolean
      {
         if(m_loadnum >= m_length)
         {
            return true;
         }
         return false;
      }
      
      public function initXML() : void
      {
         var _loc2_:int = 0;
         m_loadnum = 0;
         var _loc1_:int = int(_xmlURLs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_myLoader.getXML(_xmlURLs[_loc2_],getXMLSuccess,getFail);
            _loc2_++;
         }
         m_myLoader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(_xmlURLs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.xmlPath == "EmbedXMLs/equipment/medal.xml" && m_medalLoad == false)
            {
               m_medalLoad = true;
               MedalFunction.getInstance().allMedalsXml = XML(param1.resultXML);
               _xmls.splice(_loc3_,1);
               _xmlURLs.splice(_loc3_,1);
               m_loadnum++;
            }
            else if(_xmlURLs[_loc3_] == param1.xmlPath)
            {
               XMLSingle.getInstance()[_xmls[_loc3_]] = XML(param1.resultXML);
               _xmls.splice(_loc3_,1);
               _xmlURLs.splice(_loc3_,1);
               m_loadnum++;
            }
            _loc3_++;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

