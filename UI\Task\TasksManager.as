package UI.Task
{
   import UI.DataManagerParent;
   import UI.Task.TaskVO.MTaskVO;
   
   public class TasksManager extends DataManagerParent
   {
      
      public static var _instance:TasksManager = null;
      
      public var taskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var activityTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var acceptedEveryDayTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var acceptedActivityTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var wasteTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      private var _resetCount:int;
      
      private var _resetDate:String;
      
      public function TasksManager()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗？！ ");
      }
      
      public static function getInstance() : TasksManager
      {
         if(!_instance)
         {
            _instance = new TasksManager();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         var _loc2_:int = 0;
         if(taskVOs)
         {
            _loc1_ = int(taskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               taskVOs[_loc2_].clear();
               taskVOs[_loc2_] = null;
               _loc2_++;
            }
         }
         if(activityTaskVOs)
         {
            _loc1_ = int(activityTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               activityTaskVOs[_loc2_] = null;
               _loc2_++;
            }
            activityTaskVOs = null;
         }
         if(acceptedEveryDayTaskVOs)
         {
            _loc1_ = int(acceptedEveryDayTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               acceptedEveryDayTaskVOs[_loc2_].clear();
               acceptedEveryDayTaskVOs[_loc2_] = null;
               _loc2_++;
            }
         }
         if(acceptedActivityTaskVOs)
         {
            _loc1_ = int(acceptedActivityTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               acceptedActivityTaskVOs[_loc2_].clear();
               acceptedActivityTaskVOs[_loc2_] = null;
               _loc2_++;
            }
         }
         if(wasteTaskVOs)
         {
            _loc1_ = int(wasteTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               wasteTaskVOs[_loc2_] = null;
               _loc2_++;
            }
            wasteTaskVOs = null;
         }
         taskVOs = null;
         acceptedEveryDayTaskVOs = null;
         acceptedActivityTaskVOs = null;
         _instance = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.resetCount = _resetCount;
         _antiwear.resetDate = _resetDate;
      }
      
      public function get resetCount() : int
      {
         return _antiwear.resetCount;
      }
      
      public function set resetCount(param1:int) : void
      {
         _antiwear.resetCount = param1;
      }
      
      public function get resetDate() : String
      {
         return _antiwear.resetDate;
      }
      
      public function set resetDate(param1:String) : void
      {
         _antiwear.resetDate = param1;
      }
   }
}

