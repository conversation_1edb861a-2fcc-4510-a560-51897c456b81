package UI.Utils
{
   import YJFY.API_4399.SaveAPI.ILocalSave;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.Stage;
   import flash.events.DataEvent;
   import flash.net.SharedObject;
   import flash.utils.setTimeout;
   import unit4399.events.SaveEvent;
   
   public class LocalSave implements ILocalSave
   {
      
      public function LocalSave()
      {
         super();
      }
      
      public function saveData(param1:String, param2:String, param3:Object, param4:int, param5:Stage, param6:Boolean = false) : void
      {
         var shareObjName:String = param1;
         var tile:String = param2;
         var data:Object = param3;
         var index:int = param4;
         var stage:Stage = param5;
         var ui:Boolean = param6;
         setTimeout(function():void
         {
            var _loc2_:* = null;
            var _loc1_:SharedObject = SharedObject.getLocal(shareObjName);
            _loc1_.data["dateAndTime_" + index] = new TimeUtil().getTimeStr();
            _loc1_.data["title_" + index] = tile;
            _loc1_.data["obj_" + index] = data;
            _loc1_.flush();
            stage.dispatchEvent(new SaveEvent("saveuserdata",true));
         },300);
      }
      
      public function getData(param1:String, param2:int, param3:Stage, param4:Boolean) : void
      {
         var _loc5_:SharedObject = SharedObject.getLocal(param1);
         var _loc6_:Object = {};
         _loc6_.index = param2;
         _loc6_.data = _loc5_.data["obj_" + param2];
         _loc6_.title = _loc5_.data["title_" + param2] ? _loc5_.data["title_" + param2] : "";
         _loc6_.datetime = _loc5_.data["dateAndTime_" + param2] ? _loc5_.data["dateAndTime_" + param2] : "";
         var _loc7_:SaveEvent = new SaveEvent("getuserdata",_loc6_);
         param3.dispatchEvent(_loc7_);
      }
      
      public function getList(param1:String, param2:Stage) : void
      {
         var _loc4_:Object = null;
         var _loc7_:Object = null;
         var _loc9_:SaveEvent = null;
         var _loc8_:Object = null;
         var _loc5_:Array = [];
         var _loc6_:SharedObject = SharedObject.getLocal(param1);
         _loc4_ = _loc6_.data;
         for(var _loc3_ in _loc4_)
         {
            if(_loc3_.substr(0,3) == "obj")
            {
               _loc7_ = _loc4_[_loc3_];
               _loc8_ = {};
               _loc8_.index = int(_loc3_.substr(4,5));
               _loc8_.status = 0;
               _loc8_.datetime = _loc4_["dateAndTime_" + _loc8_.index] ? _loc4_["dateAndTime_" + _loc8_.index] : "";
               _loc8_.title = _loc4_["title_" + _loc8_.index] ? _loc4_["title_" + _loc8_.index] : "";
               _loc5_.push(_loc8_);
            }
         }
         _loc9_ = new SaveEvent("getuserdatalist",_loc5_);
         param2.dispatchEvent(_loc9_);
      }
      
      public function getStoreState(param1:Stage) : void
      {
         var stage:Stage = param1;
         var dataEvent:DataEvent = new DataEvent("StoreStateEvent",false,false,"1");
         setTimeout(function():void
         {
            stage.dispatchEvent(dataEvent);
         },500);
      }
   }
}

