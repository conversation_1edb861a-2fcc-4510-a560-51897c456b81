package UI.ConsumptionGuide.Button
{
   import UI.ConsumptionGuide.GuideUseEquipment;
   
   public class TalentUpGuideBtn extends GuideBtn
   {
      
      public function TalentUpGuideBtn(param1:int, param2:Function = null)
      {
         super(param1,param2);
         var _loc3_:GuideUseEquipment = new GuideUseEquipment();
         _loc3_.setShowWarningFun(_showWarningFun);
         _loc3_.setUseAfterFun(param2);
         _loc3_.addUseEqId("11000011");
         _loc3_.addUseEqId("11000012");
         setGuideEquipment(_loc3_);
      }
   }
}

