package YJFY.World.ProjectLayer
{
   import YJFY.World.TransformCoordinate;
   import flash.display.MovieClip;
   
   public class ProjectLayerData
   {
      
      private var m_screenX:Number;
      
      private var m_screenY:Number;
      
      private var m_screenZ:Number;
      
      private var m_show:MovieClip;
      
      public function ProjectLayerData()
      {
         super();
      }
      
      public function init(param1:Number, param2:Number, param3:Number, param4:MovieClip, param5:TransformCoordinate) : void
      {
         m_screenX = param1;
         m_screenY = param2;
         m_screenZ = param3;
         m_show = param4;
      }
      
      public function clear() : void
      {
         m_show = null;
      }
      
      public function getScreenX() : Number
      {
         return m_screenX;
      }
      
      public function getScreenY() : Number
      {
         return m_screenY;
      }
      
      public function getScreenZ() : Number
      {
         return m_screenZ;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
   }
}

