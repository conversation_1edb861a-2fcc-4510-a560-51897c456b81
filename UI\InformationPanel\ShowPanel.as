package UI.InformationPanel
{
   import UI.MySprite;
   import flash.display.Sprite;
   
   public class ShowPanel extends MySprite
   {
      
      private var _isVIPState:Boolean;
      
      private var _layer:Sprite;
      
      public function ShowPanel()
      {
         super();
         init();
      }
      
      public function set isVIPState(param1:Boolean) : void
      {
         if(_isVIPState != param1)
         {
            _isVIPState = param1;
            while(_layer.numChildren > 0)
            {
               _layer.removeChildAt(_layer.numChildren - 1);
            }
            if(_isVIPState)
            {
               _layer.addChild(new VIPFrame_ShowPanel());
            }
         }
      }
      
      public function init() : void
      {
         _layer = new Sprite();
         addChild(_layer);
      }
      
      override public function clear() : void
      {
         if(parent)
         {
            parent.removeChild(this);
         }
         if(_layer)
         {
            while(_layer.numChildren > 0)
            {
               _layer.removeChildAt(0);
            }
         }
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _layer = null;
      }
   }
}

