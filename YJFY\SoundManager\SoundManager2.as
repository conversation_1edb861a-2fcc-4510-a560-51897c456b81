package YJFY.SoundManager
{
   import UI.SoundManager.SoundManager;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   
   public class SoundManager2 extends SoundManager
   {
      
      private var m_clear:ClearHelper;
      
      private var m_myLoader:YJFYLoader;
      
      public function SoundManager2()
      {
         super();
         m_clear = new ClearHelper();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         super.clear();
         m_myLoader = null;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function play2(param1:String, param2:String, param3:String, param4:Number = 0, param5:int = 0, param6:SoundTransform = null) : SoundChannel
      {
         var _loc7_:SoundChannel = super.play(param1,param4,param5,param6);
         if(_loc7_ == null)
         {
            addSound2(param1,param2,param3,true,param4,param5,param6);
         }
         return _loc7_;
      }
      
      override protected function addSoundAndPlay(param1:String, param2:Number = 0, param3:int = 0, param4:SoundTransform = null) : SoundChannel
      {
         return null;
      }
      
      public function addSound2(param1:String, param2:String, param3:String, param4:Boolean = false, param5:Number = 0, param6:int = 0, param7:SoundTransform = null) : void
      {
         if(_soundList[param1] == null)
         {
            m_myLoader.getClass(param2,param3,getSoundSuccess,getFail,null,param1,[param4,param5,param6,param7]);
            m_myLoader.load();
         }
      }
      
      private function getSoundSuccess(param1:YJFYLoaderData, param2:Boolean, param3:Number = 0, param4:int = 0, param5:SoundTransform = null) : void
      {
         var _loc6_:Sound = null;
         if(m_clear == null)
         {
            return;
         }
         if(_soundList[param1.extra] == null)
         {
            _loc6_ = new param1.resultClass();
            addSound(param1.extra as String,_loc6_);
            ClearUtil.clearObject(_loc6_);
            _loc6_ = null;
         }
         if(param2)
         {
            play(param1.extra as String,param3,param4,param5);
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error("加载声音" + param1.swfPath + " " + param1.wantClassName + "失败。");
      }
   }
}

