package UI.Equipments.EquipmentVO
{
   import UI.UIInterface.ILimitEquipmentVO;
   import YJFY.Utils.ClearUtil;
   
   public class MedalEquipmentVO extends EquipmentVO implements ILimitEquipmentVO
   {
      
      public static const ONE_PLAYER_ALL_RANKLIST:String = "onePlayerAllRanklist";
      
      public static const TWO_PLAYER_WEEKLY_RANKLIST:String = "twoPlayerWeeklyRankList";
      
      public static const Teams:String = "teams";
      
      public static const TEAMS2:String = "teams2";
      
      public var addPlayerAttributes:Vector.<String> = new Vector.<String>();
      
      public var addPlayerAttributeValues:Vector.<Number> = new Vector.<Number>();
      
      public var medalType:String;
      
      private var _TimeLimit:Number = 0;
      
      private var _initTime:String;
      
      private var _remainingTime:int;
      
      private var _getServerTimeListener:GetServerTimeListenerForLimitEquipmentVO;
      
      public function MedalEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.TimeLimit = _TimeLimit;
         _antiwear.initTime = _initTime;
         _antiwear.remainingTime = _remainingTime;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         if(!_antiwear)
         {
            return;
         }
         super.clear();
         var _loc2_:int = 0;
         _loc1_ = int(addPlayerAttributes.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            addPlayerAttributes[_loc2_] = null;
            _loc2_++;
         }
         addPlayerAttributes = null;
         _loc1_ = int(addPlayerAttributeValues.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            addPlayerAttributeValues[_loc2_] = null;
            _loc2_++;
         }
         addPlayerAttributeValues = null;
         if(_getServerTimeListener)
         {
            _getServerTimeListener.removeLimitEquipmentVO(this);
         }
         ClearUtil.clearObject(_getServerTimeListener);
         _getServerTimeListener = null;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:MedalEquipmentVO = new MedalEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         _loc2_ = int(addPlayerAttributes.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            (param1 as MedalEquipmentVO).addPlayerAttributes.push(addPlayerAttributes[_loc4_]);
            _loc4_++;
         }
         _loc2_ = int(addPlayerAttributeValues.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            (param1 as MedalEquipmentVO).addPlayerAttributeValues.push(addPlayerAttributeValues[_loc4_]);
            _loc4_++;
         }
         (param1 as MedalEquipmentVO).TimeLimit = this.TimeLimit;
         (param1 as MedalEquipmentVO).initTime = this.initTime;
         (param1 as MedalEquipmentVO).remainingTime = this.remainingTime;
         var _loc3_:GetServerTimeListenerForLimitEquipmentVO = this.getGetServerTimeListener();
         if(_loc3_)
         {
            _loc3_.addLimitEquipmentVO(param1 as ILimitEquipmentVO);
         }
         (param1 as MedalEquipmentVO).setGetServerTimeListener(_loc3_);
      }
      
      public function setGetServerTimeListener(param1:GetServerTimeListenerForLimitEquipmentVO) : void
      {
         _getServerTimeListener = param1;
      }
      
      public function getGetServerTimeListener() : GetServerTimeListenerForLimitEquipmentVO
      {
         return _getServerTimeListener;
      }
      
      public function get TimeLimit() : Number
      {
         return _antiwear.TimeLimit;
      }
      
      public function set TimeLimit(param1:Number) : void
      {
         _antiwear.TimeLimit = param1;
      }
      
      public function get initTime() : String
      {
         return _antiwear.initTime;
      }
      
      public function set initTime(param1:String) : void
      {
         _antiwear.initTime = param1;
      }
      
      public function get remainingTime() : int
      {
         return _antiwear.remainingTime;
      }
      
      public function set remainingTime(param1:int) : void
      {
         _antiwear.remainingTime = param1;
      }
   }
}

