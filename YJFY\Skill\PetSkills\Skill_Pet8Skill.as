package YJFY.Skill.PetSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_JianSkill;
   import YJFY.World.World;
   
   public class Skill_Pet8Skill extends CuboidAreaAttackSkill_JianSkill
   {
      
      public function Skill_Pet8Skill()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         playDisappear();
         return true;
      }
   }
}

