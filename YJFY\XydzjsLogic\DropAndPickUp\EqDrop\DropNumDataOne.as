package YJFY.XydzjsLogic.DropAndPickUp.EqDrop
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   
   public class DropNumDataOne extends DataManagerParent
   {
      
      private var m_proWeight:Number;
      
      private var m_upPro:Number;
      
      private var m_downPro:Number;
      
      private var m_numDatas:Vector.<NumData>;
      
      public function DropNumDataOne(param1:Number, param2:Number, param3:Number)
      {
         super();
         m_numDatas = new Vector.<NumData>();
         this.proWeight = param1;
         this.upPro = param2;
         this.downPro = param3;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_numDatas);
         m_numDatas = null;
         super.clear();
      }
      
      public function initNumDatas(param1:XML) : void
      {
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:NumData = null;
         var _loc6_:Number = NaN;
         ClearUtil.clearObject(m_numDatas);
         m_numDatas = new Vector.<NumData>();
         var _loc7_:XMLList = param1.numData;
         _loc5_ = int(_loc7_.length());
         var _loc3_:Number = 0;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc3_ += Number(_loc7_[_loc9_].@proWeight);
            _loc9_++;
         }
         var _loc4_:Number = 0;
         var _loc8_:* = 0;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc6_ = Number(_loc7_[_loc9_].@proWeight);
            _loc4_ = _loc8_ + _loc6_ / _loc3_;
            _loc2_ = new NumData(_loc7_[_loc9_].@num,_loc6_,_loc4_,_loc8_);
            m_numDatas.push(_loc2_);
            _loc8_ = _loc4_;
            _loc9_++;
         }
      }
      
      public function getOneRandomNumData() : NumData
      {
         var _loc3_:int = 0;
         var _loc1_:Number = Math.random();
         var _loc2_:int = int(m_numDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_ <= m_numDatas[_loc3_].getUpPro())
            {
               return m_numDatas[_loc3_];
            }
            _loc3_++;
         }
         throw new Error("概率计算出错了");
      }
      
      public function getProWeight() : Number
      {
         return proWeight;
      }
      
      public function getUpPro() : Number
      {
         return upPro;
      }
      
      public function getDownPro() : Number
      {
         return downPro;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.proWeight = m_proWeight;
         _antiwear.upPro = m_upPro;
         _antiwear.downPro = m_downPro;
      }
      
      private function get proWeight() : Number
      {
         return _antiwear.proWeight;
      }
      
      private function set proWeight(param1:Number) : void
      {
         _antiwear.proWeight = param1;
      }
      
      private function get upPro() : Number
      {
         return _antiwear.upPro;
      }
      
      private function set upPro(param1:Number) : void
      {
         _antiwear.upPro = param1;
      }
      
      private function get downPro() : Number
      {
         return _antiwear.downPro;
      }
      
      private function set downPro(param1:Number) : void
      {
         _antiwear.downPro = param1;
      }
   }
}

