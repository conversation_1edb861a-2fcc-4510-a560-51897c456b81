package UI.MessageTextShow
{
   import UI.MySprite;
   import com.greensock.TweenLite;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class MessageTextShow extends MySprite
   {
      
      public static const TO:String = "to";
      
      public static const FROM:String = "from";
      
      private var _showText:TextField;
      
      private var _animationFunOptions:Array;
      
      private var _animationFunDurations:Array;
      
      private var _animationFunObjects:Array;
      
      private var _completeFun:Function;
      
      private var _completeFunParams:Array;
      
      public function MessageTextShow(param1:String, param2:TextFormat, param3:Array, param4:Array, param5:Array, param6:Array, param7:Function = null, param8:Array = null)
      {
         var myThis:MessageTextShow;
         var text:String = param1;
         var textFormat:TextFormat = param2;
         var filters:Array = param3;
         var animationFunOptions:Array = param4;
         var animationFunDurations:Array = param5;
         var animationFunObjects:Array = param6;
         var completeFun:Function = param7;
         var completeFunParams:Array = param8;
         super();
         _showText = new TextField();
         addChild(_showText);
         _showText.defaultTextFormat = textFormat;
         _showText.filters = filters;
         _showText.text = text;
         _showText.width = _showText.textWidth + 10;
         _showText.height = _showText.textHeight + 10;
         _animationFunOptions = animationFunOptions;
         _animationFunDurations = animationFunDurations;
         _animationFunObjects = animationFunObjects;
         myThis = this;
         _completeFun = function():void
         {
            if(Boolean(completeFun))
            {
               completeFun.apply(null,_completeFunParams);
            }
            if(parent)
            {
               parent.removeChild(myThis);
            }
            myThis.clear();
         };
         _completeFunParams = completeFunParams;
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         TweenLite.killTweensOf(this);
         _showText = null;
         _loc2_ = _animationFunOptions ? _animationFunOptions.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _animationFunOptions[_loc3_] = null;
            _loc3_++;
         }
         _animationFunOptions = null;
         _loc2_ = _animationFunDurations ? _animationFunDurations.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _animationFunDurations[_loc3_] = null;
            _loc3_++;
         }
         _animationFunDurations = null;
         _loc2_ = _animationFunObjects ? _animationFunObjects.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_animationFunObjects[_loc3_])
            {
               for(var _loc1_ in _animationFunObjects[_loc3_])
               {
                  _animationFunObjects[_loc3_][_loc1_] = null;
               }
               _animationFunObjects[_loc3_] = null;
            }
            _loc3_++;
         }
         _animationFunObjects = null;
         _completeFun = null;
         _loc2_ = _completeFunParams ? _completeFunParams.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _completeFunParams[_loc3_] = null;
            _loc3_++;
         }
         _completeFunParams = null;
      }
      
      public function play() : void
      {
         carryOutAnimationFun(0);
      }
      
      public function stop() : void
      {
         TweenLite.killTweensOf(this);
      }
      
      private function carryOutAnimationFun(param1:int = 0) : void
      {
         var _loc2_:int = _animationFunOptions ? _animationFunDurations.length : 0;
         if(param1 == _loc2_ - 1)
         {
            _animationFunObjects[param1].onComplete = _completeFun;
            _animationFunObjects[param1].onCompleteParams = _completeFunParams;
         }
         else
         {
            _animationFunObjects[param1].onComplete = carryOutAnimationFun;
            _animationFunObjects[param1].onCompleteParams = [param1 + 1];
         }
         TweenLite[_animationFunOptions[param1]](this,_animationFunDurations[param1],_animationFunObjects[param1]);
      }
   }
}

