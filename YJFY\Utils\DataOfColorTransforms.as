package YJFY.Utils
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.geom.ColorTransform;
   
   public class DataOfColorTransforms
   {
      
      private var m_colorTransforms:Vector.<ColorTransform>;
      
      public function DataOfColorTransforms()
      {
         super();
         m_colorTransforms = new Vector.<ColorTransform>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_colorTransforms);
         m_colorTransforms = null;
      }
      
      public function copy(param1:DataOfColorTransforms) : void
      {
         var _loc4_:int = 0;
         var _loc3_:ColorTransform = null;
         m_colorTransforms.length = 0;
         var _loc2_:int = int(param1.m_colorTransforms.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = param1.m_colorTransforms[_loc4_];
            m_colorTransforms.push(cloneColorTransform(_loc3_));
            _loc4_++;
         }
      }
      
      public function getColorTransformNum() : int
      {
         return m_colorTransforms.length;
      }
      
      public function getColorTransformByIndex(param1:int) : ColorTransform
      {
         var _loc2_:ColorTransform = m_colorTransforms[param1];
         return cloneColorTransform(_loc2_);
      }
      
      public function initByMovieClip(param1:MovieClip) : void
      {
         var _loc4_:DisplayObject = null;
         var _loc2_:ColorTransform = null;
         var _loc5_:int = 0;
         var _loc3_:int = param1.totalFrames;
         m_colorTransforms.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            param1.gotoAndStop(_loc5_ + 1);
            _loc4_ = param1.getChildAt(0);
            _loc2_ = cloneColorTransform(_loc4_.transform.colorTransform);
            m_colorTransforms.push(_loc2_);
            _loc5_++;
         }
      }
      
      private function cloneColorTransform(param1:ColorTransform) : ColorTransform
      {
         return new ColorTransform(param1.redMultiplier,param1.greenMultiplier,param1.blueMultiplier,param1.alphaMultiplier,param1.redOffset,param1.greenOffset,param1.blueOffset,param1.alphaOffset);
      }
   }
}

