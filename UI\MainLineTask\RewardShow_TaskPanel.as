package UI.MainLineTask
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO;
   import UI.MainLineTask.TaskRewardVO.EquipmentRewardVO;
   import UI.MainLineTask.TaskRewardVO.ExperienceRewardVO;
   import UI.MainLineTask.TaskRewardVO.MoneyRewardVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteDirection;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class RewardShow_TaskPanel
   {
      
      private const m_eqC_Num:int = 4;
      
      private var m_show:MovieClip;
      
      private var m_showMc:MovieClipPlayLogicShell;
      
      private var m_moneyText:TextField;
      
      private var m_expericenceText:TextField;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_rewardScrollShow_L:ScrollSpriteLogicShell;
      
      private var m_rewardScrollShow_H:ScrollSpriteLogicShell;
      
      private var m_rewardShowLayer:Sprite;
      
      private var m_currentRewards:Vector.<Equipment>;
      
      private var m_eqCs:Vector.<Sprite>;
      
      private var m_choiceEqCSS:Vector.<MovieClipPlayLogicShell>;
      
      private var m_choiceEqCSSwitchBtnGroups:Vector.<SwitchBtnGroupLogicShell>;
      
      private var m_choiceEqAreas:Vector.<Sprite>;
      
      private var m_otherEqAreas:Vector.<Sprite>;
      
      public function RewardShow_TaskPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show.removeEventListener("clickButton",clickButton,true);
         m_show = null;
         if(m_showMc)
         {
            m_showMc.clear();
         }
         m_showMc = null;
         ClearUtil.nullArr(m_eqCs);
         m_eqCs = null;
         m_moneyText = null;
         m_expericenceText = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.nullArr(m_currentRewards);
         m_currentRewards = null;
         ClearUtil.clearObject(m_rewardScrollShow_H);
         m_rewardScrollShow_H = null;
         ClearUtil.clearObject(m_rewardScrollShow_L);
         m_rewardScrollShow_L = null;
         m_rewardShowLayer = null;
         ClearUtil.nullArr(m_currentRewards);
         m_currentRewards = null;
         ClearUtil.nullArr(m_eqCs);
         m_eqCs = null;
         ClearUtil.nullArr(m_choiceEqCSS);
         m_choiceEqCSS = null;
         ClearUtil.nullArr(m_choiceEqCSSwitchBtnGroups);
         m_choiceEqCSSwitchBtnGroups = null;
         ClearUtil.nullArr(m_choiceEqAreas);
         m_choiceEqAreas = null;
         ClearUtil.nullArr(m_otherEqAreas);
         m_otherEqAreas = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMc = new MovieClipPlayLogicShell();
         m_showMc.setShow(m_show);
         m_rewardScrollShow_L = new ScrollSpriteLogicShell();
         var _loc2_:ScrollSpriteDirection = new ScrollSpriteDirection();
         _loc2_.setDirection("L");
         m_rewardScrollShow_L.setShow(m_show,_loc2_);
         m_rewardScrollShow_L.closeMouseWheel();
         m_rewardScrollShow_L.setIsAutoHideBar(true);
         _loc2_.setDirection("H");
         m_rewardShowLayer = m_rewardScrollShow_L.getDataLayer();
         m_moneyText = m_rewardShowLayer["moneyText"];
         m_expericenceText = m_rewardShowLayer["expericenceText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_moneyText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_expericenceText);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function showReward(param1:MainLineTaskVO) : void
      {
         m_rewardScrollShow_L.reset();
         arrangeShow(param1);
      }
      
      public function get getTaskRewardBtn() : ButtonLogicShell2
      {
         return m_getRewardBtn;
      }
      
      public function get rewardScrollShow_L() : ScrollSpriteLogicShell
      {
         return m_rewardScrollShow_L;
      }
      
      public function get rewardScrollShow_H() : ScrollSpriteLogicShell
      {
         return m_rewardScrollShow_H;
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function arrangeShow(param1:MainLineTaskVO) : void
      {
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc9_:Array = null;
         var _loc10_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:* = undefined;
         ClearUtil.nullArr(m_currentRewards);
         m_currentRewards = null;
         ClearUtil.nullArr(m_eqCs);
         m_eqCs = null;
         ClearUtil.nullArr(m_choiceEqCSS);
         m_choiceEqCSS = null;
         ClearUtil.nullArr(m_choiceEqCSSwitchBtnGroups);
         m_choiceEqCSSwitchBtnGroups = null;
         ClearUtil.nullArr(m_choiceEqAreas);
         m_choiceEqAreas = null;
         ClearUtil.nullArr(m_otherEqAreas);
         m_otherEqAreas = null;
         var _loc5_:Number = 0;
         var _loc8_:Number = 80;
         _loc3_ = int(param1.taskRewardVOs.length);
         var _loc6_:* = new Vector.<EquipmentVO>();
         _loc10_ = 0;
         while(_loc10_ < _loc3_)
         {
            if(param1.taskRewardVOs[_loc10_] is EquipmentRewardVO)
            {
               _loc2_ = _loc6_.concat((param1.taskRewardVOs[_loc10_] as EquipmentRewardVO).getEquipmentVOs());
               ClearUtil.nullArr(_loc6_,false,false,false);
               _loc6_ = _loc2_;
            }
            else if(param1.taskRewardVOs[_loc10_] is ChoiceEquipmentRewardVO)
            {
               _loc9_ = arragneChoiceEquipments(param1.taskRewardVOs[_loc10_] as ChoiceEquipmentRewardVO,_loc5_,_loc8_);
               _loc5_ = Number(_loc9_[0]);
               _loc8_ = Number(_loc9_[1]);
            }
            else if(param1.taskRewardVOs[_loc10_] is MoneyRewardVO)
            {
               _loc4_ += (param1.taskRewardVOs[_loc10_] as MoneyRewardVO).getMoney();
            }
            else if(param1.taskRewardVOs[_loc10_] is ExperienceRewardVO)
            {
               _loc7_ += (param1.taskRewardVOs[_loc10_] as ExperienceRewardVO).getExperience();
            }
            _loc10_++;
         }
         m_moneyText.text = _loc4_.toString();
         m_expericenceText.text = _loc7_.toString();
         if(Boolean(_loc6_) && _loc6_.length)
         {
            _loc9_ = arrangeOtherEquipments(_loc6_,_loc5_,_loc8_);
            _loc5_ = Number(_loc9_[0]);
            _loc8_ = Number(_loc9_[1]);
         }
         m_rewardScrollShow_L.refresh();
      }
      
      private function arrangeOtherEquipments(param1:Vector.<EquipmentVO>, param2:Number, param3:Number) : Array
      {
         var _loc7_:Sprite = null;
         var _loc6_:Equipment = null;
         var _loc8_:int = 0;
         var _loc4_:Sprite = MyFunction2.returnShowByClassName("OtherEqArea") as Sprite;
         _loc4_.x = param2;
         _loc4_.y = param3;
         m_rewardShowLayer.addChild(_loc4_);
         if(m_otherEqAreas == null)
         {
            m_otherEqAreas = new Vector.<Sprite>();
         }
         m_otherEqAreas.push(_loc4_);
         var _loc5_:int = param1 ? param1.length : 0;
         _loc8_ = 0;
         while(_loc8_ < _loc5_)
         {
            _loc6_ = MyFunction2.sheatheEquipmentShell(param1[_loc8_]);
            _loc6_.addEventListener("rollOver",onOver,false,0,true);
            _loc6_.addEventListener("rollOut",onOut,false,0,true);
            _loc7_ = MyFunction2.returnShowByClassName("EqC") as Sprite;
            _loc7_.addChild(_loc6_);
            if(m_currentRewards == null)
            {
               m_currentRewards = new Vector.<Equipment>();
            }
            m_currentRewards.push(_loc6_);
            if(m_eqCs == null)
            {
               m_eqCs = new Vector.<Sprite>();
            }
            m_eqCs.push(_loc7_);
            _loc7_.x = 30 + _loc8_ % 10 * 50;
            _loc7_.y = 20 + int(_loc8_ / 10) * 50;
            _loc4_.addChild(_loc7_);
            _loc8_++;
         }
         param3 += param3 + _loc4_.height + 5;
         return [param2,param3];
      }
      
      private function arragneChoiceEquipments(param1:ChoiceEquipmentRewardVO, param2:Number, param3:Number) : Array
      {
         var _loc11_:MovieClipPlayLogicShell = null;
         var _loc7_:Equipment = null;
         var _loc12_:int = 0;
         var _loc10_:int = 0;
         var _loc5_:int = 0;
         var _loc9_:int = 0;
         var _loc8_:* = null;
         var _loc4_:Sprite = MyFunction2.returnShowByClassName("ChoiceEqArea") as Sprite;
         _loc4_.x = param2;
         _loc4_.y = param3;
         m_rewardShowLayer.addChild(_loc4_);
         if(m_choiceEqAreas == null)
         {
            m_choiceEqAreas = new Vector.<Sprite>();
         }
         m_choiceEqAreas.push(_loc4_);
         var _loc6_:SwitchBtnGroupLogicShell = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         if(m_choiceEqCSSwitchBtnGroups == null)
         {
            m_choiceEqCSSwitchBtnGroups = new Vector.<SwitchBtnGroupLogicShell>();
         }
         m_choiceEqCSSwitchBtnGroups.push(_loc6_);
         _loc6_.setExtraData(param1);
         _loc5_ = int(param1.getChoosableEquipmentVOss().length);
         _loc12_ = 0;
         while(_loc12_ < _loc5_)
         {
            _loc11_ = new MovieClipPlayLogicShell();
            _loc11_.setShow(MyFunction2.returnShowByClassName("ChoiceEqCS") as MovieClip);
            _loc4_.addChild(_loc11_.getShow());
            _loc11_.gotoAndStop(param1.getChoosableEquipmentVOss()[_loc12_].length.toString());
            if(m_choiceEqCSS == null)
            {
               m_choiceEqCSS = new Vector.<MovieClipPlayLogicShell>();
            }
            m_choiceEqCSS.push(_loc11_);
            _loc6_.addSwitchBtnShow(_loc11_.getShow()["switchBtn"],param1.getChoosableEquipmentVOss()[_loc12_]);
            if(param1.getChooseEquipmentVOs() != param1.getChoosableEquipmentVOss()[_loc12_])
            {
               _loc6_.getSwitchBtnByIndex(_loc12_).turnUnActivate();
            }
            else
            {
               _loc6_.getSwitchBtnByIndex(_loc12_).turnActivate();
            }
            _loc9_ = int(param1.getChoosableEquipmentVOss()[_loc12_].length);
            _loc10_ = 0;
            while(_loc10_ < _loc9_)
            {
               _loc7_ = MyFunction2.sheatheEquipmentShell(param1.getChoosableEquipmentVOss()[_loc12_][_loc10_]);
               _loc7_.addEventListener("rollOver",onOver,false,0,true);
               _loc7_.addEventListener("rollOut",onOut,false,0,true);
               (_loc6_.getSwitchBtnByIndex(_loc12_).getShow()["eqC_" + (_loc10_ + 1)] as Sprite).addChild(_loc7_);
               if(m_currentRewards == null)
               {
                  m_currentRewards = new Vector.<Equipment>();
               }
               m_currentRewards.push(_loc7_);
               _loc10_++;
            }
            if(_loc8_ == null)
            {
               _loc11_.getShow().x = 0;
               _loc11_.getShow().y = 0;
            }
            else if(_loc8_.getShow().x + Math.min(_loc8_.getShow().width,50) + Math.min(_loc11_.getShow().width,50) > 350)
            {
               _loc11_.getShow().x = 0;
               _loc11_.getShow().y = _loc8_.getShow().y + 80;
            }
            else
            {
               _loc11_.getShow().x = _loc8_.getShow().x + Math.min(_loc8_.getShow().width,50) + 15;
               _loc11_.getShow().y = _loc8_.getShow().y;
            }
            _loc8_ = _loc11_;
            _loc12_++;
         }
         param3 += param3 + _loc4_.height + 5;
         return [param2,param3];
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         _loc2_ = m_choiceEqCSSwitchBtnGroups ? m_choiceEqCSSwitchBtnGroups.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = m_choiceEqCSSwitchBtnGroups[_loc5_].getSwitchBtnNum();
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_choiceEqCSSwitchBtnGroups[_loc5_].getSwitchBtnByIndex(_loc4_) == param1.button)
               {
                  (m_choiceEqCSSwitchBtnGroups[_loc5_].getExtraData() as ChoiceEquipmentRewardVO).setChooseEquipmentVOs(_loc4_);
                  break;
               }
               _loc4_++;
            }
            _loc5_++;
         }
      }
   }
}

