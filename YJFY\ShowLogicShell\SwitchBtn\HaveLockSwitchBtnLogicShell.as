package YJFY.ShowLogicShell.SwitchBtn
{
   import YJFY.ShowLogicShell.ButtonEvent;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class HaveLockSwitchBtnLogicShell extends SwitchBtnLogicShell
   {
      
      private var _isLock:Boolean;
      
      public function HaveLockSwitchBtnLogicShell()
      {
         super();
      }
      
      public static function setDefaultActivateBtnFromBtns(param1:Array) : HaveLockSwitchBtnLogicShell
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         return null;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
      }
      
      override protected function onClick(param1:MouseEvent) : void
      {
         if(_isLock)
         {
            m_show.dispatchEvent(new ButtonEvent("clickLockBtn",this));
            return;
         }
         super.onClick(param1);
      }
      
      override public function turnActivate() : void
      {
         if(_isLock)
         {
            return;
         }
         super.turnActivate();
      }
      
      override protected function turnInActivate() : void
      {
         if(_isLock)
         {
            return;
         }
         super.turnInActivate();
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         if(_isActive || _isLock)
         {
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop(2);
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         if(_isActive || _isLock)
         {
            return;
         }
         try
         {
            if(MovieClip(m_show).totalFrames >= 4)
            {
               MovieClip(m_show).gotoAndStop(4);
            }
            else
            {
               MovieClip(m_show).gotoAndStop(1);
            }
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
      }
      
      public function lock() : void
      {
         _isLock = true;
         MovieClip(m_show).gotoAndStop(3);
      }
      
      public function unLock() : void
      {
         _isLock = false;
         MovieClip(m_show).gotoAndStop(2);
      }
      
      public function isLock() : Boolean
      {
         return _isLock;
      }
   }
}

