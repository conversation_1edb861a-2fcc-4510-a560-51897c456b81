package YJFY.Skill.DogSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OnlyPlayBody;
   import YJFY.World.World;
   
   public class Skill_DogSkill2 extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      public function Skill_DogSkill2()
      {
         super();
      }
      
      override public function startSkill(param1:World) : <PERSON><PERSON><PERSON>
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

