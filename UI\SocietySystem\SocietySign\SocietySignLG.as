package UI.SocietySystem.SocietySign
{
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class SocietySignLG
   {
      
      public function SocietySignLG()
      {
         super();
      }
      
      public function sign(param1:String, param2:String, param3:int, param4:int, param5:<PERSON><PERSON><PERSON>, param6:SignReturnData) : int
      {
         var _loc7_:int = new TimeUtil().getDayNumBetweenTwoTime(param1,param2);
         if(_loc7_ > 1)
         {
            param6.isOutSign = true;
            param6.outSignNum = _loc7_ - 1;
         }
         if(param5)
         {
            param3 += _loc7_;
            param6.newSignNum = Math.min(param3,param4);
            param6.isSign = true;
            return param6.newSignNum;
         }
         switch(_loc7_ + 1)
         {
            case 0:
            case 2:
               param3++;
               if(param3 > param4)
               {
                  param3 = 1;
               }
               param6.newSignNum = param3;
               param6.isSign = true;
               break;
            case 1:
               break;
            default:
               param3 = 1;
               param6.isSign = true;
         }
         param6.newSignNum = Math.min(param3,param4);
         return param6.newSignNum;
      }
   }
}

