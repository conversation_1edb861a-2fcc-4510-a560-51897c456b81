package YJFY.ToolTip
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.INextStopListener;
   
   public class PlayEndListener implements INextStopListener
   {
      
      public var playEndFun:Function;
      
      public var playEnd2Fun:Function;
      
      public function PlayEndListener()
      {
         super();
      }
      
      public function clear() : void
      {
         playEndFun = null;
         playEnd2Fun = null;
      }
      
      public function stop() : void
      {
         if(Bo<PERSON>an(playEndFun))
         {
            playEndFun();
         }
      }
      
      public function stop2(param1:AnimationShowPlayLogicShell) : void
      {
         if(<PERSON><PERSON><PERSON>(playEnd2Fun))
         {
            playEnd2Fun();
         }
      }
   }
}

