package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class DOWN_PlayerTheSocietyData extends InformationBodyDetail
   {
      
      protected var m_societyId:int;
      
      protected var m_societyNameLength:int;
      
      protected var m_societyName:String;
      
      protected var m_personalTotalConValue:int;
      
      protected var m_personalReConValue:int;
      
      protected var m_societyLevel:int;
      
      protected var m_playerNumInSociety:int;
      
      protected var m_playerMaxNumInSociety:int;
      
      protected var m_uid_leader:Number;
      
      protected var m_idx_leader:int;
      
      protected var m_nameLength_leader:int;
      
      protected var m_name_leader:String;
      
      protected var m_societyTotalConValue:int;
      
      protected var m_announcementOfSocietyLength:int;
      
      protected var m_announcementOfSociety:String;
      
      protected var m_societyRank:int;
      
      protected var m_dissolveSocietyRemainTime:int;
      
      public function DOWN_PlayerTheSocietyData()
      {
         super();
         m_informationBodyId = 3007;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc2_:UidAndIdxData = null;
         super.initFromByteArray(param1);
         m_societyId = param1.readInt();
         trace("获取到的个人帮会信息如下");
         trace("帮会id",m_societyId);
         if(m_societyId)
         {
            m_societyNameLength = param1.readInt();
            if(m_societyNameLength)
            {
               m_societyName = param1.readUTFBytes(m_societyNameLength);
            }
            m_personalTotalConValue = param1.readInt();
            m_personalReConValue = param1.readInt();
            m_societyLevel = param1.readInt();
            m_playerNumInSociety = param1.readInt();
            m_playerMaxNumInSociety = param1.readInt();
            _loc2_ = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
            m_uid_leader = _loc2_.uid;
            m_idx_leader = _loc2_.idx;
            m_nameLength_leader = param1.readInt();
            if(m_nameLength_leader)
            {
               m_name_leader = param1.readUTFBytes(m_nameLength_leader);
            }
            m_societyTotalConValue = param1.readInt();
            m_announcementOfSocietyLength = param1.readInt();
            if(m_announcementOfSocietyLength)
            {
               m_announcementOfSociety = param1.readUTFBytes(m_announcementOfSocietyLength);
            }
            m_societyRank = param1.readInt();
            m_dissolveSocietyRemainTime = param1.readInt();
            m_dissolveSocietyRemainTime *= 1000;
            trace("帮会名",m_societyName,"个人总贡献值",m_personalTotalConValue,"个人剩余贡献值",m_personalReConValue,"帮会等级",m_societyLevel,"帮会成员数量",m_playerNumInSociety,"帮会成员最大数量",m_playerMaxNumInSociety,"帮主uid",m_uid_leader,"帮主存档索引",m_idx_leader,"帮主名称",m_name_leader,"帮会总贡献值",m_societyTotalConValue,"帮会公告",m_announcementOfSociety,"帮会排名",m_societyRank,"帮会剩余时间",m_dissolveSocietyRemainTime + "秒");
         }
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getSocietyNameLength() : int
      {
         return m_societyNameLength;
      }
      
      public function getSocietyName() : String
      {
         return m_societyName;
      }
      
      public function getPersonalTotalConValue() : int
      {
         return m_personalTotalConValue;
      }
      
      public function getPersonalReConValue() : int
      {
         return m_personalReConValue;
      }
      
      public function getSocietyLevel() : int
      {
         return m_societyLevel;
      }
      
      public function getPlayerNumInSociety() : int
      {
         return m_playerNumInSociety;
      }
      
      public function getPlayerMaxNumINSociety() : int
      {
         return m_playerMaxNumInSociety;
      }
      
      public function getUid_leader() : Number
      {
         return m_uid_leader;
      }
      
      public function getIdx_leader() : int
      {
         return m_idx_leader;
      }
      
      public function getNameLength_leader() : int
      {
         return m_nameLength_leader;
      }
      
      public function getName_leader() : String
      {
         return m_name_leader;
      }
      
      public function getSocietyTotalConValue() : int
      {
         return m_societyTotalConValue;
      }
      
      public function getAnnouncementOfSocietyLength() : int
      {
         return m_announcementOfSocietyLength;
      }
      
      public function getAnnouncementOfSociety() : String
      {
         return m_announcementOfSociety;
      }
      
      public function getSocietyRank() : int
      {
         return m_societyRank;
      }
      
      public function getDissolveSocietyRemainTime() : int
      {
         return m_dissolveSocietyRemainTime;
      }
   }
}

