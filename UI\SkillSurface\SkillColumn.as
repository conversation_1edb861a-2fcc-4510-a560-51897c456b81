package UI.SkillSurface
{
   import UI.Animation.AnimationObject;
   import UI.Animation.M2B;
   import UI.Button.UpgradeBtn;
   import UI.Effects.Effects_UpgradeSkill_Cell;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.SkillCells.UnusedSkillCell;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class SkillColumn extends MySprite
   {
      
      public const TIME_STRING:String = "持续时间";
      
      public const SECOND:String = "秒。";
      
      public const ADD_ATTACK:String = "%攻击力";
      
      public const XIAO_HAO:String = "消耗";
      
      public const MAGIC_STR:String = "点魔法值";
      
      public const ORIG_FORMAT_BIGIN:String = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' color=\'#efb00a\' size=\'12\'>";
      
      public const ORIG_FORMAT_END:String = "</font>";
      
      public const SMALL_FORMAT_BIGIN:String = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' color=\'#ff0000\' size=\'10\'>";
      
      public const SMALL_FORMAT_END:String = "</font>";
      
      public var oneDiamond:Diamond;
      
      public var twoDiamond:Diamond;
      
      public var threeDiamond:Diamond;
      
      public var skillNameText:TextField;
      
      public var coolDownText:TextField;
      
      public var hurtText:TextField;
      
      public var descriptionAndTimeText:TextField;
      
      public var priceText:TextField;
      
      public var upgradeBtn:UpgradeBtn;
      
      public var border:Sprite;
      
      public var skillCell:UnusedSkillCell;
      
      private const NoActiveHtmlTextFormat1:String = "<font  face=\'FZKaTong-M19S\' size=\'22\' color=\'#ffffff\'>";
      
      private const NoActiveHtmlTextFormat2:String = "</font>";
      
      private const EFFECTS_CELL_X:Number = -5;
      
      private const EFFECTS_CELL_Y:Number = -6;
      
      private const EFFECTS_CELL_REPEATCOUNT:int = 1;
      
      private const EFFECTS_CELL_DELAY:Number = 30;
      
      private var _myFont:FangZhengKaTongJianTi;
      
      private var _player:Player;
      
      private var _index:int;
      
      public function SkillColumn(param1:SkillVO, param2:Player, param3:int)
      {
         super();
         _myFont = new FangZhengKaTongJianTi();
         skillNameText.selectable = false;
         skillNameText.mouseEnabled = false;
         coolDownText.selectable = false;
         coolDownText.mouseEnabled = false;
         coolDownText.embedFonts = true;
         hurtText.selectable = false;
         hurtText.mouseEnabled = false;
         hurtText.embedFonts = true;
         descriptionAndTimeText.selectable = false;
         descriptionAndTimeText.mouseEnabled = false;
         descriptionAndTimeText.embedFonts = true;
         priceText.selectable = false;
         priceText.mouseEnabled = false;
         priceText.embedFonts = true;
         _index = param3;
         priceText.defaultTextFormat = new TextFormat(_myFont.fontName,20,16776960);
         coolDownText.embedFonts = true;
         hurtText.embedFonts = true;
         descriptionAndTimeText.embedFonts = true;
         skillCell = new UnusedSkillCell();
         skillCell.x = 35;
         skillCell.y = 36;
         addChild(skillCell);
         priceText.embedFonts = true;
         skillCell.addSkillVO(param1);
         _player = param2;
         initColumn(param2);
         addEventListener("addedToStage",addToStage,false,0,true);
         setBtnActive(param2);
      }
      
      public function initColumn(param1:Player) : void
      {
         border.visible = false;
         setDiamond(param1);
         setNameText(param1);
         setNumText(param1);
         setDescriptionAndTime(param1);
         setPriceText(param1);
      }
      
      public function upgrade() : void
      {
         var _loc5_:SkillVO = skillCell.child;
         var _loc3_:int = MyFunction.getInstance().calculateNextLevelID(int(_loc5_.id));
         var _loc4_:SkillVO = XMLSingle.getSkill(_loc3_,XMLSingle.getInstance().skillXML);
         var _loc2_:int = int(_player.playerVO.skillVOs.indexOf(_loc5_));
         if(_loc2_ == -1)
         {
            throw new Error();
         }
         _player.playerVO.skillVOs[_loc2_] = _loc4_;
         skillCell.addSkillVO(_loc4_);
         var _loc1_:Effects_UpgradeSkill_Cell = new Effects_UpgradeSkill_Cell();
         var _loc6_:AnimationObject = new AnimationObject();
         _loc6_.x = -5;
         _loc6_.y = -6;
         addChild(_loc6_);
         _loc6_.imgList = M2B.transformM2B(_loc1_,true);
         _loc6_.repeatCount = 1;
         _loc6_.delay = 30;
         _loc6_.play();
         dispatchEvent(new UIPassiveEvent("refreshAtt",0x40 | 1 | 0x0200));
         initColumn(_player);
         setBtnActive(_player);
      }
      
      public function getUpgradePrice() : int
      {
         return skillCell.child.upgradePrice;
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(skillCell)
         {
            skillCell.removeSkillVO();
            skillCell.clear();
         }
         skillCell = null;
         if(oneDiamond)
         {
            oneDiamond.clear();
         }
         oneDiamond = null;
         if(twoDiamond)
         {
            twoDiamond.clear();
         }
         twoDiamond = null;
         if(threeDiamond)
         {
            threeDiamond.clear();
         }
         threeDiamond = null;
         skillNameText = null;
         coolDownText = null;
         hurtText = null;
         descriptionAndTimeText = null;
         priceText = null;
         if(upgradeBtn)
         {
            upgradeBtn.clear();
         }
         upgradeBtn = null;
         border = null;
         _myFont = null;
         _player = null;
      }
      
      protected function setDiamond(param1:Player) : void
      {
         if(param1.playerVO.playerType == "SunWuKong")
         {
            oneDiamond.setState(0);
            twoDiamond.setState(0);
            threeDiamond.setState(0);
         }
         else if(param1.playerVO.playerType == "BaiLongMa")
         {
            oneDiamond.setState(1);
            twoDiamond.setState(1);
            threeDiamond.setState(1);
         }
         else if(param1.playerVO.playerType == "ErLangShen")
         {
            oneDiamond.setState(2);
            twoDiamond.setState(2);
            threeDiamond.setState(2);
         }
         else if(param1.playerVO.playerType == "ChangE")
         {
            oneDiamond.setState(3);
            twoDiamond.setState(3);
            threeDiamond.setState(3);
         }
         else if(param1.playerVO.playerType == "Fox")
         {
            oneDiamond.setState(4);
            twoDiamond.setState(4);
            threeDiamond.setState(4);
         }
         else if(param1.playerVO.playerType == "TieShan")
         {
            oneDiamond.setState(5);
            twoDiamond.setState(5);
            threeDiamond.setState(5);
         }
         else if(param1.playerVO.playerType == "Houyi")
         {
            oneDiamond.setState(6);
            twoDiamond.setState(6);
            threeDiamond.setState(6);
         }
         else if(param1.playerVO.playerType == "ZiXia")
         {
            oneDiamond.setState(7);
            twoDiamond.setState(7);
            threeDiamond.setState(7);
         }
         switch(skillCell.child.level)
         {
            case 0:
               oneDiamond.gotoOneFrame();
               twoDiamond.gotoOneFrame();
               threeDiamond.gotoOneFrame();
               break;
            case 1:
               oneDiamond.gotoTwoFrame();
               twoDiamond.gotoOneFrame();
               threeDiamond.gotoOneFrame();
               break;
            case 2:
               oneDiamond.gotoTwoFrame();
               twoDiamond.gotoTwoFrame();
               threeDiamond.gotoOneFrame();
               break;
            case 3:
               oneDiamond.gotoTwoFrame();
               twoDiamond.gotoTwoFrame();
               threeDiamond.gotoTwoFrame();
               break;
            default:
               trace("技能等级不存在");
         }
      }
      
      protected function setNameText(param1:Player) : void
      {
         var _loc7_:String = null;
         var _loc6_:TextFormat = null;
         var _loc5_:TextFormat = null;
         var _loc2_:TextFormat = null;
         var _loc3_:TextFormat = null;
         var _loc4_:String = skillCell.child.name;
         if(skillCell.child.className != "RushSkill" && skillCell.child.className != "Dragon_HeiLongBo" && skillCell.child.className != "ErLangShen_XiaoTianXi" && skillCell.child.className != "ChangE_ChongFengPao" && skillCell.child.className != "LingHu_LiuHuaZhan" && skillCell.child.className != "TieShan_FengHuo" && skillCell.child.className != "HeiLongBo_Houyi" && skillCell.child.className != "ZiXia_ZiQingJianQi")
         {
            _loc7_ = returnKey(_player,_index);
         }
         else
         {
            switch(param1.playerVO.playerType)
            {
               case "SunWuKong":
                  _loc7_ = "前前" + returnKey(_player,_index);
                  break;
               case "BaiLongMa":
                  _loc7_ = "长按" + returnKey(_player,_index);
                  break;
               case "ErLangShen":
                  _loc7_ = "前前" + returnKey(_player,_index);
                  break;
               case "ChangE":
                  _loc7_ = "前前" + returnKey(_player,_index);
                  break;
               case "Fox":
                  _loc7_ = "前前" + returnKey(_player,_index);
                  break;
               case "TieShan":
                  _loc7_ = "前前" + returnKey(_player,_index);
                  break;
               case "Houyi":
                  _loc7_ = "长按" + returnKey(_player,_index);
                  break;
               case "ZiXia":
                  _loc7_ = "长按" + returnKey(_player,_index);
                  break;
               default:
                  throw new Error("人物类型不存在！");
            }
         }
         skillNameText.text = _loc4_ + "\n" + _loc7_;
         if(param1.playerVO.level < skillCell.child.initLevel)
         {
            _loc6_ = new TextFormat(null,18,16777215);
            skillNameText.setTextFormat(_loc6_,0,_loc4_.length);
            _loc5_ = new TextFormat(null,12,16711680);
            skillNameText.setTextFormat(_loc5_,_loc4_.length,_loc4_.length + _loc7_.length + 1);
         }
         else
         {
            _loc2_ = new TextFormat(null,18,15708170);
            skillNameText.setTextFormat(_loc2_,0,_loc4_.length);
            _loc3_ = new TextFormat(null,12,16711680);
            skillNameText.setTextFormat(_loc3_,_loc4_.length,_loc4_.length + _loc7_.length + 1);
         }
      }
      
      protected function setNumText(param1:Player) : void
      {
         var _loc8_:String = null;
         var _loc7_:String = null;
         var _loc4_:String = null;
         var _loc5_:String = null;
         var _loc6_:String = null;
         var _loc3_:TextFormat = new TextFormat(_myFont.fontName,15,15708170);
         var _loc2_:TextFormat = new TextFormat(_myFont.fontName,12,16711680);
         var _loc9_:SkillVO = skillCell.child;
         if(_loc9_.type == "playerActive")
         {
            _loc8_ = (_loc9_ as PlayerActiveSkillVO).coolDown.toString();
            _loc7_ = (_loc9_ as PlayerActiveSkillVO).nextCoolDown.toString();
            _loc4_ = (_loc9_ as PlayerActiveSkillVO).nextHurt.toString();
            if(param1.playerVO.level < _loc9_.initLevel)
            {
               coolDownText.htmlText = "";
            }
            else if((_loc9_ as PlayerActiveSkillVO).nextCoolDown == 0)
            {
               if((_loc9_ as PlayerActiveSkillVO).coolDown == 0)
               {
                  coolDownText.text = "";
               }
               else
               {
                  coolDownText.text = _loc8_;
                  coolDownText.setTextFormat(_loc3_,0,_loc8_.length);
               }
            }
            else if((_loc9_ as PlayerActiveSkillVO).coolDown == 0)
            {
               coolDownText.text = "\n(" + _loc7_ + ")";
               coolDownText.setTextFormat(_loc2_,0,_loc7_.length + 3);
            }
            else
            {
               coolDownText.text = _loc8_ + "\n(" + _loc7_ + ")";
               coolDownText.setTextFormat(_loc3_,0,_loc8_.length);
               coolDownText.setTextFormat(_loc2_,_loc8_.length,_loc8_.length + _loc7_.length + 3);
            }
            if(param1.playerVO.level < _loc9_.initLevel)
            {
               hurtText.htmlText = "";
            }
            else
            {
               if((_loc9_ as PlayerActiveSkillVO).additionalAttack == 0)
               {
                  if((_loc9_ as PlayerActiveSkillVO).hurt == 0)
                  {
                     _loc5_ = "";
                  }
                  else
                  {
                     _loc5_ = "" + (_loc9_ as PlayerActiveSkillVO).hurt;
                  }
               }
               else if((_loc9_ as PlayerActiveSkillVO).hurt == 0)
               {
                  _loc5_ = "" + (_loc9_ as PlayerActiveSkillVO).additionalAttack + "%攻击力";
               }
               else
               {
                  _loc5_ = "" + (_loc9_ as PlayerActiveSkillVO).additionalAttack + "%攻击力" + "+" + (_loc9_ as PlayerActiveSkillVO).hurt;
               }
               if((_loc9_ as PlayerActiveSkillVO).nextAdditionalAttack == 0)
               {
                  if((_loc9_ as PlayerActiveSkillVO).nextHurt == 0)
                  {
                     _loc6_ = "";
                  }
                  else
                  {
                     _loc6_ = "" + (_loc9_ as PlayerActiveSkillVO).hurt;
                  }
               }
               else if((_loc9_ as PlayerActiveSkillVO).nextHurt == 0)
               {
                  _loc6_ = "" + (_loc9_ as PlayerActiveSkillVO).nextAdditionalAttack + "%攻击力";
               }
               else
               {
                  _loc6_ = "" + (_loc9_ as PlayerActiveSkillVO).nextAdditionalAttack + "%攻击力" + "+" + (_loc9_ as PlayerActiveSkillVO).nextHurt;
               }
               hurtText.text = _loc5_ + "\n" + _loc6_;
               if(_loc5_.length == 0)
               {
                  if(_loc6_.length != 0)
                  {
                     hurtText.setTextFormat(_loc2_,_loc5_.length,_loc5_.length + _loc6_.length + 1);
                  }
               }
               else if(_loc6_.length == 0)
               {
                  hurtText.setTextFormat(_loc3_,0,_loc5_.length);
               }
               else
               {
                  hurtText.setTextFormat(_loc3_,0,_loc5_.length);
                  hurtText.setTextFormat(_loc2_,_loc5_.length,_loc5_.length + _loc6_.length + 1);
               }
            }
         }
      }
      
      protected function setDescriptionAndTime(param1:Player) : void
      {
         var _loc3_:String = null;
         var _loc7_:String = null;
         var _loc8_:String = null;
         var _loc5_:String = null;
         var _loc2_:String = null;
         var _loc6_:String = null;
         var _loc4_:String = null;
         if(skillCell.child.type == "playerActive")
         {
            _loc3_ = ORIG_FORMAT_BIGIN + skillCell.child.description + "</font>";
            _loc7_ = (skillCell.child as PlayerActiveSkillVO).lengthOfTime.toString();
            _loc8_ = (skillCell.child as PlayerActiveSkillVO).nextLengthOfTime.toString();
            _loc5_ = (skillCell.child as PlayerActiveSkillVO).manaCost.toString();
            _loc2_ = (skillCell.child as PlayerActiveSkillVO).nextManaCost.toString();
            if(param1.playerVO.level < skillCell.child.initLevel)
            {
               descriptionAndTimeText.multiline = true;
               descriptionAndTimeText.htmlText = "<p align=\'center\'><font  face=\'FZKaTong-M19S\' size=\'22\' color=\'#ffffff\'>" + skillCell.child.initLevel + "级开通！<br>值得期待！" + "</font>" + "</p>";
            }
            else
            {
               if((skillCell.child as PlayerActiveSkillVO).lengthOfTime == 0)
               {
                  if((skillCell.child as PlayerActiveSkillVO).nextLengthOfTime == 0)
                  {
                     _loc6_ = "";
                  }
                  else
                  {
                     _loc6_ = ORIG_FORMAT_BIGIN + "持续时间" + "</font>" + SMALL_FORMAT_BIGIN + "(" + _loc8_ + ")" + "</font>" + ORIG_FORMAT_BIGIN + "秒。" + "</font>";
                  }
               }
               else if((skillCell.child as PlayerActiveSkillVO).nextLengthOfTime == 0)
               {
                  _loc6_ = ORIG_FORMAT_BIGIN + "持续时间" + _loc7_ + "秒。" + "</font>";
               }
               else
               {
                  _loc6_ = ORIG_FORMAT_BIGIN + "持续时间" + _loc7_ + "</font>" + SMALL_FORMAT_BIGIN + "(" + _loc8_ + ")" + "</font>" + ORIG_FORMAT_BIGIN + "秒。" + "</font>";
               }
               if((skillCell.child as PlayerActiveSkillVO).manaCost == 0)
               {
                  if((skillCell.child as PlayerActiveSkillVO).nextManaCost == 0)
                  {
                     _loc4_ = "";
                  }
                  else
                  {
                     _loc4_ = ORIG_FORMAT_BIGIN + "消耗" + "</font>" + SMALL_FORMAT_BIGIN + "(" + _loc2_ + ")" + "</font>" + ORIG_FORMAT_BIGIN + "点魔法值" + "</font>";
                  }
               }
               else if((skillCell.child as PlayerActiveSkillVO).nextManaCost == 0)
               {
                  _loc4_ = ORIG_FORMAT_BIGIN + "消耗" + _loc5_ + "点魔法值" + "</font>";
               }
               else
               {
                  _loc4_ = ORIG_FORMAT_BIGIN + "消耗" + _loc5_ + "</font>" + SMALL_FORMAT_BIGIN + "(" + _loc2_ + ")" + "</font>" + ORIG_FORMAT_BIGIN + "点魔法值" + "</font>";
               }
               descriptionAndTimeText.htmlText = "" + _loc3_ + _loc6_ + " " + _loc4_ + ORIG_FORMAT_BIGIN + "。" + "</font>";
            }
         }
      }
      
      protected function setPriceText(param1:Player) : void
      {
         if(param1.playerVO.level < skillCell.child.initLevel)
         {
            priceText.htmlText = "";
         }
         else if(skillCell.child.upgradePrice == 0)
         {
            priceText.text = "";
         }
         else
         {
            priceText.text = skillCell.child.upgradePrice.toString();
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",mouseOverSlot,false,0,true);
         addEventListener("rollOut",mouseOutSlot,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("addedToStage",addToStage,false);
         removeEventListener("rollOver",mouseOverSlot);
         removeEventListener("rollOut",mouseOutSlot);
      }
      
      protected function setBtnActive(param1:Player) : void
      {
         if(param1.playerVO.level < skillCell.child.initLevel || skillCell.child.level == skillCell.child.maxLevel)
         {
            upgradeBtn.setBtnActive(0);
            upgradeBtn.mouseChildren = false;
            upgradeBtn.mouseEnabled = false;
         }
         else
         {
            upgradeBtn.setBtnActive(1);
            upgradeBtn.mouseChildren = true;
            upgradeBtn.mouseEnabled = true;
         }
      }
      
      protected function mouseOverSlot(param1:MouseEvent) : void
      {
         border.visible = true;
      }
      
      protected function mouseOutSlot(param1:MouseEvent) : void
      {
         border.visible = false;
      }
      
      private function returnKey(param1:Player, param2:int) : String
      {
         switch(param1.playerVO.playerID)
         {
            case "playerOne":
               switch(param2)
               {
                  case 0:
                     return " J";
                  case 1:
                     return "K";
                  case 2:
                     return "L";
                  case 3:
                     return "U";
                  case 4:
                     return "I";
               }
               break;
            case "playerTwo":
               switch(param2)
               {
                  case 0:
                     return " 1";
                  case 1:
                     return "2";
                  case 2:
                     return "3";
                  case 3:
                     return "4";
                  case 4:
                     return "5";
               }
         }
         return "";
      }
   }
}

