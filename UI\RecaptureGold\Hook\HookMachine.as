package UI.RecaptureGold.Hook
{
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.CatchItems.CatchItemForOnlyGetGold;
   import UI.RecaptureGold.CatchItems.CatchItemForUnknownItem;
   import UI.RecaptureGold.HitPoint;
   import UI.RecaptureGold.Parent.ICatchTarget;
   import UI.RecaptureGold.Parent.IProp;
   import UI.RecaptureGold.Parent.IPutItem;
   import UI.RecaptureGold.Props.Prop_Bomb;
   import UI.RecaptureGold.Props.Prop_DiamondToGoldBook;
   import UI.RecaptureGold.Props.Prop_LuckGrass;
   import UI.RecaptureGold.PutItems.PutItemForBomb;
   import UI.RecaptureGold.PutItems.PutItemForEnbleCatch;
   import UI.RecaptureGold.RGPlayer;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import UI.RecaptureGold.RecaptureGoldGame;
   import UI.SoundManager.SoundManager;
   import flash.display.DisplayObject;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.utils.clearInterval;
   import flash.utils.getQualifiedSuperclassName;
   import flash.utils.setInterval;
   
   public class HookMachine extends MySprite
   {
      
      public static var MAX_ROPE_LENGTH:Number = 650;
      
      private const _hookX:Number = 20;
      
      private const _hookY:Number = 0;
      
      private var _hook:Hook;
      
      private var _hookHitPoints:Vector.<HitPoint>;
      
      private var _isSwing:Boolean;
      
      private var _isElongate:Boolean;
      
      private var _isContraction:Boolean;
      
      private var _isStop:Boolean;
      
      private var _isUseBomb:Boolean;
      
      private var _centerX:Number = 0;
      
      private var _centerY:Number = 0;
      
      private var _angle:Number;
      
      private var _swingPeriod:Number = 5;
      
      private var _direction:Number = 1;
      
      private var _wanCatchTargets:Vector.<ICatchTarget>;
      
      private var _currentRopeLong:Number;
      
      private var _elongateRate:Number;
      
      private var _contractRate:Number;
      
      private var _intervalForSwing:uint;
      
      private var _intervalForElongate:uint;
      
      private var _intervalForContraction:uint;
      
      private var _recaptureGoldGameXML:XML;
      
      private var _rgPlayer:RGPlayer;
      
      private var _recaptureGoldGame:RecaptureGoldGame;
      
      private var _soundManager:SoundManager;
      
      public function HookMachine(param1:XML, param2:RecaptureGoldGame, param3:SoundManager)
      {
         super();
         _recaptureGoldGame = param2;
         _recaptureGoldGameXML = param1;
         _soundManager = param3;
         init();
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(_hook)
         {
            _hook.clear();
         }
         _hook = null;
         _loc1_ = _hookHitPoints ? _hookHitPoints.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_hookHitPoints[_loc3_])
            {
               _hookHitPoints[_loc3_].clear();
            }
            _hookHitPoints[_loc3_] = null;
            _loc3_++;
         }
         _hookHitPoints = null;
         _wanCatchTargets = null;
         clearInterval(_intervalForSwing);
         clearInterval(_intervalForElongate);
         clearInterval(_intervalForContraction);
         _recaptureGoldGameXML = null;
         _rgPlayer = null;
         _recaptureGoldGame = null;
         _soundManager = null;
      }
      
      public function set wanCatchTargets(param1:Vector.<ICatchTarget>) : void
      {
         _wanCatchTargets = param1;
      }
      
      public function set rgPlayer(param1:RGPlayer) : void
      {
         _rgPlayer = param1;
         _elongateRate = _rgPlayer.getSpeed(_recaptureGoldGameXML);
      }
      
      public function get swingPeriod() : Number
      {
         return _swingPeriod;
      }
      
      public function set swingPeriod(param1:Number) : void
      {
         _swingPeriod = param1;
      }
      
      public function get angle() : Number
      {
         return _angle;
      }
      
      public function get isSwing() : Boolean
      {
         return _isSwing;
      }
      
      public function get isElongate() : Boolean
      {
         return _isElongate;
      }
      
      public function get isContraction() : Boolean
      {
         return _isContraction;
      }
      
      public function reStart() : void
      {
         _isSwing = false;
         _isElongate = false;
         _isContraction = false;
         _isStop = false;
         _angle = 0;
         _direction = 1;
         _currentRopeLong = 20;
         clearInterval(_intervalForSwing);
         clearInterval(_intervalForElongate);
         clearInterval(_intervalForContraction);
         _hook.x = Math.cos(_angle) * _currentRopeLong;
         _hook.y = Math.sin(_angle) * _currentRopeLong;
         _hook.hookItem = null;
         startSwing();
      }
      
      public function stop() : void
      {
         _isStop = true;
         clearInterval(_intervalForContraction);
         clearInterval(_intervalForElongate);
         clearInterval(_intervalForSwing);
      }
      
      public function resume() : void
      {
         _isStop = false;
         if(_isSwing)
         {
            startSwing();
         }
         else if(_isElongate)
         {
            startCatch();
         }
         else if(_isContraction)
         {
            startContract();
         }
      }
      
      public function startSwing() : void
      {
         if(!_hook)
         {
            _hook = new Hook();
            addChild(_hook);
            _hook.x = 20;
            _hook.y = 0;
            _hookHitPoints = _hook.getAllHitPoint();
         }
         _rgPlayer.playAnimation(_recaptureGoldGameXML);
         _angle = Math.random() * 3.141592653589793;
         _direction = MyFunction2.randomOneNumFromNums(1,-1);
         _intervalForSwing = setInterval(swingHook,50);
         _isSwing = true;
      }
      
      public function useBomb() : void
      {
         var hookMachine:HookMachine;
         if(_isStop)
         {
            trace("游戏已暂停!");
            return;
         }
         if(!_hook.hookItem)
         {
            trace("钩子上没有物品！不能使用炸弹！");
            return;
         }
         if(!_rgPlayer.playerPropVO.judeIsHaveBomb())
         {
            trace("没有炸弹");
            return;
         }
         if(_isUseBomb)
         {
            trace("正在使用炸弹！");
            return;
         }
         hookMachine = this;
         _rgPlayer.playAnimation(_recaptureGoldGameXML,"useBomb",[{
            "eventType":"completePressBtn",
            "fun":function():void
            {
               var prop:IProp;
               var shakeFun:Function;
               if(_isStop)
               {
                  trace("游戏已暂停!");
                  return;
               }
               if(!_hook.hookItem)
               {
                  trace("钩子上没有物品！不能使用炸弹！");
                  return;
               }
               if(_isUseBomb)
               {
                  trace("正在使用炸弹！");
                  return;
               }
               if(_rgPlayer.playerPropVO.judeIsHaveBomb())
               {
                  _isUseBomb = true;
                  prop = _rgPlayer.playerPropVO.usePropBomb();
                  _recaptureGoldGame.refreshProps(_recaptureGoldGameXML);
                  shakeFun = _recaptureGoldGame ? _recaptureGoldGame.shakeShow : null;
                  (prop as Prop_Bomb).bombHook(_recaptureGoldGameXML,hookMachine,_hook,_soundManager,function():void
                  {
                     _contractRate = _rgPlayer.getSpeed(_recaptureGoldGameXML);
                     _isUseBomb = false;
                  },null,shakeFun);
               }
            }
         }]);
      }
      
      public function startCatch() : void
      {
         if(!_isSwing || _isStop)
         {
            return;
         }
         _rgPlayer.playAnimation(_recaptureGoldGameXML,"throwRope",[{
            "eventType":"completePressBtn",
            "fun":function():void
            {
               var _loc1_:String = null;
               if(_isSwing && !_isStop)
               {
                  _loc1_ = String(_recaptureGoldGameXML.SoundData[0].@soundForThrowRope);
                  _soundManager.play(_loc1_,0,0);
                  stopSwing();
                  _isElongate = true;
                  _contractRate = _elongateRate;
                  _intervalForElongate = setInterval(elongateRope,20);
               }
            }
         }]);
      }
      
      public function startContract() : void
      {
         if(_isContraction || _isSwing || _isStop)
         {
            return;
         }
         _rgPlayer.playAnimation(_recaptureGoldGameXML,_contractRate < _elongateRate / 3 ? "dragRope2" : "dragRope1",[{
            "eventType":"completePressBtn",
            "fun":function():void
            {
               var _loc1_:String = null;
               if(!isContraction && !_isSwing && !_isStop)
               {
                  _loc1_ = String(_recaptureGoldGameXML.SoundData[0].@soundForDragRope);
                  _soundManager.play(_loc1_,0,0);
                  _isContraction = true;
                  stopElongateRope();
                  _intervalForContraction = setInterval(contractRope,20);
               }
            }
         }]);
      }
      
      private function judeHarvest() : void
      {
         var _loc4_:int = 0;
         var _loc9_:int = 0;
         var _loc5_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc1_:Prop_LuckGrass = null;
         var _loc10_:Prop_DiamondToGoldBook = null;
         var _loc13_:* = undefined;
         var _loc11_:String = null;
         var _loc8_:String = null;
         var _loc12_:Number = NaN;
         var _loc7_:Object = null;
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         if(_hook.hookItem)
         {
            _loc5_ = 1;
            _loc2_ = 1;
            _loc1_ = _rgPlayer.playerPropVO.judeIsHaveLuckGrass();
            _loc10_ = _rgPlayer.playerPropVO.judeIsHaveDiamondToBook();
            if(_loc1_)
            {
               _loc5_ = _loc1_.getPromoteValueFromHook(_recaptureGoldGameXML,_hook);
               trace("提升幸运草的幸运值：",_loc5_);
            }
            if(_loc10_)
            {
               _loc2_ = _loc10_.dealWithRockFromHook(_recaptureGoldGameXML,_hook);
               trace("提升获取金子的价值：",_loc2_);
            }
            _loc11_ = "UI.RecaptureGold.CatchItems::";
            _loc8_ = getQualifiedSuperclassName(_hook.hookItem);
            _loc12_ = 0;
            switch(_loc8_)
            {
               case _loc11_ + "CatchItemForOnlyGetGold":
                  _loc12_ = (_hook.hookItem as CatchItemForOnlyGetGold).getFunForEnbleGetGoldItem(_recaptureGoldGameXML,_recaptureGoldGame,_soundManager,_loc2_);
                  break;
               case _loc11_ + "CatchItemForBomb":
                  break;
               case _loc11_ + "CatchItemForUnknownItem":
                  switch((_loc7_ = (_hook.hookItem as CatchItemForUnknownItem).getUnknownItem(_recaptureGoldGameXML,_recaptureGoldGame,_soundManager,_loc5_)).content)
                  {
                     case "goldNum":
                        _loc12_ = Number(_loc7_.getGoldNum);
                        break;
                     case "addStrength":
                     case "subStrength":
                        _rgPlayer.rgPlayerVO.physicalStrength += _loc7_.value;
                        _elongateRate = Math.max(1,_rgPlayer.getSpeed(_recaptureGoldGameXML));
                        break;
                     case "getProps":
                        _loc13_ = _loc7_.props;
                        _loc6_ = int(_loc13_.length);
                        _loc3_ = 0;
                        while(_loc3_ < _loc6_)
                        {
                           _rgPlayer.playerPropVO.addProp(_loc13_[_loc3_],_recaptureGoldGameXML);
                           _loc13_[_loc3_] = null;
                           _loc3_++;
                        }
                        _recaptureGoldGame.refreshProps(_recaptureGoldGameXML);
                        break;
                     default:
                        throw new Error();
                  }
            }
            _hook.hookItem = null;
            RecaptureGoldData.getInstance().allGoldNum = RecaptureGoldData.getInstance().allGoldNum + _loc12_;
            dispatchEvent(new RecaptureGoldEvent("refreshUI",{"allGoldNum":RecaptureGoldData.getInstance().allGoldNum}));
         }
      }
      
      private function judeIsCatchItem() : void
      {
         var _loc4_:int = 0;
         var _loc10_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc13_:* = null;
         var _loc6_:HitPoint = null;
         var _loc12_:Point = null;
         var _loc9_:IPutItem = null;
         var _loc7_:Boolean = false;
         var _loc11_:String = null;
         var _loc8_:String = null;
         var _loc1_:Number = NaN;
         var _loc5_:Function = null;
         if(!Boolean(_wanCatchTargets))
         {
            throw new Error("要抓的物品为空");
         }
         _loc4_ = 0;
         while(_loc4_ < _wanCatchTargets.length)
         {
            if(_loc7_)
            {
               break;
            }
            _loc9_ = _wanCatchTargets[_loc4_].getItem();
            if(_loc9_)
            {
               _loc3_ = int(_hookHitPoints.length);
               _loc2_ = 0;
               while(_loc2_ < _loc3_)
               {
                  _loc6_ = _hookHitPoints[_loc2_];
                  _loc12_ = _hook.localToGlobal(new Point(_loc6_.x,_loc6_.y));
                  if(_loc9_.hitTestPoint(_loc12_.x,_loc12_.y,true))
                  {
                     RecaptureGoldData.getInstance().catchItemNum++;
                     _loc11_ = "UI.RecaptureGold.PutItems::";
                     _loc8_ = getQualifiedSuperclassName(_loc9_);
                     _loc1_ = 0;
                     switch(_loc8_)
                     {
                        case _loc11_ + "PutItemForEnbleCatch":
                           _loc1_ = (_loc9_ as PutItemForEnbleCatch).catchFunForEnbleCatchItem(_recaptureGoldGameXML,_hook,_wanCatchTargets[_loc4_],_soundManager);
                           break;
                        case _loc11_ + "PutItemForBomb":
                           _loc5_ = _recaptureGoldGame ? _recaptureGoldGame.shakeShow : null;
                           (_loc9_ as PutItemForBomb).catchFunForBombItem(_recaptureGoldGameXML,(_wanCatchTargets[_loc4_] as DisplayObject).parent,_recaptureGoldGame,_wanCatchTargets[_loc4_],_wanCatchTargets,_soundManager,_loc5_);
                     }
                     _wanCatchTargets.splice(_loc4_,1);
                     _loc4_--;
                     _contractRate = Math.max(1,_elongateRate - _loc1_ / Number(_recaptureGoldGameXML.OtherData[0].PowerToSpeed[0].@value));
                     stopElongateRope();
                     startContract();
                     _loc7_ = true;
                  }
                  _loc2_++;
               }
            }
            _loc4_++;
         }
      }
      
      private function stopContractRope() : void
      {
         _isContraction = false;
         clearInterval(_intervalForContraction);
      }
      
      private function contractRope() : void
      {
         _currentRopeLong = Math.min(MAX_ROPE_LENGTH,Math.max(20,_currentRopeLong - _contractRate));
         _hook.x = Math.cos(_angle) * _currentRopeLong;
         _hook.y = Math.sin(_angle) * _currentRopeLong;
         graphics.clear();
         graphics.moveTo(0,0);
         graphics.lineStyle(2,16777215,1);
         graphics.lineTo(_hook.x,_hook.y);
         if(_currentRopeLong == 20)
         {
            stopContractRope();
            judeHarvest();
            startSwing();
         }
      }
      
      private function stopElongateRope() : void
      {
         _isElongate = false;
         clearInterval(_intervalForElongate);
      }
      
      private function elongateRope() : void
      {
         _currentRopeLong = Math.min(MAX_ROPE_LENGTH,Math.max(20,_currentRopeLong + _elongateRate));
         if(_currentRopeLong == MAX_ROPE_LENGTH)
         {
            stopElongateRope();
            startContract();
            return;
         }
         _hook.x = Math.cos(_angle) * _currentRopeLong;
         _hook.y = Math.sin(_angle) * _currentRopeLong;
         graphics.clear();
         graphics.lineStyle(2,16777215,1);
         graphics.moveTo(0,0);
         graphics.lineTo(_hook.x,_hook.y);
         judeIsCatchItem();
      }
      
      private function stopSwing() : void
      {
         clearInterval(_intervalForSwing);
         _isSwing = false;
      }
      
      private function swingHook() : void
      {
         _angle += _direction * 3.141592653589793 / _swingPeriod / 2 / 20;
         _angle = Math.min(3.141592653589793,Math.max(0,_angle));
         if(_angle == 3.141592653589793)
         {
            _direction = -1;
         }
         else if(_angle == 0)
         {
            _direction = 1;
         }
         var _loc1_:Matrix = new Matrix();
         graphics.clear();
         graphics.lineStyle(2,16777215,1);
         graphics.moveTo(0,0);
         graphics.lineTo(Math.cos(_angle) * 20,Math.sin(_angle) * 20);
         _loc1_.rotate(_angle);
         _hook.transform.matrix = _loc1_;
         _hook.x = Math.cos(_angle) * 20;
         _hook.y = Math.sin(_angle) * 20;
      }
      
      private function init() : void
      {
         _angle = 0;
         _currentRopeLong = 20;
         _swingPeriod = Number(_recaptureGoldGameXML.OtherData[0].SwingPeriod[0].@value);
      }
   }
}

