package UI.Button.Pet
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class PlayerBtn extends SwitchBtn
   {
      
      public function PlayerBtn()
      {
         super();
         setTipString("人物");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickPlayerBtn"));
      }
   }
}

