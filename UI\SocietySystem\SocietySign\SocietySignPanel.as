package UI.SocietySystem.SocietySign
{
   import UI.AnalogServiceHoldFunction;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.SocietySystem.MySocietyPanel.MySocietyPanel;
   import UI.SocietySystem.SocietySignVO;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class SocietySignPanel
   {
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_ableDragLS:AbleDragSpriteLogicShell;
      
      private var m_societySignLG:SocietySignLG;
      
      private var m_signShowGroup:SwitchBtnGroupLogicShell;
      
      private var m_signShows:Vector.<MySwitchBtnLogicShell>;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_outSignNumText:TextField;
      
      private var m_wantTicketPointText:TextField;
      
      private var m_getRewardShow:MovieClipPlayLogicShell;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_currentTime:String;
      
      private var m_show:MovieClip;
      
      private var m_mySocietyPanel:MySocietyPanel;
      
      private var m_societySystemXML:XML;
      
      private var m_societySignVO:SocietySignVO;
      
      public function SocietySignPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            ClearUtil.clearDisplayObjectInContainer(m_show);
            if(m_show.parent)
            {
               m_show.parent.removeChild(m_show);
            }
         }
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_ableDragLS);
         m_ableDragLS = null;
         m_societySignVO = null;
         ClearUtil.clearObject(m_societySignLG);
         m_societySignLG = null;
         ClearUtil.clearObject(m_signShowGroup);
         m_signShowGroup = null;
         ClearUtil.clearObject(m_signShows);
         m_signShows = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_outSignNumText = null;
         m_wantTicketPointText = null;
         ClearUtil.clearObject(m_getRewardShow);
         m_getRewardShow = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         m_currentTime = null;
         m_mySocietyPanel = null;
         m_societySystemXML = null;
      }
      
      public function setMySocietyPanel(param1:MySocietyPanel) : void
      {
         m_mySocietyPanel = param1;
         initShow2();
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
         initShow2();
      }
      
      public function setSocietySignVO(param1:SocietySignVO) : void
      {
         m_societySignVO = param1;
         initShow2();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_ableDragLS = new AbleDragSpriteLogicShell();
         m_ableDragLS.setShow(m_show);
         initShow();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getCurrentSignNum() : int
      {
         return m_societySignVO.getCurrentSignNum();
      }
      
      public function getAbleGotConValueId() : int
      {
         var _loc2_:Vector.<int> = MyFunction.getInstance().excreteString(m_societySystemXML.SignData[0].@getConValue);
         var _loc1_:int = _loc2_[getCurrentSignNum() - 1];
         ClearUtil.nullArr(_loc2_);
         _loc2_ = null;
         return _loc1_;
      }
      
      public function gotReward() : void
      {
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         m_getRewardShow.gotoAndStop("1");
         m_societySignVO.setGetRewardTime(m_currentTime);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
      }
      
      public function gotRewardFail() : void
      {
         m_getRewardShow.gotoAndStop("2");
         m_getRewardBtn = new ButtonLogicShell2();
         m_getRewardBtn.setShow(m_getRewardShow.getShow()["getRewardBtn"]);
         m_societySignVO.setGetRewardTime("");
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
      }
      
      private function initShow() : void
      {
         var _loc3_:int = 0;
         var _loc1_:MySwitchBtnLogicShell = null;
         if(m_societySystemXML == null)
         {
            throw new Error("societySystemXML is null");
         }
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
         var _loc2_:int = int(m_societySystemXML.SignData[0].@maxSignNum);
         m_signShows = new Vector.<MySwitchBtnLogicShell>();
         m_signShowGroup = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ = new MySwitchBtnLogicShell();
            _loc1_.setShow(m_show["signShow" + (_loc3_ + 1)]);
            _loc1_.setClickEnble(false);
            m_signShowGroup.addSwitchBtn(_loc1_);
            _loc3_++;
         }
         m_sureBtn = new ButtonLogicShell2();
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_cancelBtn = new ButtonLogicShell2();
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         m_getRewardShow = new MovieClipPlayLogicShell();
         m_getRewardShow.setShow(m_show["getRewardBtnShow"]);
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_societySignVO == null)
         {
            return;
         }
         if(m_show == null)
         {
            return;
         }
         if(m_mySocietyPanel == null)
         {
            return;
         }
         if(m_societySystemXML == null)
         {
            return;
         }
         if(m_signShowGroup == null)
         {
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc6_:* = undefined;
            var _loc5_:int = 0;
            var _loc3_:SaveTaskInfo = null;
            m_currentTime = param1;
            trace("SocietySignPanel get time:",m_currentTime);
            if(m_societySignLG == null)
            {
               m_societySignLG = new SocietySignLG();
            }
            var _loc2_:SignReturnData = new SignReturnData();
            var _loc4_:int = int(m_societySystemXML.SignData[0].@maxSignNum);
            m_societySignLG.sign(m_societySignVO.getsignTime(),m_currentTime,m_societySignVO.getCurrentSignNum(),_loc4_,false,_loc2_);
            if(_loc2_.isOutSign)
            {
               m_showMC.gotoAndStop("outSign");
               m_sureBtn = new ButtonLogicShell2();
               m_sureBtn.setShow(m_show["sureBtn"]);
               m_sureBtn.setTipString("点击购买补签");
               m_cancelBtn = new ButtonLogicShell2();
               m_cancelBtn.setShow(m_show["cancelBtn"]);
               m_cancelBtn.setTipString("点击取消购买补签");
               m_outSignNumText = m_show["outSignNumText"];
               m_wantTicketPointText = m_show["wantTicketPointText"];
               m_outSignNumText.text = _loc2_.outSignNum.toString();
               _loc6_ = MyFunction.getInstance().excreteString(m_societySystemXML.SignData[0].BuQianTicket[0].@ticketPrices);
               _loc5_ = _loc6_[Math.min(_loc2_.outSignNum,_loc6_.length - 1)];
               ClearUtil.nullArr(_loc6_);
               _loc6_ = null;
               m_wantTicketPointText.text = _loc5_.toString();
            }
            else
            {
               m_showMC.gotoAndStop("normalSign");
               ClearUtil.clearObject(m_sureBtn);
               m_sureBtn = null;
               ClearUtil.clearObject(m_cancelBtn);
               m_cancelBtn = null;
               m_outSignNumText = null;
               m_wantTicketPointText = null;
               if(_loc2_.isSign)
               {
                  m_societySignVO.setCurrentSignNum(_loc2_.newSignNum);
                  m_societySignVO.setsignTime(m_currentTime);
                  trace("new sign time:",m_currentTime);
                  _loc3_ = new SaveTaskInfo();
                  _loc3_.type = "4399";
                  _loc3_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc3_);
                  MyFunction2.saveGame();
               }
            }
            m_signShowGroup.getSwitchBtnByIndex(_loc2_.newSignNum - 1).turnActivate();
            ClearUtil.clearObject(m_getRewardBtn);
            if(new TimeUtil().newDateIsNewDay(m_societySignVO.getGetRewardTime(),m_currentTime))
            {
               m_getRewardShow.gotoAndStop("2");
               m_getRewardBtn = new ButtonLogicShell2();
               m_getRewardBtn.setShow(m_getRewardShow.getShow()["getRewardBtn"]);
            }
            else
            {
               m_getRewardShow.gotoAndStop("1");
            }
         },m_mySocietyPanel.showWarningBox,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var signReturnData1:SignReturnData;
         var maxSignNum1:int;
         var prices:Vector.<int>;
         var ticketIds:Vector.<String>;
         var price:int;
         var ticketId:String;
         var dataObj:Object;
         var signReturnData2:SignReturnData;
         var maxSignNum2:int;
         var saveinfo:SaveTaskInfo;
         var e:ButtonEvent = param1;
         switch(e.button)
         {
            case m_sureBtn:
               signReturnData1 = new SignReturnData();
               maxSignNum1 = int(m_societySystemXML.SignData[0].@maxSignNum);
               m_societySignLG.sign(m_societySignVO.getsignTime(),m_currentTime,m_societySignVO.getCurrentSignNum(),maxSignNum1,false,signReturnData1);
               prices = MyFunction.getInstance().excreteString(m_societySystemXML.SignData[0].BuQianTicket[0].@ticketPrices);
               ticketIds = MyFunction.getInstance().excreteStringToString(m_societySystemXML.SignData[0].BuQianTicket[0].@ticketIds);
               price = prices[Math.min(signReturnData1.outSignNum,prices.length - 1)];
               ticketId = ticketIds[Math.min(signReturnData1.outSignNum,ticketIds.length - 1)];
               ClearUtil.nullArr(prices);
               prices = null;
               ClearUtil.nullArr(ticketIds);
               ticketIds = null;
               dataObj = {};
               dataObj["propId"] = ticketId;
               dataObj["count"] = 1;
               dataObj["price"] = price;
               dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
               dataObj["tag"] = "帮会补签";
               AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
               {
                  if(param1["propId"] != ticketId)
                  {
                     m_mySocietyPanel.showWarningBox("购买物品id前后端不相同！",0);
                     throw new Error("购买物品id前后端不相同！");
                  }
                  var _loc2_:SignReturnData = new SignReturnData();
                  var _loc4_:int = int(m_societySystemXML.SignData[0].@maxSignNum);
                  m_societySignLG.sign(m_societySignVO.getsignTime(),m_currentTime,m_societySignVO.getCurrentSignNum(),_loc4_,true,_loc2_);
                  m_societySignVO.setCurrentSignNum(_loc2_.newSignNum);
                  m_societySignVO.setsignTime(m_currentTime);
                  trace("new sign time:",m_currentTime);
                  var _loc3_:SaveTaskInfo = new SaveTaskInfo();
                  _loc3_.type = "4399";
                  _loc3_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc3_);
                  MyFunction2.saveGame();
                  gotoNormalSignFrame();
                  m_signShowGroup.getSwitchBtnByIndex(_loc2_.newSignNum - 1).turnActivate();
               },m_mySocietyPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
               break;
            case m_cancelBtn:
               signReturnData2 = new SignReturnData();
               maxSignNum2 = int(m_societySystemXML.SignData[0].@maxSignNum);
               m_societySignLG.sign(m_societySignVO.getsignTime(),m_currentTime,m_societySignVO.getCurrentSignNum(),maxSignNum2,false,signReturnData2);
               m_societySignVO.setCurrentSignNum(signReturnData2.newSignNum);
               m_societySignVO.setsignTime(m_currentTime);
               trace("new sign time:",m_currentTime);
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame();
               gotoNormalSignFrame();
               break;
            case m_quitBtn:
            case m_getRewardBtn:
         }
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      public function getRewardBtn() : ButtonLogicShell2
      {
         return m_getRewardBtn;
      }
      
      private function gotoNormalSignFrame() : void
      {
         m_showMC.gotoAndStop("normalSign");
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         m_outSignNumText = null;
         m_wantTicketPointText = null;
      }
   }
}

