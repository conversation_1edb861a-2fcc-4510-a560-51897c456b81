package UI.InformationPanel
{
   import UI.MyFunction;
   import UI.MySprite;
   import UI.UIInterface.ISegmentedBar;
   import flash.display.DisplayObject;
   
   public class ScoreStar extends MySprite
   {
      
      public var star_1:Star;
      
      public var star_2:Star;
      
      public var star_3:Star;
      
      public var star_4:Star;
      
      public var star_5:Star;
      
      private var _stars:Vector.<ISegmentedBar> = new Vector.<ISegmentedBar>();
      
      public function ScoreStar()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         if(star_1)
         {
            star_1.clear();
         }
         if(star_2)
         {
            star_2.clear();
         }
         if(star_3)
         {
            star_3.clear();
         }
         if(star_4)
         {
            star_4.clear();
         }
         if(star_5)
         {
            star_5.clear();
         }
         star_1 = null;
         star_2 = null;
         star_3 = null;
         star_4 = null;
         star_5 = null;
         var _loc3_:int = 0;
         if(_stars)
         {
            _loc1_ = int(_stars.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _stars[_loc3_] = null;
               _loc3_++;
            }
         }
         _stars = null;
      }
      
      public function init() : void
      {
         _stars.push(star_1);
         _stars.push(star_2);
         _stars.push(star_3);
         _stars.push(star_4);
         _stars.push(star_5);
      }
      
      public function setStar(param1:Number) : void
      {
         MyFunction.getInstance().setSegmentedBar(param1,_stars);
      }
   }
}

