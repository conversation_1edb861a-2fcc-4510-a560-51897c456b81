package UI.newShop
{
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class NewShopItem extends MySprite
   {
      
      private var btnShowModel:ButtonLogicShell2;
      
      private var btnHideModel:ButtonLogicShell2;
      
      public var equipmentVo:EquipmentVO;
      
      public var showEquipmentVo:EquipmentVO;
      
      public var show:MovieClip;
      
      public var newShop:NewShop;
      
      public var xml:XML;
      
      public function NewShopItem(param1:MovieClip, param2:NewShop)
      {
         super();
         show = param1;
         newShop = param2;
         btnShowModel = new ButtonLogicShell2();
         btnShowModel.setShow(show["btnShowModel"]);
         btnShowModel.setTipString("点击预览装备");
         btnHideModel = new ButtonLogicShell2();
         btnHideModel.setShow(show["btnHideModel"]);
         btnHideModel.setTipString("点击取消预览");
         (show["txtTicket"] as TextField).mouseEnabled = false;
         (show["btnBuy"] as SimpleButton).addEventListener("click",onBuyHandler);
         show["btnShowModel"].addEventListener("click",onShowModelHandler);
         show["btnHideModel"].addEventListener("click",onHideModelHandler);
         cleanShow();
      }
      
      override public function clear() : void
      {
         (show["btnBuy"] as SimpleButton).removeEventListener("click",onBuyHandler);
         show["btnShowModel"].removeEventListener("click",onShowModelHandler);
         show["btnHideModel"].removeEventListener("click",onHideModelHandler);
         super.clear();
         ClearUtil.clearObject(show);
         show = null;
         ClearUtil.clearObject(btnShowModel);
         btnShowModel = null;
         ClearUtil.clearObject(equipmentVo);
         equipmentVo = null;
         ClearUtil.clearObject(showEquipmentVo);
         showEquipmentVo = null;
         newShop = null;
      }
      
      public function setXML(param1:XML) : void
      {
         if(xml)
         {
            ClearUtil.clearObject(equipmentVo);
            equipmentVo = null;
            showEquipmentVo = null;
            xml = null;
         }
         cleanShow();
         if(param1)
         {
            xml = param1;
            equipmentVo = XMLSingle.getEquipmentVOByID(xml.@id,XMLSingle.getInstance().equipmentXML);
            if(equipmentVo is FashionEquipmentVO)
            {
               showEquipmentVo = equipmentVo;
            }
            else if(equipmentVo is ScrollEquipmentVO)
            {
               if((equipmentVo as ScrollEquipmentVO).compositeEquipmentVO is WeaponEquipmentVO || (equipmentVo as ScrollEquipmentVO).compositeEquipmentVO is ClothesEquipmentVO || (equipmentVo as ScrollEquipmentVO).compositeEquipmentVO is FashionEquipmentVO || (equipmentVo as ScrollEquipmentVO).compositeEquipmentVO is ForeverFashionEquipmentVO)
               {
                  showEquipmentVo = (equipmentVo as ScrollEquipmentVO).compositeEquipmentVO;
               }
            }
            showEquip();
         }
      }
      
      private function onBuyHandler(param1:MouseEvent) : void
      {
         newShop.dispatchEvent(new UIDataEvent("showBox",{"equipmentVO":equipmentVo.clone()}));
      }
      
      private function onShowModelHandler(param1:MouseEvent) : void
      {
         newShop.resetModel(showEquipmentVo);
      }
      
      private function onHideModelHandler(param1:MouseEvent) : void
      {
         newShop.resetModel(null);
      }
      
      private function showEquip() : void
      {
         show.visible = true;
         show["mcName"]["txt"].text = equipmentVo.name;
         show["txtTicket"].text = String(equipmentVo.ticketPrice);
         if(showEquipmentVo)
         {
            if(showEquipmentVo.id == newShop.currentShowEquipmentId)
            {
               show["btnShowModel"].visible = false;
               show["btnHideModel"].visible = true;
            }
            else
            {
               if(showEquipmentVo.owner)
               {
                  if(showEquipmentVo.owner == GamingUI.getInstance().player1.playerVO.playerType)
                  {
                     show["btnShowModel"].visible = true;
                  }
                  else if(GamingUI.getInstance().player2 && showEquipmentVo.owner == GamingUI.getInstance().player2.playerVO.playerType)
                  {
                     show["btnShowModel"].visible = true;
                  }
                  else
                  {
                     show["btnShowModel"].visible = false;
                  }
               }
               else
               {
                  show["btnShowModel"].visible = true;
               }
               show["btnHideModel"].visible = false;
            }
         }
         else
         {
            show["btnShowModel"].visible = false;
            show["btnHideModel"].visible = false;
         }
         if(String(xml.@tips))
         {
            show["mcTips"].visible = true;
            show["mcTips"].gotoAndStop(String(xml.@tips));
         }
         else
         {
            show["mcTips"].visible = false;
         }
         var _loc1_:Equipment = MyFunction2.sheatheEquipmentShell(equipmentVo);
         _loc1_.name = "eq";
         _loc1_.addEventListener("rollOver",onOver2,false,0,true);
         _loc1_.addEventListener("rollOut",onOut2,false,0,true);
         show["mcEqCell"].addChild(_loc1_);
      }
      
      private function cleanShow() : void
      {
         var _loc1_:Equipment = null;
         show.visible = false;
         show["mcName"]["txt"].text = "";
         show["txtTicket"].text = "";
         show["btnShowModel"].visible = false;
         show["btnHideModel"].visible = false;
         show["mcTips"].visible = false;
         if(show["mcEqCell"].getChildByName("eq"))
         {
            _loc1_ = show["mcEqCell"].getChildByName("eq");
            ClearUtil.clearObject(_loc1_);
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         newShop.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         newShop.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

