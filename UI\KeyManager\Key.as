package UI.KeyManager
{
   public class Key
   {
      
      public static var A:int = 65;
      
      public static var B:int = 66;
      
      public static var C:int = 67;
      
      public static var D:int = 68;
      
      public static var E:int = 69;
      
      public static var F:int = 70;
      
      public static var G:int = 71;
      
      public static var H:int = 72;
      
      public static var I:int = 73;
      
      public static var J:int = 74;
      
      public static var K:int = 75;
      
      public static var L:int = 76;
      
      public static var M:int = 77;
      
      public static var N:int = 78;
      
      public static var O:int = 79;
      
      public static var P:int = 80;
      
      public static var Q:int = 81;
      
      public static var R:int = 82;
      
      public static var S:int = 83;
      
      public static var T:int = 84;
      
      public static var U:int = 85;
      
      public static var V:int = 86;
      
      public static var W:int = 87;
      
      public static var X:int = 88;
      
      public static var Y:int = 89;
      
      public static var Z:int = 90;
      
      public static var NUMBER_0:int = 48;
      
      public static var NUMBER_1:int = 49;
      
      public static var NUMBER_2:int = 50;
      
      public static var NUMBER_3:int = 51;
      
      public static var NUMBER_4:int = 52;
      
      public static var NUMBER_5:int = 53;
      
      public static var NUMBER_6:int = 54;
      
      public static var NUMBER_7:int = 55;
      
      public static var NUMBER_8:int = 56;
      
      public static var NUMBER_9:int = 57;
      
      public static var SHIFT:int = 16;
      
      public static var CONTROL:int = 17;
      
      public static var SPACE:int = 32;
      
      public static var ENTER:int = 13;
      
      public static var TAB:int = 9;
      
      public static var LEFT:int = 37;
      
      public static var UP:int = 38;
      
      public static var RIGHT:int = 39;
      
      public static var DOWN:int = 40;
      
      public static var NUMERIC_KEYPAD_NUMBER_0:int = 96;
      
      public static var NUMERIC_KEYPAD_NUMBER_1:int = 97;
      
      public static var NUMERIC_KEYPAD_NUMBER_2:int = 98;
      
      public static var NUMERIC_KEYPAD_NUMBER_3:int = 99;
      
      public static var NUMERIC_KEYPAD_NUMBER_4:int = 100;
      
      public static var NUMERIC_KEYPAD_NUMBER_5:int = 101;
      
      public static var NUMERIC_KEYPAD_NUMBER_6:int = 102;
      
      public static var NUMERIC_KEYPAD_NUMBER_7:int = 103;
      
      public static var NUMERIC_KEYPAD_NUMBER_8:int = 104;
      
      public static var NUMERIC_KEYPAD_NUMBER_9:int = 105;
      
      private var keyId:int;
      
      private var isDown:Boolean;
      
      public function Key(param1:int)
      {
         super();
         this.keyId = param1;
         setIsDown(false);
      }
      
      public function clear() : void
      {
      }
      
      public function setIsDown(param1:Boolean) : void
      {
         this.isDown = param1;
      }
      
      public function getIsDown() : Boolean
      {
         return isDown;
      }
      
      public function getKeyId() : int
      {
         return keyId;
      }
   }
}

