package YJFY.PKMode2
{
   import UI.EnterFrameTime;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.NewRank.RankDataInfo;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SubmitData;
   import YJFY.API_4399.RankListAPI.SubmitReturnData;
   import YJFY.API_4399.RankListAPI.SubmitSuccessReturnData;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.GameData;
   import YJFY.LoadUI2;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.IPK_test;
   import YJFY.PKMode.LocalPKRankListAPI;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.PKMode2.PKLogic.PKWorld;
   import YJFY.PKMode2.PKUI.PKPanel.PKPanel;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.Sprite;
   
   public class PK2 implements IPK_test
   {
      
      public static const s_const_OnePK:String = "onePK";
      
      public static const s_const_TwoPK:String = "twoPK";
      
      private const m_const_MaxDataNumInRankList:uint = 10000;
      
      private const m_const_getRankInforNum:uint = 100;
      
      private const m_const_pkPlayerNum:uint = 8;
      
      private const m_const_winAddPKPoint:uint = 10;
      
      private const m_const_failAddPKPoint:uint = 3;
      
      private var m_show:Sprite;
      
      private var m_gameWaitShowLayer:Sprite;
      
      private var m_gameMainLayer:Sprite;
      
      private var m_pkPanel:PKPanel;
      
      private var m_myPlayerDatas:PlayerDatas;
      
      private var m_foePalyerDatas:PlayerDatas;
      
      private var m_otherPlayerDatas:PlayerDatas;
      
      private var m_otherPlayerData:InitPlayersData;
      
      private var m_myUiPlayerData:InitPlayersData;
      
      private var m_myUiPlayer1:Player;
      
      private var m_myUiPlayer2:Player;
      
      private var m_foeUiPlayerData:InitPlayersData;
      
      private var m_foeUiPlayer1:Player;
      
      private var m_foeUiPlayer2:Player;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_localRankListAPI:LocalPKRankListAPI;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_xmlLoader:YJFYLoader;
      
      private var m_loadIsEx:Boolean;
      
      private var m_myDataInRankList:UserDataInRankList;
      
      private var m_panelShowClassName:String;
      
      private var m_pkType:String;
      
      private var m_pkIndex:uint;
      
      private var m_pkMap:PKWorld;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_isSigned:Boolean;
      
      private var m_getNeedRank:uint;
      
      private var m_pk2Listeners:Vector.<IPK2Listener>;
      
      private var m_pkMode2Data:PKMode2Data;
      
      private var m_versionControl:IVersionControl;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_loadUI:IProgressShow;
      
      private var m_pkMode2VO:PKMode2VO;
      
      private var m_pkTargetPlayerData:PKTargetPlayerData;
      
      public function PK2()
      {
         super();
         m_show = new MySprite();
         m_show.addEventListener("warningBox",sureOrCancel,true,0,true);
         m_show.addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_gameWaitShowLayer = new Sprite();
         m_gameMainLayer = new Sprite();
         m_show.addChild(m_gameMainLayer);
         m_show.addChild(m_gameWaitShowLayer);
         hideGameWaitShow();
         m_myPlayerDatas = new PlayerDatas();
         m_foePalyerDatas = new PlayerDatas();
         m_getPlayerDataListener = new GetPlayerDataListener();
         m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData2;
         m_myPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_foePalyerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_myPlayerDatas.init();
         m_foePalyerDatas.init();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.rankListErrorFun = rankListError1;
         m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerRankInfoSuccess;
         m_localRankListAPI = new LocalPKRankListAPI();
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(m_localRankListAPI);
         m_pk2Listeners = new Vector.<IPK2Listener>();
         m_pkMode2Data = new PKMode2Data();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("warningBox",sureOrCancel,true);
            m_show.removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,true);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,false);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,false);
         }
         if(Boolean(Part1.getInstance().getApi4399()) && Part1.getInstance().getApi4399().rankListAPI)
         {
            Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(null);
         }
         if(m_pkMap)
         {
            m_pkMap.removeUIShow(Part1.getInstance().getPart2());
         }
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         ClearUtil.clearObject(m_gameMainLayer);
         m_gameMainLayer = null;
         ClearUtil.clearObject(m_gameWaitShowLayer);
         m_gameWaitShowLayer = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_pkPanel);
         m_pkPanel = null;
         ClearUtil.clearObject(m_myPlayerDatas);
         m_myPlayerDatas = null;
         ClearUtil.clearObject(m_foePalyerDatas);
         m_foePalyerDatas = null;
         ClearUtil.clearObject(m_otherPlayerDatas);
         m_otherPlayerDatas = null;
         ClearUtil.clearObject(m_otherPlayerData);
         m_otherPlayerData = null;
         ClearUtil.clearObject(m_myUiPlayerData);
         m_myUiPlayerData = null;
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         ClearUtil.clearObject(m_foeUiPlayerData);
         m_foeUiPlayerData = null;
         ClearUtil.clearObject(m_foeUiPlayer1);
         m_foeUiPlayer1 = null;
         ClearUtil.clearObject(m_foeUiPlayer2);
         m_foeUiPlayer2 = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         ClearUtil.clearObject(m_localRankListAPI);
         m_localRankListAPI = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_xmlLoader);
         m_xmlLoader = null;
         ClearUtil.clearObject(m_myDataInRankList);
         m_myDataInRankList = null;
         m_panelShowClassName = null;
         m_pkType = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.clearObject(m_players);
         m_players = null;
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         ClearUtil.nullArr(m_pk2Listeners,false,false,false);
         m_pk2Listeners = null;
         ClearUtil.clearObject(m_pkMode2Data);
         m_pkMode2Data = null;
         m_versionControl = null;
         m_enterFrameTime = null;
         m_loadUI = null;
         m_pkMode2VO = null;
         m_pkTargetPlayerData = null;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function setLoadUI(param1:IProgressShow) : void
      {
         m_loadUI = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
      }
      
      public function addPK2Listener(param1:IPK2Listener) : void
      {
         ListenerUtil.addListener(m_pk2Listeners,param1);
      }
      
      public function removePK2Listener(param1:IPK2Listener) : void
      {
         ListenerUtil.removeListener(m_pk2Listeners,param1);
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function startPK(param1:int) : void
      {
         var pkIndex:int = param1;
         if(m_pkMode2Data.getPKNumOneDay() - m_pkMode2VO.getPKNum() <= 0)
         {
            showWarningBox("剩余次数不足",0);
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            resetPKResultData(param1);
            m_pkMode2VO.addOneResult_DefaultFail(m_pkTargetPlayerData);
            openPKWorld();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },showWarningBox,true);
      }
      
      public function startMatch() : void
      {
         clearPKDataForNextPK();
         getOnePKTargetPlayerRankData(0);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_pkMap)
         {
            m_pkMap.render(m_enterFrameTime);
            m_pkMap.showRender();
         }
      }
      
      public function openPKWorld() : void
      {
         if(m_pkMap)
         {
            return;
         }
         Part1.getInstance().continueGame();
         RankDataInfo.getInstance().setFightType(2);
         m_pkMap = new PKWorld();
         m_pkMap.setPK(this);
         m_pkMap.setIsTwoMode(true);
         if(m_gameMainLayer.contains(m_pkPanel))
         {
            m_gameMainLayer.removeChild(m_pkPanel);
         }
         m_gameMainLayer.addChild(m_pkMap);
         m_pkMap.setVersionControl(m_versionControl);
         m_pkMap.setLoadUI(m_loadUI as LoadUI2);
         m_pkMap.setMyLoader(m_myLoader);
         (m_loadUI as LoadUI2).tranToOpacity();
         m_pkMap.setEnterFrameTime(m_enterFrameTime);
         m_pkMap.init();
         if(m_pkType == "onePK")
         {
            m_pkMap.initUiPlayerData(m_myUiPlayer1,null,m_foeUiPlayer1,null);
         }
         else
         {
            m_pkMap.initUiPlayerData(m_myUiPlayer1,m_myUiPlayer2,m_foeUiPlayer1,m_foeUiPlayer2);
         }
         m_pkMap.initByXMLPath("NewGameFolder/pkMap2.xml");
         m_show.stage.focus = Part1.getInstance();
      }
      
      public function closePKWorld() : void
      {
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         m_pkPanel.refreshShow();
         m_gameMainLayer.addChild(m_pkPanel);
      }
      
      public function getMyUIPlayer1() : Player
      {
         return m_myUiPlayer1;
      }
      
      public function getMyUIPlayer2() : Player
      {
         return m_myUiPlayer2;
      }
      
      public function getFoeUIPlayer1() : Player
      {
         return m_foeUiPlayer1;
      }
      
      public function getFoeUIPlayer2() : Player
      {
         return m_foeUiPlayer2;
      }
      
      public function getMyPkTargetPlayerData() : PKTargetPlayerData
      {
         return m_pkTargetPlayerData;
      }
      
      public function addPKDataAndSubmitToRankList(param1:Boolean) : void
      {
         var isMyWin:Boolean = param1;
         if(isMyWin)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               var _loc3_:SubmitData = null;
               resetPKResultData(param1);
               m_pkTargetPlayerData.setPKStateToWin();
               m_pkMode2VO.tranFailToWin(m_pkTargetPlayerData);
               _loc3_ = new SubmitData(m_pkMode2Data.getCurrentRankId(param1,true),m_pkMode2VO.getWinPKResultNum() > 0 ? m_pkMode2VO.getWinPKResultNum() : 1);
               m_rankListAPIListener.submitScoreInfoReturnFun = submitScoreInfoReturnFun;
               m_rankListAPIListener.submitScoreInfoErrorFun = submitScoreInfoErrorFun;
               GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
               var _loc4_:Vector.<SubmitData> = new Vector.<SubmitData>();
               _loc4_.push(_loc3_);
               GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(GameData.getInstance().getSaveFileData().index,_loc4_);
               trace("提交数据到排行榜:","存档索引:",GameData.getInstance().getSaveFileData().index,"submitData:","rankListId:",_loc3_.getRId(),"winMath:",_loc3_.getScore());
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },showWarningBox,true);
         }
      }
      
      private function getOnePKTargetPlayerRankData(param1:uint) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         showGameWaitShow();
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getRankListsDataFun = getRankInfos;
         if(!param1)
         {
            _loc2_ = m_myDataInRankList ? m_myDataInRankList.getRank() : 10000;
            _loc3_ = Math.max(1,_loc2_ - 100);
            param1 = _loc3_ + Math.random() * 100;
         }
         Part1.getInstance().getApi4399().rankListAPI.getRankListsData(m_pkMode2Data.getCurrentRankId(GamingUI.getInstance().getNewestTimeStrFromSever(),true),1,param1);
         m_getNeedRank = param1;
      }
      
      public function init(param1:String) : void
      {
         var pkType:String = param1;
         m_pkType = pkType;
         Part1.getInstance().showGameWaitShow();
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            Part1.getInstance().hideGameWaitShow();
            switch(pkType)
            {
               case "onePK":
                  m_pkMode2VO = GamingUI.getInstance().player1.getPKMode2VO1();
                  m_panelShowClassName = "PKPanel";
                  break;
               case "twoPK":
                  m_panelShowClassName = "FreePkChooseUI";
                  break;
               default:
                  throw new Error();
            }
            ClearUtil.clearObject(m_xmlLoader);
            m_xmlLoader = new YJFYLoader();
            m_xmlLoader.setVersionControl(m_versionControl);
            m_xmlLoader.setProgressShow(m_loadUI);
            m_xmlLoader.getXML("UIData/pkMode2.xml",getConfigXMLSucces,getShowFail);
            m_xmlLoader.load();
         },function():void
         {
            GamingUI.getInstance().showMessageTip("加载失败");
            Part1.getInstance().hideGameWaitShow();
            Part1.getInstance().closePK();
         },true);
      }
      
      private function getMyInfoInRankList() : void
      {
         Part1.getInstance().showGameWaitShow();
         m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerRankInfoSuccess;
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getOneRankInfo(m_pkMode2Data.getCurrentRankId(GamingUI.getInstance().getNewestTimeStrFromSever(),true),GameData.getInstance().getLoginReturnData().getName());
         trace("getMyInfoInRankList rankListId:",m_pkMode2Data.getCurrentRankId(GamingUI.getInstance().getNewestTimeStrFromSever(),true),"name:",GameData.getInstance().getLoginReturnData().getName());
      }
      
      private function init2() : void
      {
         newDayResetPkNum(GamingUI.getInstance().getNewestTimeStrFromSever());
         init3();
      }
      
      private function init3() : void
      {
         m_rankListAPIListener.rankListErrorFun = rankListError2;
         if(m_myLoader == null)
         {
            m_myLoader = new YJFYLoader();
            m_myLoader.setVersionControl(m_versionControl);
            m_myLoader.setProgressShow(m_loadUI);
         }
         m_myLoader.getClass("NewGameFolder/PKMode2/PKUI.swf",m_panelShowClassName,getPanelShowSuccess,getShowFail);
         m_myLoader.getClass("NewGameFolder/PKMode2/PKSource.swf",null,null,null);
         m_myLoader.getClass("NewGameFolder/PKMode2/PKUI.swf","GameWaitShow",getGameWaitShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      private function getConfigXMLSucces(param1:YJFYLoaderData) : void
      {
         var _loc2_:XML = param1.resultXML;
         m_pkMode2Data.initByXML(_loc2_.pk.(@pkType == m_pkType)[0]);
         resetPKResultData(GamingUI.getInstance().getNewestTimeStrFromSever());
         getMyInfoInRankList();
      }
      
      private function getGameWaitShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_gameWaitShowLayer.addChild(new _loc2_());
      }
      
      private function getPanelShowSuccess(param1:YJFYLoaderData) : void
      {
         ClearUtil.clearObject(m_pkPanel);
         var _loc2_:Class = param1.resultClass;
         m_pkPanel = new PKPanel();
         var _loc3_:uint = m_myDataInRankList ? m_myDataInRankList.getRank() : 0;
         if(m_pkType == "onePK")
         {
            m_pkPanel.init(new _loc2_(),this,GamingUI.getInstance().player1,null,_loc3_,GamingUI.getInstance().player1.getPKMode2VO1(),m_pkMode2Data,m_pkType,m_pkMode2Data.getCurrentRankId(GamingUI.getInstance().getNewestTimeStrFromSever(),true));
            m_gameMainLayer.addChild(m_pkPanel);
            return;
         }
         if(m_pkType == "twoPK")
         {
            throw new Error("还没准备好");
         }
         throw new Error("出错了");
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("加载失败");
         Part1.getInstance().closePK();
      }
      
      public function showGameWaitShow() : void
      {
         m_gameWaitShowLayer.visible = true;
      }
      
      public function hideGameWaitShow() : void
      {
         m_gameWaitShowLayer.visible = false;
      }
      
      private function newDayResetPkNum(param1:String) : void
      {
         m_pkMode2VO.resetPKNum(param1);
      }
      
      private function resetPKResultData(param1:String) : void
      {
         if(m_pkMode2Data.getRefrenceTime())
         {
            m_pkMode2VO.resetPKResultDataByTime(param1,m_pkMode2Data.getResetTimeLong(),m_pkMode2Data.getRefrenceTime());
         }
      }
      
      public function lookUpMyPlayerInfor() : void
      {
         openPlayerShowPanel(m_myUiPlayer1,m_myUiPlayer2,m_myUiPlayerData.nickNameData);
      }
      
      public function lookUpFoePlayerInfor() : void
      {
         openPlayerShowPanel(m_foeUiPlayer1,m_foeUiPlayer2,m_foeUiPlayerData.nickNameData);
      }
      
      public function lookUpPlayerInfor(param1:String, param2:uint) : void
      {
         if(m_otherPlayerDatas == null)
         {
            m_otherPlayerDatas = new PlayerDatas();
            if(m_getPlayerDataListener == null)
            {
               m_getPlayerDataListener = new GetPlayerDataListener();
            }
            m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData2;
            m_otherPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
            m_otherPlayerDatas.init();
         }
         m_otherPlayerDatas.getPlayerData(param1,param2);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         m_gameMainLayer.addChild(m_otherPlayerShowPanel);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
      
      private function getPlayerData2(param1:InitPlayersData, param2:PlayerDatas) : void
      {
         if(param2 == m_otherPlayerDatas)
         {
            m_otherPlayerData = param1;
            m_otherPlayerDatas = param2;
            openPlayerShowPanel(param1.player1,param1.player2,param1.nickNameData);
            return;
         }
         if(param2 == m_myPlayerDatas)
         {
            m_myUiPlayerData = param1;
            m_myUiPlayer1 = param1.player1;
            m_myUiPlayer2 = param1.player2;
            if(ready() == false)
            {
               getFoeSaveData();
            }
         }
         else if(param2 == m_foePalyerDatas)
         {
            m_foeUiPlayerData = param1;
            m_foeUiPlayer1 = param1.player1;
            m_foeUiPlayer2 = param1.player2;
            m_pkTargetPlayerData.updateData(param1.player1.playerVO.playerType,param1.nickNameData && param1.nickNameData.extra ? param1.nickNameData.extra : "");
            ready();
         }
      }
      
      private function ready() : Boolean
      {
         if(Boolean(m_myUiPlayer1) && Boolean(m_foeUiPlayer1))
         {
            hideGameWaitShow();
            trace("装备好了：","myPlayerType:",m_myUiPlayer1.playerVO.playerType,"foePlayerType:",m_foeUiPlayer1.playerVO.playerType);
            dispatchMatchComplete();
            return true;
         }
         return false;
      }
      
      private function getPlayerRankInfoSuccess(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         Part1.getInstance().hideGameWaitShow();
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getOneRankInfoSuccessFun = null;
         var _loc2_:int = param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(param1[_loc4_].getUid() == GameData.getInstance().getLoginReturnData().getUid() && param1[_loc4_].getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               m_myDataInRankList = param1[_loc4_];
               _loc3_ = true;
               break;
            }
            _loc4_++;
         }
         if(m_pkMode2VO.getWinPKResultNum() || m_isSigned == true || _loc3_ || m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            init2();
         }
         else
         {
            submitMyDataWhenNoHave();
         }
      }
      
      private function submitMyDataWhenNoHave() : void
      {
         var _loc1_:SubmitData = null;
         _loc1_ = new SubmitData(m_pkMode2Data.getCurrentRankId(GamingUI.getInstance().getNewestTimeStrFromSever(),true),m_pkMode2VO.getWinPKResultNum() > 0 ? m_pkMode2VO.getWinPKResultNum() : 1);
         Part1.getInstance().showGameWaitShow();
         m_rankListAPIListener.submitScoreInfoReturnFun = submitScoreInfoReturnFunWhenNotHave;
         m_rankListAPIListener.submitScoreInfoErrorFun = submitScoreInfoErrorFunWhenNotHave;
         GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         var _loc2_:Vector.<SubmitData> = new Vector.<SubmitData>();
         _loc2_.push(_loc1_);
         GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(GameData.getInstance().getSaveFileData().index,_loc2_);
         trace("提交数据到排行榜:","存档索引:",GameData.getInstance().getSaveFileData().index,"submitData:","rankListId:",_loc1_.getRId(),"winMath:",_loc1_.getScore());
      }
      
      private function getRankInfos(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc2_:UserDataInRankList = null;
         var _loc3_:PKTargetPlayerData = null;
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getRankListsDataFun = null;
         if(param1.length == 1)
         {
            _loc2_ = param1[0];
            if(_loc2_.getUid() == GameData.getInstance().getLoginReturnData().getUid() && _loc2_.getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               getOnePKTargetPlayerRankData(m_getNeedRank - 1);
               return;
            }
            _loc3_ = new PKTargetPlayerData(_loc2_.getUid(),_loc2_.getIndex(),m_pkIndex,2,_loc2_.getUserName(),"","",_loc2_);
            m_pkTargetPlayerData = _loc3_;
            m_getNeedRank = 0;
            getMySaveData();
         }
         else
         {
            if(param1.length > 1)
            {
               throw new Error("出错了");
            }
            getOnePKTargetPlayerRankData(m_getNeedRank = m_getNeedRank - 1);
         }
      }
      
      private function getMySaveData() : void
      {
         m_myPlayerDatas.getPlayerData(GameData.getInstance().getLoginReturnData().getUid(),GameData.getInstance().getSaveFileData().index,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800);
      }
      
      private function getFoeSaveData() : void
      {
         m_foePalyerDatas.getPlayerData(m_pkTargetPlayerData.getUid(),m_pkTargetPlayerData.getIndexOfSaveXML(),1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800);
      }
      
      private function submitScoreInfoReturnFunWhenNotHave(param1:Vector.<SubmitSuccessReturnData>) : void
      {
         Part1.getInstance().hideGameWaitShow();
         ClearUtil.clearObject(param1);
         param1 = null;
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         trace("提交数据到排行榜成功");
         m_isSigned = true;
         init(m_pkType);
      }
      
      private function submitScoreInfoErrorFunWhenNotHave(param1:SubmitReturnData, param2:String) : void
      {
         Part1.getInstance().hideGameWaitShow();
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(param1);
         param1 = null;
         Part1.getInstance().closePK();
         Part1.getInstance().returnCity();
         GamingUI.getInstance().showMessageTip("数据提交失败");
         trace("提交数据到排行榜失败, errorMessage:",param2);
      }
      
      private function submitScoreInfoReturnFun(param1:Vector.<SubmitSuccessReturnData>) : void
      {
         ClearUtil.clearObject(param1);
         param1 = null;
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         trace("提交数据到排行榜成功");
      }
      
      private function submitScoreInfoErrorFun(param1:SubmitReturnData, param2:String) : void
      {
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(param1);
         param1 = null;
         trace("提交数据到排行榜失败");
      }
      
      private function rankListError1(param1:String) : void
      {
         Part1.getInstance().hideGameWaitShow();
         hideGameWaitShow();
         GamingUI.getInstance().showMessageTip("加载数据出错");
         Part1.getInstance().closePK();
         Part1.getInstance().returnCity();
      }
      
      private function rankListError2(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_gameMainLayer.addChildAt(MessageBoxEngine.getInstance(),m_gameMainLayer.numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,m_otherPlayerShowPanel ? m_otherPlayerShowPanel.currentPlayer : null);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_gameMainLayer.stage);
         m_gameMainLayer.addChildAt(WarningBoxSingle.getInstance(),m_gameMainLayer.numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function clearPKDataForNextPK() : void
      {
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         m_foeUiPlayer1 = null;
         m_foeUiPlayer2 = null;
      }
      
      private function dispatchMatchComplete() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:Vector.<IPK2Listener> = m_pk2Listeners.slice(0);
         _loc2_ = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].matchComplete(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
   }
}

