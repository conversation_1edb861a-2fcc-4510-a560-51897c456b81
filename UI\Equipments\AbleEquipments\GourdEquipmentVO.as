package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class GourdEquipmentVO extends AbleEquipmentVO
   {
      
      private var _maxMagic:int;
      
      private var _minMaxMagic:int;
      
      private var _maxMaxMagic:int;
      
      public var addPlayerSaveAttr:Vector.<String> = new Vector.<String>();
      
      public var addPlayerSaveAttrVals:Vector.<Number> = new Vector.<Number>();
      
      public function GourdEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.maxMagic = _maxMagic;
         _antiwear.maxMaxMagic = _maxMaxMagic;
         _antiwear.minMaxMagic = _minMaxMagic;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:GourdEquipmentVO = new GourdEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as GourdEquipmentVO).maxMagic = this.maxMagic;
         (param1 as GourdEquipmentVO).minMaxMagic = this.minMaxMagic;
         (param1 as GourdEquipmentVO).maxMaxMagic = this.maxMaxMagic;
         _loc2_ = int(addPlayerSaveAttr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as GourdEquipmentVO).addPlayerSaveAttr.push(addPlayerSaveAttr[_loc3_]);
            _loc3_++;
         }
         _loc2_ = int(addPlayerSaveAttrVals.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            (param1 as GourdEquipmentVO).addPlayerSaveAttrVals.push(addPlayerSaveAttrVals[_loc3_]);
            _loc3_++;
         }
      }
      
      public function get maxMagic() : int
      {
         return _antiwear.maxMagic;
      }
      
      public function set maxMagic(param1:int) : void
      {
         _antiwear.maxMagic = param1;
      }
      
      public function get maxMaxMagic() : int
      {
         return _antiwear.maxMaxMagic;
      }
      
      public function set maxMaxMagic(param1:int) : void
      {
         _antiwear.maxMaxMagic = param1;
      }
      
      public function get minMaxMagic() : int
      {
         return _antiwear.minMaxMagic;
      }
      
      public function set minMaxMagic(param1:int) : void
      {
         _antiwear.minMaxMagic = param1;
      }
   }
}

