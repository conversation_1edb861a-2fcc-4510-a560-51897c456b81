package YJFY.Utils.MovieClipToBitmapDatas
{
   import YJFY.Utils.ClearUtil;
   
   public class DataOfBitmapDatas
   {
      
      private var m_dataOfBitmapDatas:Vector.<DataOfBitmapData>;
      
      public function DataOfBitmapDatas()
      {
         super();
         m_dataOfBitmapDatas = new Vector.<DataOfBitmapData>();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_dataOfBitmapDatas);
         m_dataOfBitmapDatas = null;
      }
      
      public function clearData() : void
      {
         ClearUtil.clearObject(m_dataOfBitmapDatas);
         m_dataOfBitmapDatas = new Vector.<DataOfBitmapData>();
      }
      
      public function addDataOfBitmapData(param1:DataOfBitmapData) : void
      {
         m_dataOfBitmapDatas.push(param1);
      }
      
      public function getDataNumOfBitmapData() : int
      {
         return m_dataOfBitmapDatas.length;
      }
      
      public function getDataOfBitmapDataByIndex(param1:int) : DataOfBitmapData
      {
         return m_dataOfBitmapDatas[param1];
      }
   }
}

