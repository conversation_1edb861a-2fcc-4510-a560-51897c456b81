package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_AddConValueReturn extends InformationBodyDetail
   {
      
      protected var m_isSuccess:int;
      
      public function DOWN_AddConValueReturn()
      {
         super();
         m_informationBodyId = 3028;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_isSuccess = param1.readInt();
      }
      
      public function getIsSuccess() : int
      {
         return m_isSuccess;
      }
   }
}

