package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class DOWN_RemoveMemberReturnToLeader extends InformationBodyDetail
   {
      
      protected var m_isSuccess:int;
      
      protected var m_uid_member:Number;
      
      protected var m_idx_member:int;
      
      public function DOWN_RemoveMemberReturnToLeader()
      {
         super();
         m_informationBodyId = 3034;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_isSuccess = param1.readInt();
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid_member = _loc2_.uid;
         m_idx_member = _loc2_.idx;
      }
      
      public function getIsSuccess() : Boolean
      {
         return Boolean(m_isSuccess);
      }
      
      public function getUidMember() : Number
      {
         return m_uid_member;
      }
      
      public function getIdxMember() : int
      {
         return m_idx_member;
      }
   }
}

