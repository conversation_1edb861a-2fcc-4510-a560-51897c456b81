package UI.Equipments.AbleEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Players.PlayerVO;
   import UI.ShiTu.PromoteValueObject;
   
   public class AbleEquipmentVO extends EquipmentVO
   {
      
      public var suitName:String;
      
      private var _implictProPlayerId:String;
      
      private var _rengPin:int;
      
      private var _minRenPin:int;
      
      private var _maxRenPin:int;
      
      private var _maxLevel:int;
      
      private var _upgradePrice:int;
      
      private var _upgradeGemNum:int;
      
      private var _upgradeValue:int;
      
      private var _upgradeValue2:Number;
      
      private var _upgradeSuccessRate:Number = 0;
      
      private var _detectiontype:int;
      
      private var _maxHoleNum:int;
      
      private var _insetGems:Vector.<InsetGemEquipmentVO>;
      
      public function AbleEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.rengPin = _rengPin;
         _antiwear.maxLevel = _maxLevel;
         _antiwear.minRenPin = _minRenPin;
         _antiwear.maxRenPin = _maxRenPin;
         _antiwear.upgradePrice = _upgradePrice;
         _antiwear.upgradeGemNum = _upgradeGemNum;
         _antiwear.upgradeSuccessRate = _upgradeSuccessRate;
         _antiwear.upgradeValue = _upgradeValue;
         _antiwear.upgradeValue2 = _upgradeValue2;
         _antiwear.detectiontype = _detectiontype;
         _antiwear.maxHoleNum = _maxHoleNum;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:AbleEquipmentVO = new AbleEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as AbleEquipmentVO).rengPin = this.rengPin;
         (param1 as AbleEquipmentVO).minRenPin = this.minRenPin;
         (param1 as AbleEquipmentVO).maxRenPin = this.maxRenPin;
         (param1 as AbleEquipmentVO).maxLevel = this.maxLevel;
         (param1 as AbleEquipmentVO).upgradePrice = this.upgradePrice;
         (param1 as AbleEquipmentVO).upgradeGemNum = this.upgradeGemNum;
         (param1 as AbleEquipmentVO).upgradeSuccessRate = this.upgradeSuccessRate;
         (param1 as AbleEquipmentVO).upgradeValue = this.upgradeValue;
         (param1 as AbleEquipmentVO).upgradeValue2 = this.upgradeValue2;
         (param1 as AbleEquipmentVO).detectiontype = this.detectiontype;
         (param1 as AbleEquipmentVO)._implictProPlayerId = this._implictProPlayerId;
         (param1 as AbleEquipmentVO).suitName = this.suitName;
         (param1 as AbleEquipmentVO).maxHoleNum = this.maxHoleNum;
         (param1 as AbleEquipmentVO).addInsetGems(cloneHoleAndInsetGems());
      }
      
      public function cloneHoleAndInsetGems() : Vector.<InsetGemEquipmentVO>
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:* = undefined;
         var _loc1_:InsetGemEquipmentVO = null;
         _loc2_ = _insetGems ? _insetGems.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc3_ == null)
            {
               _loc3_ = new Vector.<InsetGemEquipmentVO>();
            }
            _loc1_ = _insetGems[_loc4_] ? _insetGems[_loc4_].clone() as InsetGemEquipmentVO : null;
            _loc3_.push(_loc1_);
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function setInsetGem(param1:Vector.<InsetGemEquipmentVO>) : void
      {
         if(_insetGems)
         {
            _insetGems.length = 0;
         }
         else
         {
            _insetGems = new Vector.<InsetGemEquipmentVO>();
         }
         _insetGems = param1;
      }
      
      override public function getIsBinding() : Boolean
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(super.getIsBinding() == true)
         {
            return true;
         }
         _loc1_ = _insetGems ? _insetGems.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(Boolean(_insetGems[_loc2_]) && _insetGems[_loc2_].getIsBinding() == true)
            {
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function get implictProPlayerId() : String
      {
         return _implictProPlayerId;
      }
      
      public function set implictProPlayerId(param1:String) : void
      {
         _implictProPlayerId = param1;
      }
      
      public function get rengPin() : int
      {
         return _antiwear.rengPin;
      }
      
      public function set rengPin(param1:int) : void
      {
         _antiwear.rengPin = param1;
      }
      
      public function get minRenPin() : int
      {
         return _antiwear.minRenPin;
      }
      
      public function set minRenPin(param1:int) : void
      {
         _antiwear.minRenPin = param1;
      }
      
      public function get maxRenPin() : int
      {
         return _antiwear.maxRenPin;
      }
      
      public function set maxRenPin(param1:int) : void
      {
         _antiwear.maxRenPin = param1;
      }
      
      public function get maxLevel() : int
      {
         return _antiwear.maxLevel;
      }
      
      public function set maxLevel(param1:int) : void
      {
         _antiwear.maxLevel = param1;
      }
      
      public function get upgradePrice() : int
      {
         return _antiwear.upgradePrice;
      }
      
      public function set upgradePrice(param1:int) : void
      {
         _antiwear.upgradePrice = param1;
      }
      
      public function get upgradeGemNum() : int
      {
         return _antiwear.upgradeGemNum;
      }
      
      public function set upgradeGemNum(param1:int) : void
      {
         _antiwear.upgradeGemNum = param1;
      }
      
      public function get upgradeSuccessRate() : Number
      {
         return _antiwear.upgradeSuccessRate;
      }
      
      public function set upgradeSuccessRate(param1:Number) : void
      {
         _antiwear.upgradeSuccessRate = param1;
      }
      
      public function get upgradeValue() : int
      {
         return _antiwear.upgradeValue;
      }
      
      public function set upgradeValue(param1:int) : void
      {
         _antiwear.upgradeValue = param1;
      }
      
      public function get upgradeValue2() : Number
      {
         return _antiwear.upgradeValue2;
      }
      
      public function set upgradeValue2(param1:Number) : void
      {
         _antiwear.upgradeValue2 = param1;
      }
      
      public function get detectiontype() : int
      {
         return _antiwear.detectiontype;
      }
      
      public function set detectiontype(param1:int) : void
      {
         _antiwear.detectiontype = param1;
      }
      
      public function get maxHoleNum() : int
      {
         return _antiwear.maxHoleNum;
      }
      
      public function set maxHoleNum(param1:int) : void
      {
         _antiwear.maxHoleNum = param1;
      }
      
      override public function set playerVO(param1:PlayerVO) : void
      {
         recoverInsetGemProAttri();
         super.playerVO = param1;
         setInsetGemProAttri();
      }
      
      public function addHole() : void
      {
         if(_insetGems == null)
         {
            _insetGems = new Vector.<InsetGemEquipmentVO>();
         }
         _insetGems.push(null);
      }
      
      public function addInsetGem(param1:InsetGemEquipmentVO) : void
      {
         var _loc3_:int = 0;
         recoverInsetGemProAttri();
         var _loc2_:int = _insetGems ? _insetGems.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_insetGems[_loc3_] == null)
            {
               _insetGems[_loc3_] = param1;
               break;
            }
            _loc3_++;
         }
         setInsetGemProAttri();
      }
      
      public function addInsetGems(param1:Vector.<InsetGemEquipmentVO>) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = param1 ? param1.length : 0;
         if(_loc2_)
         {
            recoverInsetGemProAttri();
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_insetGems == null)
            {
               _insetGems = new Vector.<InsetGemEquipmentVO>();
            }
            _insetGems.push(param1[_loc3_]);
            _loc3_++;
         }
         if(_loc2_)
         {
            setInsetGemProAttri();
         }
      }
      
      public function removeInsetGem(param1:InsetGemEquipmentVO) : InsetGemEquipmentVO
      {
         var _loc3_:InsetGemEquipmentVO = null;
         var _loc2_:int = int(_insetGems.indexOf(param1));
         if(_loc2_ != -1)
         {
            recoverInsetGemProAttri();
            _loc3_ = _insetGems[_loc2_];
            _insetGems[_loc2_] = null;
            setInsetGemProAttri();
         }
         return _loc3_;
      }
      
      public function getInsetGem(param1:int) : InsetGemEquipmentVO
      {
         if(param1 > (_insetGems ? _insetGems.length - 1 : 0) || param1 < 0)
         {
            throw new Error("镶嵌宝石索引超出范围");
         }
         return _insetGems[param1];
      }
      
      public function getHoleNum() : int
      {
         return _insetGems ? _insetGems.length : 0;
      }
      
      public function isAllOpen() : Boolean
      {
         if(_insetGems == null)
         {
            return false;
         }
         return _insetGems.length >= maxHoleNum;
      }
      
      public function getIsInlayAll() : Boolean
      {
         var _loc2_:int = 0;
         if(_insetGems == null)
         {
            return true;
         }
         var _loc1_:Boolean = true;
         _loc2_ = 0;
         while(_loc2_ < _insetGems.length)
         {
            if(_insetGems[_loc2_] == null)
            {
               _loc1_ = false;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function getIsXiangqian() : Boolean
      {
         var _loc2_:int = 0;
         if(_insetGems == null)
         {
            return false;
         }
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < _insetGems.length)
         {
            if(_insetGems[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ >= 1)
         {
            return true;
         }
         return false;
      }
      
      public function getCanZhai() : Boolean
      {
         var _loc1_:int = 0;
         if(_insetGems == null)
         {
            return false;
         }
         var _loc2_:Boolean = false;
         _loc1_ = 0;
         while(_loc1_ < _insetGems.length)
         {
            if(_insetGems[_loc1_])
            {
               _loc2_ = true;
            }
            _loc1_++;
         }
         return _loc2_;
      }
      
      public function setInsetGemProAttri() : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:InsetGemEquipmentVO = null;
         _loc1_ = _insetGems ? _insetGems.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc2_ = _insetGems[_loc5_];
            if(_loc2_)
            {
               _loc3_ = int(_loc2_.attributes.length);
               _loc4_ = 0;
               while(_loc4_ < _loc3_)
               {
                  this[_loc2_.attributes[_loc4_]](_loc2_.attributeValues[_loc4_]);
                  _loc4_++;
               }
            }
            _loc5_++;
         }
      }
      
      public function recoverInsetGemProAttri() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:InsetGemEquipmentVO = null;
         var _loc2_:PromoteValueObject = null;
         _loc1_ = _insetGems ? _insetGems.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc1_)
         {
            _loc3_ = _insetGems[_loc6_];
            if(_loc3_)
            {
               _loc4_ = int(_loc3_.attributes.length);
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _loc2_ = _loc3_.attributeValues[_loc5_].getOppositeValueObject();
                  this[_loc3_.attributes[_loc5_]](_loc2_);
                  _loc2_.clear();
                  _loc5_++;
               }
            }
            _loc6_++;
         }
      }
      
      public function promotePlayerAttr(param1:PromoteValueObject) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(_playerVO)
         {
            _loc2_ = int(param1.addAttributes.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_playerVO.hasOwnProperty(param1.addAttributes[_loc3_]))
               {
                  _playerVO[param1.addAttributes[_loc3_]](param1.addAttributeValueObjects[_loc3_]);
               }
               else
               {
                  _playerVO.set(param1.addAttributes[_loc3_],(Number(playerVO.get(param1.addAttributes[_loc3_])) + Number(param1.addAttributeValueObjects[_loc3_].value)).toFixed(4));
               }
               _loc3_++;
            }
         }
      }
      
      public function promotePetAttr(param1:PromoteValueObject) : void
      {
      }
   }
}

