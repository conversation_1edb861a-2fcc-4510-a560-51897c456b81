package
{
   import YJFY.Part1;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.system.System;
   
   public class Main extends Sprite
   {
      
      public static var _4399_function_ad_id:String = "92d6cef76cd06829e088932fe9fd819b";
      
      public static var serviceHold:* = null;
      
      public var _4399_function_rankList_id:String = "69f52ab6eb1061853a761ee8c26324ae";
      
      public var _4399_function_store_id:String = "3885799f65acec467d97b4923caebaae";
      
      public var _4399_function_shop_id:String = "30ea6b51a23275df624b781c3eb43ac6";
      
      public var _4399_function_payMoney_id:String = "10f73c09b41d9f41e761232f5f322f38";
      
      private var m_isInitOk:Boolean;
      
      private var m_isInited:Boolean;
      
      public function Main()
      {
         super();
         if(stage != null)
         {
            init4399();
         }
         else
         {
            addEventListener("addedToStage",init4399);
         }
         addEventListener("xctrl",cryGameOk);
         trace("add event listener: ","xctrl");
      }
      
      public function setHold(param1:*) : void
      {
         serviceHold = param1;
      }
      
      private function init4399(param1:Event = null) : void
      {
         trace("init4399");
         if(hasEventListener("addedToStage"))
         {
            removeEventListener("addedToStage",init4399);
         }
         System.useCodePage = false;
         init();
      }
      
      private function cryGameOk(param1:Event) : void
      {
         if(hasEventListener("xctrl"))
         {
            removeEventListener("xctrl",init4399);
         }
         trace("收到初始化事件：","xctrl");
         m_isInitOk = true;
         if(Part1.getInstance().getConfigXML())
         {
            init2();
         }
      }
      
      private function init() : void
      {
         stage.stageFocusRect = false;
         stage.focus = this;
         this.focusRect = false;
         addEventListener("xctrl",cryGameOk);
         trace("add event listener: ","xctrl");
         stage.addEventListener("xctrl",cryGameOk);
         trace("stage add event listener: ","xctrl");
         var _loc1_:Part1 = new Part1();
         addChild(_loc1_);
         _loc1_.setServiceHold(serviceHold);
         _loc1_.init(loadConfigComplete);
         addEventListener("click",onClick);
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         trace("点击目标:" + param1.target + "--name:" + param1.target.name + "--parent:" + param1.target.parent);
      }
      
      private function loadConfigComplete() : void
      {
         if(m_isInitOk || Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            init2();
         }
      }
      
      private function init2() : void
      {
         if(m_isInited == false)
         {
            Part1.getInstance().init2();
            m_isInited = true;
         }
      }
      
      private function click(param1:Event) : void
      {
         trace(param1.target);
      }
   }
}

