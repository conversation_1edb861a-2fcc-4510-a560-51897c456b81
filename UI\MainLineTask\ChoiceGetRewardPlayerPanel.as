package UI.MainLineTask
{
   import UI.AbleDragSprite;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class ChoiceGetRewardPlayerPanel extends AbleDragSprite
   {
      
      private var m_show:Sprite;
      
      public var btn1:ButtonLogicShell;
      
      public var btn2:ButtonLogicShell;
      
      private var m_headShow1:MovieClipPlayLogicShell;
      
      private var m_headShow2:MovieClipPlayLogicShell;
      
      public function ChoiceGetRewardPlayerPanel()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_show = null;
         ClearUtil.clearObject(btn1);
         btn1 = null;
         ClearUtil.clearObject(btn2);
         btn2 = null;
         ClearUtil.clearObject(m_headShow1);
         m_headShow1 = null;
         ClearUtil.clearObject(m_headShow2);
         m_headShow2 = null;
      }
      
      override protected function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("ChoiceGetRewardPlayerPanel") as Sprite;
         addChild(m_show);
         btn1 = new ButtonLogicShell();
         btn1.setShow(m_show["btn1"]);
         btn2 = new ButtonLogicShell();
         btn2.setShow(m_show["btn2"]);
         m_headShow1 = new MovieClipPlayLogicShell();
         m_headShow1.setShow(m_show["headShow1"]);
         m_headShow2 = new MovieClipPlayLogicShell();
         m_headShow2.setShow(m_show["headShow2"]);
         m_headShow1.gotoAndStop(GamingUI.getInstance().player1.playerVO.playerType);
         if(GamingUI.getInstance().player2)
         {
            m_headShow2.gotoAndStop(GamingUI.getInstance().player2.playerVO.playerType);
         }
      }
   }
}

