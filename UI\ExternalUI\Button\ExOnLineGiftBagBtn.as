package UI.ExternalUI.Button
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.OnLineGiftBag.OnLineGiftBagData;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class ExOnLineGiftBagBtn
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_Btn:ButtonLogicShell;
      
      private var m_onLineGiftBagTimeShow:MovieClipPlayLogicShell;
      
      private var m_onLineGiftBagMinuteShow:MultiPlaceNumLogicShell;
      
      private var m_onLineGiftBagSecondShow:MultiPlaceNumLogicShell;
      
      private var m_isTime:Boolean;
      
      private var m_isRender:Boolean;
      
      public function ExOnLineGiftBagBtn()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_Btn);
         m_Btn = null;
         ClearUtil.clearObject(m_onLineGiftBagTimeShow);
         m_onLineGiftBagTimeShow = null;
         ClearUtil.clearObject(m_onLineGiftBagMinuteShow);
         m_onLineGiftBagMinuteShow = null;
         ClearUtil.clearObject(m_onLineGiftBagSecondShow);
         m_onLineGiftBagSecondShow = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_isTime = true;
         initOneFrame();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function setState() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(m_isRender)
         {
            return;
         }
         m_isRender = true;
         if(OnLineGiftBagData.getInstance().remainTime == 0)
         {
            if(m_isTime)
            {
               initTwoFrame();
               m_isTime = false;
            }
         }
         else if(m_isTime == false)
         {
            initOneFrame();
            m_isTime = true;
         }
         if(m_isTime)
         {
            _loc2_ = Math.ceil(OnLineGiftBagData.getInstance().remainTime / ProgramStartData.getInstance().getOneThousand());
            _loc1_ = _loc2_ / ProgramStartData.getInstance().getSixty();
            _loc2_ -= _loc1_ * ProgramStartData.getInstance().getSixty();
            try
            {
               if(m_onLineGiftBagMinuteShow)
               {
                  m_onLineGiftBagMinuteShow.showNum(_loc1_);
               }
               if(m_onLineGiftBagSecondShow)
               {
                  m_onLineGiftBagSecondShow.showNum(_loc2_);
               }
            }
            catch(error:Error)
            {
               trace(error.message);
            }
         }
         m_isRender = false;
      }
      
      public function getBtn() : ButtonLogicShell
      {
         return m_Btn;
      }
      
      private function clearShells() : void
      {
         ClearUtil.clearObject(m_Btn);
         m_Btn = null;
         ClearUtil.clearObject(m_onLineGiftBagTimeShow);
         m_onLineGiftBagTimeShow = null;
         ClearUtil.clearObject(m_onLineGiftBagMinuteShow);
         m_onLineGiftBagMinuteShow = null;
         ClearUtil.clearObject(m_onLineGiftBagSecondShow);
         m_onLineGiftBagSecondShow = null;
      }
      
      private function initOneFrame() : void
      {
         clearShells();
         m_showMC.gotoAndStop("1");
         m_Btn = new ButtonLogicShell();
         m_Btn.setShow(m_show["show"]);
         m_Btn.setTipString("查看和领取在线礼包");
         m_onLineGiftBagTimeShow = new MovieClipPlayLogicShell();
         m_onLineGiftBagTimeShow.setShow(m_Btn.getShow()["timeShow"]);
         m_onLineGiftBagMinuteShow = new MultiPlaceNumLogicShell();
         m_onLineGiftBagMinuteShow.setShow(m_onLineGiftBagTimeShow.getShow()["minute"]);
         m_onLineGiftBagSecondShow = new MultiPlaceNumLogicShell();
         m_onLineGiftBagSecondShow.setShow(m_onLineGiftBagTimeShow.getShow()["second"]);
         m_onLineGiftBagMinuteShow.setIsShowZero(true);
         m_onLineGiftBagSecondShow.setIsShowZero(true);
      }
      
      private function initTwoFrame() : void
      {
         clearShells();
         m_showMC.gotoAndStop("2");
         m_Btn = new ButtonLogicShell();
         m_Btn.setShow(m_show["show"]);
         m_Btn.setTipString("查看和领取在线礼包");
      }
   }
}

