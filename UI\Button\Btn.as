package UI.Button
{
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyMovieClip;
   import YJFY.Part1;
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.SoundData;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class Btn extends MyMovieClip
   {
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      protected var m_buttonSoundData:SoundData;
      
      public function Btn()
      {
         super();
         buttonMode = true;
         addEventListener("addedToStage",addToStage,false,0,true);
         m_buttonSoundData = new SoundData("buttonSound","buttonSound","NewGameFolder/FirstEnterSource.swf","ButtonSound");
         Part1.getInstance().getSoundManager().addSound2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            if(_loc1_ is Bitmap)
            {
               if((_loc1_ as Bitmap).bitmapData)
               {
                  (_loc1_ as Bitmap).bitmapData.dispose();
               }
               (_loc1_ as Bitmap).bitmapData = null;
            }
         }
         ClearUtil.clearObject(m_smallToolTip);
         m_smallToolTip = null;
         ClearUtil.clearObject(m_buttonSoundData);
         m_buttonSoundData = null;
         super.clear();
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("click",clickBtn2,false,0,true);
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",clickBtn,false,0,true);
         addEventListener("rollOver",over,false,0,true);
         addEventListener("rollOut",out,false,0,true);
         addEventListener("mouseDown",down,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("click",clickBtn2,false);
         removeEventListener("click",clickBtn,false);
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("rollOut",out,false);
         removeEventListener("rollOver",over,false);
         removeEventListener("mouseDown",down,false);
      }
      
      public function setTipString(param1:String) : void
      {
         if(param1 == "农场" || param1 == "炼丹炉" || param1 == "开启所有土地")
         {
            return;
         }
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               m_smallToolTip = new SmallToolTip();
               m_smallToolTip.setFont(new FangZhengKaTongJianTi());
               m_smallToolTip.setTipStr(m_tipString);
               m_smallToolTip.init();
            }
         }
      }
      
      protected function down(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
      }
      
      protected function out(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
      }
      
      protected function over(param1:MouseEvent) : void
      {
         if(m_smallToolTip)
         {
            if(stage)
            {
               stage.addChild(m_smallToolTip);
               if(stage.mouseX + 10 + m_smallToolTip.width > stage.stageWidth)
               {
                  m_smallToolTip.x = stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = stage.mouseX + 10;
               }
               if(stage.mouseY + 10 + m_smallToolTip.height > stage.stageHeight)
               {
                  m_smallToolTip.y = stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = stage.mouseY + 10;
               }
            }
         }
      }
      
      protected function clickBtn2(param1:MouseEvent) : void
      {
         Part1.getInstance().getSoundManager().play2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
      }
      
      protected function clickBtn(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         dispatchEvent(new UIBtnEvent("clickBtn"));
      }
   }
}

