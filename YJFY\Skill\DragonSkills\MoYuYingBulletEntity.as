package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.BulletEntity.BulletEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.CoordinateFunction;
   import YJFY.World.World;
   import YJFY.geom.Area3D.Cuboid;
   
   public class MoYuYingBulletEntity extends BulletEntity
   {
      
      private var m_pushCuboidArea:Cuboid;
      
      private var m_pushCuboidAreaToWorld:Cuboid;
      
      private var m_isAblePushEntity:Boolean;
      
      private var m_pushEntityListener:MoYuYingBulletEntityListener;
      
      public function MoYuYingBulletEntity()
      {
         super();
         m_pushCuboidArea = new Cuboid();
         m_pushCuboidAreaToWorld = new Cuboid();
      }
      
      override protected function fullyClear() : void
      {
         ClearUtil.clearObject(m_pushCuboidArea);
         m_pushCuboidArea = null;
         ClearUtil.clearObject(m_pushCuboidAreaToWorld);
         m_pushCuboidAreaToWorld = null;
         m_pushEntityListener = null;
         super.fullyClear();
      }
      
      public function setIsAblePushEntity(param1:Boolean) : void
      {
         m_isAblePushEntity = param1;
      }
      
      public function setPushEntityListener(param1:MoYuYingBulletEntityListener) : void
      {
         m_pushEntityListener = param1;
      }
      
      override protected function initOtherData() : void
      {
         super.initOtherData();
         m_pushCuboidArea.setTo(m_xml.pushRange[0].@x,m_xml.pushRange[0].@y,m_xml.pushRange[0].@z,m_xml.pushRange[0].@xRange,m_xml.pushRange[0].@yRange,m_xml.pushRange[0].@zRange);
      }
      
      override public function render(param1:World) : void
      {
         refreshWorldPushCuboid();
         pushEnemys();
         super.render(param1);
      }
      
      private function pushEnemys() : void
      {
         var _loc3_:int = 0;
         var _loc2_:IEntity = null;
         var _loc1_:int = int(m_world.getAllEntityNum());
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = m_world.getAllEnttiyByIndex(_loc3_);
            if(_loc2_ != this)
            {
               if(m_world.cuboidRangeIsContainsEntity(m_pushCuboidAreaToWorld,_loc2_))
               {
                  pushEntity2(this,_loc2_);
                  if(m_isAblePushEntity)
                  {
                     if(_loc2_ is AnimalEntity)
                     {
                        (_loc2_ as AnimalEntity).setShowDirection(-getShowDirection());
                        (_loc2_ as AnimalEntity).setNewPosition(getX() + _loc2_.getBodyXRange() / 2 * getShowDirection(),_loc2_.getY(),_loc2_.getZ());
                     }
                  }
               }
            }
            _loc3_++;
         }
      }
      
      private function pushEntity2(param1:MoYuYingBulletEntity, param2:IEntity) : void
      {
         if(m_pushEntityListener)
         {
            m_pushEntityListener.pushEntity(param1,param2);
         }
      }
      
      private function refreshWorldPushCuboid() : void
      {
         CoordinateFunction.cuboidRangeTranToWorld2(getShowDirection(),getX(),getY(),getZ(),m_pushCuboidArea,m_pushCuboidAreaToWorld);
      }
   }
}

