package UI2.TimeUtil
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.ITimePeriodOfOneDay;
   import YJFY.Utils.TimeUtil.ITimePeriodsOfOneDay;
   
   public class TimePeriodsOfOneDay implements ITimePeriodsOfOneDay
   {
      
      private var m_timePeriodsOfOneday:Vector.<TimePeriodOfOneDay>;
      
      public function TimePeriodsOfOneDay()
      {
         super();
         m_timePeriodsOfOneday = new Vector.<TimePeriodOfOneDay>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_timePeriodsOfOneday);
         m_timePeriodsOfOneday = null;
      }
      
      public function addTimePeriodOfOneDay(param1:TimePeriodOfOneDay) : void
      {
         m_timePeriodsOfOneday.push(param1);
      }
      
      public function getNumOfTimePeriod() : uint
      {
         return m_timePeriodsOfOneday.length;
      }
      
      public function getTimePeriodByIndex(param1:int) : ITimePeriodOfOneDay
      {
         return m_timePeriodsOfOneday[param1];
      }
   }
}

