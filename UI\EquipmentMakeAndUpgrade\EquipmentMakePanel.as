package UI.EquipmentMakeAndUpgrade
{
   import UI.Animation.AnimationObject;
   import UI.Animation.M2B;
   import UI.Button.NumberBtn.NumberBtnGroup;
   import UI.Button.NumberBtnEx.NumberBtnGroupEx;
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Effects.Effects_Fail_Make;
   import UI.Effects.Effects_Success_Make;
   import UI.EquipmentCells.NpcEquipmentCell;
   import UI.EquipmentMakeAndUpgrade.Button.MakeBtn;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Protect.ProtectData;
   import UI2.broadcast.SubmitFunction;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipmentMakePanel extends MySprite implements IEquipmentProcessPanel
   {
      
      public var scrollCell:NpcEquipmentCell;
      
      public var sayText:TextField;
      
      public var sayText2:TextField;
      
      public var sayText3:TextField;
      
      public var requiredMoneyText:TextField;
      
      public var makeBtn:MakeBtn;
      
      public var successRateText:TextField;
      
      public var makeFurnace:Sprite;
      
      private var _numBtnGrounp:NumberBtnGroup;
      
      private var _numEquipGrounp:NumberBtnGroupEx;
      
      private const WIDTH_NUM:int = 2;
      
      private const HEIGHT_NUM:int = 2;
      
      private const START_X:Number = 191;
      
      private const START_Y:Number = 65;
      
      private const SPACE_WIDTH:Number = 169;
      
      private const SPACE_HEIGHT:Number = 100;
      
      private const EQ_X:Number = 275;
      
      private const EQ_Y:Number = 235;
      
      private const EQ_SCALE_X:Number = 1.5;
      
      private const EQ_SCALE_Y:Number = 1.5;
      
      private const LUCK_STONE_X:Number = 65;
      
      private const LUCK_STONE_Y:Number = 166;
      
      private const WARNING_BOX_X:Number = 120;
      
      private const WARNING_BOX_Y:Number = 70;
      
      private const LACK_FAIL_STRING:String = "材料或金钱不足，不能合成！";
      
      private const FAIL_STRING:String = "合成失败！";
      
      private const FAIL_STRING_VIP:String = "vip返回卷轴,合成失败";
      
      private const SUCCESS_MAKE:String = "制作成功！";
      
      private var _isAbleCompose:Boolean = false;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _player:Player;
      
      private var _successRate:Number;
      
      private var _baseSuccessRate:Number;
      
      private var _luckStone:LuckStoneShow;
      
      private var _compositeEquipmentVO:EquipmentVO;
      
      private var _compositeEquipment:Equipment;
      
      private var _materialVOs:Vector.<EquipmentVO>;
      
      private var _materials:Vector.<Equipment>;
      
      private var _numOfNumBtnGroup:uint;
      
      public function EquipmentMakePanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function init() : void
      {
         _luckStone = new LuckStoneShow();
         _luckStone.x = 65;
         _luckStone.y = 166;
         addChild(_luckStone);
         _luckStone.visible = false;
         _isAbleCompose = false;
         _numBtnGrounp = new NumberBtnGroup();
         _numBtnGrounp.x = 35;
         _numBtnGrounp.y = 190;
         addChild(_numBtnGrounp);
         _numEquipGrounp = new NumberBtnGroupEx();
         _numEquipGrounp.x = 30;
         _numEquipGrounp.y = 77;
         addChild(_numEquipGrounp);
         switchToNothing();
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         sayText.defaultTextFormat = new TextFormat(_loc1_.fontName,13,16776960);
         sayText2.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16776960);
         sayText3.defaultTextFormat = new TextFormat(_loc1_.fontName,16,16776960);
         requiredMoneyText.defaultTextFormat = new TextFormat(_loc1_.fontName,15,16776960);
         successRateText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16777215);
         sayText.embedFonts = true;
         sayText2.embedFonts = true;
         sayText3.embedFonts = true;
         requiredMoneyText.embedFonts = true;
         successRateText.embedFonts = true;
         sayText.text = "请点击卷轴放入";
      }
      
      public function putInEquipmentVO(param1:EquipmentVO, param2:Player) : void
      {
         var _loc3_:BuyMaterialGuideBtn = null;
         var _loc4_:* = 0;
         if(param1.equipmentType == "scroll")
         {
            _equipmentVO = param1;
            _player = param2;
            switchToHave();
            scrollCell.addEquipmentVO(_equipmentVO);
            scrollCell.addEventListener("rollOver",equipmentInfor,false,0,true);
            scrollCell.addEventListener("rollOut",equipmentInfor,false,0,true);
            _baseSuccessRate = (_equipmentVO as ScrollEquipmentVO).successRate;
            _successRate = _baseSuccessRate;
            successRateText.text = "成功率\n" + Math.ceil(_successRate * 100).toString() + "%";
            requiredMoneyText.text = (_equipmentVO as ScrollEquipmentVO).requiredMoney.toString();
            _compositeEquipmentVO = (_equipmentVO as ScrollEquipmentVO).compositeEquipmentVO.clone();
            _compositeEquipmentVO.isBinding = _equipmentVO.isBinding;
            _compositeEquipment = MyFunction2.sheatheEquipmentShell(_compositeEquipmentVO);
            _compositeEquipment.x = 275;
            _compositeEquipment.y = 235;
            _compositeEquipment.scaleX = 1.5;
            _compositeEquipment.scaleY = 1.5;
            addChildAt(_compositeEquipment,numChildren);
            arrangeRequiredEquipment(_equipmentVO,param2);
            setMaxNum(_player);
            setMaxEquipNum(_player);
            _compositeEquipment.addEventListener("rollOver",equipmentInfor,false,0,true);
            _compositeEquipment.addEventListener("rollOut",equipmentInfor,false,0,true);
            _loc3_ = new BuyMaterialGuideBtn(10500000,refreshPanel,1);
            _loc3_.x = 38;
            _loc3_.y = 225;
            addChild(_loc3_);
            _loc4_ = Math.ceil((1 - _successRate) / 0.1);
            changeSuccessRate2(_loc4_);
            _numEquipGrounp.num = 1;
         }
      }
      
      public function refreshPanel() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc3_:int = 0;
         if(_equipmentVO == null)
         {
            return;
         }
         _successRate = 0;
         _numBtnGrounp.restore();
         makeFurnace.visible = true;
         while(numChildren > 13)
         {
            _loc2_ = getChildAt(numChildren - 1);
            removeChildAt(numChildren - 1);
            if(!(_loc2_ is Equipment) && _loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         switchToNothing();
         if(_materialVOs)
         {
            _materialVOs = null;
         }
         if(_materials)
         {
            ClearUtil.nullArr(_materials);
         }
         switchToHave();
         _baseSuccessRate = (_equipmentVO as ScrollEquipmentVO).successRate;
         _successRate = _baseSuccessRate;
         successRateText.text = "成功率\n" + Math.ceil(_successRate * 100).toString() + "%";
         _loc3_ = 1;
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num > 1 && _equipmentVO && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            _loc3_ = _numEquipGrounp.num;
         }
         requiredMoneyText.text = ((_equipmentVO as ScrollEquipmentVO).requiredMoney * _loc3_).toString();
         arrangeRequiredEquipment(_equipmentVO,_player);
         _compositeEquipment.x = 275;
         _compositeEquipment.y = 235;
         (_compositeEquipment as Sprite).scaleX = 1.5;
         (_compositeEquipment as Sprite).scaleY = 1.5;
         addChildAt(_compositeEquipment as DisplayObject,numChildren);
         setMaxNum(_player);
         var _loc1_:BuyMaterialGuideBtn = new BuyMaterialGuideBtn(10500000,refreshPanel,1);
         _loc1_.x = 38;
         _loc1_.y = 225;
         addChildAt(_loc1_,numChildren);
         changeSuccessRate2(_numOfNumBtnGroup);
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         var _loc4_:int = 0;
         var _loc6_:DisplayObject = null;
         var _loc7_:int = 0;
         var _loc2_:EquipmentVO = null;
         _player = null;
         var _loc3_:EquipmentVO = _equipmentVO ? _equipmentVO : null;
         _equipmentVO = null;
         _successRate = 0;
         _numBtnGrounp.restore();
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num >= 1 && _loc3_ && _loc3_.className.indexOf("Scroll_InsetGem") != -1)
         {
            _loc4_ = _numEquipGrounp.num;
         }
         _numEquipGrounp.restore();
         makeFurnace.visible = true;
         while(numChildren > 13)
         {
            _loc6_ = getChildAt(numChildren - 1);
            removeChildAt(numChildren - 1);
            if(!(_loc6_ is Equipment) && _loc6_.hasOwnProperty("clear"))
            {
               _loc6_["clear"]();
            }
         }
         switchToNothing();
         scrollCell.removeEquipmentVO();
         if(_materialVOs)
         {
            _materialVOs = null;
         }
         if(_materials)
         {
            ClearUtil.nullArr(_materials);
         }
         _compositeEquipmentVO = null;
         if(_compositeEquipment)
         {
            _compositeEquipment.clear();
         }
         _compositeEquipment = null;
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
         var _loc5_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(_loc3_)
         {
            _loc5_.push(_loc3_);
            _loc7_ = 1;
            while(_loc7_ < _loc4_)
            {
               _loc2_ = _loc3_.clone();
               if(_loc2_)
               {
                  _loc5_.push(_loc2_);
               }
               _loc7_++;
            }
         }
         return _loc5_;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(!(_loc1_ is Equipment) && _loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(scrollCell)
         {
            scrollCell.clear();
         }
         scrollCell = null;
         sayText = null;
         sayText2 = null;
         sayText3 = null;
         requiredMoneyText = null;
         if(makeBtn)
         {
            makeBtn.clear();
         }
         makeBtn = null;
         successRateText = null;
         makeFurnace = null;
         if(_numBtnGrounp)
         {
            _numBtnGrounp.clear();
         }
         _numBtnGrounp = null;
         if(_numEquipGrounp)
         {
            _numEquipGrounp.clear();
         }
         _numEquipGrounp = null;
         _equipmentVO = null;
         _player = null;
         _luckStone = null;
         _compositeEquipmentVO = null;
         _materialVOs = null;
         _materials = null;
      }
      
      private function switchToNothing() : void
      {
         sayText.visible = true;
         sayText2.visible = false;
         sayText3.visible = false;
         if(_numBtnGrounp)
         {
            _numBtnGrounp.visible = false;
         }
         if(_numEquipGrounp)
         {
            _numEquipGrounp.visible = false;
         }
         requiredMoneyText.text = "";
         successRateText.visible = false;
         _luckStone.visible = false;
      }
      
      private function switchToHave() : void
      {
         sayText.visible = false;
         sayText2.visible = true;
         sayText3.visible = true;
         _numBtnGrounp.visible = true;
         if(_equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            _numEquipGrounp.visible = true;
         }
         successRateText.visible = true;
         _luckStone.visible = true;
      }
      
      public function setMaxNum(param1:Player) : void
      {
         var equipVO:EquipmentVO;
         var player:Player = param1;
         var maxNum:int = 0;
         _numBtnGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":param1,
               "flag":0
            }));
         },["背包中的幸运石为0个, 没有更多了！"]);
         for each(equipVO in player.playerVO.packageEquipmentVOs)
         {
            if(equipVO)
            {
               if(equipVO.className == "LuckStone")
               {
                  maxNum += (equipVO as StackEquipmentVO).num;
                  _numBtnGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":param1,
                        "flag":0
                     }));
                  },["背包中的幸运石为" + maxNum + "个, 没有更多了！"]);
               }
            }
         }
      }
      
      public function setMaxEquipNum(param1:Player) : void
      {
         var equipVO:EquipmentVO;
         var player:Player = param1;
         var maxNum:int = 0;
         _numEquipGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":param1,
               "flag":0
            }));
         },["背包中的个数为0个, 没有更多了！"]);
         for each(equipVO in player.playerVO.packageEquipmentVOs)
         {
            if(equipVO)
            {
               if(_equipmentVO.className.indexOf("Scroll_InsetGem") != -1 && equipVO.className == _equipmentVO.className)
               {
                  maxNum += 1;
                  _numEquipGrounp.setMaxNumAndFun(maxNum,function(param1:String):void
                  {
                     dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":param1,
                        "flag":0
                     }));
                  },["背包中的个数为" + maxNum + "个, 没有更多了！"]);
               }
            }
         }
      }
      
      private function arrangeRequiredEquipment(param1:EquipmentVO, param2:Player) : void
      {
         var _loc17_:int = 0;
         var _loc7_:Equipment = null;
         var _loc9_:int = 0;
         var _loc8_:int = 0;
         var _loc12_:TextField = null;
         var _loc13_:int = 0;
         var _loc3_:BuyMaterialGuideBtn = null;
         var _loc16_:* = false;
         var _loc11_:TextFormat = null;
         var _loc6_:TextFormat = null;
         var _loc10_:BuyGoldPocketGuideBtn = null;
         var _loc4_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         _isAbleCompose = true;
         _materialVOs = (param1 as ScrollEquipmentVO).materialVOs;
         _materials = new Vector.<Equipment>(_materialVOs.length);
         var _loc14_:Vector.<int> = (param1 as ScrollEquipmentVO).requiredNums;
         var _loc15_:int = int(_materialVOs.length);
         _loc9_ = 1;
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num > 1 && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            _loc9_ *= _numEquipGrounp.num;
         }
         _loc8_ = 0;
         while(_loc8_ < _loc15_)
         {
            _loc7_ = MyFunction2.sheatheEquipmentShell(_materialVOs[_loc8_]);
            _loc7_.addEventListener("rollOver",equipmentInfor,false,0,true);
            _loc7_.addEventListener("rollOut",equipmentInfor,false,0,true);
            _materials[_loc8_] = _loc7_;
            _materials[_loc8_].x = 191 + _loc8_ % 2 * 169;
            _materials[_loc8_].y = 65 + int(_loc8_ / 2) * 100;
            _loc12_ = new TextField();
            _loc12_.embedFonts = true;
            _loc12_.selectable = false;
            _loc12_.mouseEnabled = false;
            _loc13_ = _loc14_[_loc8_] * _loc9_;
            _loc17_ = 0;
            for each(var _loc5_ in param2.playerVO.packageEquipmentVOs)
            {
               if(_loc5_)
               {
                  if(_loc5_.id == _materialVOs[_loc8_].id)
                  {
                     if(_loc5_ is StackEquipmentVO)
                     {
                        _loc17_ += (_loc5_ as StackEquipmentVO).num;
                     }
                     else
                     {
                        _loc17_++;
                     }
                     _compositeEquipmentVO.isBinding ||= _loc5_.isBinding;
                  }
               }
            }
            _isAbleCompose &&= _loc17_ >= _loc13_;
            if(_loc17_ >= _loc13_)
            {
               _loc12_.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#00ff33\'>" + _loc17_.toString() + "/" + _loc13_.toString() + "</font>";
            }
            else
            {
               _loc12_.htmlText = "<font face=\'" + new FangZhengKaTongJianTi().fontName + "\' size=\'15\' color=\'#00ff33\'>" + "<font color=\'#ff0000\'>" + _loc17_.toString() + "</font>" + "/" + _loc13_.toString() + "</font>";
               if(_materialVOs[_loc8_].ticketPrice)
               {
                  _loc3_ = new BuyMaterialGuideBtn(_materialVOs[_loc8_].id,refreshPanel,1);
                  _loc3_.x = _loc7_.x - _loc7_.width / 2;
                  _loc3_.y = _loc7_.y + _loc7_.height / 2 + 3;
                  addChildAt(_loc3_,numChildren);
               }
            }
            _loc12_.width = _loc12_.textWidth + 5;
            _loc12_.height = _loc12_.textHeight + 5;
            _loc12_.x = _loc7_.x + _loc7_.width / 2 - _loc12_.textWidth;
            _loc12_.y = _loc7_.y + _loc7_.height / 2 - _loc12_.textHeight;
            addChildAt(_loc7_,numChildren);
            addChildAt(_loc12_,numChildren);
            _loc12_.filters = [filter()];
            _loc16_ = (param1 as ScrollEquipmentVO).requiredMoney <= param2.playerVO.money;
            if(_loc16_)
            {
               _loc11_ = new TextFormat(_loc4_.fontName,15,16776960);
               requiredMoneyText.setTextFormat(_loc11_);
               requiredMoneyText.width = requiredMoneyText.textWidth + 15;
            }
            else
            {
               _loc6_ = new TextFormat(_loc4_.fontName,15,16711680);
               requiredMoneyText.setTextFormat(_loc6_);
               requiredMoneyText.width = requiredMoneyText.textWidth + 15;
               _loc10_ = new BuyGoldPocketGuideBtn(11100000,refreshPanel,1);
               _loc10_.x = requiredMoneyText.x + requiredMoneyText.textWidth + 5;
               _loc10_.y = 286;
               addChildAt(_loc10_,numChildren);
            }
            _isAbleCompose &&= _loc16_;
            _loc8_++;
         }
      }
      
      private function deleEquipmentVOs(param1:EquipmentVO, param2:Player) : void
      {
         var _loc3_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:Vector.<EquipmentVO> = (param1 as ScrollEquipmentVO).materialVOs;
         var _loc5_:Vector.<int> = (param1 as ScrollEquipmentVO).requiredNums;
         var _loc6_:int = int(_loc7_.length);
         _loc3_ = 1;
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num > 1 && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            _loc3_ *= _numEquipGrounp.num;
         }
         _loc8_ = 0;
         while(_loc8_ < _loc6_)
         {
            _loc4_ = _loc5_[_loc8_] * _loc3_;
            MyFunction.getInstance().minusEquipmentVOs(param2.playerVO.packageEquipmentVOs,_loc4_,_loc7_[_loc8_].id);
            _loc8_++;
         }
         MyFunction.getInstance().minusEquipmentVOs(param2.playerVO.packageEquipmentVOs,_numBtnGrounp.num,10500000);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         makeBtn.addEventListener("clickMakeEquipmentBtn",make,false,0,true);
         addEventListener("changeNum",changeSuccessRate,true,0,true);
         addEventListener("changeNumDown",changeSourceEquipDown,true,0,true);
         addEventListener("changeNumUp",changeSourceEquipUp,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         makeBtn.removeEventListener("clickMakeEquipmentBtn",make);
         removeEventListener("changeNumDown",changeSourceEquipDown,true);
         removeEventListener("changeNumUp",changeSourceEquipUp,true);
      }
      
      private function make(param1:UIBtnEvent) : void
      {
         var n:Number;
         var numOfSourceEq:int;
         var compositeEquipmentVO:EquipmentVO;
         var e:UIBtnEvent = param1;
         if(scrollCell.isHaveChild)
         {
            n = Math.random();
            if(_isAbleCompose)
            {
               numOfSourceEq = 1;
               if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num > 1 && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
               {
                  numOfSourceEq *= _numEquipGrounp.num;
               }
               compositeEquipmentVO = _compositeEquipmentVO;
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"打造过程中请勿刷新！",
                  "flag":1 | 2,
                  "task":{
                     "type":"EquipmentMake",
                     "okFunction":function():void
                     {
                        MyFunction2.getUserStateIsRightFunction(function():void
                        {
                           var _loc5_:Boolean = false;
                           var _loc1_:int = 0;
                           var _loc3_:SaveTaskInfo = null;
                           var _loc4_:int = 0;
                           var _loc2_:SaveTaskInfo = null;
                           if(n <= _successRate)
                           {
                              _loc1_ = 1;
                              while(_loc1_ <= numOfSourceEq)
                              {
                                 if(_numEquipGrounp && _numEquipGrounp.visible == true && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
                                 {
                                    _equipmentVO.isPutInOperate = false;
                                    MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,1,_equipmentVO.id);
                                    _loc5_ = MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,compositeEquipmentVO,0);
                                 }
                                 else
                                 {
                                    _loc5_ = MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,compositeEquipmentVO,_equipmentVO);
                                 }
                                 if(_loc5_)
                                 {
                                    if(compositeEquipmentVO is PreciousEquipmentVO)
                                    {
                                       SubmitFunction.getInstance().setData1(8,(compositeEquipmentVO as PreciousEquipmentVO).implictProPlayerId,compositeEquipmentVO.name);
                                    }
                                    else if(compositeEquipmentVO is AbleEquipmentVO)
                                    {
                                       SubmitFunction.getInstance().setData1(10,(compositeEquipmentVO as AbleEquipmentVO).implictProPlayerId,compositeEquipmentVO.name);
                                    }
                                 }
                                 _loc1_++;
                              }
                              _player.playerVO.money -= (_equipmentVO as ScrollEquipmentVO).requiredMoney * numOfSourceEq;
                              deleEquipmentVOs(_equipmentVO,_player);
                              scrollCell.removeEquipmentVO();
                              _equipmentVO = null;
                              GamingUI.getInstance().addMainLineTaskGoalGameEventStr("EquipmentMake");
                              _loc3_ = new SaveTaskInfo();
                              _loc3_.type = "4399";
                              _loc3_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc3_);
                              MyFunction2.saveGame2();
                              resultSuccessAnimation();
                              materialsAnimation();
                           }
                           else
                           {
                              _equipmentVO.isPutInOperate = false;
                              _player.playerVO.money -= (_equipmentVO as ScrollEquipmentVO).requiredMoney * numOfSourceEq;
                              deleEquipmentVOs(_equipmentVO,_player);
                              if(_player.vipVO.isLostEquipmentInMake && ProtectData.getInstance().isLostEquipmentInMake)
                              {
                                 _loc4_ = 1;
                                 while(_loc4_ <= numOfSourceEq)
                                 {
                                    MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,1,_equipmentVO.id);
                                    _loc4_++;
                                 }
                                 scrollCell.removeEquipmentVO();
                                 _equipmentVO = null;
                              }
                              _loc2_ = new SaveTaskInfo();
                              _loc2_.type = "4399";
                              _loc2_.isHaveData = false;
                              SaveTaskList.getInstance().addData(_loc2_);
                              MyFunction2.saveGame2();
                              resultFailAnimation();
                              materialsAnimation();
                           }
                        },function(param1:String, param2:int):void
                        {
                           dispatchEvent(new UIPassiveEvent("showWarningBox",{
                              "text":param1,
                              "flag":param2
                           }));
                        },true);
                     }
                  }
               }));
            }
            else
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"材料或金钱不足，不能合成！",
                  "flag":1
               }));
            }
         }
      }
      
      private function changeSuccessRate(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 1;
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _numEquipGrounp.num > 1 && _equipmentVO && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            _loc2_ = _numEquipGrounp.num;
         }
         _successRate = _baseSuccessRate + int(_numBtnGrounp.num / _loc2_) * 0.1;
         if(_successRate > 1)
         {
            _successRate = 1;
         }
         if(_successRate < 0)
         {
            _successRate = 0;
         }
         successRateText.text = "成功率\n" + Math.ceil(_successRate * 100).toString() + "%";
         _numOfNumBtnGroup = _numBtnGrounp.num;
      }
      
      private function changeSourceEquipDown(param1:UIPassiveEvent) : void
      {
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            changeSuccessRate(null);
            requiredMoneyText.text = ((_equipmentVO as ScrollEquipmentVO).requiredMoney * _numEquipGrounp.num).toString();
            arrangeRequiredEquipment(_equipmentVO,_player);
            dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
         }
      }
      
      private function changeSourceEquipUp(param1:UIPassiveEvent) : void
      {
         if(_numEquipGrounp && _numEquipGrounp.visible == true && _equipmentVO.className.indexOf("Scroll_InsetGem") != -1)
         {
            changeSuccessRate(null);
            requiredMoneyText.text = ((_equipmentVO as ScrollEquipmentVO).requiredMoney * _numEquipGrounp.num).toString();
            arrangeRequiredEquipment(_equipmentVO,_player);
            dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
         }
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGrounp.num = param1;
         changeSuccessRate(null);
      }
      
      private function resultSuccessAnimation() : void
      {
         parent.parent.mouseChildren = false;
         parent.parent.mouseEnabled = false;
         makeFurnace.visible = false;
         var _loc1_:Effects_Success_Make = new Effects_Success_Make();
         var _loc2_:AnimationObject = new AnimationObject();
         _loc2_.x = makeFurnace.x;
         _loc2_.y = makeFurnace.y;
         addChildAt(_loc2_,numChildren);
         _loc2_.imgList = M2B.transformM2B(_loc1_,false);
         _loc2_.repeatCount = 1;
         _loc2_.delay = 60;
         _loc2_.play();
         _loc2_.addEventListener("play over event",playOver,false,0,true);
      }
      
      private function materialsAnimation() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(_materials)
         {
            _loc1_ = int(_materials.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(_materials[_loc2_])
               {
                  TweenLite.to(_materials[_loc2_],2,{
                     "alpha":0,
                     "x":275,
                     "y":126,
                     "ease":Back.easeIn
                  });
               }
               _loc2_++;
            }
         }
      }
      
      private function resultFailAnimation() : void
      {
         parent.parent.mouseChildren = false;
         parent.parent.mouseEnabled = false;
         makeFurnace.visible = false;
         var _loc1_:Effects_Fail_Make = new Effects_Fail_Make();
         var _loc2_:AnimationObject = new AnimationObject();
         _loc2_.x = makeFurnace.x;
         _loc2_.y = makeFurnace.y;
         addChildAt(_loc2_,numChildren);
         _loc2_.imgList = M2B.transformM2B(_loc1_,false);
         _loc2_.repeatCount = 1;
         _loc2_.delay = 60;
         _loc2_.play();
         _loc2_.addEventListener("play over event",playerOver2,false,0,true);
      }
      
      private function resultAnimation() : void
      {
         makeFurnace.visible = true;
         if(_compositeEquipment)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"制作成功！",
               "flag":1
            }));
            TweenLite.to(_compositeEquipment,2,{
               "alpha":0.5,
               "x":600,
               "y":100,
               "ease":Back.easeIn,
               "onComplete":onFinishTween
            });
         }
      }
      
      private function playOver(param1:Event) : void
      {
         resultAnimation();
      }
      
      private function playerOver2(param1:Event) : void
      {
         var _loc2_:String = null;
         if(_player.vipVO.isLostEquipmentInMake)
         {
            _loc2_ = "合成失败！";
         }
         else
         {
            _loc2_ = "vip返回卷轴,合成失败";
         }
         putOutEquipmentVO();
         _player = null;
         dispatchEvent(new UIPassiveEvent("showWarningBox",{
            "text":_loc2_,
            "flag":1
         }));
         dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
      }
      
      private function onFinishTween() : void
      {
         try
         {
            putOutEquipmentVO();
            _player = null;
            dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
         }
         catch(error:ArgumentError)
         {
            trace(error.message);
         }
         parent.parent.mouseChildren = true;
         parent.parent.mouseEnabled = true;
      }
      
      private function filter() : GlowFilter
      {
         return new GlowFilter(0,1,2,2,10,3);
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               if(param1.target == _compositeEquipment)
               {
                  dispatchEvent(new UIPassiveEvent("showMessageBox",{
                     "equipment":_compositeEquipment,
                     "messageBoxMode":3
                  }));
               }
               else if(param1.target is NpcEquipmentCell)
               {
                  dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":(param1.target as NpcEquipmentCell).equipment}));
               }
               else
               {
                  dispatchEvent(new UIPassiveEvent("showMessageBox",{
                     "equipment":param1.target,
                     "messageBoxMode":3
                  }));
               }
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

