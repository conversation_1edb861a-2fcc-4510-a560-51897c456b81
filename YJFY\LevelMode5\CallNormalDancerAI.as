package YJFY.LevelMode5
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.EntityAI.AutomationAI.SearchAttackTargetAI;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class CallNormalDancerAI
   {
      
      private var m_callNormalDancer:CallNormalDancer;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      private var m_startX:int;
      
      private var m_startY:int;
      
      private var m_searchAttackTargetAI:SearchAttackTargetAI;
      
      private var m_callNormalDancerMoveToAI:CallNormalDancerMoveToAI;
      
      private var m_callNormalDancerAttackAI:CallNormalDancerAttackAI;
      
      private var m_nState:int = 1;
      
      public function CallNormalDancerAI()
      {
         super();
         m_nState = 1;
         m_callNormalDancerMoveToAI = new CallNormalDancerMoveToAI();
         m_callNormalDancerAttackAI = new CallNormalDancerAttackAI();
         m_searchAttackTargetAI = new SearchAttackTargetAI();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_callNormalDancerMoveToAI);
         m_callNormalDancerMoveToAI = null;
         ClearUtil.clearObject(m_callNormalDancerAttackAI);
         m_callNormalDancerAttackAI = null;
         ClearUtil.clearObject(m_searchAttackTargetAI);
         m_searchAttackTargetAI = null;
      }
      
      public function init(param1:IAnimalEntity, param2:World, param3:CallNormalDancer) : void
      {
         m_owner = param1 as IAnimalEntity;
         m_world = param2;
         m_callNormalDancer = param3;
         m_callNormalDancerMoveToAI.init(m_owner,m_world,m_callNormalDancer);
         m_searchAttackTargetAI.init(m_owner,m_world,param3,param3.getRebuildAllFoeInterval());
         m_callNormalDancerAttackAI.init(m_owner,m_world,param3.getUnableAttackMinInterval(),param3.getUnableAttackMaxInterval(),m_searchAttackTargetAI);
      }
      
      public function render() : void
      {
         updateState();
         if(NormalDancer.m_isStop == false)
         {
            m_nState = 2;
            m_callNormalDancerMoveToAI.render();
         }
         else
         {
            m_nState = 1;
         }
         m_callNormalDancerAttackAI.render();
         m_searchAttackTargetAI.render();
      }
      
      private function updateState() : void
      {
         if(m_nState == 2)
         {
            if(!m_world.isStop() && m_callNormalDancer.getAnimalEntity().isInAttack() == false && m_callNormalDancer.getAnimalEntity().isInDie() == false && m_callNormalDancer.getAnimalEntity().isInHurt() == false && m_callNormalDancer.getAnimalEntity().isInSkill() == false && m_callNormalDancer.getAnimalEntity().isinWalk() == false)
            {
               m_callNormalDancer.getAnimalEntity().walk();
            }
         }
         else if(!m_world.isStop() && m_callNormalDancer.getAnimalEntity().isInAttack() == false && m_callNormalDancer.getAnimalEntity().isInDie() == false && m_callNormalDancer.getAnimalEntity().isInHurt() == false && m_callNormalDancer.getAnimalEntity().isInSkill() == false && m_callNormalDancer.getAnimalEntity().isInIdle() == false)
         {
            m_callNormalDancer.getAnimalEntity().idle();
         }
      }
      
      public function resetPos() : void
      {
         m_callNormalDancer.getAnimalEntity().setNewPosition(m_startX,m_startY,m_callNormalDancer.getAnimalEntity().getZ());
      }
   }
}

