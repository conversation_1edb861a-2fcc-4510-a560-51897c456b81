package YJFY.PKMode.PKLogic
{
   import YJFY.Entity.AttackData;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.Utils.ClearUtil;
   
   public class BeAttackDataOfOneStepData
   {
      
      public var testIndex:int;
      
      private var m_attackData:AttackData;
      
      private var m_beAttackEntity:IEntity;
      
      public function BeAttackDataOfOneStepData(param1:IEntity, param2:AttackData)
      {
         super();
         m_beAttackEntity = param1;
         m_attackData = param2;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_attackData);
         m_attackData = null;
         m_beAttackEntity = null;
      }
      
      public function getBeAttackEntity() : IEntity
      {
         return m_beAttackEntity;
      }
      
      public function getAttackData() : AttackData
      {
         return m_attackData;
      }
   }
}

