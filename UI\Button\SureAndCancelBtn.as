package UI.Button
{
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class SureAndCancelBtn extends Btn
   {
      
      public static const OK:int = 1;
      
      public static const CANCEL:int = 2;
      
      private var _detail:int;
      
      private var _text:TextField;
      
      public function SureAndCancelBtn()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         _text = null;
      }
      
      private function init() : void
      {
         _text = new TextField();
         _text.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,0);
         _text.embedFonts = true;
         _text.x = -15;
         _text.y = 3.5;
         _text.selectable = false;
         _text.mouseEnabled = false;
         _text.autoSize = "center";
         addChild(_text);
      }
      
      public function showText(param1:String) : void
      {
         _text.text = param1;
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickSureAndCancelBtn",_detail));
      }
      
      public function get detail() : int
      {
         return _detail;
      }
      
      public function set detail(param1:int) : void
      {
         _detail = param1;
      }
   }
}

