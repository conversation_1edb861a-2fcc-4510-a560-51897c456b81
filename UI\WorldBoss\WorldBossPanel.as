package UI.WorldBoss
{
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.SoundManager.SoundManager;
   import UI.VersionControl;
   import UI.WarningBox.WarningBoxSingle;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AttackAnimationData;
   import UI.WorldBoss.AnimationQueueData.IPlayAnimationDataListener;
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Boss.BossData;
   import UI.WorldBoss.Boss.BossFactory;
   import UI.WorldBoss.GetNextStepActionEntities.GetNextStepActionEntitiesFactory;
   import UI.WorldBoss.Utils.GetWorldBossData;
   import UI.WorldBoss.Utils.WorldBossFun;
   import UI.WorldBoss.Utils.WorldBossNeedSomeData;
   import UI.XMLSingle;
   import UI2.broadcast.SubmitFunction;
   import YJFY.GameData;
   import YJFY.LoadUI2;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.system.System;
   import flash.text.TextField;
   
   public class WorldBossPanel extends MySprite implements IPlayAnimationDataListener
   {
      
      private var m_worldBossData:WorldBossData;
      
      private var m_worldBossStartPanel:WorlBossStartPanel;
      
      private var m_versionControl:VersionControl;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_worldBossStageShow:Sprite;
      
      private var m_view:View;
      
      private var m_bossData:BossData;
      
      private var m_worldBoss:WorldBoss;
      
      private var m_worldXML:XML;
      
      private var m_gameOverPanel:AnimationShowPlayLogicShell;
      
      private var m_bossLostVolumeText:TextField;
      
      private var m_totalBossLostVolumeText:TextField;
      
      private var m_returnBtn:ButtonLogicShell2;
      
      private var m_nowOverBtn:ButtonLogicShell2;
      
      private var m_soundManager:SoundManager;
      
      private var m_bossXML:XML;
      
      private var m_rankXML:XML;
      
      private var m_currentBossId:String;
      
      private var m_currentTimeStr:String;
      
      private var m_effectiveTimeStr:String;
      
      private var m_currentTimeInCycle:Number;
      
      private var m_currentCycleNum:int;
      
      private var m_rankId:int;
      
      private var m_currentRewardVOs:Vector.<EquipmentVO>;
      
      private var m_bossIds:Array;
      
      private var m_gamingUI:GamingUI;
      
      private var m_roundNumForNowOverBtn:int;
      
      private var m_loadUI:LoadUI2;
      
      public function WorldBossPanel()
      {
         super();
         m_bossIds = ["boss1","boss2",null];
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         super.clear();
         ClearUtil.clearObject(m_worldBossData);
         m_worldBossData = null;
         ClearUtil.clearObject(m_worldBossStartPanel);
         m_worldBossStartPanel = null;
         m_versionControl = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearDisplayObjectInContainer(m_worldBossStageShow);
         m_worldBossStageShow = null;
         ClearUtil.clearObject(m_bossData);
         m_bossData = null;
         ClearUtil.clearObject(m_worldBoss);
         m_worldBoss = null;
         System.disposeXML(m_worldXML);
         m_worldXML = null;
         ClearUtil.clearObject(m_gameOverPanel);
         m_gameOverPanel = null;
         m_bossLostVolumeText = null;
         ClearUtil.clearObject(m_returnBtn);
         m_returnBtn = null;
         ClearUtil.clearObject(m_nowOverBtn);
         m_nowOverBtn = null;
         System.disposeXML(m_bossXML);
         m_bossXML = null;
         System.disposeXML(m_rankXML);
         m_rankXML = null;
         ClearUtil.nullArr(m_currentRewardVOs);
         m_currentRewardVOs = null;
         ClearUtil.nullArr(m_bossIds);
         m_bossIds = null;
         m_gamingUI = null;
         m_totalBossLostVolumeText = null;
         ClearUtil.clearObject(m_view);
         m_view = null;
         ClearUtil.clearObject(m_soundManager);
         m_soundManager = null;
         m_loadUI = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function isCanStartFight(param1:IsCanFightReturn = null) : Boolean
      {
         if(Boolean(m_currentBossId) == false || m_bossXML == null)
         {
            if(param1)
            {
               param1.isCanFight = false;
               param1.message = "error1";
            }
            return false;
         }
         var _loc3_:String = getAbleFightBossId();
         if(_loc3_ != m_currentBossId)
         {
            if(param1)
            {
               param1.isCanFight = false;
               param1.message = "现在不能进攻该Boss！";
            }
            return false;
         }
         var _loc2_:int = int(m_bossXML.@lostPowerValue);
         if(m_worldBossData.getPowerValue() < _loc2_)
         {
            showWarningBox("体力值不足！",0);
            if(param1)
            {
               param1.isCanFight = false;
               param1.message = "体力值不足";
            }
            return false;
         }
         if(param1)
         {
            param1.isCanFight = true;
            param1.message = "";
         }
         return true;
      }
      
      public function startFight(param1:PlayerEntity, param2:PetEntity) : void
      {
         var myThis:WorldBossPanel;
         var playerEntity:PlayerEntity = param1;
         var petEntity:PetEntity = param2;
         m_nowOverBtn.getShow().visible = false;
         myThis = this;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            m_currentTimeStr = param1;
            reSetBossDataByTime(m_currentTimeStr);
            if(Boolean(m_currentBossId) == false || m_bossXML == null)
            {
               return;
            }
            var _loc4_:String = String(m_bossXML.@xmlPath);
            var _loc8_:String = String(m_bossXML.@className);
            var _loc10_:int = int(m_bossXML.@lostPowerValue);
            var _loc3_:String = String(m_bossXML.@gNAEclassName);
            var _loc5_:GetNextStepActionEntitiesFactory = new GetNextStepActionEntitiesFactory();
            var _loc2_:IGetNextStepActionEntities = _loc5_.createByClassName(_loc3_);
            ClearUtil.clearObject(_loc5_);
            _loc5_ = null;
            m_worldBoss = new WorldBoss();
            m_worldBoss.setLoader(m_myLoader);
            m_worldBoss.setWorldBossPanel(myThis);
            m_worldBoss.setWorldBossStageShow(m_worldBossStageShow);
            m_worldBoss.setGetNextStepActionEntities(_loc2_);
            m_bossData = new BossData();
            var _loc11_:Vector.<OwnSideEntity> = new Vector.<OwnSideEntity>();
            playerEntity.setPosition(m_worldBoss.getLeftPosition(2,2));
            playerEntity.setFightPositionSprite(m_worldBoss.getLeftMiddlePosition());
            playerEntity.setProAttackPercent(m_worldBossData.getProAttackPercent());
            playerEntity.setFightStage(m_worldBossStageShow);
            playerEntity.setAnimationQueueData(m_worldBoss.getAnimationQueueData());
            playerEntity.setView(m_view);
            playerEntity.setSoundManager(m_soundManager);
            playerEntity.setIsShowBar(true);
            _loc11_.push(playerEntity);
            if(petEntity)
            {
               petEntity.setPosition(m_worldBoss.getLeftPosition(1,2));
               petEntity.setFightPositionSprite(m_worldBoss.getLeftMiddlePosition());
               petEntity.setProAttackPercent(m_worldBossData.getProAttackPercent());
               petEntity.setFightStage(m_worldBossStageShow);
               petEntity.setView(m_view);
               petEntity.setSoundManager(m_soundManager);
               petEntity.setAnimationQueueData(m_worldBoss.getAnimationQueueData());
               _loc11_.push(petEntity);
            }
            var _loc7_:Vector.<Boss> = new Vector.<Boss>();
            var _loc9_:BossFactory = new BossFactory();
            var _loc6_:Boss = _loc9_.createBossByClassNameAndXMLPath(_loc8_,_loc4_,m_myLoader);
            _loc6_.setPosition(m_worldBoss.getRightPosition(m_bossXML.@startX,m_bossXML.@startY));
            _loc6_.setFightPositionSprite(m_worldBoss.getRightMiddlePosition());
            _loc6_.setAnimationQueueData(m_worldBoss.getAnimationQueueData());
            _loc6_.setView(m_view);
            if(_loc6_ is AbleAttackedBoss)
            {
               (_loc6_ as AbleAttackedBoss).setBossData(m_bossData);
            }
            _loc6_.setFightStage(m_worldBossStageShow);
            _loc6_.setSoundManager(m_soundManager);
            _loc6_.setWorldBoss(m_worldBoss);
            _loc7_.push(_loc6_);
            m_worldBoss.setBosses(_loc7_);
            m_worldBoss.setOwnSideEntities(_loc11_);
            m_worldBoss.startFight();
            addChildAt(m_worldBossStageShow,0);
            ClearUtil.clearObject(m_worldBossStartPanel);
            m_worldBossStartPanel = null;
            m_worldBossData.setPowerValue(m_worldBossData.getPowerValue() - _loc10_);
            m_worldBossData.setZeroPromoteAttackPercent();
            GamingUI.getInstance().addMainLineTaskGoalGameEventStr("fightWorldBoss");
         },showWarningBox,true);
      }
      
      public function dealWithDataAndSaveGame() : void
      {
         var _loc2_:BossHurtData = m_worldBossData.getBossHurtDataByBossId(m_currentBossId);
         if(_loc2_ == null)
         {
            _loc2_ = new BossHurtData();
            _loc2_.setBossId(m_currentBossId);
            m_worldBossData.addBossHurtData(_loc2_);
         }
         _loc2_.addHurt(m_bossData.totalBloodOfBossLost,m_bossData.totalHurtNum);
         WorldBossSaveData.getInstance().reSetLastFightBossId(m_currentBossId);
         WorldBossSaveData.getInstance().reSetPowerValueSaveData(m_worldBossData,m_currentTimeStr);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
         submitScoreToRankList();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_worldBossData)
         {
            m_worldBossData.render(param1);
         }
         if(m_worldBossStartPanel)
         {
            m_worldBossStartPanel.render(param1);
         }
      }
      
      public function init(param1:VersionControl) : void
      {
         m_versionControl = param1;
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_roundNumForNowOverBtn = int(XMLSingle.getInstance().dataXML.WorldBossData[0].@roundNumForNowOverBtn);
         m_soundManager = new SoundManager();
         init2();
      }
      
      private function init2() : void
      {
         m_loadUI = Part1.getInstance().getLoadUI();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.getXML("UIData/worldBoss/worldBoss.xml",getWorldBossXMLSuccess,getFailFun);
         m_myLoader.getClass("WorldBoss.swf","WorldBossStage",getWorldBossStageSuccess,getFailFun);
         m_myLoader.load();
      }
      
      private function initWorldBossPanel() : void
      {
         if(m_worldXML == null || m_worldBossStageShow == null || Boolean(m_currentTimeStr) == false)
         {
            return;
         }
         initWorldBossData();
         initStartPanel();
      }
      
      private function initStartPanel() : void
      {
         selectBoss(m_bossIds[0]);
         showStartPanel();
      }
      
      private function initWorldBossData() : void
      {
         m_worldBossData = new WorldBossData();
         WorldBossSaveData.getInstance().getGameData(GamingUI.getInstance().player1.vipVO,m_currentTimeStr,m_worldBossData);
      }
      
      public function showStartPanel() : void
      {
         ClearUtil.clearObject(m_worldBossStartPanel);
         m_worldBossStartPanel = new WorlBossStartPanel();
         m_worldBossStartPanel.setWorldBossData(m_worldBossData);
         m_worldBossStartPanel.setGamingUI(m_gamingUI);
         m_worldBossStartPanel.setLoader(m_myLoader);
         m_worldBossStartPanel.setWorldBossPanel(this);
         m_worldBossStartPanel.setWorldBossXML(m_worldXML);
         m_worldBossStartPanel.setBossIds(m_bossIds,getAbleFightBossId());
         m_worldBossStartPanel.setTotalBossHurtTextShow(getTotalBossHurt());
         m_worldBossStartPanel.initShow();
         addChildAt(m_worldBossStartPanel,0);
      }
      
      private function getWorldBossStageSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_worldBossStageShow = new _loc2_();
         m_view = new View();
         m_view.setShow(m_worldBossStageShow);
         m_nowOverBtn = new ButtonLogicShell2();
         m_nowOverBtn.setShow(m_worldBossStageShow["nowOverBtn"]);
         initWorldBossPanel();
         MyFunction2.getServerTimeFunction(getTimeStr,showWarningBox,true);
      }
      
      private function getWorldBossXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_worldXML = param1.resultXML;
      }
      
      private function getTimeStr(param1:String) : void
      {
         m_currentTimeStr = param1;
         reSetBossDataByTime(m_currentTimeStr);
         initWorldBossPanel();
      }
      
      private function getAbleFightBossId() : String
      {
         var _loc2_:String = null;
         var _loc1_:int = 0;
         if(Boolean(m_worldBossData.getSaveData().getLastFightBossId()) == false)
         {
            _loc2_ = m_bossIds[0];
         }
         else
         {
            _loc1_ = int(m_bossIds.indexOf(m_worldBossData.getSaveData().getLastFightBossId()));
            if(_loc1_ < m_bossIds.length - 2)
            {
               _loc2_ = m_bossIds[_loc1_ + 1];
            }
            else
            {
               _loc2_ = m_bossIds[0];
            }
         }
         return _loc2_;
      }
      
      public function selectBoss(param1:String) : void
      {
         m_currentBossId = param1;
         getBossData();
      }
      
      private function reSetBossDataByTime(param1:String) : void
      {
         m_currentTimeStr = param1;
         var _loc2_:WorldBossNeedSomeData = new WorldBossNeedSomeData();
         new GetWorldBossData().getData(m_currentTimeStr,m_worldXML,true,_loc2_);
         m_effectiveTimeStr = _loc2_.effectiveTime;
         m_currentTimeInCycle = _loc2_.timeInCycle;
         m_currentCycleNum = _loc2_.cycleNum;
         WorldBossSaveData.getInstance().isResetData(m_currentTimeStr,_loc2_.cycleLength,_loc2_.effectiveTime);
         m_rankId = _loc2_.rankId;
         _loc2_.clear();
      }
      
      private function getBossData() : void
      {
         var _loc1_:int = 0;
         ClearUtil.nullArr(m_currentRewardVOs);
         m_currentRewardVOs = null;
         m_bossXML = new GetWorldBossData().getBossXML(m_currentBossId,m_worldXML);
         if(m_bossXML)
         {
            m_currentRewardVOs = XMLSingle.getEquipmentVOs(m_bossXML.rewards[0].reward[int(m_currentTimeInCycle / 24)],XMLSingle.getInstance().equipmentXML,false);
            _loc1_ = int(m_bossXML.@lostPowerValue);
         }
         if(m_worldBossStartPanel)
         {
            m_worldBossStartPanel.setRewardVOs(m_currentRewardVOs);
            m_worldBossStartPanel.setLostPowerValue(_loc1_);
         }
      }
      
      private function getTotalBossHurt() : Number
      {
         return new WorldBossFun().getTotalBossHurt();
      }
      
      private function getTotalBossHurtNum() : int
      {
         return new WorldBossFun().getTotalBossHurtNum();
      }
      
      private function getTotalBossFightNum() : int
      {
         return new WorldBossFun().getTotalBossFightNum();
      }
      
      private function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      public function gameOverShow() : void
      {
         ClearUtil.clearObject(m_gameOverPanel);
         m_gameOverPanel = null;
         ClearUtil.clearObject(m_returnBtn);
         m_returnBtn = null;
         m_myLoader.getClass("WorldBoss.swf","gameOverPanel",getGameOverPanelSuccess,getFailFun);
         m_myLoader.load();
      }
      
      private function getGameOverPanelSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_gameOverPanel = new AnimationShowPlayLogicShell();
         m_gameOverPanel.setShow(new _loc2_());
         addChild(m_gameOverPanel.getShow() as DisplayObject);
         m_bossLostVolumeText = m_gameOverPanel.getShow()["bossLostBloodVolumeTxt"];
         m_totalBossLostVolumeText = m_gameOverPanel.getShow()["totalBossLostBloodVolumeTxt"];
         m_returnBtn = new ButtonLogicShell2();
         m_returnBtn.setShow(m_gameOverPanel.getShow()["returnBtn"]);
         m_bossLostVolumeText.text = m_bossData.totalBloodOfBossLost.toString();
         m_totalBossLostVolumeText.text = getTotalBossHurt().toString();
         SubmitFunction.getInstance().setData4(14,m_bossData.totalBloodOfBossLost);
         if(m_worldBoss)
         {
            m_worldBoss.clear();
         }
         m_worldBoss = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_returnBtn:
               if(m_worldBossStageShow.parent)
               {
                  m_worldBossStageShow.parent.removeChild(m_worldBossStageShow);
               }
               if(Boolean(m_gameOverPanel) && m_gameOverPanel.getShow().parent)
               {
                  m_gameOverPanel.getShow().parent.removeChild(m_gameOverPanel.getShow());
               }
               ClearUtil.clearObject(m_gameOverPanel);
               m_gameOverPanel = null;
               ClearUtil.clearObject(m_returnBtn);
               m_returnBtn = null;
               m_bossLostVolumeText = null;
               initStartPanel();
               break;
            case m_nowOverBtn:
               gameOverShow();
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      public function openRankList() : void
      {
         var _loc1_:GamingUI = m_gamingUI;
         var _loc3_:int = m_rankId;
         var _loc2_:Number = getTotalBossHurt();
         _loc1_.closeWorldBossPanel();
         _loc1_.openWorldBossRankListPanel(_loc3_,_loc2_);
      }
      
      public function isAbleGetRewards() : Boolean
      {
         var _loc2_:BossHurtData = m_worldBossData.getBossHurtDataByBossId(m_currentBossId);
         if(_loc2_ == null)
         {
            return true;
         }
         var _loc1_:String = _loc2_.getRewardDate();
         if(Boolean(_loc1_) == false)
         {
            return true;
         }
         if(MyFunction.getInstance().newDateIsNewDay(_loc1_,m_currentTimeStr))
         {
            return true;
         }
         return false;
      }
      
      public function getRewards() : Boolean
      {
         var isAddSuccess:Boolean;
         MyFunction2.addEquipmentVOs(m_currentRewardVOs,GamingUI.getInstance().player1,function():void
         {
            isAddSuccess = false;
         },function():void
         {
            var _loc1_:BossHurtData = m_worldBossData.getBossHurtDataByBossId(m_currentBossId);
            if(_loc1_ == null)
            {
               _loc1_ = new BossHurtData();
               _loc1_.setBossId(m_currentBossId);
               m_worldBossData.addBossHurtData(_loc1_);
            }
            _loc1_.setRewardDate(m_currentTimeStr);
            isAddSuccess = true;
            if(isAddSuccess)
            {
               showWarningBox("首杀奖励获取成功！",0);
            }
         },null,null,0);
         return isAddSuccess;
      }
      
      private function submitScoreToRankList() : void
      {
         var rankInfoAry:Array;
         var myThis:DisplayObject;
         var worldBossRankListFun:WorldBossRankListFun;
         var rId:int = m_rankId;
         var score:Number = getTotalBossHurt();
         var extra:XML = <Data></Data>;
         extra.appendChild(<hurtNum />);
         extra.appendChild(<fightNum />);
         extra.hurtNum[0].@num = getTotalBossHurtNum();
         extra.fightNum[0].@num = getTotalBossFightNum();
         rankInfoAry = [{
            "rId":rId,
            "score":score,
            "extra":extra
         }];
         myThis = this;
         worldBossRankListFun = new WorldBossRankListFun();
         worldBossRankListFun.submitScoreToRankList(GameData.getInstance().getSaveFileData().index,rankInfoAry,null,function(param1:Array):void
         {
         },null,null,function(param1:String):void
         {
         },null);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:* = undefined;
         var _loc4_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function getCurrentBossId() : String
      {
         return m_currentBossId;
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function playAnimationData(param1:AnimationData) : void
      {
         if(param1 is AttackAnimationData)
         {
            if((param1 as AttackAnimationData).roundNum <= m_roundNumForNowOverBtn)
            {
               if(m_nowOverBtn)
               {
                  m_nowOverBtn.getShow().visible = false;
               }
            }
            else if(m_nowOverBtn)
            {
               m_nowOverBtn.getShow().visible = true;
            }
         }
      }
   }
}

import YJFY.LoadXML.ILoadXMLListener;

class MyLoadXMLListener implements ILoadXMLListener
{
   
   public var loadSuccessFun:Function;
   
   public var loadErrorFun:Function;
   
   public function MyLoadXMLListener()
   {
      super();
   }
   
   public function clear() : void
   {
      loadErrorFun = null;
      loadSuccessFun = null;
   }
   
   public function loadSuccess(param1:XML) : void
   {
      if(loadSuccessFun)
      {
         loadSuccessFun(param1);
      }
      clear();
   }
   
   public function errorHandler() : void
   {
      if(loadErrorFun)
      {
         loadErrorFun();
      }
      clear();
   }
   
   public function securityError() : void
   {
   }
   
   public function ioError() : void
   {
   }
}
