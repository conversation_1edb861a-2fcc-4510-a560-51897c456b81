package UI.newGuide.GuideEveryDay
{
   import UI.newGuide.NewGuidePanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GuideEveryPanel
   {
      
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_guideeverylist:GuideEveryList;
      
      private var m_guideeverybtnshell:GuideEveryBtnShell;
      
      public function GuideEveryPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_guideeverylist);
         m_guideeverylist = null;
         ClearUtil.clearObject(m_guideeverybtnshell);
         m_guideeverybtnshell = null;
      }
      
      public function show() : void
      {
         m_guideeverylist.show();
         m_guideeverybtnshell.show();
      }
      
      public function refreshlist() : void
      {
         m_guideeverylist.refreshlist();
      }
      
      public function hide() : void
      {
         m_guideeverylist.hide();
         m_guideeverybtnshell.hide();
      }
      
      public function init(param1:NewGuidePanel, param2:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_show = param2;
         initParams();
      }
      
      private function initParams() : void
      {
         m_guideeverylist = new GuideEveryList();
         m_guideeverylist.init(m_newguidepanel,this,m_show);
         m_guideeverybtnshell = new GuideEveryBtnShell();
         m_guideeverybtnshell.init(m_newguidepanel,this,m_show);
      }
      
      public function refreshScript(param1:GuideEveryItem) : void
      {
         m_guideeverybtnshell.refreshScript(param1.getData());
      }
   }
}

