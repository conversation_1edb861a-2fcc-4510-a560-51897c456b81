package UI.Task.TaskVO
{
   public class AccumulatedTaskVO extends MTaskVO
   {
      
      public static const FINISH_CURRENT_DAY_TASK:Boolean = true;
      
      public static const UNFINISH_CURRENT_DAY_TASK:Boolean = false;
      
      private var _isFinishCurrentDayTask:Boolean;
      
      private var _taskCount:int;
      
      private var _deteDate:String;
      
      private var _currentTaskCount:int;
      
      public function AccumulatedTaskVO()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currentTaskCount = _currentTaskCount;
         _antiwear.deteDate = _deteDate;
      }
      
      public function get isFinishCurrentDayTask() : Boolean
      {
         return _antiwear.isFinishCurrentDayTask;
      }
      
      public function set isFinishCurrentDayTask(param1:Boolean) : void
      {
         _antiwear.isFinishCurrentDayTask = param1;
      }
      
      public function get taskCount() : int
      {
         return _antiwear.taskCount;
      }
      
      public function set taskCount(param1:int) : void
      {
         _antiwear.taskCount = param1;
      }
      
      public function get currentTaskCount() : int
      {
         return _antiwear.currentTaskCount;
      }
      
      public function set currentTaskCount(param1:int) : void
      {
         _antiwear.currentTaskCount = param1;
      }
      
      public function get deteDate() : String
      {
         return _antiwear.deteDate;
      }
      
      public function set deteDate(param1:String) : void
      {
         _antiwear.deteDate = param1;
      }
   }
}

