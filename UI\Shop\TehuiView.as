package UI.Shop
{
   import UI.Button.SureAndCancelBtn;
   import UI.Button.TehuiBtnOkNo;
   import UI.Event.UIBtnEvent;
   import UI.WarningBox.WarningBox;
   
   public class TehuiView extends WarningBox
   {
      
      public var okFun:Function;
      
      public var noFun:Function;
      
      public function TehuiView()
      {
         super();
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         if(parent)
         {
            parent.removeChild(this);
         }
      }
      
      override protected function createBtn(param1:String, param2:uint) : SureAndCancelBtn
      {
         var _loc3_:TehuiBtnOkNo = new TehuiBtnOkNo();
         _loc3_.type = param2;
         _loc3_.setBtnControl(this);
         _loc3_.showText(param1);
         _loc3_.detail = param2;
         return _loc3_;
      }
   }
}

