package UI.SignPanel.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GetSignAwardBtn extends Btn
   {
      
      public function GetSignAwardBtn()
      {
         super();
         setTipString("点击获取签到礼包");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGetSignAwardBtn"));
      }
   }
}

