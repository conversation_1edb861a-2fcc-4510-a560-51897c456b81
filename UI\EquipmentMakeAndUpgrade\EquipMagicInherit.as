package UI.EquipmentMakeAndUpgrade
{
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.GhostTreasureShow;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.SmallPackage.SmallPackage;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.EntityShowContainer;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipMagicInherit extends MySprite
   {
      
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      public var sayText:TextField;
      
      public var sayText2:TextField;
      
      public var sayText3:TextField;
      
      public var requiredMoneyText:TextField;
      
      public var successRateText:TextField;
      
      public var blessShowText:TextField;
      
      private const GHOST_TREASURE_X:Number = 65;
      
      private const GHOST_TREASURE_Y:Number = 166;
      
      private var _ghostTreasure:GhostTreasureShow;
      
      private var _player:Player;
      
      private var _numBtnGroup:NumberBtnGroupLogicShell;
      
      private var _startOpenHoleBtn:ButtonLogicShell2;
      
      private var _chgOldEqBtn:ButtonLogicShell2;
      
      private var _chgNewEqBtn:ButtonLogicShell2;
      
      private var _equipMagicXML:XML;
      
      private var _wantLoadSources:Array = ["OpenEquipMagic"];
      
      private var _oldContainer:Sprite;
      
      private var _materialContainer:Sprite;
      
      private var _luckStoneContainer:Sprite;
      
      private var _huYouContainer:Sprite;
      
      private var _successRateText:TextField;
      
      private var _moneyText:TextField;
      
      public var hitTestArea:Sprite;
      
      private var _equipmentVO:EquipmentVO;
      
      private var _needMagicMaterial:Equipment;
      
      private var _needMagicMaterialNum:int;
      
      private var _needMagicNumText:TextField;
      
      private var _magicMeterialContainer:Sprite;
      
      private var _haveMagicNumText:TextField;
      
      private var _buyMagicMaterialBtn:BuyMaterialGuideBtn;
      
      private var _needBMaterial:Equipment;
      
      private var _needBMaterialNum:int;
      
      private var _needBNumText:TextField;
      
      private var _BMeterialContainer:Sprite;
      
      private var _haveBNumText:TextField;
      
      private var _buyBMaterialBtn:BuyMaterialGuideBtn;
      
      private var _oldEquipment:Equipment;
      
      private var _newEquipment:Equipment;
      
      private var _baseSuccessRate:Number;
      
      private var _maxSuccessRate:Number;
      
      private var _successRate:Number;
      
      private var _needMoney:int;
      
      private var _needMaterial:Equipment;
      
      private var _needMaterialNum:int;
      
      private var _newContainer:Sprite;
      
      private var _luckStoneShow:LuckStoneShow;
      
      private var _huYouVipShow:HuYouVipShow;
      
      private var _needNumText:TextField;
      
      private var _buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var _buyLuckStoneGuideBtn:BuyMaterialGuideBtn;
      
      private var _haveNumText:TextField;
      
      private var _buyGoldPocketBtn:BuyGoldPocketGuideBtn;
      
      private var _currentOpenHoleXML:XML;
      
      private var _targetClickObject:*;
      
      public var assistantPetContainer:Sprite;
      
      public var mainPetContainer:Sprite;
      
      private var _mainPetShowContainer:EntityShowContainer;
      
      private var _assistantPetShowContainer:EntityShowContainer;
      
      private var m_resultShowPanel:EquipmentInheritResult;
      
      private var m_timeUtil:TimeUtil;
      
      private var m_blessProtectShow:MagicChargeBlessShow;
      
      private var m_isBuyBless:Boolean;
      
      public function EquipMagicInherit()
      {
         super();
         m_isBuyBless = false;
         addEventListener("addedToStage",addToStage,false,0,true);
         m_timeUtil = new TimeUtil();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("click",showSmallPackage,false,0,true);
      }
      
      private function switchPlayerPanel(param1:UIBtnEvent) : void
      {
         clearPanel();
         _player = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1 : GamingUI.getInstance().player2;
         var _loc2_:EqMagicIsShow = new EqMagicIsShow(_player);
         SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc2_);
      }
      
      private function clearPanel() : void
      {
         initNotPutInFrame();
         clearBuyBtn();
         clearBuyLuckStoneBtn();
         clearBuyGoldPocketBtn();
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("click",showSmallPackage,false);
      }
      
      private function openUseContractSuccessInforPanel() : void
      {
         if(m_resultShowPanel == null)
         {
            m_resultShowPanel = new EquipmentInheritResult();
            m_resultShowPanel.init(this,true);
         }
         if(_newEquipment)
         {
            m_resultShowPanel.AddShowReultEquipment(_newEquipment,true,false);
         }
         addChild(m_resultShowPanel);
      }
      
      private function openFailInforPanel(param1:Boolean) : void
      {
         if(m_resultShowPanel == null)
         {
            m_resultShowPanel = new EquipmentInheritResult();
            m_resultShowPanel.init(this,false);
         }
         if(_oldEquipment)
         {
            m_resultShowPanel.AddShowReultEquipment(_oldEquipment,false,param1);
         }
         addChild(m_resultShowPanel);
      }
      
      public function closeUseContractSuccessInforPanel() : void
      {
         ClearUtil.clearObject(m_resultShowPanel);
         m_resultShowPanel = null;
      }
      
      private function showSmallPackage(param1:MouseEvent) : void
      {
         var _loc3_:EqMagicIsShow = null;
         var _loc2_:int = 0;
         if(param1.target == mainPetContainer || param1.target == assistantPetContainer)
         {
            _targetClickObject = param1.target;
            var _loc4_:* = param1.target;
            if(mainPetContainer !== _loc4_)
            {
               if(SmallPackage.getInstance().smallPackage_1 && SmallPackage.getInstance().smallPackage_1.equipmentVOs)
               {
                  SmallPackage.getInstance().smallPackage_1.equipmentVOs.length = 0;
                  _loc2_ = 0;
                  while(_loc2_ < GamingUI.getInstance().player1.playerVO.packageEquipmentVOs.length)
                  {
                     if(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_] && _oldEquipment && GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_].equipmentType == _oldEquipment.equipmentVO.equipmentType && GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_].owner == _oldEquipment.equipmentVO.owner && GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_] != _oldEquipment.equipmentVO)
                     {
                        SmallPackage.getInstance().smallPackage_1.equipmentVOs.push(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_]);
                     }
                     _loc2_++;
                  }
               }
               if(SmallPackage.getInstance().smallPackage_2 && SmallPackage.getInstance().smallPackage_2.equipmentVOs)
               {
                  SmallPackage.getInstance().smallPackage_2.equipmentVOs.length = 0;
                  _loc2_ = 0;
                  while(_loc2_ < GamingUI.getInstance().player2.playerVO.packageEquipmentVOs.length)
                  {
                     if(GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc2_] && _oldEquipment && GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc2_].equipmentType == _oldEquipment.equipmentVO.equipmentType && GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc2_].owner == _oldEquipment.equipmentVO.owner && GamingUI.getInstance().player1.playerVO.packageEquipmentVOs[_loc2_] != _oldEquipment.equipmentVO)
                     {
                        SmallPackage.getInstance().smallPackage_2.equipmentVOs.push(GamingUI.getInstance().player2.playerVO.packageEquipmentVOs[_loc2_]);
                     }
                     _loc2_++;
                  }
               }
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               SmallPackage.getInstance().x = assistantPetContainer.x + assistantPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
            }
            else
            {
               clearPanel();
               _loc3_ = new EqMagicIsShow(_player);
               _loc3_.isFumo = true;
               SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc3_);
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               SmallPackage.getInstance().x = mainPetContainer.x + mainPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
            }
            if(!getChildByName(SmallPackage.getInstance().name))
            {
               addChild(SmallPackage.getInstance());
            }
            SmallPackage.getInstance().visible = true;
         }
      }
      
      public function init(param1:Player) : void
      {
         var loadFinishListener:LoadFinishListener1;
         var player:Player = param1;
         _player = player;
         if(_player == GamingUI.getInstance().player1)
         {
            swapPlayerVO(_player,1);
         }
         else if(GamingUI.getInstance().player2)
         {
            swapPlayerVO(_player,2);
         }
         _luckStoneShow = new LuckStoneShow();
         _ghostTreasure = new GhostTreasureShow();
         loadFinishListener = new LoadFinishListener1(function():void
         {
            MyFunction2.loadXMLFunction("equipMagicInherit",function(param1:XML):void
            {
               _equipMagicXML = param1;
               if(_show == null)
               {
                  _show = MyFunction2.returnShowByClassName("MagicInheritPanel") as MovieClip;
               }
               this.x = 40.35;
               this.y = 118;
               addChild(_show);
               _showMC = new MovieClipPlayLogicShell();
               _showMC.setShow(_show);
               _mainPetShowContainer = new EntityShowContainer();
               _mainPetShowContainer.init();
               _assistantPetShowContainer = new EntityShowContainer();
               _assistantPetShowContainer.init();
               _chgOldEqBtn = new ButtonLogicShell2();
               _chgOldEqBtn.setShow(_show["chgOldEqBtn"]);
               _chgOldEqBtn.setTipString("点击更换装备");
               _chgNewEqBtn = new ButtonLogicShell2();
               _chgNewEqBtn.setShow(_show["chgNewEqBtn"]);
               _chgNewEqBtn.setTipString("点击更换装备");
               mainPetContainer = _show["mainPetContainer"];
               assistantPetContainer = _show["assistantPetContainer"];
               blessShowText = _show["shenQiInterpretText"];
               mainPetContainer.mouseChildren = false;
               assistantPetContainer.mouseChildren = false;
               mainPetContainer.addChild(_mainPetShowContainer.getShow());
               assistantPetContainer.addChild(_assistantPetShowContainer.getShow());
               if(isHaveFreeBless() == false)
               {
                  _huYouVipShow = new HuYouVipShow(false);
                  blessShowText.text = "无“圣器护佑”保护，如果继承失败您的装备属性将会消失";
               }
               else
               {
                  _huYouVipShow = new HuYouVipShow(true);
                  blessShowText.text = "受“圣器护佑”保护，即使继承失败您的装备属性也不会消失";
               }
               _huYouContainer = _show["huYouVipContainer"];
               _huYouContainer.addChild(_huYouVipShow);
               initNotPutInFrame();
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadFinishListener);
      }
      
      private function initNotPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("notPutIn");
         this.x = 40.35;
         this.y = 118;
      }
      
      private function clearNotPutInFrame() : void
      {
      }
      
      public function swapPlayerVO(param1:Player, param2:int) : void
      {
         _player = param1;
         var _loc3_:EqMagicIsShow = new EqMagicIsShow(_player);
         if(param2 == 1)
         {
            SmallPackage.getInstance().refreshSmallPackage("singleOnePackage",_loc3_);
         }
         else
         {
            SmallPackage.getInstance().refreshSmallPackage("singleTwOPackage",_loc3_);
         }
      }
      
      private function clearPutInFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _magicMeterialContainer = null;
         _BMeterialContainer = null;
         _luckStoneContainer = null;
         _huYouContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
      }
      
      private function clearSuccessFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _huYouContainer = null;
         _magicMeterialContainer = null;
         _BMeterialContainer = null;
      }
      
      private function clearFailFrame() : void
      {
         ClearUtil.clearDisplayObjectInContainer(_oldContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_materialContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_magicMeterialContainer,false,false);
         ClearUtil.clearDisplayObjectInContainer(_BMeterialContainer,false,false);
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _magicMeterialContainer = null;
         _BMeterialContainer = null;
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         ClearUtil.clearDisplayObjectInContainer(_oldContainer);
         _oldContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_materialContainer);
         _materialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer);
         _luckStoneContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_huYouContainer);
         _huYouContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_magicMeterialContainer);
         _magicMeterialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_BMeterialContainer);
         _BMeterialContainer = null;
         _successRateText = null;
         _moneyText = null;
         blessShowText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startOpenHoleBtn)
         {
            _startOpenHoleBtn.clear();
         }
         _startOpenHoleBtn = null;
         if(_chgOldEqBtn)
         {
            _chgOldEqBtn.clear();
         }
         _chgOldEqBtn = null;
         if(_chgNewEqBtn)
         {
            _chgNewEqBtn.clear();
         }
         _chgNewEqBtn = null;
         _ghostTreasure = null;
         sayText = null;
         sayText2 = null;
         sayText3 = null;
         requiredMoneyText = null;
         successRateText = null;
         _player = null;
         _equipMagicXML = null;
         hitTestArea = null;
         _equipmentVO = null;
         if(_oldEquipment)
         {
            _oldEquipment.clear();
         }
         _oldEquipment = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         if(_needMaterial)
         {
            _needMaterial.clear();
         }
         _needMaterial = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         _luckStoneShow = null;
         _huYouVipShow = null;
         _needNumText = null;
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
         _haveNumText = null;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
         _currentOpenHoleXML = null;
         SmallPackage.getInstance().clear();
         while(mainPetContainer && mainPetContainer.numChildren > 1)
         {
            mainPetContainer.removeChildAt(1);
         }
         while(assistantPetContainer && assistantPetContainer.numChildren > 1)
         {
            assistantPetContainer.removeChildAt(1);
         }
         ClearUtil.clearObject(_mainPetShowContainer);
         _mainPetShowContainer = null;
         ClearUtil.clearObject(_assistantPetShowContainer);
         _assistantPetShowContainer = null;
         ClearUtil.clearObject(m_resultShowPanel);
         m_resultShowPanel = null;
         ClearUtil.clearObject(m_blessProtectShow);
         m_blessProtectShow = null;
         if(_needMagicMaterial)
         {
            _needMagicMaterial.clear();
         }
         _needMagicMaterial = null;
         _needMagicNumText = null;
         _haveMagicNumText = null;
         if(_buyMagicMaterialBtn)
         {
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
         if(_needBMaterial)
         {
            _needBMaterial.clear();
         }
         _needBMaterial = null;
         _needBNumText = null;
         _haveBNumText = null;
         if(_buyBMaterialBtn)
         {
            _buyBMaterialBtn.clear();
         }
         _buyBMaterialBtn = null;
         ClearUtil.clearObject(m_timeUtil);
         m_timeUtil = null;
      }
      
      public function isHaveFreeBless() : Boolean
      {
         if(_player)
         {
            if(_player.vipVO.vipLevel >= 7 && m_timeUtil.newDateIsNewDay(_player.playerVO.eqMagicVO.getUseBlessTime(),m_timeUtil.getTimeStr()) == true)
            {
               return true;
            }
         }
         return false;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:EqMagicIsShow = null;
         switch(param1.button)
         {
            case _numBtnGroup:
               changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
               break;
            case _startOpenHoleBtn:
               if(conditionPreCheck() == false)
               {
                  return;
               }
               if(isHaveFreeBless() == false)
               {
                  m_blessProtectShow = new MagicChargeBlessShow();
                  m_blessProtectShow.init(this);
                  addChild(m_blessProtectShow);
               }
               else
               {
                  start();
               }
               break;
            case _chgNewEqBtn:
               _targetClickObject = _chgNewEqBtn;
               SmallPackage.getInstance().x = assistantPetContainer.x + assistantPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
               if(!getChildByName(SmallPackage.getInstance().name))
               {
                  addChild(SmallPackage.getInstance());
               }
               SmallPackage.getInstance().visible = true;
               break;
            case _chgOldEqBtn:
               _targetClickObject = _chgOldEqBtn;
               clearPanel();
               _loc2_ = new EqMagicIsShow(_player);
               _loc2_.isFumo = true;
               SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc2_);
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               SmallPackage.getInstance().x = mainPetContainer.x + mainPetContainer.width + 30;
               SmallPackage.getInstance().y = 185;
               if(!getChildByName(SmallPackage.getInstance().name))
               {
                  addChild(SmallPackage.getInstance());
               }
               SmallPackage.getInstance().visible = true;
         }
      }
      
      private function ClearMagicFail(param1:EquipmentVO) : void
      {
         var _loc5_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc3_:WeaponEquipmentVO = null;
         var _loc6_:PreciousEquipmentVO = null;
         var _loc4_:NecklaceEquipmentVO = null;
         var _loc7_:GourdEquipmentVO = null;
         var _loc2_:ClothesEquipmentVO = null;
         switch(param1.equipmentType)
         {
            case "weapon":
               _loc3_ = param1 as WeaponEquipmentVO;
               _loc5_ = int(_loc3_.addPlayerSaveAttr.length);
               _loc3_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc3_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "precious":
               _loc6_ = param1 as PreciousEquipmentVO;
               _loc5_ = int(_loc6_.addPlayerSaveAttr.length);
               _loc6_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc6_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "necklace":
               _loc4_ = param1 as NecklaceEquipmentVO;
               _loc5_ = int(_loc4_.addPlayerSaveAttr.length);
               _loc4_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc4_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "gourd":
               _loc7_ = param1 as GourdEquipmentVO;
               _loc5_ = int(_loc7_.addPlayerSaveAttr.length);
               _loc7_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc7_.addPlayerSaveAttrVals.splice(0,_loc5_);
               break;
            case "clothes":
               _loc2_ = param1 as ClothesEquipmentVO;
               _loc5_ = int(_loc2_.addPlayerSaveAttr.length);
               _loc2_.addPlayerSaveAttr.splice(0,_loc5_);
               _loc2_.addPlayerSaveAttrVals.splice(0,_loc5_);
         }
      }
      
      private function MagicContinue(param1:EquipmentVO, param2:EquipmentVO) : void
      {
         var _loc14_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:int = 0;
         var _loc11_:WeaponEquipmentVO = null;
         var _loc7_:WeaponEquipmentVO = null;
         var _loc5_:PreciousEquipmentVO = null;
         var _loc12_:PreciousEquipmentVO = null;
         var _loc13_:NecklaceEquipmentVO = null;
         var _loc10_:NecklaceEquipmentVO = null;
         var _loc15_:GourdEquipmentVO = null;
         var _loc4_:GourdEquipmentVO = null;
         var _loc9_:ClothesEquipmentVO = null;
         var _loc3_:ClothesEquipmentVO = null;
         switch(param1.equipmentType)
         {
            case "weapon":
               _loc11_ = param1 as WeaponEquipmentVO;
               _loc7_ = param2 as WeaponEquipmentVO;
               _loc14_ = int(_loc11_.addPlayerSaveAttr.length);
               _loc6_ = int(_loc7_.addPlayerSaveAttr.length);
               _loc7_.addPlayerSaveAttr.splice(0,_loc6_);
               _loc7_.addPlayerSaveAttrVals.splice(0,_loc6_);
               _loc8_ = 0;
               while(_loc8_ < _loc14_)
               {
                  _loc7_.addPlayerSaveAttr.push(_loc11_.addPlayerSaveAttr[_loc8_]);
                  _loc7_.addPlayerSaveAttrVals.push(_loc11_.addPlayerSaveAttrVals[_loc8_]);
                  _loc8_++;
               }
               _loc11_.addPlayerSaveAttr.splice(0,_loc14_);
               _loc11_.addPlayerSaveAttrVals.splice(0,_loc14_);
               break;
            case "precious":
               _loc5_ = param1 as PreciousEquipmentVO;
               _loc12_ = param2 as PreciousEquipmentVO;
               _loc14_ = int(_loc5_.addPlayerSaveAttr.length);
               _loc6_ = int(_loc12_.addPlayerSaveAttr.length);
               _loc12_.addPlayerSaveAttr.splice(0,_loc6_);
               _loc12_.addPlayerSaveAttrVals.splice(0,_loc6_);
               _loc8_ = 0;
               while(_loc8_ < _loc14_)
               {
                  _loc12_.addPlayerSaveAttr.push(_loc5_.addPlayerSaveAttr[_loc8_]);
                  _loc12_.addPlayerSaveAttrVals.push(_loc5_.addPlayerSaveAttrVals[_loc8_]);
                  _loc8_++;
               }
               _loc5_.addPlayerSaveAttr.splice(0,_loc14_);
               _loc5_.addPlayerSaveAttrVals.splice(0,_loc14_);
               break;
            case "necklace":
               _loc13_ = param1 as NecklaceEquipmentVO;
               _loc10_ = param2 as NecklaceEquipmentVO;
               _loc14_ = int(_loc13_.addPlayerSaveAttr.length);
               _loc6_ = int(_loc10_.addPlayerSaveAttr.length);
               _loc10_.addPlayerSaveAttr.splice(0,_loc6_);
               _loc10_.addPlayerSaveAttrVals.splice(0,_loc6_);
               _loc8_ = 0;
               while(_loc8_ < _loc14_)
               {
                  _loc10_.addPlayerSaveAttr.push(_loc13_.addPlayerSaveAttr[_loc8_]);
                  _loc10_.addPlayerSaveAttrVals.push(_loc13_.addPlayerSaveAttrVals[_loc8_]);
                  _loc8_++;
               }
               _loc13_.addPlayerSaveAttr.splice(0,_loc14_);
               _loc13_.addPlayerSaveAttrVals.splice(0,_loc14_);
               break;
            case "gourd":
               _loc15_ = param1 as GourdEquipmentVO;
               _loc4_ = param2 as GourdEquipmentVO;
               _loc14_ = int(_loc15_.addPlayerSaveAttr.length);
               _loc6_ = int(_loc4_.addPlayerSaveAttr.length);
               _loc4_.addPlayerSaveAttr.splice(0,_loc6_);
               _loc4_.addPlayerSaveAttrVals.splice(0,_loc6_);
               _loc8_ = 0;
               while(_loc8_ < _loc14_)
               {
                  _loc4_.addPlayerSaveAttr.push(_loc15_.addPlayerSaveAttr[_loc8_]);
                  _loc4_.addPlayerSaveAttrVals.push(_loc15_.addPlayerSaveAttrVals[_loc8_]);
                  _loc8_++;
               }
               _loc15_.addPlayerSaveAttr.splice(0,_loc14_);
               _loc15_.addPlayerSaveAttrVals.splice(0,_loc14_);
               break;
            case "clothes":
               _loc9_ = param1 as ClothesEquipmentVO;
               _loc3_ = param2 as ClothesEquipmentVO;
               _loc14_ = int(_loc9_.addPlayerSaveAttr.length);
               _loc6_ = int(_loc3_.addPlayerSaveAttr.length);
               _loc3_.addPlayerSaveAttr.splice(0,_loc6_);
               _loc3_.addPlayerSaveAttrVals.splice(0,_loc6_);
               _loc8_ = 0;
               while(_loc8_ < _loc14_)
               {
                  _loc3_.addPlayerSaveAttr.push(_loc9_.addPlayerSaveAttr[_loc8_]);
                  _loc3_.addPlayerSaveAttrVals.push(_loc9_.addPlayerSaveAttrVals[_loc8_]);
                  _loc8_++;
               }
               _loc9_.addPlayerSaveAttr.splice(0,_loc14_);
               _loc9_.addPlayerSaveAttrVals.splice(0,_loc14_);
         }
      }
      
      public function conditionPreCheck() : Boolean
      {
         if(_oldEquipment == null)
         {
            return false;
         }
         if(_newEquipment == null)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"请放入继承物品！",
               "flag":0
            }));
            return false;
         }
         if(_needMaterialNum > getHaveMaterialNum(_needMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return false;
         }
         if(_needMagicMaterialNum > getHaveMaterialNum(_needMagicMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return false;
         }
         if(_needBMaterialNum > getHaveMaterialNum(_needBMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return false;
         }
         if(_needMoney > _player.playerVO.money)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足！",
               "flag":0
            }));
            return false;
         }
         return true;
      }
      
      public function start() : void
      {
         if(conditionPreCheck() == false)
         {
            return;
         }
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            var saveinfo:SaveTaskInfo;
            var saveinfo1:SaveTaskInfo;
            var r:Number = Math.random();
            _player.playerVO.money -= _needMoney;
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMaterialNum,_needMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMagicMaterialNum,_needMagicMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needBMaterialNum,_needBMaterial.equipmentVO.id);
            MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_numBtnGroup.num,10500000);
            clearBuyBtn();
            clearBuyLuckStoneBtn();
            clearBuyGoldPocketBtn();
            if(r <= _successRate)
            {
               parent.parent.mouseChildren = false;
               parent.parent.mouseEnabled = false;
               MagicContinue(_oldEquipment.equipmentVO,_newEquipment.equipmentVO);
               if(isHaveFreeBless() == true)
               {
                  _huYouVipShow.IsHaveBless(false);
                  blessShowText.text = "无“圣器护佑”保护，如果继承失败您的装备属性将会消失";
                  _player.playerVO.eqMagicVO.setUseBlessTime(m_timeUtil.getTimeStr());
               }
               if(m_isBuyBless)
               {
                  m_isBuyBless = false;
               }
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               initSuccessFrame();
               equipmentsAnimation(function():void
               {
                  openUseContractSuccessInforPanel();
                  clearNewEquipment();
                  clearOldEquipment();
                  clearNeedMaterial();
                  initNotPutInFrame();
                  dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
                  parent.parent.mouseChildren = true;
                  parent.parent.mouseEnabled = true;
               });
            }
            else
            {
               if(m_isBuyBless == false && isHaveFreeBless() == false)
               {
                  ClearMagicFail(_oldEquipment.equipmentVO);
               }
               saveinfo1 = new SaveTaskInfo();
               saveinfo1.type = "4399";
               saveinfo1.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo1);
               MyFunction2.saveGame2();
               initFailFrame();
               equipmentsAnimation(function():void
               {
                  var _loc1_:Boolean = false;
                  _loc1_ = false;
                  if(isHaveFreeBless() == true)
                  {
                     _huYouVipShow.IsHaveBless(false);
                     blessShowText.text = "无“圣器护佑”保护，如果继承失败您的装备属性将会消失";
                     _player.playerVO.eqMagicVO.setUseBlessTime(m_timeUtil.getTimeStr());
                     _loc1_ = true;
                  }
                  else if(m_isBuyBless)
                  {
                     _loc1_ = true;
                     m_isBuyBless = false;
                  }
                  openFailInforPanel(_loc1_);
                  initNotPutInFrame();
                  clearOldEquipment();
                  clearNewEquipment();
                  clearNeedMaterial();
                  clearBuyBtn();
                  dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
               });
            }
         },function():void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"网络连接失败！",
               "flag":0
            }));
         },true);
      }
      
      private function initFailFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("fail");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _magicMeterialContainer = _show["materiaAlContainer"];
         _BMeterialContainer = _show["materialBContainer"];
      }
      
      private function equipmentsAnimation(param1:Function) : void
      {
         if(_oldEquipment)
         {
            TweenLite.to(_oldEquipment,2,{
               "alpha":0,
               "x":164,
               "y":0,
               "ease":Back.easeIn,
               "onComplete":param1
            });
         }
         if(_needMaterial)
         {
            TweenLite.to(_needMaterial,2,{
               "alpha":0,
               "x":84,
               "y":84,
               "ease":Back.easeIn
            });
         }
         if(_needMagicMaterial)
         {
            TweenLite.to(_needMagicMaterial,2,{
               "alpha":0,
               "x":84,
               "y":0,
               "ease":Back.easeIn
            });
         }
         if(_needBMaterial)
         {
            TweenLite.to(_needBMaterial,2,{
               "alpha":0,
               "x":84,
               "y":-84,
               "ease":Back.easeIn
            });
         }
      }
      
      private function initSuccessFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("success");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _magicMeterialContainer = _show["materiaAlContainer"];
         _BMeterialContainer = _show["materialBContainer"];
      }
      
      private function switchToNothing() : void
      {
      }
      
      public function get player() : Player
      {
         return _player;
      }
      
      public function set player(param1:Player) : void
      {
         _player = param1;
      }
      
      public function refreshPanel() : void
      {
      }
      
      public function putInEquipmentVO(param1:EquipmentVO) : Vector.<EquipmentVO>
      {
         var _loc2_:Vector.<EquipmentVO> = putOutEquipmentVO(param1);
         _equipmentVO = param1;
         putInEquipmentVO1(param1);
         return _loc2_;
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         _equipmentVO = null;
         initNotPutInFrame();
         var _loc3_:EquipmentVO = _oldEquipment ? _oldEquipment.equipmentVO : null;
         clearOldEquipment();
         clearNewEquipment();
         clearBuyBtn();
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      public function putInEquipmentVO1(param1:EquipmentVO) : void
      {
         clearOldEquipment();
         _oldEquipment = MyFunction2.sheatheEquipmentShell(param1);
         _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
         _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
         var _loc3_:XML = getCurrentEquipMagicXML(_oldEquipment.equipmentVO);
         switch(param1.equipmentType)
         {
            case "weapon":
               _currentOpenHoleXML = _loc3_["weapon"][0];
               break;
            case "necklace":
               _currentOpenHoleXML = _loc3_["necklace"][0];
               break;
            case "gourd":
               _currentOpenHoleXML = _loc3_["gourd"][0];
               break;
            case "clothes":
               _currentOpenHoleXML = _loc3_["clothes"][0];
               break;
            default:
               return;
         }
         _baseSuccessRate = Number(_currentOpenHoleXML.@successRate);
         _maxSuccessRate = Number(_currentOpenHoleXML.@maxSuccessRate);
         _maxSuccessRate = Math.min(1,_maxSuccessRate);
         _successRate = _baseSuccessRate;
         _needMoney = int(_currentOpenHoleXML.@needMoney);
         clearNeedMaterial();
         _needMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[0].@id,XMLSingle.getInstance().equipmentXML));
         _needMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needMaterialNum = int(_currentOpenHoleXML.material[0].@num);
         clearNewEquipment();
         _newEquipment = _oldEquipment.clone();
         _newEquipment.addEventListener("rollOver",onOver,false,0,true);
         _newEquipment.addEventListener("rollOut",onOut,false,0,true);
         (_newEquipment.equipmentVO as AbleEquipmentVO).addHole();
         _newEquipment.equipmentVO = _newEquipment.equipmentVO;
         (_newEquipment as DisplayObject).scaleX = (_newEquipment as DisplayObject).scaleY = 1.3;
         initPutInFrame();
         initShow();
         var _loc2_:uint = Math.ceil((_maxSuccessRate - _successRate) / 0.1);
         changeSuccessRate2(_loc2_);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 300;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      public function showWarningBoxEx(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      public function putInEquipment(param1:UIBtnEvent) : void
      {
         var _loc12_:int = 0;
         var _loc10_:int = 0;
         var _loc18_:int = 0;
         var _loc11_:int = 0;
         var _loc14_:WeaponEquipmentVO = null;
         var _loc9_:PreciousEquipmentVO = null;
         var _loc17_:NecklaceEquipmentVO = null;
         var _loc19_:GourdEquipmentVO = null;
         var _loc13_:ClothesEquipmentVO = null;
         var _loc8_:EquipmentVO = null;
         var _loc7_:EquipmentVO = null;
         var _loc6_:XML = null;
         var _loc15_:* = 0;
         var _loc4_:EquipmentVO = null;
         var _loc16_:EquipmentVO = null;
         var _loc5_:IEquipmentCell = param1.target.parent.parent;
         var _loc2_:int = int(SmallPackage.getInstance().currentSmallPackage.area.equipmentCells.indexOf(param1.target.parent.parent));
         var _loc3_:EquipmentVO = _loc5_.child as EquipmentVO;
         switch(_targetClickObject)
         {
            case mainPetContainer:
            case _chgOldEqBtn:
               switch(_loc3_.equipmentType)
               {
                  case "weapon":
                     _loc14_ = _loc3_ as WeaponEquipmentVO;
                     if(_loc14_.addPlayerSaveAttr.length <= 0)
                     {
                        showWarningBox("装备末附魔不能作为被继承物品添加！",0);
                        return;
                     }
                     break;
                  case "precious":
                     _loc9_ = _loc3_ as PreciousEquipmentVO;
                     if(_loc9_.addPlayerSaveAttr.length <= 0)
                     {
                        showWarningBox("装备末附魔不能作为被继承物品添加！",0);
                        return;
                     }
                     break;
                  case "necklace":
                     _loc17_ = _loc3_ as NecklaceEquipmentVO;
                     if(_loc17_.addPlayerSaveAttr.length <= 0)
                     {
                        showWarningBox("装备末附魔不能作为被继承物品添加！",0);
                        return;
                     }
                     break;
                  case "gourd":
                     _loc19_ = _loc3_ as GourdEquipmentVO;
                     if(_loc19_.addPlayerSaveAttr.length <= 0)
                     {
                        showWarningBox("装备末附魔不能作为被继承物品添加！",0);
                        return;
                     }
                     break;
                  case "clothes":
                     _loc13_ = _loc3_ as ClothesEquipmentVO;
                     if(_loc13_.addPlayerSaveAttr.length <= 0)
                     {
                        showWarningBox("装备末附魔不能作为被继承物品添加！",0);
                        return;
                     }
                     break;
               }
               while(assistantPetContainer.numChildren > 1)
               {
                  assistantPetContainer.removeChildAt(1);
               }
               initNotPutInFrame();
               _loc8_ = _oldEquipment ? _oldEquipment.equipmentVO : null;
               clearOldEquipment();
               clearBuyBtn();
               _loc7_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
               _oldEquipment = MyFunction2.sheatheEquipmentShell(_loc7_);
               _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
               _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
               if(_loc8_)
               {
                  SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(_loc8_,_loc2_);
               }
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               _loc6_ = getCurrentEquipMagicXML(_oldEquipment.equipmentVO);
               switch(_loc3_.equipmentType)
               {
                  case "weapon":
                     _currentOpenHoleXML = _loc6_["weapon"][0];
                     break;
                  case "precious":
                     _currentOpenHoleXML = _loc6_["precious"][0];
                     break;
                  case "necklace":
                     _currentOpenHoleXML = _loc6_["necklace"][0];
                     break;
                  case "gourd":
                     _currentOpenHoleXML = _loc6_["gourd"][0];
                     break;
                  case "clothes":
                     _currentOpenHoleXML = _loc6_["clothes"][0];
                     break;
                  default:
                     return;
               }
               _baseSuccessRate = Number(_currentOpenHoleXML.@successRate);
               _maxSuccessRate = Number(_currentOpenHoleXML.@maxSuccessRate);
               _maxSuccessRate = Math.min(1,_maxSuccessRate);
               _successRate = _baseSuccessRate;
               _needMoney = int(_currentOpenHoleXML.@needMoney);
               clearNeedMaterial();
               _needMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[0].@id,XMLSingle.getInstance().equipmentXML));
               _needMaterial.addEventListener("rollOver",onOver,false,0,true);
               _needMaterial.addEventListener("rollOut",onOut,false,0,true);
               _needMaterialNum = int(_currentOpenHoleXML.material[0].@num);
               clearNeedMagicMaterial();
               _needMagicMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[1].@id,XMLSingle.getInstance().equipmentXML));
               _needMagicMaterial.addEventListener("rollOver",onOver,false,0,true);
               _needMagicMaterial.addEventListener("rollOut",onOut,false,0,true);
               _needMagicMaterialNum = int(_currentOpenHoleXML.material[1].@num);
               clearNeedBMaterial();
               _needBMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentOpenHoleXML.material[2].@id,XMLSingle.getInstance().equipmentXML));
               _needBMaterial.addEventListener("rollOver",onOver,false,0,true);
               _needBMaterial.addEventListener("rollOut",onOut,false,0,true);
               _needBMaterialNum = int(_currentOpenHoleXML.material[2].@num);
               initPutInFrame();
               (_oldEquipment as DisplayObject).scaleX = (_oldEquipment as DisplayObject).scaleY = 1.3;
               (_needMagicMaterial as DisplayObject).scaleX = (_needMagicMaterial as DisplayObject).scaleY = 1.3;
               (_needBMaterial as DisplayObject).scaleX = (_needBMaterial as DisplayObject).scaleY = 1.3;
               (_needMaterial as DisplayObject).scaleX = (_needMaterial as DisplayObject).scaleY = 1.3;
               _oldContainer.addChild(_oldEquipment as DisplayObject);
               _materialContainer.addChild(_needMaterial as DisplayObject);
               _luckStoneContainer.addChild(_luckStoneShow);
               _needNumText.text = _needMaterialNum.toString();
               _magicMeterialContainer.addChild(_needMagicMaterial as DisplayObject);
               _needMagicNumText.text = _needMagicMaterialNum.toString();
               _BMeterialContainer.addChild(_needBMaterial as DisplayObject);
               _needBNumText.text = _needBMaterialNum.toString();
               _successRateText.text = int(_successRate * 100) + "%";
               clearBuyLuckStoneBtn();
               _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
               _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
               _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
               addChild(_buyLuckStoneGuideBtn);
               refreshMaterialNumShow();
               refreshMagicMaterialNumShow();
               refreshBMaterialNumShow();
               refreshMoneyShow();
               _loc15_ = Math.ceil((_maxSuccessRate - _successRate) / 0.1);
               changeSuccessRate2(_loc15_);
               break;
            case assistantPetContainer:
            case _chgNewEqBtn:
               if(!_oldEquipment || !_oldEquipment.equipmentVO)
               {
                  showWarningBox("请先放入被继承物品！",0);
                  return;
               }
               if(_oldEquipment.equipmentVO.equipmentType != _loc3_.equipmentType)
               {
                  showWarningBox("继承的类型不符合！",0);
                  return;
               }
               if(_oldEquipment.equipmentVO.owner != "")
               {
                  if(_oldEquipment.equipmentVO.owner == "SunWuKong")
                  {
                     if(_loc3_.owner != "SunWuKong")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "BaiLongMa")
                  {
                     if(_loc3_.owner != "BaiLongMa")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "ErLangShen")
                  {
                     if(_loc3_.owner != "ErLangShen")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "ChangE")
                  {
                     if(_loc3_.owner != "ChangE")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "Fox")
                  {
                     if(_loc3_.owner != "Fox")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "TieShan")
                  {
                     if(_loc3_.owner != "TieShan")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
                  else if(_oldEquipment.equipmentVO.owner == "ZiXia")
                  {
                     if(_loc3_.owner != "ZiXia")
                     {
                        showWarningBox("继承装备的人物类型不符合！",0);
                        return;
                     }
                  }
               }
               _loc4_ = _newEquipment ? _newEquipment.equipmentVO : null;
               clearNewEquipment();
               _loc16_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
               _newEquipment = MyFunction2.sheatheEquipmentShell(_loc16_);
               _newEquipment.addEventListener("rollOver",onOver,false,0,true);
               _newEquipment.addEventListener("rollOut",onOut,false,0,true);
               if(_loc4_)
               {
                  SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(_loc4_,_loc2_);
               }
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               (_newEquipment as DisplayObject).scaleX = (_newEquipment as DisplayObject).scaleY = 1.3;
               _newContainer.addChild(_newEquipment as DisplayObject);
               break;
            default:
               showWarningBox("请先选择宠物放入的位置， 点击要放入的地方！",0);
               return;
         }
         hideSmallPackage();
      }
      
      private function hideSmallPackage() : void
      {
         SmallPackage.getInstance().visible = false;
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGroup.num = param1;
         changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
      }
      
      private function changeSuccessRate(param1:Number) : void
      {
         _successRate = Math.min(Math.max(0,param1),_maxSuccessRate);
         _successRateText.text = int(_successRate * 100) + "%";
      }
      
      private function initShow() : void
      {
         (_oldEquipment as DisplayObject).scaleX = (_oldEquipment as DisplayObject).scaleY = 1.3;
         (_needMaterial as DisplayObject).scaleX = (_needMaterial as DisplayObject).scaleY = 1.3;
         (_needMagicMaterial as DisplayObject).scaleX = (_needMagicMaterial as DisplayObject).scaleY = 1.3;
         (_needBMaterial as DisplayObject).scaleX = (_needBMaterial as DisplayObject).scaleY = 1.3;
         _oldContainer.addChild(_oldEquipment as DisplayObject);
         _materialContainer.addChild(_needMaterial as DisplayObject);
         _magicMeterialContainer.addChild(_needMagicMaterial as DisplayObject);
         _BMeterialContainer.addChild(_needBMaterial as DisplayObject);
         _luckStoneContainer.addChild(_luckStoneShow);
         _needMagicNumText.text = _needMagicMaterialNum.toString();
         _needBNumText.text = _needBMaterialNum.toString();
         _newContainer.addChild(_newEquipment as DisplayObject);
         _needNumText.text = _needMaterialNum.toString();
         _successRateText.text = int(_successRate * 100) + "%";
         clearBuyLuckStoneBtn();
         _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
         _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
         _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
         addChild(_buyLuckStoneGuideBtn);
         refreshMaterialNumShow();
         refreshMagicMaterialNumShow();
         refreshBMaterialNumShow();
         refreshMoneyShow();
      }
      
      private function clearBuyMagicMaterialBtn() : void
      {
         if(_buyMagicMaterialBtn)
         {
            if(_buyMagicMaterialBtn.parent)
            {
               _buyMagicMaterialBtn.parent.removeChild(_buyMagicMaterialBtn);
            }
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
      }
      
      private function refreshMagicMaterialNumShow() : void
      {
         if(_needMagicMaterial == null || _haveMagicNumText == null)
         {
            return;
         }
         clearBuyMagicMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMagicMaterial,_player);
         var _loc1_:TextFormat = _haveMagicNumText.defaultTextFormat;
         if(_loc2_ >= _needMagicMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMagicMaterial.equipmentVO.ticketPrice)
            {
               _buyMagicMaterialBtn = new BuyMaterialGuideBtn(_needMagicMaterial.equipmentVO.id,refreshMagicMaterialNumShow,1);
               _buyMagicMaterialBtn.x = _magicMeterialContainer.x - _buyMagicMaterialBtn.width / 2;
               _buyMagicMaterialBtn.y = _magicMeterialContainer.y + 33;
               addChild(_buyMagicMaterialBtn);
            }
         }
         _haveMagicNumText.defaultTextFormat = _loc1_;
         _haveMagicNumText.text = _loc2_.toString();
      }
      
      private function clearBuyBMaterialBtn() : void
      {
         if(_buyBMaterialBtn)
         {
            if(_buyBMaterialBtn.parent)
            {
               _buyBMaterialBtn.parent.removeChild(_buyBMaterialBtn);
            }
            _buyBMaterialBtn.clear();
         }
         _buyBMaterialBtn = null;
      }
      
      private function refreshBMaterialNumShow() : void
      {
         if(_needBMaterial == null || _haveBNumText == null)
         {
            return;
         }
         clearBuyBMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needBMaterial,_player);
         var _loc1_:TextFormat = _haveBNumText.defaultTextFormat;
         if(_loc2_ >= _needBMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needBMaterial.equipmentVO.ticketPrice)
            {
               _buyBMaterialBtn = new BuyMaterialGuideBtn(_needBMaterial.equipmentVO.id,refreshBMaterialNumShow,1);
               _buyBMaterialBtn.x = _BMeterialContainer.x - _buyBMaterialBtn.width / 2;
               _buyBMaterialBtn.y = _BMeterialContainer.y + 33;
               addChild(_buyBMaterialBtn);
            }
         }
         _haveBNumText.defaultTextFormat = _loc1_;
         _haveBNumText.text = _loc2_.toString();
      }
      
      private function refreshMoneyShow() : void
      {
         if(_moneyText == null)
         {
            return;
         }
         clearBuyGoldPocketBtn();
         var _loc1_:TextFormat = _moneyText.defaultTextFormat;
         if(_player.playerVO.money >= _needMoney)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyGoldPocketBtn = new BuyGoldPocketGuideBtn(11100000,refreshMoneyShow,1);
         }
         _moneyText.defaultTextFormat = _loc1_;
         _moneyText.text = _needMoney.toString();
         _moneyText.width = _moneyText.textWidth + 5;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.x = _moneyText.x + _moneyText.width + 2;
            _buyGoldPocketBtn.y = _moneyText.y;
            addChild(_buyGoldPocketBtn);
            _buyGoldPocketBtn.IsBtnLaterOn(true);
         }
      }
      
      private function clearBuyGoldPocketBtn() : void
      {
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
      
      private function clearBuyMaterialBtn() : void
      {
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
      }
      
      private function refreshMaterialNumShow() : void
      {
         if(_needMaterial == null || _haveNumText == null)
         {
            return;
         }
         clearBuyMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMaterial,_player);
         var _loc1_:TextFormat = _haveNumText.defaultTextFormat;
         if(_loc2_ >= _needMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMaterial.equipmentVO.ticketPrice)
            {
               _buyMaterialBtn = new BuyMaterialGuideBtn(_needMaterial.equipmentVO.id,refreshMaterialNumShow,1);
               _buyMaterialBtn.x = _materialContainer.x - _buyMaterialBtn.width / 2;
               _buyMaterialBtn.y = _materialContainer.y + 33;
               addChild(_buyMaterialBtn);
            }
         }
         _haveNumText.defaultTextFormat = _loc1_;
         _haveNumText.text = _loc2_.toString();
      }
      
      private function setNumBtnGroupMaxNum() : void
      {
         if(_numBtnGroup)
         {
            _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         }
      }
      
      private function getHaveMaterialNum(param1:*, param2:Player) : int
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is Equipment)
         {
            _loc3_ = (param1 as Equipment).equipmentVO.id;
         }
         else
         {
            _loc3_ = param1;
         }
         var _loc5_:Vector.<EquipmentVO> = param2.playerVO.packageEquipmentVOs;
         var _loc4_:int = int(_loc5_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(_loc5_[_loc7_])
            {
               if(_loc5_[_loc7_].id == _loc3_)
               {
                  if(_loc5_[_loc7_] is StackEquipmentVO)
                  {
                     _loc6_ += (_loc5_[_loc7_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc6_++;
                  }
               }
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function clearBuyLuckStoneBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            if(_buyLuckStoneGuideBtn.parent)
            {
               _buyLuckStoneGuideBtn.parent.removeChild(_buyLuckStoneGuideBtn);
            }
            _buyLuckStoneGuideBtn.clear();
            _buyLuckStoneGuideBtn = null;
         }
      }
      
      private function initPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putIn");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _magicMeterialContainer = _show["materiaAlContainer"];
         _BMeterialContainer = _show["materialBContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _successRateText = _show["successRateText"];
         _moneyText = _show["moneyText"];
         _haveNumText = _show["haveNumText"];
         _needNumText = _show["needNumText"];
         _haveMagicNumText = _show["haveMagicNumText"];
         _needMagicNumText = _show["needMagicNumText"];
         _haveBNumText = _show["haveBNumText"];
         _needBNumText = _show["needBNumText"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_successRateText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_moneyText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveMagicNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needMagicNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveBNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needBNumText,true);
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.setShow(_show["numBtnGroup"]);
         setNumBtnGroupMaxNum();
         _startOpenHoleBtn = new ButtonLogicShell2();
         _startOpenHoleBtn.setShow(_show["startOpenHoleBtn"]);
         _startOpenHoleBtn.setTipString("点击开始附魔继承");
      }
      
      private function getCurrentEquipMagicXML(param1:EquipmentVO) : XML
      {
         var _loc8_:int = 0;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:XML = null;
         var _loc2_:* = null;
         var _loc5_:XMLList = _equipMagicXML.equipment;
         _loc3_ = int(_loc5_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc3_)
         {
            _loc4_ = _loc5_[_loc8_];
            _loc6_ = int(_loc4_.@num);
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               if(String(_loc4_["id" + (_loc7_ + 1)]) == param1.id.toString())
               {
                  _loc2_ = _loc4_;
                  break;
               }
               _loc7_++;
            }
            _loc8_++;
         }
         if(_loc2_ == null)
         {
            _loc2_ = _equipMagicXML.defau[0];
         }
         return _loc2_;
      }
      
      private function clearOldEquipment() : void
      {
         if(_oldEquipment)
         {
            if((_oldEquipment as DisplayObject).parent)
            {
               (_oldEquipment as DisplayObject).parent.removeChild(_oldEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_oldEquipment);
            _oldEquipment.clear();
            _oldEquipment = null;
         }
      }
      
      private function clearNewEquipment() : void
      {
         if(_newEquipment)
         {
            if((_newEquipment as DisplayObject).parent)
            {
               (_newEquipment as DisplayObject).parent.removeChild(_newEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_newEquipment);
            _newEquipment.clear();
            _newEquipment = null;
         }
      }
      
      private function clearNeedMaterial() : void
      {
         if(_needMaterial)
         {
            if((_needMaterial as DisplayObject).parent)
            {
               (_needMaterial as DisplayObject).parent.removeChild(_needMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMaterial);
            _needMaterial.clear();
            _needMaterial = null;
         }
      }
      
      private function clearNeedMagicMaterial() : void
      {
         if(_needMagicMaterial)
         {
            if((_needMagicMaterial as DisplayObject).parent)
            {
               (_needMagicMaterial as DisplayObject).parent.removeChild(_needMagicMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMagicMaterial);
            _needMagicMaterial.clear();
            _needMagicMaterial = null;
         }
      }
      
      private function clearNeedBMaterial() : void
      {
         if(_needBMaterial)
         {
            if((_needBMaterial as DisplayObject).parent)
            {
               (_needBMaterial as DisplayObject).parent.removeChild(_needBMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needBMaterial);
            _needBMaterial.clear();
            _needBMaterial = null;
         }
      }
      
      private function clearBuyBtn() : void
      {
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyMagicMaterialBtn)
         {
            _buyMagicMaterialBtn.clear();
         }
         _buyMagicMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      public function blessProtect() : void
      {
         m_isBuyBless = true;
         start();
      }
      
      public function isCanPutEquipmentVO(param1:EquipmentVO) : IsCanPutInfo
      {
         var _loc2_:IsCanPutInfo = new IsCanPutInfo();
         if(param1.equipmentType == "clothes" || param1.equipmentType == "necklace" || param1.equipmentType == "weapon" || param1.equipmentType == "gourd")
         {
            _loc2_.isCanPut = true;
         }
         else
         {
            _loc2_.isCanPut = false;
            _loc2_.message = "只有武器、护甲、项链、葫芦才能用来附魔！";
         }
         return _loc2_;
      }
   }
}

import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
import UI.Equipments.AbleEquipments.GourdEquipmentVO;
import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
import UI.Equipments.EquipmentVO.EquipmentVO;
import UI.Players.Player;
import UI.SmallPackage.IEqIsShowInSmallPackage;

class EqMagicIsShow implements IEqIsShowInSmallPackage
{
   
   public var isMaxLevel:Boolean;
   
   private var _player:Player;
   
   public var isFumo:Boolean;
   
   public var typeSource:String;
   
   public function EqMagicIsShow(param1:*)
   {
      super();
      _player = param1;
   }
   
   public function clear() : void
   {
      _player = null;
   }
   
   public function judeIsShow(param1:EquipmentVO) : Boolean
   {
      if(param1.equipmentType != "clothes" && param1.equipmentType != "necklace" && param1.equipmentType != "weapon" && param1.equipmentType != "gourd" && param1.equipmentType != "precious")
      {
         return false;
      }
      if(typeSource && typeSource != param1.equipmentType)
      {
         return false;
      }
      if(isFumo)
      {
         switch(param1.equipmentType)
         {
            case "weapon":
               if((param1 as WeaponEquipmentVO).addPlayerSaveAttr.length == 0)
               {
                  return false;
               }
               break;
            case "precious":
               if((param1 as PreciousEquipmentVO).addPlayerSaveAttr.length == 0)
               {
                  return false;
               }
               break;
            case "necklace":
               if((param1 as NecklaceEquipmentVO).addPlayerSaveAttr.length == 0)
               {
                  return false;
               }
               break;
            case "gourd":
               if((param1 as GourdEquipmentVO).addPlayerSaveAttr.length == 0)
               {
                  return false;
               }
               break;
            case "clothes":
               if((param1 as ClothesEquipmentVO).addPlayerSaveAttr.length == 0)
               {
                  return false;
               }
               break;
         }
      }
      return true;
   }
}
