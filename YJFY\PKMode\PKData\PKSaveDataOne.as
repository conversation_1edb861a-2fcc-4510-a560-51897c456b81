package YJFY.PKMode.PKData
{
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class PKSaveDataOne
   {
      
      private const m_const_numOfPKTargetPlayerData:uint = 8;
      
      private var m_resetNum:uint;
      
      private var m_onekeyPKNum:uint;
      
      private var m_pkDate:String;
      
      private var m_resetType:int = 1;
      
      private var m_pkTargetPlayerDatas:Vector.<PKTargetPlayerData>;
      
      public function PKSaveDataOne()
      {
         super();
         m_pkTargetPlayerDatas = new Vector.<PKTargetPlayerData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_pkTargetPlayerDatas);
         m_pkTargetPlayerDatas = null;
         m_pkDate = null;
      }
      
      public function checknewday() : void
      {
         if(TimeUtil.getTimeUtil().newDateIsNewDay(m_pkDate,TimeUtil.timeStr))
         {
            m_resetType = 1;
         }
      }
      
      public function initFromSaveXML(param1:XML, param2:uint, param3:uint) : void
      {
         var _loc7_:int = 0;
         var _loc4_:PKTargetPlayerData = null;
         m_resetNum = param2;
         m_onekeyPKNum = param3;
         m_pkDate = MyFunction2.resetErrorTime(String(param1.@d));
         if(TimeUtil.getTimeUtil().newDateIsNewDay(m_pkDate,TimeUtil.timeStr))
         {
            m_resetType = 1;
         }
         else
         {
            m_resetType = param1.@resettype;
         }
         var _loc6_:XMLList = param1.player;
         var _loc5_:int = int(_loc6_ ? _loc6_.length() : 0);
         if(_loc5_ > 8)
         {
            _loc5_ = 8;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc4_ = new PKTargetPlayerData(_loc6_[_loc7_].@uid,_loc6_[_loc7_].@sI,_loc6_[_loc7_].@pkI,_loc6_[_loc7_].@pkS,_loc6_[_loc7_].@uN,_loc6_[_loc7_].@p1T,_loc6_[_loc7_].@nN,null);
            m_pkTargetPlayerDatas.push(_loc4_);
            _loc7_++;
         }
      }
      
      public function exportSaveXML(param1:String) : XML
      {
         var _loc4_:int = 0;
         var _loc3_:XML = new XML("<" + (param1 + " ") + "/>");
         _loc3_.@d = m_pkDate ? m_pkDate : "";
         if(TimeUtil.getTimeUtil().newDateIsNewDay(m_pkDate,TimeUtil.timeStr))
         {
            _loc3_.@resettype = 1;
         }
         else
         {
            _loc3_.@resettype = m_resetType;
         }
         var _loc2_:int = int(m_pkTargetPlayerDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_.appendChild(<player />);
            _loc3_.children()[_loc4_].@pkI = m_pkTargetPlayerDatas[_loc4_].getPKIndex();
            _loc3_.children()[_loc4_].@uid = m_pkTargetPlayerDatas[_loc4_].getUid() ? m_pkTargetPlayerDatas[_loc4_].getUid() : "";
            _loc3_.children()[_loc4_].@sI = m_pkTargetPlayerDatas[_loc4_].getIndexOfSaveXML();
            _loc3_.children()[_loc4_].@pkS = m_pkTargetPlayerDatas[_loc4_].getPKState();
            _loc3_.children()[_loc4_].@p1T = m_pkTargetPlayerDatas[_loc4_].getPlayer1Type() ? m_pkTargetPlayerDatas[_loc4_].getPlayer1Type() : "";
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function newDataResetData(param1:String) : void
      {
         resetData(param1);
         m_resetNum = 0;
         m_onekeyPKNum = 0;
      }
      
      public function resetData(param1:String) : void
      {
         m_pkDate = param1;
         ClearUtil.clearObject(m_pkTargetPlayerDatas);
         m_pkTargetPlayerDatas.length = 0;
         ++m_resetNum;
      }
      
      public function oneKeyPK() : void
      {
         ++m_onekeyPKNum;
      }
      
      public function addPKTargetPlayerData(param1:PKTargetPlayerData) : void
      {
         m_pkTargetPlayerDatas.push(param1);
      }
      
      public function getresetType() : int
      {
         return m_resetType;
      }
      
      public function setresetType(param1:int) : void
      {
         m_resetType = param1;
      }
      
      public function getResetNum() : uint
      {
         return m_resetNum;
      }
      
      public function getOneKeyPKNum() : uint
      {
         return m_onekeyPKNum;
      }
      
      public function getPkDate() : String
      {
         return m_pkDate;
      }
      
      public function getPKTargetPlayerNum() : int
      {
         return m_pkTargetPlayerDatas.length;
      }
      
      public function getPKTargetPlayerDataByIndex(param1:int) : PKTargetPlayerData
      {
         return m_pkTargetPlayerDatas[param1];
      }
   }
}

