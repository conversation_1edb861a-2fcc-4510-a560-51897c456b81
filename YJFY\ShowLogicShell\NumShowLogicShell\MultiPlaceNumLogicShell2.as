package YJFY.ShowLogicShell.NumShowLogicShell
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MultiPlaceNumLogicShell2
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_onePlaceNums:Vector.<OnePlaceNumLogicShell>;
      
      private var m_num:uint;
      
      private var m_times:uint = 1;
      
      public function MultiPlaceNumLogicShell2()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_onePlaceNums);
         m_onePlaceNums = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         showNum(m_num,m_times);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function showNum(param1:uint, param2:uint = 1) : void
      {
         var _loc5_:OnePlaceNumLogicShell = null;
         var _loc4_:int = 0;
         ClearUtil.clearObject(m_onePlaceNums);
         m_onePlaceNums = new Vector.<OnePlaceNumLogicShell>();
         m_num = param1;
         m_times = param2;
         if(m_show == null)
         {
            return;
         }
         var _loc3_:String = String(param1);
         if(param2 > 1)
         {
            _loc3_ += "x" + String(param2);
         }
         m_show.gotoAndStop(_loc3_.length);
         _loc4_ = _loc3_.length - 1;
         while(_loc4_ >= 0)
         {
            _loc5_ = new OnePlaceNumLogicShell();
            _loc5_.setShow(m_show["num_" + (_loc3_.length - _loc4_)]);
            if(_loc3_.charAt(_loc4_) == "x")
            {
               _loc5_.showNum(11);
            }
            else
            {
               _loc5_.showNum(int(_loc3_.charAt(_loc4_)));
            }
            m_onePlaceNums.push(_loc5_);
            _loc4_--;
         }
      }
   }
}

