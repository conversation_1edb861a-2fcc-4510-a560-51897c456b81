package YJFY.ShowLogicShell.NumBtnGroupLogicShell
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.Utils.UIMath;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.FocusEvent;
   import flash.events.KeyboardEvent;
   import flash.text.TextField;
   
   public class NumberBtnGroupLogicShell implements IButton
   {
      
      private var _show:Sprite;
      
      private var _numBtnUp:ButtonLogicShell2;
      
      private var _numBtnDown:ButtonLogicShell2;
      
      private var _numText:TextField;
      
      private var _num:int;
      
      private var _minNum:int;
      
      private var _maxNum:int;
      
      private var _isOpenInPut:Boolean;
      
      private var _listeners:Vector.<INumBtnGroupListener>;
      
      public function NumberBtnGroupLogicShell()
      {
         super();
      }
      
      public function setShow(param1:Sprite, param2:Boolean = true) : void
      {
         _show = param1;
         _numBtnUp = new ButtonLogicShell2();
         _numBtnUp.setShow(param1["numBtnUp"]);
         _numBtnDown = new ButtonLogicShell2();
         _numBtnDown.setShow(param1["numBtnDown"]);
         _numText = param1["numText"];
         _numText.multiline = false;
         _numText.wordWrap = false;
         if(_numText != null && param2)
         {
         }
         isOpenInput = _isOpenInPut;
         _numText.text = _num.toString();
         param1.addEventListener("clickButton",onClick,true,0,true);
      }
      
      public function getShow() : Sprite
      {
         return _show;
      }
      
      public function clear() : void
      {
         if(_show)
         {
            _show.removeEventListener("clickButton",onClick,true);
         }
         _show = null;
         if(_numBtnDown)
         {
            _numBtnDown.clear();
         }
         _numBtnDown = null;
         if(_numBtnUp)
         {
            _numBtnUp.clear();
         }
         _numBtnUp = null;
         if(Boolean(_numText) && _numText.type == "input")
         {
            _numText.removeEventListener("focusOut",focusOut,false);
            _numText.removeEventListener("keyUp",keyUp,false);
         }
         _numText = null;
         ClearUtil.nullArr(_listeners);
         _listeners = null;
      }
      
      public function addListener(param1:INumBtnGroupListener) : void
      {
         if(_listeners == null)
         {
            _listeners = new Vector.<INumBtnGroupListener>();
         }
         _listeners.push(param1);
      }
      
      public function removeListener(param1:INumBtnGroupListener) : INumBtnGroupListener
      {
         if(_listeners == null)
         {
            return null;
         }
         var _loc2_:int = int(_listeners.indexOf(param1));
         if(_loc2_ != -1)
         {
            return _listeners.splice(_loc2_,1)[0];
         }
         return null;
      }
      
      public function get num() : int
      {
         return _num;
      }
      
      public function set num(param1:int) : void
      {
         if(param1 < 0)
         {
            throw new Error("数量不能小于0");
         }
         if(param1 > _maxNum)
         {
            param1 = _maxNum;
         }
         _num = param1;
         if(_numText)
         {
            _numText.text = param1.toString();
         }
      }
      
      public function get maxNum() : int
      {
         return _maxNum;
      }
      
      public function set maxNum(param1:int) : void
      {
         if(param1 < 0)
         {
            throw new Error("数量不能小于0");
         }
         _maxNum = param1;
         if(_minNum > _maxNum)
         {
            _minNum = _maxNum;
         }
         if(Boolean(_numText) && _numText.type == "input")
         {
            _numText.maxChars = UIMath.getDigitForInt(_maxNum);
         }
         if(num > _maxNum)
         {
            outMaxNum(num);
            num = _maxNum;
         }
      }
      
      public function get minNum() : int
      {
         return _minNum;
      }
      
      public function set minNum(param1:int) : void
      {
         if(param1 < 0)
         {
            throw new Error("数量不能小于0");
         }
         _minNum = param1;
         if(_minNum > _maxNum)
         {
            _maxNum = _minNum;
         }
         if(num < _minNum)
         {
            outMinNum(num);
            num = _minNum;
         }
      }
      
      public function get isOpenInput() : Boolean
      {
         return _isOpenInPut;
      }
      
      public function set isOpenInput(param1:Boolean) : void
      {
         _isOpenInPut = param1;
         if(_isOpenInPut)
         {
            _numText.addEventListener("focusOut",focusOut,false,0,true);
            _numText.addEventListener("keyUp",keyUp,false,0,true);
            _numText.maxChars = UIMath.getDigitForInt(_maxNum);
            _numText.selectable = true;
            _numText.type = "input";
            _numText.restrict = "0-9";
         }
         else
         {
            _numText.selectable = false;
            _numText.border = false;
            _numText.type = "dynamic";
            _numText.removeEventListener("focusOut",focusOut,false);
            _numText.removeEventListener("keyUp",keyUp,false);
         }
      }
      
      private function focusOut(param1:FocusEvent) : void
      {
         inputText();
      }
      
      private function keyUp(param1:KeyboardEvent) : void
      {
         if(param1.keyCode == 13)
         {
            inputText();
         }
      }
      
      private function onClick(param1:ButtonEvent) : void
      {
         var _loc2_:int = 0;
         switch(param1.button)
         {
            case _numBtnUp:
               if(_num < _maxNum)
               {
                  _num++;
                  _numText.text = _num.toString();
                  _show.dispatchEvent(new ButtonEvent("clickButton",this));
               }
               else
               {
                  _loc2_ = _num + 1;
                  outMaxNum(_loc2_);
               }
               break;
            case _numBtnDown:
               if(_num > _minNum)
               {
                  _num--;
                  _numText.text = _num.toString();
                  _show.dispatchEvent(new ButtonEvent("clickButton",this));
               }
               else
               {
                  _loc2_ = _num - 1;
                  outMinNum(_loc2_);
               }
         }
      }
      
      private function inputText() : void
      {
         if(_numText == null)
         {
            return;
         }
         var _loc1_:int = int(_numText.text);
         if(_loc1_ > _maxNum)
         {
            outMaxNum(_loc1_);
            _loc1_ = _maxNum;
            _numText.text = _loc1_.toString();
         }
         else if(_loc1_ < _minNum)
         {
            outMinNum(_loc1_);
            _loc1_ = _minNum;
            _numText.text = _loc1_.toString();
         }
         _num = _loc1_;
      }
      
      private function outMaxNum(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = _listeners ? _listeners.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _listeners[_loc3_].outMaxNumWarning(param1,_maxNum);
            _loc3_++;
         }
      }
      
      private function outMinNum(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = _listeners ? _listeners.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _listeners[_loc3_].outMinNumWarning(param1,_minNum);
            _loc3_++;
         }
      }
   }
}

