package YJFY.PKMode.PKData
{
   import YJFY.Utils.ClearUtil;
   
   public class MyPKSaveData
   {
      
      private static var m_instance:MyPKSaveData;
      
      private var m_onePKSaveData:PKSaveDataOne;
      
      private var m_twoPKSaveData:PKSaveDataOne;
      
      public function MyPKSaveData()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            m_onePKSaveData = new PKSaveDataOne();
            m_twoPKSaveData = new PKSaveDataOne();
            return;
         }
         throw new Error("实例已经存在");
      }
      
      public static function getInstance() : MyPKSaveData
      {
         if(m_instance == null)
         {
            m_instance = new MyPKSaveData();
         }
         return m_instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_onePKSaveData);
         m_onePKSaveData = null;
         ClearUtil.clearObject(m_twoPKSaveData);
         m_twoPKSaveData = null;
         m_instance = null;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc2_:XML = null;
         if(param1.hasOwnProperty("PK2"))
         {
            _loc2_ = param1.PK2[0];
            if(_loc2_.hasOwnProperty("onePK"))
            {
               m_onePKSaveData.initFromSaveXML(_loc2_.onePK[0],uint(_loc2_.@onePKrN),uint(_loc2_.@oneOneKeyN));
            }
            if(_loc2_.hasOwnProperty("twoPK"))
            {
               m_twoPKSaveData.initFromSaveXML(_loc2_.twoPK[0],uint(_loc2_.@twoPKrN),uint(_loc2_.@twoOneKeyN));
            }
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <PK2 />;
         if(m_onePKSaveData.getResetNum())
         {
            _loc1_.@onePKrN = m_onePKSaveData.getResetNum();
         }
         if(m_twoPKSaveData.getResetNum())
         {
            _loc1_.@twoPKrN = m_twoPKSaveData.getResetNum();
         }
         if(m_onePKSaveData.getOneKeyPKNum())
         {
            _loc1_.@oneOneKeyN = m_onePKSaveData.getOneKeyPKNum();
         }
         if(m_twoPKSaveData.getOneKeyPKNum())
         {
            _loc1_.@twoOneKeyN = m_twoPKSaveData.getOneKeyPKNum();
         }
         _loc1_.appendChild(m_onePKSaveData.exportSaveXML("onePK"));
         _loc1_.appendChild(m_twoPKSaveData.exportSaveXML("twoPK"));
         return _loc1_;
      }
      
      public function getOnePKSaveData() : PKSaveDataOne
      {
         return m_onePKSaveData;
      }
      
      public function getTwoPKSaveData() : PKSaveDataOne
      {
         return m_twoPKSaveData;
      }
   }
}

