package UI.ActivityPanel
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Other.EquipmentAndNumShow;
   import UI.Other.EquipmentAndNumShow2;
   import UI.Players.PlayerVO;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ExchangeColume extends ButtonLogicShell2
   {
      
      private var m_needEqAndNumShows:Vector.<EquipmentAndNumShow2>;
      
      private var m_targetEqAndNumShows:Vector.<EquipmentAndNumShow>;
      
      private var m_exchangeBtnShow:MovieClipPlayLogicShell;
      
      private var m_exchangeBtn:ButtonLogicShell2;
      
      private var m_needEqs:Vector.<Equipment>;
      
      private var m_targetEqs:Vector.<Equipment>;
      
      private var m_exchangeOneData:ExchangeOneData;
      
      private var m_activeOnePanel:ActivityOnePanel;
      
      public function ExchangeColume()
      {
         super();
         m_needEqAndNumShows = new Vector.<EquipmentAndNumShow2>();
         m_targetEqAndNumShows = new Vector.<EquipmentAndNumShow>();
         m_needEqs = new Vector.<Equipment>();
         m_targetEqs = new Vector.<Equipment>();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_needEqAndNumShows);
         m_needEqAndNumShows = null;
         ClearUtil.clearObject(m_targetEqAndNumShows);
         m_targetEqAndNumShows = null;
         ClearUtil.clearObject(m_exchangeBtnShow);
         m_exchangeBtnShow = null;
         ClearUtil.clearObject(m_exchangeBtn);
         m_exchangeBtn = null;
         ClearUtil.clearObject(m_needEqs);
         m_needEqs = null;
         ClearUtil.clearObject(m_targetEqs);
         m_targetEqs = null;
         m_exchangeOneData = null;
      }
      
      public function setActivityOnePanel(param1:ActivityOnePanel) : void
      {
         m_activeOnePanel = param1;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         initShow();
      }
      
      public function setExchangeOneData(param1:ExchangeOneData) : void
      {
         m_exchangeOneData = param1;
         initShow2();
      }
      
      public function refreshShow() : void
      {
         if(!m_exchangeOneData)
         {
            return;
         }
         if(GamingUI.getInstance().player2)
         {
            m_exchangeOneData.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,GamingUI.getInstance().player2.playerVO);
         }
         else
         {
            m_exchangeOneData.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,null);
         }
         initShow3();
      }
      
      private function initShow() : void
      {
         var _loc7_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc2_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:EquipmentAndNumShow = null;
         var _loc3_:EquipmentAndNumShow2 = null;
         var _loc5_:int = m_show.numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc1_ = m_show.getChildAt(_loc7_);
            if(_loc1_.name.substr(0,"needEqShow".length) == "needEqShow")
            {
               _loc2_++;
            }
            else if(_loc1_.name.substr(0,"targetEqShow".length) == "targetEqShow")
            {
               _loc6_++;
            }
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc2_)
         {
            _loc3_ = new EquipmentAndNumShow2();
            _loc3_.setShow(m_show["needEqShow" + (_loc7_ + 1)]);
            m_needEqAndNumShows.push(_loc3_);
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc4_ = new EquipmentAndNumShow();
            _loc4_.setShow(m_show["targetEqShow" + (_loc7_ + 1)]);
            m_targetEqAndNumShows.push(_loc4_);
            _loc7_++;
         }
         m_exchangeBtnShow = new MovieClipPlayLogicShell();
         m_exchangeBtnShow.setShow(m_show["exchangeBtnShow"]);
         m_exchangeBtnShow.gotoAndStop("unable");
         initShow2();
      }
      
      private function initShow2() : void
      {
         var _loc1_:PlayerVO = null;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:Equipment = null;
         if(m_exchangeOneData == null)
         {
            return;
         }
         if(GamingUI.getInstance().player2)
         {
            _loc1_ = GamingUI.getInstance().player2.playerVO;
         }
         m_exchangeOneData.refreshNeedEqHaveNumInPlayer(GamingUI.getInstance().player1.playerVO,_loc1_);
         ClearUtil.clearObject(m_needEqs);
         m_needEqs = new Vector.<Equipment>();
         ClearUtil.clearObject(m_targetEqs);
         m_targetEqs = new Vector.<Equipment>();
         _loc2_ = m_exchangeOneData.getNeedEquipmentDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(m_exchangeOneData.getNeedEquipmentDataByIndex(_loc4_).getEquipmentVO());
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_needEqs.push(_loc3_);
            _loc4_++;
         }
         _loc2_ = m_exchangeOneData.getTargetEquipmentDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(m_exchangeOneData.getTargetEquipmentDataByIndex(_loc4_).getEquipmentVO());
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_targetEqs.push(_loc3_);
            _loc4_++;
         }
         initShow3();
      }
      
      private function initShow3() : void
      {
         var _loc2_:int = 0;
         ClearUtil.clearObject(m_exchangeBtn);
         m_exchangeBtn = null;
         if(m_exchangeOneData.getIsAbleExChange())
         {
            m_exchangeBtnShow.gotoAndStop("able");
            m_exchangeBtn = new ButtonLogicShell2();
            m_exchangeBtn.setTipString("点击兑换物品");
            m_exchangeBtn.setShow(m_exchangeBtnShow.getShow()["exchangeBtn"]);
         }
         else
         {
            m_exchangeBtnShow.gotoAndStop("unable");
         }
         var _loc1_:int = int(m_needEqAndNumShows.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_loc2_ < m_needEqs.length)
            {
               m_needEqAndNumShows[_loc2_].getEqContainer().addChild(m_needEqs[_loc2_]);
               m_needEqAndNumShows[_loc2_].getCurrentNumText().text = m_exchangeOneData.getNeedEquipmentDataByIndex(_loc2_).getCurrentNum().toString();
               m_needEqAndNumShows[_loc2_].getSlashText().text = "/";
               m_needEqAndNumShows[_loc2_].getNumText().text = m_exchangeOneData.getNeedEquipmentDataByIndex(_loc2_).getNum().toString();
               if(m_exchangeOneData.getNeedEquipmentDataByIndex(_loc2_).getCurrentNum() < m_exchangeOneData.getNeedEquipmentDataByIndex(_loc2_).getNum())
               {
                  m_needEqAndNumShows[_loc2_].changeCurrentNumTextColor(16711680);
               }
               else
               {
                  m_needEqAndNumShows[_loc2_].changeCurrentNumTextColor(52224);
               }
            }
            _loc2_++;
         }
         _loc1_ = int(m_targetEqAndNumShows.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_loc2_ < m_targetEqs.length)
            {
               m_targetEqAndNumShows[_loc2_].getEqContainer().addChild(m_targetEqs[_loc2_]);
               m_targetEqAndNumShows[_loc2_].getNumText().text = m_exchangeOneData.getTargetEquipmentDataByIndex(_loc2_).getNum().toString();
            }
            _loc2_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_exchangeBtn === _loc2_)
         {
            if(isCanPutPackage())
            {
               exchange();
            }
            else
            {
               GamingUI.getInstance().showMessageTip("您的背包已满。");
            }
         }
      }
      
      private function isCanPutPackage() : Boolean
      {
         var equipmentData:EquipmentData;
         var isCanPutPackage:Boolean;
         var j:int;
         var length2:int;
         var length:int = m_exchangeOneData.getTargetEquipmentDataNum();
         var equipmentVOs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var i:int = 0;
         while(i < length)
         {
            equipmentData = m_exchangeOneData.getTargetEquipmentDataByIndex(i);
            length2 = int(equipmentData.getNum());
            j = 0;
            while(j < length2)
            {
               equipmentVOs.push(equipmentData.getEquipmentVO().clone());
               ++j;
            }
            ++i;
         }
         MyFunction2.falseAddEquipmentVOs(equipmentVOs,GamingUI.getInstance().player1,function():void
         {
            isCanPutPackage = false;
         },function():void
         {
            isCanPutPackage = true;
         },null,null);
         return isCanPutPackage;
      }
      
      private function exchange() : void
      {
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         if(m_exchangeOneData.getIsAbleExChange() == false)
         {
            return;
         }
         var _loc3_:int = m_exchangeOneData.getNeedEquipmentDataNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
            _loc1_ = MyFunction.getInstance().minusEquipmentVOs(_loc2_,m_exchangeOneData.getNeedEquipmentDataByIndex(_loc4_).getNum(),m_exchangeOneData.getNeedEquipmentDataByIndex(_loc4_).getEquipmentVO().id);
            if(_loc1_)
            {
               _loc2_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs;
               MyFunction.getInstance().minusEquipmentVOs(_loc2_,_loc1_,m_exchangeOneData.getNeedEquipmentDataByIndex(_loc4_).getEquipmentVO().id);
            }
            _loc4_++;
         }
         putInPackage();
         m_activeOnePanel.refreshShow();
      }
      
      private function putInPackage() : void
      {
         var _loc7_:int = 0;
         var _loc2_:EquipmentData = null;
         var _loc6_:Boolean = false;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:int = m_exchangeOneData.getTargetEquipmentDataNum();
         var _loc3_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         _loc7_ = 0;
         while(_loc7_ < _loc1_)
         {
            _loc2_ = m_exchangeOneData.getTargetEquipmentDataByIndex(_loc7_);
            _loc4_ = int(_loc2_.getNum());
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc3_.push(_loc2_.getEquipmentVO().clone());
               _loc5_++;
            }
            _loc7_++;
         }
         MyFunction2.trueAddEquipmentVOs(_loc3_,GamingUI.getInstance().player1,GamingUI.getInstance().showMessageTip,["兑换成功"]);
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

