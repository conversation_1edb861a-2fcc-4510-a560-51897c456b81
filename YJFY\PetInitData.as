package YJFY
{
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.XMLSingle;
   
   public class PetInitData
   {
      
      private var m_eqClassName:String;
      
      private var m_petId:String;
      
      private var m_petXMLPath:String;
      
      private var m_petInitDataXML:XML;
      
      public var m_petEquipmentVO:PetEquipmentVO;
      
      public function PetInitData()
      {
         super();
         m_petInitDataXML = XMLSingle.getInstance().dataXML.PetInitData[0];
      }
      
      public function clear() : void
      {
         m_eqClassName = null;
         m_petId = null;
         m_petXMLPath = null;
         m_petInitDataXML = null;
         m_petEquipmentVO = null;
      }
      
      public function setEqClassName(param1:String) : void
      {
         var _loc2_:XML = null;
         m_eqClassName = param1;
         if(m_eqClassName)
         {
            _loc2_ = m_petInitDataXML.data.(@eqClassName == m_eqClassName)[0];
            m_petId = String(_loc2_.@petId);
            m_petXMLPath = String(_loc2_.@petXMLPath);
         }
         else
         {
            m_petId = null;
            m_petXMLPath = null;
         }
      }
      
      public function setPetId(param1:String) : void
      {
         m_petId = param1;
         var _loc2_:XML = m_petInitDataXML.data.(@petId == m_petId)[0];
         m_eqClassName = String(_loc2_.@eqClassName);
         m_petXMLPath = String(_loc2_.@petXMLPath);
      }
      
      public function setPetXMLPath(param1:String) : void
      {
         m_petXMLPath = param1;
         var _loc2_:XML = m_petInitDataXML.data.(@petXMLPath == m_petXMLPath)[0];
         m_eqClassName = String(_loc2_.@eqClassName);
         m_petId = String(_loc2_.@petId);
      }
      
      public function getEqClassName() : String
      {
         return m_eqClassName;
      }
      
      public function getPetId() : String
      {
         return m_petId;
      }
      
      public function getPetXMLPath() : String
      {
         return m_petXMLPath;
      }
   }
}

