package UI.newTask.NewMainTask
{
   import GM_UI.GMData;
   import UI.Event.UIPassiveEvent;
   import UI.MainLineTask.DataLayer_TaskPanel;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.MainLineTask.TaskDescription.TaskDescription1;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Utils.LoadXML.LoadXMLListener1;
   import UI.WarningBox.WarningBoxSingle;
   import UI.newTask.NewTaskPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class NewMainDetail
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_newmaintaskpanel:NewMainTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_mainLineTaskDescriptionXML:XML;
      
      private var m_taskDetailShow:ScrollSpriteLogicShell;
      
      private var m_dataLayer:DataLayer_TaskPanel;
      
      private var m_currentTaskVO:MainLineTaskVO;
      
      private var m_txtName:TextField;
      
      private var m_txtDoneNum:TextField;
      
      public function NewMainDetail()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_taskDetailShow);
         m_taskDetailShow = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewMainTaskPanel) : void
      {
         m_newtaskpanel = param1;
         m_newmaintaskpanel = param3;
         m_show = param2;
         m_mc = param2["taskDetailShow"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_txtName = m_show["txttaskname"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName,true);
         m_txtDoneNum = m_show["txttaskdonenum"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtDoneNum,true);
         m_taskDetailShow = new ScrollSpriteLogicShell();
         m_taskDetailShow.setShow(m_show["taskDetailShow"]);
         m_dataLayer = new DataLayer_TaskPanel();
         m_dataLayer.setShow(m_taskDetailShow.getDataLayer() as MovieClip);
      }
      
      private function showTaskTitle(param1:MainLineTaskVO) : void
      {
         m_txtName.text = param1.name;
         var _loc4_:* = param1.getCurrentTaskGoalByIndex(0) ? param1.getCurrentTaskGoalByIndex(0).num : 0;
         var _loc3_:int = param1.getNeedTaskGoalByIndex(0).num;
         if(param1.isGotReward)
         {
            _loc4_ = _loc3_;
         }
         var _loc2_:TextFormat = m_txtDoneNum.defaultTextFormat;
         if(_loc4_ >= _loc3_)
         {
            _loc2_.color = 52224;
         }
         else
         {
            _loc2_.color = 16711680;
         }
         m_txtDoneNum.defaultTextFormat = _loc2_;
         m_txtDoneNum.text = "（" + _loc4_ + "/" + _loc3_ + "）";
         m_txtDoneNum.x = m_txtName.x + m_txtName.textWidth + 5;
      }
      
      public function refreshScript(param1:NewMainListItem) : void
      {
         var taskLine:NewMainListItem = param1;
         var taskVO:MainLineTaskVO = taskLine.getData();
         m_currentTaskVO = taskVO;
         showTaskTitle(taskVO);
         if(m_mainLineTaskDescriptionXML == null)
         {
            MyFunction2.loadXMLFunction("mainLineTask/mainLineTaskDescription",function(param1:XML):void
            {
               m_mainLineTaskDescriptionXML = param1;
               taskVO.initTaskDescription(m_mainLineTaskDescriptionXML);
               showDescription(taskVO);
            },function():void
            {
               showWarningBox("加载失败！",0);
            });
         }
         else
         {
            taskVO.initTaskDescription(m_mainLineTaskDescriptionXML);
            showDescription(taskVO);
         }
      }
      
      private function showDescription(param1:MainLineTaskVO) : void
      {
         var taskDescription1:TaskDescription1;
         var loadXMLListener:LoadXMLListener1;
         var taskVO:MainLineTaskVO = param1;
         if(taskVO.description is TaskDescription1 && GMData.getInstance().isGMApplication == false)
         {
            taskDescription1 = taskVO.description as TaskDescription1;
            if(taskDescription1.dropOutEquipmentVOs_boss == null || taskDescription1.dropOutEquipmentVOs_enemy == null)
            {
               loadXMLListener = new LoadXMLListener1();
               loadXMLListener.loadSuccessFun = function(param1:XML):void
               {
                  m_dataLayer.setTaskVO(taskVO);
                  m_taskDetailShow.refresh();
                  rewardShow(taskVO);
               };
               taskDescription1.loadXML.addLoadXMLListener(loadXMLListener);
            }
            else
            {
               m_dataLayer.setTaskVO(taskVO);
               m_taskDetailShow.refresh();
               rewardShow(taskVO);
            }
         }
         else
         {
            m_dataLayer.setTaskVO(taskVO);
            m_taskDetailShow.refresh();
            rewardShow(taskVO);
         }
      }
      
      private function rewardShow(param1:MainLineTaskVO) : void
      {
         m_newmaintaskpanel.showReward(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChild(WarningBoxSingle.getInstance());
      }
      
      public function initShow() : void
      {
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_taskDetailShow === _loc2_)
         {
            m_taskDetailShow.openMouseWheel();
         }
      }
      
      public function show() : void
      {
         registerEvent();
         m_mc.visible = true;
         m_mc.x = 336.15;
         m_mc.y = 118.45;
      }
      
      public function hide() : void
      {
         closeEvent();
         m_mc.visible = false;
         m_mc.x = 10000;
         m_mc.y = 10000;
      }
      
      private function registerEvent() : void
      {
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      private function closeEvent() : void
      {
         m_mc.removeEventListener("clickButton",clickButton,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,true);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,false);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,false);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

