package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_EveryTargetAddShow2;
   import YJFY.Skill.IStunSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   import flash.display.MovieClip;
   
   public class Skill_DragonSkill3 extends CuboidAreaAttackSkill_EveryTargetAddShow2 implements IStunSkill
   {
      
      private var m_dataOfBinCiHeightPoints:DataOfPoints;
      
      public function Skill_DragonSkill3()
      {
         super();
         m_dataOfBinCiHeightPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_dataOfBinCiHeightPoints);
         m_dataOfBinCiHeightPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_myLoader.getClass(param1.binCiHeightData[0].@swfPath,param1.binCiHeightData[0].@className,getBinCiHeightSuccess,null);
         m_myLoader.load();
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function createEntityShow() : AnimationEntityLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_everyEntityAddShowDefinitionData));
         var _loc1_:AnimationEntityLogicShell = new BinCiAnimationEntityLogicShell();
         _loc1_.setShow(_loc2_);
         _loc1_.getAniamtionShowPlay().addNextStopListener(m_everyEntityAddShowPlayEndListener);
         if(m_dataOfBinCiHeightPoints)
         {
            (_loc1_ as BinCiAnimationEntityLogicShell).setDataOfBinCiHeightPoint(m_dataOfBinCiHeightPoints);
         }
         return _loc1_;
      }
      
      override protected function addEffectShowAddToTarget(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if(param1 is IAnimalEntity)
         {
            _loc2_ = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc2_.extra = param1;
            (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect.push(param1);
            _loc2_.gotoAndPlay("start");
            (_loc2_.getShow() as AnimationShow).y = 0 - param1.getBodyZRange();
            param1.addOtherAniamtion(_loc2_);
         }
      }
      
      private function getBinCiHeightSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:MovieClip = new param1.resultClass();
         m_dataOfBinCiHeightPoints.initByMovieClip(_loc2_);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
   }
}

