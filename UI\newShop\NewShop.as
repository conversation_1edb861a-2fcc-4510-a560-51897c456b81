package UI.newShop
{
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.ShopWall.ShopWall_BuyPopUpBox;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.EntityShowContainer;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class NewShop extends MySprite
   {
      
      private var newShopXml:XML;
      
      private var show:MovieClip;
      
      private var btnClose:ButtonLogicShell2;
      
      private var btnPageNext:ButtonLogicShell2;
      
      private var btnPagePre:ButtonLogicShell2;
      
      private var btnRecharge:ButtonLogicShell2;
      
      private var txtPage:TextField;
      
      private var txtTicket:TextField;
      
      private var btnTopArr:Array;
      
      private var topBtnGroup:SwitchBtnGroupLogicShell;
      
      private var subpageBtnGroup:SwitchBtnGroupLogicShell;
      
      private var currentPage:int = 1;
      
      private var currentXml:XML;
      
      private var items:Vector.<NewShopItem>;
      
      private var popUpBox:ShopWall_BuyPopUpBox;
      
      private var player1ShowContainer:EntityShowContainer;
      
      private var player2ShowContainer:EntityShowContainer;
      
      public var currentShowEquipmentId:int = 0;
      
      private var page1:int = 0;
      
      private var page2:int = 0;
      
      public function NewShop(param1:int = 0, param2:int = 0)
      {
         super();
         page1 = param1;
         page2 = param2;
         this.name = "newShop";
         initUI();
         loadXml();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("showBox",showBox);
         removeEventListener("cancelBuy",hideBox);
         removeEventListener("buyEquipment",buyEquipment,true);
         GameEvent.eventDispacher.removeEventListener("ticketChange",onResetTicketTextHandler);
         ClearUtil.clearObject(topBtnGroup);
         topBtnGroup = null;
         ClearUtil.clearObject(subpageBtnGroup);
         subpageBtnGroup = null;
         ClearUtil.clearObject(btnClose);
         btnClose = null;
         ClearUtil.clearObject(btnPageNext);
         btnPageNext = null;
         ClearUtil.clearObject(btnPagePre);
         btnPagePre = null;
         ClearUtil.clearObject(btnRecharge);
         btnRecharge = null;
         txtPage = null;
         txtTicket = null;
         ClearUtil.clearObject(items);
         items = null;
         ClearUtil.clearObject(newShopXml);
         newShopXml = null;
         ClearUtil.clearObject(currentXml);
         currentXml = null;
         ClearUtil.clearObject(popUpBox);
         popUpBox = null;
         ClearUtil.clearObject(player1ShowContainer);
         player1ShowContainer = null;
         ClearUtil.clearObject(player2ShowContainer);
         player2ShowContainer = null;
         super.clear();
      }
      
      private function initUI() : void
      {
         var _loc2_:MySwitchBtnLogicShell = null;
         var _loc1_:NewShopItem = null;
         show = MyFunction2.returnShowByClassName("NewShopUI") as MovieClip;
         this.addChild(show);
         btnClose = new ButtonLogicShell2();
         btnClose.setShow(show["btnClose"]);
         btnPageNext = new ButtonLogicShell2();
         btnPageNext.setShow(show["btnPageNext"]);
         btnPagePre = new ButtonLogicShell2();
         btnPagePre.setShow(show["btnPagePre"]);
         btnRecharge = new ButtonLogicShell2();
         btnRecharge.setShow(show["btnRecharge"]);
         btnRecharge.setTipString("点击前往充值页面");
         txtPage = show["txtPage"];
         txtTicket = show["txtTicket"];
         topBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         var _loc3_:int = 1;
         while(show.getChildByName("btnTop" + _loc3_) != null)
         {
            _loc2_ = new MySwitchBtnLogicShell();
            _loc2_.setShow(show.getChildByName("btnTop" + _loc3_) as Sprite);
            topBtnGroup.addSwitchBtn(_loc2_);
            _loc3_++;
         }
         topBtnGroup.addEnd();
         items = new Vector.<NewShopItem>();
         _loc3_ = 0;
         while(show.getChildByName("mcItem" + _loc3_) != null)
         {
            _loc1_ = new NewShopItem(show.getChildByName("mcItem" + _loc3_) as MovieClip,this);
            items.push(_loc1_);
            _loc3_++;
         }
         player1ShowContainer = new EntityShowContainer();
         player1ShowContainer.init();
         if(GamingUI.getInstance().player2)
         {
            player2ShowContainer = new EntityShowContainer();
            player2ShowContainer.init();
         }
         txtTicket.text = String(CurrentTicketPointManager.getInstance().getCurrentTicketPoint());
         resetModel();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("showBox",showBox);
         addEventListener("cancelBuy",hideBox);
         addEventListener("buyEquipment",buyEquipment,true);
         GameEvent.eventDispacher.addEventListener("ticketChange",onResetTicketTextHandler);
      }
      
      private function onClickTopBtn() : void
      {
         var _loc1_:MySwitchBtnLogicShell = null;
         if(subpageBtnGroup)
         {
            ClearUtil.clearObject(subpageBtnGroup);
            subpageBtnGroup = null;
         }
         subpageBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         show["mcSubpageBar"].gotoAndStop(topBtnGroup.currentActivateBtn().getShow().name);
         var _loc2_:int = 1;
         while(show["mcSubpageBar"].getChildByName("btnSubpage" + _loc2_) != null)
         {
            _loc1_ = new MySwitchBtnLogicShell();
            _loc1_.setShow(show["mcSubpageBar"].getChildByName("btnSubpage" + _loc2_) as Sprite);
            subpageBtnGroup.addSwitchBtn(_loc1_);
            _loc2_++;
         }
         subpageBtnGroup.addEnd();
         subpageBtnGroup.getSwitchBtnByIndex(page2).turnActiveAndDispatchEvent();
         page2 = 0;
      }
      
      private function onClickSubpage() : void
      {
         currentPage = 1;
         currentXml = newShopXml[topBtnGroup.currentActivateBtn().getShow().name][subpageBtnGroup.currentActivateBtn().getShow().name][0];
         processXmlForDate(currentXml);
         showItems();
      }
      
      private function processXmlForDate(param1:XML) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc4_:int = 0;
         _loc4_ = currentXml.item.length() - 1;
         while(_loc4_ >= 0)
         {
            if(String(currentXml.item[_loc4_].@startDate))
            {
               _loc3_ = new TimeUtil().timeInterval(currentXml.item[_loc4_].@startDate,TimeUtil.timeStr);
               _loc2_ = new TimeUtil().timeInterval(TimeUtil.timeStr,currentXml.item[_loc4_].@endDate);
               if(_loc3_ < 0 || _loc2_ < 0)
               {
                  delete currentXml.item[_loc4_];
               }
            }
            _loc4_--;
         }
      }
      
      private function showItems() : void
      {
         var _loc1_:int = 0;
         resetPageShow();
         _loc1_ = 0;
         while(_loc1_ < items.length)
         {
            if(_loc1_ + items.length * (currentPage - 1) < currentXml.item.length())
            {
               items[_loc1_].setXML(currentXml.item[_loc1_ + items.length * (currentPage - 1)]);
            }
            else
            {
               items[_loc1_].setXML(null);
            }
            _loc1_++;
         }
      }
      
      private function resetPageShow() : void
      {
         txtPage.text = String(currentPage) + "/" + String(getMaxPage());
      }
      
      private function loadXml() : void
      {
         MyFunction2.loadXMLFunction("NewShop",function(param1:XML):void
         {
            newShopXml = param1;
            topBtnGroup.getSwitchBtnByIndex(page1).turnActiveAndDispatchEvent();
         },showWarningBox,true);
      }
      
      public function resetModel(param1:EquipmentVO = null) : void
      {
         var _loc4_:String = null;
         var _loc3_:String = null;
         ClearUtil.clearDisplayObjectInContainer(show["playerShowContainer"]);
         if(param1)
         {
            currentShowEquipmentId = param1.id;
         }
         else
         {
            currentShowEquipmentId = 0;
         }
         var _loc2_:Sprite = new Sprite();
         if(param1 && (!param1.owner || GamingUI.getInstance().player1.playerVO.playerType == param1.owner))
         {
            if(param1 is FashionEquipmentVO || param1 is ForeverFashionEquipmentVO)
            {
               player1ShowContainer.changePlayerShow2(GamingUI.getInstance().player1.playerVO.playerType,param1.className);
            }
            else if(param1 is WeaponEquipmentVO)
            {
               _loc4_ = GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[1] ? GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[1].className : "defaultClothes";
               player1ShowContainer.changePlayerShow(GamingUI.getInstance().player1.playerVO.playerType,(param1 as WeaponEquipmentVO).className,_loc4_);
            }
            else if(param1 is ClothesEquipmentVO)
            {
               _loc3_ = GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[4] ? GamingUI.getInstance().player1.playerVO.inforEquipmentVOs[4].className : "defaultWeapon";
               player1ShowContainer.changePlayerShow(GamingUI.getInstance().player1.playerVO.playerType,_loc3_,(param1 as ClothesEquipmentVO).className);
            }
         }
         else
         {
            player1ShowContainer.refreshPlayerShow(GamingUI.getInstance().player1.playerVO);
         }
         _loc2_.addChild(player1ShowContainer.getShow());
         _loc2_.scaleX = _loc2_.scaleY = 2;
         if(player2ShowContainer)
         {
            if(param1 && (!param1.owner || GamingUI.getInstance().player2.playerVO.playerType == param1.owner))
            {
               if(param1 is FashionEquipmentVO || param1 is ForeverFashionEquipmentVO)
               {
                  player2ShowContainer.changePlayerShow2(GamingUI.getInstance().player2.playerVO.playerType,param1.className);
               }
               else if(param1 is WeaponEquipmentVO)
               {
                  _loc4_ = GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[1] ? GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[1].className : "defaultClothes";
                  player2ShowContainer.changePlayerShow(GamingUI.getInstance().player2.playerVO.playerType,(param1 as WeaponEquipmentVO).className,_loc4_);
               }
               else if(param1 is ClothesEquipmentVO)
               {
                  _loc3_ = GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[4] ? GamingUI.getInstance().player2.playerVO.inforEquipmentVOs[4].className : "defaultWeapon";
                  player2ShowContainer.changePlayerShow(GamingUI.getInstance().player2.playerVO.playerType,_loc3_,(param1 as ClothesEquipmentVO).className);
               }
            }
            else
            {
               player2ShowContainer.refreshPlayerShow(GamingUI.getInstance().player2.playerVO);
            }
            player1ShowContainer.getShow().x = -40;
            player2ShowContainer.getShow().x = 40;
            _loc2_.addChild(player2ShowContainer.getShow());
            _loc2_.scaleX = _loc2_.scaleY = 1.5;
         }
         show["playerShowContainer"].addChild(_loc2_);
         show["playerShowContainer"].mouseChildren = false;
         show["playerShowContainer"].mouseEnabled = false;
         if(currentXml)
         {
            showItems();
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(String(param1.button["getShow"]().name).substr(0,6) == "btnTop")
         {
            onClickTopBtn();
            return;
         }
         if(String(param1.button["getShow"]().name).substr(0,10) == "btnSubpage")
         {
            onClickSubpage();
            return;
         }
         switch(param1.button)
         {
            case btnClose:
               clear();
               GamingUI.getInstance().clearNewShop();
               break;
            case btnRecharge:
               AnalogServiceHoldFunction.getInstance().payMoney_As3();
               break;
            case btnPagePre:
               if(currentPage > 1)
               {
                  currentPage--;
                  showItems();
               }
               break;
            case btnPageNext:
               if(currentPage < getMaxPage())
               {
                  currentPage++;
                  showItems();
               }
         }
      }
      
      private function getMaxPage() : int
      {
         return (currentXml.item.length() - 1) / 6 + 1;
      }
      
      private function onResetTicketTextHandler(param1:GameEvent) : void
      {
         txtTicket.text = String(param1.data);
      }
      
      protected function showBox(param1:UIDataEvent) : void
      {
         var _loc2_:EquipmentVO = param1.data.equipmentVO;
         var _loc3_:int = 100;
         if(getChildByName("popUpBox"))
         {
            popUpBox.initBox(_loc2_,"shopWallPrice");
            popUpBox.setNumBtnGroupMaxNum(_loc3_,showWarningBox,["该物品一次购买最大购买数量为" + _loc3_,0]);
         }
         else
         {
            popUpBox = new ShopWall_BuyPopUpBox(_loc2_,"shopWallPrice");
            popUpBox.setNumBtnGroupMaxNum(_loc3_,showWarningBox,["该物品一次购买最大购买数量为" + _loc3_,0]);
            popUpBox.x = 350;
            popUpBox.y = 170;
            popUpBox.name = "popUpBox";
            addChild(popUpBox);
         }
         if(!GamingUI.getInstance().player2)
         {
            popUpBox.state = 1;
         }
         else
         {
            popUpBox.state = 2;
         }
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(popUpBox)
         {
            if(getChildByName(popUpBox.name))
            {
               removeChild(popUpBox);
            }
            popUpBox.clear();
            popUpBox = null;
         }
      }
      
      protected function buyEquipment(param1:UIDataEvent) : void
      {
         var e:UIDataEvent = param1;
         var addUnAbleBuyId:* = function():void
         {
         };
         var equipmentVO:EquipmentVO = e.data.equipmentVO;
         var num:int = int(e.data.num);
         var price:int = int(XMLSingle.getInstance().equipmentXML.item.(@id == equipmentVO.id)[0].@ticketPrice);
         var ticketId:String = String(XMLSingle.getInstance().equipmentXML.item.(@id == equipmentVO.id)[0].@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = num;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         AnalogServiceHoldFunction.getInstance().buyByPayData(dataObj,function(param1:Function):void
         {
            var _loc2_:* = e.data.player;
            if(1 !== _loc2_)
            {
               MyFunction.getInstance().buyEquipmentVO(GamingUI.getInstance().player2,equipmentVO,num,showWarningBox,"ticketObj",param1,0,addUnAbleBuyId);
            }
            else
            {
               MyFunction.getInstance().buyEquipmentVO(GamingUI.getInstance().player1,equipmentVO,num,showWarningBox,"ticketObj",param1,0,addUnAbleBuyId);
            }
         },stage,showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         hideBox();
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChild(WarningBoxSingle.getInstance());
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

