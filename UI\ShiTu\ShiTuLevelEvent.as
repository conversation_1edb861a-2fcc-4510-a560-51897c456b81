package UI.ShiTu
{
   import flash.events.Event;
   
   public class ShiTuLevelEvent extends Event
   {
      
      public static const START_SHI_TU_LEVEL:String = "startShiTuLevel";
      
      private var _shiTuLevelEventData:ShiTuLevelEventData;
      
      public function ShiTuLevelEvent(param1:String, param2:ShiTuLevelEventData, param3:Boolean = false, param4:Boolean = false)
      {
         super(param1,false,false);
         _shiTuLevelEventData = param2;
      }
      
      public function get shiTuLevelEventData() : ShiTuLevelEventData
      {
         return _shiTuLevelEventData;
      }
      
      public function set shiTuLevelEventData(param1:ShiTuLevelEventData) : void
      {
         _shiTuLevelEventData = param1;
      }
   }
}

