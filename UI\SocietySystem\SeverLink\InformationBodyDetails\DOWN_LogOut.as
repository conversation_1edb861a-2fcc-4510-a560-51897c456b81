package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class DOWN_LogOut extends InformationBodyDetail
   {
      
      protected var m_uid:Number;
      
      protected var m_idx:int;
      
      public function DOWN_LogOut()
      {
         super();
         m_informationBodyId = 3108;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid = _loc2_.uid;
         m_idx = _loc2_.idx;
      }
      
      public function getUid() : Number
      {
         return m_uid;
      }
      
      public function getIdx() : int
      {
         return m_idx;
      }
   }
}

