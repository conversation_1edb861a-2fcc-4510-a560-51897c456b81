package UI.ShiTu
{
   import UI.Button.QuitBtn;
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.PlayerVO;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class ShiTuPanel extends MySprite
   {
      
      public var quitBtn:QuitBtn;
      
      private var _show:Sprite;
      
      private var _choiceTuDiShow:MovieClipPlayLogicShell;
      
      private var _xiaoKong:ChoiceTuDiButton;
      
      private var _xiaoBa:ChoiceTuDiButton;
      
      private var _chengAnLou:ButtonLogicShell;
      
      private var _showMovieClip:MovieClipPlayLogicShell;
      
      private var _choiceDifficultyShow:MovieClipPlayLogicShell;
      
      private var _normalLevelBtn:ButtonLogicShell2;
      
      private var _middleLevelBtn:ButtonLogicShell2;
      
      private var _highLevelBtn:ButtonLogicShell2;
      
      private var _devilLevellBtn:ButtonLogicShell2;
      
      private var _choiceShowQuitBtn:ButtonLogicShell2;
      
      private var _startGameBtn:ButtonLogicShell2;
      
      private var _reChoiceBtn:ButtonLogicShell2;
      
      private var _systemBtn:ButtonLogicShell;
      
      private var _bloodBar1:CMSXChangeBarLogicShell;
      
      private var _bloodBar2:CMSXChangeBarLogicShell;
      
      private var _energyBar1:CMSXChangeBarLogicShell;
      
      private var _energyBar2:CMSXChangeBarLogicShell;
      
      private var _xiuLianValueBar1:CMSXChangeBarLogicShell;
      
      private var _xiuLianValueBar2:CMSXChangeBarLogicShell;
      
      private var _touXiang1:MovieClipPlayLogicShell;
      
      private var _touXiang2:MovieClipPlayLogicShell;
      
      private var _skillShows1:Vector.<MovieClipPlayLogicShell>;
      
      private var _skillShows2:Vector.<MovieClipPlayLogicShell>;
      
      private var _skillSpaces1:Vector.<MovieClipPlayLogicShell>;
      
      private var _skillSpaces2:Vector.<MovieClipPlayLogicShell>;
      
      private var _shiTuMap:XML;
      
      private var _currentLevelName:String;
      
      private var _player1TuDiVO:TuDiVO;
      
      private var _player2TuDiVO:TuDiVO;
      
      private var _isLoadChoiceTuDiShow:Boolean;
      
      private var _oldTime:Number;
      
      private var _isStop:Boolean;
      
      public function ShiTuPanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         if(GamingUI.getInstance().player1.playerVO.tuDiVO)
         {
            GamingUI.getInstance().player1.playerVO.tuDiVO.bloodPercent = 1;
         }
         if(Boolean(GamingUI.getInstance().player2) && GamingUI.getInstance().player2.playerVO.tuDiVO)
         {
            GamingUI.getInstance().player2.playerVO.tuDiVO.bloodPercent = 1;
         }
         removeEventListener("removedFromStage",removeFromStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_isLoadChoiceTuDiShow)
         {
            GamingUI.getInstance().loadQueue.unLoad(["choiceTuDiShow"]);
         }
         _show = null;
         if(_choiceTuDiShow)
         {
            _choiceTuDiShow.clear();
         }
         _choiceTuDiShow = null;
         if(_xiaoKong)
         {
            _xiaoKong.clear();
         }
         _xiaoKong = null;
         if(_xiaoBa)
         {
            _xiaoBa.clear();
         }
         _xiaoBa = null;
         if(_chengAnLou)
         {
            _chengAnLou.clear();
         }
         _chengAnLou = null;
         if(_showMovieClip)
         {
            _showMovieClip.clear();
         }
         _showMovieClip = null;
         if(_choiceDifficultyShow)
         {
            _choiceDifficultyShow.clear();
         }
         _choiceDifficultyShow = null;
         if(_normalLevelBtn)
         {
            _normalLevelBtn.clear();
         }
         _normalLevelBtn = null;
         if(_middleLevelBtn)
         {
            _middleLevelBtn.clear();
         }
         _middleLevelBtn = null;
         if(_highLevelBtn)
         {
            _highLevelBtn.clear();
         }
         _highLevelBtn = null;
         if(_devilLevellBtn)
         {
            _devilLevellBtn.clear();
         }
         _devilLevellBtn = null;
         if(_choiceShowQuitBtn)
         {
            _choiceShowQuitBtn.clear();
         }
         _choiceShowQuitBtn = null;
         if(_startGameBtn)
         {
            _startGameBtn.clear();
         }
         _startGameBtn = null;
         if(_reChoiceBtn)
         {
            _reChoiceBtn.clear();
         }
         _reChoiceBtn = null;
         if(_systemBtn)
         {
            _systemBtn.clear();
         }
         _systemBtn = null;
         if(_bloodBar1)
         {
            _bloodBar1.clear();
         }
         _bloodBar1 = null;
         if(_bloodBar2)
         {
            _bloodBar2.clear();
         }
         _bloodBar2 = null;
         if(_energyBar1)
         {
            _energyBar1.clear();
         }
         _energyBar1 = null;
         if(_energyBar2)
         {
            _energyBar2.clear();
         }
         _energyBar2 = null;
         if(_xiuLianValueBar1)
         {
            _xiuLianValueBar1.clear();
         }
         _xiuLianValueBar1 = null;
         if(_xiuLianValueBar2)
         {
            _xiuLianValueBar2.clear();
         }
         _xiuLianValueBar2 = null;
         if(_touXiang1)
         {
            _touXiang1.clear();
         }
         _touXiang1 = null;
         if(_touXiang2)
         {
            _touXiang2.clear();
         }
         _touXiang2 = null;
         ClearUtil.nullArr(_skillShows1);
         _skillShows1 = null;
         ClearUtil.nullArr(_skillShows2);
         _skillShows2 = null;
         ClearUtil.nullArr(_skillSpaces1);
         _skillSpaces1 = null;
         ClearUtil.nullArr(_skillSpaces2);
         _skillSpaces2 = null;
         _shiTuMap = null;
         if(_player1TuDiVO)
         {
            _player1TuDiVO.clear();
         }
         _player1TuDiVO = null;
         if(_player2TuDiVO)
         {
            _player2TuDiVO.clear();
         }
         _player2TuDiVO = null;
      }
      
      public function init() : void
      {
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _show = MyFunction2.returnShowByClassName("ShiTuPanel") as Sprite;
         _showMovieClip = new MovieClipPlayLogicShell();
         _showMovieClip.setShow(_show as MovieClip);
         addChildAt(_show,0);
         MyFunction2.loadXMLFunction("shiTuMap",function(param1:XML):void
         {
            _shiTuMap = param1;
            if(GamingUI.getInstance().player1.playerVO.tuDiVO)
            {
               gotoMap();
            }
            else
            {
               gotoChoicePanel();
            }
         },function():void
         {
            showWarningBox("加载失败\t！",0);
         },true);
      }
      
      private function gotoChoicePanel() : void
      {
         var loadFinishListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            if(_choiceTuDiShow)
            {
               if(_choiceTuDiShow.getShow().parent)
               {
                  _choiceTuDiShow.getShow().parent.removeChild(_choiceTuDiShow.getShow());
               }
               _choiceTuDiShow.clear();
            }
            _choiceTuDiShow = new MovieClipPlayLogicShell();
            _choiceTuDiShow.setShow(MyFunction2.returnShowByClassName("ChoiceTuDiShow") as MovieClip);
            addChildAt(_choiceTuDiShow.getShow(),getChildIndex(quitBtn));
            _choiceTuDiShow.gotoAndPlay("choiceStart",null,null,function():void
            {
               _xiaoKong = new ChoiceTuDiButton(_choiceTuDiShow.getShow()["xiaoKong"]);
               _xiaoBa = new ChoiceTuDiButton(_choiceTuDiShow.getShow()["xiaoBa"]);
               if(_startGameBtn)
               {
                  _startGameBtn.clear();
               }
               _startGameBtn = null;
               if(_reChoiceBtn)
               {
                  _reChoiceBtn.clear();
               }
               _reChoiceBtn = null;
            },null);
            _isLoadChoiceTuDiShow = true;
         },null);
         GamingUI.getInstance().loadQueue.load(["choiceTuDiShow"],loadFinishListener);
      }
      
      private function gotoMap() : void
      {
         gotoMapLabel();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("clickLockBtn",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("clickLockBtn",clickButton,true);
         removeEventListener("enterFrame",render,false);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc7_:XML = null;
         var _loc4_:Boolean = false;
         var _loc9_:int = 0;
         var _loc6_:XML = null;
         var _loc3_:XML = null;
         var _loc5_:XML = null;
         var _loc2_:String = null;
         var _loc8_:ShiTuLevelEventData = null;
         if(param1.button == _xiaoKong || param1.button == _xiaoBa)
         {
            if(param1.button == _xiaoKong)
            {
               _loc7_ = <Player><ShiTu><TuDi type="xiaoKong" bloodPercent="1" energyPercent="0" xLValue="0" xLNum="0">
														 </TuDi></ShiTu></Player>;
            }
            else
            {
               _loc7_ = <Player><ShiTu><TuDi type="xiaoBa" bloodPercent="1" energyPercent="0" xLValue="0" xLNum="0">
														 </TuDi></ShiTu></Player>;
            }
            if(GamingUI.getInstance().player1.playerVO.tuDiVO == null && _player1TuDiVO == null)
            {
               _loc6_ = XMLSingle.getInstance()[String(_loc7_.ShiTu[0].TuDi[0].@type) + "XML"];
               _player1TuDiVO = InitUI.getInstance().initTuDi(_loc7_,_loc6_,XMLSingle.getInstance().xiuLianContentXML,"");
               _loc3_ = XMLSingle.getInstance().xiuLianContentXML;
               InitUI.getInstance().initXiuLianContents(GamingUI.getInstance().player1.playerVO,null,"ShiFu",_loc3_,MyFunction.getInstance().excreteStringToString(_loc6_.@shiFuHaveContents));
               _loc4_ = true;
               _loc9_ = 0;
            }
            else if(GamingUI.getInstance().player2 != null && GamingUI.getInstance().player2.playerVO.tuDiVO == null && _player2TuDiVO == null)
            {
               _loc6_ = XMLSingle.getInstance()[String(_loc7_.ShiTu[0].TuDi[0].@type) + "XML"];
               _player2TuDiVO = InitUI.getInstance().initTuDi(_loc7_,_loc6_,XMLSingle.getInstance().xiuLianContentXML,"");
               _loc3_ = XMLSingle.getInstance().xiuLianContentXML;
               InitUI.getInstance().initXiuLianContents(GamingUI.getInstance().player2.playerVO,null,"ShiFu",_loc3_,MyFunction.getInstance().excreteStringToString(_loc6_.@shiFuHaveContents));
               _loc4_ = true;
               _loc9_ = 1;
            }
            if(_loc4_)
            {
               (param1.button as ChoiceTuDiButton).lock(_loc9_);
            }
            if((Boolean(GamingUI.getInstance().player1.playerVO.tuDiVO) || Boolean(_player1TuDiVO)) && (GamingUI.getInstance().player2 == null || Boolean(GamingUI.getInstance().player2.playerVO.tuDiVO) || Boolean(_player2TuDiVO)))
            {
               gotoChoiceStart();
            }
         }
         else if(param1.button == _chengAnLou)
         {
            _currentLevelName = "changAnChengLou";
            gotoChoiceDifficultyShow("changAnChengLouLevel","Choice_changAnCheng");
         }
         else if(param1.button == _choiceShowQuitBtn)
         {
            choiceDefficultyShowGotoPlayEnd();
         }
         else if(param1.button == _normalLevelBtn || param1.button == _middleLevelBtn || param1.button == _highLevelBtn || param1.button == _devilLevellBtn)
         {
            _loc5_ = _shiTuMap.level.(@name == _currentLevelName)[0];
            switch(param1.button)
            {
               case _normalLevelBtn:
                  if(_normalLevelBtn.isLock)
                  {
                     showWarningBox("在徒弟到达" + int(_loc5_.difficulty.(@level == "normalLevel")[0].@needLevel) + "级后开启！",0);
                     return;
                  }
                  _loc2_ = "normal";
                  break;
               case _middleLevelBtn:
                  if(_middleLevelBtn.isLock)
                  {
                     showWarningBox("在徒弟到达" + int(_loc5_.difficulty.(@level == "middleLevel")[0].@needLevel) + "级后开启！",0);
                     return;
                  }
                  _loc2_ = "middle";
                  break;
               case _highLevelBtn:
                  if(_highLevelBtn.isLock)
                  {
                     showWarningBox("在徒弟到达" + int(_loc5_.difficulty.(@level == "heightLevel")[0].@needLevel) + "级后开启！",0);
                     return;
                  }
                  _loc2_ = "high";
                  break;
               case _devilLevellBtn:
                  if(_devilLevellBtn.isLock)
                  {
                     showWarningBox("在徒弟到达" + int(_loc5_.difficulty.(@level == "devilLevel")[0].@needLevel) + "级后开启！",0);
                     return;
                  }
                  _loc2_ = "devil";
                  break;
            }
            _loc8_ = new ShiTuLevelEventData();
            _loc8_.player1TuDiVO = GamingUI.getInstance().player1.playerVO.tuDiVO;
            _loc8_.player2TuDiVO = GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO.tuDiVO : null;
            _loc8_.player1TuDiVO.energyPercent = 0;
            if(_loc8_.player2TuDiVO)
            {
               _loc8_.player2TuDiVO.energyPercent = 0;
            }
            _loc8_.levelName = _currentLevelName;
            _loc8_.levelDifficulty = _loc2_;
            dispatchEvent(new ShiTuLevelEvent("startShiTuLevel",_loc8_));
            gotoGameUI();
         }
         else if(param1.button == _startGameBtn)
         {
            if(_player1TuDiVO)
            {
               GamingUI.getInstance().player1.playerVO.tuDiVO = _player1TuDiVO;
               _player1TuDiVO = null;
            }
            if(_player2TuDiVO)
            {
               GamingUI.getInstance().player2.playerVO.tuDiVO = _player2TuDiVO;
               _player2TuDiVO = null;
            }
            gotoMapLabel();
         }
         else if(param1.button == _reChoiceBtn)
         {
            if(_player1TuDiVO)
            {
               _player1TuDiVO.clear();
            }
            _player1TuDiVO = null;
            if(_player2TuDiVO)
            {
               _player2TuDiVO.clear();
            }
            _player2TuDiVO = null;
            gotoChoicePanel();
         }
         else if(param1.button == _systemBtn)
         {
            _systemBtn.getShow().dispatchEvent(new UIBtnEvent("enterIntoSystem"));
         }
      }
      
      private function gotoChoiceStart() : void
      {
         _choiceTuDiShow.gotoAndStop("startGame",function():void
         {
            if(_xiaoKong)
            {
               _xiaoKong.clear();
            }
            _xiaoKong = null;
            if(_xiaoBa)
            {
               _xiaoBa.clear();
            }
            _xiaoBa = null;
            _startGameBtn = new ButtonLogicShell2();
            _startGameBtn.setShow(_choiceTuDiShow.getShow()["startGameBtn"]);
            _startGameBtn.setTipString("点击开始游戏");
            _reChoiceBtn = new ButtonLogicShell2();
            _reChoiceBtn.setShow(_choiceTuDiShow.getShow()["reChoiceBtn"]);
            _reChoiceBtn.setTipString("重新选择角色");
         },null);
      }
      
      private function gotoMapLabel() : void
      {
         _showMovieClip.gotoAndStop("map",function():void
         {
            if(_startGameBtn)
            {
               _startGameBtn.clear();
            }
            _startGameBtn = null;
            if(_reChoiceBtn)
            {
               _reChoiceBtn.clear();
            }
            _reChoiceBtn = null;
            if(_choiceTuDiShow)
            {
               _choiceTuDiShow.gotoAndPlay("removeStart",null,null,function():void
               {
                  if(Boolean(_choiceTuDiShow) && _choiceTuDiShow.getShow().parent)
                  {
                     _choiceTuDiShow.getShow().parent.removeChild(_choiceTuDiShow.getShow());
                  }
                  _choiceTuDiShow = null;
               },null);
            }
            _chengAnLou = new ButtonLogicShell();
            _chengAnLou.setShow(_show["changAnChengLou"]);
         },null);
      }
      
      private function gotoChoiceDifficultyShow(param1:String, param2:String) : void
      {
         var label:String = param1;
         var choiceDifficultyShowName:String = param2;
         _showMovieClip.gotoAndStop(label,function():void
         {
            clearLevelBtn();
            _choiceDifficultyShow = new MovieClipPlayLogicShell();
            _choiceDifficultyShow.setShow(_show[choiceDifficultyShowName]);
            choiceDifficultyShowGotoPlayStart();
         },null);
      }
      
      private function gotoGameUI() : void
      {
         var initSystemBtnAndHideQuitBtn:* = function():void
         {
            if(quitBtn)
            {
               quitBtn.visible = false;
            }
            if(_systemBtn)
            {
               _systemBtn.clear();
            }
            _systemBtn = new ButtonLogicShell();
            _systemBtn.setShow(_show["systemBtn"]);
            _systemBtn.setTipString("打开系统设置");
         };
         var initBar1s:* = function():void
         {
            if(_bloodBar1)
            {
               _bloodBar1.clear();
            }
            if(_energyBar1)
            {
               _energyBar1.clear();
            }
            if(_xiuLianValueBar1)
            {
               _xiuLianValueBar1.clear();
            }
            if(_touXiang1)
            {
               _touXiang1.clear();
            }
            _bloodBar1 = new CMSXChangeBarLogicShell();
            _bloodBar1.setShow(_show["bloodBar1"]);
            _energyBar1 = new CMSXChangeBarLogicShell();
            _energyBar1.setShow(_show["energyBar1"]);
            _xiuLianValueBar1 = new CMSXChangeBarLogicShell();
            _xiuLianValueBar1.setShow(_show["xiuLiuValueBar1"]);
            _touXiang1 = new MovieClipPlayLogicShell();
            _touXiang1.setShow(_show["touXiang1"]);
            _touXiang1.gotoAndStop(GamingUI.getInstance().player1.playerVO.tuDiVO.type);
            if(_skillSpaces1 == null)
            {
               _skillSpaces1 = new Vector.<MovieClipPlayLogicShell>();
            }
            else
            {
               ClearUtil.nullArr(_skillSpaces1);
            }
            if(_skillShows1 == null)
            {
               _skillShows1 = new Vector.<MovieClipPlayLogicShell>();
            }
            else
            {
               ClearUtil.nullArr(_skillShows1);
            }
            var _loc1_:MovieClipPlayLogicShell = new MovieClipPlayLogicShell();
            _loc1_.setShow(_show["skillSpace11"]);
            _skillSpaces1.push(_loc1_);
            _loc1_ = new MovieClipPlayLogicShell();
            _loc1_.setShow(_show["skillSpace12"]);
            _skillSpaces1.push(_loc1_);
            initSkillShow(GamingUI.getInstance().player1.playerVO,_skillSpaces1,_skillShows1);
         };
         var initBar2s:* = function():void
         {
            if(_bloodBar2)
            {
               _bloodBar2.clear();
            }
            if(_energyBar2)
            {
               _energyBar2.clear();
            }
            if(_xiuLianValueBar2)
            {
               _xiuLianValueBar2.clear();
            }
            if(_touXiang2)
            {
               _touXiang2.clear();
            }
            _bloodBar2 = new CMSXChangeBarLogicShell();
            _bloodBar2.setShow(_show["bloodBar2"]);
            _energyBar2 = new CMSXChangeBarLogicShell();
            _energyBar2.setShow(_show["energyBar2"]);
            _xiuLianValueBar2 = new CMSXChangeBarLogicShell();
            _xiuLianValueBar2.setShow(_show["xiuLianValueBar2"]);
            _touXiang2 = new MovieClipPlayLogicShell();
            _touXiang2.setShow(_show["touXiang2"]);
            _touXiang2.gotoAndStop(GamingUI.getInstance().player2.playerVO.tuDiVO.type);
            if(_skillSpaces2 == null)
            {
               _skillSpaces2 = new Vector.<MovieClipPlayLogicShell>();
            }
            else
            {
               ClearUtil.nullArr(_skillSpaces1);
            }
            if(_skillShows2 == null)
            {
               _skillShows2 = new Vector.<MovieClipPlayLogicShell>();
            }
            else
            {
               ClearUtil.nullArr(_skillShows2);
            }
            var _loc1_:MovieClipPlayLogicShell = new MovieClipPlayLogicShell();
            _loc1_.setShow(_show["skillSpace21"]);
            _skillSpaces2.push(_loc1_);
            _loc1_ = new MovieClipPlayLogicShell();
            _loc1_.setShow(_show["skillSpace22"]);
            _skillSpaces2.push(_loc1_);
            initSkillShow(GamingUI.getInstance().player2.playerVO,_skillSpaces2,_skillShows2);
         };
         var initSkillShow:* = function(param1:PlayerVO, param2:Vector.<MovieClipPlayLogicShell>, param3:Vector.<MovieClipPlayLogicShell>):void
         {
            var _loc7_:int = 0;
            var _loc4_:MovieClipPlayLogicShell = null;
            var _loc5_:Vector.<TuDiSkillVO> = param1.tuDiVO.skillVOs;
            var _loc6_:int = _loc5_ ? _loc5_.length : 0;
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               _loc4_ = new MovieClipPlayLogicShell();
               _loc4_.setShow(MyFunction2.returnShowByClassName(param1.tuDiVO.skillVOs[_loc7_].className) as MovieClip);
               param3.push(_loc4_);
               param2[_loc7_].getShow().addChildAt(_loc4_.getShow(),0);
               param2[_loc7_].gotoAndStop("k" + MyFunction2.returnTuDiKeyStrBySerialNumber(param1.tuDiVO.skillVOs[_loc7_].serialNumber,param1));
               _loc7_++;
            }
         };
         addEventListener("enterFrame",render,false);
         if(GamingUI.getInstance().player2)
         {
            _showMovieClip.gotoAndStop("gameUI_2",function():void
            {
               initSystemBtnAndHideQuitBtn();
               initBar1s();
               initBar2s();
            },null);
         }
         else
         {
            _showMovieClip.gotoAndStop("gameUI_1",function():void
            {
               initSystemBtnAndHideQuitBtn();
               initBar1s();
            },null);
         }
      }
      
      private function clearLevelBtn() : void
      {
         if(_chengAnLou)
         {
            _chengAnLou.clear();
         }
         _chengAnLou = null;
      }
      
      private function choiceDifficultyShowGotoPlayStart() : void
      {
         _choiceDifficultyShow.gotoAndPlay("start",function():void
         {
            if(_normalLevelBtn)
            {
               _normalLevelBtn.clear();
            }
            if(_middleLevelBtn)
            {
               _middleLevelBtn.clear();
            }
            if(_highLevelBtn)
            {
               _highLevelBtn.clear();
            }
            if(_devilLevellBtn)
            {
               _devilLevellBtn.clear();
            }
            _normalLevelBtn = new ButtonLogicShell2();
            _normalLevelBtn.setShow(_choiceDifficultyShow.getShow()["normalLevel"]);
            _normalLevelBtn.setTipString("点击开始游戏");
            _middleLevelBtn = new ButtonLogicShell2();
            _middleLevelBtn.setShow(_choiceDifficultyShow.getShow()["middleLevel"]);
            _middleLevelBtn.setTipString("点击开始游戏");
            _highLevelBtn = new ButtonLogicShell2();
            _highLevelBtn.setShow(_choiceDifficultyShow.getShow()["highLevel"]);
            _highLevelBtn.setTipString("点击开始游戏");
            _devilLevellBtn = new ButtonLogicShell2();
            _devilLevellBtn.setShow(_choiceDifficultyShow.getShow()["devilLevel"]);
            _devilLevellBtn.setTipString("点击开始游戏");
            _choiceShowQuitBtn = new ButtonLogicShell2();
            _choiceShowQuitBtn.setShow(_choiceDifficultyShow.getShow()["quitBtn3"]);
            _choiceShowQuitBtn.setTipString("点击关闭");
            analysisIsOpenDifficulty();
         },null);
      }
      
      private function choiceDefficultyShowGotoPlayEnd() : void
      {
         if(_choiceDifficultyShow)
         {
            _choiceDifficultyShow.gotoAndPlay("end",null,null,function():void
            {
               if(_normalLevelBtn)
               {
                  _normalLevelBtn.clear();
               }
               if(_middleLevelBtn)
               {
                  _middleLevelBtn.clear();
               }
               if(_highLevelBtn)
               {
                  _highLevelBtn.clear();
               }
               if(_devilLevellBtn)
               {
                  _devilLevellBtn.clear();
               }
               _normalLevelBtn = null;
               _middleLevelBtn = null;
               _highLevelBtn = null;
               _devilLevellBtn = null;
               gotoMapLabel();
            },null);
         }
      }
      
      private function analysisIsOpenDifficulty() : void
      {
         var _loc2_:XML = _shiTuMap.level.(@name == _currentLevelName)[0];
         if(_loc2_ == null)
         {
            _normalLevelBtn.lock();
            _middleLevelBtn.lock();
            _highLevelBtn.lock();
            _devilLevellBtn.lock();
            return;
         }
         var _loc1_:int = Math.max(GamingUI.getInstance().player1.playerVO.tuDiVO.level,GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO.tuDiVO.level : 0);
         if(_loc1_ < int(_loc2_.difficulty.(@level == "normalLevel")[0].@needLevel))
         {
            _normalLevelBtn.lock();
         }
         if(_loc1_ < int(_loc2_.difficulty.(@level == "middleLevel")[0].@needLevel))
         {
            _middleLevelBtn.lock();
         }
         if(_loc1_ < int(_loc2_.difficulty.(@level == "heightLevel")[0].@needLevel))
         {
            _highLevelBtn.lock();
         }
         if(_loc1_ < int(_loc2_.difficulty.(@level == "devilLevel")[0].@needLevel))
         {
            _devilLevellBtn.lock();
         }
      }
      
      private function render(param1:Event) : void
      {
         var tuDiVO1:TuDiVO;
         var tuDiVO2:TuDiVO;
         var e:Event = param1;
         var setSkillShow:* = function(param1:Vector.<MovieClipPlayLogicShell>, param2:TuDiVO):void
         {
            var _loc5_:int = 0;
            var _loc3_:TuDiSkillVO = null;
            var _loc4_:int = param2.skillVOs ? param2.skillVOs.length : 0;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc3_ = param2.skillVOs[_loc5_];
               if(_loc3_.level == 0)
               {
                  param1[_loc5_].gotoAndStop("notHave");
               }
               else if(_loc3_.requireEnergy > param2.energyPercent * param2.energy)
               {
                  param1[_loc5_].gotoAndStop("lack");
               }
               else
               {
                  param1[_loc5_].gotoAndStop("normal");
               }
               _loc5_++;
            }
         };
         if(_isStop)
         {
            return;
         }
         tuDiVO1 = GamingUI.getInstance().player1.playerVO.tuDiVO;
         tuDiVO2 = GamingUI.getInstance().player2 ? GamingUI.getInstance().player2.playerVO.tuDiVO : null;
         if(_bloodBar1)
         {
            _bloodBar1.change(tuDiVO1.bloodPercent);
            _bloodBar1.setDataShow(Math.round(tuDiVO1.bloodPercent * tuDiVO1.blood) + "/" + tuDiVO1.blood);
         }
         if(_energyBar1)
         {
            _energyBar1.change(tuDiVO1.energyPercent);
            _energyBar1.setDataShow(Math.round(tuDiVO1.energyPercent * tuDiVO1.energy) + "/" + tuDiVO1.energy);
         }
         if(_xiuLianValueBar1)
         {
            _xiuLianValueBar1.change(tuDiVO1.currentXiuLianValue / tuDiVO1.xiuLianValue);
            _xiuLianValueBar1.setDataShow(tuDiVO1.currentXiuLianValue + "/" + tuDiVO1.xiuLianValue);
         }
         if(_bloodBar2)
         {
            _bloodBar2.change(tuDiVO2.bloodPercent);
            _bloodBar2.setDataShow(Math.round(tuDiVO2.bloodPercent * tuDiVO2.blood) + "/" + tuDiVO2.blood);
         }
         if(_energyBar2)
         {
            _energyBar2.change(tuDiVO2.energyPercent);
            _energyBar2.setDataShow(Math.round(tuDiVO2.energyPercent * tuDiVO2.energy) + "/" + tuDiVO2.energy);
         }
         if(_xiuLianValueBar2)
         {
            _xiuLianValueBar2.change(tuDiVO2.currentXiuLianValue / tuDiVO2.xiuLianValue);
            _xiuLianValueBar2.setDataShow(tuDiVO2.currentXiuLianValue + "/" + tuDiVO2.xiuLianValue);
         }
         if(_skillShows1)
         {
            setSkillShow(_skillShows1,tuDiVO1);
         }
         if(_skillShows2)
         {
            setSkillShow(_skillShows2,tuDiVO2);
         }
         recoverPlayerEnergy(tuDiVO1,tuDiVO2);
      }
      
      private function recoverPlayerEnergy(param1:TuDiVO, param2:TuDiVO) : void
      {
         if(isNaN(_oldTime))
         {
            _oldTime = new Date().getTime();
         }
         var _loc3_:Number = Number(new Date().getTime());
         var _loc4_:Number = (_loc3_ - _oldTime) / 1000;
         _oldTime = _loc3_;
         if(param1)
         {
            param1.energyPercent = Math.max(0,Math.min((param1.energyPercent * param1.energy + param1.energyRecover * _loc4_) / param1.energy,1));
         }
         if(param2)
         {
            param2.energyPercent = Math.max(0,Math.min((param2.energyPercent * param2.energy + param2.energyRecover * _loc4_) / param2.energy,1));
         }
      }
      
      public function stopGame() : void
      {
         _isStop = true;
         _oldTime = NaN;
      }
      
      public function continueGame() : void
      {
         _isStop = false;
         _oldTime = new Date().getTime();
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

