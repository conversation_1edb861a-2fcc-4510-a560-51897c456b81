package UI.DirtyWordFilter
{
   import flash.utils.Dictionary;
   
   public class Dirty<PERSON>ordData
   {
      
      private var _contents:Array = null;
      
      private var _keys:Dictionary = null;
      
      public function DirtyWordData()
      {
         super();
         init();
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         if(_keys)
         {
            for(var _loc2_ in _keys)
            {
               delete _keys[_loc2_];
            }
         }
         _keys = null;
         var _loc3_:int = 0;
         if(_contents)
         {
            _loc1_ = int(_contents.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _contents[_loc3_] = null;
               _loc3_++;
            }
         }
         _contents = null;
      }
      
      public function getContentByKey(param1:Object) : Array
      {
         return _keys[param1];
      }
      
      public function put(param1:Object, param2:Object) : void
      {
         var _loc4_:Array = null;
         var _loc3_:int = 0;
         if(!_keys[param1])
         {
            _keys[param1] = [];
            _contents.push(_keys[param1]);
            _keys[param1].push(param2);
         }
         else
         {
            _loc4_ = _keys[param1];
            _loc3_ = int(_loc4_.indexOf(param2));
            if(param2 > -1)
            {
               _loc4_.splice(_loc3_,1);
            }
            _loc4_.push(param2);
         }
      }
      
      private function init() : void
      {
         _keys = new Dictionary();
         _contents = [];
      }
   }
}

