package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.Players.Player;
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PK2Listener;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MatchInforShow
   {
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_myPlayersPKInforShow:PlayersPKInforShow;
      
      private var m_startMatchBtn:ButtonLogicShell2;
      
      private var m_foePlayersPKInforShow:PlayersPKInforShow;
      
      private var m_startPKBtn:ButtonLogicShell2;
      
      private var m_reMatchBtn:ButtonLogicShell2;
      
      private var m_pk2Listener:PK2Listener;
      
      private var m_pkType:String;
      
      private var m_show:MovieClip;
      
      private var m_myPlayer1:Player;
      
      private var m_myPlayer2:Player;
      
      private var m_foePlayer1:Player;
      
      private var m_foePlayer2:Player;
      
      private var m_pk:PK2;
      
      public function MatchInforShow()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         m_myPlayersPKInforShow = new PlayersPKInforShow();
         m_pk2Listener = new PK2Listener();
         m_pk2Listener.matchCompleteFun = matchComplete;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         if(m_pk)
         {
            m_pk.removePK2Listener(m_pk2Listener);
         }
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_myPlayersPKInforShow);
         m_myPlayersPKInforShow = null;
         ClearUtil.clearObject(m_startMatchBtn);
         m_startMatchBtn = null;
         ClearUtil.clearObject(m_foePlayersPKInforShow);
         m_foePlayersPKInforShow = null;
         ClearUtil.clearObject(m_startPKBtn);
         m_startPKBtn = null;
         ClearUtil.clearObject(m_reMatchBtn);
         m_reMatchBtn = null;
         ClearUtil.clearObject(m_pk2Listener);
         m_pk2Listener = null;
         m_pkType = null;
         m_show = null;
         m_myPlayer1 = null;
         m_myPlayer2 = null;
         m_foePlayer1 = null;
         m_foePlayer2 = null;
         m_pk = null;
      }
      
      public function init(param1:MovieClip, param2:PK2, param3:String) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         if(m_pk)
         {
            m_pk.removePK2Listener(m_pk2Listener);
         }
         m_pk = param2;
         m_pkType = param3;
         if(m_pk)
         {
            m_pk.addPK2Listener(m_pk2Listener);
         }
         initShow();
      }
      
      public function setMyPlayers(param1:Player, param2:Player, param3:uint) : void
      {
         m_myPlayer1 = param1;
         m_myPlayer2 = param2;
         initShow2();
         if(m_myPlayersPKInforShow)
         {
            m_myPlayersPKInforShow.setPlayersAndOtherData(param1,param2,param3);
         }
      }
      
      private function setFoePlayers(param1:Player, param2:Player, param3:uint) : void
      {
         m_foePlayer1 = param1;
         m_foePlayer2 = param2;
         initShow2();
         if(m_foePlayersPKInforShow)
         {
            m_foePlayersPKInforShow.setPlayersAndOtherData(param1,param2,param3);
         }
      }
      
      private function showMatchingFrame() : void
      {
         initMatchingFrame();
      }
      
      private function matchComplete(param1:PK2) : void
      {
         setFoePlayers(m_pk.getFoeUIPlayer1(),m_pk.getFoeUIPlayer2(),m_pk.getMyPkTargetPlayerData().getUserDataInRank().getRank());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_startMatchBtn:
               m_pk.startMatch();
               showMatchingFrame();
               break;
            case m_startPKBtn:
               m_pk.startPK(0);
               break;
            case m_reMatchBtn:
               m_pk.startMatch();
               showMatchingFrame();
         }
      }
      
      private function initShow() : void
      {
         m_showMC.setShow(m_show);
         m_myPlayersPKInforShow.init(m_show["myInforShow"],m_pkType);
      }
      
      private function initShow2() : void
      {
         if(m_foePlayer1 == null)
         {
            initStartMatchFrame();
         }
         else
         {
            initMatchCompleteFrame();
         }
      }
      
      private function initStartMatchFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("startMatch");
         m_startMatchBtn = new ButtonLogicShell2();
         m_startMatchBtn.setShow(m_show["startMatchBtn"]);
      }
      
      private function initMatchingFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("matching");
      }
      
      private function initMatchCompleteFrame() : void
      {
         frameClear();
         m_showMC.gotoAndStop("matchComplete");
         m_startPKBtn = new ButtonLogicShell2();
         m_startPKBtn.setShow(m_show["startPKBtn"]);
         m_reMatchBtn = new ButtonLogicShell2();
         m_reMatchBtn.setShow(m_show["reMatchBtn"]);
         m_foePlayersPKInforShow = new PlayersPKInforShow();
         m_foePlayersPKInforShow.init(m_show["foeInforShow"],m_pkType);
      }
      
      private function frameClear() : void
      {
         ClearUtil.clearObject(m_startMatchBtn);
         m_startMatchBtn = null;
         ClearUtil.clearObject(m_startPKBtn);
         m_startPKBtn = null;
         ClearUtil.clearObject(m_reMatchBtn);
         m_reMatchBtn = null;
         ClearUtil.clearObject(m_foePlayersPKInforShow);
         m_foePlayersPKInforShow = null;
      }
   }
}

