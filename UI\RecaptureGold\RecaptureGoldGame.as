package UI.RecaptureGold
{
   import UI.Animation.AnimationObjectByOneBitmap.AnimationObjectByOneBitmap;
   import UI.Animation.AnimationObjectByOneBitmap.AnimationObjectDataByOneBitmap;
   import UI.GamingUI;
   import UI.KeyManager.Key;
   import UI.KeyManager.KeyCombo;
   import UI.KeyManager.KeyManager;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.Hook.HookMachine;
   import UI.RecaptureGold.Parent.ICatchTarget;
   import UI.RecaptureGold.UI.RGoldUI;
   import UI.SoundManager.SoundManager;
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Point;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class RecaptureGoldGame extends MySprite
   {
      
      private const _CHANGE_ANGLE_RATE:Number = 0.01;
      
      private var _recaptureGoldXML:XML;
      
      private var _rgPlayer1:RGPlayer;
      
      private var _rgPlayer2:RGPlayer;
      
      private var _hookMachine1:HookMachine;
      
      private var _hookMachine2:HookMachine;
      
      private var _wantCatchTargets:Vector.<ICatchTarget>;
      
      private var _rGoldUI:RGoldUI;
      
      private var _currentGameLevel:int;
      
      private var _levelMap:RecaptureGoldLevelMap;
      
      private var _oldLevelMap:RecaptureGoldLevelMap;
      
      private var _UILayer:Sprite;
      
      private var _gameLayer:Sprite;
      
      private var _backgroudLayer:Sprite;
      
      private var _backgound1:AnimationObjectByOneBitmap;
      
      private var _backgound2:AnimationObjectByOneBitmap;
      
      private var _keyManager:KeyManager;
      
      private var _keyCombo_Catch_1:KeyCombo;
      
      private var _keyCombo_Contract_1:KeyCombo;
      
      private var _keyCombo_UseBomb_1:KeyCombo;
      
      private var _keyCombo_Catch_2:KeyCombo;
      
      private var _keyCombo_Contract_2:KeyCombo;
      
      private var _keyCombo_UseBomb_2:KeyCombo;
      
      private var _levelGoldNumTarget:int;
      
      private var _isShake:Boolean;
      
      private var _intervalForShake:uint;
      
      private var _soundManager:SoundManager;
      
      private var _isPlayer1FlewIntoComplete:Boolean;
      
      private var _isPlayer2FlewIntoComplete:Boolean;
      
      public function RecaptureGoldGame(param1:SoundManager)
      {
         super();
         _soundManager = param1;
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(_UILayer)
         {
            while(_UILayer.numChildren > 0)
            {
               _loc2_ = _UILayer.getChildAt(0);
               _UILayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _UILayer = null;
         if(_gameLayer)
         {
            while(_gameLayer.numChildren > 0)
            {
               _loc2_ = _gameLayer.getChildAt(0);
               _gameLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _gameLayer = null;
         if(_backgroudLayer)
         {
            while(_backgroudLayer.numChildren > 0)
            {
               _loc2_ = _backgroudLayer.getChildAt(0);
               _backgroudLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _backgroudLayer = null;
         _recaptureGoldXML = null;
         if(_rgPlayer1)
         {
            _rgPlayer1.clear();
         }
         _rgPlayer1 = null;
         if(_rgPlayer2)
         {
            _rgPlayer2.clear();
         }
         _rgPlayer2 = null;
         if(_hookMachine1)
         {
            _hookMachine1.clear();
         }
         _hookMachine1 = null;
         if(_hookMachine2)
         {
            _hookMachine2.clear();
         }
         _hookMachine2 = null;
         _loc1_ = _wantCatchTargets ? _wantCatchTargets.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(_wantCatchTargets[_loc3_])
            {
               _wantCatchTargets[_loc3_].clear();
            }
            _wantCatchTargets[_loc3_] = null;
            _loc3_++;
         }
         _wantCatchTargets = null;
         if(_rGoldUI)
         {
            _rGoldUI.clear();
         }
         _rGoldUI = null;
         if(_levelMap)
         {
            _levelMap.clear();
         }
         _levelMap = null;
         if(_oldLevelMap)
         {
            _oldLevelMap.clear();
         }
         _oldLevelMap = null;
         if(_backgound1)
         {
            _backgound1.clear();
         }
         _backgound1 = null;
         if(_backgound2)
         {
            _backgound2.clear();
         }
         _backgound2 = null;
         if(_keyManager)
         {
            _keyManager.clear();
         }
         _keyManager = null;
         if(_keyCombo_Catch_1)
         {
            _keyCombo_Catch_1.clear();
         }
         _keyCombo_Catch_1 = null;
         if(_keyCombo_Contract_1)
         {
            _keyCombo_Contract_1.clear();
         }
         _keyCombo_Contract_1 = null;
         if(_keyCombo_UseBomb_1)
         {
            _keyCombo_UseBomb_1.clear();
         }
         _keyCombo_UseBomb_1 = null;
         if(_keyCombo_Catch_2)
         {
            _keyCombo_Catch_2.clear();
         }
         _keyCombo_Catch_2 = null;
         if(_keyCombo_Contract_2)
         {
            _keyCombo_Contract_2.clear();
         }
         _keyCombo_Contract_2 = null;
         if(_keyCombo_UseBomb_2)
         {
            _keyCombo_UseBomb_2.clear();
         }
         _keyCombo_UseBomb_2 = null;
         clearInterval(_intervalForShake);
         _soundManager = null;
         RecaptureGoldData.getInstance().allGoldNum = 0;
         RecaptureGoldData.getInstance().catchItemNum = 0;
      }
      
      public function stopGame() : void
      {
         _hookMachine1.stop();
         if(_hookMachine2)
         {
            _hookMachine2.stop();
         }
         if(_rGoldUI)
         {
            _rGoldUI.stopConutDown();
         }
         _backgound1.stop();
         _backgound2.stop();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("recaptureGoldGameEnd",gameEnd,true,0,true);
         addEventListener("refreshUI",refreshUI,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("recaptureGoldGameEnd",gameEnd,true);
         removeEventListener("refreshUI",refreshUI,true);
      }
      
      private function refreshUI(param1:RecaptureGoldEvent) : void
      {
         _rGoldUI.changeGoldNum(param1.data.allGoldNum);
      }
      
      private function gameEnd(param1:RecaptureGoldEvent) : void
      {
         _hookMachine1.stop();
         if(_hookMachine2)
         {
            _hookMachine2.stop();
         }
         if(_rGoldUI)
         {
            _rGoldUI.stopConutDown();
         }
         _backgound1.stop();
         _backgound2.stop();
         _rgPlayer1.playerPropVO.removeOneGameLevelProp();
         refreshProps(_recaptureGoldXML);
         if(RecaptureGoldData.getInstance().allGoldNum < _levelGoldNumTarget)
         {
            _soundManager.play(String(_recaptureGoldXML.SoundData[0].@failPassLevelSound),0,0,soundTransform);
            (parent as RecaptureGoldMain).showPassLevelFail();
         }
         else
         {
            _soundManager.play(String(_recaptureGoldXML.SoundData[0].@successPassLevelSound),0,0,soundTransform);
            (parent as RecaptureGoldMain).showPassLevel();
         }
      }
      
      public function init(param1:XML) : void
      {
         _recaptureGoldXML = param1;
         initGameBackgroud();
         initGame();
      }
      
      public function set recaptureGoldXML(param1:XML) : void
      {
         _recaptureGoldXML = param1;
      }
      
      public function nextLevel() : void
      {
         var levelId:int;
         var game:RecaptureGoldGame;
         var length:int = int(_wantCatchTargets.length);
         var i:int = 0;
         while(i < length)
         {
            _wantCatchTargets[i] = null;
            i++;
         }
         _currentGameLevel += 1;
         if(_currentGameLevel >= int(_recaptureGoldXML.GameLevelData[0].LevelNum[0].@maxLevel))
         {
            levelId = Math.ceil(Math.random() * int(_recaptureGoldXML.GameLevelData[0].LevelNum[0].@maxLevel));
         }
         else
         {
            levelId = _currentGameLevel;
         }
         _oldLevelMap = _levelMap;
         _levelMap = new RecaptureGoldLevelMap();
         _levelMap.x = 960;
         _levelMap.y = 0;
         _gameLayer.addChild(_levelMap);
         _levelMap.setLevelNum(levelId,_recaptureGoldXML);
         wantCatchTargets = _levelMap.getAllCatchTarget();
         initUI();
         _rgPlayer1.rgPlayerVO.physicalStrength = Number(_recaptureGoldXML.PlayerData.@normalPhysicalStrength);
         if(_rgPlayer2)
         {
            _rgPlayer2.rgPlayerVO.physicalStrength = Number(_recaptureGoldXML.PlayerData.@normalPhysicalStrength);
         }
         _hookMachine1.rgPlayer = _rgPlayer1;
         if(_hookMachine2)
         {
            _hookMachine2.rgPlayer = _rgPlayer2;
         }
         game = this;
         TweenLite.to(_oldLevelMap,2,{
            "x":-960,
            "ease":Linear.easeNone
         });
         TweenLite.to(_levelMap,2,{
            "x":0,
            "ease":Linear.easeNone,
            "onComplete":function():void
            {
               _hookMachine1.reStart();
               if(_hookMachine2)
               {
                  _hookMachine2.reStart();
               }
               _rGoldUI.startCountDown();
               _backgound1.play();
               _backgound2.play();
               stage.focus = game;
               if(_oldLevelMap)
               {
                  TweenLite.killTweensOf(_oldLevelMap);
                  _gameLayer.removeChild(_oldLevelMap);
                  _oldLevelMap.clear();
                  _oldLevelMap = null;
               }
            }
         });
         refreshProps(_recaptureGoldXML);
      }
      
      public function refreshProps(param1:XML) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:* = null;
         if(Boolean(_rgPlayer1) && _rgPlayer1.playerPropVO)
         {
            _rGoldUI.setPropState(_rgPlayer1.playerPropVO.judeIsHaveBomb(),_rgPlayer1.playerPropVO.judeIsHaveDiamondToBook() ? 1 : 0,_rgPlayer1.playerPropVO.judeIsHaveLuckGrass() ? 1 : 0,_rgPlayer1.playerPropVO.judeIsHaveStrengthPotion() ? 1 : 0);
         }
         else
         {
            _rGoldUI.setPropState(0,0,0,0);
         }
      }
      
      public function shakeShow(param1:uint = 4, param2:uint = 4, param3:uint = 32) : void
      {
         var point:Point;
         var offsetXYArray:Array;
         var num:int;
         var times:uint = param1;
         var offset:uint = param2;
         var speed:uint = param3;
         if(_isShake)
         {
            return;
         }
         _isShake = true;
         point = new Point(_gameLayer.x,_gameLayer.y);
         offsetXYArray = [0,0];
         num = 0;
         _intervalForShake = setInterval(function():void
         {
            offsetXYArray[num % 2] = num++ % 4 < 2 ? 0 : offset;
            if(num > times * 4 + 1)
            {
               clearInterval(_intervalForShake);
               num = 0;
               _isShake = false;
            }
            _gameLayer.x = offsetXYArray[0] + point.x;
            _gameLayer.y = offsetXYArray[1] + point.y;
         },speed);
      }
      
      private function startGame() : void
      {
         if(!_isPlayer1FlewIntoComplete || !(_isPlayer2FlewIntoComplete || !_rgPlayer2))
         {
            return;
         }
         _hookMachine1.startSwing();
         if(_hookMachine2)
         {
            _hookMachine2.startSwing();
         }
         _rGoldUI.startCountDown();
         stage.focus = this;
      }
      
      private function initGame() : void
      {
         var newPlayerPropVO:PlayerPropVO;
         _UILayer = new Sprite();
         _gameLayer = new Sprite();
         addChild(_gameLayer);
         addChild(_UILayer);
         newPlayerPropVO = new PlayerPropVO();
         _rgPlayer1 = initPlayer(newPlayerPropVO);
         _rgPlayer1.x = 400;
         _rgPlayer1.y = 0;
         addChild(_rgPlayer1);
         _rgPlayer1.playerType = GamingUI.getInstance().player1.playerVO.playerType;
         _hookMachine1 = new HookMachine(_recaptureGoldXML,this,_soundManager);
         _gameLayer.addChild(_hookMachine1);
         _hookMachine1.rgPlayer = _rgPlayer1;
         _rgPlayer1.playAnimation(_recaptureGoldXML,"flewInto",[{
            "eventType":"playerOver",
            "fun":function():void
            {
               _isPlayer1FlewIntoComplete = true;
               startGame();
            }
         }]);
         if(GamingUI.getInstance().player2)
         {
            _rgPlayer2 = initPlayer(newPlayerPropVO);
            _rgPlayer1.x = 350;
            _rgPlayer2.x = 450;
            _rgPlayer2.y = 0;
            addChild(_rgPlayer2);
            _rgPlayer2.playerType = GamingUI.getInstance().player2.playerVO.playerType;
            _hookMachine2 = new HookMachine(_recaptureGoldXML,this,_soundManager);
            _gameLayer.addChild(_hookMachine2);
            _hookMachine2.rgPlayer = _rgPlayer2;
            _hookMachine1.x = 390;
            _hookMachine1.y = 100;
            _hookMachine2.x = 490;
            _hookMachine2.y = 100;
            _rgPlayer2.playAnimation(_recaptureGoldXML,"flewInto",[{
               "eventType":"playerOver",
               "fun":function():void
               {
                  _isPlayer2FlewIntoComplete = true;
                  startGame();
               }
            }]);
         }
         else
         {
            _hookMachine1.x = 430;
            _hookMachine1.y = 100;
         }
         _levelMap = new RecaptureGoldLevelMap();
         _currentGameLevel = 1;
         _levelMap.setLevelNum(_currentGameLevel,_recaptureGoldXML);
         _gameLayer.addChild(_levelMap);
         wantCatchTargets = _levelMap.getAllCatchTarget();
         initkeys();
         initUI();
         refreshProps(_recaptureGoldXML);
      }
      
      private function initGameBackgroud() : void
      {
         if(!_backgroudLayer)
         {
            _backgroudLayer = new Sprite();
         }
         addChildAt(_backgroudLayer,0);
         var _loc2_:Class = MyFunction2.returnClassByClassName(_recaptureGoldXML.OtherData.Background.@background1);
         var _loc1_:Class = MyFunction2.returnClassByClassName(_recaptureGoldXML.OtherData.Background.@background2);
         var _loc5_:BitmapData = new _loc2_();
         var _loc4_:BitmapData = new _loc1_();
         var _loc3_:AnimationObjectDataByOneBitmap = new AnimationObjectDataByOneBitmap();
         var _loc6_:AnimationObjectDataByOneBitmap = new AnimationObjectDataByOneBitmap();
         _loc3_.delay = 50;
         _loc3_.direction = 1;
         _loc3_.moveDistance = Number(_recaptureGoldXML.OtherData.Background.@speed1);
         _loc3_.sourceBitmapData = _loc5_;
         _loc3_.width = 960;
         _loc3_.height = 560;
         _loc6_.delay = 50;
         _loc6_.direction = 1;
         _loc6_.moveDistance = Number(_recaptureGoldXML.OtherData.Background.@speed2);
         _loc6_.sourceBitmapData = _loc4_;
         _loc6_.width = 960;
         _loc6_.height = 560;
         _backgound1 = new AnimationObjectByOneBitmap();
         _backgroudLayer.addChild(_backgound1);
         _backgound1.animationData = _loc3_;
         _backgound1.play();
         _backgound2 = new AnimationObjectByOneBitmap();
         _backgroudLayer.addChild(_backgound2);
         _backgound2.animationData = _loc6_;
         _backgound2.play();
      }
      
      private function initUI() : void
      {
         if(!_rGoldUI)
         {
            _rGoldUI = new RGoldUI();
         }
         if(!_UILayer.getChildByName(_rGoldUI.name))
         {
            _UILayer.addChild(_rGoldUI);
         }
         var _loc2_:Number = MyFunction.getInstance().excreteString(_recaptureGoldXML.GameLevelData[0].LevelTimeLimit[0].@limitMaxTime)[0];
         var _loc7_:Number = MyFunction.getInstance().excreteString(_recaptureGoldXML.GameLevelData[0].LevelTimeLimit[0].@limitMinTime)[0];
         var _loc9_:Number = _loc7_ + Math.round(Math.random() * (_loc2_ - _loc7_));
         var _loc1_:Vector.<Number> = MyFunction.getInstance().excreteStringToNumber(_recaptureGoldXML.GameLevelData[0].LevelGoldNumTarget[0].@multi);
         var _loc5_:Number = _loc1_[0] + Math.round(Math.random() * (_loc1_[1] - _loc1_[0]));
         var _loc4_:Vector.<Number> = MyFunction.getInstance().excreteStringToNumber(_recaptureGoldXML.GameLevelData[0].LevelGoldNumTarget[0].@multi2);
         var _loc3_:Number = _loc4_[0] + Math.round(Math.random() * (_loc4_[1] - _loc4_[0]));
         var _loc6_:Vector.<Number> = MyFunction.getInstance().excreteStringToNumber(_recaptureGoldXML.GameLevelData[0].LevelGoldNumTarget[0].@add);
         var _loc8_:Number = _loc6_[0] + Math.round(Math.random() * (_loc6_[1] - _loc6_[0]));
         _levelGoldNumTarget = _loc3_ * _currentGameLevel * (_currentGameLevel - 1) + _loc5_ * _currentGameLevel + _loc8_;
         _rGoldUI.initUIData(_loc9_,_currentGameLevel,RecaptureGoldData.getInstance().allGoldNum,_levelGoldNumTarget);
      }
      
      private function initPlayer(param1:PlayerPropVO) : RGPlayer
      {
         var _loc2_:RGPlayerVO = new RGPlayerVO();
         _loc2_.physicalStrength = Number(_recaptureGoldXML.PlayerData.@normalPhysicalStrength);
         _loc2_.GoldNum = 0;
         var _loc3_:RGPlayer = new RGPlayer(_loc2_,_recaptureGoldXML);
         _loc3_.playerPropVO = param1;
         return _loc3_;
      }
      
      private function set wantCatchTargets(param1:Vector.<ICatchTarget>) : void
      {
         _wantCatchTargets = param1;
         _hookMachine1.wanCatchTargets = param1;
         if(_hookMachine2)
         {
            _hookMachine2.wanCatchTargets = param1;
         }
      }
      
      public function get playerPropVO() : PlayerPropVO
      {
         return _rgPlayer1.playerPropVO;
      }
      
      private function initkeys() : void
      {
         if(_keyManager)
         {
            _keyManager.clear();
         }
         _keyManager = new KeyManager();
         _keyManager.addKeyEventListener(this.stage);
         _keyCombo_Catch_1 = _keyManager.createKeyCombo(_hookMachine1.startCatch,null,null,null,null,null,Key.S);
         _keyCombo_UseBomb_1 = _keyManager.createKeyCombo(_hookMachine1.useBomb,null,null,null,null,null,Key.J);
         if(_hookMachine2)
         {
            _keyCombo_Catch_2 = _keyManager.createKeyCombo(_hookMachine2.startCatch,null,null,null,null,null,Key.DOWN);
            _keyCombo_UseBomb_2 = _keyManager.createKeyCombo(_hookMachine2.useBomb,null,null,null,null,null,Key.NUMERIC_KEYPAD_NUMBER_1);
         }
      }
   }
}

