package UI2.SVActivity.Show
{
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.ChengZhangJiHuaData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ChengZhangJiHuaPanel
   {
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_buyBtn:ButtonLogicShell2;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_show:MovieClip;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_chengZhangJiHuaData:ChengZhangJiHuaData;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      public function ChengZhangJiHuaPanel()
      {
         super();
         m_eqCells = new Vector.<Sprite>();
         m_buyBtn = new ButtonLogicShell2();
         m_getRewardBtn = new ButtonLogicShell2();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(m_buyBtn);
         m_buyBtn = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         m_show = null;
         m_svActivitySaveData = null;
         m_svActivityPanel = null;
         m_chengZhangJiHuaData = null;
         m_svActivityPanel = null;
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:ChengZhangJiHuaData, param4:SVActivityPanel) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_svActivitySaveData = param2;
         m_chengZhangJiHuaData = param3;
         m_svActivityPanel = param4;
         initShow();
         initShow2();
         initShow3();
      }
      
      private function initShow() : void
      {
         var _loc3_:int = 0;
         m_buyBtn.setShow(m_show["buyBtn"]);
         m_getRewardBtn.setShow(m_show["getRewardBtn"]);
         var _loc1_:int = m_show.numChildren;
         var _loc2_:uint = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            if(m_show.getChildAt(_loc3_).name.substr(0,"eqCell".length) == "eqCell")
            {
               _loc2_++;
            }
            _loc3_++;
         }
         m_eqCells.length = 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc3_ + 1)]);
            _loc3_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc2_:Equipment = null;
         var _loc4_:int = 0;
         if(m_show == null || m_svActivitySaveData == null || m_chengZhangJiHuaData == null)
         {
            return;
         }
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         var _loc1_:int = int(m_chengZhangJiHuaData.getEquipmentVONum());
         var _loc3_:uint = m_eqCells.length;
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc2_ = MyFunction2.sheatheEquipmentShell(m_chengZhangJiHuaData.getEquipmentVOByIndex(_loc4_));
            _loc2_.addEventListener("rollOver",onOver2,false,0,true);
            _loc2_.addEventListener("rollOut",onOut2,false,0,true);
            if(_loc4_ < _loc3_)
            {
               m_eqCells[_loc4_].addChild(_loc2_);
            }
            m_equipments.push(_loc2_);
            _loc4_++;
         }
      }
      
      private function initShow3() : void
      {
         var _loc5_:String = null;
         var _loc4_:String = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc1_:String = null;
         var _loc6_:String = null;
         if(m_show == null || m_svActivitySaveData == null || m_chengZhangJiHuaData == null)
         {
            return;
         }
         if(m_svActivitySaveData.getIsBuy() == false)
         {
            _loc5_ = GamingUI.getInstance().getNewestTimeStrFromSever();
            _loc4_ = m_chengZhangJiHuaData.getStartTime();
            _loc2_ = m_chengZhangJiHuaData.getEndTime();
            if((!Boolean(_loc4_) || new TimeUtil().timeInterval(_loc4_,_loc5_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc5_,_loc2_) > 0))
            {
               initShow_notBuy();
            }
            else
            {
               m_svActivityPanel.showWarningBox("不在活动期间内!",0);
               initShow_notBuy();
            }
         }
         else if(m_svActivitySaveData.getIsAbleGetRewardOfChengZhangJiHua_newWeek(GamingUI.getInstance().getNewestTimeStrFromSever()))
         {
            _loc3_ = GamingUI.getInstance().getNewestTimeStrFromSever();
            _loc1_ = m_chengZhangJiHuaData.getStartTime();
            _loc6_ = m_chengZhangJiHuaData.getEndTime();
            if((!Boolean(_loc1_) || new TimeUtil().timeInterval(_loc1_,_loc3_) > 0) && (!Boolean(_loc6_) || new TimeUtil().timeInterval(_loc3_,_loc6_) > 0))
            {
               initShow_ableGetReward();
            }
            else
            {
               initShow_unableGetReward();
            }
         }
         else
         {
            initShow_unableGetReward();
         }
      }
      
      private function initShow_notBuy() : void
      {
         m_buyBtn.getShow().visible = true;
         m_getRewardBtn.getShow().visible = false;
      }
      
      private function initShow_ableGetReward() : void
      {
         m_buyBtn.getShow().visible = false;
         m_getRewardBtn.getShow().visible = true;
         MyFunction.getInstance().changeSaturation(m_getRewardBtn.getShow(),0);
         (m_getRewardBtn.getShow() as MovieClip).mouseEnabled = true;
         (m_getRewardBtn.getShow() as MovieClip).mouseChildren = true;
      }
      
      private function initShow_unableGetReward() : void
      {
         m_buyBtn.getShow().visible = false;
         m_getRewardBtn.getShow().visible = true;
         MyFunction.getInstance().changeSaturation(m_getRewardBtn.getShow(),-100);
         (m_getRewardBtn.getShow() as MovieClip).mouseEnabled = false;
         (m_getRewardBtn.getShow() as MovieClip).mouseChildren = false;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:String = null;
         var _loc3_:String = null;
         var _loc7_:String = null;
         var _loc6_:String = null;
         var _loc5_:String = null;
         var _loc2_:String = null;
         switch(param1.button)
         {
            case m_buyBtn:
               _loc4_ = GamingUI.getInstance().getNewestTimeStrFromSever();
               _loc3_ = m_chengZhangJiHuaData.getStartTime();
               _loc7_ = m_chengZhangJiHuaData.getEndTime();
               if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_loc4_) > 0) && (!Boolean(_loc7_) || new TimeUtil().timeInterval(_loc4_,_loc7_) > 0))
               {
                  buy();
               }
               else
               {
                  m_svActivityPanel.showWarningBox("不在活动时间内!",0);
               }
               break;
            case m_getRewardBtn:
               _loc6_ = GamingUI.getInstance().getNewestTimeStrFromSever();
               _loc5_ = m_chengZhangJiHuaData.getStartTime();
               _loc2_ = m_chengZhangJiHuaData.getEndTime();
               if((!Boolean(_loc5_) || new TimeUtil().timeInterval(_loc5_,_loc6_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc6_,_loc2_) > 0))
               {
                  getReward();
               }
               else
               {
                  m_svActivityPanel.showWarningBox("不在活动时间内!",0);
               }
         }
      }
      
      private function getReward() : void
      {
         if(m_getEquipmentVOsLogic.getEquipmentVOs(m_chengZhangJiHuaData,GamingUI.getInstance().player1))
         {
            m_svActivitySaveData.setNewTimeOfGetRewardOfChengZhangJiHua(GamingUI.getInstance().getNewestTimeStrFromSever());
            initShow3();
            m_svActivityPanel.showWarningBox("获取礼包成功",0);
         }
         else
         {
            m_svActivityPanel.showWarningBox("背包已满, 不能获取礼包",0);
         }
      }
      
      private function buy() : void
      {
         m_svActivityPanel.showWarningBox("是否花费" + m_chengZhangJiHuaData.getTicketPrice() + "点券购买暑期成长礼包",1 | 2,{
            "type":"buyChengZhangJiHua",
            "okFunction":buy2
         });
      }
      
      private function buy2() : void
      {
         var price:uint = m_chengZhangJiHuaData.getTicketPrice();
         var ticketId:String = m_chengZhangJiHuaData.getTicketPriceId();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买暑期成长礼包";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_svActivitySaveData.buy();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            initShow3();
         },m_svActivityPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

