package UI.UIConstant
{
   import UI.MyFont.FangZhengKaTongJianTi;
   
   public class UIConstantData
   {
      
      public static const NULL:String = "null";
      
      public static const CLOTHES:String = "clothes";
      
      public static const FASHION:String = "fashion";
      
      public static const GOURD:String = "gourd";
      
      public static const NECKLACE:String = "necklace";
      
      public static const WEAPON:String = "weapon";
      
      public static const SKILL_EQUIPMENT:String = "skillEquipment";
      
      public static const PET:String = "pet";
      
      public static const MATERIAL:String = "material";
      
      public static const PRECIOUS:String = "precious";
      
      public static const SCROLL:String = "scroll";
      
      public static const EGG:String = "egg";
      
      public static const POTION:String = "potion";
      
      public static const POCKET:String = "pocket";
      
      public static const GRASS:String = "grass";
      
      public static const FOREVER_FASHION:String = "forever_fashion";
      
      public static const DAN_MEDICINE:String = "danMedicine";
      
      public static const BUFF_EQUIPMENT:String = "buffEquipment";
      
      public static const MEDAL:String = "medal";
      
      public static const FORCE_DAN:String = "forceDan";
      
      public static const PET_SKILL_BOOK:String = "petSkillBook";
      
      public static const INSET_GEM:String = "insetGem";
      
      public static const DROP_EQ:String = "dropEq";
      
      public static const CONTRACT:String = "contract";
      
      public static const UNDETERMINED:String = "undeterminded";
      
      public static const PLAYER_PASSIVE:String = "playerPassive";
      
      public static const PLAYER_ACTIVE:String = "playerActive";
      
      public static const PET_ACTIVE:String = "petActive";
      
      public static const PET_PASSIVE:String = "petPassive";
      
      public static const PET_AWAKE_PASSIVE:String = "petAwakePassive";
      
      public static const ATTACK_PASSIVE:String = "attackPassive";
      
      public static const GUARD_PASSIVE:String = "guardPassive";
      
      public static const AUXILIARY_PASSIVE:String = "auxiliaryPassvie";
      
      public static const MONKEY:String = "SunWuKong";
      
      public static const DRAGON:String = "BaiLongMa";
      
      public static const ER_LANG_SHEN:String = "ErLangShen";
      
      public static const CHANG_E:String = "ChangE";
      
      public static const Fox:String = "Fox";
      
      public static const TIESHAN:String = "TieShan";
      
      public static const Houyi:String = "Houyi";
      
      public static const ZIXIA:String = "ZiXia";
      
      public static const NOTHING:String = "";
      
      public static const ATTACK:String = "attack";
      
      public static const DEFENCE:String = "defence";
      
      public static const AUXILIARY:String = "auxiliary";
      
      public static const SHOP_GOODS_NUMBER_ONE_PAGE:int = 4;
      
      public static const SHOP_START_PAGE_NUM:int = 1;
      
      public static const BUY_START_NUM:int = 0;
      
      public static const BUY_MAX_NUM:int = 100;
      
      public static const BUY_MIN_NUM:int = 0;
      
      public static const PACKAGE_WIDTH:int = 6;
      
      public static const PACKAGE_HEIGHT:int = 5;
      
      public static const PACKAGE_SART_PAGE_NUM:int = 1;
      
      public static const STORAGE_WIDTH:int = 6;
      
      public static const STORAGE_HEIGHT:int = 6;
      
      public static const PUBLIC_STORAGE_WIDTH:int = 6;
      
      public static const PUBLIC_STORAGE_HEIGHT:int = 4;
      
      public static const STORAGE_START_PAGE_NUM:int = 1;
      
      public static const SKILL_PANEL_START_NUM:int = 1;
      
      public static const SKILL_PANEL_NUMBER_ONE_PAGE:int = 4;
      
      public static const MESSAGE_DX:int = 20;
      
      public static const MESSAGE_DY:int = 20;
      
      public static const PACKAGE_IN_PANEL_X:Number = 500;
      
      public static const PACKAGE_IN_PANEL_Y:Number = 150;
      
      public static const STORAGE_IN_PANEL_X:Number = 60;
      
      public static const STORAGE_IN_PANEL_Y:Number = 143;
      
      public static const SHOP_IN_PANEL_X:Number = 10;
      
      public static const SHOP_IN_PANEL_Y:Number = 115;
      
      public static const INFORMATION_PANEL_X:Number = -30;
      
      public static const INFORMATION_PANEL_Y:Number = 110;
      
      public static const PLAYER_PANEL_IN_PANEL_X:Number = 20;
      
      public static const PLAYER_PANEL_IN_PANEL_Y:Number = 55;
      
      public static const SKILL_PANEL_IN_PANEL_X:Number = 35.25;
      
      public static const SKILL_PANEL_IN_PANEL_Y:Number = 60;
      
      public static const PET_PANEL_X:Number = 30;
      
      public static const PET_PANEL_Y:Number = 58.15;
      
      public static const TUDI_PANEL_X:Number = 63;
      
      public static const TUDI_PANEL_Y:Number = 140;
      
      public static const MAKE_UPGRADE_PANEL_X:Number = 0;
      
      public static const MAKE_UPGRADE_PANEL_Y:Number = 50;
      
      public static const EQUIP_MAGIC_PANEL_X:Number = 0;
      
      public static const EQUIP_MAGIC_PANEL_Y:Number = 50;
      
      public static const QUIT_BTN_X:Number = 915.8;
      
      public static const QUIT_BTN_Y:Number = -3.4;
      
      public static const PACKAGE_NAME:String = "package";
      
      public static const STORAGE_NAME:String = "storage";
      
      public static const SHOP_NAME:String = "shop";
      
      public static const PLAYER_PANEL_NAME:String = "playerInformationPanel";
      
      public static const SKILL_PANEL_NAME:String = "skillPanel";
      
      public static const MESSAGE_BOX_NAME:String = "messageBox";
      
      public static const LACK_MONEY_TEXT:String = "元宝不足";
      
      public static const DRAG_SELL_TEXT:String = "已卖出一个物品！";
      
      public static const SKILL_MAX_LEVEL:String = "skillMaxLevel";
      
      public static const ALL_SKILL_GRADE:int = 3;
      
      public static const SKILL_COLUMN_X:Number = 10.5;
      
      public static const SKILL_COLUMN_Y:Number = 116;
      
      public static const SKILL_CELL_LABEL_COLOR:int = 65280;
      
      public static const SKILL_CELL_LABEL_SIZE:int = 15;
      
      public static const SKILL_CELL_LABEL_FILTER_DISTANCE:int = 0;
      
      public static const SKILL_CELL_LABEL_FILTER_ANGLE:int = 0;
      
      public static const SKILL_CELL_LABEL_FILTER_COLOR:int = 0;
      
      public static const SKILL_CELL_LABEL_FILTER_ALPHA:int = 1;
      
      public static const SKILL_CELL_LABEL_FILTER_BLURX:int = 4;
      
      public static const SKILL_CELL_LABEL_FILTER_BLURY:int = 4;
      
      public static const SKILL_CELL_LABEL_FILTER_STRENGTH:int = 2000;
      
      public static const SKILL_CELL_LABEL_FILTER_QUALITY:int = 1;
      
      public static const SKILL_CELL_LABEL_X:Number = 9;
      
      public static const SKILL_CELL_LABEL_Y:Number = -25;
      
      public static const SKILL_CELL_TIME_COLOR:int = 16711680;
      
      public static const SKILL_CELL_TIME_SIZE:int = 40;
      
      public static const SKILL_CELL_TIME_X:Number = -2;
      
      public static const SKILL_CELL_TIME_Y:Number = -26;
      
      public static const EXTERNAL_PANEL_X:Number = 0;
      
      public static const EXTERNAL_PANEL_Y:Number = 0;
      
      public static const PLAYER_PANEL_ONE_X:Number = -1;
      
      public static const PLAYER_PANEL_ONE_Y:Number = -2;
      
      public static const PLAYER_PANEL_TWO_X:Number = 722;
      
      public static const PLAYER_PANEL_TWO_Y:Number = -2;
      
      public static const MIDDLE_PANEL_X:Number = 481.25;
      
      public static const MIDDLE_PANEL_Y:Number = 50;
      
      public static const MONKEY_PANEL_X:Number = 50;
      
      public static const MONKEY_PANEL_Y:Number = 115;
      
      public static const DRAGON_PANEL_X:Number = 50;
      
      public static const DRAGON_PANEL_Y:Number = 115;
      
      public static const SHOW_PANEL_CHILD_X:Number = 70;
      
      public static const SHOW_PANEL_CHILD_Y:Number = 150;
      
      public static const SHOW_PANEL_CHILD_SCALE_X:Number = 1.5;
      
      public static const SHOW_PANEL_CHILD_SCALE_Y:Number = 1.5;
      
      public static const A_KEY_SOLD_TASK:String = "aKeySoldTask";
      
      public static const BUY_CELL_TASK:String = "buyCellTask";
      
      public static const SURE_RECHARGE:String = "sureRecharge";
      
      public static const BUY_LAND:String = "buyLand";
      
      public static const BUY_ALL_LAND:String = "buyAllLand";
      
      public static const RECOVER_LAND:String = "recoverLand";
      
      public static const BUY_FARM_BLOCK:String = "buyFarmBlock";
      
      public static const SELL_VALUABLE_EQUIPMENT:String = "sellValuableEquipment";
      
      public static const START_REFINE:String = "startRefine";
      
      public static const START_REFINE2:String = "startRefine2";
      
      public static const AT_ONCE_COMPLETE_REFINE:String = "atOnceCompleteRefine";
      
      public static const FREE_CHANGE_NICKNAME:String = "freeChangeNickname";
      
      public static const BUY_TEHUILIBAO:String = "buyTeHuiLiBao";
      
      public static const CHANGE_RED_NICKNAME:String = "changeRedNickname";
      
      public static const CLEAR:String = "clear";
      
      public static const SKILL_CELL_LABEL_FONT:String = new FangZhengKaTongJianTi().fontName;
      
      public static const SKILL_CELL_TIME_FONT:String = new FangZhengKaTongJianTi().fontName;
      
      public static const ZHULONG_ID_ARRAY:Array = [10400122,10400222,10400322,10400423];
      
      public static const QIONGQI_ID_ARRAY:Array = [10400124,10400224,10400324,10400425];
      
      public function UIConstantData()
      {
         super();
      }
   }
}

