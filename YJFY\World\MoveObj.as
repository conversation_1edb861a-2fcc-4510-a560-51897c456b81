package YJFY.World
{
   public class MoveObj
   {
      
      private var m_isMove:Boolean;
      
      private var m_moveSpeed:Number;
      
      private var m_moveSpeedX:Number;
      
      private var m_moveSpeedY:Number;
      
      private var m_directionX_moveAngleCos:Number;
      
      private var m_directionY_moveAngleSin:Number;
      
      public function MoveObj()
      {
         super();
         m_isMove = false;
         m_moveSpeed = 0;
         m_moveSpeedX = 0;
         m_moveSpeedY = 0;
         m_directionX_moveAngleCos = 0;
         m_directionY_moveAngleSin = 0;
      }
      
      public function setMoveSpeed(param1:Number) : void
      {
         setMoveSpeed_in(param1);
      }
      
      public function setDirection(param1:Number, param2:Number) : void
      {
         var _loc3_:Number = NaN;
         if(param1 == 0 && param2 == 0)
         {
            m_directionX_moveAngleCos = 0;
            m_directionY_moveAngleSin = 0;
         }
         else
         {
            _loc3_ = Math.sqrt(Math.pow(param1,2) + Math.pow(param2,2));
            m_directionX_moveAngleCos = param1 / _loc3_;
            m_directionY_moveAngleSin = param2 / _loc3_;
         }
         setMoveSpeed_in(m_moveSpeed);
      }
      
      public function startMove() : void
      {
         m_isMove = true;
      }
      
      public function stopMove() : void
      {
         m_isMove = false;
      }
      
      public function getIsMove() : Boolean
      {
         return m_isMove;
      }
      
      public function getMoveSpeed() : Number
      {
         return m_moveSpeed;
      }
      
      public function getMoveSpeedX() : Number
      {
         return m_moveSpeedX;
      }
      
      public function getMoveSpeedY() : Number
      {
         return m_moveSpeedY;
      }
      
      public function getDirectionX_moveAngleCos() : Number
      {
         return m_directionX_moveAngleCos;
      }
      
      public function getDirectionY_moveAngleSin() : Number
      {
         return m_directionY_moveAngleSin;
      }
      
      private function setMoveSpeed_in(param1:Number) : void
      {
         m_moveSpeed = param1;
         m_moveSpeedX = m_moveSpeed * m_directionX_moveAngleCos;
         m_moveSpeedY = m_moveSpeed * m_directionY_moveAngleSin;
      }
   }
}

