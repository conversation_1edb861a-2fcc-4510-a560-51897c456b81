package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Players.Player;
   import YJFY.EntityShowContainer;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class PlayersPKInforShow
   {
      
      private var m_pkScoreInforText:TextField;
      
      private var m_pkRankText:TextField;
      
      private var m_player1ShowContainer:EntityShowContainer;
      
      private var m_player2ShowContainer:EntityShowContainer;
      
      private var m_rank:uint;
      
      private var m_pkType:String;
      
      private var m_show:MovieClip;
      
      private var m_showContainer:Sprite;
      
      private var m_player1:Player;
      
      private var m_player2:Player;
      
      public function PlayersPKInforShow()
      {
         super();
      }
      
      public function clear() : void
      {
         m_pkScoreInforText = null;
         m_pkRankText = null;
         ClearUtil.clearObject(m_player1ShowContainer);
         m_player1ShowContainer = null;
         ClearUtil.clearObject(m_player2ShowContainer);
         m_player2ShowContainer = null;
         m_pkType = null;
         m_show = null;
         m_showContainer = null;
         m_player1 = null;
         m_player2 = null;
      }
      
      public function init(param1:MovieClip, param2:String) : void
      {
         m_pkType = param2;
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setPlayersAndOtherData(param1:Player, param2:Player, param3:uint) : void
      {
         m_player1 = param1;
         m_player2 = param2;
         m_rank = param3;
         initShow2();
      }
      
      private function initShow() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_showContainer = m_show["showContainer"];
         m_pkScoreInforText = m_show["pkScoreInforText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_pkScoreInforText);
         m_pkRankText = m_show["pkRankText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_pkRankText);
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_player1 == null)
         {
            return;
         }
         if(m_player1ShowContainer == null)
         {
            m_player1ShowContainer = new EntityShowContainer();
            m_player1ShowContainer.init();
            m_player1ShowContainer.getShow().scaleX = m_player1ShowContainer.getShow().scaleY = 1.5;
         }
         m_player1ShowContainer.refreshPlayerShow(m_player1.playerVO);
         if(m_player2 && m_pkType == "twoPK")
         {
            if(m_player2ShowContainer == null)
            {
               m_player2ShowContainer = new EntityShowContainer();
               m_player2ShowContainer.init();
               m_player2ShowContainer.getShow().scaleX = m_player2ShowContainer.getShow().scaleY = 1.5;
            }
            m_player2ShowContainer.refreshPlayerShow(m_player2.playerVO);
         }
         else
         {
            ClearUtil.clearObject(m_player2ShowContainer);
            m_player2ShowContainer = null;
         }
         if(!m_player2ShowContainer)
         {
         }
         m_showContainer.addChild(m_player1ShowContainer.getShow());
         if(m_player2ShowContainer)
         {
            m_showContainer.addChild(m_player2ShowContainer.getShow());
         }
         m_pkScoreInforText.text = m_player1.getPKMode2VO1().getWinPKResultNum() + "胜" + m_player1.getPKMode2VO1().getFailPKResultNum() + "负";
         if(m_rank == 0)
         {
            m_pkRankText.text = "暂无排名";
         }
         else
         {
            m_pkRankText.text = m_rank.toString();
         }
      }
   }
}

