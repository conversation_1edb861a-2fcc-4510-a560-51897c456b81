package UI.DetectionClass
{
   import UI.CheatData.CheatData;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import YJFY.Part1;
   
   public class DetectionClass2
   {
      
      private static var _instance:DetectionClass2 = null;
      
      public function DetectionClass2()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗！？");
      }
      
      public static function getInstance() : DetectionClass2
      {
         if(!_instance)
         {
            _instance = new DetectionClass2();
         }
         return _instance;
      }
      
      public function detectionEquipmentVOsQuoteIsSame(param1:Vector.<EquipmentVO>) : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:EquipmentVO = null;
         var _loc2_:EquipmentVO = null;
         _loc4_ = param1 ? param1.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_ = param1[_loc6_];
            if(_loc3_)
            {
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  _loc2_ = param1[_loc5_];
                  if(_loc6_ != _loc5_ && Boolean(_loc2_) && (_loc3_ == _loc2_ || _loc3_ == _loc2_))
                  {
                     Part1.getInstance().openCheatGamePanel();
                     GamingUI.getInstance().closeInternalPanel();
                     CheatData.getInstance().addCheatDataStr("引用出现相同！");
                     throw new Error("引用出现相同！");
                  }
                  _loc5_++;
               }
            }
            _loc6_++;
         }
      }
   }
}

