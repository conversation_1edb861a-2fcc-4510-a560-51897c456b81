package UI.WorldBoss.Boss
{
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.AbleAttackedBoss.AttackUpgradeBoss;
   import UI.WorldBoss.Boss.CreateOwnImageBoss.CreateOwnImageBoss;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class BossFactory
   {
      
      private var m_classes:Array;
      
      public function BossFactory()
      {
         super();
         m_classes = [Boss,AbleAttackedBoss,AttackUpgradeBoss,CreateOwnImageBoss];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_classes);
         m_classes = null;
      }
      
      public function createBossByClassName(param1:String) : Boss
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
      
      public function createBossByXML(param1:XML) : Boss
      {
         var _loc2_:Boss = createBossByClassName(param1.@className);
         _loc2_.initByXML(param1);
         return _loc2_;
      }
      
      public function createBossByClassNameAndXMLPath(param1:String, param2:String, param3:YJFYLoader) : Boss
      {
         var _loc4_:Boss = createBossByClassName(param1);
         _loc4_.setLoader(param3);
         _loc4_.initByXMLPath(param2);
         return _loc4_;
      }
      
      public function createBossByClassNameAndXML(param1:String, param2:XML, param3:YJFYLoader) : Boss
      {
         var _loc4_:Boss = createBossByClassName(param1);
         _loc4_.setLoader(param3);
         _loc4_.initByXML(param2);
         return _loc4_;
      }
   }
}

