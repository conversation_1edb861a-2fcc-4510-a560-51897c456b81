package UI.InformationPanel.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class LookDanMedicinesBtn extends Btn
   {
      
      public function LookDanMedicinesBtn()
      {
         super();
         setTipString("查看丹药");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickLookDanMedicinesBtn"));
      }
   }
}

