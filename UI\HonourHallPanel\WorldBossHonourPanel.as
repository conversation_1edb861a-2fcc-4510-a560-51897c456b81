package UI.HonourHallPanel
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.PKUI.PKFunction;
   import UI.WorldBoss.Utils.GetWorldBossData;
   import UI.WorldBoss.Utils.WorldBossNeedSomeData;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class WorldBossHonourPanel
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_closeGetPanel:MovieClipPlayLogicShell;
      
      private var m_hourShow:MultiPlaceNumLogicShell;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_honourHallXML:XML;
      
      private var m_hounourHallPanel:HonourHallPanel;
      
      private var m_columes:Vector.<WorldBossColume>;
      
      private var m_rankText:TextField;
      
      private var m_timeStr:String;
      
      private var m_worldBossXML:XML;
      
      private var m_worldBossHouourPanelXML:XML;
      
      private var m_openTime:Number;
      
      private var m_remainTime:int;
      
      private var m_startTime:Number;
      
      private var m_bossIds:Array;
      
      public function WorldBossHonourPanel()
      {
         super();
         m_bossIds = ["boss1","boss2"];
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         m_honourHallXML = null;
         m_hounourHallPanel = null;
         ClearUtil.nullArr(m_columes);
         m_columes = null;
         m_timeStr = "";
         m_worldBossXML = null;
         ClearUtil.nullArr(m_bossIds);
         m_bossIds = null;
         m_rankText = null;
         m_worldBossHouourPanelXML = null;
         ClearUtil.clearObject(m_closeGetPanel);
         m_closeGetPanel = null;
         clearTimeShow();
      }
      
      public function setHounorHallXML(param1:XML) : void
      {
         m_honourHallXML = param1;
      }
      
      public function setTimeStr(param1:String) : void
      {
         m_timeStr = param1;
      }
      
      public function setHonourHallPanel(param1:HonourHallPanel) : void
      {
         m_hounourHallPanel = param1;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         var _loc5_:int = 0;
         var _loc2_:WorldBossColume = null;
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_showMC.gotoAndStop("2");
         var _loc3_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_rankText = m_show["rankText"];
         MyFunction2.changeTextFieldFont(_loc3_.fontName,m_rankText);
         m_rankText.text = "";
         var _loc4_:int = 6;
         m_columes = new Vector.<WorldBossColume>();
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = new WorldBossColume();
            _loc2_.setShow(m_show["colume" + (_loc5_ + 1)]);
            _loc2_.setHonourHallPanel(m_hounourHallPanel);
            m_columes.push(_loc2_);
            _loc5_++;
         }
         m_closeGetPanel = new MovieClipPlayLogicShell();
         m_closeGetPanel.setShow(m_show["closeGetPanel"]);
         m_closeGetPanel.gotoAndStop("1");
         initTimeShow();
      }
      
      public function initData() : void
      {
         MyFunction2.loadXMLAndGetServerTimeFunction("worldBoss/worldBoss",function(param1:XML, param2:String):void
         {
            var rankId:int;
            var i:int;
            var length:int;
            var myRank:int;
            var xml:XML = param1;
            var timeStr:String = param2;
            m_timeStr = timeStr;
            m_worldBossXML = xml;
            var worldbossNeedData:WorldBossNeedSomeData = new WorldBossNeedSomeData();
            new GetWorldBossData().getData(m_timeStr,m_worldBossXML,false,worldbossNeedData);
            rankId = worldbossNeedData.rankId;
            m_worldBossHouourPanelXML = m_honourHallXML.WorldBossHonourPanel[0];
            m_openTime = Number(m_worldBossHouourPanelXML.@openTime);
            m_remainTime = Math.max(0,m_openTime - worldbossNeedData.timeInCycle) * 3600;
            m_startTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
            if(m_remainTime <= 0)
            {
               m_closeGetPanel.gotoAndStop("2");
               clearTimeShow();
            }
            PKFunction.getInstance().getOneRankListDataByUserName(rankId,GameData.getInstance().getLoginReturnData().getName(),null,function(param1:Array):void
            {
               var _loc2_:Boolean = false;
               if(!param1 || param1.length == 0)
               {
                  m_rankText.text = "您未进排行榜";
                  myRank = -1;
               }
               else
               {
                  length = param1.length;
                  _loc2_ = false;
                  i = 0;
                  while(i < length)
                  {
                     if(param1[i].index == GameData.getInstance().getSaveFileData().index)
                     {
                        myRank = param1[i].rank;
                        m_rankText.text = myRank.toString();
                        _loc2_ = true;
                        break;
                     }
                     i++;
                  }
                  if(!_loc2_)
                  {
                     myRank = -1;
                     m_rankText.text = "您未进排行榜";
                  }
               }
               initColume(m_worldBossHouourPanelXML,myRank,m_timeStr);
               i = 0;
               while(i < length)
               {
                  param1[i] = null;
                  i++;
               }
               param1 = null;
            });
         },m_hounourHallPanel.showWarningBox,true);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         if(m_remainTime <= 0)
         {
            return;
         }
         if(isNaN(m_startTime))
         {
            return;
         }
         var _loc4_:int = m_remainTime * 1000 - (param1.getOnLineTimeForThisInit() - m_startTime);
         if(_loc4_ <= 0)
         {
            m_closeGetPanel.gotoAndStop("2");
            clearTimeShow();
         }
         else
         {
            _loc3_ = Math.ceil(_loc4_ / 1000);
            _loc5_ = _loc3_ / 3600;
            _loc3_ -= _loc5_ * 3600;
            _loc2_ = _loc3_ / 60;
            _loc3_ -= _loc2_ * 60;
            m_hourShow.showNum(_loc5_);
            m_minuteShow.showNum(_loc2_);
            m_secondShow.showNum(_loc3_);
         }
      }
      
      private function clearTimeShow() : void
      {
         ClearUtil.clearObject(m_hourShow);
         m_hourShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
      }
      
      private function initTimeShow() : void
      {
         m_hourShow = new MultiPlaceNumLogicShell();
         m_hourShow.setShow(m_closeGetPanel.getShow()["timeShow"]["hour"]);
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_closeGetPanel.getShow()["timeShow"]["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_closeGetPanel.getShow()["timeShow"]["second"]);
         m_hourShow.setIsShowZero(true);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
      }
      
      private function initColume(param1:XML, param2:int, param3:String) : void
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc9_:Object = null;
         var _loc5_:WorldBossColume = null;
         var _loc4_:* = undefined;
         var _loc8_:XMLList = param1.honour;
         var _loc6_:XML = XMLSingle.getInstance().equipmentXML;
         _loc7_ = int(m_columes.length);
         _loc10_ = 0;
         while(_loc10_ < _loc7_)
         {
            _loc9_ = {};
            _loc9_.name = String(_loc8_[_loc10_].@name);
            _loc9_.description = String(_loc8_[_loc10_].@description);
            _loc9_["medalID"] = String(_loc8_[_loc10_].@medal);
            _loc5_ = m_columes[_loc10_];
            _loc5_.initData(_loc9_,param3,_loc6_);
            _loc4_ = MyFunction.getInstance().excreteString(String(_loc8_[_loc10_].@rank));
            if(param2 >= _loc4_[0] && param2 <= _loc4_[1])
            {
               _loc5_.active();
            }
            _loc10_++;
         }
      }
   }
}

