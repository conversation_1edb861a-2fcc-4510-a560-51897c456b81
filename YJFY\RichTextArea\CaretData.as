package YJFY.RichTextArea
{
   public class CaretData
   {
      
      private var m_caretIndex:int;
      
      private var m_frontPartLength:int;
      
      private var m_behindPartLength:int;
      
      private var m_selectionBeginIndex:int;
      
      private var m_selectionEndIndex:int;
      
      public function CaretData(param1:int, param2:int, param3:int, param4:int, param5:int)
      {
         super();
         m_caretIndex = param1;
         m_frontPartLength = param2;
         m_behindPartLength = param3;
         m_selectionBeginIndex = param4;
         m_selectionEndIndex = param5;
      }
      
      public function getCaretIndex() : int
      {
         return m_caretIndex;
      }
      
      public function getFrontPartLength() : int
      {
         return m_frontPartLength;
      }
      
      public function getBehindPartLength() : int
      {
         return m_behindPartLength;
      }
      
      public function getSelectionBeginIndex() : int
      {
         return m_selectionBeginIndex;
      }
      
      public function getSelectionEndIndex() : int
      {
         return m_selectionEndIndex;
      }
   }
}

