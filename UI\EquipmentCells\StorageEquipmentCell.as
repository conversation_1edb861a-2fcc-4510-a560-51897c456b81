package UI.EquipmentCells
{
   import UI.Button.ChoiceBtns.EquipmentDestinationBtnListSingle;
   import UI.CellBorderSingle;
   import UI.EquipmentCell;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction2;
   import UI.UIInterface.OldInterface.IEquipmentCellBackground;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class StorageEquipmentCell extends EquipmentCell
   {
      
      public var equipbackground:IEquipmentCellBackground;
      
      public function StorageEquipmentCell()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(equipbackground)
         {
            equipbackground.clear();
         }
         equipbackground = null;
      }
      
      override protected function initCell() : void
      {
         super.initCell();
      }
      
      override protected function rollOut(param1:MouseEvent) : void
      {
         if(getChildByName("storageBtnList"))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
         super.rollOut(param1);
      }
      
      override protected function mouseDown(param1:MouseEvent) : void
      {
         super.mouseDown(param1);
      }
      
      override protected function click(param1:MouseEvent) : void
      {
         super.click(param1);
         if(_intervalTime < 500)
         {
            if(isHaveChild)
            {
               if(!getChildByName("storageBtnList"))
               {
                  EquipmentDestinationBtnListSingle.getInstance().name = "storageBtnList";
                  EquipmentDestinationBtnListSingle.getInstance().x = mouseX;
                  EquipmentDestinationBtnListSingle.getInstance().y = mouseY;
                  EquipmentDestinationBtnListSingle.getInstance().switchToPackageState(MyFunction2.brushBtn(8 | 0x0100,child));
                  addChild(EquipmentDestinationBtnListSingle.getInstance());
               }
            }
         }
      }
      
      override public function get equipmentCellBackground() : IEquipmentCellBackground
      {
         return equipbackground;
      }
      
      override public function showBorder() : void
      {
         if(!_layer.getChildByName("cellBorder") && parent.parent.visible == true)
         {
            CellBorderSingle.getInstance().x = 0.75;
            CellBorderSingle.getInstance().y = -0.87;
            CellBorderSingle.getInstance().width = 48;
            CellBorderSingle.getInstance().height = 48;
            CellBorderSingle.getInstance().name = "cellBorder";
            _layer.addChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function hideBorder() : void
      {
         if(_layer.getChildByName("cellBorder"))
         {
            _layer.removeChild(CellBorderSingle.getInstance());
         }
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         return equipbackground.getRect(param1);
      }
      
      protected function hideBtn(param1:UIBtnEvent) : void
      {
         if(getChildByName("storageBtnList"))
         {
            removeChild(EquipmentDestinationBtnListSingle.getInstance());
         }
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickTakeOutBtn",hideBtn,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickTakeOutBtn",hideBtn,true);
      }
   }
}

