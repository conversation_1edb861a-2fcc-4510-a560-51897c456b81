package YJFY.World
{
   public class Coordinate
   {
      
      private var m_x:Number;
      
      private var m_y:Number;
      
      private var m_z:Number;
      
      private var m_InitX:Number;
      
      private var m_InitY:Number;
      
      private var m_InitZ:Number;
      
      private var m_bIsXFisrt:Boolean;
      
      private var m_bIsYFisrt:Boolean;
      
      private var m_bIsZFisrt:Boolean;
      
      public function Coordinate(param1:Number = 0, param2:Number = 0, param3:Number = 0)
      {
         super();
         m_x = param1;
         m_y = param2;
         m_z = param3;
         m_InitX = param1;
         m_InitY = param2;
         m_InitZ = param3;
         m_bIsXFisrt = true;
         m_bIsYFisrt = true;
         m_bIsZFisrt = true;
      }
      
      public function addCoordinate(param1:Coordinate) : Coordinate
      {
         var _loc2_:Coordinate = new Coordinate();
         _loc2_.setX(m_x + param1.getX());
         _loc2_.setY(m_y + param1.getY());
         _loc2_.setZ(m_z + param1.getZ());
         return _loc2_;
      }
      
      public function clone() : Coordinate
      {
         return new Coordinate(m_x,m_y,m_x);
      }
      
      public function copy(param1:Coordinate) : void
      {
         m_x = param1.m_x;
         m_y = param1.m_y;
         m_z = param1.m_z;
      }
      
      public function toString() : String
      {
         return "Coord: x:" + m_x + "  Y:" + m_y + "   z:" + m_z;
      }
      
      public function setTo(param1:Number, param2:Number, param3:Number) : void
      {
         m_x = param1;
         m_y = param2;
         m_z = param3;
      }
      
      public function getX() : Number
      {
         return m_x;
      }
      
      public function setX(param1:Number) : void
      {
         m_x = param1;
         if(m_bIsXFisrt && param1 != 0)
         {
            m_bIsXFisrt = false;
            m_InitX = param1;
         }
      }
      
      public function getY() : Number
      {
         return m_y;
      }
      
      public function setY(param1:Number) : void
      {
         m_y = param1;
         if(m_bIsYFisrt && param1 != 0)
         {
            m_bIsYFisrt = false;
            m_InitY = param1;
         }
      }
      
      public function getZ() : Number
      {
         return m_z;
      }
      
      public function setZ(param1:Number) : void
      {
         m_z = param1;
         if(m_bIsZFisrt)
         {
            m_bIsZFisrt = false;
            m_InitZ = param1;
         }
      }
      
      public function getInitX() : Number
      {
         return m_InitX;
      }
      
      public function getInitY() : Number
      {
         return m_InitY;
      }
      
      public function getInitZ() : Number
      {
         return m_InitZ;
      }
   }
}

