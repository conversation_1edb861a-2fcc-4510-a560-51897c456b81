package UI.Skills.PlayerSkills
{
   import UI.Skills.SkillVO;
   
   public class PlayerPassiveSkillVO extends SkillVO
   {
      
      public function PlayerPassiveSkillVO()
      {
         super();
      }
      
      override public function clone() : SkillVO
      {
         var _loc1_:PlayerPassiveSkillVO = new PlayerPassiveSkillVO();
         cloneAttribue(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribue(param1:SkillVO) : void
      {
         super.cloneAttribue(param1);
      }
   }
}

