package UI.Equipments.StackEquipments
{
   import GM_UI.GMData;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class StackEquipment extends Equipment
   {
      
      private static const path:String = "UI.Equipments.StackEquipments.";
      
      private var _showLayer:Sprite = new Sprite();
      
      private var _textLayer:Sprite = new Sprite();
      
      private var _text:TextField = new TextField();
      
      public function StackEquipment(param1:StackEquipmentVO)
      {
         addChild(_showLayer);
         _textLayer.mouseChildren = false;
         _textLayer.mouseEnabled = false;
         addChild(_textLayer);
         super(param1);
      }
      
      override public function clear() : void
      {
         super.clear();
         _showLayer = null;
         _textLayer = null;
         _text = null;
      }
      
      override protected function setShow(param1:EquipmentVO) : void
      {
         var loadListener:LoadFinishListener1;
         var equipmentVO:EquipmentVO = param1;
         if(equipmentVO == null)
         {
            return;
         }
         loadListener = new LoadFinishListener1(function():void
         {
            if(_equipmentVO == null)
            {
               return;
            }
            setmg(equipmentVO.className);
            setText((equipmentVO as StackEquipmentVO).num);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSources,loadListener);
      }
      
      override protected function setmg(param1:String) : void
      {
         while(_showLayer.numChildren > 0)
         {
            _showLayer.removeChildAt(0);
         }
         _equipmentSprite = MyFunction2.returnShowByClassName("UI.Equipments.StackEquipments." + param1) as Sprite;
         if(_equipmentSprite == null)
         {
            trace("没有装备显示！");
            return;
         }
         _showLayer.addChild(_equipmentSprite);
      }
      
      override public function set equipmentVO(param1:EquipmentVO) : void
      {
         _equipmentVO = param1;
         if(param1)
         {
            setShow(_equipmentVO);
         }
      }
      
      override public function get equipmentVO() : EquipmentVO
      {
         return _equipmentVO;
      }
      
      public function initText() : void
      {
      }
      
      override public function imperfectClone() : Equipment
      {
         return new StackEquipment(equipmentVO as StackEquipmentVO);
      }
      
      override public function clone() : Equipment
      {
         return new StackEquipment((equipmentVO as StackEquipmentVO).clone() as StackEquipmentVO);
      }
      
      private function setText(param1:int) : void
      {
         var _loc4_:Array = null;
         var _loc3_:FangZhengKaTongJianTi = null;
         var _loc2_:TextFormat = null;
         if(GMData.getInstance().isGMApplication)
         {
            if(param1 == 1)
            {
               _text.text = "";
            }
            else
            {
               _text.text = param1.toString();
            }
            _text.scaleX = _text.scaleY = 1.6;
            _loc4_ = [new GlowFilter(16777215,1,2,2,10,1,false,false)];
            _text.filters = _loc4_;
            _textLayer.addChild(_text);
         }
         else
         {
            _loc3_ = new FangZhengKaTongJianTi();
            _loc2_ = new TextFormat(_loc3_.fontName,15,16763904);
            _text.autoSize = "right";
            _text.selectable = false;
            _text.embedFonts = true;
            if(param1 == 1)
            {
               _text.text = "";
            }
            else
            {
               _text.text = param1.toString();
               _text.setTextFormat(_loc2_,0,_text.text.length);
            }
            _text.x = 22 - _text.width;
            _text.y = 0;
            _textLayer.addChild(_text);
            _text.filters = [filter()];
         }
      }
      
      private function filter() : GlowFilter
      {
         return new GlowFilter(0,1,2,2,10,3);
      }
   }
}

