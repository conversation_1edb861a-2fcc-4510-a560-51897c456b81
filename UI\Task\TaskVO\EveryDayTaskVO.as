package UI.Task.TaskVO
{
   public class EveryDayTaskVO extends MTaskVO
   {
      
      private var _receiveTaskDate:String;
      
      public function EveryDayTaskVO()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.receiveTaskDate = _receiveTaskDate;
      }
      
      public function get receiveTaskDate() : String
      {
         return _antiwear.receiveTaskDate;
      }
      
      public function set receiveTaskDate(param1:String) : void
      {
         _antiwear.receiveTaskDate = param1;
      }
   }
}

