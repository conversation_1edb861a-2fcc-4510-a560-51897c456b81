package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   public class CMSXChangeBarLogicShellType
   {
      
      public static const HORIZONTAL:String = "horizontal";
      
      public static const VERTICAL:String = "vertical";
      
      private var _type:String;
      
      public function CMSXChangeBarLogicShellType()
      {
         super();
         _type = "horizontal";
      }
      
      public function get type() : String
      {
         return _type;
      }
      
      public function set type(param1:String) : void
      {
         if(param1 != "horizontal" && param1 != "vertical")
         {
            throw new Error("value必须是:horizontal或vertical");
         }
         _type = param1;
      }
   }
}

