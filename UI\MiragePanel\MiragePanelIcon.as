package UI.MiragePanel
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   import UI.MyFunction;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class MiragePanelIcon extends SwitchBtn
   {
      
      public static const LOCK_STATE:String = "lockState";
      
      public static const OPEN_STATE:String = "openState";
      
      private var _target:DisplayObject;
      
      private var _state:String;
      
      public function MiragePanelIcon()
      {
         super();
         addFrameScript(0,addObjectToOne);
         addFrameScript(1,addObjectToTwo);
      }
      
      override public function clear() : void
      {
         super.clear();
         _target = null;
      }
      
      public function set target(param1:DisplayObject) : void
      {
         _target = param1;
      }
      
      public function get target() : DisplayObject
      {
         return _target;
      }
      
      public function set state(param1:String) : void
      {
         var _loc2_:* = param1;
         if("openState" !== _loc2_)
         {
            _state = param1;
            setIsLock(true,null);
            filters = [];
            MyFunction.getInstance().changeSaturation(this,-100);
         }
         else
         {
            _state = param1;
            setIsLock(false,null);
            MyFunction.getInstance().changeSaturation(this,0);
         }
      }
      
      public function get state() : String
      {
         return _state;
      }
      
      private function addObjectToOne() : void
      {
         addChildAt(_target,0);
      }
      
      private function addObjectToTwo() : void
      {
         addChild(_target);
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchMiragePanelIconBtn"));
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("rollOver",mouseOverSlot,false,0,true);
         addEventListener("rollOut",mouseOutSlot,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("rollOver",mouseOverSlot);
         removeEventListener("rollOut",mouseOutSlot);
      }
      
      private function mouseOverSlot(param1:MouseEvent) : void
      {
      }
      
      private function mouseOutSlot(param1:MouseEvent) : void
      {
         if(this.currentFrame != 1)
         {
         }
      }
   }
}

