package UI.ActivityPanel
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.system.System;
   
   public class ActivityPanel extends MySprite
   {
      
      private var m_activityData:ActivityData;
      
      private var m_show:MovieClip;
      
      private var m_activityDetailAreaMC:MovieClipPlayLogicShell;
      
      private var m_activityOnePanel:ActivityOnePanel;
      
      private var m_activityBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_activityBtnObj:Object;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_wantLoadSources:Array = ["activityPanel"];
      
      private var m_activityXML:XML;
      
      private var m_gamingUI:GamingUI;
      
      public function ActivityPanel()
      {
         super();
         m_activityData = new ActivityData();
         m_activityBtnObj = {};
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         GamingUI.getInstance().loadQueue.unLoad(m_wantLoadSources);
         super.clear();
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_activityData);
         m_activityData = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_activityDetailAreaMC);
         m_activityDetailAreaMC = null;
         ClearUtil.clearObject(m_activityOnePanel);
         m_activityOnePanel = null;
         ClearUtil.clearObject(m_activityBtnGroup);
         m_activityBtnGroup = null;
         ClearUtil.clearObject(m_activityBtnObj);
         m_activityBtnObj = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_wantLoadSources);
         m_wantLoadSources = null;
         System.disposeXML(m_activityXML);
         m_activityXML = null;
         m_gamingUI = null;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function init() : void
      {
         MyFunction2.loadXMLAndGetServerTimeFunction("activity",function(param1:XML, param2:String):void
         {
            var loadFinishListenr:LoadFinishListener1;
            var xml:XML = param1;
            var timeStr:String = param2;
            m_activityXML = xml;
            initActivityData(timeStr);
            loadFinishListenr = new LoadFinishListener1(function():void
            {
               var _loc5_:int = 0;
               var _loc4_:int = 0;
               var _loc1_:DisplayObject = null;
               var _loc2_:MySwitchBtnLogicShell = null;
               if(m_show == null)
               {
                  m_show = MyFunction2.returnShowByClassName("ActivityPanel") as MovieClip;
               }
               m_show.addEventListener("clickButton",clickButton,true,0,true);
               if(stage)
               {
                  m_show.x = (stage.stageWidth - m_show.width) / 2;
                  m_show.y = (stage.stageHeight - m_show.height) / 2;
               }
               addChild(m_show);
               m_activityDetailAreaMC = new MovieClipPlayLogicShell();
               m_activityDetailAreaMC.setShow(m_show["activityDetailArea"]);
               m_activityBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
               var _loc3_:int = m_show.numChildren;
               _loc5_ = 0;
               while(_loc5_ < _loc3_)
               {
                  _loc1_ = m_show.getChildAt(_loc5_);
                  if(_loc1_.name.substr(0,"activityBtn".length) == "activityBtn")
                  {
                     _loc4_++;
                     _loc2_ = new MySwitchBtnLogicShell();
                     _loc2_.setShow(_loc1_ as Sprite);
                     m_activityBtnGroup.addSwitchBtn(_loc2_);
                     m_activityBtnObj[_loc1_.name.split("_")[1]] = _loc2_;
                  }
                  _loc5_++;
               }
               m_activityBtnGroup.addEnd();
               m_quitBtn = new ButtonLogicShell2();
               m_quitBtn.setShow(m_show["quitBtn"]);
            },null);
            GamingUI.getInstance().loadQueue.load(m_wantLoadSources,loadFinishListenr);
         },function():void
         {
            GamingUI.getInstance().showMessageTip("加载失败");
         },true);
      }
      
      private function initActivityData(param1:String) : void
      {
         m_activityData.initByXML(m_activityXML,param1);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:* = param1.button;
         if(m_quitBtn === _loc3_)
         {
            m_gamingUI.closeActivityPanel();
         }
         for(var _loc2_ in m_activityBtnObj)
         {
            if(m_activityBtnObj[_loc2_] == param1.button)
            {
               m_activityDetailAreaMC.gotoAndStop(_loc2_);
               ClearUtil.clearObject(m_activityOnePanel);
               m_activityOnePanel = new ActivityOnePanel();
               m_activityOnePanel.setShow(m_activityDetailAreaMC.getShow());
               m_activityOnePanel.setActivityOneData(m_activityData.getActivityOneDataByName(_loc2_));
               return;
            }
         }
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChild(WarningBoxSingle.getInstance());
      }
   }
}

