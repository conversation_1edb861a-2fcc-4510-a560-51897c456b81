package UI.ExchangeEquipment
{
   import UI.Buff.Buff.BuffDrive;
   import UI.Players.Player;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.EntityShowContainer;
   import YJFY.Utils.ClearUtil;
   
   public class ExEPlayerData
   {
      
      private static var _instance:ExEPlayerData;
      
      public var myPlayerss:Vector.<Vector.<Player>>;
      
      public var myPlayerBuffss:Vector.<Vector.<Vector.<BuffDrive>>>;
      
      public var myPlayerNicknameDatas:Vector.<Object>;
      
      public var myPlayerSaveFileDatas:Vector.<SaveFileData>;
      
      public var myPlayerXMLs:Vector.<XML>;
      
      public var myPlayerShowss:Vector.<Vector.<EntityShowContainer>>;
      
      public var haveExeNum:int;
      
      public var exeNum:int;
      
      public function ExEPlayerData()
      {
         super();
         if(!_instance)
         {
            myPlayerBuffss = new Vector.<Vector.<Vector.<BuffDrive>>>(8);
            myPlayerss = new Vector.<Vector.<Player>>(8);
            myPlayerNicknameDatas = new Vector.<Object>(8);
            myPlayerSaveFileDatas = new Vector.<SaveFileData>(8);
            myPlayerXMLs = new Vector.<XML>(8);
            myPlayerShowss = new Vector.<Vector.<EntityShowContainer>>(8);
            _instance = this;
            return;
         }
         throw new Error();
      }
      
      public static function getInstance() : ExEPlayerData
      {
         if(!_instance)
         {
            _instance = new ExEPlayerData();
         }
         return _instance;
      }
      
      public function exist() : void
      {
         clear();
         delPlayers();
         delNickName();
         delSaveXML();
         delPlayerXML();
         delPlayerShow();
      }
      
      public function delPlayers() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(ExEPlayerData.getInstance().myPlayerss[_loc2_])
            {
               _loc1_ = int(ExEPlayerData.getInstance().myPlayerss[_loc2_].length);
               _loc3_ = 0;
               while(_loc3_ < _loc1_)
               {
                  if(ExEPlayerData.getInstance().myPlayerss[_loc2_][_loc3_])
                  {
                     ExEPlayerData.getInstance().myPlayerss[_loc2_][_loc3_].clear();
                  }
                  ExEPlayerData.getInstance().myPlayerss[_loc2_][_loc3_] = null;
                  _loc3_++;
               }
               ExEPlayerData.getInstance().myPlayerss[_loc2_] = null;
            }
            _loc2_++;
         }
      }
      
      public function delNickName() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(myPlayerNicknameDatas.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            myPlayerNicknameDatas[_loc2_] = null;
            _loc2_++;
         }
      }
      
      public function delSaveXML() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(myPlayerSaveFileDatas.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            myPlayerSaveFileDatas[_loc2_] = null;
            _loc2_++;
         }
      }
      
      public function delPlayerXML() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(myPlayerXMLs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            myPlayerXMLs[_loc2_] = null;
            _loc2_++;
         }
      }
      
      public function delPlayerShow() : void
      {
         ClearUtil.clearObject(myPlayerShowss);
         myPlayerShowss = null;
         myPlayerShowss = new Vector.<Vector.<EntityShowContainer>>(8);
      }
      
      public function clear() : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         if(ExEPlayerData.getInstance().myPlayerBuffss)
         {
            _loc2_ = int(ExEPlayerData.getInstance().myPlayerBuffss.length);
            _loc6_ = 0;
            while(_loc6_ < _loc2_)
            {
               if(ExEPlayerData.getInstance().myPlayerBuffss[_loc6_])
               {
                  _loc3_ = int(ExEPlayerData.getInstance().myPlayerBuffss[_loc6_].length);
                  _loc4_ = 0;
                  while(_loc4_ < _loc3_)
                  {
                     if(ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_])
                     {
                        _loc5_ = int(ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_].length);
                        _loc1_ = 0;
                        while(_loc1_ < _loc5_)
                        {
                           if(ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_][_loc1_])
                           {
                              ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_][_loc1_].clear();
                           }
                           ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_][_loc1_] = null;
                           _loc1_++;
                        }
                     }
                     ExEPlayerData.getInstance().myPlayerBuffss[_loc6_][_loc4_] = null;
                     _loc4_++;
                  }
               }
               ExEPlayerData.getInstance().myPlayerBuffss[_loc6_] = null;
               _loc6_++;
            }
         }
         ExEPlayerData.getInstance().myPlayerBuffss = null;
         _instance = null;
      }
   }
}

