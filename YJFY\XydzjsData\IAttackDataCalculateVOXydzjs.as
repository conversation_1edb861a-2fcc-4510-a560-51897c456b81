package YJFY.XydzjsData
{
   public interface IAttackDataCalculateVOXydzjs
   {
      
      function setAttackSkill(param1:IAttackSkillVOForAttackDataCalculate) : void;
      
      function getTotalHp() : uint;
      
      function getCurrentHp() : uint;
      
      function decCurrentHp(param1:uint) : void;
      
      function getAttack() : Number;
      
      function getAttackHurtDuration() : uint;
      
      function getDefence() : Number;
      
      function getPetPassiveSkillDecHurt() : Number;
      
      function getDogdeRate() : Number;
      
      function getCriticalRate() : Number;
      
      function getCriticalMulti() : Number;
      
      function getDeCriticalRate() : Number;
      
      function getHitRate() : Number;
   }
}

