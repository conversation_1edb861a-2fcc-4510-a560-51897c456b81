package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_RemoveMember extends InformationBodyDetail
   {
      
      private var m_uid_leader:Number;
      
      private var m_idx_leader:int;
      
      private var m_uid_member:Number;
      
      private var m_idx_member:int;
      
      public function UP_RemoveMember()
      {
         super();
         m_informationBodyId = 3033;
      }
      
      public function initData(param1:Number, param2:int, param3:Number, param4:int) : void
      {
         m_uid_leader = param1;
         m_idx_leader = param2;
         m_uid_member = param3;
         m_idx_member = param4;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid_leader,m_idx_leader);
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid_member,m_idx_member);
         return _loc1_;
      }
      
      public function getUidMember() : Number
      {
         return m_uid_member;
      }
      
      public function getIdxMember() : Number
      {
         return m_idx_member;
      }
   }
}

