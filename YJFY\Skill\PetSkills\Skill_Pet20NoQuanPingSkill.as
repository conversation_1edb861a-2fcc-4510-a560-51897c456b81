package YJFY.Skill.PetSkills
{
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.SkillVO;
   import YJFY.ActivityMode.SantaClaus;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.PlayerEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetXydzjs;
   import YJFY.GameEntity.XydzjsMount.MountXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   import flash.events.Event;
   import flash.utils.Timer;
   
   public class Skill_Pet20NoQuanPingSkill extends CuboidAreaAttackSkill
   {
      
      private const m_const_changeStartId:String = "changeStart";
      
      private const m_const_startEffectFrameLabel:String = "start";
      
      protected const m_const_endEffectFrameLabel:String = "end^stop^";
      
      private const m_const_startPlayEffectFrameLabel:String = "changeTarget";
      
      public const m_const_effectShowDefId:String = "petSkillEffect";
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      private var m_RecoverTimer:Timer;
      
      private var m_BackTimer:Timer;
      
      protected var m_bodyDefId:String;
      
      protected var m_bodyAttackReachFrameLabel:String;
      
      protected var m_bodySkillEndFrameLabel:String;
      
      protected var m_everyEntityAddShowDefId:String;
      
      protected var m_everyEntityAddShowDefinitionData:AnimationDefinitionData;
      
      protected var m_everyEntityAddShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_everyEntityAddShowPlayEndListener:StopListener;
      
      protected var m_everyEntityAddShowPlayReachFrameListener:AnimationPlayFrameLabelListener;
      
      protected var m_wasteEveryEntityAddShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_everyEntityAddShowPlaysPool:ObjectsPool;
      
      private const FearTime:int = 15;
      
      private var remMoveSpeed:Number = 0;
      
      private var remState:String;
      
      public function Skill_Pet20NoQuanPingSkill()
      {
         super();
         m_everyEntityAddShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_everyEntityAddShowPlayEndListener = new StopListener();
         m_everyEntityAddShowPlayEndListener.stop2Fun = AnimationStop;
         m_everyEntityAddShowPlayReachFrameListener = new AnimationPlayFrameLabelListener();
         m_everyEntityAddShowPlayReachFrameListener.reachFrameLabelFun2 = everyEntityAddShowPlayReachFrame;
         m_wasteEveryEntityAddShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_everyEntityAddShowPlaysPool = new ObjectsPool(m_everyEntityAddShowPlays,m_wasteEveryEntityAddShowPlays,createEntityShow,null);
         m_RecoverTimer = new Timer(15 * 1000);
         m_RecoverTimer.addEventListener("timer",AnimationStop,false,0,true);
         m_BackTimer = new Timer(5000);
         m_BackTimer.addEventListener("timer",Back,false,0,true);
      }
      
      protected function createEntityShow() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_everyEntityAddShowDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_);
         return _loc1_;
      }
      
      protected function Back(param1:Event) : void
      {
         var _loc5_:int = 0;
         var _loc4_:AnimationShowPlayLogicShell = null;
         var _loc3_:IEntity = null;
         if(m_BackTimer)
         {
            m_BackTimer.stop();
         }
         var _loc2_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = m_everyEntityAddShowPlays[_loc5_];
            _loc3_ = _loc4_.extra as IEntity;
            if(_loc3_ && _loc3_ is AnimalEntity)
            {
               if((_loc3_ as AnimalEntity).isFear1)
               {
                  (_loc3_ as AnimalEntity).isFear1 = false;
               }
            }
            _loc5_++;
         }
      }
      
      protected function AnimationStop(param1:Event) : void
      {
         var _loc5_:int = 0;
         var _loc4_:AnimationShowPlayLogicShell = null;
         var _loc3_:IEntity = null;
         if(m_RecoverTimer)
         {
            m_RecoverTimer.stop();
         }
         var _loc2_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = m_everyEntityAddShowPlays[_loc5_];
            _loc3_ = _loc4_.extra as IEntity;
            if(_loc3_ && _loc3_ is AnimalEntity)
            {
               if(!(_loc3_ && _loc3_.getExtra() is PlayerXydzjs))
               {
                  if((_loc3_ as AnimalEntity).isFear)
                  {
                     (_loc3_ as AnimalEntity).isFear = false;
                     if((_loc3_ as AnimalEntity).m_entityLogicVO && !(_loc3_ as AnimalEntity).isInDie())
                     {
                        if(remMoveSpeed == 0)
                        {
                           if((_loc3_ as AnimalEntity).isinWalk())
                           {
                              remMoveSpeed = (_loc3_ as AnimalEntity).m_walkSpeed;
                           }
                           if((_loc3_ as AnimalEntity).isInRun())
                           {
                              remMoveSpeed = (_loc3_ as AnimalEntity).m_runSpeed;
                           }
                        }
                        (_loc3_ as AnimalEntity).setMoveSpeed(remMoveSpeed);
                     }
                  }
               }
            }
            (_loc4_.extra as IEntity).removeOtherAnimation(_loc4_);
            m_everyEntityAddShowPlaysPool.wasteOneObj(_loc4_);
            _loc5_--;
            _loc2_--;
            _loc5_++;
         }
      }
      
      protected function everyEntityAddShowPlayReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
      
      override public function clear() : void
      {
         clearEveryEntityAddShowAnimationPlays();
         clearWasteEveryEntityAddShowAnimationPlays();
         m_bodyDefId = null;
         m_bodyAttackReachFrameLabel = null;
         m_bodySkillEndFrameLabel = null;
         m_everyEntityAddShowDefId = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlays);
         m_everyEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayEndListener);
         m_everyEntityAddShowPlayEndListener = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayReachFrameListener);
         m_everyEntityAddShowPlayReachFrameListener = null;
         ClearUtil.clearObject(m_wasteEveryEntityAddShowPlays);
         m_wasteEveryEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlaysPool);
         m_everyEntityAddShowPlaysPool = null;
         ClearUtil.clearObject(m_everyEntityAddShowDefinitionData);
         m_everyEntityAddShowDefinitionData = null;
         super.clear();
         if(m_RecoverTimer)
         {
            m_RecoverTimer.stop();
         }
         m_RecoverTimer = null;
         if(m_BackTimer)
         {
            m_BackTimer.stop();
         }
         m_BackTimer = null;
      }
      
      protected function clearEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_everyEntityAddShowPlays[_loc2_].extra)
            {
               (m_everyEntityAddShowPlays[_loc2_].extra as IEntity).removeOtherAnimation(m_everyEntityAddShowPlays[_loc2_]);
            }
            ClearUtil.clearObject(m_everyEntityAddShowPlays[_loc2_].getShow());
            ClearUtil.clearObject(m_everyEntityAddShowPlays[_loc2_]);
            m_everyEntityAddShowPlays[_loc2_] = null;
            _loc2_++;
         }
         m_everyEntityAddShowPlays.length = 0;
      }
      
      protected function clearWasteEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_wasteEveryEntityAddShowPlays ? m_wasteEveryEntityAddShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_wasteEveryEntityAddShowPlays[0];
            m_wasteEveryEntityAddShowPlays.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_wasteEveryEntityAddShowPlays = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         var _loc3_:String = null;
         var _loc2_:String = null;
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_bodyAttackReachFrameLabel = String(param1.@bodyAttackReachFrameLabel);
         m_bodySkillEndFrameLabel = String(param1.@bodySkillEndFrameLabel);
         m_everyEntityAddShowDefId = String(param1.@everyEntityAddShowDefId);
         if(m_everyEntityAddShowDefId)
         {
            m_everyEntityAddShowDefinitionData = new AnimationDefinitionData();
            m_everyEntityAddShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_everyEntityAddShowDefId)[0]);
         }
         if(m_bodyAttackReachFrameLabel)
         {
            _loc3_ = String(param1.shakeView[0].@swfPath);
            _loc2_ = String(param1.shakeView[0].@className);
            m_shakeViewDataOfPoints = new DataOfPoints();
            m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
            m_myLoader.load();
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setShowDirection((m_owner.getExtra() as PetXydzjs).getOwner().getAnimalEntity().getShowDirection());
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      public function setPostion() : void
      {
         m_owner.setNewPosition(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc2_:SkillVO = null;
         if(param1 is AnimalEntity && Boolean(m_attackData) && (m_attackData.getIsAttack() || (param1 as AnimalEntity).getAttackData().getIsAttack()) && !(param1 as AnimalEntity).isInDie())
         {
            if(!(param1.getExtra() is AutoAttackPetXydzjs) && !(param1.getExtra() is SantaClaus) && !(param1.getExtra() is PetXydzjs) && !(param1 as AnimalEntity is PlayerEntity) && !(param1 as AnimalEntity is TangMonkEntity) && !(param1.getExtra() is MountXydzjs) && !(param1 as AnimalEntity).isFear)
            {
               if((param1 as AnimalEntity).isOutOfControl)
               {
                  (param1 as AnimalEntity).removeFearAnimation();
                  (param1 as AnimalEntity).isOutOfControl = false;
                  (param1 as AnimalEntity).isOutOfControlEffect = false;
               }
               (param1 as AnimalEntity).isFear = true;
               if(!(param1 as AnimalEntity).notShowBeattack)
               {
                  (param1 as AnimalEntity).isFear1 = true;
                  m_BackTimer.reset();
                  m_BackTimer.start();
               }
               m_RecoverTimer.reset();
               m_RecoverTimer.start();
               addEntityShow(param1);
               if((param1 as AnimalEntity).isinWalk())
               {
                  remMoveSpeed = (param1 as AnimalEntity).m_walkSpeed;
               }
               if((param1 as AnimalEntity).isInRun())
               {
                  remMoveSpeed = (param1 as AnimalEntity).m_runSpeed;
               }
               (param1 as AnimalEntity).setMoveSpeed(remMoveSpeed * 0.3);
               if(m_owner && m_owner.getExtra() is PetXydzjs && (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO())
               {
                  _loc2_ = (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO().activeSkillVO;
                  if(_loc2_)
                  {
                     if(_loc2_.className == "PetSkill_Molong")
                     {
                        (param1 as AnimalEntity).AddValue = (_loc2_ as PetActiveSkillVO).subWalkSpeed;
                     }
                  }
               }
            }
         }
         super.attackSuccess(param1);
      }
      
      protected function addEntityShow(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if(m_world == null)
         {
            return;
         }
         if(param1.getIsShowBody() == false || param1 is IAnimalEntity && (param1 as IAnimalEntity).isInDie())
         {
            return;
         }
         if(m_everyEntityAddShowDefinitionData)
         {
            _loc2_ = m_everyEntityAddShowPlaysPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc2_.extra = param1;
            _loc2_.getDisplayShow().name = "molongEffect";
            _loc2_.gotoAndPlay("start");
            _loc2_.getDisplayShow().scaleX = _loc2_.getDisplayShow().scaleY = 0.8;
            _loc2_.getDisplayShow().x = _loc2_.getDisplayShow().x - 5;
            _loc2_.getDisplayShow().y = 0 - param1.getBodyZRange();
            param1.addOtherAniamtion(_loc2_);
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_bodyAttackReachFrameLabel:
               attackReach(m_world);
               m_world.shakeView(m_shakeViewDataOfPoints);
               break;
            case m_bodySkillEndFrameLabel:
               endSkill1();
         }
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
   }
}

