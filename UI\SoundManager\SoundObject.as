package UI.SoundManager
{
   import YJFY.Utils.ClearUtil;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   import flash.system.Capabilities;
   
   public class SoundObject extends EventDispatcher
   {
      
      private var _canPlaySound:Boolean = Capabilities.hasAudio;
      
      private var _name:String;
      
      private var _sound:Sound;
      
      private var _soundChannels:Vector.<SoundChannel>;
      
      private var _soundChannel:SoundChannel;
      
      private var _soundTransform:SoundTransform;
      
      private var _offset:Number;
      
      private var _pauseTime:Number;
      
      private var _paused:Boolean;
      
      private var _muted:<PERSON>olean;
      
      private var _playing:Boolean;
      
      private var _loops:int;
      
      public function SoundObject(param1:String, param2:Sound)
      {
         super();
         _name = param1;
         _sound = param2;
         _soundChannels = new Vector.<SoundChannel>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(_sound);
         _sound = null;
         ClearUtil.clearObject(_soundChannels);
         _soundChannels = null;
         _soundTransform = null;
      }
      
      public function play(param1:Number = 0, param2:int = 0, param3:SoundTransform = null) : SoundChannel
      {
         if(_canPlaySound)
         {
            _offset = param1;
            if(param2 < 0)
            {
               param2 = 2147483647;
            }
            _loops = param2;
            _soundTransform = param3;
            _soundChannel = _sound.play(param1,param2,param3);
            if(_soundChannel == null)
            {
               return null;
            }
            _soundChannels.push(_soundChannel);
            _soundChannel.addEventListener("soundComplete",complete,false,0,true);
            _playing = true;
            return _soundChannel;
         }
         return null;
      }
      
      public function complete(param1:Event) : void
      {
         var _loc2_:SoundChannel = param1.currentTarget as SoundChannel;
         _loc2_.stop();
         _soundChannels.splice(_soundChannels.indexOf(_loc2_),1);
         if(_canPlaySound && _soundChannel == _loc2_)
         {
            _playing = false;
         }
      }
      
      public function stop() : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            _soundChannel.stop();
            _loops = 0;
            _playing = false;
         }
      }
      
      public function get playing() : Boolean
      {
         return _playing;
      }
      
      public function get soundTransform() : SoundTransform
      {
         if(_canPlaySound && _soundChannel != null)
         {
            return _soundTransform;
         }
         return null;
      }
      
      public function set soundTransform(param1:SoundTransform) : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            _soundTransform = param1;
            if(!_muted && _soundTransform)
            {
               _soundChannel.soundTransform = _soundTransform;
            }
         }
      }
      
      public function get name() : String
      {
         return _name;
      }
      
      public function set name(param1:String) : void
      {
         _name = param1;
      }
      
      public function get sound() : Sound
      {
         return _sound;
      }
      
      public function set sound(param1:Sound) : void
      {
         _sound = param1;
      }
      
      public function get soundChannel() : SoundChannel
      {
         return _soundChannel;
      }
      
      public function get volume() : Number
      {
         if(_canPlaySound && _soundChannel != null)
         {
            return _soundChannel.soundTransform.volume;
         }
         return 0;
      }
      
      public function set volume(param1:Number) : void
      {
         var _loc2_:SoundTransform = null;
         if(_canPlaySound && _soundChannel != null)
         {
            if(_soundTransform == null)
            {
               return;
            }
            _loc2_ = _soundTransform;
            _loc2_.volume = param1;
            _soundTransform = _loc2_;
            if(!_muted)
            {
               _soundChannel.soundTransform = _soundTransform;
            }
         }
      }
      
      public function mute() : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            if(_muted)
            {
               if(_soundTransform)
               {
                  _soundChannel.soundTransform = _soundTransform;
               }
            }
            else
            {
               _soundChannel.soundTransform = new SoundTransform(0,0);
            }
            _muted = !_muted;
         }
      }
      
      public function turnMuteOn() : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            _soundChannel.soundTransform = new SoundTransform(0,0);
            _muted = true;
         }
      }
      
      public function turnMuteOff() : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            if(_soundTransform)
            {
               _soundChannel.soundTransform = _soundTransform;
            }
            _muted = false;
         }
      }
      
      public function get isMuted() : Boolean
      {
         return _muted;
      }
      
      public function pause() : void
      {
         if(_canPlaySound && _soundChannel != null)
         {
            if(_paused)
            {
               play(_pauseTime,_loops,_soundTransform);
            }
            else
            {
               _pauseTime = _soundChannel.position;
               _soundChannel.stop();
            }
            _paused = !_paused;
         }
      }
      
      public function get isPause() : Boolean
      {
         return _paused;
      }
   }
}

