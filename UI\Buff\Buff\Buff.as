package UI.Buff.Buff
{
   import UI.Event.UIPassiveEvent;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Other.CDAnimationShape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class Buff extends MySprite
   {
      
      protected var _buffVO:BuffVO;
      
      protected var _showSprite:Sprite;
      
      protected var _timeText:TextField;
      
      protected var _isStart:Boolean;
      
      protected var _cdAnimationShape:CDAnimationShape;
      
      public function Buff(param1:BuffVO)
      {
         super();
         _buffVO = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
         getShow(_buffVO);
         if(_buffVO.type != "noTimeBuff")
         {
            initCDAnimationShape();
         }
         setTimeShow(_buffVO);
         setStateShow(_buffVO);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _buffVO = null;
         _showSprite = null;
         _timeText = null;
         _cdAnimationShape = null;
      }
      
      public function get isStart() : Boolean
      {
         return _isStart;
      }
      
      protected function init() : void
      {
         _showSprite = new Sprite();
         _timeText = new TextField();
         _timeText.defaultTextFormat = new TextFormat(null,15,16777215,true);
         addChild(_timeText);
         _timeText.selectable = false;
         addChild(_showSprite);
         buttonMode = true;
      }
      
      protected function getShow(param1:BuffVO) : void
      {
         while(_showSprite.numChildren > 0)
         {
            _showSprite.removeChildAt(0);
         }
         _showSprite.addChild(MyFunction2.returnShowByClassName(param1.className));
      }
      
      protected function initCDAnimationShape() : void
      {
         _cdAnimationShape = new CDAnimationShape();
         _cdAnimationShape.x = _showSprite.x + _showSprite.width / 2;
         _cdAnimationShape.y = _showSprite.y + _showSprite.height / 2;
         _cdAnimationShape.init(_showSprite.width,_showSprite.height,0,0.8);
         _cdAnimationShape.drawShape(2 * 3.141592653589793,-1);
         addChild(_cdAnimationShape);
         _showSprite.mask = _cdAnimationShape;
      }
      
      protected function setTimeShow(param1:BuffVO) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         if(param1.type != "noTimeBuff")
         {
            _loc4_ = param1.remainTime / 3600;
            _loc3_ = param1.remainTime - _loc4_ * 3600;
            _loc2_ = _loc3_ / 60;
            if(_loc4_)
            {
               _timeText.text = _loc4_ + "h";
            }
            else if(_loc2_)
            {
               _timeText.text = _loc2_ + "m";
            }
            else
            {
               _timeText.text = param1.remainTime + "s";
            }
         }
         else
         {
            _timeText.text = "N/A";
         }
         _timeText.width = _timeText.textWidth + 5;
         _timeText.height = _timeText.textHeight + 2;
         _timeText.x = (_showSprite.width - _timeText.width) / 2 + _showSprite.x;
         _timeText.y = _showSprite.y + _showSprite.height;
      }
      
      protected function setStateShow(param1:BuffVO) : void
      {
         if(!_showSprite.filters[0])
         {
            _showSprite.filters = [new GlowFilter(65280,1,10,10,2,1)];
            _timeText.filters = [new GlowFilter(65280,1,10,10,2,1)];
         }
         if(param1.type == "noTimeBuff")
         {
            return;
         }
         if(param1.remainTime <= 10)
         {
            if((_showSprite.filters[0] as GlowFilter).color != 16711680)
            {
               _showSprite.filters = [new GlowFilter(16711680,1,10,10,2,1)];
               _timeText.filters = [new GlowFilter(16711680,1,10,10,2,1)];
            }
            if(param1.remainTime >= 0)
            {
               _cdAnimationShape.drawShape(2 * 3.141592653589793 * param1.remainTime / 10,-1);
            }
         }
         if(param1.remainTime > 10 && (_showSprite.filters[0] as GlowFilter).color != 65280)
         {
            _showSprite.filters = [new GlowFilter(65280,1,10,10,2,1)];
            _timeText.filters = [new GlowFilter(65280,1,10,10,2,1)];
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",onRollOver,false,0,true);
         addEventListener("rollOut",onRollOut,false,0,true);
         _buffVO.addEventListener("changeBuffRemaineTime",changeTimeShow,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("rollOver",onRollOver,false);
         removeEventListener("rollOut",onRollOut,false);
         _buffVO.removeEventListener("changeBuffRemaineTime",changeTimeShow,false);
      }
      
      protected function changeTimeShow(param1:UIPassiveEvent) : void
      {
         setTimeShow(_buffVO);
         setStateShow(_buffVO);
      }
      
      protected function onRollOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_buffVO}));
      }
      
      protected function onRollOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

