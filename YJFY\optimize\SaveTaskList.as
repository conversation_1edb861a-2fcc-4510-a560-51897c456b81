package YJFY.optimize
{
   import YJFY.Utils.ClearUtil;
   
   public class SaveTaskList
   {
      
      private static var _instance:SaveTaskList;
      
      public var datalist:Vector.<SaveTaskInfo>;
      
      public function SaveTaskList()
      {
         super();
         datalist = new Vector.<SaveTaskInfo>();
      }
      
      public static function getInstance() : SaveTaskList
      {
         if(_instance == null)
         {
            _instance = new SaveTaskList();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(datalist);
         datalist = null;
      }
      
      public function addData(param1:SaveTaskInfo) : void
      {
         datalist.push(param1);
      }
      
      public function desData() : void
      {
         if(datalist.length > 0)
         {
            datalist.splice(0,1);
         }
      }
      
      public function getSaveCount() : int
      {
         return datalist.length;
      }
      
      public function getData() : SaveTaskInfo
      {
         if(datalist.length > 0)
         {
            return datalist[0];
         }
         return null;
      }
   }
}

