package YJFY.World.ProjectLayer
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class ProjectLayer
   {
      
      private var m_show:MovieClip;
      
      private var m_showLs:AnimationShowPlayLogicShell;
      
      private var m_screenX:Number;
      
      private var m_screenY:Number;
      
      private var m_screenZ:Number;
      
      public function ProjectLayer()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showLs);
         m_showLs = null;
      }
      
      public function init(param1:ProjectLayerData) : void
      {
         m_screenX = param1.getScreenX();
         m_screenY = param1.getScreenY();
         m_screenZ = param1.getScreenZ();
         m_show = param1.getShow();
         m_showLs = new AnimationShowPlayLogicShell();
         m_showLs.setShow(m_show,true);
      }
      
      public function getScreenX() : Number
      {
         return m_screenX;
      }
      
      public function getScreenY() : Number
      {
         return m_screenY;
      }
      
      public function getScreenZ() : Number
      {
         return m_screenZ;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
   }
}

