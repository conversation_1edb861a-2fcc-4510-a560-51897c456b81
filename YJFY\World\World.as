package YJFY.World
{
   import UI.EnterFrameTime;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.ContainerEntity.ContainerEntity;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.IEntityContainer;
   import YJFY.Entity.PlayerEntity;
   import YJFY.EntityAnimation.AnimationDefinitionManager;
   import YJFY.Loader.YJFYLoader;
   import YJFY.MySprite;
   import YJFY.Point3DPhysics.IP3DTime;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Point3DPhysics.P3DWorld;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.SoundManager.SoundManager2;
   import YJFY.Utils.ArrayUtil;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfColorTransforms;
   import YJFY.Utils.DataOfPoints;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.Camera.Camera;
   import YJFY.World.Camera.View.ColorTransformsDataFlashView;
   import YJFY.World.Camera.View.PointsDataShakeView;
   import YJFY.World.ProjectLayer.ProjectLayer;
   import YJFY.geom.Area3D.Cuboid;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.media.SoundTransform;
   
   public class World implements IP3DTime, IEntityContainer
   {
      
      private const m_const_abitBiger_X:uint = 100;
      
      private const m_const_abitBiger_Y:uint = 100;
      
      private const m_const_abitBiger_Z:uint = 100;
      
      private var m_worldArea:Cuboid;
      
      private var m_worldArea_abitBiger:Cuboid;
      
      private var m_worldScreanArea:Cuboid;
      
      private var m_allEntitys:Vector.<IEntity>;
      
      private var m_waitRemoveEntitys:Vector.<IEntity>;
      
      private var m_animation:AnimationShowPlayLogicShell;
      
      private var m_waitAddEntitys:Vector.<IEntity>;
      
      private var m_cameraFollowEntity:IEntity;
      
      private var m_transformCoordinate:TransformCoordinate;
      
      private var m_worldMainRenderLayer:ProjectLayer;
      
      private var m_allProjectLayers:Vector.<ProjectLayer>;
      
      private var m_frontLayer:MySprite;
      
      private var m_mainLayer:MySprite;
      
      private var m_groundLayer:MySprite;
      
      private var m_camera:Camera;
      
      private var m_screenTransformCoord:Coordinate;
      
      private var m_animationsInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitAddAnimaitionsInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitRemoveAnimationsInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_animationsOfOutWorldTimeInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitAddAnimationsOfOutWorldTimeInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitRemoveAnimationsOfOutWorldTimeInGround:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_animationsInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitAddAnimationsInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitRemoveAnimationsInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_animationsOfOutWorldTimeInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitAddAnimationsOfOutWorldTimeInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_waitRemoveAnimationsOfOutWorldTimeInFront:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_shakeView:PointsDataShakeView;
      
      private var m_flashView:ColorTransformsDataFlashView;
      
      private var m_shakeViewOutOfTime:PointsDataShakeView;
      
      private var m_flashViewOutOfTime:ColorTransformsDataFlashView;
      
      private var m_isStopWorldTime:Boolean;
      
      private var m_gameTime:Number;
      
      private var m_worldInitTimeToGameTime:Number;
      
      private var m_worldDelayTimeToGameTime:Number;
      
      private var m_worldTime:Number;
      
      private var m_worldExistTime:Number;
      
      private var m_worldExistTimeElapseRate:Number;
      
      private var m_addTimeOneFrameForExistTime:uint;
      
      private var m_addTimeOneFrame:uint;
      
      private var m_normalShowRenderInterval:uint = 60;
      
      private var m_normalShowLastRenderTime:Number;
      
      private var m_normalShowLastRenderWorldTime:Number;
      
      private var m_p3dWorld:P3DWorld;
      
      private var m_utilFunction:UtilFunction;
      
      private var m_isReady:Boolean;
      
      private var m_worldListeners:Vector.<IWorldListener>;
      
      private var m_cX:int;
      
      private var m_cY:int;
      
      private var m_cZ:int;
      
      private var m_cLengthX:int;
      
      private var m_cLengthY:int;
      
      private var m_cLengthZ:int;
      
      private var m_startX:Number;
      
      private var m_startY:Number;
      
      private var m_startZ:Number;
      
      private var m_cRangeX:Number;
      
      private var m_cRangeY:Number;
      
      private var m_cRangeZ:Number;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_soundManager:SoundManager2;
      
      private var m_animationDefinitionManager:AnimationDefinitionManager;
      
      private var m_i:int;
      
      private var m_length:int;
      
      private var m_index:int;
      
      private var m_displayObject:DisplayObject;
      
      private var m_coordinate:Coordinate;
      
      private var m_entity:IEntity;
      
      public var resurgenceNum1:int = 0;
      
      public var resurgenceNum2:int = 0;
      
      private var m_renderIndex:int;
      
      private var m_nReaderIndex1:int = 0;
      
      private var m_nReaderStartIndex1:int = 0;
      
      private var m_nReaderEndIndex1:int = 0;
      
      private var m_nReaderNum:int = 200;
      
      private var m_nTotalTime:int = 0;
      
      private var m_bReaderStart1:Boolean = false;
      
      private var m_bReaderStart2:Boolean = false;
      
      public function World()
      {
         super();
         m_allProjectLayers = new Vector.<ProjectLayer>();
         m_allEntitys = new Vector.<IEntity>();
         m_waitRemoveEntitys = new Vector.<IEntity>();
         m_waitAddEntitys = new Vector.<IEntity>();
         m_transformCoordinate = new TransformCoordinate();
         m_camera = new Camera();
         m_camera.setTransformcoordinate(m_transformCoordinate);
         m_camera.setWorld(this);
         m_camera.init();
         m_frontLayer = new MySprite();
         m_mainLayer = new MySprite();
         m_mainLayer.mouseChildren = true;
         m_mainLayer.mouseEnabled = true;
         m_groundLayer = new MySprite();
         m_screenTransformCoord = new Coordinate();
         m_worldArea = new Cuboid();
         m_worldArea_abitBiger = new Cuboid();
         m_worldScreanArea = new Cuboid();
         m_animationsInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_waitAddAnimaitionsInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_waitRemoveAnimationsInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_animationsOfOutWorldTimeInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_waitAddAnimationsOfOutWorldTimeInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_waitRemoveAnimationsOfOutWorldTimeInGround = new Vector.<AnimationShowPlayLogicShell>();
         m_animationsInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_waitAddAnimationsInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_waitRemoveAnimationsInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_animationsOfOutWorldTimeInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_waitAddAnimationsOfOutWorldTimeInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_waitRemoveAnimationsOfOutWorldTimeInFront = new Vector.<AnimationShowPlayLogicShell>();
         m_p3dWorld = new P3DWorld(new P3DVector3D(0,0,-500),false);
         m_shakeView = new PointsDataShakeView();
         m_shakeViewOutOfTime = new PointsDataShakeView();
         m_flashView = new ColorTransformsDataFlashView();
         m_flashViewOutOfTime = new ColorTransformsDataFlashView();
         m_utilFunction = new UtilFunction();
         m_worldListeners = new Vector.<IWorldListener>();
         m_worldExistTimeElapseRate = 1;
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         ClearUtil.clearObject(m_worldArea);
         m_worldArea = null;
         ClearUtil.clearObject(m_worldArea_abitBiger);
         m_worldArea_abitBiger = null;
         ClearUtil.clearObject(m_worldScreanArea);
         m_worldScreanArea = null;
         if(m_allEntitys)
         {
            _loc1_ = int(m_allEntitys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(Boolean(m_mainLayer) && m_allEntitys[_loc2_].getShow() != null && m_mainLayer.contains(m_allEntitys[_loc2_].getShow()))
               {
                  m_mainLayer.removeChild(m_allEntitys[_loc2_].getShow());
               }
               if(Boolean(m_groundLayer) && m_allEntitys[_loc2_].getShadeShow() != null && m_groundLayer.contains(m_allEntitys[_loc2_].getShadeShow()))
               {
                  m_groundLayer.removeChild(m_allEntitys[_loc2_].getShadeShow());
               }
               m_allEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_allEntitys.length = 0;
            m_allEntitys = null;
         }
         if(m_utilFunction)
         {
            m_utilFunction.clearOtherAnimations(m_animationsInGround,m_groundLayer);
         }
         m_animationsInGround = null;
         ClearUtil.nullArr(m_waitAddAnimaitionsInGround,false,false,false);
         m_waitAddAnimaitionsInGround = null;
         ClearUtil.nullArr(m_waitRemoveAnimationsInGround,false,false,false);
         m_waitRemoveAnimationsInGround = null;
         if(m_utilFunction)
         {
            m_utilFunction.clearOtherAnimations(m_animationsOfOutWorldTimeInGround,m_groundLayer);
         }
         m_animationsOfOutWorldTimeInGround = null;
         ClearUtil.nullArr(m_waitAddAnimationsOfOutWorldTimeInGround,false,false,false);
         m_waitAddAnimationsOfOutWorldTimeInGround = null;
         ClearUtil.nullArr(m_waitRemoveAnimationsOfOutWorldTimeInGround,false,false,false);
         m_waitRemoveAnimationsOfOutWorldTimeInGround = null;
         if(m_utilFunction)
         {
            m_utilFunction.clearOtherAnimations(m_animationsInFront,m_groundLayer);
         }
         m_animationsInFront = null;
         ClearUtil.nullArr(m_waitAddAnimationsInFront,false,false,false);
         m_waitAddAnimationsInFront = null;
         ClearUtil.nullArr(m_waitRemoveAnimationsInFront,false,false,false);
         m_waitRemoveAnimationsInFront = null;
         if(m_utilFunction)
         {
            m_utilFunction.clearOtherAnimations(m_animationsOfOutWorldTimeInFront,m_groundLayer);
         }
         m_animationsOfOutWorldTimeInFront = null;
         ClearUtil.nullArr(m_waitAddAnimationsOfOutWorldTimeInFront,false,false,false);
         m_waitAddAnimationsOfOutWorldTimeInFront = null;
         ClearUtil.nullArr(m_waitRemoveAnimationsOfOutWorldTimeInFront,false,false,false);
         m_waitRemoveAnimationsOfOutWorldTimeInFront = null;
         ClearUtil.nullArr(m_allEntitys,false,false,false);
         m_allEntitys = null;
         ClearUtil.nullArr(m_waitRemoveEntitys,false,false,false);
         m_waitRemoveEntitys = null;
         ClearUtil.nullArr(m_waitAddEntitys,false,false,false);
         m_waitAddEntitys = null;
         m_cameraFollowEntity = null;
         ClearUtil.clearObject(m_transformCoordinate);
         m_transformCoordinate = null;
         ClearUtil.clearObject(m_worldMainRenderLayer);
         m_worldMainRenderLayer = null;
         ClearUtil.clearObject(m_allProjectLayers);
         m_allProjectLayers = null;
         ClearUtil.clearObject(m_frontLayer);
         m_frontLayer = null;
         ClearUtil.clearObject(m_mainLayer);
         m_mainLayer = null;
         ClearUtil.clearObject(m_groundLayer);
         m_groundLayer = null;
         ClearUtil.clearObject(m_camera);
         m_camera = null;
         ClearUtil.clearObject(m_screenTransformCoord);
         m_screenTransformCoord = null;
         ClearUtil.clearObject(m_shakeView);
         m_shakeView = null;
         ClearUtil.clearObject(m_flashView);
         m_flashView = null;
         ClearUtil.clearObject(m_shakeViewOutOfTime);
         m_shakeViewOutOfTime = null;
         ClearUtil.clearObject(m_flashViewOutOfTime);
         m_flashViewOutOfTime = null;
         ClearUtil.clearObject(m_p3dWorld);
         m_p3dWorld = null;
         ClearUtil.clearObject(m_utilFunction);
         m_utilFunction = null;
         ClearUtil.nullArr(m_worldListeners,false,false,false);
         m_worldListeners = null;
         m_myLoader = null;
         m_enterFrameTime = null;
         m_soundManager = null;
         m_animationDefinitionManager = null;
         m_displayObject = null;
         m_coordinate = null;
         m_entity = null;
      }
      
      public function setAnimationDefinitionManager(param1:AnimationDefinitionManager) : void
      {
         m_animationDefinitionManager = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
         m_animationDefinitionManager.setLoader(m_myLoader);
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
         m_gameTime = param1.getGameTimeForThisInit();
         m_worldInitTimeToGameTime = param1.getGameTimeForThisInit();
         m_worldDelayTimeToGameTime = 0;
         m_worldExistTime = 0;
         m_worldTime = 0;
         m_normalShowLastRenderTime = 0;
         m_normalShowLastRenderWorldTime = 0;
      }
      
      public function setSoundManger(param1:SoundManager2) : void
      {
         m_soundManager = param1;
      }
      
      public function initWoldArea(param1:Number, param2:Number, param3:Number, param4:Number, param5:Number, param6:Number) : void
      {
         m_worldArea.setTo(param1,param2,param3,param4,param5,param6);
         m_worldArea_abitBiger.setTo(param1 - 100 / 2,param2 - 100 / 2,param3 - 100 / 2,param4 + 100,param5 + 100,param6 + 100);
         var _loc11_:Number = m_worldArea.getMaxX();
         var _loc7_:Number = m_worldArea.getMinX();
         var _loc10_:Number = m_transformCoordinate.getMaxScreenY(m_worldArea.getMinY(),m_worldArea.getMaxZ());
         var _loc9_:Number = m_transformCoordinate.getMinScreenY(m_worldArea.getMaxY(),m_worldArea.getMinZ());
         var _loc8_:Number = m_transformCoordinate.getMaxScreenZ(m_worldArea.getMinY(),m_worldArea.getMinZ());
         var _loc12_:Number = m_transformCoordinate.getMinScreenZ(m_worldArea.getMaxY(),m_worldArea.getMaxZ());
         m_worldScreanArea.setTo2(_loc7_,_loc9_,_loc12_,_loc11_,_loc10_,_loc8_);
         m_isReady = true;
      }
      
      public function initProjectLayers(param1:ProjectLayer, param2:Vector.<ProjectLayer>) : void
      {
         var _loc5_:int = 0;
         var _loc4_:ProjectLayer = null;
         m_worldMainRenderLayer = param1;
         m_worldMainRenderLayer.getShow().addChild(m_groundLayer);
         m_worldMainRenderLayer.getShow().addChild(m_mainLayer);
         m_worldMainRenderLayer.getShow().addChild(m_frontLayer);
         m_allProjectLayers.push(m_worldMainRenderLayer);
         m_transformCoordinate.setZChange(m_worldMainRenderLayer.getScreenZ());
         m_camera.setWorldMainRenderLayerScreenZ(m_worldMainRenderLayer.getScreenZ());
         m_camera.setNewScreenCoordinate(0,0 - 560);
         var _loc3_:int = param2 ? param2.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = param2[_loc5_];
            m_allProjectLayers.push(_loc4_);
            _loc5_++;
         }
         m_allProjectLayers.sort(sortProjectLayer);
         _loc3_ = int(m_allProjectLayers.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            m_camera.getView().addChild(m_allProjectLayers[_loc5_].getShow());
            _loc5_++;
         }
      }
      
      public function addWorldListener(param1:IWorldListener) : void
      {
         ListenerUtil.addListener(m_worldListeners,param1);
      }
      
      public function removeWorldListener(param1:IWorldListener) : void
      {
         ListenerUtil.removeListener(m_worldListeners,param1);
      }
      
      public function setCameraCentreScreenX(param1:Number) : void
      {
         if(m_cameraFollowEntity)
         {
            return;
         }
         m_camera.setNewScreenCoordinate(Math.min(m_worldScreanArea.getMaxX() - 960,Math.max(m_worldScreanArea.getMinX(),param1 - 960 / 2)),m_camera.getScreenY());
      }
      
      public function setWorldExistTimeElapseRate(param1:Number) : void
      {
         if(isNaN(param1))
         {
            throw new Error("elapseRate is NaN.");
         }
         if(param1 < 0)
         {
            throw new Error("elapseRate cann\'t be less than 0.");
         }
         m_worldExistTimeElapseRate = param1;
      }
      
      public function checkIn(param1:AnimationShowPlayLogicShell) : Boolean
      {
         var _loc2_:Boolean = false;
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_animationsOfOutWorldTimeInGround.length)
         {
            if(param1 == m_animationsOfOutWorldTimeInGround[_loc3_])
            {
               _loc2_ = true;
               break;
            }
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < m_animationsOfOutWorldTimeInFront.length)
         {
            if(param1 == m_animationsOfOutWorldTimeInFront[_loc3_])
            {
               _loc2_ = true;
               break;
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         m_length = m_waitRemoveEntitys.length;
         m_i = 0;
         while(m_i < m_length)
         {
            m_entity = m_waitRemoveEntitys[m_i];
            m_index = m_allEntitys.indexOf(m_entity);
            while(m_index != -1)
            {
               m_p3dWorld.removeBody(m_entity.getBody());
               if(Boolean(m_entity.getShow()) && m_entity.getShow().parent)
               {
                  m_entity.getShow().parent.removeChild(m_entity.getShow());
               }
               if(Boolean(m_entity.getShadeShow()) && m_entity.getShadeShow().parent)
               {
                  m_entity.getShadeShow().parent.removeChild(m_entity.getShadeShow());
               }
               m_allEntitys.splice(m_index,1);
               m_index = m_allEntitys.indexOf(m_entity);
               m_entity.setParent(null,null);
            }
            ++m_i;
         }
         m_waitRemoveEntitys.length = 0;
         if(m_waitRemoveAnimationsInGround.length > 0)
         {
            m_animation = m_waitRemoveAnimationsInGround[0];
            m_utilFunction.removeAniamtionFromAnimations(m_animation,m_animationsInGround,m_groundLayer);
            m_waitRemoveAnimationsInGround.splice(0,1);
         }
         if(m_waitRemoveAnimationsOfOutWorldTimeInGround.length > 0)
         {
            m_animation = m_waitRemoveAnimationsOfOutWorldTimeInGround[0];
            m_utilFunction.removeAniamtionFromAnimations(m_animation,m_animationsOfOutWorldTimeInGround,m_groundLayer);
            m_waitRemoveAnimationsOfOutWorldTimeInGround.splice(0,1);
         }
         if(m_waitRemoveAnimationsInFront.length > 0)
         {
            m_animation = m_waitRemoveAnimationsInFront[0];
            m_utilFunction.removeAniamtionFromAnimations(m_animation,m_animationsInFront,m_frontLayer);
            m_waitRemoveAnimationsInFront.splice(0,1);
         }
         if(m_waitRemoveAnimationsOfOutWorldTimeInFront.length > 0)
         {
            m_animation = m_waitRemoveAnimationsOfOutWorldTimeInFront[0];
            m_utilFunction.removeAniamtionFromAnimations(m_animation,m_animationsOfOutWorldTimeInFront,m_frontLayer);
            m_waitRemoveAnimationsOfOutWorldTimeInFront.splice(0,1);
         }
         renderWorldTime(param1);
         m_p3dWorld.render(this);
         m_length = m_allEntitys.length;
         m_i = 0;
         while(m_i < m_length)
         {
            if(m_allEntitys[m_i].getWorld())
            {
               m_allEntitys[m_i].render(this);
            }
            ++m_i;
         }
         if(m_isReady && m_cameraFollowEntity)
         {
            m_camera.setNewScreenCoordinate(Math.min(m_worldScreanArea.getMaxX() - 960,Math.max(m_worldScreanArea.getMinX(),m_cameraFollowEntity.getScreenX() - 960 / 2)),m_camera.getScreenY());
         }
         if(m_waitAddAnimaitionsInGround.length > 0)
         {
            m_animation = m_waitAddAnimaitionsInGround[0];
            m_utilFunction.addAnimationFromAnimations(m_animation,m_animationsInGround,m_groundLayer);
            m_waitAddAnimaitionsInGround.splice(0,1);
         }
         if(m_waitAddAnimationsOfOutWorldTimeInGround.length > 0)
         {
            m_animation = m_waitAddAnimationsOfOutWorldTimeInGround[0];
            m_utilFunction.addAnimationFromAnimations(m_animation,m_animationsOfOutWorldTimeInGround,m_groundLayer);
            m_waitAddAnimationsOfOutWorldTimeInGround.splice(0,1);
         }
         if(m_waitAddAnimationsInFront.length > 0)
         {
            m_animation = m_waitAddAnimationsInFront[0];
            m_utilFunction.addAnimationFromAnimations(m_animation,m_animationsInFront,m_frontLayer);
            m_waitAddAnimationsInFront.splice(0,1);
         }
         if(m_waitAddAnimationsOfOutWorldTimeInFront.length > 0)
         {
            m_animation = m_waitAddAnimationsOfOutWorldTimeInFront[0];
            m_utilFunction.addAnimationFromAnimations(m_animation,m_animationsOfOutWorldTimeInFront,m_frontLayer);
            m_waitAddAnimationsOfOutWorldTimeInFront.splice(0,1);
         }
         while(m_waitAddEntitys.length > 0)
         {
            m_entity = m_waitAddEntitys[0];
            m_waitAddEntitys.splice(0,1);
            if(m_entity != null)
            {
               if(m_entity.getBody() != null)
               {
                  if(m_entity.getShow() != null)
                  {
                     m_index = m_allEntitys.indexOf(m_entity);
                     if(m_index == -1)
                     {
                        m_allEntitys.push(m_entity);
                        m_p3dWorld.addBody(m_entity.getBody());
                        m_entity.setParent(this,this);
                        m_mainLayer.addChild(m_entity.getShow());
                        if(m_entity.getShadeShow())
                        {
                           m_groundLayer.addChild(m_entity.getShadeShow());
                        }
                        addAEntityListener(m_entity);
                     }
                  }
               }
            }
         }
      }
      
      public function showRender() : void
      {
         refreshShowAllProjectLayers();
         sortAllItems();
         if(m_worldExistTime - m_normalShowLastRenderTime > m_normalShowRenderInterval)
         {
            m_normalShowLastRenderTime = m_worldExistTime;
            if(m_worldTime - m_normalShowLastRenderWorldTime > m_normalShowRenderInterval)
            {
               m_normalShowLastRenderWorldTime = m_worldTime;
               m_length = m_allEntitys.length;
               m_i = 0;
               while(m_i < m_length)
               {
                  if(m_allEntitys[m_i].getWorld())
                  {
                     m_allEntitys[m_i].showRender();
                  }
                  ++m_i;
               }
               renderAnimations(m_animationsInGround);
               renderAnimations(m_animationsInFront);
               m_shakeView.render();
               m_flashView.render();
            }
            else
            {
               m_length = m_allEntitys.length;
               m_i = 0;
               while(m_i < m_length)
               {
                  if(m_allEntitys[m_i].getWorld() != null)
                  {
                     if(m_allEntitys[m_i] is IAnimalEntity && ((m_allEntitys[m_i] as IAnimalEntity).isInHurt() || (m_allEntitys[m_i] as IAnimalEntity).isInDie()))
                     {
                        m_allEntitys[m_i].showRender();
                     }
                  }
                  ++m_i;
               }
            }
            renderAnimations(m_animationsOfOutWorldTimeInFront);
            renderAnimations(m_animationsOfOutWorldTimeInGround);
            m_shakeViewOutOfTime.render();
            m_flashViewOutOfTime.render();
         }
      }
      
      public function stopWorldTime() : void
      {
         m_isStopWorldTime = true;
      }
      
      public function continueWorldTime() : void
      {
         m_isStopWorldTime = false;
      }
      
      public function shakeView(param1:DataOfPoints) : void
      {
         m_shakeView.initByDataOfPoints(param1);
         m_shakeView.shakeView(m_camera.getView());
      }
      
      public function flashView(param1:DataOfColorTransforms) : void
      {
         m_flashView.initByDataOfColorTransforms(param1);
         m_flashView.flashView(m_camera.getView());
      }
      
      public function shakeViewOutOfTime(param1:DataOfPoints) : void
      {
         m_shakeViewOutOfTime.initByDataOfPoints(param1);
         m_shakeViewOutOfTime.shakeView(m_camera.getView());
      }
      
      public function flashViewOutOfTime(param1:DataOfColorTransforms) : void
      {
         m_flashViewOutOfTime.initByDataOfColorTransforms(param1);
         m_flashViewOutOfTime.flashView(m_camera.getView());
      }
      
      public function isStop() : Boolean
      {
         return m_isStopWorldTime;
      }
      
      public function getEntityCoordinateInView(param1:IEntity, param2:Coordinate) : void
      {
         param2.setX(param1.getScreenX() - m_camera.getScreenX());
         param2.setY(param1.getScreenY() - m_camera.getScreenY());
         param2.setZ(param1.getScreenZ() - m_camera.getScreenZ());
      }
      
      public function getViewCoordinateFromScreenCoordinate(param1:Coordinate, param2:Coordinate) : void
      {
         param2.setX(param1.getX() - m_camera.getScreenX());
         param2.setY(param1.getY() - m_camera.getScreenY());
         param2.setZ(param1.getZ() - m_camera.getScreenZ());
      }
      
      public function getWorldCoordinateFromViewCoordinate(param1:Number, param2:Number, param3:Number, param4:Coordinate) : void
      {
         m_transformCoordinate.screenToWorld22(param1 + m_camera.getScreenX(),param2 + m_camera.getScreenY(),param4,param3);
      }
      
      public function addEntity(param1:IEntity) : void
      {
         m_waitAddEntitys.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitRemoveEntitys);
      }
      
      public function setCameraFollowEntity(param1:IEntity) : void
      {
         m_cameraFollowEntity = param1;
      }
      
      public function addLight(param1:MovieClip) : void
      {
         if(param1)
         {
            m_mainLayer.addChild(param1);
         }
      }
      
      private function addEntityFromWaitAddEntitys() : void
      {
         while(m_waitAddEntitys.length > 0)
         {
            m_entity = m_waitAddEntitys[0];
            m_waitAddEntitys.splice(0,1);
            if(m_entity != null)
            {
               if(m_entity.getBody() != null)
               {
                  if(m_entity.getShow() != null)
                  {
                     m_index = m_allEntitys.indexOf(m_entity);
                     if(m_index == -1)
                     {
                        m_allEntitys.push(m_entity);
                        m_p3dWorld.addBody(m_entity.getBody());
                        m_entity.setParent(this,this);
                        m_mainLayer.addChild(m_entity.getShow());
                        if(m_entity.getShadeShow())
                        {
                           m_groundLayer.addChild(m_entity.getShadeShow());
                        }
                        addAEntityListener(m_entity);
                     }
                  }
               }
            }
         }
      }
      
      public function removeEntity(param1:IEntity) : void
      {
         m_waitRemoveEntitys.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitAddEntitys);
      }
      
      public function addShowInGround(param1:DisplayObject) : void
      {
         if(param1)
         {
            m_groundLayer.addChild(param1);
         }
      }
      
      public function removeShowInGround(param1:DisplayObject) : void
      {
         if(Boolean(param1) && param1.parent == m_groundLayer)
         {
            m_groundLayer.removeChild(param1);
         }
      }
      
      public function addAnimationInGround(param1:AnimationShowPlayLogicShell) : void
      {
         if(param1 == null)
         {
            throw new Error("animation 不能为null");
         }
         m_waitAddAnimaitionsInGround.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitRemoveAnimationsInGround);
      }
      
      public function addAnimationOfOutWorldTimeInGround(param1:AnimationShowPlayLogicShell) : void
      {
         if(param1 == null)
         {
            throw new Error("animation 不能为null");
         }
         m_waitAddAnimationsOfOutWorldTimeInGround.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitRemoveAnimationsOfOutWorldTimeInGround);
      }
      
      public function addAnimationInFront(param1:AnimationShowPlayLogicShell) : void
      {
         if(param1 == null)
         {
            throw new Error("animation 不能为null");
         }
         m_waitAddAnimationsInFront.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitRemoveAnimationsInFront);
      }
      
      public function addAnimationOfOutWorldTimeInFront(param1:AnimationShowPlayLogicShell) : void
      {
         if(param1 == null)
         {
            throw new Error("animation 不能为null");
         }
         m_waitAddAnimationsOfOutWorldTimeInFront.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitRemoveAnimationsOfOutWorldTimeInFront);
      }
      
      public function removeAnimationInGround(param1:AnimationShowPlayLogicShell) : void
      {
         m_waitRemoveAnimationsInGround.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitAddAnimaitionsInGround);
      }
      
      public function removeAnimationOfOutWorldTimeInGround(param1:AnimationShowPlayLogicShell) : void
      {
         m_waitRemoveAnimationsOfOutWorldTimeInGround.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitAddAnimationsOfOutWorldTimeInGround);
      }
      
      public function removeAnimationInFront(param1:AnimationShowPlayLogicShell) : void
      {
         m_waitRemoveAnimationsInFront.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitAddAnimationsInFront);
      }
      
      public function removeAnimationOfOutWorldTimeInFront(param1:AnimationShowPlayLogicShell) : void
      {
         m_waitRemoveAnimationsOfOutWorldTimeInFront.push(param1);
         ArrayUtil.removeItemFromArrays(param1,m_waitAddAnimationsOfOutWorldTimeInFront);
      }
      
      public function addSound(param1:SoundData) : void
      {
         m_soundManager.addSound2(param1.getName(),param1.getSwfPath(),param1.getClassName());
      }
      
      public function playSound(param1:SoundData, param2:int = 0) : void
      {
         if(param1 == null)
         {
            return;
         }
         var _loc3_:SoundTransform = new SoundTransform(1);
         m_soundManager.play2(param1.getName(),param1.getSwfPath(),param1.getClassName(),0,param2,_loc3_);
      }
      
      public function getAnimationDefinitionManager() : AnimationDefinitionManager
      {
         return m_animationDefinitionManager;
      }
      
      public function getSoundManager() : SoundManager2
      {
         return m_soundManager;
      }
      
      public function getTransformCoordinate() : TransformCoordinate
      {
         return m_transformCoordinate;
      }
      
      public function getCamera() : Camera
      {
         return m_camera;
      }
      
      public function getWorldArea() : Cuboid
      {
         return m_worldArea;
      }
      
      public function getWorldArea_abitBiger() : Cuboid
      {
         return m_worldArea_abitBiger;
      }
      
      public function getAllEntityNum() : uint
      {
         return m_allEntitys.length;
      }
      
      public function getAllEnttiyByIndex(param1:int) : IEntity
      {
         return m_allEntitys[param1];
      }
      
      public function getEnterFrameTime() : EnterFrameTime
      {
         return m_enterFrameTime;
      }
      
      public function getWorldTime() : Number
      {
         return m_worldTime;
      }
      
      public function getWorldExistTime() : Number
      {
         return m_worldExistTime;
      }
      
      public function getAddTimeOneFrame() : uint
      {
         return m_addTimeOneFrame;
      }
      
      public function getAddTimeOneFrameForExistTime() : uint
      {
         return m_addTimeOneFrameForExistTime;
      }
      
      public function cuboidRangeIsContainsEntity(param1:Cuboid, param2:IEntity) : Boolean
      {
         m_cRangeX = Math.min(param2.getBodyXRange(),param1.getXRange());
         m_cRangeY = Math.min(param2.getBodyYRange(),param1.getYRange());
         m_cRangeZ = Math.min(param2.getBodyZRange(),param1.getZRange());
         m_startX = param2.getX() - param2.getBodyXRange() / 2;
         m_startZ = param2.getZ();
         m_cLengthX = Math.floor(param2.getBodyXRange() / m_cRangeX);
         m_cLengthZ = Math.floor(param2.getBodyZRange() / m_cRangeZ);
         if(param2.getBodyYRange())
         {
            m_cLengthY = Math.floor(param2.getBodyYRange() / m_cRangeY);
            m_startY = param2.getY() - param2.getBodyYRange() / 2;
            m_cX = 0;
            while(m_cX <= m_cLengthX)
            {
               m_cY = 0;
               while(m_cY <= m_cLengthY)
               {
                  m_cZ = 0;
                  while(m_cZ <= m_cLengthZ)
                  {
                     if(param1.contains(m_startX + m_cX * m_cRangeX,m_startY + m_cY * m_cRangeY,m_startZ + m_cZ * m_cRangeZ))
                     {
                        return true;
                     }
                     ++m_cZ;
                  }
                  ++m_cY;
               }
               ++m_cX;
            }
         }
         else
         {
            m_cX = 0;
            while(m_cX <= m_cLengthX)
            {
               m_cZ = 0;
               while(m_cZ <= m_cLengthZ)
               {
                  if(param1.contains(m_startX + m_cX * m_cRangeX,param2.getY(),m_startZ + m_cZ * m_cRangeZ))
                  {
                     return true;
                  }
                  ++m_cZ;
               }
               ++m_cX;
            }
         }
         return false;
      }
      
      protected function sortFun(param1:IEntity, param2:IEntity) : Number
      {
         return param2.getScreenZ() - param1.getScreenZ();
      }
      
      protected function sortProjectLayer(param1:ProjectLayer, param2:ProjectLayer) : Number
      {
         return param2.getScreenZ() - param1.getScreenZ();
      }
      
      protected function sortAllItems() : void
      {
         m_allEntitys.sort(sortFun);
         m_length = m_allEntitys.length;
         m_i = 0;
         while(m_i < m_length)
         {
            m_displayObject = m_allEntitys[m_i].getShow() as DisplayObject;
            m_displayObject.x = m_allEntitys[m_i].getScreenX();
            m_displayObject.y = m_allEntitys[m_i].getScreenY();
            if(m_displayObject.parent == m_mainLayer)
            {
               m_mainLayer.setChildIndex(m_displayObject,m_mainLayer.numChildren - 1);
            }
            m_displayObject = m_allEntitys[m_i].getShadeShow() as DisplayObject;
            if(m_displayObject)
            {
               m_displayObject.x = m_allEntitys[m_i].getGroundScreenX();
               m_displayObject.y = m_allEntitys[m_i].getGroundScreenY();
            }
            ++m_i;
         }
      }
      
      protected function EntityRenderAllItems0() : void
      {
         if(m_bReaderStart1 || m_bReaderStart2)
         {
            return;
         }
         m_length = m_allEntitys.length;
         if(m_length % m_nReaderNum == 0)
         {
            m_nTotalTime = m_length / m_nReaderNum;
         }
         else
         {
            m_nTotalTime = m_length / m_nReaderNum + 1;
         }
         m_nReaderIndex1 = 0;
         m_bReaderStart1 = true;
         m_bReaderStart2 = false;
      }
      
      protected function EntityRenderAllItems1() : void
      {
         if(m_bReaderStart1 == false || m_bReaderStart2)
         {
            return;
         }
         if(m_nReaderIndex1 > m_nTotalTime)
         {
            m_bReaderStart1 = false;
            m_bReaderStart2 = false;
            m_nReaderIndex1 = 0;
            return;
         }
         m_nReaderStartIndex1 = m_nReaderIndex1 * m_nReaderNum;
         m_nReaderEndIndex1 = m_nReaderStartIndex1 + m_nReaderNum - 1;
         m_nReaderIndex1++;
         m_bReaderStart1 = false;
         m_bReaderStart2 = true;
      }
      
      protected function EntityRenderAllItems2() : void
      {
         if(m_bReaderStart1 || m_bReaderStart2 == false)
         {
            return;
         }
         m_i = m_nReaderStartIndex1;
         while(m_i <= m_nReaderEndIndex1 && m_i < m_allEntitys.length)
         {
            if(!(m_allEntitys[m_i].getWorld() == null || m_allEntitys[m_i] is PlayerEntity || m_allEntitys[m_i] is AnimalEntity || m_allEntitys[m_i] is ContainerEntity))
            {
               m_allEntitys[m_i].render(this);
            }
            ++m_i;
         }
         m_bReaderStart1 = true;
         m_bReaderStart2 = false;
      }
      
      protected function refreshShowAllProjectLayers() : void
      {
         m_length = m_allProjectLayers.length;
         m_i = 0;
         while(m_i < m_length)
         {
            m_coordinate = m_camera.getView().projectScreenCoordToView(m_allProjectLayers[m_i].getScreenX(),m_allProjectLayers[m_i].getScreenY(),m_allProjectLayers[m_i].getScreenZ());
            m_allProjectLayers[m_i].getShow().x = m_coordinate.getX();
            m_allProjectLayers[m_i].getShow().y = m_coordinate.getY();
            m_i++;
         }
      }
      
      protected function renderWorldTime(param1:EnterFrameTime) : void
      {
         if(isNaN(m_worldInitTimeToGameTime))
         {
            return;
         }
         m_addTimeOneFrameForExistTime = param1.getGameTimeForThisInit() - m_gameTime;
         m_addTimeOneFrame = 0;
         if(m_addTimeOneFrameForExistTime > param1.getAddTimeOneFrame())
         {
            m_addTimeOneFrameForExistTime = param1.getAddTimeOneFrame();
         }
         else if(m_addTimeOneFrameForExistTime < 0)
         {
            m_addTimeOneFrameForExistTime = param1.getAddTimeOneFrame();
         }
         m_gameTime = param1.getGameTimeForThisInit();
         m_addTimeOneFrameForExistTime *= m_worldExistTimeElapseRate;
         m_worldExistTime += m_addTimeOneFrameForExistTime;
         if(m_isStopWorldTime == false)
         {
            m_worldTime = m_worldExistTime - m_worldDelayTimeToGameTime;
            m_addTimeOneFrame = m_addTimeOneFrameForExistTime;
         }
         else
         {
            m_worldDelayTimeToGameTime += m_addTimeOneFrameForExistTime;
            m_addTimeOneFrame = 0;
         }
      }
      
      protected function removeEntityFromWaitRemoveEntitys() : void
      {
         m_length = m_waitRemoveEntitys.length;
         m_i = 0;
         while(m_i < m_length)
         {
            m_entity = m_waitRemoveEntitys[m_i];
            m_index = m_allEntitys.indexOf(m_entity);
            while(m_index != -1)
            {
               m_p3dWorld.removeBody(m_entity.getBody());
               if(Boolean(m_entity.getShow()) && m_entity.getShow().parent)
               {
                  m_entity.getShow().parent.removeChild(m_entity.getShow());
               }
               if(Boolean(m_entity.getShadeShow()) && m_entity.getShadeShow().parent)
               {
                  m_entity.getShadeShow().parent.removeChild(m_entity.getShadeShow());
               }
               m_allEntitys.splice(m_index,1);
               m_index = m_allEntitys.indexOf(m_entity);
               m_entity.setParent(null,null);
            }
            ++m_i;
         }
         m_waitRemoveEntitys.length = 0;
      }
      
      private function renderAnimations(param1:Vector.<AnimationShowPlayLogicShell>) : void
      {
         m_length = param1.length;
         m_i = 0;
         while(m_i < m_length)
         {
            param1[m_i].render();
            ++m_i;
         }
      }
      
      private function addAEntityListener(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IWorldListener> = m_worldListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_].addAEntityToWorld(this,param1);
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
   }
}

