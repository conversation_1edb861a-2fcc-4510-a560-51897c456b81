package YJFY.Skill
{
   public class SkillListener implements ISkillListener
   {
      
      public var startSkillFun:Function;
      
      public var endSkillFun:Function;
      
      public var releaseSkillFun:Function;
      
      public function SkillListener()
      {
         super();
      }
      
      public function clear() : void
      {
         startSkillFun = null;
         endSkillFun = null;
         releaseSkillFun = null;
      }
      
      public function startSkill(param1:Skill) : void
      {
         if(<PERSON><PERSON><PERSON>(startSkillFun))
         {
            startSkillFun(param1);
         }
      }
      
      public function endSkill(param1:Skill) : void
      {
         if(<PERSON><PERSON><PERSON>(endSkillFun))
         {
            endSkillFun(param1);
         }
      }
      
      public function releaseSkill(param1:Skill) : void
      {
         if(<PERSON><PERSON><PERSON>(releaseSkillFun))
         {
            releaseSkillFun(param1);
         }
      }
   }
}

