package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_AgreeOrRefuseJoinDetail extends InformationBodyDetail
   {
      
      private var m_uid_leader:Number;
      
      private var m_idx_leader:int;
      
      private var m_uid_applicant:Number;
      
      private var m_idx_applicant:int;
      
      private var m_isAgree:int;
      
      public function UP_AgreeOrRefuseJoinDetail()
      {
         super();
         m_informationBodyId = 3015;
      }
      
      public function initData(param1:Number, param2:int, param3:Number, param4:int, param5:int) : void
      {
         m_uid_leader = param1;
         m_idx_leader = param2;
         m_uid_applicant = param3;
         m_idx_applicant = param4;
         m_isAgree = param5;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid_leader,m_idx_leader);
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid_applicant,m_idx_applicant);
         _loc1_.writeInt(m_isAgree);
         return _loc1_;
      }
      
      public function getUidApplicant() : Number
      {
         return m_uid_applicant;
      }
      
      public function getIsAgree() : int
      {
         return m_isAgree;
      }
   }
}

