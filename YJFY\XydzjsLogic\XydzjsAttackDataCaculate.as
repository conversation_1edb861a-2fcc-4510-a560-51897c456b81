package YJFY.XydzjsLogic
{
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO;
   import YJFY.Entity.AttackData;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.XydzjsData.AttackDataCalculate;
   import YJFY.XydzjsData.IAttackDataCalculateVOXydzjs;
   import YJFY.XydzjsData.PlayerAISkillVO;
   
   public class XydzjsAttackDataCaculate
   {
      
      private var m_attackDataCalculate:AttackDataCalculate;
      
      public function XydzjsAttackDataCaculate()
      {
         super();
         m_attackDataCalculate = new AttackDataCalculate();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_attackDataCalculate);
         m_attackDataCalculate = null;
      }
      
      public function caculateAttackData(param1:AttackData, param2:IAttackDataCalculateVOXydzjs, param3:IAttackDataCalculateVOXydzjs, param4:ISkill) : void
      {
         param1.setIsAttack(true);
         param1.resetData(0,false,false,param2.getAttackHurtDuration());
         param2.setAttackSkill(null);
         if(param4)
         {
            if(param4.getExtra() is PlayerAISkillVO)
            {
               if((param4.getExtra() as PlayerAISkillVO).getSkillVO() is PlayerActiveSkillVO)
               {
                  param2.setAttackSkill((param4.getExtra() as PlayerAISkillVO).getSkillVO() as PlayerActiveSkillVO);
               }
               else if(!((param4.getExtra() as PlayerAISkillVO).getSkillVO() is PetActiveSkillVO))
               {
                  throw new Error("出错了");
               }
            }
            else
            {
               if(!(param4.getExtra() is AutomaticPetAttackSkillVO))
               {
                  throw new Error("出错了");
               }
               param2.setAttackSkill(param4.getExtra() as AutomaticPetAttackSkillVO);
            }
         }
         m_attackDataCalculate.caculateHurt(param2,param3,param1);
      }
   }
}

