package YJFY.Utils
{
   import YJFY.data.linkList.LinkList;
   import YJFY.data.linkList.Node;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Loader;
   import flash.utils.getQualifiedClassName;
   
   public class ClearUtil
   {
      
      public static const CLEAR:String = "clear";
      
      public function ClearUtil()
      {
         super();
      }
      
      public static function clearDisplayObjectInContainer(param1:DisplayObjectContainer, param2:Boolean = true, param3:Boolean = true) : void
      {
         var _loc4_:DisplayObject = null;
         if(param1 == null)
         {
            return;
         }
         while(param1.numChildren > 0)
         {
            _loc4_ = param1.getChildAt(0);
            if(_loc4_ == null)
            {
               break;
            }
            param1.removeChildAt(0);
            if(param2 && _loc4_)
            {
               if(_loc4_.hasOwnProperty("clear"))
               {
                  _loc4_["clear"]();
               }
               if(_loc4_ is BitmapData)
               {
                  BitmapData(_loc4_).dispose();
               }
            }
            if(param3)
            {
               if(_loc4_ is DisplayObjectContainer)
               {
                  clearDisplayObjectInContainer(_loc4_ as DisplayObjectContainer);
               }
            }
         }
      }
      
      public static function nullArr(param1:*, param2:Boolean = true, param3:Boolean = true, param4:Boolean = true) : void
      {
         var _loc7_:int = 0;
         var _loc6_:String = null;
         if(param1 == null)
         {
            return;
         }
         var _loc5_:int = int(param1 ? param1.length : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            if(param1[_loc7_])
            {
               if(param2)
               {
                  if(param1[_loc7_].hasOwnProperty("clear"))
                  {
                     param1[_loc7_]["clear"]();
                  }
                  if(param1[_loc7_] is BitmapData)
                  {
                     BitmapData(param1[_loc7_]).dispose();
                  }
                  if(param1[_loc7_] is DisplayObject)
                  {
                     if((param1[_loc7_] as DisplayObject).parent)
                     {
                        (param1[_loc7_] as DisplayObject).parent.removeChild(param1[_loc7_] as DisplayObject);
                     }
                  }
                  if(param1[_loc7_] is Loader)
                  {
                     param1[_loc7_].close();
                     param1[_loc7_].unloadAndStop();
                  }
               }
               if(param3)
               {
                  _loc6_ = getQualifiedClassName(param1[_loc7_]);
                  if(_loc6_ == "Array" || _loc6_.substr(0,20) == "__AS3__.vec::Vector.")
                  {
                     nullArr(param1[_loc7_],param2);
                  }
                  else if(_loc6_ == "Object" || _loc6_ == "flash.utils::Dictionary")
                  {
                     nullObject(param1[_loc7_],param2);
                  }
               }
               if(param4)
               {
                  if(param1[_loc7_] is DisplayObjectContainer)
                  {
                     clearDisplayObjectInContainer(param1[_loc7_],param2,param4);
                  }
               }
            }
            param1[_loc7_] = null;
            _loc7_++;
         }
      }
      
      public static function nullObject(param1:Object, param2:Boolean = true) : void
      {
         var _loc4_:String = null;
         if(param1 == null)
         {
            return;
         }
         for(var _loc3_ in param1)
         {
            if(param1[_loc3_])
            {
               if(param2)
               {
                  if(param1[_loc3_].hasOwnProperty("clear"))
                  {
                     param1[_loc3_]["clear"]();
                  }
                  if(param1[_loc3_] is BitmapData)
                  {
                     BitmapData(param1[_loc3_]).dispose();
                  }
               }
               _loc4_ = getQualifiedClassName(param1[_loc3_]);
               if(_loc4_ == "Array" || _loc4_.substr(0,20) == "__AS3__.vec::Vector.")
               {
                  nullArr(param1[_loc3_],param2);
               }
               else if(_loc4_ == "Object" || _loc4_ == "flash.utils::Dictionary")
               {
                  nullObject(param1[_loc3_],param2);
               }
            }
            param1[_loc3_] = null;
         }
      }
      
      public static function clearLinkList(param1:LinkList, param2:Boolean) : void
      {
         var _loc3_:Node = null;
         if(param1 == null)
         {
            return;
         }
         if(param2)
         {
            _loc3_ = param1.getFirstNode();
            while(_loc3_)
            {
               ClearUtil.clearObject(_loc3_.getItem());
               _loc3_ = _loc3_.getNextNode();
            }
         }
         param1.clear();
         param1 = null;
      }
      
      public static function clearObject(param1:Object) : void
      {
         var _loc2_:String = null;
         if(param1)
         {
            if(param1 is LinkList)
            {
               ClearUtil.clearLinkList(param1 as LinkList,true);
               return;
            }
            if(param1.hasOwnProperty("clear"))
            {
               param1["clear"]();
            }
            if(param1 is Bitmap)
            {
               Bitmap(param1).bitmapData.dispose();
               Bitmap(param1).bitmapData = null;
               return;
            }
            if(param1 is BitmapData)
            {
               BitmapData(param1).dispose();
               return;
            }
            if(param1 is DisplayObject)
            {
               if((param1 as DisplayObject).parent)
               {
                  (param1 as DisplayObject).parent.removeChild(param1 as DisplayObject);
               }
               return;
            }
            if(param1 is DisplayObjectContainer)
            {
               clearDisplayObjectInContainer(param1 as DisplayObjectContainer);
               return;
            }
            if(param1 is Loader)
            {
               param1.close();
               param1.unloadAndStop();
               return;
            }
            _loc2_ = getQualifiedClassName(param1);
            if(_loc2_ == "Array" || _loc2_.substr(0,20) == "__AS3__.vec::Vector.")
            {
               nullArr(param1);
            }
            else if(_loc2_ == "Object" || _loc2_ == "flash.utils::Dictionary")
            {
               nullObject(param1);
            }
         }
      }
   }
}

