package YJFY.Utils
{
   import flash.system.ApplicationDomain;
   import flash.utils.getQualifiedClassName;
   
   public class CloneUtil
   {
      
      public static const CLONE:String = "clone";
      
      public function CloneUtil()
      {
         super();
      }
      
      public static function cloneArr(param1:*, param2:Boolean = true) : *
      {
         var _loc7_:int = 0;
         var _loc6_:* = undefined;
         if(param1 == null)
         {
            return null;
         }
         var _loc4_:int = int(param1 ? param1.length : 0);
         var _loc5_:String = getQualifiedClassName(param1);
         var _loc3_:Class = ApplicationDomain.currentDomain.getDefinition(_loc5_) as Class;
         _loc6_ = new _loc3_();
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            if(Boolean(param1[_loc7_]) && param2)
            {
               if(param1[_loc7_].hasOwnProperty("clone"))
               {
                  _loc6_.push(param1[_loc7_]["clone"]());
               }
               else
               {
                  _loc5_ = getQualifiedClassName(param1[_loc7_]);
                  if(_loc5_ == "Array" || _loc5_.substr(0,20) == "__AS3__.vec::Vector.")
                  {
                     _loc6_.push(cloneArr(param1[_loc7_],param2));
                  }
                  else if(_loc5_ == "Object" || _loc5_ == "flash.utils::Dictionary")
                  {
                     _loc6_.push(cloneObj(param1[_loc7_],param2));
                  }
                  else
                  {
                     _loc6_.push(param1[_loc7_]);
                  }
               }
            }
            else
            {
               _loc6_.push(param1[_loc7_]);
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      public static function cloneObj(param1:Object, param2:Boolean = true) : Object
      {
         var _loc5_:String = null;
         if(param1 == null)
         {
            return null;
         }
         var _loc4_:Object = {};
         for each(var _loc3_ in param1)
         {
            if(Boolean(param1[_loc3_]) && param2)
            {
               if(param1[_loc3_].hasOwnProperty("clone"))
               {
                  _loc4_[_loc3_] = param1[_loc3_]["clone"]();
               }
               else
               {
                  _loc5_ = getQualifiedClassName(param1[_loc3_]);
                  if(_loc5_ == "Array" || _loc5_.substr(0,20) == "__AS3__.vec::Vector.")
                  {
                     _loc4_[_loc3_] = cloneArr(param1[_loc3_],param2);
                  }
                  else if(_loc5_ == "Object" || _loc5_ == "flash.utils::Dictionary")
                  {
                     _loc4_[_loc3_] = cloneObj(param1[_loc3_],param2);
                  }
                  else
                  {
                     _loc4_[_loc3_] = param1[_loc3_];
                  }
               }
            }
            else
            {
               _loc4_[_loc3_] = param1[_loc3_];
            }
         }
         return _loc4_;
      }
   }
}

