package UI
{
   import YJFY.Utils.ClearUtil;
   
   public class UnAbleBuyEquipmentData
   {
      
      private static var _instance:UnAbleBuyEquipmentData;
      
      private var _unAbleBuyEquipmentIds:Vector.<String>;
      
      public function UnAbleBuyEquipmentData()
      {
         super();
         if(_instance == null)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : UnAbleBuyEquipmentData
      {
         if(_instance == null)
         {
            _instance = new UnAbleBuyEquipmentData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_unAbleBuyEquipmentIds);
         _unAbleBuyEquipmentIds = null;
         _instance = null;
      }
      
      public function addId(param1:String) : void
      {
         if(_unAbleBuyEquipmentIds == null)
         {
            _unAbleBuyEquipmentIds = new Vector.<String>();
         }
         _unAbleBuyEquipmentIds.push(param1);
      }
      
      public function isHaveId(param1:String) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = _unAbleBuyEquipmentIds ? _unAbleBuyEquipmentIds.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_unAbleBuyEquipmentIds[_loc3_] == param1)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function getUnAbleBuyEquipmentIdStrs() : String
      {
         return MyFunction.getInstance().combineStringsToArr(_unAbleBuyEquipmentIds);
      }
      
      public function getIdsFromStr(param1:String) : void
      {
         _unAbleBuyEquipmentIds = MyFunction.getInstance().excreteStringToString(param1);
      }
   }
}

