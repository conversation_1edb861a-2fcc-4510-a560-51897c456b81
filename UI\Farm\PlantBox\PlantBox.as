package UI.Farm.PlantBox
{
   import UI.Button.PageBtn.PageBtnGroup_Vertical;
   import UI.Button.QuitBtn3;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.InitUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class PlantBox extends MySprite
   {
      
      public var quitBtn:QuitBtn3;
      
      private const ONE_PAGE_NUM:int = 4;
      
      private var _equipmentVOs:Vector.<EquipmentVO>;
      
      private var _pageBtn:PageBtnGroup_Vertical;
      
      private const X:Number = 275;
      
      private const Y:Number = 270;
      
      private var _eqXML:XML;
      
      private var _farmXML:XML;
      
      private var buyPlantBoxXML:XML;
      
      public function PlantBox()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         var _loc3_:int = 0;
         if(_equipmentVOs)
         {
            _loc1_ = int(_equipmentVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_equipmentVOs[_loc3_])
               {
                  _equipmentVOs[_loc3_].clear();
               }
               _equipmentVOs[_loc3_] = null;
               _loc3_++;
            }
         }
         _equipmentVOs = null;
         if(_pageBtn)
         {
            _pageBtn.clear();
         }
         _pageBtn = null;
         _eqXML = null;
         _farmXML = null;
         buyPlantBoxXML = null;
      }
      
      public function refreshBox() : void
      {
         setPage(1);
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      private function init() : void
      {
         _eqXML = XMLSingle.getInstance().equipmentXML;
         _farmXML = XMLSingle.getInstance().farmXML;
         MyFunction2.loadXMLFunction("BuyPlantBox",onComplete,null);
      }
      
      private function onComplete(param1:XML) : void
      {
         _equipmentVOs = InitUI.getInstance().getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,"");
         quitBtn = new QuitBtn3();
         quitBtn.x = 662;
         quitBtn.y = 208;
         addChild(quitBtn);
         _pageBtn = new PageBtnGroup_Vertical();
         _pageBtn.changeBtnCoord(47);
         _pageBtn.x = 661;
         _pageBtn.y = 286;
         addChild(_pageBtn);
         refreshBox();
      }
      
      private function setPage(param1:int) : void
      {
         var _loc2_:int = 0;
         if(_equipmentVOs)
         {
            _loc2_ = int(_equipmentVOs.length);
         }
         if(!Boolean(_equipmentVOs) || !_loc2_)
         {
            _pageBtn.initPageNumber(1,1);
            return;
         }
         if(_loc2_ % 4 == 0)
         {
            _pageBtn.initPageNumber(param1,_loc2_ / 4);
         }
         else
         {
            _pageBtn.initPageNumber(param1,int(_loc2_ / 4) + 1);
         }
      }
      
      private function arrangeGoods(param1:int) : void
      {
         var _loc5_:DisplayObject = null;
         var _loc4_:PlantBoxSlot = null;
         var _loc3_:UnOpenPlantBoxSlot = null;
         var _loc7_:* = 0;
         _loc7_ = 0;
         while(_loc7_ < numChildren)
         {
            _loc5_ = getChildAt(_loc7_);
            if(_loc5_ is PlantBoxSlot || _loc5_ is UnOpenPlantBoxSlot)
            {
               removeChildAt(_loc7_);
               _loc5_["clear"]();
               _loc7_--;
            }
            _loc7_++;
         }
         var _loc2_:int = int(_equipmentVOs.length);
         var _loc6_:int = param1 + 4;
         _loc7_ = param1;
         while(_loc7_ < _loc6_ && _loc7_ < _loc2_)
         {
            if(_equipmentVOs[_loc7_])
            {
               _loc4_ = new PlantBoxSlot();
               _loc4_.init(_equipmentVOs[_loc7_],_eqXML,_farmXML);
               _loc4_.x = 275 + 96 * (_loc7_ - param1);
               _loc4_.y = 270;
               addChild(_loc4_);
            }
            else
            {
               _loc3_ = new UnOpenPlantBoxSlot();
               _loc3_.x = 275 + 96 * (_loc7_ - param1);
               _loc3_.y = 270;
               addChild(_loc3_);
            }
            _loc7_++;
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function pageUp(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
      
      private function pageDown(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 4);
      }
   }
}

