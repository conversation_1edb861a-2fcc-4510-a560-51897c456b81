package YJFY.Utils.MovieClipToBitmapDatas
{
   import YJFY.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   
   public class MovieClipToBitmapDatas
   {
      
      private var m_isAnalysising:Boolean;
      
      private var m_analysisMovieClipTotalFrames:int;
      
      private var m_currentAnalysisFrame:int;
      
      private var m_analysisMovieClipTotalNum:int;
      
      private var m_sprite:MySprite;
      
      private var m_maxWidthDraw:int;
      
      private var m_maxHeightDraw:int;
      
      private var m_isNotTranslate:Boolean;
      
      private var m_widthDraw:Number;
      
      private var m_heightDraw:Number;
      
      private var m_dataOfBitmapDatas:DataOfBitmapDatas;
      
      private var m_analysisMovieClips:Vector.<MovieClip>;
      
      private var m_completeFun:Function;
      
      public function MovieClipToBitmapDatas()
      {
         super();
         m_sprite = new MySprite();
      }
      
      public function clear() : void
      {
         ClearUtil.clearDisplayObjectInContainer(m_sprite,false,false);
         m_sprite = null;
         m_dataOfBitmapDatas = null;
         m_analysisMovieClips = null;
         m_completeFun = null;
      }
      
      public function setMaxWidthDraw(param1:int) : void
      {
         m_maxWidthDraw = param1;
      }
      
      public function setMaxHeightDraw(param1:int) : void
      {
         m_maxHeightDraw = param1;
      }
      
      public function transform(param1:MovieClip) : DataOfBitmapDatas
      {
         var _loc7_:int = 0;
         var _loc2_:Rectangle = null;
         var _loc3_:BitmapData = null;
         var _loc5_:DataOfBitmapData = null;
         var _loc6_:DataOfBitmapDatas = new DataOfBitmapDatas();
         var _loc4_:int = param1.totalFrames;
         _loc7_ = 1;
         while(_loc7_ <= _loc4_)
         {
            param1.gotoAndStop(_loc7_);
            _loc2_ = param1.getBounds(param1);
            _loc3_ = new BitmapData(_loc2_.width,_loc2_.height);
            _loc3_.draw(param1);
            _loc5_ = new DataOfBitmapData(_loc3_,_loc2_.x,_loc2_.y,param1.currentFrameLabel);
            _loc6_.addDataOfBitmapData(_loc5_);
            _loc7_++;
         }
         return _loc6_;
      }
      
      public function render() : void
      {
         if(m_isAnalysising)
         {
            analysis();
         }
      }
      
      private function analysis() : void
      {
         var _loc1_:Rectangle = null;
         var _loc2_:BitmapData = null;
         var _loc4_:DataOfBitmapData = null;
         var _loc5_:Matrix = null;
         var _loc3_:String = null;
         var _loc6_:int = 0;
         if(m_isAnalysising == false)
         {
            return;
         }
         if(m_currentAnalysisFrame > m_analysisMovieClipTotalFrames)
         {
            m_isAnalysising = false;
            m_completeFun();
            return;
         }
         _loc3_ = null;
         _loc6_ = 0;
         while(_loc6_ < m_analysisMovieClipTotalNum)
         {
            if(m_currentAnalysisFrame == 1)
            {
               movieClipGotoAndStop(m_analysisMovieClips[_loc6_],1);
            }
            else
            {
               MovieClipAllNextFrame(m_analysisMovieClips[_loc6_]);
            }
            if(Boolean(_loc3_) == false && m_analysisMovieClips[_loc6_].currentFrameLabel)
            {
               _loc3_ = m_analysisMovieClips[_loc6_].currentFrameLabel;
            }
            _loc6_++;
         }
         _loc1_ = m_sprite.getBounds(m_sprite);
         _loc5_ = new Matrix();
         if(m_isNotTranslate == false)
         {
            _loc1_.x = Math.round(_loc1_.x);
            _loc1_.y = Math.round(_loc1_.y);
            _loc5_.translate(-_loc1_.x,-_loc1_.y);
         }
         else
         {
            _loc1_ = new Rectangle(0,0,Math.max(0,_loc1_.right),Math.max(0,_loc1_.bottom));
         }
         if(m_maxWidthDraw)
         {
            m_widthDraw = Math.round(Math.min(m_maxWidthDraw,Math.max(1,_loc1_.width)));
         }
         else
         {
            m_widthDraw = Math.round(Math.max(1,_loc1_.width));
         }
         if(m_maxHeightDraw)
         {
            m_heightDraw = Math.round(Math.min(m_maxHeightDraw,Math.max(1,_loc1_.height)));
         }
         else
         {
            m_heightDraw = Math.round(Math.max(1,_loc1_.height));
         }
         _loc2_ = new BitmapData(m_widthDraw,m_heightDraw,true,0);
         _loc2_.draw(m_sprite,_loc5_);
         _loc4_ = new DataOfBitmapData(_loc2_,_loc1_.x,_loc1_.y,_loc3_);
         m_dataOfBitmapDatas.addDataOfBitmapData(_loc4_);
         ++m_currentAnalysisFrame;
      }
      
      public function transform2(param1:Vector.<MovieClip>, param2:DataOfBitmapDatas, param3:Function, param4:Boolean = false) : void
      {
         var _loc5_:int = 0;
         if(m_dataOfBitmapDatas)
         {
            m_dataOfBitmapDatas.clear();
         }
         param2.clearData();
         m_completeFun = param3;
         m_analysisMovieClips = param1;
         m_dataOfBitmapDatas = param2;
         m_analysisMovieClipTotalNum = param1.length;
         m_isNotTranslate = param4;
         ClearUtil.clearObject(m_sprite);
         _loc5_ = 0;
         while(_loc5_ < m_analysisMovieClipTotalNum)
         {
            m_sprite.addChild(param1[_loc5_]);
            m_analysisMovieClipTotalFrames = Math.max(m_analysisMovieClipTotalFrames,param1[_loc5_].totalFrames);
            _loc5_++;
         }
         m_isAnalysising = true;
         m_currentAnalysisFrame = 1;
      }
      
      public function getIsAnalysising() : Boolean
      {
         return m_isAnalysising;
      }
      
      private function movieClipGotoAndStop(param1:MovieClip, param2:int) : void
      {
         var _loc5_:int = 0;
         var _loc4_:DisplayObject = null;
         param1.gotoAndStop(param2);
         var _loc3_:int = param1.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = param1.getChildAt(_loc5_);
            if(_loc4_ is MovieClip)
            {
               movieClipGotoAndStop(_loc4_ as MovieClip,param2);
            }
            _loc5_++;
         }
      }
      
      private function MovieClipAllNextFrame(param1:MovieClip) : void
      {
         var _loc4_:int = 0;
         var _loc3_:DisplayObject = null;
         if(param1.currentFrame == param1.totalFrames)
         {
            param1.gotoAndStop(1);
         }
         else
         {
            param1.nextFrame();
         }
         var _loc2_:int = param1.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_ = param1.getChildAt(_loc4_);
            if(_loc3_ is MovieClip)
            {
               MovieClipAllNextFrame(_loc3_ as MovieClip);
            }
            _loc4_++;
         }
      }
   }
}

