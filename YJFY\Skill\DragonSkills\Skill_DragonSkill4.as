package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_OneSkillShow2;
   import YJFY.Skill.ActiveSkill.IChoiceLocationSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.MoveObj;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_DragonSkill4 extends CuboidAreaAttackSkill2_OneSkillShow2 implements IChoiceLocationSkill
   {
      
      private const m_niaoFlyEndFrameLabel:String = "flyEnd^stop^";
      
      private const m_areaShowStartPointXDistanceToOwner:Number = 100;
      
      private const m_areaShowStartPointYDistanceToOwner:Number = 0;
      
      private var m_areaShowId:String;
      
      private var m_niaoFlyShowDefId:String;
      
      private var m_niaoFlyShowDefinitionData:AnimationDefinitionData;
      
      private var m_niaoFlyInterval:uint = 500;
      
      private var m_niaoFlyLastTime:Number;
      
      private var m_usedNiaoFlyShowEntitys:Vector.<AnimationEntityLogicShell>;
      
      private var m_wasteNiaoFlyShowEntitys:Vector.<AnimationEntityLogicShell>;
      
      private var m_niaoFlyShowPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_niaoFlyShowEntitysPool:ObjectsPool;
      
      private var m_areaShowPlay:AnimationShowPlayLogicShell;
      
      private var m_moveObj:MoveObj;
      
      public function Skill_DragonSkill4()
      {
         super();
         m_usedNiaoFlyShowEntitys = new Vector.<AnimationEntityLogicShell>();
         m_wasteNiaoFlyShowEntitys = new Vector.<AnimationEntityLogicShell>();
         m_niaoFlyShowPlayListener = new AnimationPlayFrameLabelListener();
         m_niaoFlyShowPlayListener.reachFrameLabelFun2 = niaoFlyReachFrameLabel;
         m_niaoFlyShowEntitysPool = new ObjectsPool(m_usedNiaoFlyShowEntitys,m_wasteNiaoFlyShowEntitys,createOneFlyNiaoFlyShowEntity,null);
         m_areaShowPlay = new AnimationShowPlayLogicShell();
         m_moveObj = new MoveObj();
         m_moveObj.setMoveSpeed(500);
      }
      
      override public function clear() : void
      {
         m_areaShowId = null;
         m_niaoFlyShowDefId = null;
         ClearUtil.clearObject(m_niaoFlyShowDefinitionData);
         m_niaoFlyShowDefinitionData = null;
         clearNiaoFlyShowEntitys();
         ClearUtil.clearObject(m_niaoFlyShowPlayListener);
         m_niaoFlyShowPlayListener = null;
         ClearUtil.clearObject(m_niaoFlyShowEntitysPool);
         m_niaoFlyShowEntitysPool = null;
         ClearUtil.clearObject(m_areaShowPlay);
         m_areaShowPlay = null;
         ClearUtil.clearObject(m_moveObj);
         m_moveObj = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_areaShowId = String(param1.@areaShowId);
         m_niaoFlyInterval = uint(param1.@niaoFlyInterval);
         m_niaoFlyShowDefId = String(param1.@niaoFlyShowDefId);
         m_niaoFlyShowDefinitionData = new AnimationDefinitionData();
         m_niaoFlyShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_niaoFlyShowDefId)[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         m_owner.setMoveSpeed(0);
         setAttackPoint_in(m_owner.getX(),m_owner.getY(),m_owner.getZ());
         m_areaShowPlay.setShow(m_owner.getAnimationByDefId(m_areaShowId),true);
         m_moveObj.startMove();
         (m_areaShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         (m_areaShowPlay.getShow() as DisplayObject).x = m_attackPointToScreen.getX();
         (m_areaShowPlay.getShow() as DisplayObject).y = m_attackPointToScreen.getY();
         m_areaShowPlay.gotoAndPlay("1");
         m_world.addAnimationInGround(m_areaShowPlay);
         return true;
      }
      
      override public function render(param1:World) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         super.render(param1);
         if(m_isRun == false)
         {
            return;
         }
         playOneNiaoFlyShow();
         if(m_moveObj.getIsMove())
         {
            _loc3_ = Math.min(param1.getCamera().getMaxX(),Math.max(param1.getCamera().getMinX(),m_attackPoint.getX() + m_moveObj.getMoveSpeedX() * param1.getAddTimeOneFrame() / 1000));
            _loc2_ = Math.min(param1.getWorldArea().getMaxY(),Math.max(param1.getWorldArea().getMinY(),m_attackPoint.getY() + m_moveObj.getMoveSpeedY() * param1.getAddTimeOneFrame() / 1000));
            setAttackPoint_in(_loc3_,_loc2_,m_attackPoint.getZ());
            (m_areaShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
            (m_areaShowPlay.getShow() as DisplayObject).x = m_attackPointToScreen.getX();
            (m_areaShowPlay.getShow() as DisplayObject).y = m_attackPointToScreen.getY();
         }
      }
      
      override public function releaseSkill(param1:World) : void
      {
         super.releaseSkill(param1);
         releaseSkill2(param1);
      }
      
      public function setMoveSpeed(param1:Number) : void
      {
         m_moveObj.setMoveSpeed(param1);
      }
      
      public function setDirection(param1:Number, param2:Number) : void
      {
         m_moveObj.setDirection(param1,param2);
      }
      
      public function startMove() : void
      {
         m_moveObj.startMove();
      }
      
      public function stopMove() : void
      {
         m_moveObj.stopMove();
      }
      
      public function setLocation(param1:Number, param2:Number) : void
      {
         setAttackPoint_in(param1,param2,m_attackPoint.getZ());
      }
      
      override protected function endSkill2() : void
      {
         m_niaoFlyLastTime = NaN;
         super.endSkill2();
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_moveObj.stopMove();
         m_world.removeAnimationInGround(m_areaShowPlay);
         super.releaseSkill2(param1);
      }
      
      override protected function skillAttackReach() : void
      {
         super.skillAttackReach();
      }
      
      private function playOneNiaoFlyShow() : void
      {
         var _loc3_:AnimationEntityLogicShell = null;
         var _loc2_:Number = NaN;
         var _loc1_:Number = NaN;
         if(!isNaN(m_niaoFlyLastTime) && m_niaoFlyLastTime + m_niaoFlyInterval < m_world.getWorldTime())
         {
            _loc3_ = getWasteOrCreateOneNiaoFlyShowEntity();
            _loc2_ = m_cuboidRangeToWorld.getMaxX() - Math.random() * m_cuboidRange.getXRange();
            _loc1_ = m_cuboidRangeToWorld.getMaxY() - Math.random() * m_cuboidRange.getYRange();
            _loc3_.setNewPosition(_loc2_,_loc1_,0);
            _loc3_.getAniamtionShowPlay().gotoAndPlay("1");
            _loc3_.getShow().scaleX = m_owner.getShowDirection();
            m_world.addEntity(_loc3_);
            m_niaoFlyLastTime = m_world.getWorldTime();
         }
      }
      
      private function niaoFlyReachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         if(m_isRun == false)
         {
            return;
         }
         var _loc5_:* = param2;
         if("flyEnd^stop^" === _loc5_)
         {
            _loc3_ = int(m_usedNiaoFlyShowEntitys.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_usedNiaoFlyShowEntitys[_loc4_].getAniamtionShowPlay() == param1)
               {
                  m_world.removeEntity(m_usedNiaoFlyShowEntitys[_loc4_]);
                  wasteOneNiaoFlyShowEntity(m_usedNiaoFlyShowEntitys[_loc4_]);
                  _loc4_--;
                  _loc3_--;
               }
               _loc4_++;
            }
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_releaseFrameLabel2 === _loc2_)
         {
            m_niaoFlyLastTime = 0;
            playOneNiaoFlyShow();
         }
      }
      
      private function getWasteOrCreateOneNiaoFlyShowEntity() : AnimationEntityLogicShell
      {
         return m_niaoFlyShowEntitysPool.getOneOrCreateOneObj() as AnimationEntityLogicShell;
      }
      
      private function createOneFlyNiaoFlyShowEntity() : AnimationEntityLogicShell
      {
         var _loc1_:AnimationEntityLogicShell = null;
         var _loc2_:AnimationShow = new AnimationShow();
         _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_niaoFlyShowDefinitionData));
         _loc1_ = new AnimationEntityLogicShell();
         _loc1_.setShow(_loc2_);
         _loc1_.init(0,0,0);
         _loc1_.getAniamtionShowPlay().addFrameLabelListener(m_niaoFlyShowPlayListener);
         return _loc1_;
      }
      
      private function wasteOneNiaoFlyShowEntity(param1:AnimationEntityLogicShell) : void
      {
         m_niaoFlyShowEntitysPool.wasteOneObj(param1);
      }
      
      private function clearNiaoFlyShowEntitys() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_usedNiaoFlyShowEntitys ? m_usedNiaoFlyShowEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_usedNiaoFlyShowEntitys[_loc2_])
            {
               if(m_world)
               {
                  m_world.removeEntity(m_usedNiaoFlyShowEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_usedNiaoFlyShowEntitys[_loc2_].getAnimationShow());
               ClearUtil.clearObject(m_usedNiaoFlyShowEntitys[_loc2_]);
               m_usedNiaoFlyShowEntitys[_loc2_] = null;
            }
            _loc2_++;
         }
         m_usedNiaoFlyShowEntitys.length = 0;
         m_usedNiaoFlyShowEntitys = null;
         _loc1_ = m_wasteNiaoFlyShowEntitys ? m_wasteNiaoFlyShowEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_wasteNiaoFlyShowEntitys[_loc2_])
            {
               ClearUtil.clearObject(m_wasteNiaoFlyShowEntitys[_loc2_].getAnimationShow());
               ClearUtil.clearObject(m_wasteNiaoFlyShowEntitys[_loc2_]);
               m_wasteNiaoFlyShowEntitys[_loc2_] = null;
            }
            _loc2_++;
         }
         m_wasteNiaoFlyShowEntitys.length = 0;
         m_wasteNiaoFlyShowEntitys = null;
      }
   }
}

