package YJFY.XydzjsLogic
{
   import UI.Players.Player;
   import UI2.ProgramStartData.ProgramStartData;
   
   public class XydzjsMainLineLogic
   {
      
      public function XydzjsMainLineLogic()
      {
         super();
      }
      
      public function getIsAbleToCityMap2(param1:Player, param2:Player) : <PERSON><PERSON><PERSON>
      {
         var _loc3_:uint = uint(param1.playerVO.level);
         if(param2)
         {
            _loc3_ = Math.max(_loc3_,param2.playerVO.level);
         }
         if(_loc3_ < 30)
         {
            return false;
         }
         return true;
      }
      
      public function getIsAbleToExchangeEquipment(param1:Player, param2:Player) : Bo<PERSON>an
      {
         var _loc3_:uint = uint(param1.playerVO.level);
         if(param2)
         {
            _loc3_ = Math.max(_loc3_,param2.playerVO.level);
         }
         if(_loc3_ < ProgramStartData.getInstance().getSixty())
         {
            return false;
         }
         return true;
      }
   }
}

