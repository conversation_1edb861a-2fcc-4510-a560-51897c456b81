package UI.ActivityPanel
{
   import YJFY.Utils.ClearUtil;
   
   public class ActivityOneData
   {
      
      private var m_exchangeOneDatas:Vector.<ExchangeOneData>;
      
      public function ActivityOneData()
      {
         super();
         m_exchangeOneDatas = new Vector.<ExchangeOneData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_exchangeOneDatas);
         m_exchangeOneDatas = null;
      }
      
      public function initByXML(param1:XML, param2:String) : void
      {
         var _loc6_:int = 0;
         var _loc3_:ExchangeOneData = null;
         var _loc5_:XMLList = param1.exchange;
         var _loc4_:int = int(_loc5_ ? _loc5_.length() : 0);
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_ = new ExchangeOneData();
            _loc3_.initByXML(_loc5_[_loc6_],param2);
            m_exchangeOneDatas.push(_loc3_);
            _loc6_++;
         }
      }
      
      public function getExchangeOneDataNum() : int
      {
         return m_exchangeOneDatas.length;
      }
      
      public function getExchangeOneDataByIndex(param1:int) : ExchangeOneData
      {
         return m_exchangeOneDatas[param1];
      }
   }
}

