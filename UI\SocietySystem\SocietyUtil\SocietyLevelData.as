package UI.SocietySystem.SocietyUtil
{
   public class SocietyLevelData
   {
      
      public var societyLevel:int;
      
      public var totalConValueOfNextLevel:int;
      
      public function SocietyLevelData()
      {
         super();
      }
      
      public function init(param1:int, param2:int, param3:XML) : void
      {
         this.societyLevel = param2;
         var _loc4_:XML = param3.SocietyLevelData[0].levelData.(@level == param2)[0];
         var _loc5_:* = param3.SocietyLevelData[0].levelData.(@level == param2 + 1)[0];
         if(_loc5_ == null)
         {
            _loc5_ = _loc4_;
         }
         totalConValueOfNextLevel = int(_loc5_.@totalConValue);
      }
   }
}

