package UI.ExchangeEquipment
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.PointTicketBuyBox;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class BuyExeNumBox extends PointTicketBuyBox
   {
      
      public var _exchangeEquipmentXML:XML;
      
      public var ticketText:TextField;
      
      private var _cell:IEquipmentCell;
      
      private var _exChangeWord:ExchangeWorld;
      
      public function BuyExeNumBox(param1:XML, param2:IEquipmentCell, param3:ExchangeWorld)
      {
         _exchangeEquipmentXML = param1;
         _cell = param2;
         _exChangeWord = param3;
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         _exchangeEquipmentXML = null;
         ticketText = null;
         _cell = null;
         if(_exChangeWord)
         {
            _exChangeWord.clockTarget = false;
         }
         _exChangeWord = null;
      }
      
      override protected function init() : void
      {
         ticketText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.embedFonts = true;
         var _loc1_:Vector.<int> = MyFunction.getInstance().excreteString(_exchangeEquipmentXML.@ticketPrice);
         ticketText.text = String(_loc1_[Math.min(ExEPlayerData.getInstance().exeNum,_loc1_.length - 1)]);
         super.init();
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var prices:Vector.<int>;
         var ticketIds:Vector.<String>;
         var price:int;
         var ticketId:String;
         var cell:IEquipmentCell;
         var dataObj:Object;
         var e:UIBtnEvent = param1;
         var myParent:ExchangeWorld = parent as ExchangeWorld;
         if(e.target == _sureBtn)
         {
            prices = MyFunction.getInstance().excreteString(_exchangeEquipmentXML.@ticketPrice);
            ticketIds = MyFunction.getInstance().excreteStringToString(_exchangeEquipmentXML.@ticketId);
            price = prices[Math.min(ExEPlayerData.getInstance().exeNum,prices.length - 1)];
            ticketId = ticketIds[Math.min(ExEPlayerData.getInstance().exeNum,ticketIds.length - 1)];
            cell = _cell;
            dataObj = {};
            dataObj["propId"] = ticketId;
            dataObj["count"] = 1;
            dataObj["price"] = price;
            dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
            dataObj["tag"] = "购买传送！";
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
            {
               if(param1["propId"] != ticketId)
               {
                  myParent.showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               ExEPlayerData.getInstance().haveExeNum++;
               ExEPlayerData.getInstance().exeNum++;
               myParent.sendEquipment(null,cell);
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
               GamingUI.getInstance().mouseChildren = false;
               GamingUI.getInstance().mouseEnabled = false;
               GamingUI.getInstance().manBan.text.text = "传送中， 请勿关闭游戏！";
            },null,WarningBoxSingle.getInstance().getTextFontSize());
         }
         super.clickBtn(e);
      }
   }
}

