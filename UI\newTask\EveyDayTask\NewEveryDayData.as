package UI.newTask.EveyDayTask
{
   import UI.DataManagerParent;
   import UI.MyFunction;
   import UI.Task.TaskVO.MTaskVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class NewEveryDayData extends DataManagerParent
   {
      
      public static var _instance:NewEveryDayData = null;
      
      private var m_isShowTipFrame:Boolean = true;
      
      public var everyDayTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var everyAddNums:Vector.<NewEveryDataItem> = new Vector.<NewEveryDataItem>();
      
      private var saveXML:XML;
      
      private var m_time:String;
      
      public function NewEveryDayData()
      {
         super();
      }
      
      public static function getInstance() : NewEveryDayData
      {
         if(!_instance)
         {
            _instance = new NewEveryDayData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(everyDayTaskVOs)
         {
            _loc1_ = int(everyDayTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               everyDayTaskVOs[_loc2_].clear();
               everyDayTaskVOs[_loc2_] = null;
               _loc2_++;
            }
         }
         everyDayTaskVOs = null;
         ClearUtil.clearObject(everyAddNums);
         everyAddNums = null;
         super.clear();
      }
      
      public function setShowTipFrame(param1:Boolean) : void
      {
         m_isShowTipFrame = param1;
      }
      
      public function getShowTipFrame() : Boolean
      {
         return m_isShowTipFrame;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         saveXML = param1;
         if(everyDayTaskVOs)
         {
            ClearUtil.clearObject(everyDayTaskVOs);
            everyDayTaskVOs = null;
            everyDayTaskVOs = new Vector.<MTaskVO>();
         }
         if(everyAddNums)
         {
            ClearUtil.clearObject(everyAddNums);
            everyAddNums = null;
            everyAddNums = new Vector.<NewEveryDataItem>();
         }
         m_time = null;
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc8_:int = 0;
         var _loc3_:XML = null;
         var _loc6_:int = 0;
         var _loc5_:XML = null;
         var _loc1_:XML = null;
         var _loc7_:int = 0;
         var _loc2_:XML = null;
         var _loc4_:XML = <NewEveryTask></NewEveryTask>;
         _loc8_ = 0;
         while(_loc8_ < everyDayTaskVOs.length)
         {
            _loc3_ = <task></task>;
            _loc3_.@id = everyDayTaskVOs[_loc8_].id;
            _loc3_.@isGotReward = everyDayTaskVOs[_loc8_].isGotReward;
            _loc6_ = 0;
            while(_loc6_ < everyDayTaskVOs[_loc8_].taskGoalVO_ids.length)
            {
               _loc5_ = <goaltask></goaltask>;
               _loc5_.@id = everyDayTaskVOs[_loc8_].taskGoalVO_ids[_loc6_];
               _loc5_.@num = everyDayTaskVOs[_loc8_].currentTaskGoalVO_nums[_loc6_];
               _loc3_.appendChild(_loc5_);
               _loc6_++;
            }
            _loc4_.appendChild(_loc3_);
            _loc8_++;
         }
         if(everyAddNums.length > 0)
         {
            _loc1_ = <addevery></addevery>;
            _loc4_.appendChild(_loc1_);
            _loc7_ = 0;
            while(_loc7_ < everyAddNums.length)
            {
               _loc2_ = <additem></additem>;
               _loc2_.@id = everyAddNums[_loc7_].id;
               _loc2_.@num = everyAddNums[_loc7_].num;
               _loc1_.appendChild(_loc2_);
               _loc7_++;
            }
         }
         if(m_time == null)
         {
            _loc4_.@time = TimeUtil.getTimeUtil().getTimeStr();
         }
         else
         {
            _loc4_.@time = m_time;
         }
         return _loc4_;
      }
      
      public function refreshData() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(everyDayTaskVOs && everyDayTaskVOs.length > 0)
         {
            _loc3_ = 0;
            while(_loc3_ < everyDayTaskVOs.length)
            {
               _loc2_ = 0;
               while(_loc2_ < everyDayTaskVOs[_loc3_].taskGoalVO_ids.length)
               {
                  _loc1_ = 0;
                  while(_loc1_ < everyAddNums.length)
                  {
                     if(everyAddNums[_loc1_].id == everyDayTaskVOs[_loc3_].taskGoalVO_ids[_loc2_])
                     {
                        var _loc4_:* = _loc2_;
                        var _loc5_:* = everyDayTaskVOs[_loc3_].currentTaskGoalVO_nums[_loc4_] + everyAddNums[_loc1_].num;
                        everyDayTaskVOs[_loc3_].currentTaskGoalVO_nums[_loc4_] = _loc5_;
                     }
                     _loc1_++;
                  }
                  _loc2_++;
               }
               _loc3_++;
            }
            everyAddNums.length = 0;
         }
      }
      
      public function initXML(param1:XML, param2:String) : void
      {
         var _loc11_:MTaskVO = null;
         var _loc12_:int = 0;
         var _loc16_:XML = null;
         var _loc3_:XMLList = null;
         var _loc10_:XMLList = null;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc13_:int = 0;
         var _loc15_:int = 0;
         var _loc14_:XML = null;
         var _loc7_:XMLList = null;
         var _loc18_:int = 0;
         var _loc6_:NewEveryDataItem = null;
         m_time = param2;
         if(everyDayTaskVOs.length > 0)
         {
            refreshData();
            return;
         }
         everyDayTaskVOs.length = 0;
         var _loc5_:XML = XMLSingle.getInstance().taskXML;
         var _loc17_:XMLList = param1.item;
         _loc12_ = 0;
         while(_loc12_ < _loc17_.length())
         {
            if(MyFunction.getInstance().getTaskTypeFromID(_loc17_[_loc12_].@id) == 0)
            {
               _loc11_ = XMLSingle.getTask(int(_loc17_[_loc12_].@id),_loc5_);
               everyDayTaskVOs.push(_loc11_);
            }
            _loc12_++;
         }
         var _loc4_:Boolean = false;
         if(saveXML.hasOwnProperty("NewEveryTask"))
         {
            if(TimeUtil.getTimeUtil().newDateIsNewDay(String(saveXML.NewEveryTask[0].@time),m_time))
            {
               _loc4_ = true;
            }
         }
         else
         {
            _loc4_ = true;
         }
         if(_loc4_)
         {
            everyAddNums.length = 0;
         }
         if(saveXML.hasOwnProperty("NewEveryTask") && _loc4_ == false)
         {
            _loc10_ = saveXML.NewEveryTask[0].task;
            _loc8_ = 0;
            while(_loc8_ < _loc10_.length())
            {
               _loc16_ = _loc10_[_loc8_];
               _loc9_ = 0;
               while(_loc9_ < everyDayTaskVOs.length)
               {
                  if(everyDayTaskVOs[_loc9_].id == int(_loc16_.@id))
                  {
                     everyDayTaskVOs[_loc9_].isGotReward = int(_loc16_.@isGotReward);
                     _loc3_ = _loc16_.goaltask;
                     _loc13_ = 0;
                     while(_loc13_ < _loc3_.length())
                     {
                        _loc15_ = 0;
                        while(_loc15_ < everyDayTaskVOs[_loc9_].taskGoalVO_ids.length)
                        {
                           if(int(_loc3_[_loc13_].@id) == everyDayTaskVOs[_loc9_].taskGoalVO_ids[_loc15_])
                           {
                              everyDayTaskVOs[_loc9_].currentTaskGoalVO_nums[_loc15_] = int(_loc3_[_loc13_].@num);
                           }
                           _loc15_++;
                        }
                        _loc13_++;
                     }
                  }
                  _loc9_++;
               }
               _loc8_++;
            }
            _loc14_ = saveXML.NewEveryTask[0];
            if(_loc14_.hasOwnProperty("addevery"))
            {
               _loc7_ = _loc14_.addevery[0].additem;
               _loc18_ = 0;
               while(_loc18_ < _loc7_.length())
               {
                  _loc6_ = new NewEveryDataItem();
                  _loc6_.id = int(_loc7_[_loc18_].@id);
                  _loc6_.num = int(_loc7_[_loc18_].@num);
                  everyAddNums.push(_loc6_);
                  _loc18_++;
               }
            }
         }
         refreshData();
      }
   }
}

