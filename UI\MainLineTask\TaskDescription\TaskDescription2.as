package UI.MainLineTask.TaskDescription
{
   public class TaskDescription2 extends TaskDescriptionVO_MainLineTask
   {
      
      public var npcName:String;
      
      public var mapName:String;
      
      public var npcFunctionDescription:String;
      
      public function TaskDescription2()
      {
         super();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         npcName = String(param1.npcName[0].@value);
         mapName = String(param1.mapName[0].@value);
         npcFunctionDescription = String(param1.npcFunctionDescription[0].@value);
      }
   }
}

