package YJFY.Skill.HouyiSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_HouyiSkill2 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private var m_beatBackForceVec:P3DVector3D;
      
      public function Skill_HouyiSkill2()
      {
         super();
         m_beatBackForceVec = new P3DVector3D();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_beatBackForceVec);
         m_beatBackForceVec = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc2_:int = 0;
         var _loc3_:Boolean = false;
         _loc2_ = 0;
         while(_loc2_ < m_world.getAllEntityNum())
         {
            if(m_world.getAllEnttiyByIndex(_loc2_).getId() == "Houyi")
            {
               if(m_world.getAllEnttiyByIndex(_loc2_).getExtra() as PlayerXydzjs && (m_world.getAllEnttiyByIndex(_loc2_).getExtra() as PlayerXydzjs).isFoe(param1))
               {
                  _loc3_ = true;
               }
            }
            _loc2_++;
         }
         if(param1.getId() != "Houyi" && _loc3_)
         {
            super.attackSuccess(param1);
            m_beatBackForceVec.setTo(500000 * m_owner.getShowDirection(),0,2000000);
            param1.getBody().applyImpulse(m_beatBackForceVec);
         }
      }
   }
}

