package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.IEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import flash.geom.Point;
   
   public class BinCiAnimationEntityLogicShell extends AnimationEntityLogicShell
   {
      
      private var m_dataOfBinCiHeightPoints:DataOfPoints;
      
      public function BinCiAnimationEntityLogicShell()
      {
         super();
      }
      
      override protected function fullyClear() : void
      {
         m_dataOfBinCiHeightPoints = null;
         super.fullyClear();
      }
      
      public function setDataOfBinCiHeightPoint(param1:DataOfPoints) : void
      {
         m_dataOfBinCiHeightPoints = param1;
      }
      
      override public function render(param1:World) : void
      {
         var _loc2_:Point = null;
         var _loc4_:Coordinate = null;
         var _loc3_:IEntity = null;
         super.render(param1);
         var _loc5_:int = m_dataOfBinCiHeightPoints ? m_dataOfBinCiHeightPoints.getPointNum() : 0;
         if(m_animationShowPlay.extra)
         {
            if(m_animationShow.currentFrame - 1 < _loc5_)
            {
               _loc2_ = m_dataOfBinCiHeightPoints.getPointByIndex(m_animationShow.currentFrame - 1);
               _loc3_ = m_animationShowPlay.extra as IEntity;
               _loc4_ = new Coordinate(_loc2_.x,0,-_loc2_.y);
               if(_loc3_.getZ() < _loc4_.getZ())
               {
                  _loc3_.setNewPosition(_loc3_.getX(),_loc3_.getY(),_loc4_.getZ());
                  _loc3_.getBody().setVelocity(new P3DVector3D(0,0,0));
               }
            }
         }
      }
   }
}

