package deng.fzip
{
   import flash.events.Event;
   
   public class FZipEvent extends Event
   {
      
      public static const FILE_LOADED:String = "fileLoaded";
      
      public var file:FZipFile;
      
      public function FZipEvent(param1:String, param2:FZipFile = null, param3:<PERSON>olean = false, param4:Boolean = false)
      {
         this.file = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new FZipEvent(type,file,bubbles,cancelable);
      }
   }
}

