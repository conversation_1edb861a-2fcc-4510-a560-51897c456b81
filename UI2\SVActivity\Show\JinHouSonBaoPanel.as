package UI2.SVActivity.Show
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.BuffData;
   import UI.CheatData.CheatData;
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.JinHouSonLiData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.TextEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   import flash.utils.setTimeout;
   
   public class JinHouSonBaoPanel
   {
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_show:MovieClip;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      private var m_jinhousonliData:JinHouSonLiData;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:IVersionControl;
      
      private var m_time:TextField;
      
      private var m_freeTimes:TextField;
      
      private var m_openOneBtn:ButtonLogicShell2;
      
      private var m_openTenBtn:ButtonLogicShell2;
      
      private var _isstar:Boolean;
      
      private var currentXml:XML;
      
      private var _Equipments:Vector.<EquipmentVO>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var useFreeUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/giftbox/open";
      
      private var _isFree:Boolean = false;
      
      private var _buffId:int;
      
      private var _isCheat:Boolean = false;
      
      public function JinHouSonBaoPanel()
      {
         super();
         m_openOneBtn = new ButtonLogicShell2();
         m_openTenBtn = new ButtonLogicShell2();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         _isstar = false;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         if(m_show["perSHowTxt"])
         {
            (m_show["perSHowTxt"] as TextField).removeEventListener("link",textHandler);
         }
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_time = null;
         m_freeTimes = null;
         m_jinhousonliData = null;
         ClearUtil.clearObject(m_openOneBtn);
         m_openOneBtn = null;
         ClearUtil.clearObject(m_openTenBtn);
         m_openTenBtn = null;
         ClearUtil.clearObject(currentXml);
         currentXml = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(_Equipments);
         _Equipments = null;
         if(_buffId)
         {
            GamingUI.getInstance().refresh(16384);
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         referText();
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:JinHouSonLiData, param4:SVActivityPanel, param5:IVersionControl) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_jinhousonliData = param3;
         m_openOneBtn.setShow(m_show["openOneBtn"]);
         m_openOneBtn.setTipString("点击购买并使用一个命中祝福");
         m_openTenBtn.setShow(m_show["openTenBtn"]);
         m_openTenBtn.setTipString("点击购买并使用十个命中祝福");
         m_svActivitySaveData = param2;
         m_svActivityPanel = param4;
         m_versionControl = param5;
         m_time = m_show["timeText"];
         m_freeTimes = m_show["freeTimes"];
         (m_show["perSHowTxt"] as TextField).htmlText = "<font><a href=\'event:percent\'><u>概率公示</u></a> </font>";
         (m_show["perSHowTxt"] as TextField).addEventListener("link",textHandler);
         if(m_myLoader)
         {
            throw new Error("出错了");
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/SVActivity/jinhou.xml",getXMLSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         currentXml = param1.resultXML;
         _isstar = true;
      }
      
      private function textHandler(param1:TextEvent) : void
      {
         var _loc2_:* = param1.text;
         if("percent" === _loc2_)
         {
            navigateToURL(new URLRequest("http://my.4399.com/forums/thread-59073578"),"_blank");
         }
      }
      
      private function processXmlForDate(param1:XML) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc4_:int = 0;
         _loc4_ = currentXml.item.length() - 1;
         while(_loc4_ >= 0)
         {
            if(String(currentXml.item[_loc4_].@startDate))
            {
               _loc3_ = new TimeUtil().timeInterval(currentXml.item[_loc4_].@startDate,TimeUtil.timeStr);
               _loc2_ = new TimeUtil().timeInterval(TimeUtil.timeStr,currentXml.item[_loc4_].@endDate);
               if(_loc3_ < 0 || _loc2_ < 0)
               {
                  delete currentXml.item[_loc4_];
               }
            }
            _loc4_--;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         trace("get xml fall");
         m_svActivityPanel.closeCurrPanel();
      }
      
      private function referText() : void
      {
         var _loc3_:String = null;
         var _loc5_:String = null;
         var _loc6_:String = null;
         m_svActivitySaveData.runCheckOnlineTime();
         var _loc2_:int = m_svActivitySaveData.getHounianCountDownTime();
         var _loc7_:int = _loc2_ / 3600;
         var _loc1_:int = _loc2_ % 3600 / 60;
         var _loc4_:int = _loc2_ % 60;
         if(_loc7_ > 9)
         {
            _loc3_ = String(_loc7_);
         }
         else
         {
            _loc3_ = "0" + _loc7_;
         }
         if(_loc1_ > 9)
         {
            _loc5_ = String(_loc1_);
         }
         else
         {
            _loc5_ = "0" + _loc1_;
         }
         if(_loc4_ > 9)
         {
            _loc6_ = String(_loc4_);
         }
         else
         {
            _loc6_ = "0" + _loc4_;
         }
         m_time.text = _loc3_ + ":" + _loc5_ + ":" + _loc6_;
         m_freeTimes.text = String(m_svActivitySaveData.getHounianFreeTimes());
         if(!_isCheat && m_svActivitySaveData.getHounianFreeTimes() > m_jinhousonliData.getTimesMax())
         {
            CheatData.getInstance().addCheatDataStr("金猴送宝次数异常");
            CheatData.getInstance().addCheatDataStr("金猴送宝次数异常+1");
            CheatData.getInstance().addCheatDataStr("金猴送宝次数异常+2");
            _isCheat = true;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:String = null;
         var _loc3_:String = null;
         var _loc7_:String = null;
         var _loc6_:String = null;
         var _loc5_:String = null;
         var _loc2_:String = null;
         if(_isstar == false)
         {
            return;
         }
         switch(param1.button)
         {
            case m_openOneBtn:
               _loc4_ = GamingUI.getInstance().getNewestTimeStrFromSever();
               _loc3_ = m_jinhousonliData.getStartTime();
               _loc7_ = m_jinhousonliData.getEndTime();
               if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_loc4_) > 0) && (!Boolean(_loc7_) || new TimeUtil().timeInterval(_loc4_,_loc7_) > 0))
               {
                  checkOpenOne();
               }
               else
               {
                  m_svActivityPanel.showWarningBox("不在活动时间内!",0);
               }
               break;
            case m_openTenBtn:
               _loc6_ = GamingUI.getInstance().getNewestTimeStrFromSever();
               _loc5_ = m_jinhousonliData.getStartTime();
               _loc2_ = m_jinhousonliData.getEndTime();
               if((!Boolean(_loc5_) || new TimeUtil().timeInterval(_loc5_,_loc6_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc6_,_loc2_) > 0))
               {
                  checkOpenTen();
               }
               else
               {
                  m_svActivityPanel.showWarningBox("不在活动时间内!",0);
               }
         }
      }
      
      private function checkOpenOne() : void
      {
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
         {
            m_svActivityPanel.showWarningBox("背包空间不足,请先清理背包",0);
         }
         else if(m_svActivitySaveData.getHounianFreeTimes() > 0)
         {
            _isFree = true;
            seedToServer1();
         }
         else
         {
            _isFree = false;
            m_svActivityPanel.showWarningBox("是否花费" + m_jinhousonliData.getTicketPriceOne() + "点券购买并使用一个命中祝福",1 | 2,{
               "type":"buyjinhousonli1",
               "okFunction":seedToServer1
            });
         }
      }
      
      private function seedToServer1() : void
      {
         var _loc8_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:EquipmentVO = null;
         _isstar = false;
         var _loc6_:int = int(currentXml.item.length());
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         var _loc5_:Array = [];
         _loc8_ = 0;
         while(_loc8_ < 1)
         {
            _loc1_ = Math.random() * _loc6_;
            _loc3_ = XMLSingle.getEquipmentVOByID(int(currentXml.item[_loc1_].@id),XMLSingle.getInstance().equipmentXML);
            _Equipments.push(_loc3_);
            _loc5_.push(_loc3_.id);
            _loc8_++;
         }
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",seedToServerCompleteHandler);
         _loc2_.addEventListener("ioError",onErrorseedToServer);
         _loc2_.addEventListener("securityError",onErrorseedToServer);
         var _loc4_:URLRequest = new URLRequest(this.useFreeUrl);
         _loc4_.method = "POST";
         _loc4_.contentType = "application/json";
         var _loc7_:Object = {};
         _loc7_.MD5 = "2B01530154A2C991";
         _loc7_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc7_.Items = _loc5_;
         _loc7_.Free = _isFree;
         _loc4_.data = MyJSON.encode(_loc7_);
         _loc2_.load(_loc4_);
      }
      
      private function seedToServer10() : void
      {
         var _loc8_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:EquipmentVO = null;
         _isstar = false;
         var _loc6_:int = int(currentXml.item.length());
         ClearUtil.clearObject(_Equipments);
         _Equipments = new Vector.<EquipmentVO>();
         var _loc5_:Array = [];
         _loc8_ = 0;
         while(_loc8_ < 11)
         {
            _loc1_ = Math.random() * _loc6_;
            _loc3_ = XMLSingle.getEquipmentVOByID(int(currentXml.item[_loc1_].@id),XMLSingle.getInstance().equipmentXML);
            _Equipments.push(_loc3_);
            _loc5_.push(_loc3_.id);
            _loc8_++;
         }
         var _loc2_:URLLoader = new URLLoader();
         _loc2_.addEventListener("complete",seedToServerCompleteHandler);
         _loc2_.addEventListener("ioError",onErrorseedToServer);
         _loc2_.addEventListener("securityError",onErrorseedToServer);
         var _loc4_:URLRequest = new URLRequest(this.useFreeUrl);
         _loc4_.method = "POST";
         _loc4_.contentType = "application/json";
         var _loc7_:Object = {};
         _loc7_.MD5 = "2B01530154A2C991";
         _loc7_.UID = GameData.getInstance().getLoginReturnData().getUid();
         _loc7_.Items = _loc5_;
         _loc7_.Free = _isFree;
         _loc4_.data = MyJSON.encode(_loc7_);
         _loc2_.load(_loc4_);
      }
      
      private function seedToServerCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",seedToServerCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorseedToServer);
         param1.currentTarget.removeEventListener("securityError",onErrorseedToServer);
         var _loc2_:int = int(MyJSON.decode(param1.currentTarget.data).Result);
         if(_loc2_ == 1)
         {
            trace("抽奖成功");
         }
         else
         {
            m_svActivityPanel.showWarningBox("连接错误",0);
         }
         _isstar = true;
         if(_Equipments.length > 1)
         {
            if(_isFree)
            {
               pushGoodsToPackeg10();
            }
            else
            {
               buy10();
            }
         }
         else if(_isFree)
         {
            pushGoodsToPackeg1();
         }
         else
         {
            buy1();
         }
      }
      
      private function onErrorseedToServer(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",seedToServerCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorseedToServer);
         param1.currentTarget.removeEventListener("securityError",onErrorseedToServer);
         m_svActivityPanel.showWarningBox("连接错误",0);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         _isstar = true;
      }
      
      private function buy1() : void
      {
         var price:uint = uint(m_jinhousonliData.getTicketPriceOne());
         var ticketId:String = m_jinhousonliData.getTicketPriceIdOne();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买一次命中祝福";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            pushGoodsToPackeg1();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function addBuff(param1:String) : void
      {
         var _loc2_:AllTimeBuffVO = XMLSingle.getBuff(_buffId,XMLSingle.getInstance().buffXML) as AllTimeBuffVO;
         _loc2_.startDate = param1;
         _loc2_.totalTime = _loc2_.xml.@time;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc2_),BuffData.getInstance().buffDrives);
      }
      
      private function checkOpenTen() : void
      {
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) >= 11)
         {
            if(m_svActivitySaveData.getHounianFreeTimes() >= 10)
            {
               _isFree = true;
               seedToServer10();
            }
            else
            {
               _isFree = false;
               m_svActivityPanel.showWarningBox("是否花费" + m_jinhousonliData.getTicketPriceTen() + "点券购买并使用十个命中祝福",1 | 2,{
                  "type":"buyjinhousonli1",
                  "okFunction":seedToServer10
               });
            }
         }
         else
         {
            m_svActivityPanel.showWarningBox("背包空间不足11格,请先清理背包",0);
         }
      }
      
      private function buy10() : void
      {
         var price:uint = uint(m_jinhousonliData.getTicketPriceTen());
         var ticketId:String = m_jinhousonliData.getTicketPriceIdTen();
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买十次命中祝福";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            pushGoodsToPackeg10();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function pushGoodsToPackeg1() : void
      {
         if(_isFree)
         {
            m_svActivitySaveData.setHounianFreeTimes(m_svActivitySaveData.getHounianFreeTimes() - 1);
         }
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            m_svActivityPanel.showGoodSTip(_Equipments,shareLunTan);
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            _buffId = 13011;
            MyFunction2.getServerTimeFunction(addBuff,null,false);
            setTimeout(MyFunction2.saveGame2,1000);
         },null);
      }
      
      private function pushGoodsToPackeg10() : void
      {
         if(_isFree)
         {
            m_svActivitySaveData.setHounianFreeTimes(m_svActivitySaveData.getHounianFreeTimes() - 10);
         }
         MyFunction2.trueAddEquipmentVOs(_Equipments,GamingUI.getInstance().player1,function():void
         {
            m_svActivityPanel.showGoodSTip(_Equipments,shareLunTan);
            var _loc1_:SaveTaskInfo = new SaveTaskInfo();
            _loc1_.type = "4399";
            _loc1_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc1_);
            _buffId = 13012;
            MyFunction2.getServerTimeFunction(addBuff,null,false);
            setTimeout(MyFunction2.saveGame2,1000);
         },null);
      }
      
      private function shareLunTan() : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/forums/thread-send-tagid-81260"),"_blank");
         trace("点击分享论坛");
      }
   }
}

