package UI2.broadcast
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import UI2.SVActivity.Data.TotalRechargeData;
   import YJFY.API_4399.API_4399;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SubmitReturnData;
   import YJFY.API_4399.RankListAPI.SubmitSuccessReturnData;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.events.Event;
   
   public class SubmitFunction
   {
      
      private static var _instance:SubmitFunction;
      
      private var CWFHID:int = 1828;
      
      private var CWCJHID:int = 1829;
      
      private var RWDJID:int = 1830;
      
      private var YJHCID:int = 1831;
      
      private var YJSJID:int = 1832;
      
      private var ZQZHID:int = 1833;
      
      private var ZQSJID:int = 1834;
      
      private var LFHCID:int = 1835;
      
      private var LFSJID:int = 1836;
      
      private var ZBHCID:int = 1837;
      
      private var ZBSJID:int = 1838;
      
      private var VIPSJID:int = 1839;
      
      private var BWDHID:int = 1840;
      
      private var SJBOSSID:int = 1841;
      
      private var m_payAPI:PayAPI;
      
      private var m_totalRechargeData:TotalRechargeData;
      
      private var m_payAPIListener:PayAPIListener;
      
      private var m_score:uint;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_4399API:API_4399;
      
      public function SubmitFunction()
      {
         super();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.submitScoreInfoReturnFun = submitScoreInfoReturnFunWhenNotHave;
         m_rankListAPIListener.submitScoreInfoErrorFun = submitScoreInfoErrorFunWhenNotHave;
         GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         GameEvent.eventDispacher.addEventListener("rechargeCallBack",CallBackHandler);
         m_totalRechargeData = new TotalRechargeData();
         m_payAPIListener = new PayAPIListener();
         m_payAPIListener.getRechargedMoneyErrorFun = getRechargedMoneyError;
         m_payAPIListener.getRechargedMoneySuccessFun = getRechargedMoneySuccess;
         m_payAPI = GamingUI.getInstance().getAPI4399().payAPI;
      }
      
      public static function getInstance() : SubmitFunction
      {
         if(_instance == null)
         {
            _instance = new SubmitFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         ClearUtil.clearObject(m_payAPIListener);
         m_payAPIListener = null;
         ClearUtil.clearObject(m_totalRechargeData);
         m_totalRechargeData = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         GameEvent.eventDispacher.removeEventListener("rechargeCallBack",CallBackHandler);
      }
      
      public function set4399API(param1:API_4399) : void
      {
         m_4399API = param1;
      }
      
      private function CallBackHandler(param1:Event) : void
      {
         getTotalRechargeForSomeTime();
      }
      
      private function getTotalRechargeForSomeTime() : void
      {
         if(m_payAPI == null || m_payAPIListener == null || m_payAPI.getVersionControl() == null)
         {
            return;
         }
         m_payAPI.addPayAPIListener(m_payAPIListener);
         m_payAPI.getTotalRechargeFun();
      }
      
      private function getRechargedMoneySuccess(param1:int) : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_totalRechargeData.setTotalRecharge(param1);
         var _loc2_:int = XMLSingle.getVipVO(param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().privilegeXML,TimeUtil.timeStr).vipLevel;
         if(_loc2_ > BroadDataManager.getInstance().nowVip)
         {
            SubmitFunction.getInstance().setData6(12,_loc2_);
         }
      }
      
      private function getRechargedMoneyError() : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
      }
      
      public function submitData(param1:int, param2:int, param3:int, param4:Object) : void
      {
         var _loc5_:Array = [];
         var _loc6_:Object = {};
         _loc6_.rId = param2;
         _loc6_.score = param3;
         _loc6_.extra = param4;
         _loc5_.push(_loc6_);
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine")
         {
            BroadDataManager.getInstance().refreshNum();
            if(BroadDataManager.getInstance().upNum >= BroadDataManager.getInstance().maxNum)
            {
               return;
            }
            BroadDataManager.getInstance().upNum++;
            m_4399API.getServiceHold().submitScoreToRankLists(GameData.getInstance().getSaveFileData().index,_loc5_);
         }
      }
      
      private function submitScoreInfoReturnFunWhenNotHave(param1:Vector.<SubmitSuccessReturnData>) : void
      {
         ClearUtil.clearObject(param1);
         param1 = null;
         GamingUI.getInstance().showMessageTip("成功提交数据");
         trace("成功提交数据");
      }
      
      private function submitScoreInfoErrorFunWhenNotHave(param1:SubmitReturnData, param2:String) : void
      {
         ClearUtil.clearObject(param1);
         param1 = null;
         GamingUI.getInstance().showMessageTip("数据提交失败");
         trace("提交数据到排行榜失败, errorMessage:",param2);
      }
      
      public function setData1(param1:int, param2:String, param3:String) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var id:String = param2;
         var name:String = param3;
         if(checkId(type,id))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + name;
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&1";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      public function setData2(param1:int, param2:int) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var level:int = param2;
         if(checkLevel(type,level))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + String(level);
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&2";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      public function setData3(param1:int, param2:String, param3:String, param4:int) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var id:String = param2;
         var name:String = param3;
         var level:int = param4;
         if(checkLevel(type,level) && checkId(type,id))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + name + "|" + String(level);
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&3";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      public function setData4(param1:int, param2:uint) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var hp:uint = param2;
         if(checkHp(type,hp))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + String(hp);
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&4";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      public function setData5(param1:int, param2:int, param3:String) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var level:int = param2;
         var role:String = param3;
         if(checkLevel(type,level))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + role + "|" + String(level);
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&5";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      public function setData6(param1:int, param2:int) : void
      {
         var date:Date;
         var strinfo:String;
         var subStr:String;
         var txtStr:String;
         var array:Array;
         var type:int = param1;
         var level:int = param2;
         if(checkLevel(type,level))
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               date = TimeUtil.getTimeUtil().stringToDate(param1);
               m_score = date.getDate() * 24 * 60 + date.getHours() * 60 + date.getMinutes();
               strinfo = getTxtInfo(type);
               if(strinfo)
               {
                  array = strinfo.split("|");
                  txtStr = GameData.getInstance().getLoginReturnData().getNickname() + "|" + String(level);
                  subStr = String(type) + "&" + strinfo + "&" + GameData.getInstance().getLoginReturnData().getNickname() + "&" + GameData.getInstance().getLoginReturnData().getUid() + "&" + param1 + "&" + txtStr + "&6";
               }
               submitData(type,getIdByType(type),m_score,subStr);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      private function getIdByType(param1:int) : int
      {
         if(param1 == 1)
         {
            return CWFHID;
         }
         if(param1 == 2)
         {
            return CWCJHID;
         }
         if(param1 == 3)
         {
            return RWDJID;
         }
         if(param1 == 4)
         {
            return YJHCID;
         }
         if(param1 == 5)
         {
            return YJSJID;
         }
         if(param1 == 6)
         {
            return ZQZHID;
         }
         if(param1 == 7)
         {
            return ZQSJID;
         }
         if(param1 == 8)
         {
            return LFHCID;
         }
         if(param1 == 9)
         {
            return LFSJID;
         }
         if(param1 == 10)
         {
            return ZBHCID;
         }
         if(param1 == 11)
         {
            return ZBSJID;
         }
         if(param1 == 12)
         {
            return VIPSJID;
         }
         if(param1 == 13)
         {
            return BWDHID;
         }
         if(param1 == 14)
         {
            return SJBOSSID;
         }
         return -1;
      }
      
      private function getTxtInfo(param1:int) : String
      {
         var _loc5_:int = 0;
         var _loc4_:* = null;
         var _loc3_:int = 0;
         var _loc2_:XMLList = XMLSingle.getInstance().broadcast.tipinfo;
         _loc5_ = 0;
         while(_loc5_ < _loc2_.length())
         {
            if(param1 == int(_loc2_[_loc5_].@type))
            {
               return String(_loc2_[_loc5_].@value);
            }
            _loc5_++;
         }
         return null;
      }
      
      private function checkId(param1:int, param2:String) : Boolean
      {
         var _loc6_:int = 0;
         var _loc5_:Array = null;
         var _loc4_:int = 0;
         var _loc3_:XMLList = XMLSingle.getInstance().broadcast.tipinfo;
         _loc6_ = 0;
         while(_loc6_ < _loc3_.length())
         {
            if(param1 == int(_loc3_[_loc6_].@type))
            {
               _loc5_ = String(_loc3_[_loc6_].@ids).split(",");
               _loc4_ = 0;
               while(_loc4_ < _loc5_.length)
               {
                  if(String(_loc5_[_loc4_]) == param2)
                  {
                     return true;
                  }
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         return false;
      }
      
      private function checkLevel(param1:int, param2:int) : Boolean
      {
         var _loc6_:int = 0;
         var _loc5_:Array = null;
         var _loc4_:int = 0;
         var _loc3_:XMLList = XMLSingle.getInstance().broadcast.tipinfo;
         _loc6_ = 0;
         while(_loc6_ < _loc3_.length())
         {
            if(param1 == int(_loc3_[_loc6_].@type))
            {
               _loc5_ = String(_loc3_[_loc6_].@level).split(",");
               _loc4_ = 0;
               while(_loc4_ < _loc5_.length)
               {
                  if(int(_loc5_[_loc4_]) == param2)
                  {
                     return true;
                  }
                  _loc4_++;
               }
            }
            _loc6_++;
         }
         return false;
      }
      
      private function checkHp(param1:int, param2:uint) : Boolean
      {
         var _loc4_:int = 0;
         var _loc3_:XMLList = XMLSingle.getInstance().broadcast.tipinfo;
         _loc4_ = 0;
         while(_loc4_ < _loc3_.length())
         {
            if(param1 == int(_loc3_[_loc4_].@type))
            {
               if(param2 >= uint(_loc3_[_loc4_].@hp))
               {
                  return true;
               }
            }
            _loc4_++;
         }
         return false;
      }
   }
}

