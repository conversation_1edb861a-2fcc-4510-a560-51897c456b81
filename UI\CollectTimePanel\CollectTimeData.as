package UI.CollectTimePanel
{
   import UI.DataManagerParent;
   
   public class CollectTimeData extends DataManagerParent
   {
      
      private var m_cofficient:uint;
      
      private var m_ticket:int;
      
      private var m_ticketId:String;
      
      private var m_multiOfTicket:Number;
      
      private var m_minTime:uint;
      
      private var m_maxTime:uint;
      
      public function CollectTimeData(param1:uint, param2:int, param3:String, param4:Number, param5:uint, param6:uint)
      {
         super();
         this.cofficient = param1;
         this.ticket = param2;
         this.ticketId = param3;
         this.minTime = param5;
         this.maxTime = param6;
         this.multiOfTicket = param4;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function getCofficient() : uint
      {
         return cofficient;
      }
      
      public function getTicket() : int
      {
         return ticket;
      }
      
      public function getTicketId() : String
      {
         return ticketId;
      }
      
      public function getMultiOfTicket() : Number
      {
         return multiOfTicket;
      }
      
      public function getMinTime() : uint
      {
         return minTime;
      }
      
      public function getMaxTime() : uint
      {
         return maxTime;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.cofficient = m_cofficient;
         _antiwear.ticket = m_ticket;
         _antiwear.ticketId = m_ticketId;
         _antiwear.minTime = m_minTime;
         _antiwear.maxTime = m_maxTime;
         _antiwear.multiOfTicket = m_multiOfTicket;
      }
      
      private function get cofficient() : uint
      {
         return _antiwear.cofficient;
      }
      
      private function set cofficient(param1:uint) : void
      {
         _antiwear.cofficient = param1;
      }
      
      private function get ticket() : int
      {
         return _antiwear.ticket;
      }
      
      private function set ticket(param1:int) : void
      {
         _antiwear.ticket = param1;
      }
      
      private function get ticketId() : String
      {
         return _antiwear.ticketId;
      }
      
      private function set ticketId(param1:String) : void
      {
         _antiwear.ticketId = param1;
      }
      
      private function get minTime() : uint
      {
         return _antiwear.minTime;
      }
      
      private function set minTime(param1:uint) : void
      {
         _antiwear.minTime = param1;
      }
      
      private function get maxTime() : uint
      {
         return _antiwear.maxTime;
      }
      
      private function set maxTime(param1:uint) : void
      {
         _antiwear.maxTime = param1;
      }
      
      private function get multiOfTicket() : Number
      {
         return _antiwear.multiOfTicket;
      }
      
      private function set multiOfTicket(param1:Number) : void
      {
         _antiwear.multiOfTicket = param1;
      }
   }
}

