package YJFY.Skill.PetSkills
{
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.SkillVO;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.EnemyEntity.EnemyEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.SkillLogic.EffectShowAddToTargetInHurt;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   import flash.utils.getTimer;
   
   public class Skill_Pet23Skill extends Skill_Pet16Skill
   {
      
      private const m_const_effectAttackLabel:String = "skillattack";
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      protected var m_effectShowAddToTargetInHurt:EffectShowAddToTargetInHurt;
      
      private var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      public function Skill_Pet23Skill()
      {
         super();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowAddToTargetInHurt);
         m_effectShowAddToTargetInHurt = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         var _loc3_:String = null;
         var _loc2_:String = null;
         super.initByXML(param1);
         if(param1.hasOwnProperty("shakeView"))
         {
            _loc3_ = String(param1.shakeView[0].@swfPath);
            _loc2_ = String(param1.shakeView[0].@className);
            m_shakeViewDataOfPoints = new DataOfPoints();
            m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
            m_myLoader.load();
         }
         m_effectAddtoTargetDefinitionData = new AnimationDefinitionData();
         m_effectAddtoTargetDefinitionData.initByXML(param1.animationDefinition.(@id == "debuff")[0]);
         m_effectShowAddToTargetInHurt = new EffectShowAddToTargetInHurt();
         m_effectShowAddToTargetInHurt.setEffectAddToTargetDefinitionData(m_effectAddtoTargetDefinitionData);
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         m_owner.setMoveSpeed(0);
         releaseSkill2(param1);
         if(m_effectShowAddToTargetInHurt)
         {
            m_effectShowAddToTargetInHurt.setWorld(param1);
         }
         return true;
      }
      
      override public function relearseFun(param1:World) : void
      {
         m_owner.changeAnimationShow("petSkillBodyShow");
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      override public function reachFun(param1:String) : void
      {
         switch(param1)
         {
            case "down":
               playEffect();
               break;
            case "end^stop^":
               endSkill1();
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc4_:SkillVO = null;
         var _loc2_:AttackData = null;
         var _loc3_:int = 0;
         if(param1 is AnimalEntity && Boolean(m_attackData) && !(param1 as AnimalEntity).isInDie() && param1.getExtra() && param1.getExtra() is EnemyXydzjs)
         {
            if(m_owner && m_owner.getExtra() is PetXydzjs && (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO())
            {
               _loc4_ = (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO().activeSkillVO;
               if(!_loc4_)
               {
                  return;
               }
               _loc2_ = (param1 as EnemyEntity).getAttackData();
               if(m_effectShowAddToTargetInHurt)
               {
                  m_effectShowAddToTargetInHurt.addEffectShowAddToTarget(param1);
               }
               _loc2_.setDecDefence(0,0);
               _loc3_ = 100 - (_loc4_ as PetActiveSkillVO).diePrecentBase - (_loc4_ as PetActiveSkillVO).passiveYiShuJinTong;
               if(_loc3_ < 1)
               {
                  _loc3_ = 1;
               }
               _loc2_.setDecDefence((_loc4_ as PetActiveSkillVO).buffTime,_loc3_);
               _loc2_.setDecProTime(getTimer());
               _loc2_.setDecSpeedDuration(0);
               _loc2_.setDecSpeedPencent(0);
            }
            return;
         }
         super.attackSuccess(param1);
      }
      
      private function playEffect() : void
      {
         m_playEffectShowObj.setShow(m_owner.getAnimationByDefId("petSkillEffect"));
         m_playEffectShowObj.getDisplayShow().x = m_world.getCamera().getScreenX();
         m_playEffectShowObj.getDisplayShow().y = m_world.getCamera().getScreenY();
         m_playEffectShowObj.gotoAndPlay("start");
         m_world.addAnimationInFront(m_playEffectShowObj);
      }
      
      override public function effectReachFrameLabel(param1:String) : void
      {
         if(m_owner.isInDie() || m_owner.getWorld() == null)
         {
            return;
         }
         super.effectReachFrameLabel(param1);
         if(param1 == "skillattack")
         {
            attackReach(m_world);
            if(m_shakeViewDataOfPoints && m_shakeViewDataOfPoints.getPoint() && m_shakeViewDataOfPoints.getPoint().length > 0)
            {
               m_world.shakeView(m_shakeViewDataOfPoints);
            }
         }
      }
      
      private function dieAllEnemy() : void
      {
         var _loc8_:PlayerXydzjs = null;
         var _loc6_:PetXydzjs = null;
         var _loc7_:String = null;
         var _loc5_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc4_:AttackData = null;
         var _loc1_:Number = NaN;
         if(m_world == null)
         {
            return;
         }
         var _loc10_:int = 0;
         var _loc9_:int = int(m_world.getAllEntityNum());
         _loc10_ = 0;
         for(; _loc10_ < _loc9_; _loc10_++)
         {
            if(m_world.getAllEnttiyByIndex(_loc10_) != null)
            {
               _loc7_ = m_world.getAllEnttiyByIndex(_loc10_).getId();
               if(_loc7_ != null && (_loc7_.toLowerCase() == "monkey" || _loc7_.toLowerCase() == "dragon" || _loc7_.toLowerCase() == "dog" || _loc7_.toLowerCase() == "change" || _loc7_.toLowerCase() == "fox" || _loc7_.toLowerCase() == "tieshan" || _loc7_.toLowerCase() == "houyi"))
               {
                  if(m_world.getAllEnttiyByIndex(_loc10_).getExtra() == null || m_world.getAllEnttiyByIndex(_loc10_).getExtra() as PlayerXydzjs == null)
                  {
                     continue;
                  }
                  _loc8_ = m_world.getAllEnttiyByIndex(_loc10_).getExtra() as PlayerXydzjs;
               }
               if(_loc7_ && _loc7_ == "pet23" || _loc7_ == "pet24")
               {
                  if(!(m_world.getAllEnttiyByIndex(_loc10_).getExtra() == null || m_world.getAllEnttiyByIndex(_loc10_).getExtra() as PetXydzjs == null))
                  {
                     _loc6_ = m_world.getAllEnttiyByIndex(_loc10_).getExtra() as PetXydzjs;
                  }
               }
            }
         }
         if(_loc8_)
         {
            if(_loc8_.getPet() == null || _loc8_.getPet() as PetXydzjs == null || (_loc8_.getPet() as PetXydzjs).getPetEquipmentVO() == null || (_loc8_.getPet() as PetXydzjs).getPetEquipmentVO().activeSkillVO == null || (_loc8_.getPet() as PetXydzjs).getPetEquipmentVO().activeSkillVO as PetActiveSkillVO == null)
            {
               return;
            }
            _loc5_ = ((_loc8_.getPet() as PetXydzjs).getPetEquipmentVO().activeSkillVO as PetActiveSkillVO).diePrecent * 0.01;
            if(_loc5_ > 1)
            {
               _loc5_ = 1;
            }
            _loc10_ = 0;
            while(_loc10_ < _loc9_)
            {
               if(_loc8_.isFoe(m_world.getAllEnttiyByIndex(_loc10_)) && m_world.getAllEnttiyByIndex(_loc10_).getExtra())
               {
                  if(m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs != null)
                  {
                     _loc3_ = (m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getTotalHp();
                     _loc2_ = (m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getCurrentHp();
                     if(_loc2_ <= _loc3_ * _loc5_)
                     {
                        _loc1_ = 9999999999;
                        _loc4_ = new AttackData(true,_loc1_,false,false,0);
                        if((m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getAnimalEntity() == null)
                        {
                           return;
                        }
                        (m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getAnimalEntity().beAttack(_loc8_.getPet().getAnimalEntity(),_loc4_,this,m_world.getAllEnttiyByIndex(_loc10_));
                     }
                     else if(_loc6_ && _loc6_ as PetXydzjs)
                     {
                        if((_loc6_ as PetXydzjs).getPetEquipmentVO() == null || (_loc6_ as PetXydzjs).getPetEquipmentVO().activeSkillVO == null || (_loc6_ as PetXydzjs).getPetEquipmentVO().activeSkillVO as PetActiveSkillVO == null)
                        {
                           return;
                        }
                        _loc1_ = ((_loc6_ as PetXydzjs).getPetEquipmentVO().activeSkillVO as PetActiveSkillVO).originalHurt;
                        _loc4_ = new AttackData(true,_loc1_,false,false,0);
                        if((m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getAnimalEntity() == null)
                        {
                           return;
                        }
                        (m_world.getAllEnttiyByIndex(_loc10_).getExtra() as EnemyXydzjs).getAnimalEntity().beAttack(_loc8_.getPet().getAnimalEntity(),_loc4_,this,m_world.getAllEnttiyByIndex(_loc10_));
                     }
                  }
               }
               _loc10_++;
            }
         }
      }
   }
}

