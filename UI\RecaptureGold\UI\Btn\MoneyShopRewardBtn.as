package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.GamingUI;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class MoneyShopRewardBtn extends Btn
   {
      
      public function MoneyShopRewardBtn()
      {
         super();
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("gotoMoneyShopReward"));
         GamingUI.getInstance().openRecaptureGoldRewardPanel();
      }
   }
}

