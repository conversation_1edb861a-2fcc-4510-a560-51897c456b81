package YJFY.World.Camera.View
{
   import YJFY.MySprite;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Camera.Camera;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import flash.display.Sprite;
   
   public class View extends MySprite
   {
      
      private var m_world:World;
      
      private var m_camera:Camera;
      
      private var m_show:Sprite;
      
      private var m_isShake:Boolean;
      
      private var m_isFlash:Boolean;
      
      public function View()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
      }
      
      override public function clear() : void
      {
         m_world = null;
         m_camera = null;
         ClearUtil.clearDisplayObjectInContainer(m_show,false,false);
         m_show = null;
         super.clear();
      }
      
      public function setWorld(param1:World) : void
      {
         m_world = param1;
      }
      
      public function setCamera(param1:Camera) : void
      {
         m_camera = param1;
      }
      
      public function init() : void
      {
      }
      
      public function projectScreenCoordToView(param1:Number, param2:Number, param3:Number) : Coordinate
      {
         return new Coordinate((param1 - m_camera.getScreenX()) * 1 * m_camera.getWorldMainRenderLayerScreenZ() / param3,(param2 - m_camera.getScreenY()) * 1 * m_camera.getWorldMainRenderLayerScreenZ() / param3,0);
      }
      
      public function getIsShake() : Boolean
      {
         return m_isShake;
      }
      
      public function getIsFlash() : Boolean
      {
         return m_isFlash;
      }
      
      public function setIsShake(param1:Boolean) : void
      {
         m_isShake = param1;
      }
      
      public function setIsFlash(param1:Boolean) : void
      {
         m_isFlash = param1;
      }
   }
}

