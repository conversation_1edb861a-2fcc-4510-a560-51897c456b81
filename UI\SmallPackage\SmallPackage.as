package UI.SmallPackage
{
   import UI.Button.SmallPackageQuitBtn;
   import UI.EquipmentDetailShow.EquipmentDetailShow;
   import UI.EquipmentDetailShow.EquipmentDetailShowFactory;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.MySprite;
   import UI.PackageAndStorage.StorageArea;
   import UI.Players.Player;
   import UI.SmallPackage.Button.SmallPackageBtn_SwitchToPlayer1;
   import UI.SmallPackage.Button.SmallPackageBtn_SwitchToPlayer2;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class SmallPackage extends MySprite
   {
      
      public static var _instance:SmallPackage = null;
      
      public static const SINGLE_PACKAGE:String = "singlePackage";
      
      public static const DOUBLE_PACKAGE:String = "doublePackage";
      
      public static const SINGLEONE_PACKAGE:String = "singleOnePackage";
      
      public static const SINGLETWO_PACKAGE:String = "singleTwOPackage";
      
      public var player1Btn:SmallPackageBtn_SwitchToPlayer1;
      
      public var player2Btn:SmallPackageBtn_SwitchToPlayer2;
      
      public var quitBtn:SmallPackageQuitBtn;
      
      public var dragShow:Sprite;
      
      public var smallPackage_1:StorageArea;
      
      public var smallPackage_2:StorageArea;
      
      public var currentSmallPackage:StorageArea;
      
      public var currentPotion:String;
      
      public var eqIsShow:IEqIsShowInSmallPackage;
      
      private var _equipmentDetailShow:EquipmentDetailShow;
      
      public function SmallPackage()
      {
         super();
         if(!_instance)
         {
            init();
            addEventListener("addedToStage",addToStage,false,0,true);
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了么？！");
      }
      
      public static function getInstance() : SmallPackage
      {
         if(!_instance)
         {
            _instance = new SmallPackage();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(player1Btn)
         {
            player1Btn.clear();
         }
         if(player2Btn)
         {
            player2Btn.clear();
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         player1Btn = null;
         player2Btn = null;
         quitBtn = null;
         _instance = null;
         if(smallPackage_1)
         {
            smallPackage_1.clear();
         }
         if(smallPackage_2)
         {
            smallPackage_2.clear();
         }
         smallPackage_1 = null;
         smallPackage_2 = null;
         currentSmallPackage = null;
         eqIsShow = null;
      }
      
      public function deleEquipmentVOs(param1:EquipmentVO) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         if(currentSmallPackage == smallPackage_1)
         {
            _loc3_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
         }
         else
         {
            if(currentSmallPackage != smallPackage_2)
            {
               throw new Error();
            }
            _loc3_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs;
         }
         _loc2_ = int(_loc3_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(Boolean(_loc3_[_loc4_]) && _loc3_[_loc4_] == param1)
            {
               _loc3_[_loc4_] = null;
               refreshSmallPackage(currentPotion,eqIsShow);
               GamingUI.getInstance().refresh(2);
               return;
            }
            _loc4_++;
         }
         throw new Error();
      }
      
      public function addEquipmentVOs(param1:EquipmentVO) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:int = 0;
         var _loc5_:int = 0;
         if(currentSmallPackage == smallPackage_1)
         {
            _loc3_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
         }
         else
         {
            if(currentSmallPackage != smallPackage_2)
            {
               throw new Error();
            }
            _loc3_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs;
         }
         var _loc4_:Boolean = MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_loc3_,param1,2);
         refreshSmallPackage(currentPotion,eqIsShow);
         if(!_loc4_)
         {
            throw new Error();
         }
      }
      
      public function refreshSmallPackage(param1:String, param2:IEqIsShowInSmallPackage) : void
      {
         var _loc3_:* = undefined;
         currentPotion = param1;
         if(eqIsShow)
         {
            eqIsShow.clear();
         }
         eqIsShow = param2;
         if(!smallPackage_1)
         {
            smallPackage_1 = new StorageArea(3);
         }
         if(!getChildByName(smallPackage_1.name))
         {
            addChild(smallPackage_1);
         }
         currentSmallPackage = smallPackage_1;
         if(param1 == "doublePackage" && GamingUI.getInstance().player2)
         {
            if(!smallPackage_2)
            {
               smallPackage_2 = new StorageArea(3);
            }
            if(!player1Btn)
            {
               player1Btn = new SmallPackageBtn_SwitchToPlayer1();
            }
            if(!player2Btn)
            {
               player2Btn = new SmallPackageBtn_SwitchToPlayer2();
            }
            player1Btn.x = 3;
            player1Btn.y = -14 - player1Btn.height;
            player2Btn.x = player1Btn.x + player1Btn.width + 3;
            player2Btn.y = player1Btn.y;
            player1Btn.init(false);
            player2Btn.init(true);
            if(!getChildByName(player1Btn.name))
            {
               addChild(player1Btn);
            }
            if(!getChildByName(player2Btn.name))
            {
               addChild(player2Btn);
            }
            smallPackage_1.refreshEquipments(BrushEquipments(eqIsShow,GamingUI.getInstance().player1.playerVO.packageEquipmentVOs));
            smallPackage_2.refreshEquipments(BrushEquipments(eqIsShow,GamingUI.getInstance().player2.playerVO.packageEquipmentVOs));
         }
         else if(param1 == "singleOnePackage")
         {
            if(Boolean(smallPackage_2) && getChildByName(smallPackage_2.name))
            {
               removeChild(smallPackage_2);
            }
            if(smallPackage_2)
            {
               smallPackage_2 = null;
            }
            if(Boolean(player1Btn) && getChildByName(player1Btn.name))
            {
               removeChild(player1Btn);
            }
            if(player1Btn)
            {
               player1Btn = null;
            }
            if(Boolean(player2Btn) && getChildByName(player2Btn.name))
            {
               removeChild(player2Btn);
            }
            if(player2Btn)
            {
               player2Btn = null;
            }
            smallPackage_1.refreshEquipments(BrushEquipments(param2,GamingUI.getInstance().player1.playerVO.packageEquipmentVOs));
         }
         else if(param1 == "singleTwOPackage" && GamingUI.getInstance().player2)
         {
            if(Boolean(smallPackage_2) && getChildByName(smallPackage_2.name))
            {
               removeChild(smallPackage_2);
            }
            if(smallPackage_2)
            {
               smallPackage_2 = null;
            }
            if(Boolean(player1Btn) && getChildByName(player1Btn.name))
            {
               removeChild(player1Btn);
            }
            if(player1Btn)
            {
               player1Btn = null;
            }
            if(Boolean(player2Btn) && getChildByName(player2Btn.name))
            {
               removeChild(player2Btn);
            }
            if(player2Btn)
            {
               player2Btn = null;
            }
            smallPackage_1.refreshEquipments(BrushEquipments(param2,GamingUI.getInstance().player2.playerVO.packageEquipmentVOs));
         }
         else
         {
            if(Boolean(smallPackage_2) && getChildByName(smallPackage_2.name))
            {
               removeChild(smallPackage_2);
            }
            if(smallPackage_2)
            {
               smallPackage_2 = null;
            }
            if(Boolean(player1Btn) && getChildByName(player1Btn.name))
            {
               removeChild(player1Btn);
            }
            if(player1Btn)
            {
               player1Btn = null;
            }
            if(Boolean(player2Btn) && getChildByName(player2Btn.name))
            {
               removeChild(player2Btn);
            }
            if(player2Btn)
            {
               player2Btn = null;
            }
            if(param1 == "doublePackage")
            {
               smallPackage_1.refreshEquipments(BrushEquipments(param2,GamingUI.getInstance().player1.playerVO.packageEquipmentVOs));
            }
            else
            {
               _loc3_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
               if(GamingUI.getInstance().player2)
               {
                  _loc3_ = _loc3_.concat(GamingUI.getInstance().player2.playerVO.packageEquipmentVOs);
               }
               smallPackage_1.refreshEquipments(BrushEquipments(param2,_loc3_));
            }
         }
      }
      
      private function BrushEquipments(param1:IEqIsShowInSmallPackage, param2:Vector.<EquipmentVO>) : Vector.<EquipmentVO>
      {
         var _loc3_:* = null;
         var _loc4_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc5_:int = 0;
         for each(_loc3_ in param2)
         {
            if(_loc3_)
            {
               if(param1 == null)
               {
                  _loc4_.push(_loc3_);
               }
               else if(param1.judeIsShow(_loc3_))
               {
                  _loc4_.push(_loc3_);
               }
            }
         }
         return _loc4_;
      }
      
      private function BrushEquipments2(param1:IEqIsShowInSmallPackage, param2:Vector.<EquipmentVO>) : Vector.<EquipmentVO>
      {
         var _loc3_:* = null;
         var _loc4_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc5_:int = 0;
         for each(_loc3_ in param2)
         {
            if(_loc3_ && _loc3_.equipmentType == "pet" && (_loc3_ as PetEquipmentVO).level >= (_loc3_ as PetEquipmentVO).maxLevel)
            {
               _loc4_.push(_loc3_);
            }
         }
         return _loc4_;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchPlayerInSmallPackage",switchPackage,true,0,true);
         dragShow.addEventListener("mouseDown",onMouseDown,false,0,true);
         addEventListener("mouseUp",onMouseUp,false,0,true);
         addEventListener("clickQuitBtn",quit,true,0,true);
         addEventListener("clickDetailBtn",detailShow,true,0,true);
         addEventListener("enterFrame",render,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchPlayerInSmallPackage",switchPackage,true);
         dragShow.removeEventListener("mouseDown",onMouseDown,false);
         removeEventListener("mouseUp",onMouseUp,false);
         removeEventListener("clickQuitBtn",quit,true);
         removeEventListener("clickDetailBtn",detailShow,true);
         removeEventListener("enterFrame",render,false);
      }
      
      public function render(param1:Event) : void
      {
         if(smallPackage_1)
         {
            smallPackage_1.render();
         }
         if(smallPackage_2)
         {
            smallPackage_2.render();
         }
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         quitBtn.quitBtn.dispatchEvent(new MouseEvent("rollOut"));
         if(parent)
         {
            parent.removeChild(this);
         }
      }
      
      private function switchPackage(param1:UIBtnEvent) : void
      {
         switch(param1.target)
         {
            case player1Btn:
               player2Btn.gotoTwoFrame();
               removeChild(smallPackage_2);
               addChild(smallPackage_1);
               currentSmallPackage = smallPackage_1;
               break;
            case player2Btn:
               player1Btn.gotoTwoFrame();
               removeChild(smallPackage_1);
               addChild(smallPackage_2);
               currentSmallPackage = smallPackage_2;
               break;
            default:
               throw new Error();
         }
      }
      
      private function onMouseDown(param1:MouseEvent) : void
      {
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      private function onMouseUp(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      private function test(param1:Event) : void
      {
         var _loc2_:Point = localToGlobal(new Point(mouseX,mouseY));
         if(_loc2_.x > stage.stageWidth)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            x -= 10;
         }
         if(_loc2_.x < 0)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            x += 10;
         }
         if(_loc2_.y > stage.stageHeight)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            y -= 10;
         }
         if(_loc2_.y < 0)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            y += 10;
         }
      }
      
      private function init() : void
      {
      }
      
      protected function detailShow(param1:UIBtnEvent) : void
      {
         var _loc2_:Point = null;
         var _loc6_:Player = null;
         var _loc3_:EquipmentVO = param1.target.parent.parent.child;
         if(_loc3_ == null)
         {
            return;
         }
         var _loc4_:EquipmentDetailShowFactory = new EquipmentDetailShowFactory();
         var _loc5_:String = String(XMLSingle.getInstance().dataXML.ChoiceBtns[0].DetailBtn[0].show.(@equipmentType == _loc3_.equipmentType)[0].@detailShowClassName);
         ClearUtil.clearObject(_equipmentDetailShow);
         _equipmentDetailShow = _loc4_.createByClassName(_loc5_);
         _loc4_.clear();
         if(parent)
         {
            parent.addChild(_equipmentDetailShow);
         }
         else
         {
            addChild(_equipmentDetailShow);
            if(stage)
            {
               _loc2_ = this.globalToLocal(new Point(0,0));
               _equipmentDetailShow.x = _loc2_.x;
               _equipmentDetailShow.y = _loc2_.y;
            }
         }
         if(currentSmallPackage == smallPackage_2)
         {
            _loc6_ = GamingUI.getInstance().player2;
         }
         else
         {
            _loc6_ = GamingUI.getInstance().player1;
         }
         _equipmentDetailShow.setPlayer(_loc6_);
         _equipmentDetailShow.initDetailShow(_loc3_);
      }
   }
}

