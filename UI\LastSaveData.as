package UI
{
   public class LastSaveData extends DataManagerParent
   {
      
      private var m_uid:String;
      
      private var m_idx:uint;
      
      private var m_version:uint;
      
      public function LastSaveData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.uid = m_uid;
         _antiwear.idx = m_idx;
      }
      
      public function initData(param1:String, param2:uint, param3:uint) : void
      {
         this.uid = param1;
         this.idx = param2;
         this.version = param3;
      }
      
      public function getUid() : String
      {
         return uid;
      }
      
      public function getIdx() : uint
      {
         return idx;
      }
      
      public function getVersion() : uint
      {
         return version;
      }
      
      private function get uid() : String
      {
         return _antiwear.uid;
      }
      
      private function set uid(param1:String) : void
      {
         _antiwear.uid = param1;
      }
      
      private function get idx() : uint
      {
         return _antiwear.idx;
      }
      
      private function set idx(param1:uint) : void
      {
         _antiwear.idx = param1;
      }
      
      private function get version() : uint
      {
         return _antiwear.version;
      }
      
      private function set version(param1:uint) : void
      {
         _antiwear.version = param1;
      }
   }
}

