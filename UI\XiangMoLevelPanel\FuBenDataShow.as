package UI.XiangMoLevelPanel
{
   import UI.GamingUI;
   import UI.LogicShell.MyHaveLockSwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class FuBenDataShow extends MyHaveLockSwitchBtnLogicShell
   {
      
      private var m_remainNumText:TextField;
      
      private var m_lockDescriptionText:TextField;
      
      private var m_pictruecontainerMC:MovieClipPlayLogicShell;
      
      private var m_fuBenData:FuBenData;
      
      private var m_xiangMoLevelSaveOneData:XiangMoLevelSaveOneData;
      
      private var m_remainNum:uint;
      
      public function FuBenDataShow()
      {
         super();
         m_pictruecontainerMC = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         m_remainNumText = null;
         m_lockDescriptionText = null;
         ClearUtil.clearObject(m_pictruecontainerMC);
         m_pictruecontainerMC = null;
         m_fuBenData = null;
         m_xiangMoLevelSaveOneData = null;
         super.clear();
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
         initShow2();
      }
      
      public function setData(param1:FuBenData, param2:XiangMoLevelSaveOneData) : void
      {
         m_fuBenData = param1;
         m_xiangMoLevelSaveOneData = param2;
         m_remainNum = Math.max(0,m_fuBenData.getMaxChNum() - m_xiangMoLevelSaveOneData.getChallengeNum());
         initShow2();
         var _loc3_:uint = uint(GamingUI.getInstance().player1.playerVO.level);
         if(GamingUI.getInstance().player2)
         {
            _loc3_ = Math.max(_loc3_,GamingUI.getInstance().player2.playerVO.level);
         }
         if(_loc3_ < param1.getNeedLevel())
         {
            lock();
         }
         else
         {
            unLock();
         }
      }
      
      public function setEmpty() : void
      {
         m_fuBenData = null;
         m_xiangMoLevelSaveOneData = null;
         m_remainNum = 0;
         m_pictruecontainerMC.gotoAndStop("empty");
         lock();
      }
      
      public function getFuBenData() : FuBenData
      {
         return m_fuBenData;
      }
      
      public function getXiangMoLevelSaveOneData() : XiangMoLevelSaveOneData
      {
         return m_xiangMoLevelSaveOneData;
      }
      
      public function getRemainNum() : uint
      {
         return Math.max(0,m_fuBenData.getMaxChNum() - m_xiangMoLevelSaveOneData.getChallengeNum());
      }
      
      private function initShow() : void
      {
         m_pictruecontainerMC.setShow(m_show["pictureContainer"]);
         try
         {
            m_lockDescriptionText = m_show["lockDescriptionTxt"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_lockDescriptionText);
         }
         catch(error:Error)
         {
            m_lockDescriptionText = null;
         }
         try
         {
            m_remainNumText = m_show["remainNumTxt"];
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remainNumText);
         }
         catch(error:Error)
         {
            m_remainNumText = null;
         }
      }
      
      private function initShow2() : void
      {
         if(m_fuBenData == null || m_xiangMoLevelSaveOneData == null)
         {
            return;
         }
         showRemainNum();
         showLockDescription();
         m_pictruecontainerMC.gotoAndStop(m_fuBenData.getShowFrameLabel());
      }
      
      override public function lock() : void
      {
         super.lock();
         m_lockDescriptionText = m_show["lockDescriptionTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_lockDescriptionText);
         m_remainNumText = null;
         showLockDescription();
      }
      
      override public function unLock() : void
      {
         super.unLock();
         m_lockDescriptionText = null;
         m_remainNumText = m_show["remainNumTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remainNumText);
         showRemainNum();
      }
      
      private function showLockDescription() : void
      {
         if(m_lockDescriptionText != null && m_fuBenData != null)
         {
            m_lockDescriptionText.text = m_fuBenData.getLockDescription();
         }
      }
      
      private function showRemainNum() : void
      {
         if(m_remainNumText != null && m_fuBenData != null && m_xiangMoLevelSaveOneData != null)
         {
            m_remainNumText.text = m_remainNum.toString();
         }
      }
   }
}

