package UI.MessageBox
{
   import UI.Buff.Buff.BuffVO;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.EquipmentCells.ShopEquipmentCell;
   import UI.EquipmentCells.StorageEquipmentCell;
   import UI.EquipmentCells.UsedEquipmentCell;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MiragePanel.EssenceIcon;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Pets.Talents.Talent;
   import UI.Players.ImplicitProPlayer.ImplicitProPlayer;
   import UI.Players.Player;
   import UI.Privilege.Privilege;
   import UI.ShiTu.TuDiSkillVO;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetAwakeSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.Skill;
   import UI.Skills.SkillVO;
   import UI.TaskPanel.DataLayer_ExTaskPanel;
   import UI.TaskPanel.DataLayer_InTaskPanel;
   import UI.VIPPanel.VIPPanel;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountSkillVO.MountSkillVO;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.geom.Point;
   
   public class MessageBoxEngine extends MySprite
   {
      
      private static var instance:MessageBoxEngine = null;
      
      public var messageBoxMode:int;
      
      private const NORMAL_WIDTH:Number = 220;
      
      private const PET_MESSAGE_WIDTH:Number = 220;
      
      private const SCROLL_MESSAGE_WIDTH:Number = 200;
      
      private const SKILL_MESSAGE_WIDTH:Number = 200;
      
      private const SHOP_MESSAGE_WIDTH:Number = 200;
      
      private const MAKE_PANEL_MESSAGE_WIDTH:Number = 200;
      
      private const VIP_PANEL_WIDTH:Number = 200;
      
      private const OTHER_WIDTH:Number = 250;
      
      private const TALENT_MESSAGE_WIDTH:Number = 200;
      
      private const NAME_SIZE:int = 16;
      
      private const ATTRIBUTE_SIZE:int = 16;
      
      private const DESCRIPTION_SIZE:int = 14;
      
      private const OWNER_SIZE:int = 16;
      
      private const PRICE_SIZE:int = 14;
      
      private var _messageBox:MessageBox;
      
      private var _messageBox_equipped:MessageBox;
      
      private var m_showPrilegeMessage:ShowPrilegeMessage;
      
      private var m_showTalentMessage:ShowTalentMessage;
      
      private var m_showEssenceIconMessage:ShowEssenceIconMessage;
      
      private var m_showBuffVOMessage:ShowBuffVOMessage;
      
      private var m_showTuDiSkillMessage:ShowTuDiSkillMessage;
      
      private var m_showPetAwakeSkillMessage:ShowPetAwakePassiveSkillMessage;
      
      private var m_showOtherMessage:ShowOtherMessage;
      
      private var m_showAutomaticPetSkillMessage:ShowAutomaticPetSkillMessage;
      
      private var m_showMountSkillMessage:ShowMountSkillMessage;
      
      private var _font:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
      
      public function MessageBoxEngine()
      {
         if(instance == null)
         {
            super();
            _messageBox = new MessageBox();
            _messageBox_equipped = new MessageBox();
            _messageBox.init("",220,0,10,10,16777215,393216,0.7,2,16744512,0.7);
            _messageBox_equipped.init("",220,0,10,10,16777215,393216,0.7,2,16744512,0.7);
            _messageBox.addEventListener("addedToStage",addToStage,false,0,true);
            addChild(_messageBox);
            instance = this;
            return;
         }
         throw new Error("fuck you ! 没看见实例已经存在么？！");
      }
      
      public static function getInstance() : MessageBoxEngine
      {
         if(instance == null)
         {
            instance = new MessageBoxEngine();
         }
         return instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_messageBox)
         {
            _messageBox.removeEventListener("addedToStage",addToStage,false);
            _messageBox.clear();
         }
         _messageBox = null;
         if(_messageBox_equipped)
         {
            _messageBox_equipped.clear();
         }
         _messageBox_equipped = null;
         ClearUtil.clearObject(m_showPrilegeMessage);
         m_showPrilegeMessage = null;
         ClearUtil.clearObject(m_showTalentMessage);
         m_showTalentMessage = null;
         ClearUtil.clearObject(m_showEssenceIconMessage);
         m_showEssenceIconMessage = null;
         ClearUtil.clearObject(m_showBuffVOMessage);
         m_showBuffVOMessage = null;
         ClearUtil.clearObject(m_showTuDiSkillMessage);
         m_showTuDiSkillMessage = null;
         ClearUtil.clearObject(m_showPetAwakeSkillMessage);
         m_showPetAwakeSkillMessage = null;
         ClearUtil.clearObject(m_showOtherMessage);
         m_showOtherMessage = null;
         ClearUtil.clearObject(m_showAutomaticPetSkillMessage);
         m_showAutomaticPetSkillMessage = null;
         ClearUtil.clearObject(m_showMountSkillMessage);
         m_showMountSkillMessage = null;
         instance = null;
      }
      
      public function initBox(param1:*, param2:Player, param3:MessageBox) : void
      {
         var _loc29_:String = null;
         var _loc20_:String = null;
         var _loc5_:String = null;
         var _loc16_:String = null;
         var _loc36_:String = null;
         var _loc6_:Equipment = null;
         var _loc27_:String = null;
         var _loc14_:String = null;
         var _loc45_:String = null;
         var _loc44_:int = 0;
         var _loc31_:int = 0;
         var _loc35_:* = undefined;
         var _loc43_:* = undefined;
         var _loc51_:ImplicitProPlayer = null;
         var _loc19_:ShowAbleEquipmentVOMessage = null;
         var _loc26_:String = null;
         var _loc28_:String = null;
         var _loc46_:String = null;
         var _loc22_:Array = null;
         var _loc30_:String = null;
         var _loc47_:String = null;
         var _loc7_:Array = null;
         var _loc32_:String = null;
         var _loc48_:String = null;
         var _loc4_:Array = null;
         var _loc39_:String = null;
         var _loc37_:String = null;
         var _loc52_:int = 0;
         var _loc10_:Array = null;
         var _loc42_:* = undefined;
         var _loc15_:Number = NaN;
         var _loc8_:* = null;
         var _loc50_:String = null;
         var _loc49_:String = null;
         var _loc33_:Array = null;
         var _loc17_:Array = null;
         var _loc38_:PetEquipmentVO = null;
         var _loc21_:int = 0;
         var _loc23_:String = null;
         var _loc9_:int = 0;
         var _loc41_:String = null;
         var _loc11_:String = null;
         var _loc40_:int = 0;
         var _loc24_:XML = null;
         var _loc53_:Array = null;
         var _loc25_:Array = null;
         var _loc34_:ShowInsetGemMessage = null;
         var _loc12_:String = null;
         var _loc13_:String = null;
         var _loc18_:Skill = null;
         param3.clearSprite();
         if(messageBoxMode == 1)
         {
            param3.boxWidth = 200;
         }
         else if(messageBoxMode == 0 || messageBoxMode == 4)
         {
            param3.boxWidth = 220;
         }
         else if(messageBoxMode == 2)
         {
            param3.boxWidth = 220;
         }
         else if(messageBoxMode == 3)
         {
            param3.boxWidth = 200;
         }
         else if(messageBoxMode == 5)
         {
            param3.boxWidth = 200;
         }
         else
         {
            param3.boxWidth = 250;
         }
         if(param1 is EquipmentVO)
         {
            param1 = MyFunction2.sheatheEquipmentShell(param1 as EquipmentVO);
         }
         else if(param1 is SkillVO)
         {
            param1 = new Skill(param1 as SkillVO);
         }
         if(param1 is Equipment)
         {
            _loc29_ = "";
            _loc20_ = "";
            _loc5_ = "";
            _loc16_ = "";
            _loc36_ = "";
            _loc6_ = param1 as Equipment;
            _loc27_ = "";
            _loc14_ = "";
            _loc45_ = "";
            _loc44_ = 0;
            if(_loc6_ == null || _loc6_.equipmentVO == null)
            {
               return;
            }
            if(_loc6_.equipmentVO.isShopItem)
            {
               _loc6_.equipmentVO = InitUI.getInstance().getShopItemVO(_loc6_.equipmentVO,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
            }
            _loc20_ += MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.name,16);
            if(_loc6_.equipmentVO is AbleEquipmentVO)
            {
               if(_loc6_.equipmentVO.level > 0)
               {
                  _loc20_ += MessageBoxFunction.getInstance().toHTMLText(" +" + _loc6_.equipmentVO.level,16);
               }
               if(Boolean((_loc6_.equipmentVO as AbleEquipmentVO).implictProPlayerId) && param2)
               {
                  _loc51_ = param2.playerVO.getImplicitProPlayer((_loc6_.equipmentVO as AbleEquipmentVO).implictProPlayerId);
                  if(_loc51_)
                  {
                     _loc27_ = _loc51_.getProDescription();
                  }
               }
               if((_loc6_.equipmentVO as AbleEquipmentVO).maxHoleNum)
               {
                  _loc14_ = MessageBoxFunction.getInstance().toHTMLText("开孔数：",15) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as AbleEquipmentVO).getHoleNum() + "/" + (_loc6_.equipmentVO as AbleEquipmentVO).maxHoleNum,15) + MessageBoxFunction.getInstance().toHTMLText("<br>");
                  if((_loc6_.equipmentVO as AbleEquipmentVO).getHoleNum())
                  {
                     _loc19_ = new ShowAbleEquipmentVOMessage();
                     _loc14_ += _loc19_.addMessage_InsetGem(_loc6_.equipmentVO as AbleEquipmentVO);
                  }
               }
            }
            if(_loc6_.equipmentVO.equipmentType == "clothes")
            {
               _loc26_ = (_loc6_.equipmentVO as ClothesEquipmentVO).defence.toString();
               _loc28_ = ((_loc6_.equipmentVO as ClothesEquipmentVO).riot * 100).toFixed() + "%";
               _loc46_ = (_loc6_.equipmentVO as ClothesEquipmentVO).rengPin.toString();
               _loc29_ = "";
               _loc35_ = (_loc6_.equipmentVO as ClothesEquipmentVO).addPlayerSaveAttr;
               _loc43_ = (_loc6_.equipmentVO as ClothesEquipmentVO).addPlayerSaveAttrVals;
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc22_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc29_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc22_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc22_[1],16);
                  _loc22_[0] = null;
                  _loc22_[1] = null;
                  _loc22_ = null;
                  _loc44_++;
               }
               if(_loc31_ > 0)
               {
                  _loc29_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc29_,"#00ff33");
                  _loc29_ = _loc29_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
               }
               if(messageBoxMode == 1 || messageBoxMode == 3)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：套装",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("防御力：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as ClothesEquipmentVO).minDefence + "-" + (_loc6_.equipmentVO as ClothesEquipmentVO).maxDefence,16) + ((_loc6_.equipmentVO as ClothesEquipmentVO).maxRiot ? "<br>" + MessageBoxFunction.getInstance().toHTMLText("防暴值：",16) + MessageBoxFunction.getInstance().toHTMLText(((_loc6_.equipmentVO as ClothesEquipmentVO).minRiot * 100).toFixed() + "%" + "-" + ((_loc6_.equipmentVO as ClothesEquipmentVO).maxRiot * 100).toFixed() + "%",16) : "") + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as ClothesEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as ClothesEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_
                  .equipmentVO.description,14);
               }
               else if(messageBoxMode == 2)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：套装",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("防御力：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc26_,16) + ((_loc6_.equipmentVO as ClothesEquipmentVO).riot ? "<br>" + MessageBoxFunction.getInstance().toHTMLText("防暴值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc28_,16) : "") + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as ClothesEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as ClothesEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：套装",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("防御力：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc26_,16) + ((_loc6_.equipmentVO as ClothesEquipmentVO).riot ? "<br>" + MessageBoxFunction.getInstance().toHTMLText("防暴值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc28_,16) : "") + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc46_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[1])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[1],param2,_messageBox_equipped);
                  addChild(_messageBox_equipped);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "gourd")
            {
               _loc30_ = (_loc6_.equipmentVO as GourdEquipmentVO).maxMagic.toString();
               _loc47_ = (_loc6_.equipmentVO as GourdEquipmentVO).rengPin.toString();
               _loc29_ = "";
               _loc35_ = (_loc6_.equipmentVO as GourdEquipmentVO).addPlayerSaveAttr;
               _loc43_ = (_loc6_.equipmentVO as GourdEquipmentVO).addPlayerSaveAttrVals;
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc7_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc29_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc7_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc7_[1],16);
                  _loc7_[0] = null;
                  _loc7_[1] = null;
                  _loc7_ = null;
                  _loc44_++;
               }
               if(_loc31_ > 0)
               {
                  _loc29_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc29_,"#00ff33");
                  _loc29_ = _loc29_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
               }
               if(messageBoxMode == 1 || messageBoxMode == 3)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：葫芦",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("魔法值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as GourdEquipmentVO).minMaxMagic + "-" + (_loc6_.equipmentVO as GourdEquipmentVO).maxMaxMagic,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as GourdEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as GourdEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else if(messageBoxMode == 2)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：葫芦",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("魔法值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc30_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as GourdEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as GourdEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：葫芦",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("魔法值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc30_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc47_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[2])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[2],param2,_messageBox_equipped);
                  addChild(_messageBox_equipped);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "necklace")
            {
               _loc32_ = (_loc6_.equipmentVO as NecklaceEquipmentVO).criticalRate.toString();
               _loc48_ = (_loc6_.equipmentVO as NecklaceEquipmentVO).rengPin.toString();
               _loc29_ = "";
               _loc35_ = (_loc6_.equipmentVO as NecklaceEquipmentVO).addPlayerSaveAttr;
               _loc43_ = (_loc6_.equipmentVO as NecklaceEquipmentVO).addPlayerSaveAttrVals;
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc4_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc29_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc4_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc4_[1],16);
                  _loc4_[0] = null;
                  _loc4_[1] = null;
                  _loc4_ = null;
                  _loc44_++;
               }
               if(_loc31_ > 0)
               {
                  _loc29_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc29_,"#00ff33");
                  _loc29_ = _loc29_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
               }
               if(messageBoxMode == 1 || messageBoxMode == 3)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：项链",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("暴击率：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as NecklaceEquipmentVO).minCriticalRate + "-" + (_loc6_.equipmentVO as NecklaceEquipmentVO).maxCriticalRate,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as NecklaceEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as NecklaceEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else if(messageBoxMode == 2)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：项链",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("暴击率：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc32_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as NecklaceEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as NecklaceEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：项链",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("暴击率：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc32_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc48_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[5])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[5],param2,_messageBox_equipped);
                  addChild(_messageBox_equipped);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "precious")
            {
               _loc52_ = 15;
               param3.boxWidth += 40;
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：灵符",16);
               _loc5_ = _loc5_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
               if(messageBoxMode != 3 && messageBoxMode != 7)
               {
                  _loc35_ = (_loc6_.equipmentVO as PreciousEquipmentVO).basisAttr;
                  _loc43_ = (_loc6_.equipmentVO as PreciousEquipmentVO).basisAttrValue;
                  _loc42_ = (_loc6_.equipmentVO as PreciousEquipmentVO).basisUpValue;
                  _loc31_ = int(_loc35_.length);
                  _loc44_ = 0;
                  while(_loc44_ < _loc31_)
                  {
                     if((_loc6_.equipmentVO as PreciousEquipmentVO).level > 0)
                     {
                        _loc10_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_] + _loc42_[_loc44_]);
                        _loc15_ = _loc42_[_loc44_] / (_loc6_.equipmentVO as PreciousEquipmentVO).level;
                     }
                     else
                     {
                        _loc10_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                        _loc15_ = _loc42_[_loc44_];
                     }
                     _loc39_ = "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc10_[0] + "：",_loc52_);
                     _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#DB70DB");
                     _loc5_ += _loc39_;
                     _loc37_ = MessageBoxFunction.getInstance().toHTMLText(_loc10_[1],_loc52_);
                     _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                     _loc5_ += _loc37_;
                     if((_loc6_.equipmentVO as PreciousEquipmentVO).level < 9)
                     {
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText(" 成长:",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#DB70DB");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(_loc15_.toFixed(2)) + "%",_loc52_) + "<br>";
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                     }
                     _loc10_[0] = null;
                     _loc10_[1] = null;
                     _loc10_ = null;
                     _loc44_++;
                  }
                  if(_loc31_ > 0)
                  {
                     _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>";
                  }
                  _loc29_ = "";
                  _loc35_ = (_loc6_.equipmentVO as PreciousEquipmentVO).addPlayerSaveAttr;
                  _loc43_ = (_loc6_.equipmentVO as PreciousEquipmentVO).addPlayerSaveAttrVals;
                  _loc31_ = int(_loc35_.length);
                  _loc44_ = 0;
                  while(_loc44_ < _loc31_)
                  {
                     _loc10_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                     _loc29_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc10_[0] + "：",_loc52_) + MessageBoxFunction.getInstance().toHTMLText(_loc10_[1],_loc52_);
                     _loc10_[0] = null;
                     _loc10_[1] = null;
                     _loc10_ = null;
                     _loc44_++;
                  }
                  if(_loc31_ > 0)
                  {
                     _loc29_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc29_,"#00ff33");
                     _loc29_ = _loc29_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
                  }
                  _loc5_ += _loc29_;
                  _loc35_ = (_loc6_.equipmentVO as PreciousEquipmentVO).sAttrName;
                  _loc43_ = (_loc6_.equipmentVO as PreciousEquipmentVO).sAttrValue;
                  _loc42_ = (_loc6_.equipmentVO as PreciousEquipmentVO).sAvgValue;
                  _loc31_ = int(_loc35_.length);
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("特殊属性",14);
                  _loc44_ = 0;
                  while(_loc44_ < _loc31_)
                  {
                     if(_loc35_[_loc44_] == "doubleexpgold")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("获得的经验值和金钱可翻",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc43_[_loc44_])),16 - 1);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("倍",16 - 1);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "increaseAttack")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("每",16 - 1);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc42_[_loc44_])),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个魔法点增加",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(_loc43_[_loc44_].toFixed(2)),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个攻击点",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "increaseShanbi")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("每",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc42_[_loc44_])),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个防御点增加",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(_loc43_[_loc44_].toFixed(2)),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个闪避点",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "dincreasehp")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("每发动普攻或者技能时降低百分之",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc43_[_loc44_])),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("的血量",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "dincreaseDef")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("防御降低",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc43_[_loc44_])) + "%",_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                     }
                     else if(_loc35_[_loc44_] == "increaseBaoji")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("每",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc42_[_loc44_])),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个攻击点增加",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(_loc43_[_loc44_].toFixed(2)),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个暴击点",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "changerenpin")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("将人品值全部转换为防御力",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     else if(_loc35_[_loc44_] == "increaseMingzhong")
                     {
                        _loc5_ += MessageBoxFunction.getInstance().toHTMLText("<br>",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("血量每降低",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(int(_loc42_[_loc44_])),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("%增加",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                        _loc37_ = MessageBoxFunction.getInstance().toHTMLText(String(_loc43_[_loc44_].toFixed(4)),_loc52_);
                        _loc37_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc37_,"#00ff33");
                        _loc5_ += _loc37_;
                        _loc39_ = MessageBoxFunction.getInstance().toHTMLText("个命中点",_loc52_);
                        _loc39_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc39_,"#FF7F00");
                        _loc5_ += _loc39_;
                     }
                     _loc44_++;
                  }
                  if(_loc31_ > 0)
                  {
                     _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>";
                  }
               }
               _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as PreciousEquipmentVO).description,14) + "<br>";
            }
            else if(_loc6_.equipmentVO.equipmentType == "weapon")
            {
               _loc50_ = (_loc6_.equipmentVO as WeaponEquipmentVO).attack.toString();
               _loc49_ = (_loc6_.equipmentVO as WeaponEquipmentVO).rengPin.toString();
               _loc29_ = "";
               _loc35_ = (_loc6_.equipmentVO as WeaponEquipmentVO).addPlayerSaveAttr;
               _loc43_ = (_loc6_.equipmentVO as WeaponEquipmentVO).addPlayerSaveAttrVals;
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc33_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc29_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc33_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc33_[1],16);
                  _loc33_[0] = null;
                  _loc33_[1] = null;
                  _loc33_ = null;
                  _loc44_++;
               }
               if(_loc31_ > 0)
               {
                  _loc29_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc29_,"#00ff33");
                  _loc29_ = _loc29_ + ("<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>");
               }
               if(messageBoxMode == 1 || messageBoxMode == 3)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：武器",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("攻击力：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as WeaponEquipmentVO).minAttack + "-" + (_loc6_.equipmentVO as WeaponEquipmentVO).maxAttack,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as WeaponEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as WeaponEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else if(messageBoxMode == 2)
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：武器",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("攻击力：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc50_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as WeaponEquipmentVO).minRenPin + "-" + (_loc6_.equipmentVO as WeaponEquipmentVO).maxRenPin,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               else
               {
                  _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：武器",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("攻击力：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc50_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("人品值：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc49_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + _loc29_ + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               }
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[4])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[4],param2,_messageBox_equipped);
                  addChildAt(_messageBox_equipped,numChildren);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "forever_fashion")
            {
               _loc35_ = (_loc6_.equipmentVO as ForeverFashionEquipmentVO).addPlayerAttributes;
               _loc43_ = (_loc6_.equipmentVO as ForeverFashionEquipmentVO).addPlayerAttributeValues;
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：永久时装",16);
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc17_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc17_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc17_[1],16);
                  _loc17_[0] = null;
                  _loc17_[1] = null;
                  _loc17_ = null;
                  _loc44_++;
               }
               _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("期限：永久",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[3])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[3],param2,_messageBox_equipped);
                  addChildAt(_messageBox_equipped,numChildren);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "pet")
            {
               _loc38_ = _loc6_.equipmentVO as PetEquipmentVO;
               _loc21_ = _loc38_.petLevel;
               _loc23_ = "";
               _loc9_ = _loc38_.level;
               _loc41_ = _loc38_.activeSkillVO.name;
               _loc11_ = "";
               if(_loc38_.talentVO)
               {
                  _loc23_ = _loc38_.talentVO.name;
               }
               else
               {
                  _loc23_ = MessageBoxFunction.getInstance().addHTMLTextColor("未知","#ff0000");
               }
               if(_loc38_.passiveSkillVOs)
               {
                  _loc31_ = int(_loc38_.passiveSkillVOs.length);
                  _loc44_ = 0;
                  while(_loc44_ < _loc31_)
                  {
                     _loc11_ += _loc38_.passiveSkillVOs[_loc44_].name;
                     if(_loc44_ < _loc31_ - 1)
                     {
                        _loc11_ += "，";
                     }
                     else
                     {
                        _loc11_ += "。";
                     }
                     _loc44_++;
                  }
                  if(_loc31_ == 0 && messageBoxMode == 1)
                  {
                     _loc11_ += MessageBoxFunction.getInstance().addHTMLTextColor("未知","#ff0000");
                  }
               }
               else if(messageBoxMode == 1)
               {
                  _loc11_ += MessageBoxFunction.getInstance().addHTMLTextColor("未知","#ff0000");
               }
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc21_.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("天赋：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc23_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("进化阶段：",16) + MessageBoxFunction.getInstance().toHTMLText("第" + MyFunction.getInstance().changeNum(_loc9_) + "阶段",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("主动技能：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc41_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("被动技能：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc11_,16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("------------------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               _loc40_ = _loc38_.id;
               if(_loc40_ == 10400120 || _loc40_ == 10400220 || _loc40_ == 10400320 || _loc40_ == 10400421)
               {
                  _loc24_ = XMLSingle.getInstance().equipmentXML.item.(@id == _loc40_)[0];
                  if(_loc24_)
                  {
                     _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("------------------------------",14) + "<br>";
                     _loc5_ = _loc5_ + (MessageBoxFunction.getInstance().toHTMLText("增加" + int(_loc24_.@addHitPet) + "点命中",14) + "<br>");
                     _loc5_ = _loc5_ + (MessageBoxFunction.getInstance().toHTMLText("------------------------------",14) + "<br>");
                  }
               }
               param3.boxWidth = 220;
            }
            else if(_loc6_.equipmentVO.equipmentType == "material")
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：材料",16);
               if((_loc6_.equipmentVO as StackEquipmentVO).num > 1)
               {
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("数量：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as StackEquipmentVO).num.toString(),16);
               }
               _loc5_ += "<br>";
               _loc5_ = _loc5_ + (MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14));
            }
            else if(_loc6_.equipmentVO.equipmentType == "scroll")
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("制作物品类型：",16) + "<br>" + scrollFunction((_loc6_.equipmentVO as ScrollEquipmentVO).compositeEquipmentVO) + "<br>" + scrollFunction2((_loc6_.equipmentVO as ScrollEquipmentVO).materialVOs,(_loc6_.equipmentVO as ScrollEquipmentVO).requiredNums) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("合成所需金钱：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc6_.equipmentVO as ScrollEquipmentVO).requiredMoney + "元宝",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("--------------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
               param3.boxWidth = 200;
            }
            else if(_loc6_.equipmentVO.equipmentType == "egg")
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("快把它放到\n孵化NPC的\n孵化器中孵\n化出可爱的\n宠物吧！",16);
            }
            else if(_loc6_.equipmentVO.equipmentType == "fashion")
            {
               _loc35_ = (_loc6_.equipmentVO as FashionEquipmentVO).addPlayerAttributes;
               _loc43_ = (_loc6_.equipmentVO as FashionEquipmentVO).addPlayerAttributeValues;
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc53_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc53_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc53_[1],16);
                  _loc53_[0] = null;
                  _loc53_[1] = null;
                  _loc53_ = null;
                  _loc44_++;
               }
               _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>";
               _loc5_ = _loc5_ + (MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("期限：" + (_loc6_.equipmentVO as FashionEquipmentVO).remainingTime.toString() + "天",16));
               if(param3 == _messageBox && messageBoxMode == 0 && Boolean(param2) && param2.playerVO.inforEquipmentVOs[3])
               {
                  initBox(param2.playerVO.inforEquipmentVOs[3],param2,_messageBox_equipped);
                  addChildAt(_messageBox_equipped,numChildren);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "medal")
            {
               _loc35_ = (_loc6_.equipmentVO as MedalEquipmentVO).addPlayerAttributes;
               _loc43_ = (_loc6_.equipmentVO as MedalEquipmentVO).addPlayerAttributeValues;
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText("类型：勋章",16);
               _loc31_ = int(_loc35_.length);
               _loc44_ = 0;
               while(_loc44_ < _loc31_)
               {
                  _loc25_ = returnAttributeNameByAttribute(_loc35_[_loc44_],_loc43_[_loc44_]);
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc25_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc25_[1],16);
                  _loc25_[0] = null;
                  _loc25_[1] = null;
                  _loc25_ = null;
                  _loc44_++;
               }
               if((_loc6_.equipmentVO as MedalEquipmentVO).TimeLimit == -1)
               {
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("期限：永久",16);
               }
               else
               {
                  _loc5_ += "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("期限：" + (_loc6_.equipmentVO as MedalEquipmentVO).remainingTime.toString() + "天",16);
               }
            }
            else if(_loc6_.equipmentVO.equipmentType == "insetGem")
            {
               _loc34_ = new ShowInsetGemMessage();
               _loc5_ += _loc34_.addMessage(_loc6_.equipmentVO as InsetGemEquipmentVO);
            }
            else
            {
               _loc5_ += MessageBoxFunction.getInstance().toHTMLText(_loc6_.equipmentVO.description,14);
            }
            if(_loc6_.equipmentVO.owner != "" && param3 != _messageBox_equipped)
            {
               if(_loc6_.equipmentVO.owner == "SunWuKong")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：孙悟空",16);
                  if(param2 && param2.playerVO.playerType != "SunWuKong")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "BaiLongMa")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：白龙马",16);
                  if(param2 && param2.playerVO.playerType != "BaiLongMa")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "ErLangShen")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：二郎神",16);
                  if(param2 && param2.playerVO.playerType != "ErLangShen")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "ChangE")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：嫦娥",16);
                  if(param2 && param2.playerVO.playerType != "ChangE")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "Fox")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：灵狐",16);
                  if(param2 && param2.playerVO.playerType != "Fox")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "TieShan")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：铁扇公主",16);
                  if(param2 && param2.playerVO.playerType != "TieShan")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "Houyi")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：后羿",16);
                  if(param2 && param2.playerVO.playerType != "Houyi")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
               else if(_loc6_.equipmentVO.owner == "ZiXia")
               {
                  _loc16_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("使用者：紫霞仙子",16);
                  if(param2 && param2.playerVO.playerType != "ZiXia")
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#ff0000");
                  }
                  else
                  {
                     _loc16_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc16_,"#00ff00");
                  }
               }
            }
            if((messageBoxMode == 0 || messageBoxMode == 4) && param3 != _messageBox_equipped)
            {
               if(_loc6_.equipmentVO.isAbleSell)
               {
                  _loc36_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("出售价格：",14) + MessageBoxFunction.getInstance().toHTMLText(int(_loc6_.equipmentVO.price * ProgramStartData.getInstance().getDiscount()) + "元宝",14);
                  _loc36_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc36_,"#ffff00");
               }
               else
               {
                  _loc36_ += MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("贵重物品！",14);
                  _loc36_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc36_,"#ff0000");
               }
            }
            _loc20_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc20_,"#" + _loc6_.equipmentVO.messageBoxColor.toString(16));
            _loc5_ = MessageBoxFunction.getInstance().addHTMLTextColor(_loc5_,"#ffffff");
            _loc45_ += _loc6_.equipmentVO.getIsBinding() ? MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText("已锁定, 不能传送！",15),"#ff0000") : "";
            param3.addSprite((_loc6_ as Equipment).imperfectClone() as DisplayObject);
            if(param3 == _messageBox_equipped)
            {
               param3.htmlText = MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText("已装备",20),"#ff0000") + MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().toHTMLText("<br>") + _loc20_ + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + _loc5_ + _loc27_ + _loc14_;
            }
            else
            {
               param3.htmlText = _loc20_ + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + _loc5_ + _loc27_ + _loc14_ + _loc45_ + _loc16_ + _loc36_;
            }
         }
         else if(param1 is Skill && !((param1 as Skill).skillVO is PetAwakeSkillVO))
         {
            _loc12_ = "";
            _loc13_ = "";
            _loc18_ = param1 as Skill;
            _loc12_ += MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.name,16);
            if(_loc18_.skillVO.type == "playerPassive")
            {
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.level.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.description,14);
            }
            else if(_loc18_.skillVO.type == "playerActive")
            {
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.level.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("冷却时间：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PlayerActiveSkillVO).coolDown.toString() + "秒",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("消耗：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PlayerActiveSkillVO).manaCost.toString() + "MP",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.description,14);
            }
            else if(_loc18_.skillVO.type == "petPassive")
            {
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.level.toString(),16) + "<br>";
               switch((_loc18_.skillVO as PetPassiveSkillVO).passiveType)
               {
                  case "attack":
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("类别：攻击系",16) + "<br>";
                     break;
                  case "defence":
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("类别：防御系",16) + "<br>";
                     break;
                  case "auxiliary":
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("类别：辅助系",16) + "<br>";
                     break;
                  default:
                     throw new Error("不存在的宠物被动技能类型");
               }
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.description + (_loc18_.skillVO as PetPassiveSkillVO).value.toString(),14) + MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText("（幻化提升： " + (_loc18_.skillVO as PetPassiveSkillVO).promoteValue + "）",14),"#00ff00") + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetPassiveSkillVO).unit,14);
            }
            else if(_loc18_.skillVO.type == "petActive")
            {
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("等级：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.level.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("冷却时间：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).coolDown.toString() + "秒",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("消耗：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).manaCost.toString() + "MP",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("消耗：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).essenceCost.toString() + "点精气",16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("伤害：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).hurt.toString(),16) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("PK伤害：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO)
               .pkHurt.toString(),16) + "<br>";
               if((_loc18_.skillVO as PetActiveSkillVO).diePrecent != 0)
               {
                  _loc13_ += MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>";
               }
               if((_loc18_.skillVO as PetActiveSkillVO).className == "PetSkill_Zhulong")
               {
                  if((_loc18_.skillVO as PetActiveSkillVO).diePrecent != 0)
                  {
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("斩杀场上血量值低于：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).diePrecent.toString() + "%的敌人",16) + "<br>";
                  }
               }
               if((_loc18_.skillVO as PetActiveSkillVO).className == "PetSkill_QiongQi")
               {
                  if((_loc18_.skillVO as PetActiveSkillVO).diePrecentBase != 0)
                  {
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("破除场上所有怪物：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).diePrecentBase.toString() + "%的防御",16) + "<br>";
                  }
               }
               if((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed != 0 || (_loc18_.skillVO as PetActiveSkillVO).subDefence != 0)
               {
                  _loc13_ += MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>";
               }
               if((_loc18_.skillVO as PetActiveSkillVO).className == "PetSkill_Molong")
               {
                  if((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed != 0)
                  {
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("无视敌人闪避：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed.toString() + "%",16) + "<br>";
                     _loc13_ = _loc13_ + (MessageBoxFunction.getInstance().toHTMLText("无视敌人防爆：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed.toString() + "%",16) + "<br>");
                  }
               }
               else
               {
                  if((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed != 0)
                  {
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("敌人减速：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed.toString() + "%",16) + "<br>";
                  }
                  if((_loc18_.skillVO as PetActiveSkillVO).subDefence != 0)
                  {
                     _loc13_ += MessageBoxFunction.getInstance().toHTMLText("护甲穿透：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc18_.skillVO as PetActiveSkillVO).subDefence.toString() + "%",16) + "<br>";
                  }
               }
               if((_loc18_.skillVO as PetActiveSkillVO).subWalkSpeed != 0 || (_loc18_.skillVO as PetActiveSkillVO).subDefence != 0)
               {
                  _loc13_ += MessageBoxFunction.getInstance().toHTMLText("持续15秒",14) + "<br>";
               }
               _loc13_ += MessageBoxFunction.getInstance().toHTMLText("----------------------",14) + "<br>" + "<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc18_.skillVO.description,14);
            }
            param3.boxWidth = 200;
            param3.addSprite((_loc18_ as Skill).clone() as DisplayObject);
            param3.htmlText = _loc12_ + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + MessageBoxFunction.getInstance().toHTMLText("<br>",16) + _loc13_;
         }
         if(param1 is Privilege)
         {
            if(m_showPrilegeMessage == null)
            {
               m_showPrilegeMessage = new ShowPrilegeMessage();
            }
            m_showPrilegeMessage.showMessage(param1,param3);
         }
         else if(param1 is Talent)
         {
            if(m_showTalentMessage == null)
            {
               m_showTalentMessage = new ShowTalentMessage();
            }
            m_showTalentMessage.showMessage(param1,param3);
         }
         else if(param1 is EssenceIcon)
         {
            if(m_showEssenceIconMessage == null)
            {
               m_showEssenceIconMessage = new ShowEssenceIconMessage();
            }
            m_showEssenceIconMessage.showMessage(param1,param3);
         }
         else if(param1 is BuffVO)
         {
            if(m_showBuffVOMessage == null)
            {
               m_showBuffVOMessage = new ShowBuffVOMessage();
            }
            m_showBuffVOMessage.showMessage(param1,param3);
         }
         else if(param1 is TuDiSkillVO)
         {
            if(m_showTuDiSkillMessage == null)
            {
               m_showTuDiSkillMessage = new ShowTuDiSkillMessage();
            }
            m_showTuDiSkillMessage.showMessage(param1,param2,param3);
         }
         else if(param1 is AutomaticPetSkillVO)
         {
            if(m_showAutomaticPetSkillMessage == null)
            {
               m_showAutomaticPetSkillMessage = new ShowAutomaticPetSkillMessage();
            }
            m_showAutomaticPetSkillMessage.showMessage(param1,param3);
         }
         else if(param1 is MountSkillVO)
         {
            if(m_showMountSkillMessage == null)
            {
               m_showMountSkillMessage = new ShowMountSkillMessage();
            }
            m_showMountSkillMessage.showMessage(param1,param3);
         }
         if(param1 is Skill && (param1 as Skill).skillVO is PetAwakeSkillVO)
         {
            if(m_showPetAwakeSkillMessage == null)
            {
               m_showPetAwakeSkillMessage = new ShowPetAwakePassiveSkillMessage();
            }
            m_showPetAwakeSkillMessage.showMessage(param1.skillVO as PetAwakeSkillVO,param2,param3);
         }
         if(m_showOtherMessage == null)
         {
            m_showOtherMessage = new ShowOtherMessage();
         }
         m_showOtherMessage.showMessage(param1,param3,param2);
         setEquippedBoxPosition();
      }
      
      private function imgHtml(param1:String) : String
      {
         return "<img src=\'" + param1 + "\'  align=\'right\' hspace=\'0\'  />";
      }
      
      private function addToStage(param1:Event) : void
      {
         _messageBox.addEventListener("removedFromStage",removeFromStage,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         _messageBox.removeEventListener("removedFromStage",removeFromStage,false);
      }
      
      public function showMessageBox(param1:UIPassiveEvent, param2:Player = null) : void
      {
         if(param1.target is ShopEquipmentCell || param1.target is DataLayer_InTaskPanel || param1.target is DataLayer_ExTaskPanel)
         {
            messageBoxMode = 1;
         }
         else if(param1.target is PackageEquipmentCell || param1.target is StorageEquipmentCell || param1.target is VIPPanel)
         {
            messageBoxMode = 0;
         }
         else if(param1.target is UsedEquipmentCell)
         {
            messageBoxMode = 4;
         }
         else if(param1.target is Privilege)
         {
            messageBoxMode = 5;
         }
         else if(param1.target is Talent)
         {
            messageBoxMode = 6;
         }
         else if((param1.data as Object).hasOwnProperty("messageBoxMode"))
         {
            messageBoxMode = param1.data.messageBoxMode;
         }
         else
         {
            messageBoxMode = -1;
         }
         initBox(param1.data.equipment,param2,_messageBox);
         recycling(_messageBox,_messageBox.stage.stageHeight);
         if(_messageBox_equipped)
         {
            recycling(_messageBox_equipped,_messageBox.stage.stageHeight);
         }
         var _loc3_:Point = _messageBox.localToGlobal(new Point(_messageBox.mouseX,_messageBox.mouseY));
         if(stage.mouseX + width + 20 <= stage.stageWidth)
         {
            x = _loc3_.x + 20;
         }
         else
         {
            x = _loc3_.x - width - 20;
         }
         if(stage.mouseY + height + 20 <= stage.stageHeight)
         {
            y = _loc3_.y + 20;
         }
         else
         {
            if(stage.stageHeight < height)
            {
               throw new Error("信息框的高度高于舞台的高度.");
            }
            y = stage.stageHeight - height;
         }
         _messageBox.visible = true;
      }
      
      public function hideMessageBox(param1:UIPassiveEvent) : void
      {
         _messageBox.visible = false;
         if(getChildByName(_messageBox_equipped.name))
         {
            removeChild(_messageBox_equipped);
         }
      }
      
      private function recycling(param1:MessageBox, param2:Number) : void
      {
         if(param1.height <= param2)
         {
            return;
         }
         param1.boxWidth += 9;
         setEquippedBoxPosition();
         recycling(param1,param2);
      }
      
      private function setEquippedBoxPosition() : void
      {
         _messageBox_equipped.x = _messageBox.width + 2;
      }
      
      private function scrollFunction(param1:EquipmentVO) : String
      {
         var _loc2_:AbleEquipmentVO = null;
         var _loc3_:StackEquipmentVO = null;
         var _loc4_:String = "";
         switch(param1.equipmentType)
         {
            case "weapon":
            case "clothes":
            case "gourd":
            case "necklace":
               _loc2_ = param1 as AbleEquipmentVO;
               switch(_loc2_.equipmentType)
               {
                  case "weapon":
                     switch(_loc2_.owner)
                     {
                        case "SunWuKong":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("孙悟空武器",16);
                           break;
                        case "BaiLongMa":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("白龙马武器",16);
                           break;
                        case "ErLangShen":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("二郎神武器",16);
                           break;
                        case "ChangE":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("嫦娥武器",16);
                           break;
                        case "Fox":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("灵狐武器",16);
                           break;
                        case "TieShan":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("铁扇公主武器",16);
                           break;
                        case "ZiXia":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("紫霞仙子武器",16);
                           break;
                        default:
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("武器",16);
                     }
                     _loc4_ += "<br><br>";
                     _loc4_ = _loc4_ + MessageBoxFunction.getInstance().toHTMLText("物品属性：",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + (MessageBoxFunction.getInstance().toHTMLText("攻击力：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc2_ as WeaponEquipmentVO).minAttack + "-" + (_loc2_ as WeaponEquipmentVO).maxAttack,16));
                     break;
                  case "clothes":
                     switch(_loc2_.owner)
                     {
                        case "SunWuKong":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("孙悟空套装",16);
                           break;
                        case "BaiLongMa":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("白龙马套装",16);
                           break;
                        case "ErLangShen":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("二郎神套装",16);
                           break;
                        case "ChangE":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("嫦娥套装",16);
                           break;
                        case "Fox":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("灵狐套装",16);
                           break;
                        case "TieShan":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("铁扇公主套装",16);
                           break;
                        case "ZiXia":
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("紫霞仙子套装",16);
                           break;
                        default:
                           _loc4_ += MessageBoxFunction.getInstance().toHTMLText("套装",16);
                     }
                     _loc4_ += "<br>";
                     _loc4_ = _loc4_ + MessageBoxFunction.getInstance().toHTMLText("物品属性：",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + (MessageBoxFunction.getInstance().toHTMLText("防御力：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc2_ as ClothesEquipmentVO).minDefence + "-" + (_loc2_ as ClothesEquipmentVO).maxDefence,16));
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + ((_loc2_ as ClothesEquipmentVO).maxRiot ? MessageBoxFunction.getInstance().toHTMLText("防暴值：",16) + MessageBoxFunction.getInstance().toHTMLText(((_loc2_ as ClothesEquipmentVO).minRiot * 100).toFixed() + "%" + "-" + ((_loc2_ as ClothesEquipmentVO).maxRiot * 100).toFixed() + "%",16) : "");
                     break;
                  case "necklace":
                     _loc4_ += MessageBoxFunction.getInstance().toHTMLText("项链",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + MessageBoxFunction.getInstance().toHTMLText("物品属性：",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + (MessageBoxFunction.getInstance().toHTMLText("暴击率：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc2_ as NecklaceEquipmentVO).minCriticalRate + "-" + (_loc2_ as NecklaceEquipmentVO).maxCriticalRate,16));
                     break;
                  case "gourd":
                     _loc4_ += MessageBoxFunction.getInstance().toHTMLText("葫芦",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + MessageBoxFunction.getInstance().toHTMLText("物品属性：",16);
                     _loc4_ = _loc4_ + "<br>";
                     _loc4_ = _loc4_ + (MessageBoxFunction.getInstance().toHTMLText("魔法值：",16) + MessageBoxFunction.getInstance().toHTMLText((_loc2_ as GourdEquipmentVO).minMaxMagic + "-" + (_loc2_ as GourdEquipmentVO).maxMaxMagic,16));
                     break;
                  default:
                     throw new Error("物品类型错误");
               }
               _loc4_ += "<br>";
               _loc4_ = _loc4_ + (MessageBoxFunction.getInstance().toHTMLText("人品：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc2_.minRenPin + "-" + _loc2_.maxRenPin,16));
               break;
            case "material":
               _loc3_ = param1 as StackEquipmentVO;
               _loc4_ += MessageBoxFunction.getInstance().toHTMLText("材料",16);
         }
         return _loc4_;
      }
      
      private function scrollFunction2(param1:Vector.<EquipmentVO>, param2:Vector.<int>) : String
      {
         var _loc5_:int = 0;
         var _loc3_:String = "";
         _loc3_ += MessageBoxFunction.getInstance().toHTMLText("需要材料：",16);
         var _loc4_:int = int(param1.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ += "<br>";
            _loc3_ += MessageBoxFunction.getInstance().toHTMLText(param1[_loc5_].name,16) + MessageBoxFunction.getInstance().toHTMLText("×",16) + MessageBoxFunction.getInstance().toHTMLText(param2[_loc5_].toString(),16);
            _loc5_++;
         }
         return _loc3_;
      }
      
      public function returnAttributeNameByAttribute(param1:String, param2:Number) : Array
      {
         var _loc3_:* = null;
         var _loc4_:Array = [];
         switch(param1)
         {
            case "renPin":
            case "renPin_foreverFashionAdd":
            case "renPin_MagicAdd":
               _loc4_.push("人品");
               _loc4_.push(param2.toFixed(0));
               break;
            case "blood_FashionAdd":
            case "blood_MagicAdd":
               _loc4_.push("生命");
               _loc4_.push(param2.toFixed(0));
               break;
            case "magic_FashionAdd":
            case "magic_MagicAdd":
               _loc4_.push("魔法");
               _loc4_.push(param2.toFixed(0));
               break;
            case "regmp_FashionAdd":
            case "regmp_MagicAdd":
               _loc4_.push("回蓝");
               _loc4_.push(param2.toFixed(0));
               break;
            case "reghp_FashionAdd":
            case "reghp_MagicAdd":
               _loc4_.push("回血");
               _loc4_.push(param2.toFixed(0));
               break;
            case "attack_FashionAdd":
            case "attack_MagicAdd":
               _loc4_.push("攻击");
               _loc4_.push(param2.toFixed(0));
               break;
            case "defence_FashionAdd":
            case "defence_MagicAdd":
               _loc4_.push("防御");
               _loc4_.push(param2.toFixed(0));
               break;
            case "hit_FashionAdd":
            case "hit_MagicAdd":
               _loc4_.push("命中");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "dodge_FashionAdd":
            case "dodge_MagicAdd":
               _loc4_.push("闪避");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "criticalRate_FashionAdd":
            case "criticalRate_MagicAdd":
               _loc4_.push("暴击");
               _loc4_.push(param2.toFixed(0) + "%");
               break;
            case "viotValue_FashionAdd":
            case "viotValue_MagicAdd":
               _loc4_.push("防爆");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "offensiveValue_FashionAdd":
            case "offensiveValue_MagicAdd":
               _loc4_.push("先手");
               _loc4_.push((param2 / 10).toFixed(2) + "%");
               break;
            case "attack":
               _loc4_.push("攻击力");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherAttack":
               _loc4_.push("攻击力");
               _loc4_.push(param2.toFixed(0));
               break;
            case "defence":
               _loc4_.push("防御力");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherDefence":
               _loc4_.push("防御力");
               _loc4_.push(param2.toFixed(0));
               break;
            case "bloodVolume":
               _loc4_.push("血量值");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherBloodVolume":
               _loc4_.push("血量值");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherMaxMagic":
               _loc4_.push("魔法值");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherCriticalRate":
               _loc4_.push("暴击率");
               _loc4_.push(param2.toFixed(0) + "%");
               break;
            case "otherRiotValue":
               _loc4_.push("防暴值");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "otherDodge":
               _loc4_.push("闪避");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "otherHit":
               _loc4_.push("命中");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "otherDodge":
               _loc4_.push("闪避");
               _loc4_.push((param2 * 100).toFixed(0) + "%");
               break;
            case "otherRegHp":
               _loc4_.push("回血");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherRegMp":
               _loc4_.push("回蓝");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherRenPin":
               _loc4_.push("人品");
               _loc4_.push(param2.toFixed(0));
               break;
            case "otherOffensiveValue":
               _loc4_.push("先手");
               _loc4_.push((param2 / 10).toFixed(2) + "%");
               break;
            case "addrenpin":
               _loc4_.push("人品增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addmagic":
               _loc4_.push("魔法增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addhp":
               _loc4_.push("血量增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addfangbao":
               _loc4_.push("防爆增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addshanbi":
               _loc4_.push("闪避增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addbaoji":
               _loc4_.push("暴击增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addmingzhong":
               _loc4_.push("命中增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "addgongji":
               _loc4_.push("攻击增加");
               _loc4_.push(param2.toFixed(2) + "%");
               break;
            case "downHurt":
               _loc4_.push("血量降");
               _loc4_.push(param2.toFixed(2) + "%");
         }
         return _loc4_;
      }
      
      public function getAttrName(param1:String) : String
      {
         var _loc2_:String = null;
         if(param1 == "addrenpin")
         {
            _loc2_ = "人品增加";
         }
         else if(param1 == "addmagic")
         {
            _loc2_ = "魔法增加";
         }
         else if(param1 == "addhp")
         {
            _loc2_ = "血量增加";
         }
         else if(param1 == "addfangbao")
         {
            _loc2_ = "防爆增加";
         }
         else if(param1 == "addshanbi")
         {
            _loc2_ = "闪避增加";
         }
         else if(param1 == "addbaoji")
         {
            _loc2_ = "暴击增加";
         }
         else if(param1 == "addmingzhong")
         {
            _loc2_ = "命中增加";
         }
         else if(param1 == "addgongji")
         {
            _loc2_ = "攻击增加";
         }
         else if(param1 == "doubleexpgold")
         {
            _loc2_ = "获得的经验和金钱的倍数";
         }
         else if(param1 == "increaseAttack")
         {
            _loc2_ = "每100个魔法点增加攻击点";
         }
         else if(param1 == "increaseShanbi")
         {
            _loc2_ = "每1000个防御点增加闪避点";
         }
         else if(param1 == "dincreasehp")
         {
            _loc2_ = "每次普攻和释放技能降低血量的百分比";
         }
         else if(param1 == "dincreaseDef")
         {
            _loc2_ = "防御力降低的百分比";
         }
         else if(param1 == "increaseBaoji")
         {
            _loc2_ = "每1000个攻击点增加的暴击点";
         }
         else if(param1 == "changerenpin")
         {
            _loc2_ = "人品全部转为防御力";
         }
         else if(param1 == "increaseMingzhong")
         {
            _loc2_ = "血量每降低10%增加的命中点";
         }
         return _loc2_;
      }
      
      public function getInfoEquipmentDes(param1:EquipmentVO) : String
      {
         var _loc12_:int = 0;
         var _loc17_:* = undefined;
         var _loc22_:* = undefined;
         var _loc3_:String = null;
         var _loc6_:String = null;
         var _loc30_:String = null;
         var _loc34_:Array = null;
         var _loc10_:String = null;
         var _loc31_:String = null;
         var _loc7_:Array = null;
         var _loc13_:String = null;
         var _loc32_:String = null;
         var _loc4_:Array = null;
         var _loc20_:String = null;
         var _loc19_:String = null;
         var _loc36_:int = 0;
         var _loc11_:Array = null;
         var _loc21_:* = undefined;
         var _loc25_:Number = NaN;
         var _loc8_:* = null;
         var _loc35_:String = null;
         var _loc33_:String = null;
         var _loc14_:Array = null;
         var _loc27_:Array = null;
         var _loc16_:InsetGemEquipmentVO = null;
         var _loc15_:int = 0;
         var _loc9_:* = "";
         var _loc29_:String = "";
         var _loc5_:String = "";
         var _loc26_:String = "";
         var _loc18_:String = "";
         var _loc2_:String = "";
         var _loc23_:String = "";
         var _loc28_:String = "";
         var _loc24_:int = 0;
         if(param1 == null)
         {
            return "";
         }
         _loc5_ += "\n------";
         _loc5_ = _loc5_ + ("\n" + param1.name + "+" + param1.level + " ");
         if(param1.equipmentType == "clothes")
         {
            _loc3_ = (param1 as ClothesEquipmentVO).defence.toString();
            _loc6_ = ((param1 as ClothesEquipmentVO).riot * 100).toFixed() + "%";
            _loc30_ = (param1 as ClothesEquipmentVO).rengPin.toString();
            _loc9_ = "";
            _loc17_ = (param1 as ClothesEquipmentVO).addPlayerSaveAttr;
            _loc22_ = (param1 as ClothesEquipmentVO).addPlayerSaveAttrVals;
            _loc12_ = int(_loc17_.length);
            _loc24_ = 0;
            while(_loc24_ < _loc12_)
            {
               _loc34_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
               _loc9_ += "\n" + _loc34_[0] + "：" + _loc34_[1];
               _loc34_[0] = null;
               _loc34_[1] = null;
               _loc34_ = null;
               _loc24_++;
            }
         }
         else if(param1.equipmentType == "gourd")
         {
            _loc10_ = (param1 as GourdEquipmentVO).maxMagic.toString();
            _loc31_ = (param1 as GourdEquipmentVO).rengPin.toString();
            _loc9_ = "";
            _loc17_ = (param1 as GourdEquipmentVO).addPlayerSaveAttr;
            _loc22_ = (param1 as GourdEquipmentVO).addPlayerSaveAttrVals;
            _loc12_ = int(_loc17_.length);
            _loc24_ = 0;
            while(_loc24_ < _loc12_)
            {
               _loc7_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
               _loc9_ += "\n" + _loc7_[0] + "：" + _loc7_[1];
               _loc7_[0] = null;
               _loc7_[1] = null;
               _loc7_ = null;
               _loc24_++;
            }
         }
         else if(param1.equipmentType == "necklace")
         {
            _loc13_ = (param1 as NecklaceEquipmentVO).criticalRate.toString();
            _loc32_ = (param1 as NecklaceEquipmentVO).rengPin.toString();
            _loc9_ = "";
            _loc17_ = (param1 as NecklaceEquipmentVO).addPlayerSaveAttr;
            _loc22_ = (param1 as NecklaceEquipmentVO).addPlayerSaveAttrVals;
            _loc12_ = int(_loc17_.length);
            _loc24_ = 0;
            while(_loc24_ < _loc12_)
            {
               _loc4_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
               _loc9_ += "\n" + _loc4_[0] + "：" + _loc4_[1];
               _loc4_[0] = null;
               _loc4_[1] = null;
               _loc4_ = null;
               _loc24_++;
            }
         }
         else if(param1.equipmentType == "precious")
         {
            _loc36_ = 15;
            _loc5_ += "\n";
            if(messageBoxMode != 3 && messageBoxMode != 7)
            {
               _loc17_ = (param1 as PreciousEquipmentVO).basisAttr;
               _loc22_ = (param1 as PreciousEquipmentVO).basisAttrValue;
               _loc21_ = (param1 as PreciousEquipmentVO).basisUpValue;
               _loc12_ = int(_loc17_.length);
               _loc24_ = 0;
               while(_loc24_ < _loc12_)
               {
                  if((param1 as PreciousEquipmentVO).level > 0)
                  {
                     _loc11_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_] + _loc21_[_loc24_]);
                     _loc25_ = _loc21_[_loc24_] / (param1 as PreciousEquipmentVO).level;
                  }
                  else
                  {
                     _loc11_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
                     _loc25_ = _loc21_[_loc24_];
                  }
                  _loc5_ += "\n";
                  _loc20_ = _loc11_[0];
                  _loc5_ += _loc20_;
                  _loc19_ = _loc11_[1];
                  _loc5_ += _loc19_;
                  if((param1 as PreciousEquipmentVO).level < 9)
                  {
                     _loc20_ = " 成长:";
                     _loc5_ += _loc20_;
                     _loc19_ = String(_loc25_.toFixed(2)) + "\n";
                     _loc5_ += _loc19_;
                  }
                  _loc11_[0] = null;
                  _loc11_[1] = null;
                  _loc11_ = null;
                  _loc24_++;
               }
               if(_loc12_ > 0)
               {
                  _loc5_ += "\n-----\n";
               }
               _loc9_ = "";
               _loc17_ = (param1 as PreciousEquipmentVO).addPlayerSaveAttr;
               _loc22_ = (param1 as PreciousEquipmentVO).addPlayerSaveAttrVals;
               _loc12_ = int(_loc17_.length);
               _loc24_ = 0;
               while(_loc24_ < _loc12_)
               {
                  _loc11_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
                  _loc9_ += "\n" + _loc11_[0] + "：" + _loc11_[1];
                  _loc11_[0] = null;
                  _loc11_[1] = null;
                  _loc11_ = null;
                  _loc24_++;
               }
               if(_loc12_ > 0)
               {
                  _loc9_ = _loc9_;
                  _loc9_ = _loc9_ + "\n-----\n";
               }
               _loc5_ += _loc9_;
               _loc17_ = (param1 as PreciousEquipmentVO).sAttrName;
               _loc22_ = (param1 as PreciousEquipmentVO).sAttrValue;
               _loc21_ = (param1 as PreciousEquipmentVO).sAvgValue;
               _loc12_ = int(_loc17_.length);
               _loc5_ += "\n特殊属性";
               _loc24_ = 0;
               while(_loc24_ < _loc12_)
               {
                  if(_loc17_[_loc24_] == "increaseAttack")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "每";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc21_[_loc24_]));
                     _loc5_ += _loc19_;
                     _loc20_ = "个魔法点增加";
                     _loc5_ += _loc20_;
                     _loc19_ = String(_loc22_[_loc24_].toFixed(2));
                     _loc5_ += _loc19_;
                     _loc20_ = "个攻击点";
                     _loc5_ += _loc20_;
                  }
                  else if(_loc17_[_loc24_] == "increaseShanbi")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "每";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc21_[_loc24_]));
                     _loc5_ += _loc19_;
                     _loc20_ = "个防御点增加";
                     _loc5_ += _loc20_;
                     _loc19_ = String(_loc22_[_loc24_].toFixed(2));
                     _loc5_ += _loc19_;
                     _loc20_ = "个闪避点";
                     _loc5_ += _loc20_;
                  }
                  else if(_loc17_[_loc24_] == "dincreasehp")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "每发动普攻或者技能时降低百分之";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc22_[_loc24_]));
                     _loc5_ += _loc19_;
                     _loc20_ = "的血量";
                     _loc5_ += _loc20_;
                  }
                  else if(_loc17_[_loc24_] == "dincreaseDef")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "防御降低";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc22_[_loc24_])) + "%";
                     _loc5_ += _loc19_;
                  }
                  else if(_loc17_[_loc24_] == "increaseBaoji")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "每";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc21_[_loc24_]));
                     _loc5_ += _loc19_;
                     _loc20_ = "个攻击点增加";
                     _loc5_ += _loc20_;
                     _loc19_ = String(_loc22_[_loc24_].toFixed(2));
                     _loc5_ += _loc19_;
                     _loc20_ = "个暴击点";
                     _loc5_ += _loc20_;
                  }
                  else if(_loc17_[_loc24_] == "increaseMingzhong")
                  {
                     _loc5_ += "\n";
                     _loc20_ = "血量每降低";
                     _loc5_ += _loc20_;
                     _loc19_ = String(int(_loc21_[_loc24_]));
                     _loc5_ += _loc19_;
                     _loc20_ = "%增加";
                     _loc5_ += _loc20_;
                     _loc19_ = String(_loc22_[_loc24_].toFixed(4));
                     _loc5_ += _loc19_;
                     _loc20_ = "个命中点";
                     _loc5_ += _loc20_;
                  }
                  _loc24_++;
               }
            }
         }
         else if(param1.equipmentType == "weapon")
         {
            _loc35_ = (param1 as WeaponEquipmentVO).attack.toString();
            _loc33_ = (param1 as WeaponEquipmentVO).rengPin.toString();
            _loc9_ = "";
            _loc17_ = (param1 as WeaponEquipmentVO).addPlayerSaveAttr;
            _loc22_ = (param1 as WeaponEquipmentVO).addPlayerSaveAttrVals;
            _loc12_ = int(_loc17_.length);
            _loc24_ = 0;
            while(_loc24_ < _loc12_)
            {
               _loc14_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
               _loc9_ += "\n" + _loc14_[0] + "：" + _loc14_[1];
               _loc14_[0] = null;
               _loc14_[1] = null;
               _loc14_ = null;
               _loc24_++;
            }
            if(_loc12_ > 0)
            {
               _loc9_ += "\n-----\n";
            }
            if(messageBoxMode == 1 || messageBoxMode == 3)
            {
               _loc5_ += "\n攻击力：" + (param1 as WeaponEquipmentVO).minAttack + "-" + (param1 as WeaponEquipmentVO).maxAttack + "\n" + "人品值：" + (param1 as WeaponEquipmentVO).minRenPin + "-" + (param1 as WeaponEquipmentVO).maxRenPin + "\n";
            }
            else if(messageBoxMode == 2)
            {
               _loc5_ += "：武器\n攻击力：" + _loc35_ + "\n" + "人品值：" + (param1 as WeaponEquipmentVO).minRenPin + "-" + (param1 as WeaponEquipmentVO).maxRenPin + "\n";
            }
            else
            {
               _loc5_ += "：武器\n攻击力：" + _loc35_ + "\n" + "人品值：" + _loc33_ + "\n";
            }
         }
         else if(param1.equipmentType == "forever_fashion")
         {
            _loc17_ = (param1 as ForeverFashionEquipmentVO).addPlayerAttributes;
            _loc22_ = (param1 as ForeverFashionEquipmentVO).addPlayerAttributeValues;
            _loc5_ += "";
            _loc12_ = int(_loc17_.length);
            _loc24_ = 0;
            while(_loc24_ < _loc12_)
            {
               _loc27_ = returnAttributeNameByAttribute(_loc17_[_loc24_],_loc22_[_loc24_]);
               _loc5_ += "\n" + _loc27_[0] + "：" + _loc27_[1];
               _loc27_[0] = null;
               _loc27_[1] = null;
               _loc27_ = null;
               _loc24_++;
            }
            _loc5_ += "\n期限:永久\n";
         }
         if((param1 as AbleEquipmentVO).maxHoleNum)
         {
            if((param1 as AbleEquipmentVO).getHoleNum())
            {
               _loc5_ += "宝石:";
               _loc12_ = (param1 as AbleEquipmentVO).getHoleNum();
               _loc15_ = 0;
               while(_loc15_ < _loc12_)
               {
                  _loc16_ = (param1 as AbleEquipmentVO).getInsetGem(_loc15_);
                  if(_loc16_)
                  {
                     _loc5_ += _loc16_.level + ",";
                  }
                  _loc15_++;
               }
               _loc5_ += "\n";
            }
         }
         return _loc5_.substr(0,65);
      }
   }
}

