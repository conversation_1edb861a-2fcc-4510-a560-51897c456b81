package UI.AutomaticPetPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class AddDevourAndShowBtn extends ButtonLogicShell2
   {
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_addAndPetShowMC:MovieClipPlayLogicShell;
      
      private var m_pictureContainerMC:MovieClipPlayLogicShell;
      
      public function AddDevourAndShowBtn()
      {
         super();
         m_addAndPetShowMC = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         m_automaticPetVO = null;
         ClearUtil.clearObject(m_addAndPetShowMC);
         m_addAndPetShowMC = null;
         ClearUtil.clearObject(m_pictureContainerMC);
         m_pictureContainerMC = null;
         super.clear();
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
         initShow2();
      }
      
      public function setAutomaticPetVO(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         initShow2();
      }
      
      public function getAutomaticPetVO() : AutomaticPetVO
      {
         return m_automaticPetVO;
      }
      
      private function initShow() : void
      {
         m_addAndPetShowMC.setShow(m_show["show"]);
      }
      
      private function initShow2() : void
      {
         ClearUtil.clearObject(m_pictureContainerMC);
         m_pictureContainerMC = null;
         if(m_automaticPetVO)
         {
            m_addAndPetShowMC.gotoAndStop("2");
            m_pictureContainerMC = new MovieClipPlayLogicShell();
            m_pictureContainerMC.setShow(m_addAndPetShowMC.getShow()["petShow"]);
            m_pictureContainerMC.gotoAndStop(m_automaticPetVO.getId());
         }
         else
         {
            m_addAndPetShowMC.gotoAndStop("1");
         }
      }
   }
}

