package YJFY.Skill.HouyiSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TimeLimitEntity.SunSkillDownEntity;
   import YJFY.Entity.TimeLimitEntity.SunSkillEntity;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.MonkeySkills.Skill_MonkeySkill5;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_HouyiSkill5 extends Skill_MonkeySkill5
   {
      
      private var m_createImagesList:Vector.<SunSkillEntity>;
      
      private var m_createImagesList2:Vector.<SunSkillDownEntity>;
      
      public function Skill_HouyiSkill5()
      {
         super();
         m_createImagesList = new Vector.<SunSkillEntity>();
         m_createImagesList2 = new Vector.<SunSkillDownEntity>();
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(m_createImagesList)
         {
            _loc1_ = int(m_createImagesList.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_createImagesList[_loc2_]);
               }
               ClearUtil.clearObject(m_createImagesList[_loc2_]);
               m_createImagesList[_loc2_] = null;
               _loc2_++;
            }
            m_createImagesList.length = 0;
            m_createImagesList = null;
         }
         if(m_createImagesList2)
         {
            _loc1_ = int(m_createImagesList2.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_createImagesList2[_loc2_]);
               }
               ClearUtil.clearObject(m_createImagesList2[_loc2_]);
               m_createImagesList2[_loc2_] = null;
               _loc2_++;
            }
            m_createImagesList2.length = 0;
            m_createImagesList2 = null;
         }
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
      }
      
      override protected function createImages() : void
      {
         var _loc2_:SunSkillEntity = null;
         var _loc1_:SunSkillDownEntity = null;
         _loc2_ = new SunSkillEntity();
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.setDisappearTime(m_imageDurationTime,"^stop^");
         _loc2_.initByXML(m_imageXML);
         _loc2_.setNewPosition(m_world.getCamera().getScreenX(),0,0);
         _loc2_.attack();
         m_world.addEntity(_loc2_);
         m_createImagesList.push(_loc2_);
         _loc2_.addAnimalEntityListener(m_animalEntityListener);
         _loc1_ = new SunSkillDownEntity();
         _loc1_.setOwner(m_owner);
         _loc1_.setMyLoader(m_myLoader);
         _loc1_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc1_.setSoundManager(m_world.getSoundManager());
         _loc1_.initByXML(m_skillXML.image[1]);
         _loc1_.setNewPosition(m_world.getCamera().getScreenX(),-m_world.getCamera().getScreenY() * 2,0);
         _loc1_.attack();
         m_world.addEntity(_loc1_);
         m_createImagesList2.push(_loc1_);
      }
      
      override protected function listeneredImageAttackSuccess(param1:IEntity, param2:AttackSkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as SunSkillEntity).setAttackData(m_attackData);
      }
   }
}

