package UI.RecaptureGold.Props
{
   import UI.MyFunction;
   import UI.RecaptureGold.Hook.Hook;
   import UI.RecaptureGold.Parent.Prop;
   import flash.utils.getQualifiedClassName;
   
   public class Prop_LuckGrass extends Prop implements IOneGameLevelProp
   {
      
      public function Prop_LuckGrass()
      {
         super();
      }
      
      public function getPromoteValueFromHook(param1:XML, param2:Hook) : Number
      {
         var _loc3_:String = null;
         var _loc6_:XML = null;
         var _loc5_:Number = NaN;
         var _loc7_:String = null;
         var _loc4_:* = undefined;
         if(param2.hookItem)
         {
            _loc3_ = getQualifiedClassName(this);
            _loc6_ = param1.PropData[0].item.(@classNameForShow == _loc3_)[0];
            _loc5_ = 1;
            _loc7_ = getQualifiedClassName(param2.hookItem);
            _loc4_ = MyFunction.getInstance().excreteStringToString(_loc7_);
            if(_loc4_[0] == String(_loc6_.@target))
            {
               _loc5_ = Number(_loc6_.@promoteValue);
            }
         }
         return _loc5_;
      }
   }
}

