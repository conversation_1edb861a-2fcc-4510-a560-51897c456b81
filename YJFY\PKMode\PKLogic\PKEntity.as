package YJFY.PKMode.PKLogic
{
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.GameEntityListener;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   
   public class PKEntity implements IPKEntity
   {
      
      private var m_gameEntityListener:GameEntityListener;
      
      private var m_gameEntity:GameEntity;
      
      public function PKEntity()
      {
         super();
         m_gameEntityListener = new GameEntityListener();
         m_gameEntityListener.attackSuccessFun = attackSuccess;
         m_gameEntityListener.beAttackFun = beAttack;
      }
      
      public function clear() : void
      {
         if(m_gameEntity)
         {
            m_gameEntity.removeGameEntityListener(m_gameEntityListener);
         }
         ClearUtil.clearObject(m_gameEntityListener);
         m_gameEntityListener = null;
         ClearUtil.clearObject(m_gameEntity);
         m_gameEntity = null;
      }
      
      public function setEntity(param1:GameEntity) : void
      {
         if(m_gameEntity)
         {
            m_gameEntity.removeGameEntityListener(m_gameEntityListener);
         }
         m_gameEntity = param1;
         if(m_gameEntity)
         {
            m_gameEntity.addGameEntityListener(m_gameEntityListener);
         }
      }
      
      public function render() : void
      {
      }
      
      public function getAnimalEntity() : IAnimalEntity
      {
         return m_gameEntity.getAnimalEntity();
      }
      
      public function getAnimalEntity_control() : IAnimalEntity
      {
         return m_gameEntity.getAnimalEntity_control();
      }
      
      private function attackSuccess(param1:GameEntity, param2:IEntity, param3:ISkill, param4:IEntity) : void
      {
      }
      
      private function beAttack(param1:GameEntity, param2:IEntity, param3:AttackData, param4:ISkill, param5:IEntity) : void
      {
      }
   }
}

