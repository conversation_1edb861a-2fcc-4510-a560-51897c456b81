package UI.OnLineGiftBag
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class OnLineGiftBagSmallPanel
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_bg:MovieClipPlayLogicShell;
      
      private var m_eqContainers:Vector.<Sprite>;
      
      private var m_getGiftBagBtn:ButtonLogicShell2;
      
      private var m_timeShow:Sprite;
      
      private var m_minuteShow:MultiPlaceNumLogicShell;
      
      private var m_secondShow:MultiPlaceNumLogicShell;
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      private var m_onLineGiftBagPanel:OnLineGiftBagPanel;
      
      private var m_isActive:Boolean;
      
      public function OnLineGiftBagSmallPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_bg);
         m_bg = null;
         ClearUtil.nullArr(m_eqContainers);
         m_eqContainers = null;
         ClearUtil.clearObject(m_getGiftBagBtn);
         m_getGiftBagBtn = null;
         m_timeShow = null;
         ClearUtil.clearObject(m_minuteShow);
         m_minuteShow = null;
         ClearUtil.clearObject(m_secondShow);
         m_secondShow = null;
         ClearUtil.nullArr(m_equipmentVOs);
         m_equipmentVOs = null;
         m_onLineGiftBagPanel = null;
      }
      
      public function setOnLineGiftBagPanel(param1:OnLineGiftBagPanel) : void
      {
         m_onLineGiftBagPanel = param1;
      }
      
      public function setShow(param1:MovieClip, param2:int) : void
      {
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_showMC.gotoAndStop("" + (param2 + 1));
         m_bg = new MovieClipPlayLogicShell();
         m_bg.setShow(m_show["bg"]);
         ClearUtil.nullArr(m_eqContainers);
         m_eqContainers = new Vector.<Sprite>();
         m_eqContainers.push(m_show["eqContainer_1"]);
         m_eqContainers.push(m_show["eqContainer_2"]);
         m_eqContainers.push(m_show["eqContainer_3"]);
         m_eqContainers.push(m_show["eqContainer_4"]);
         m_getGiftBagBtn = new ButtonLogicShell2();
         m_getGiftBagBtn.setShow(m_show["getGiftBagBtn"]);
         m_getGiftBagBtn.setTipString("点击获取礼包");
         m_timeShow = m_show["timeShow"];
         m_minuteShow = new MultiPlaceNumLogicShell();
         m_minuteShow.setShow(m_show["timeShow"]["minute"]);
         m_secondShow = new MultiPlaceNumLogicShell();
         m_secondShow.setShow(m_show["timeShow"]["second"]);
         m_minuteShow.setIsShowZero(true);
         m_secondShow.setIsShowZero(true);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function activePanel() : void
      {
         m_bg.gotoAndStop("1");
         m_timeShow.visible = true;
         m_getGiftBagBtn.unLock();
         m_isActive = true;
      }
      
      public function unActivePanel() : void
      {
         m_bg.gotoAndStop("2");
         m_timeShow.visible = false;
         m_getGiftBagBtn.lock();
         m_isActive = false;
      }
      
      public function setquipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:Equipment = null;
         _loc2_ = int(m_eqContainers.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            ClearUtil.clearDisplayObjectInContainer(m_eqContainers[_loc4_]);
            _loc4_++;
         }
         m_equipmentVOs = param1;
         if(param1 == null)
         {
            return;
         }
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(_loc4_ < param1.length)
            {
               _loc3_ = MyFunction2.sheatheEquipmentShell(param1[_loc4_]);
               _loc3_.x = 0;
               _loc3_.y = 0;
               _loc3_.addEventListener("rollOver",onOver,false,0,true);
               _loc3_.addEventListener("rollOut",onOut,false,0,true);
               m_eqContainers[_loc4_].addChild(_loc3_);
            }
            _loc4_++;
         }
      }
      
      public function setRemainTime(param1:int) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(m_isActive == false)
         {
            return;
         }
         if(param1 == 0)
         {
            m_timeShow.visible = false;
            m_getGiftBagBtn.unLock();
         }
         else
         {
            m_timeShow.visible = true;
            m_getGiftBagBtn.lock();
            _loc3_ = Math.ceil(param1 / 1000);
            _loc2_ = _loc3_ / 60;
            _loc3_ -= _loc2_ * 60;
            m_minuteShow.showNum(_loc2_);
            m_secondShow.showNum(_loc3_);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      public function getGitBagBtn() : ButtonLogicShell2
      {
         return m_getGiftBagBtn;
      }
      
      public function getGiftBag() : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("addGifts",{
            "successFunction":m_onLineGiftBagPanel.successGetGiftBag,
            "successFunParams":[this],
            "failFunction":m_onLineGiftBagPanel.showWarningBox,
            "failFunParams":["背包空间不足，放不下礼包！",0],
            "giftBags":m_equipmentVOs
         }));
      }
   }
}

