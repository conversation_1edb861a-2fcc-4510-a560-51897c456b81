package UI.WorldBoss.DaZhao.ChangeHurtDaZhao
{
   public class RoundHurtMultiD<PERSON><PERSON><PERSON> extends ChangeHurtDa<PERSON>hao
   {
      
      public var hurtMulti:Number = 1;
      
      public var roundNum:uint;
      
      public function RoundHurtMultiDaZhao()
      {
         super();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         hurtMulti = Number(param1.@hurtMulti);
         roundNum = uint(param1.@roundNum);
      }
      
      override public function isRunDaZhao() : Boolean
      {
         var _loc1_:int = m_world.getGetNextStepActionEnities().getRoundNum();
         if(_loc1_ % roundNum == 0)
         {
            return true;
         }
         return false;
      }
      
      override public function changeHurt(param1:uint) : uint
      {
         return param1 * hurtMulti;
      }
   }
}

