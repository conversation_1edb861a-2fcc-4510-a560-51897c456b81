package UI.TextTrace
{
   import UI.GamingUI;
   
   public function traceText(... rest) : void
   {
      trace(rest);
      var _loc3_:int = 0;
      var _loc2_:int = int(rest.length);
      if(!GamingUI.getInstance().textTrace)
      {
         trace("输出板没初始化！为null");
         return;
      }
      _loc3_ = 0;
      for(; _loc3_ < _loc2_; _loc3_++)
      {
         if(!(rest[_loc3_] is String))
         {
            try
            {
               GamingUI.getInstance().textTrace.appendText(rest[_loc3_].toString() + "  ");
            }
            catch(error:Error)
            {
               GamingUI.getInstance().textTrace.appendText(typeof rest[_loc3_] + "  " + error.message + "  ");
            }
            continue;
         }
         GamingUI.getInstance().textTrace.appendText(rest[_loc3_] + "  ");
      }
      GamingUI.getInstance().textTrace.appendText("\n");
   }
}

