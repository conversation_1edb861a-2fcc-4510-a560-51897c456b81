package UI.RecaptureGold.UI
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.PointTicketBuyBox;
   import UI.RecaptureGold.RecaptureGoldData;
   import UI.RecaptureGold.RecaptureGoldMain;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class BuyRGNumBox extends PointTicketBuyBox
   {
      
      public var ticketText:TextField;
      
      private var _recaptureGoldXML:XML;
      
      private var _funAfterBuy:Function;
      
      private var _price:int;
      
      private var _ticketId:String;
      
      private var _oneBuyNum:int;
      
      private var _recaptureGoldMain:RecaptureGoldMain;
      
      public function BuyRGNumBox(param1:XML, param2:RecaptureGoldMain, param3:Function)
      {
         _recaptureGoldXML = param1;
         _recaptureGoldMain = param2;
         _funAfterBuy = param3;
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ticketText = null;
         _recaptureGoldXML = null;
         _funAfterBuy = null;
         _recaptureGoldMain = null;
      }
      
      override protected function init() : void
      {
         ticketText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,18,14946839);
         ticketText.embedFonts = true;
         _oneBuyNum = int(_recaptureGoldXML.OtherData[0].BuyNumData[0].@oneBuyNum);
         var _loc1_:Vector.<int> = MyFunction.getInstance().excreteString(_recaptureGoldXML.OtherData[0].BuyNumData[0].@buyTickets);
         var _loc2_:Vector.<String> = MyFunction.getInstance().excreteStringToString(_recaptureGoldXML.OtherData[0].BuyNumData[0].@buyTicketIds);
         _price = _loc1_[Math.min(RecaptureGoldData.getInstance().buyNum / _oneBuyNum,_loc1_.length - 1)];
         _ticketId = _loc2_[Math.min(RecaptureGoldData.getInstance().buyNum / _oneBuyNum,_loc2_.length - 1)];
         ticketText.text = _price.toString();
         super.init();
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         var dataObj:Object;
         var funAfterBuy:Function;
         var e:UIBtnEvent = param1;
         if(e.target == _sureBtn)
         {
            dataObj = {};
            dataObj["propId"] = _ticketId;
            dataObj["count"] = 1;
            dataObj["price"] = _price;
            dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
            dataObj["tag"] = "购买夺金";
            funAfterBuy = _funAfterBuy;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
            {
               if(param1["propId"] != dataObj["propId"])
               {
                  throw new Error("购买物品id前后端不相同！");
               }
               RecaptureGoldData.getInstance().buyNum = RecaptureGoldData.getInstance().buyNum + _oneBuyNum;
               if(Boolean(funAfterBuy))
               {
                  funAfterBuy();
               }
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },_recaptureGoldMain.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         super.clickBtn(e);
      }
   }
}

