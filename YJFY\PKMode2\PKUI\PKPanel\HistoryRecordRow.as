package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class HistoryRecordRow
   {
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_historyInforText:TextField;
      
      private var m_show:MovieClip;
      
      private var m_pkTargetPlayerData:PKTargetPlayerData;
      
      public function HistoryRecordRow()
      {
         super();
         m_lookUpBtn = new ButtonLogicShell2();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         m_historyInforText = null;
         m_show = null;
         m_pkTargetPlayerData = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_lookUpBtn.setShow(m_show["lookUpBtn"]);
         m_historyInforText = m_show["hitstoryInforText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_historyInforText);
      }
      
      public function setPKTargetPlayerData(param1:PKTargetPlayerData) : void
      {
         m_pkTargetPlayerData = param1;
         if(m_pkTargetPlayerData == null)
         {
            return;
         }
         if(m_pkTargetPlayerData.getPKState() == 2)
         {
            m_historyInforText.text = "我被" + m_pkTargetPlayerData.getUid() + "打败了";
         }
         else
         {
            if(m_pkTargetPlayerData.getPKState() != 1)
            {
               throw new Error("出错了pkTargetPlayerData.getPKState():" + m_pkTargetPlayerData.getPKState());
            }
            m_historyInforText.text = "我战胜了" + m_pkTargetPlayerData.getUid();
         }
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      public function getPKTargetPlayerData() : PKTargetPlayerData
      {
         return m_pkTargetPlayerData;
      }
   }
}

