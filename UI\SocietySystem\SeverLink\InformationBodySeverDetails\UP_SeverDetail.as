package UI.SocietySystem.SeverLink.InformationBodySeverDetails
{
   import flash.utils.ByteArray;
   
   public class UP_SeverDetail extends InformationBodySeverDetail
   {
      
      protected var m_checkValue:int;
      
      public function UP_SeverDetail()
      {
         super();
      }
      
      public function setCheckValue(param1:int) : void
      {
         m_checkValue = param1;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_checkValue);
         _loc1_.position = 0;
         return _loc1_;
      }
   }
}

