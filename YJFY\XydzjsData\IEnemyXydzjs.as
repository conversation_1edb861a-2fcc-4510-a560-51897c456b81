package YJFY.XydzjsData
{
   import YJFY.Entity.IAnimalEntity;
   
   public interface IEnemyXydzjs extends IAttackDataCalculateVOXydzjs
   {
      
      function getExpOfDieThisEnemey() : uint;
      
      function isInvincible() : Boolean;
      
      function getCurrentMp() : uint;
      
      function getTotalMp() : uint;
      
      function addMp(param1:uint) : void;
      
      function decMp(param1:uint) : void;
      
      function getAnimalEntity() : IAnimalEntity;
   }
}

