package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class CuboidAreaAttackSkill2_PiKaQiuSkill extends CuboidAreaAttackSkill2_EveryTargetAddShow2
   {
      
      protected var m_createRandomShowInterval:uint;
      
      protected var m_nextCreateRandomShowTime:Number;
      
      protected var m_randomEntityReachGroundFrameLabel:String;
      
      protected var m_randomEntityShowId:String;
      
      protected var m_randomEntityShowDefinitionData:AnimationDefinitionData;
      
      protected var m_useRandomEntityShows:Vector.<AnimationEntityLogicShell>;
      
      protected var m_wasteRandomEntityShows:Vector.<AnimationEntityLogicShell>;
      
      protected var m_randomEntityShowsPool:ObjectsPool;
      
      protected var m_randomEntityFrameLabelLietener:AnimationPlayFrameLabelListener;
      
      protected var m_addEffectEntityForRandomEntitysId:String;
      
      protected var m_addEffectEntityForRandomEntitysDefinitionData:AnimationDefinitionData;
      
      protected var m_useAddEffectEntityForRandomEntitys:Vector.<AnimationEntityLogicShell>;
      
      protected var m_wasteAddEffectEntityForRandomEntitys:Vector.<AnimationEntityLogicShell>;
      
      protected var m_addEffectEntityForRandomEntitysPool:ObjectsPool;
      
      protected var m_addGroundEffectOneRandomEntityId:String;
      
      protected var m_addGroundEffectOneRandomEntityDefinitionData:AnimationDefinitionData;
      
      protected var m_useAddGroundEffectOnRandomEntitys:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_wasteAddGroundEffectOnRandomEntitys:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_addGroundEffectOnRandomEntitysPool:ObjectsPool;
      
      protected var m_stopListener1:StopListener;
      
      protected var m_stopListener2:StopListener;
      
      protected var m_stopListener3:StopListener;
      
      public function CuboidAreaAttackSkill2_PiKaQiuSkill()
      {
         super();
         m_useRandomEntityShows = new Vector.<AnimationEntityLogicShell>();
         m_wasteRandomEntityShows = new Vector.<AnimationEntityLogicShell>();
         m_randomEntityShowsPool = new ObjectsPool(m_useRandomEntityShows,m_wasteRandomEntityShows,createOneRandomEntityShow,null);
         m_stopListener1 = new StopListener();
         m_stopListener1.stop2Fun = randomEntityStop1;
         m_stopListener2 = new StopListener();
         m_stopListener2.stop2Fun = randomEntityStop2;
         m_stopListener3 = new StopListener();
         m_stopListener3.stop2Fun = randomEntityStop3;
         m_randomEntityFrameLabelLietener = new AnimationPlayFrameLabelListener();
         m_randomEntityFrameLabelLietener.reachFrameLabelFun2 = randomEntityReachFrame;
         m_useAddEffectEntityForRandomEntitys = new Vector.<AnimationEntityLogicShell>();
         m_wasteAddEffectEntityForRandomEntitys = new Vector.<AnimationEntityLogicShell>();
         m_addEffectEntityForRandomEntitysPool = new ObjectsPool(m_useAddEffectEntityForRandomEntitys,m_wasteAddEffectEntityForRandomEntitys,createAddEffectEntityOnRandomEntity,null);
         m_useAddGroundEffectOnRandomEntitys = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteAddGroundEffectOnRandomEntitys = new Vector.<AnimationShowPlayLogicShell>();
         m_addGroundEffectOnRandomEntitysPool = new ObjectsPool(m_useAddGroundEffectOnRandomEntitys,m_wasteAddGroundEffectOnRandomEntitys,createAddGroundEffectOnRandomEntity,null);
      }
      
      override public function clear() : void
      {
         clearUseRandomEntityShow();
         clearWasteRandomEntityShow();
         clearAddEffectOnRandomEntity();
         clearAddGroundEffectOnRandomEntity();
         m_randomEntityReachGroundFrameLabel = null;
         m_randomEntityShowId = null;
         ClearUtil.clearObject(m_randomEntityShowDefinitionData);
         m_randomEntityShowDefinitionData = null;
         ClearUtil.clearObject(m_useRandomEntityShows);
         m_useRandomEntityShows = null;
         ClearUtil.clearObject(m_wasteRandomEntityShows);
         m_wasteRandomEntityShows = null;
         ClearUtil.clearObject(m_randomEntityShowsPool);
         m_randomEntityShowsPool = null;
         ClearUtil.clearObject(m_randomEntityFrameLabelLietener);
         m_randomEntityFrameLabelLietener = null;
         m_addEffectEntityForRandomEntitysId = null;
         ClearUtil.clearObject(m_addEffectEntityForRandomEntitysDefinitionData);
         m_addEffectEntityForRandomEntitysDefinitionData = null;
         ClearUtil.clearObject(m_useAddEffectEntityForRandomEntitys);
         m_useAddEffectEntityForRandomEntitys = null;
         ClearUtil.clearObject(m_wasteAddEffectEntityForRandomEntitys);
         m_wasteAddEffectEntityForRandomEntitys = null;
         ClearUtil.clearObject(m_addEffectEntityForRandomEntitysPool);
         m_addEffectEntityForRandomEntitysPool = null;
         m_addGroundEffectOneRandomEntityId = null;
         ClearUtil.clearObject(m_addGroundEffectOneRandomEntityDefinitionData);
         m_addGroundEffectOneRandomEntityDefinitionData = null;
         ClearUtil.clearObject(m_useAddGroundEffectOnRandomEntitys);
         m_useAddGroundEffectOnRandomEntitys = null;
         ClearUtil.clearObject(m_wasteAddGroundEffectOnRandomEntitys);
         m_wasteAddGroundEffectOnRandomEntitys = null;
         ClearUtil.clearObject(m_addGroundEffectOnRandomEntitysPool);
         m_addGroundEffectOnRandomEntitysPool = null;
         ClearUtil.clearObject(m_stopListener1);
         m_stopListener1 = null;
         ClearUtil.clearObject(m_stopListener2);
         m_stopListener2 = null;
         ClearUtil.clearObject(m_stopListener3);
         m_stopListener3 = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_createRandomShowInterval = uint(param1.@createRandomShowInterval);
         m_randomEntityReachGroundFrameLabel = String(param1.@randomEntityReachGroundFrameLabel);
         m_randomEntityShowId = String(param1.@randomEntityShowId);
         m_randomEntityShowDefinitionData = new AnimationDefinitionData();
         m_randomEntityShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_randomEntityShowId)[0]);
         m_addEffectEntityForRandomEntitysId = String(param1.@addEffectOnRandomEntitysId);
         m_addEffectEntityForRandomEntitysDefinitionData = new AnimationDefinitionData();
         m_addEffectEntityForRandomEntitysDefinitionData.initByXML(param1.animationDefinition.(@id == m_addEffectEntityForRandomEntitysId)[0]);
         m_addGroundEffectOneRandomEntityId = String(param1.@addGroundEffectOneRandomEntityId);
         m_addGroundEffectOneRandomEntityDefinitionData = new AnimationDefinitionData();
         m_addGroundEffectOneRandomEntityDefinitionData.initByXML(param1.animationDefinition.(@id == m_addGroundEffectOneRandomEntityId)[0]);
      }
      
      override protected function attackReach(param1:World) : void
      {
         super.attackReach(param1);
         if(isNaN(m_nextCreateRandomShowTime))
         {
            m_nextCreateRandomShowTime = m_world.getWorldTime();
         }
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun == false)
         {
            return;
         }
         createRandomEntityShow();
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         m_nextCreateRandomShowTime = NaN;
      }
      
      protected function randomEntityReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         if(!m_world)
         {
            return;
         }
         var _loc5_:* = param2;
         if(m_randomEntityReachGroundFrameLabel === _loc5_)
         {
            _loc3_ = int(m_useRandomEntityShows.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_useRandomEntityShows[_loc4_].getAniamtionShowPlay() == param1)
               {
                  addEffectForRandomEntity(m_useRandomEntityShows[_loc4_]);
                  addGroundEffectForRandomEntity(m_useRandomEntityShows[_loc4_]);
                  break;
               }
               _loc4_++;
            }
         }
      }
      
      protected function createRandomEntityShow() : void
      {
         var _loc1_:AnimationEntityLogicShell = null;
         if(!isNaN(m_nextCreateRandomShowTime) && m_nextCreateRandomShowTime < m_world.getWorldTime())
         {
            _loc1_ = m_randomEntityShowsPool.getOneOrCreateOneObj() as AnimationEntityLogicShell;
            m_world.addEntity(_loc1_);
            _loc1_.getAniamtionShowPlay().gotoAndPlay("1");
            m_nextCreateRandomShowTime = m_world.getWorldTime() + m_createRandomShowInterval;
         }
      }
      
      protected function addEffectForRandomEntity(param1:AnimationEntityLogicShell) : void
      {
         var _loc2_:AnimationEntityLogicShell = m_addEffectEntityForRandomEntitysPool.getOneOrCreateOneObj() as AnimationEntityLogicShell;
         _loc2_.getAniamtionShowPlay().gotoAndPlay("1");
         _loc2_.init(param1.getX(),param1.getY(),0);
         m_world.addEntity(_loc2_);
      }
      
      protected function addGroundEffectForRandomEntity(param1:AnimationEntityLogicShell) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = m_addGroundEffectOnRandomEntitysPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
         _loc2_.gotoAndPlay("1");
         (_loc2_.getShow() as DisplayObject).x = param1.getScreenX();
         (_loc2_.getShow() as DisplayObject).y = param1.getScreenY();
         m_world.addAnimationInGround(_loc2_);
      }
      
      protected function randomEntityStop1(param1:AnimationShowPlayLogicShell) : void
      {
         var _loc5_:int = 0;
         var _loc3_:AnimationEntityLogicShell = null;
         var _loc2_:* = null;
         if(!m_world)
         {
            return;
         }
         var _loc4_:int = m_useRandomEntityShows ? m_useRandomEntityShows.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(m_useRandomEntityShows[_loc5_].getAniamtionShowPlay() == param1)
            {
               _loc3_ = m_useRandomEntityShows[_loc5_];
               m_world.removeEntity(_loc3_);
               m_randomEntityShowsPool.wasteOneObj(_loc3_);
               return;
            }
            _loc5_++;
         }
      }
      
      protected function randomEntityStop2(param1:AnimationShowPlayLogicShell) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:AnimationEntityLogicShell = null;
         _loc3_ = m_useAddEffectEntityForRandomEntitys ? m_useAddEffectEntityForRandomEntitys.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_useAddEffectEntityForRandomEntitys[_loc4_].getAniamtionShowPlay() == param1)
            {
               _loc2_ = m_useAddEffectEntityForRandomEntitys[_loc4_];
               if(m_world)
               {
                  m_world.removeEntity(_loc2_);
               }
               m_addEffectEntityForRandomEntitysPool.wasteOneObj(_loc2_);
               return;
            }
            _loc4_++;
         }
      }
      
      protected function randomEntityStop3(param1:AnimationShowPlayLogicShell) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = m_useAddGroundEffectOnRandomEntitys ? m_useAddGroundEffectOnRandomEntitys.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_useAddGroundEffectOnRandomEntitys[_loc3_] == param1)
            {
               if(m_world)
               {
                  m_world.removeAnimationInGround(m_useAddGroundEffectOnRandomEntitys[_loc3_]);
               }
               m_addGroundEffectOnRandomEntitysPool.wasteOneObj(m_useAddGroundEffectOnRandomEntitys[_loc3_]);
               return;
            }
            _loc3_++;
         }
      }
      
      protected function createOneRandomEntityShow() : AnimationEntityLogicShell
      {
         var _loc3_:IAnimationShow = new AnimationShow();
         (_loc3_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_randomEntityShowDefinitionData));
         var _loc1_:AnimationEntityLogicShell = new AnimationEntityLogicShell();
         _loc1_.setShow(_loc3_);
         _loc1_.getAniamtionShowPlay().addNextStopListener(m_stopListener1);
         _loc1_.getAniamtionShowPlay().addFrameLabelListener(m_randomEntityFrameLabelLietener);
         var _loc4_:Number = m_cuboidRangeToWorld.getMinX() + Math.random() * m_cuboidRangeToWorld.getXRange();
         var _loc2_:Number = m_cuboidRangeToWorld.getMinY() + Math.random() * m_cuboidRangeToWorld.getYRange();
         _loc1_.init(_loc4_,_loc2_,0);
         return _loc1_;
      }
      
      protected function clearUseRandomEntityShow() : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         var _loc1_:* = null;
         var _loc3_:int = m_useRandomEntityShows ? m_useRandomEntityShows.length : 0;
         while(_loc3_)
         {
            _loc2_ = m_useRandomEntityShows[0];
            m_useRandomEntityShows.splice(0,1);
            _loc3_--;
            if(m_world)
            {
               m_world.removeEntity(_loc2_);
            }
            _loc2_.getAniamtionShowPlay().removeNextStopListener(m_stopListener1);
            _loc2_.getAniamtionShowPlay().removeFrameLabelListener(m_randomEntityFrameLabelLietener);
            ClearUtil.clearObject(_loc2_.getAnimationShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_useRandomEntityShows = null;
      }
      
      protected function clearWasteRandomEntityShow() : void
      {
         var _loc1_:AnimationEntityLogicShell = null;
         var _loc2_:int = m_wasteRandomEntityShows ? m_wasteRandomEntityShows.length : 0;
         while(_loc2_)
         {
            _loc1_ = m_wasteRandomEntityShows[0];
            m_wasteRandomEntityShows.splice(0,1);
            _loc2_--;
            ClearUtil.clearObject(_loc1_.getAnimationShow());
            ClearUtil.clearObject(_loc1_);
            _loc1_ = null;
         }
      }
      
      protected function createAddEffectEntityOnRandomEntity() : AnimationEntityLogicShell
      {
         var _loc1_:IAnimationShow = new AnimationShow();
         (_loc1_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_addEffectEntityForRandomEntitysDefinitionData));
         var _loc2_:AnimationEntityLogicShell = new AnimationEntityLogicShell();
         _loc2_.getAniamtionShowPlay().addNextStopListener(m_stopListener2);
         _loc2_.setShow(_loc1_);
         return _loc2_;
      }
      
      protected function clearAddEffectOnRandomEntity() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_useAddEffectEntityForRandomEntitys ? m_useAddEffectEntityForRandomEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_useAddEffectEntityForRandomEntitys[_loc2_].getAniamtionShowPlay().removeNextStopListener(m_stopListener2);
            if(m_world)
            {
               m_world.removeEntity(m_useAddEffectEntityForRandomEntitys[_loc2_]);
            }
            ClearUtil.clearObject(m_useAddEffectEntityForRandomEntitys[_loc2_].getAnimationShow());
            ClearUtil.clearObject(m_useAddEffectEntityForRandomEntitys[_loc2_]);
            m_useAddEffectEntityForRandomEntitys[_loc2_] = null;
            _loc2_++;
         }
         m_useAddEffectEntityForRandomEntitys.length = 0;
         m_useAddEffectEntityForRandomEntitys = null;
         _loc1_ = m_wasteAddEffectEntityForRandomEntitys ? m_wasteAddEffectEntityForRandomEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_wasteAddEffectEntityForRandomEntitys[_loc2_].getAniamtionShowPlay().removeNextStopListener(m_stopListener2);
            ClearUtil.clearObject(m_wasteAddEffectEntityForRandomEntitys[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteAddEffectEntityForRandomEntitys[_loc2_]);
            m_wasteAddEffectEntityForRandomEntitys[_loc2_] = null;
            _loc2_++;
         }
         m_wasteAddEffectEntityForRandomEntitys.length = 0;
         m_wasteAddEffectEntityForRandomEntitys = null;
      }
      
      protected function createAddGroundEffectOnRandomEntity() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_addGroundEffectOneRandomEntityDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_stopListener3);
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      protected function clearAddGroundEffectOnRandomEntity() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_useAddGroundEffectOnRandomEntitys ? m_useAddGroundEffectOnRandomEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_world)
            {
               m_world.removeAnimationInGround(m_useAddGroundEffectOnRandomEntitys[_loc2_]);
            }
            m_useAddGroundEffectOnRandomEntitys[_loc2_].removeNextStopListener(m_stopListener3);
            ClearUtil.clearObject(m_useAddGroundEffectOnRandomEntitys[_loc2_].getShow());
            ClearUtil.clearObject(m_useAddGroundEffectOnRandomEntitys[_loc2_]);
            m_useAddGroundEffectOnRandomEntitys[_loc2_] = null;
            _loc2_++;
         }
         m_useAddGroundEffectOnRandomEntitys.length = 0;
         m_useAddGroundEffectOnRandomEntitys = null;
         _loc1_ = m_wasteAddGroundEffectOnRandomEntitys ? m_wasteAddGroundEffectOnRandomEntitys.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            ClearUtil.clearObject(m_wasteAddGroundEffectOnRandomEntitys[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteAddGroundEffectOnRandomEntitys[_loc2_]);
            m_wasteAddGroundEffectOnRandomEntitys[_loc2_] = null;
            _loc2_++;
         }
         m_wasteAddGroundEffectOnRandomEntitys.length = 0;
         m_wasteAddGroundEffectOnRandomEntitys = null;
      }
   }
}

