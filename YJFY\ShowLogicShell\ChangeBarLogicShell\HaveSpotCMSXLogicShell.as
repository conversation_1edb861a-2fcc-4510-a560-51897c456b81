package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   import flash.display.Sprite;
   
   public class HaveSpotCMSXLogicShell extends CMSXChangeBarLogicShell
   {
      
      protected var _spot:Sprite;
      
      protected var _spotMask:Sprite;
      
      public function HaveSpotCMSXLogicShell()
      {
         super();
      }
      
      override public function setShow(param1:Sprite, param2:Boolean = true) : void
      {
         super.setShow(param1,param2);
         if(_show.hasOwnProperty("spot"))
         {
            _spot = _show["spot"];
            _spotMask = _show["spotMask"];
            _spot.visible = false;
            _spot.mask = _spotMask;
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         _spot = null;
      }
      
      override public function change(param1:Number) : void
      {
         super.change(param1);
         if(_spot)
         {
            _spot.x = _mask.x + _mask.width;
            if(param1 == 0 || param1 == 1)
            {
               _spot.visible = false;
            }
            else
            {
               _spot.visible = true;
            }
         }
      }
   }
}

