package YJFY
{
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.BuffData;
   import UI.CollectTimePanel.CollectTimeSaveData;
   import UI.DetectionClass.DetectionClass;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.InitUI2;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.PKVO;
   import UI.Players.Player;
   import UI.Players.VipVO;
   import UI.SocietySystem.SocietyDataVO;
   import UI.Task.TaskFunction;
   import UI.VersionControl;
   import UI.WorldBoss.WorldBossSaveData;
   import UI.XMLSingle;
   import UI.XiangMoLevelPanel.XiangMoLevelSaveData;
   import UI.newTask.EveyDayTask.NewEveryDayData;
   import UI.newTask.NewActivityTask.MewActivityData;
   import UI.newTask.NewMainTask.NewMainTaskData;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.Consumer.ConsumerData;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.Mount.MountData.MountsVO;
   import UI2.NewPrecious.PreDataInfo;
   import UI2.NewRank.RankDataInfo;
   import UI2.SVActivity.Data.HuanLeZhuanPanData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SmallAssistant.ActiveTask.ActiveTasksSaveData;
   import UI2.SmallAssistant.SmallAssistantSaveData;
   import UI2.broadcast.BroadDataManager;
   import UI2.buchang.BuchangData;
   import UI2.firstPay.FirstPayData;
   import UI2.newSign.NewSignData;
   import UI2.tehui.TeHuiData;
   import UI2.weekpay.WeekPayData;
   import UI2.wuyi.WuyiData;
   import YJFY.API_4399.SaveAPI.SaveAPI;
   import YJFY.ActivityMode.ActivityManager;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.BossMode.BossUIAndData.BossSaveData;
   import YJFY.BossMode.PetBossUIAndData.PetBossSaveData;
   import YJFY.EndlessMode.EndlessLevelData;
   import YJFY.LevelMode1.DreamLand.DreamLandSaveData;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.LevelMode4.AnyeLevelData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.PKData.MyPKSaveData;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.net.URLLoader;
   import flash.system.System;
   import flash.utils.setTimeout;
   
   public class Part2 extends UI.MySprite
   {
      
      private var _xmls:Array = ["monkeyXML","dragonXML","erLangShenXML","changEXML","foxXML","tieShanXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","skillXML","talentXML","dataXML","privilegeXML","vipXML","taskXML","textXML","farmXML","buffXML","xiaoKongXML","xiaoBaXML","xiuLianContentXML","tuDiSkillXML","tuDiDataXML","subEquipmentXML","subEquipmentXML","onLineGiftBagXML","mainLineTaskGoalsXML","mainLineTaskXML","mainLineTaskListXML","signXML","automaticPetSkillsXML","automaticPetOtherDataXML","subEquipmentXML","activeTaskListXML","smallAssistantXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","subEquipmentXML","mountSkillsXML"
      ,"subEquipmentXML","subEquipmentXML","NewRank","subEquipmentXML","subEquipmentXML","ziXiaXML","subEquipmentXML","subEquipmentXML"];
      
      private var _xmlURLs:Array = ["player/monkey.xml","player/dragon.xml","player/erLangShen.xml","player/changE.xml","player/fox.xml","player/tieshan.xml","equipment/monkeyWeapon.xml","equipment/dragonWeapon.xml","equipment/dogWeapon.xml","equipment/rabbitWeapon.xml","equipment/necklace.xml","equipment/monkeyClothes.xml","equipment/dragonClothes.xml","equipment/dogClothes.xml","equipment/rabbitClothes.xml","equipment/gourd.xml","equipment/foreverFashion.xml","equipment/material.xml","equipment/fashion.xml","equipment/pet.xml","equipment/scroll.xml","equipment/egg.xml","equipment/potion.xml","equipment/buffEquipment.xml","equipment/pocket.xml","equipment/medal.xml","equipment/grass.xml","equipment/danMedicine.xml","equipment/forceDan.xml","skill.xml","talent.xml","data.xml","privilege.xml","vip.xml","task.xml","text.xml","farm.xml","buff.xml","tuDi/xiaoKong.xml","tuDi/xiaoBa.xml","xiuLianContent.xml","tuDi/tuDiSkill.xml","tuDi/tuDiData.xml","equipment/petSkillBook.xml","equipment/insetGem.xml"
      ,"onLineGiftBag.xml","mainLineTask/mainLineTaskGoals.xml","mainLineTask/mainLineTask.xml","mainLineTask/mainLineTaskList.xml","Sign.xml","automaticPet/automaticPetSkills.xml","automaticPet/automaticPetOtherData.xml","equipment/contract.xml","smallAssistant/activeTaskList.xml","smallAssistant/smallAssistant.xml","equipment/foxClothes.xml","equipment/foxWeapon.xml","equipment/tieShanClothes.xml","equipment/tieShanWeapon.xml","mount/mountSkills.xml","equipment/dropEq.xml","equipment/precious.xml","NewRank.xml","equipment/houyiClothes.xml","equipment/houyiWeapon.xml","player/zixia.xml","equipment/zixiaClothes.xml","equipment/zixiaWeapon.xml"];
      
      private var _automaticPetsXMLs:Array = ["automaticPets1.xml","automaticPets2.xml"];
      
      private var _mountsXMLs:Array = ["mounts1.xml"];
      
      private var _loadedAutomaticPetsXMLNum:uint;
      
      private var _loadedMountsXMLNum:uint;
      
      private var _urlCount:Array = [];
      
      private var _loader:URLLoader;
      
      public var _timeStr:String;
      
      private var _everyDayTaskXML:XML;
      
      private var _gamingUI:GamingUI;
      
      private var _player1Type:String;
      
      private var _player2Type:String;
      
      private var _completeFun:Function;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:VersionControl;
      
      private var m_otherPartSaveXML:XML;
      
      private var m_saveXML:XML;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      public var _xml:XML = <Data>
			<Monkey name="SunWuKong" experiencePercent="0" bloodPercent="1" magicPercent="1" level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				    <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 <!--  <item id="1400200" petLevel="1" experiencePercent="0.9" lifePercent="0.1" activeSkill="1101200" passiveSkills="1201000_1201000" talent="1103100"/>-->
				</PetPanel>
				<Skill>
					
					<item id="10101001" currentCDTime="0"/>
					<item id="10101002" currentCDTime="0"/>
					<item id="10101004" currentCDTime="0"/>
					<item id="10101003" currentCDTime="0"/>
					<item id="10101000" currentCDTime="0"/>
					
				</Skill>
			   
				
			</Monkey>
			<Dragon name="BaiLongMa" experiencePercent="0" bloodPercent="1" magicPercent="1"
				level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				   <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 
				</PetPanel>
				<Skill>
					
					<item id="10102000" currentCDTime="0"/>
					<item id="10102001" currentCDTime="0"/>
					<item id="10102002" currentCDTime="0"/>
					<item id="10102003" currentCDTime="0"/>
					<item id="10102004" currentCDTime="0" />
				</Skill>
			  
			</Dragon>
					
		<ErLangShen name="ErLangShen" experiencePercent="0" bloodPercent="1" magicPercent="1" level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				   <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 <!--  <item id="1400200" petLevel="1" experiencePercent="0.9" lifePercent="0.1" activeSkill="1101200" passiveSkills="1201000_1201000" talent="1103100"/>-->
				</PetPanel>
				<Skill>
					
					<item id="10104000" currentCDTime="0"/>
					<item id="10104001" currentCDTime="0"/>
					<item id="10104002" currentCDTime="0"/>
					<item id="10104003" currentCDTime="0"/>
					<item id="10104004" currentCDTime="0" />
					
				</Skill>
			   
				
			</ErLangShen>
		<ChangE name="ChangE" experiencePercent="0" bloodPercent="1" magicPercent="1" level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				   <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 <!--  <item id="1400200" petLevel="1" experiencePercent="0.9" lifePercent="0.1" activeSkill="1101200" passiveSkills="1201000_1201000" talent="1103100"/>-->
				</PetPanel>
				<Skill>
					
					<item id="10105000" currentCDTime="0"/>
					<item id="10105001" currentCDTime="0"/>
					<item id="10105002" currentCDTime="0"/>
					<item id="10105003" currentCDTime="0"/>
					<item id="10105004" currentCDTime="0" />
					
				</Skill>
			   
				
			</ChangE>
            <Fox name="Fox" experiencePercent="0" bloodPercent="1" magicPercent="1" level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				    <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 <!--  <item id="1400200" petLevel="1" experiencePercent="0.9" lifePercent="0.1" activeSkill="1101200" passiveSkills="1201000_1201000" talent="1103100"/>-->
				</PetPanel>
				<Skill>
					
					<item id="10106000" currentCDTime="0"/>
					<item id="10106001" currentCDTime="0"/>
					<item id="10106002" currentCDTime="0"/>
					<item id="10106003" currentCDTime="0"/>
					<item id="10106004" currentCDTime="0" />
					
				</Skill>
			   
				
			</Fox>
			<TieShan name="TieShan" experiencePercent="0" bloodPercent="1" magicPercent="1" level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
						 <item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 <!--  <item id="1400200" petLevel="1" experiencePercent="0.9" lifePercent="0.1" activeSkill="1101200" passiveSkills="1201000_1201000" talent="1103100"/>-->
				</PetPanel>
				<Skill>
					
					<item id="10107000" currentCDTime="0"/>
					<item id="10107001" currentCDTime="0"/>
					<item id="10107002" currentCDTime="0"/>
					<item id="10107003" currentCDTime="0"/>
					<item id="10107004" currentCDTime="0" />
					<item id="10107005" currentCDTime="0" />
					<item id="10107006" currentCDTime="0" />
					<item id="10107007" currentCDTime="0" />
					
				</Skill>
			   
				
			</TieShan>
			<Houyi name="Houyi" experiencePercent="0" bloodPercent="1" magicPercent="1"
				level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				   <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 
				</PetPanel>
				<Skill>
					<item id="10108000" currentCDTime="0"/>
					<item id="10108001" currentCDTime="0"/>
					<item id="10108002" currentCDTime="0"/>
					<item id="10108003" currentCDTime="0"/>
					<item id="10108004" currentCDTime="0" />
				</Skill>
			  
			</Houyi>
			<ZiXia name="ZiXia" experiencePercent="0" bloodPercent="1" magicPercent="1"
				level="1" money="0" sF="1">
				<Package>
					<item id="11700010" num="1" />		
					<item id="11100000" num="1" />		
					<item id="10800099" num="1" />		
					<item id="10800099" num="1" />		
					<item id="11400000" num="1" />		
					<item id="11400000" num="1" />		
					<item id="12000003" num="1" />	
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				   <item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					
				</Package>
				<Storage>
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
					<item id="000000" />
				</Storage>
				<InformationPanel>
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
					<item id="0000000" />
				</InformationPanel>
				<PetPanel>
					 
				</PetPanel>
				<Skill>
					<item id="10109000" currentCDTime="0"/>
					<item id="10109001" currentCDTime="0"/>
					<item id="10109002" currentCDTime="0"/>
					<item id="10109003" currentCDTime="0"/>
					<item id="10109004" currentCDTime="0" />
				</Skill>
			  
			</ZiXia>
			<PublicStorage>
					<item id="0"/>
					<item id="0"/>
					<item id="0"/>
					<item id="0"/>
					<item id="0"/>
					<item id="0"/>
					
			</PublicStorage>
			
		   <VIP   oldDateGetGift=""   />
		   
		</Data>
		;
      
      public var _xml2:XML = _xml;
      
      public function Part2()
      {
         super();
      }
      
      override public function clear() : void
      {
         if(m_getEquipmentVOsLogic)
         {
            ClearUtil.clearObject(m_getEquipmentVOsLogic);
         }
         m_getEquipmentVOsLogic = null;
         equipmentVOsData = null;
         ClearUtil.clearObject(_xmls);
         _xmls = null;
         ClearUtil.clearObject(_xmlURLs);
         _xmlURLs = null;
         ClearUtil.clearObject(_urlCount);
         _urlCount = null;
         ClearUtil.clearObject(_loader);
         _loader = null;
         _timeStr = null;
         if(_everyDayTaskXML)
         {
            System.disposeXML(_everyDayTaskXML);
         }
         _everyDayTaskXML = null;
         ClearUtil.clearObject(_gamingUI);
         _gamingUI = null;
         _player1Type = null;
         _player2Type = null;
         m_otherPartSaveXML = null;
         _completeFun = null;
         m_myLoader = null;
         m_versionControl = null;
         System.disposeXML(m_saveXML);
         m_saveXML = null;
         _loadedAutomaticPetsXMLNum = 0;
         _loadedMountsXMLNum = 0;
         super.clear();
      }
      
      public function setVersionControl(param1:VersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function init(param1:String, param2:String, param3:XML, param4:Function) : void
      {
         var delayInitGame:*;
         var player1Str:String = param1;
         var player2Str:String = param2;
         var saveXML:XML = param3;
         var completeFun:Function = param4;
         _player1Type = player1Str;
         _player2Type = player2Str;
         _completeFun = completeFun;
         _gamingUI = GamingUI.getInstance();
         addChild(_gamingUI);
         _gamingUI.init();
         m_saveXML = saveXML;
         if(saveXML)
         {
            _xml = saveXML.Data[0];
            if(saveXML.hasOwnProperty("uiWord"))
            {
               m_otherPartSaveXML = saveXML.uiWord[0];
            }
            ActivityManager.getInstance().initKillNum(m_saveXML);
            HuanLeZhuanPanData.getInstance().initSaveData(m_saveXML);
         }
         if(Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine")
         {
            initXML2();
         }
         else
         {
            delayInitGame = function(param1:String):void
            {
               _timeStr = param1;
               ActivityManager.getInstance().loadXmlData(m_myLoader);
               init2();
            };
            MyFunction2.getServerTimeFunction(delayInitGame,null,false);
            XMLSingle.getInstance().getEqupipmentXMLPartData().init(XMLSingle.getInstance().equipmentXML);
            _urlCount = null;
            _loader = null;
            _xmls = null;
            _xmlURLs = null;
            _automaticPetsXMLs = null;
         }
      }
      
      private function init2() : void
      {
         initGame(_player1Type,_player2Type,_xml,m_otherPartSaveXML);
      }
      
      private function initXML2() : void
      {
         _xmlURLs.length = 0;
         ClearUtil.clearObject(_automaticPetsXMLs);
         _automaticPetsXMLs.length = 0;
         ClearUtil.clearObject(_mountsXMLs);
         _mountsXMLs.length = 0;
         m_myLoader.getXML("UIData/EveryDayTask.xml",getXMLSuccess,getFail,null,"UIData/EveryDayTask.xml");
         ActivityManager.getInstance().loadXmlData(m_myLoader);
         m_myLoader.load();
      }
      
      private function initXML() : void
      {
         var _loc2_:int = 0;
         m_myLoader.getXML("UIData/EveryDayTask.xml",getXMLSuccess,getFail,null,"UIData/EveryDayTask.xml");
         _loadedAutomaticPetsXMLNum = 0;
         _loadedMountsXMLNum = 0;
         var _loc1_:int = int(_xmlURLs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_myLoader.getXML("EmbedXMLs/" + _xmlURLs[_loc2_],getXMLSuccess,getFail);
            _loc2_++;
         }
         _loc1_ = int(_automaticPetsXMLs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_myLoader.getXML("EmbedXMLs/automaticPet/" + _automaticPetsXMLs[_loc2_],getXMLSuccess,getFail,null,"automaticPets");
            _loc2_++;
         }
         _loc1_ = int(_mountsXMLs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_myLoader.getXML("EmbedXMLs/mount/" + _mountsXMLs[_loc2_],getXMLSuccess,getFail,null,"mounts");
            _loc2_++;
         }
         ActivityManager.getInstance().loadXmlData(m_myLoader);
         m_myLoader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var i:int;
         var length:int;
         var delayInitGame:*;
         var loaderData:YJFYLoaderData = param1;
         if(loaderData.extra == "UIData/EveryDayTask.xml")
         {
            _everyDayTaskXML = loaderData.resultXML;
         }
         else if(loaderData.extra == "automaticPets")
         {
            XMLSingle.getInstance().subAutomaticPetsXML = loaderData.resultXML;
            ++_loadedAutomaticPetsXMLNum;
         }
         else if(loaderData.extra == "mounts")
         {
            ++_loadedMountsXMLNum;
         }
         else
         {
            length = int(_xmlURLs.length);
            i = 0;
            while(i < length)
            {
               if("EmbedXMLs/" + _xmlURLs[i] == loaderData.xmlPath)
               {
                  XMLSingle.getInstance()[_xmls[i]] = loaderData.resultXML;
                  _xmls.splice(i,1);
                  _xmlURLs.splice(i,1);
               }
               ++i;
            }
         }
         if(_xmlURLs.length == 0 && _everyDayTaskXML && _loadedAutomaticPetsXMLNum == _automaticPetsXMLs.length && _loadedMountsXMLNum == _mountsXMLs.length)
         {
            delayInitGame = function(param1:String):void
            {
               _timeStr = param1;
               init2();
            };
            MyFunction2.getServerTimeFunction(delayInitGame,null,false);
            XMLSingle.getInstance().getEqupipmentXMLPartData().init(XMLSingle.getInstance().equipmentXML);
            _urlCount = null;
            _loader = null;
            _xmls = null;
            _xmlURLs = null;
            _automaticPetsXMLs = null;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function initGame(param1:String, param2:String, param3:XML, param4:XML) : void
      {
         var player1:Player;
         var player2:Player;
         var pkMode2VO1:PKMode2VO;
         var mountsData:MountsVO;
         var pkVO:PKVO;
         var automaticPetsData:AutomaticPetsData;
         var smallAssistantSaveData:SmallAssistantSaveData;
         var svActivitySaveData:SVActivitySaveData;
         var activeTasksSaveData:ActiveTasksSaveData;
         var playerType1:String = param1;
         var playerType2:String = param2;
         var xml:XML = param3;
         var otherPartSaveXML:XML = param4;
         var eqXML:XML = XMLSingle.getInstance().equipmentXML;
         var skillXML:XML = XMLSingle.getInstance().skillXML;
         var talentXML:XML = XMLSingle.getInstance().talentXML;
         var privilegeXML:XML = XMLSingle.getInstance().privilegeXML;
         var taskXML:XML = XMLSingle.getInstance().taskXML;
         var dataXML:XML = XMLSingle.getInstance().dataXML;
         var buffXML:XML = XMLSingle.getInstance().buffXML;
         var farmXML:XML = XMLSingle.getInstance().farmXML;
         var vipVO:VipVO = InitUI.getInstance().initVIPVO(xml,eqXML,privilegeXML,_timeStr);
         var societyDataVO:SocietyDataVO = new SocietyDataVO();
         XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment = XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipmentFun(eqXML);
         pkMode2VO1 = new PKMode2VO();
         mountsData = new MountsVO();
         pkVO = new PKVO();
         switch(playerType1)
         {
            case "SunWuKong":
               player1 = InitUI.getInstance().initMonkey(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "BaiLongMa":
               player1 = InitUI.getInstance().initDragon(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "ErLangShen":
               player1 = InitUI.getInstance().initErLangShen(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "ChangE":
               player1 = InitUI.getInstance().initChangE(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "Fox":
               player1 = InitUI.getInstance().initFox(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "TieShan":
               player1 = InitUI.getInstance().initTieShan(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "Houyi":
               player1 = InitUI.getInstance().initHouYi(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            case "ZiXia":
               player1 = InitUI.getInstance().initZiXia(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerOne");
               break;
            default:
               throw new Error(playerType1 + "不存在的人物类型！");
         }
         player1.vipVO = vipVO;
         player1.setsocietyDataVO(societyDataVO);
         player1.setPKMode2VO1(pkMode2VO1);
         player1.setMountsVO(mountsData);
         player1.setPKVO(pkVO);
         GamingUI.getInstance().player1 = player1;
         if(playerType2)
         {
            switch(playerType2)
            {
               case "SunWuKong":
                  player2 = InitUI.getInstance().initMonkey(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "Houyi":
                  player2 = InitUI.getInstance().initHouYi(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "BaiLongMa":
                  player2 = InitUI.getInstance().initDragon(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "ErLangShen":
                  player2 = InitUI.getInstance().initErLangShen(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "ChangE":
                  player2 = InitUI.getInstance().initChangE(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "Fox":
                  player2 = InitUI.getInstance().initFox(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "TieShan":
                  player2 = InitUI.getInstance().initTieShan(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               case "ZiXia":
                  player2 = InitUI.getInstance().initZiXia(xml,eqXML,skillXML,talentXML,dataXML,_timeStr,1 | 2 | 4 | 8 | 0x10 | 0x20 | 0x40 | 0x0200 | 0x80 | 0x0400 | 0x0800,"playerTwo");
                  break;
               default:
                  throw new Error(playerType2 + "不存在的人物类型！");
            }
            player2.vipVO = vipVO;
            player2.setsocietyDataVO(societyDataVO);
            player2.setPKMode2VO1(pkMode2VO1);
            player2.setMountsVO(mountsData);
            player2.setPKVO(pkVO);
            GamingUI.getInstance().player2 = player2;
         }
         pkVO.initByXML(xml.PK[0]);
         mountsData.initFromSaveXML(xml,_timeStr,player1,player2);
         InitUI.getInstance().initTaskGoals(xml,taskXML,_timeStr);
         InitUI.getInstance().dealInvalidTaskGoalVO(_timeStr);
         TaskFunction.getInstance().initExTask(XMLSingle.getInstance().everyDayTask,taskXML,_timeStr);
         InitUI.getInstance().initMirageData(xml);
         InitUI.getInstance().initPKData(xml,dataXML,_timeStr);
         InitUI.getInstance().initFarm(xml,farmXML);
         InitUI.getInstance().initLianDanFactory(xml);
         InitUI.getInstance().initExchangeGiftData(xml);
         InitUI.getInstance().initBuffs(xml,_timeStr,buffXML,player1,player2);
         InitUI.getInstance().initProtect(xml,buffXML);
         InitUI.getInstance().initSign(xml,_timeStr);
         InitUI.getInstance().initNicknameData(xml);
         InitUI2.getInstance().initRecaptureGoldData(xml,_timeStr);
         InitUI2.getInstance().initExEData(xml,_timeStr);
         InitUI2.getInstance().initOnLineGiftBagData(xml,_timeStr);
         InitUI.getInstance().initOther(xml);
         NewMainTaskData.getInstance().initByXML(xml,XMLSingle.getInstance().mainLineTaskListXML,XMLSingle.getInstance().mainLineTaskGoalsXML,XMLSingle.getInstance().mainLineTaskXML);
         NewEveryDayData.getInstance().initFromSaveXML(xml);
         MewActivityData.getInstance().initFromSaveXML(xml);
         TeHuiData.getInstance().initSaveData(xml);
         ConsumerData.getInstance().initSaveData(xml);
         AnyeLevelData.getInstance().initFromSaveXML(xml);
         BuchangData.getInstance().initSaveData(xml);
         WorldBossSaveData.getInstance().initFromSaveXML(xml,_timeStr,dataXML.WorldBossData[0]);
         societyDataVO.initDataFromSaveXML(xml);
         BossSaveData.getInstance().initFromSaveXML(xml,_timeStr);
         PetBossSaveData.getInstance().initFromSaveXML(xml,_timeStr);
         LevelModeSaveData.getInstance().initFromSaveXML(xml,_timeStr,otherPartSaveXML);
         MyPKSaveData.getInstance().initFromSaveXML(xml);
         XiangMoLevelSaveData.getInstance().initFromSaveXML(xml);
         RankDataInfo.getInstance().initFromSaveXML(xml);
         WeekPayData.getInstance().initSaveData(xml);
         WuyiData.getInstance().initSaveData(xml);
         FirstPayData.getInstance().initFromSaveXML(xml,_timeStr);
         NewSignData.getInstance().initFromSaveXML(xml,_timeStr);
         BroadDataManager.getInstance().initFromSaveXML(xml);
         EndlessLevelData.getInstance().initFromSaveXML(xml,_timeStr);
         PreDataInfo.getInstance().initFromSaveXML(xml);
         automaticPetsData = new AutomaticPetsData();
         automaticPetsData.initFromSaveXML(xml,_timeStr,player1,player2);
         BroadDataManager.getInstance().initData();
         CollectTimeSaveData.getInstance().initFromSaveXML(xml,_timeStr);
         if(xml.hasOwnProperty("PK21"))
         {
            pkMode2VO1.initFromSaveXML(xml.PK21[0]);
         }
         smallAssistantSaveData = new SmallAssistantSaveData();
         smallAssistantSaveData.initBySaveXML(xml,XMLSingle.getInstance().activeTaskListXML,XMLSingle.getInstance().mainLineTaskGoalsXML,XMLSingle.getInstance().mainLineTaskXML,_timeStr);
         svActivitySaveData = new SVActivitySaveData();
         svActivitySaveData.initBySaveXML(xml,_timeStr);
         ActivityManager.getInstance().initDoubleEggNum(xml);
         activeTasksSaveData = new ActiveTasksSaveData();
         activeTasksSaveData.initBySaveXML(xml,XMLSingle.getInstance().activeTaskListXML,XMLSingle.getInstance().mainLineTaskGoalsXML,XMLSingle.getInstance().mainLineTaskXML);
         DreamLandSaveData.getInstance().initByXML(xml);
         APIDataAnalyze.getInstance().initBySaveXML(xml);
         _gamingUI.initUI(player1,player2,InitUI.getInstance().initPublicStorage(xml,eqXML,skillXML,talentXML,_timeStr),automaticPetsData,smallAssistantSaveData,svActivitySaveData,_timeStr,null);
         setTimeout(function():void
         {
            try
            {
               APIDataAnalyze.getInstance().sendData();
            }
            catch(error:Error)
            {
            }
         },1000);
         DetectionClass.getInstance().resetCheckTime();
         SaveAPI.saveFileDataBackup = null;
         _completeFun();
      }
      
      private function ZhouNianIsNewGame() : void
      {
         var _loc1_:* = undefined;
         if(!GameData.getInstance().isNewGame)
         {
            return;
         }
         GameData.getInstance().isNewGame = false;
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         equipmentVOsData = new EquipmentVOsData();
         var _loc2_:XML = <fuBenXML><eq>
       		<showEq  id="12000005" num="1"/>
			</eq></fuBenXML>;
         _loc1_ = XMLSingle.getEquipmentVOs(_loc2_.eq[0],XMLSingle.getInstance().equipmentXML,false,_timeStr);
         equipmentVOsData.setEquipmentVOs(_loc1_);
         m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player1);
         if(GamingUI.getInstance().player2)
         {
            m_getEquipmentVOsLogic.getEquipmentVOs(equipmentVOsData,GamingUI.getInstance().player2);
         }
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
         ClearUtil.clearObject(_loc1_);
         _loc1_ = null;
      }
      
      private function ZhouNianWuBeiJingYan(param1:String) : void
      {
         var _loc10_:BuffVO = null;
         var _loc6_:int = int(param1.search(" "));
         var _loc11_:String = param1.substring(0,_loc6_);
         var _loc8_:String = _loc11_ + " " + String(XMLSingle.getInstance().dataXML.ZhouNianHuoDongWuBeiJingyan[0].@EndTime);
         var _loc9_:String = _loc11_ + " " + String(XMLSingle.getInstance().dataXML.ZhouNianHuoDongWuBeiJingyan[0].@StartTime);
         var _loc5_:Number = TimeUtil.getTimeUtil().timeInterval(_loc9_,param1);
         var _loc3_:Number = TimeUtil.getTimeUtil().timeInterval(param1,_loc8_);
         var _loc2_:Number = TimeUtil.getTimeUtil().timeInterval(String(XMLSingle.getInstance().dataXML.ZhouNianHuoDongWuBeiJingyan[0].@StartData),param1);
         var _loc7_:Number = TimeUtil.getTimeUtil().timeInterval(param1,String(XMLSingle.getInstance().dataXML.ZhouNianHuoDongWuBeiJingyan[0].@EndData));
         if(_loc2_ < 0 || _loc7_ < 0 || _loc5_ < 0 || _loc3_ < 0)
         {
            return;
         }
         _loc10_ = XMLSingle.getBuff(10004,XMLSingle.getInstance().buffXML);
         Part1.getInstance().setIsFive(true);
         var _loc4_:int = TimeUtil.getTimeUtil().timeIntervalBySecond(param1,_loc8_);
         (_loc10_ as AllTimeBuffVO).startDate = param1;
         (_loc10_ as AllTimeBuffVO).totalTime = _loc4_ / 3600;
         _loc10_.remainTime = _loc4_;
         BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc10_),BuffData.getInstance().buffDrives);
         ZhouNianIsNewGame();
      }
   }
}

