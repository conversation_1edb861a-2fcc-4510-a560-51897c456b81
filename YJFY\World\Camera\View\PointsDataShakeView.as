package YJFY.World.Camera.View
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import flash.display.MovieClip;
   import flash.geom.Point;
   
   public class PointsDataShakeView
   {
      
      protected var m_isMyShake:Boolean;
      
      protected var m_view:View;
      
      protected var m_viewOriginalPoint:Point;
      
      protected var m_dataOfPoints:DataOfPoints;
      
      protected var m_currentPointFrame:int;
      
      public function PointsDataShakeView()
      {
         super();
         m_viewOriginalPoint = new Point();
         m_dataOfPoints = new DataOfPoints();
      }
      
      public function clear() : void
      {
         recover();
         m_view = null;
         m_viewOriginalPoint = null;
         ClearUtil.clearObject(m_dataOfPoints);
         m_dataOfPoints = null;
      }
      
      public function initByMovieClip(param1:MovieClip) : void
      {
         ClearUtil.clearObject(m_dataOfPoints);
         m_dataOfPoints = new DataOfPoints();
         m_dataOfPoints.initByMovieClip(param1);
      }
      
      public function initByDataOfPoints(param1:DataOfPoints) : void
      {
         m_dataOfPoints.copy(param1);
      }
      
      public function shakeView(param1:View) : void
      {
         if(m_isMyShake)
         {
            return;
         }
         m_view = param1;
         if(m_view.getIsShake())
         {
            return;
         }
         if(m_dataOfPoints == null)
         {
            return;
         }
         m_viewOriginalPoint.x = m_view.x;
         m_viewOriginalPoint.y = m_view.y;
         m_isMyShake = true;
         m_view.setIsShake(true);
         m_currentPointFrame = 1;
      }
      
      public function render() : void
      {
         if(m_isMyShake == false)
         {
            return;
         }
         if(m_currentPointFrame <= m_dataOfPoints.getPointNum())
         {
            m_view.x = m_dataOfPoints.getPointByIndex(m_currentPointFrame - 1).x;
            m_view.y = m_dataOfPoints.getPointByIndex(m_currentPointFrame - 1).y;
            ++m_currentPointFrame;
         }
         else
         {
            recover();
         }
      }
      
      protected function recover() : void
      {
         if(m_isMyShake)
         {
            m_view.setIsShake(false);
         }
         m_isMyShake = false;
         m_currentPointFrame = 1;
      }
      
      public function getIsMyShake() : Boolean
      {
         return m_isMyShake;
      }
   }
}

