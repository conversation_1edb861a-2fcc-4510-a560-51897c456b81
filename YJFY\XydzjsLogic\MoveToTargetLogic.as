package YJFY.XydzjsLogic
{
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   
   public class MoveToTargetLogic
   {
      
      private var m_nowCoordinate:Coordinate;
      
      private var m_targetCoordinate:Coordinate;
      
      private var m_speedCoordinate:Coordinate;
      
      private var m_isReach:Boolean;
      
      private var m_isStart:Boolean;
      
      private var m_tX:Number;
      
      private var m_tY:Number;
      
      private var m_tZ:Number;
      
      private var m_moveX:Number;
      
      private var m_moveY:Number;
      
      private var m_moveZ:Number;
      
      public function MoveToTargetLogic()
      {
         super();
         m_nowCoordinate = new Coordinate();
         m_targetCoordinate = new Coordinate();
         m_speedCoordinate = new Coordinate();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_nowCoordinate);
         m_nowCoordinate = null;
         ClearUtil.clearObject(m_targetCoordinate);
         m_targetCoordinate = null;
         ClearUtil.clearObject(m_speedCoordinate);
         m_speedCoordinate = null;
      }
      
      public function move(param1:Coordinate, param2:Coordinate, param3:Number) : void
      {
         m_isReach = false;
         m_isStart = true;
         m_nowCoordinate.copy(param1);
         m_targetCoordinate.copy(param2);
         var _loc7_:Number = m_targetCoordinate.getX() - m_nowCoordinate.getX();
         var _loc6_:Number = m_targetCoordinate.getY() - m_nowCoordinate.getY();
         var _loc4_:Number = m_targetCoordinate.getZ() - m_nowCoordinate.getZ();
         var _loc5_:Number = Math.sqrt(Math.pow(_loc7_,2) + Math.pow(_loc6_,2) + Math.pow(_loc4_,2));
         m_speedCoordinate.setTo(_loc7_ * param3 / _loc5_,_loc6_ * param3 / _loc5_,_loc4_ * param3 / _loc5_);
         if(m_speedCoordinate.getX() == 0 && m_speedCoordinate.getY() == 0 && m_speedCoordinate.getZ() == 0)
         {
            m_isReach = true;
            m_isStart = false;
         }
      }
      
      public function getIsReach() : Boolean
      {
         return m_isReach;
      }
      
      public function getNowCoordinate() : Coordinate
      {
         return m_nowCoordinate;
      }
      
      public function render(param1:Number) : void
      {
         if(m_isStart == false)
         {
            return;
         }
         if(m_isReach)
         {
            return;
         }
         m_tX = m_targetCoordinate.getX() - m_nowCoordinate.getX();
         m_tY = m_targetCoordinate.getY() - m_nowCoordinate.getY();
         m_tZ = m_targetCoordinate.getZ() - m_nowCoordinate.getZ();
         m_moveX = m_speedCoordinate.getX() * param1;
         m_moveY = m_speedCoordinate.getY() * param1;
         m_moveZ = m_speedCoordinate.getZ() * param1;
         if(m_tX == 0 && m_tY == 0 && m_tZ == 0)
         {
            m_isReach = true;
            m_isStart = false;
         }
         if(Math.abs(m_tX) <= Math.abs(m_moveX) && Math.abs(m_tY) <= Math.abs(m_moveY) && Math.abs(m_tZ) <= Math.abs(m_moveZ))
         {
            m_nowCoordinate.setTo(m_targetCoordinate.getX(),m_targetCoordinate.getY(),m_targetCoordinate.getZ());
            m_isReach = true;
            m_isStart = false;
         }
         else
         {
            m_nowCoordinate.setX(m_nowCoordinate.getX() + m_moveX);
            m_nowCoordinate.setY(m_nowCoordinate.getY() + m_moveY);
            m_nowCoordinate.setZ(m_nowCoordinate.getZ() + m_moveZ);
         }
      }
   }
}

