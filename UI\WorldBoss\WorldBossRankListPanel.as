package UI.WorldBoss
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFunction2;
   import UI.PKUI.RankListPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class WorldBossRankListPanel extends RankListPanel
   {
      
      private var m_lookPkHonourPanelBtn:ButtonLogicShell;
      
      public function WorldBossRankListPanel()
      {
         super();
         _isTwoMode = true;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("WorldBossRankList") as MovieClip;
         addChild(m_show);
         super.init(param1,param2);
         m_lookPkHonourPanelBtn = new ButtonLogicShell();
         m_lookPkHonourPanelBtn.setShow(m_show["lookPkHonourPanelBtn"]);
         m_lookPkHonourPanelBtn.setTipString("点击打开荣誉大厅");
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_lookPkHonourPanelBtn);
         m_lookPkHonourPanelBtn = null;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:GamingUI = null;
         super.clickButton(param1);
         if(param1.button == m_lookPkHonourPanelBtn)
         {
            _loc2_ = _gamingUI;
            _loc2_.closeRankList();
            _loc2_.openHonourPanel("worldBossHonourHall");
         }
      }
   }
}

