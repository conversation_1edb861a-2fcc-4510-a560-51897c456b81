package UI.HonourHallPanel
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PKHonourColume
   {
      
      public static const NAME:String = "name";
      
      public static const DESCRIPTION:String = "description";
      
      public static const MEDAL_ID:String = "medalID";
      
      private var m_show:MovieClip;
      
      private var m_showMc:MovieClipPlayLogicShell;
      
      private var m_nameText:TextField;
      
      private var m_descriptionText:TextField;
      
      private var m_getAwardBtn:ButtonLogicShell;
      
      private var _dataObject:Object;
      
      private var _medalEquipmentVO:EquipmentVO;
      
      private var _medalEquipment:Equipment;
      
      private var m_honourHallPanel:HonourHallPanel;
      
      public function PKHonourColume()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMc = new MovieClipPlayLogicShell();
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_showMc.setShow(m_show);
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_nameText);
         m_descriptionText = m_show["descriptionText"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_descriptionText);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function setHonourHallPanel(param1:HonourHallPanel) : void
      {
         m_honourHallPanel = param1;
      }
      
      public function initData(param1:Object, param2:String, param3:XML) : void
      {
         _dataObject = param1;
         _medalEquipmentVO = XMLSingle.getEquipmentVOByID(param1["medalID"],param3,param2);
         _medalEquipment = MyFunction2.sheatheEquipmentShell(_medalEquipmentVO);
         _medalEquipment.x = 34;
         _medalEquipment.y = 34;
         _medalEquipment.addEventListener("rollOver",equipmentInfor,false,0,true);
         _medalEquipment.addEventListener("rollOut",equipmentInfor,false,0,true);
         m_show.addChild(_medalEquipment as DisplayObject);
         m_nameText.text = param1.name;
         m_descriptionText.text = param1.description;
      }
      
      public function active() : void
      {
         m_showMc.gotoAndStop("2");
         m_getAwardBtn = new ButtonLogicShell();
         m_getAwardBtn.setShow(m_show["getAwardBtn"]);
         m_getAwardBtn.setTipString("点击获取奖励");
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = null;
         ClearUtil.clearObject(m_showMc);
         m_showMc = null;
         m_nameText = null;
         m_descriptionText = null;
         ClearUtil.clearObject(m_getAwardBtn);
         m_getAwardBtn = null;
         ClearUtil.clearObject(_medalEquipment);
         _medalEquipment = null;
         if(_medalEquipmentVO)
         {
            _medalEquipmentVO.clear();
         }
         _medalEquipmentVO = null;
         for(var _loc1_ in _dataObject)
         {
            _dataObject[_loc1_] = null;
         }
         _dataObject = null;
         m_honourHallPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_getAwardBtn === _loc2_)
         {
            getAward();
         }
      }
      
      private function getAward() : void
      {
         var _loc9_:int = 0;
         var _loc4_:int = 0;
         var _loc8_:* = undefined;
         var _loc2_:EquipmentVO = null;
         var _loc7_:* = undefined;
         var _loc3_:Player = GamingUI.getInstance().player1;
         var _loc5_:Player = GamingUI.getInstance().player2;
         var _loc1_:Vector.<EquipmentVO> = GamingUI.getInstance().publicStorageEquipmentVOs;
         var _loc6_:Boolean = false;
         if(_loc5_)
         {
            _loc8_ = MyFunction2.concatEquipmentVOVector(_loc3_.playerVO.inforEquipmentVOs,_loc3_.playerVO.packageEquipmentVOs,_loc3_.playerVO.storageEquipmentVOs,_loc5_.playerVO.inforEquipmentVOs,_loc5_.playerVO.packageEquipmentVOs,_loc5_.playerVO.storageEquipmentVOs,_loc3_.playerVO.medalEquipmentVOs,_loc5_.playerVO.medalEquipmentVOs,_loc1_);
         }
         else
         {
            _loc8_ = MyFunction2.concatEquipmentVOVector(_loc3_.playerVO.inforEquipmentVOs,_loc3_.playerVO.packageEquipmentVOs,_loc3_.playerVO.storageEquipmentVOs,_loc3_.playerVO.medalEquipmentVOs,_loc1_);
         }
         _loc4_ = int(_loc8_.length);
         _loc9_ = 0;
         while(_loc9_ < _loc4_)
         {
            if(Boolean(_loc8_[_loc9_]) && _loc8_[_loc9_].id == _dataObject["medalID"])
            {
               _loc6_ = true;
               break;
            }
            _loc9_++;
         }
         if(!_loc6_)
         {
            _loc2_ = XMLSingle.getEquipmentVOByID(_dataObject["medalID"],XMLSingle.getInstance().equipmentXML,TimeUtil.timeStr,true);
            _loc7_ = new Vector.<EquipmentVO>();
            _loc7_.push(_loc2_);
            MyFunction2.addEquipmentVOs(_loc7_,_loc3_,m_honourHallPanel.showWarningBox,m_honourHallPanel.showWarningBox,["背包已满！",0],["获取成功！",0],2);
            _loc4_ = int(_loc7_.length);
            _loc9_ = 0;
            while(_loc9_ < _loc4_)
            {
               if(_loc7_[_loc9_])
               {
                  _loc7_[_loc9_] = null;
               }
               _loc9_++;
            }
            _loc7_ = null;
         }
         else
         {
            m_honourHallPanel.showWarningBox("您已经有这个勋章了！",0);
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

