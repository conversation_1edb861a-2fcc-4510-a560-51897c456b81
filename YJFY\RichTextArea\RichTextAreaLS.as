package YJFY.RichTextArea
{
   import UI.MySprite;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.RichTextArea.IconData.IconData;
   import YJFY.RichTextArea.IconData.IconData_Gif;
   import YJFY.RichTextArea.IconData.IconData_Jpg;
   import YJFY.RichTextArea.IconData.IconData_MovieClip;
   import YJFY.RichTextArea.IconData.IconType;
   import YJFY.Utils.ClearUtil;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.geom.Rectangle;
   import flash.net.URLRequest;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.text.TextLineMetrics;
   import org.bytearray.gif.player.GIFPlayer;
   
   public class RichTextAreaLS
   {
      
      private const m_const_movieClipFaceWidth:Number = 60;
      
      private const m_const_movieClipFaceHeigth:Number = 60;
      
      private var m_refeCaretData:CaretData;
      
      private var m_iconDatas:Vector.<IconData>;
      
      private var m_iconDataObjByStr:Object;
      
      private var m_iconDataStrMaxLength:int;
      
      private var m_defaultFormat:TextFormat;
      
      private var m_nowExistIconDatas:Vector.<ExistIconData>;
      
      private var m_nowExistIconDataObjByIndex:Object;
      
      private var m_upShowContainer:Sprite;
      
      private var m_const_placeHolder:String = String.fromCharCode(12288);
      
      private var m_imageLoaders:Vector.<Loader>;
      
      private var m_cacheTextField:TextField;
      
      private var m_refreshListeners:Vector.<IRichTextAreaRefreshListener>;
      
      private var m_minWidth_textField:Number;
      
      private var m_minHeight_textField:Number;
      
      private var m_isAbleWordWrap:Boolean;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_show:MovieClip;
      
      private var m_textField:TextField;
      
      public function RichTextAreaLS()
      {
         super();
         trace("m_const_placeHolder length:",m_const_placeHolder.length,",charcode:",m_const_placeHolder.charCodeAt(0));
      }
      
      public function clear() : void
      {
         removeListeners(m_textField);
         ClearUtil.clearObject(m_refeCaretData);
         m_refeCaretData = null;
         ClearUtil.nullArr(m_iconDatas);
         m_iconDatas = null;
         ClearUtil.nullObject(m_iconDataObjByStr);
         m_iconDataObjByStr = null;
         m_defaultFormat = null;
         ClearUtil.nullArr(m_nowExistIconDatas);
         m_nowExistIconDatas = null;
         ClearUtil.nullObject(m_nowExistIconDataObjByIndex);
         m_nowExistIconDataObjByIndex = null;
         ClearUtil.clearObject(m_upShowContainer);
         m_upShowContainer = null;
         m_const_placeHolder = null;
         ClearUtil.nullArr(m_imageLoaders);
         m_imageLoaders = null;
         m_cacheTextField = null;
         ClearUtil.nullArr(m_refreshListeners,false,false,false);
         m_refreshListeners = null;
         m_myLoader = null;
         m_show = null;
         m_textField = null;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setShow(param1:TextField) : void
      {
         m_show = param1.parent as MovieClip;
         removeListeners(m_textField);
         m_textField = param1;
         addListeners(m_textField);
         m_textField.width = m_textField.width;
         m_textField.height = m_textField.height;
         m_textField.useRichTextClipboard = true;
         m_textField.mouseWheelEnabled = false;
         m_textField.multiline = true;
         m_textField.wordWrap = true;
         m_defaultFormat = m_textField.defaultTextFormat;
         m_minWidth_textField = param1.width;
         m_minHeight_textField = param1.height;
         init();
      }
      
      public function addRefreshListener(param1:IRichTextAreaRefreshListener) : void
      {
         if(m_refreshListeners == null)
         {
            m_refreshListeners = new Vector.<IRichTextAreaRefreshListener>();
         }
         m_refreshListeners.push(param1);
      }
      
      public function removeRefreshListener(param1:IRichTextAreaRefreshListener) : void
      {
         if(m_refreshListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_refreshListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_refreshListeners.splice(_loc2_,1);
            _loc2_ = int(m_refreshListeners.indexOf(param1));
         }
      }
      
      private function init() : void
      {
         m_cacheTextField = new TextField();
         m_iconDatas = new Vector.<IconData>();
         m_iconDataObjByStr = {};
         m_nowExistIconDatas = new Vector.<ExistIconData>();
         m_nowExistIconDataObjByIndex = {};
         m_upShowContainer = new MySprite();
         m_show.addChild(m_upShowContainer);
         m_upShowContainer.mouseChildren = false;
         m_upShowContainer.mouseEnabled = false;
         m_imageLoaders = new Vector.<Loader>();
      }
      
      private function clearExistIconDataAndRefreCaretData() : void
      {
         ClearUtil.nullArr(m_nowExistIconDatas);
         m_nowExistIconDatas = new Vector.<ExistIconData>();
         ClearUtil.nullObject(m_nowExistIconDataObjByIndex);
         m_nowExistIconDataObjByIndex = {};
         m_refeCaretData = null;
         m_textField.text = "";
      }
      
      public function setRichText(param1:String) : void
      {
         clearExistIconDataAndRefreCaretData();
         if(Boolean(param1) == false)
         {
            param1 = "";
         }
         m_textField.text = param1;
         convert();
         refresh();
         resetRefeCaretData();
      }
      
      public function getRichText() : String
      {
         var _loc7_:int = 0;
         var _loc4_:* = null;
         var _loc2_:ExistIconData = null;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc5_:int = int(m_nowExistIconDatas.length);
         var _loc6_:int = m_const_placeHolder.length;
         m_nowExistIconDatas.sort(sortExistIcons);
         m_cacheTextField.text = m_textField.text;
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc2_ = m_nowExistIconDatas[_loc7_];
            _loc3_ = _loc2_.getIndex();
            _loc3_ += _loc1_;
            m_cacheTextField.replaceText(_loc3_,_loc3_ + _loc6_,_loc2_.getIconData().getIconStr());
            _loc1_ += _loc2_.getIconData().getIconStr().length - _loc6_;
            _loc7_++;
         }
         return m_cacheTextField.text;
      }
      
      public function setMaxChars(param1:uint) : void
      {
         m_textField.maxChars = param1;
      }
      
      public function setIsAbleWordWrap(param1:Boolean) : void
      {
         m_isAbleWordWrap = param1;
      }
      
      public function getDefaultTextformat() : TextFormat
      {
         return m_defaultFormat;
      }
      
      public function setDefaultTextformat(param1:TextFormat) : void
      {
         m_defaultFormat = param1;
         resetDefaultFormat();
         refresh();
      }
      
      public function getRichTextLength() : int
      {
         var _loc4_:int = 0;
         var _loc1_:int = m_textField.text.length;
         var _loc2_:int = int(m_nowExistIconDatas.length);
         var _loc3_:int = m_const_placeHolder.length;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ += m_nowExistIconDatas[_loc4_].getIconData().getIconStr().length - _loc3_;
            _loc4_++;
         }
         return _loc1_;
      }
      
      public function addIconData(param1:IconData) : void
      {
         if(m_iconDataObjByStr[param1.getIconStr()] == null)
         {
            m_iconDatas.push(param1);
            m_iconDataObjByStr[param1.getIconStr()] = param1;
            m_iconDataStrMaxLength = Math.max(m_iconDataStrMaxLength,param1.getIconStr().length);
            return;
         }
         throw new Error("相同的 iconStr 的iconData重复增加了。",param1.getIconStr());
      }
      
      public function appendRichText(param1:String, param2:Object = null, param3:Object = null) : void
      {
         var _loc6_:TextFormat = null;
         var _loc5_:int = 0;
         var _loc4_:String = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         if(param1)
         {
            resetDefaultFormat();
            _loc6_ = m_textField.defaultTextFormat;
            if(param2 != null)
            {
               _loc6_.color = param2;
            }
            if(param3 != null)
            {
               _loc6_.size = param3;
            }
            _loc5_ = m_textField.length;
            _loc4_ = m_textField.text;
            _loc7_ = _loc4_.length;
            param1 = param1.replace(/\r/g,"\n");
            m_textField.appendText(param1);
            _loc8_ = m_textField.length;
            m_textField.setTextFormat(_loc6_,_loc5_,_loc8_);
            convert(Math.max(0,_loc5_ - m_iconDataStrMaxLength),_loc8_);
            refresh();
         }
      }
      
      public function insertRichText(param1:String) : void
      {
         resetRefeCaretData();
         var _loc2_:int = m_textField.selectionBeginIndex;
         m_textField.replaceText(m_textField.selectionBeginIndex,m_textField.selectionEndIndex,param1);
         m_textField.setTextFormat(m_defaultFormat,_loc2_,_loc2_ + param1.length);
         resetDefaultFormat();
         change();
         refresh();
         resetRefeCaretData();
         m_textField.stage.focus = m_textField;
      }
      
      private function convert(param1:int = -1, param2:int = -1) : void
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:ExistIconData = searchOneIconDataStrInStr(m_textField.text,param1,param2);
         while(_loc3_)
         {
            _loc5_ = _loc3_.getIconData().getIconStr().length;
            m_textField.replaceText(_loc3_.getIndex(),_loc3_.getIndex() + _loc5_,m_const_placeHolder);
            reLocationCaret(_loc3_.getIndex() + m_const_placeHolder.length);
            refreshExistIconDatas(_loc3_.getIndex(),m_const_placeHolder.length - _loc5_);
            addExistIconData(_loc3_);
            addExistIconShow(_loc3_);
            _loc3_ = searchOneIconDataStrInStr(m_textField.text,param1,param2);
         }
      }
      
      private function change() : void
      {
         var _loc2_:int = 0;
         deleNotUseExistIcon(m_refeCaretData.getSelectionEndIndex(),m_refeCaretData.getSelectionBeginIndex());
         removewordWrap();
         var _loc3_:CaretData = getCurCaretData();
         var _loc1_:int = _loc3_.getCaretIndex() < m_refeCaretData.getCaretIndex() ? _loc3_.getCaretIndex() : m_refeCaretData.getCaretIndex();
         var _loc4_:int = _loc3_.getFrontPartLength() + _loc3_.getBehindPartLength() - m_refeCaretData.getFrontPartLength() - m_refeCaretData.getBehindPartLength();
         if(_loc3_.getCaretIndex() > m_refeCaretData.getCaretIndex())
         {
            _loc2_ = getAddTextLengthByIconDataFromTxtFormat(m_refeCaretData.getCaretIndex(),_loc3_.getCaretIndex());
         }
         if(Boolean(m_textField.maxChars) && getRichTextLength() + _loc2_ > m_textField.maxChars)
         {
            deleText(m_refeCaretData.getCaretIndex(),_loc3_.getCaretIndex());
            return;
         }
         if(_loc3_.getCaretIndex() > m_refeCaretData.getCaretIndex())
         {
            refreshExistIconDatas(_loc1_,_loc4_);
            createIconDataFromTxtFormat(m_refeCaretData.getCaretIndex(),_loc3_.getCaretIndex());
            convert(Math.max(0,m_refeCaretData.getCaretIndex() - m_iconDataStrMaxLength),Math.min(m_textField.length,_loc3_.getCaretIndex() + m_iconDataStrMaxLength));
         }
         else
         {
            deleNotUseExistIcon(m_refeCaretData.getCaretIndex(),_loc3_.getCaretIndex());
            refreshExistIconDatas(_loc1_,_loc4_);
         }
      }
      
      private function removewordWrap() : void
      {
         var _loc1_:int = 0;
         if(m_isAbleWordWrap)
         {
            return;
         }
         var _loc2_:CaretData = getCurCaretData();
         if(_loc2_.getCaretIndex() > m_refeCaretData.getCaretIndex())
         {
            _loc1_ = int(m_textField.text.search(/\r/));
            while(_loc1_ != -1)
            {
               m_textField.replaceText(_loc1_,_loc1_ + 1,"");
               reLocationCaret(_loc2_.getCaretIndex() - 1);
               _loc1_ = int(m_textField.text.search(/\r/));
            }
         }
      }
      
      private function getAddTextLengthByIconDataFromTxtFormat(param1:int, param2:int) : int
      {
         var _loc7_:int = 0;
         var _loc10_:* = 0;
         var _loc4_:TextFormat = null;
         var _loc8_:String = null;
         var _loc5_:Array = null;
         var _loc6_:IconData = null;
         var _loc3_:* = null;
         var _loc11_:int = param2 - param1;
         if(_loc11_ < 0)
         {
            throw new Error();
         }
         var _loc9_:IconType = new IconType();
         _loc10_ = param1;
         while(_loc10_ < param2)
         {
            _loc4_ = m_textField.getTextFormat(_loc10_);
            _loc8_ = _loc4_.target;
            if(_loc8_.indexOf(m_const_placeHolder) != -1)
            {
               _loc5_ = _loc8_.split(m_const_placeHolder);
               _loc6_ = m_iconDataObjByStr[_loc5_[0]];
               if(_loc6_)
               {
                  _loc7_ += _loc6_.getIconStr().length - m_const_placeHolder.length;
               }
            }
            _loc10_++;
         }
         return _loc7_;
      }
      
      private function createIconDataFromTxtFormat(param1:int, param2:int) : void
      {
         var _loc9_:* = 0;
         var _loc4_:TextFormat = null;
         var _loc7_:String = null;
         var _loc5_:Array = null;
         var _loc6_:IconData = null;
         var _loc3_:ExistIconData = null;
         var _loc10_:int = param2 - param1;
         if(_loc10_ < 0)
         {
            throw new Error();
         }
         var _loc8_:IconType = new IconType();
         _loc9_ = param1;
         while(_loc9_ < param2)
         {
            _loc4_ = m_textField.getTextFormat(_loc9_);
            _loc7_ = _loc4_.target;
            if(_loc7_.indexOf(m_const_placeHolder) != -1)
            {
               _loc5_ = _loc7_.split(m_const_placeHolder);
               _loc6_ = m_iconDataObjByStr[_loc5_[0]];
               if(_loc6_)
               {
                  _loc3_ = new ExistIconData(_loc6_,_loc9_,null,null);
                  addExistIconData(_loc3_);
                  addExistIconShow(_loc3_);
               }
            }
            _loc9_++;
         }
      }
      
      private function deleText(param1:int, param2:int) : void
      {
         m_textField.replaceText(param1,param2,"");
      }
      
      private function deleNotUseExistIcon(param1:int, param2:int) : void
      {
         var _loc4_:int = 0;
         if(param2 > param1)
         {
            throw new Error();
         }
         var _loc3_:int = int(m_nowExistIconDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(m_nowExistIconDatas[_loc4_].getIndex() >= param2 && m_nowExistIconDatas[_loc4_].getIndex() < param1)
            {
               deleOneExistIcon(_loc4_);
               _loc4_--;
               _loc3_--;
            }
            _loc4_++;
         }
      }
      
      private function sortExistIcons(param1:ExistIconData, param2:ExistIconData) : Number
      {
         return param1.getIndex() - param2.getIndex();
      }
      
      private function deleOneExistIcon(param1:int) : void
      {
         m_nowExistIconDataObjByIndex[m_nowExistIconDatas[param1].getIndex()] = null;
         ClearUtil.clearObject(m_nowExistIconDatas[param1].getIconContainer());
         ClearUtil.clearObject(m_nowExistIconDatas[param1]);
         m_nowExistIconDatas.splice(param1,1);
      }
      
      private function foucusInHandler(param1:Event) : void
      {
         resetRefeCaretData();
      }
      
      private function keyHandler(param1:KeyboardEvent) : void
      {
         resetDefaultFormat();
         resetRefeCaretData();
      }
      
      private function scrollHandler(param1:Event) : void
      {
         if(m_textField.text == null || m_textField.text == "")
         {
            return;
         }
         refresh();
      }
      
      private function changeHandler(param1:Event) : void
      {
         trace("change m_textField length:",m_textField.length,"text length:",m_textField.text.length);
         resetDefaultFormat();
         change();
         refresh();
         resetRefeCaretData();
      }
      
      private function resetDefaultFormat() : void
      {
         m_textField.defaultTextFormat = m_defaultFormat;
      }
      
      private function addExistIconData(param1:ExistIconData) : void
      {
         if(m_nowExistIconDataObjByIndex[param1.getIndex()] == null)
         {
            m_nowExistIconDataObjByIndex[param1.getIndex()] = param1;
            m_nowExistIconDatas.push(param1);
            return;
         }
         throw new Error("m_nowExistIconDataObjByIndex[existIconData.getIndex()]中本不应该有数据index:" + param1.getIndex());
      }
      
      private function addExistIconShow(param1:ExistIconData) : void
      {
         var onItemLoaded:Function;
         var existIconData:ExistIconData = param1;
         var iconContainer:MySprite = new MySprite();
         existIconData.setIconContainer(iconContainer);
         onItemLoaded = function():void
         {
            setFormat(existIconData);
            refresh();
         };
         if(existIconData.getIconData() is IconData_MovieClip)
         {
            addMovieClip(existIconData.getIconData() as IconData_MovieClip,iconContainer,onItemLoaded);
         }
         else if(existIconData.getIconData() is IconData_Jpg)
         {
            addJpg(existIconData.getIconData() as IconData_Jpg,iconContainer,onItemLoaded);
         }
         else
         {
            if(!(existIconData.getIconData() is IconData_Gif))
            {
               throw new Error();
            }
            addGif(existIconData.getIconData() as IconData_Gif,iconContainer,onItemLoaded);
         }
         m_upShowContainer.addChild(iconContainer);
      }
      
      private function setFormat(param1:ExistIconData) : void
      {
         param1.setTextFormat(new TextFormat());
         param1.getTextFormat().size = Math.max(param1.getIconContainer().width,param1.getIconContainer().height);
         param1.getTextFormat().target = param1.getIconData().getIconStr() + m_const_placeHolder + param1.getIconData().getIconType() + m_const_placeHolder + param1.getIconContainer().name;
         param1.getTextFormat().letterSpacing = param1.getIconContainer().width - getTxtWidth(param1.getIconContainer().width);
         m_textField.setTextFormat(param1.getTextFormat(),param1.getIndex(),param1.getIndex() + 1);
      }
      
      public function refresh() : void
      {
         var _loc7_:int = 0;
         var _loc1_:ExistIconData = null;
         var _loc5_:Sprite = null;
         var _loc6_:Rectangle = null;
         var _loc3_:TextLineMetrics = null;
         var _loc4_:int = 0;
         var _loc2_:int = int(m_nowExistIconDatas.length);
         _loc7_ = 0;
         while(_loc7_ < _loc2_)
         {
            _loc1_ = m_nowExistIconDatas[_loc7_];
            _loc5_ = _loc1_.getIconContainer();
            if(_loc5_)
            {
               _loc6_ = m_textField.getCharBoundaries(_loc1_.getIndex());
               if(_loc6_)
               {
                  _loc3_ = m_textField.getLineMetrics(m_textField.getLineIndexOfChar(_loc1_.getIndex()));
                  _loc4_ = _loc6_.height * 0.5 > _loc5_.height ? _loc3_.ascent - _loc5_.height : (_loc6_.height - _loc5_.height) * 0.5;
                  _loc5_.visible = true;
                  _loc5_.x = _loc6_.x + (_loc6_.width - _loc5_.width) * 0.5;
                  _loc5_.y = _loc6_.y + _loc4_;
               }
               else
               {
                  _loc5_.visible = false;
               }
            }
            _loc7_++;
         }
         refreshTextFiledSize();
         setContainerPos();
         listenRefresh();
      }
      
      private function refreshTextFiledSize() : void
      {
         if(m_textField.wordWrap)
         {
            m_textField.height = Math.max(m_textField.textHeight + 5,m_minHeight_textField);
         }
         else
         {
            m_textField.width = Math.max(m_textField.textWidth + 5,m_minWidth_textField);
         }
      }
      
      private function listenRefresh() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IRichTextAreaRefreshListener> = m_refreshListeners ? m_refreshListeners.slice(0) : null;
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].refresh();
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_ = null;
      }
      
      private function setContainerPos() : void
      {
         var _loc1_:Object = getTextFieldPos();
         m_upShowContainer.x = m_textField.x + _loc1_.x;
         m_upShowContainer.y = m_textField.y + _loc1_.y;
      }
      
      private function getTextFieldPos() : Object
      {
         var _loc2_:Number = m_textField.scrollH;
         var _loc3_:int = m_textField.scrollV - 1;
         var _loc1_:Number = 0;
         while(_loc3_--)
         {
            _loc1_ += m_textField.getLineMetrics(_loc3_).height;
         }
         return {
            "x":-_loc2_,
            "y":-_loc1_
         };
      }
      
      private function getTxtWidth(param1:int) : int
      {
         var _loc3_:TextField = new TextField();
         var _loc2_:TextFormat = new TextFormat();
         _loc3_.text = m_const_placeHolder;
         _loc2_.size = param1;
         _loc3_.setTextFormat(_loc2_);
         return _loc3_.textWidth - 2;
      }
      
      private function addMovieClip(param1:IconData_MovieClip, param2:MySprite, param3:Function) : void
      {
         var iconData_movieClip:IconData_MovieClip = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         if(m_myLoader == null)
         {
            trace("loader is null, can not load movieclip");
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         }
         else
         {
            m_myLoader.getClass(iconData_movieClip.getSwfPath(),iconData_movieClip.getClassName(),function(param1:YJFYLoaderData):void
            {
               var _loc2_:Class = param1.resultClass;
               var _loc3_:MovieClip = new _loc2_();
               existIconShowContainer.addChild(_loc3_);
               if(Boolean(completeFun))
               {
                  completeFun();
               }
            },function(param1:YJFYLoaderData):void
            {
               drawErrGraphics(existIconShowContainer);
               if(Boolean(completeFun))
               {
                  completeFun();
               }
            });
            m_myLoader.load();
         }
      }
      
      private function addJpg(param1:IconData_Jpg, param2:MySprite, param3:Function) : void
      {
         var onComplete:Function;
         var onError:Function;
         var iconData_jpg:IconData_Jpg = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         var imgLoader:Loader = new Loader();
         existIconShowContainer.addChild(imgLoader);
         m_imageLoaders.push(imgLoader);
         imgLoader.load(new URLRequest(iconData_jpg.getUrl()));
         imgLoader.contentLoaderInfo.addEventListener("complete",onComplete);
         imgLoader.contentLoaderInfo.addEventListener("ioError",onError);
         onComplete = function(param1:Event):void
         {
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         onError = function(param1:Event):void
         {
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
      }
      
      private function addGif(param1:IconData_Gif, param2:MySprite, param3:Function) : void
      {
         var onComplete:Function;
         var onError:Function;
         var iconData_gif:IconData_Gif = param1;
         var existIconShowContainer:MySprite = param2;
         var completeFun:Function = param3;
         var gifPlayer:GIFPlayer = new GIFPlayer();
         existIconShowContainer.addChild(gifPlayer);
         onComplete = function(param1:Object):void
         {
            var _loc2_:Rectangle = param1.rect;
            gifPlayer.play();
            trace("gifPlayer totalFrames:",gifPlayer.totalFrames);
            drawRectGraphics(existIconShowContainer,_loc2_.width,_loc2_.height,false,0);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         onError = function(param1:Event):void
         {
            drawErrGraphics(existIconShowContainer);
            if(Boolean(completeFun))
            {
               completeFun();
            }
         };
         gifPlayer.addEventListener("complete",onComplete);
         gifPlayer.addEventListener("ioError",onError);
         gifPlayer.load(new URLRequest(iconData_gif.getUrl()));
      }
      
      private function searchOneIconDataStrInStr(param1:String, param2:int, param3:int) : ExistIconData
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:* = null;
         if(param2 == -1)
         {
            param2 = 0;
         }
         if(param3 == -1)
         {
            param3 = param1.length;
         }
         param1 = param1.slice(param2,param3);
         var _loc6_:int = int(m_iconDatas.length);
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc5_ = int(param1.indexOf(m_iconDatas[_loc7_].getIconStr()));
            if(_loc5_ != -1)
            {
               _loc5_ += param2;
               return new ExistIconData(m_iconDatas[_loc7_],_loc5_,null,null);
            }
            _loc7_++;
         }
         return null;
      }
      
      private function refreshExistIconDatas(param1:int, param2:int) : void
      {
         var _loc6_:int = 0;
         var _loc3_:ExistIconData = null;
         var _loc4_:int = 0;
         var _loc5_:int = int(m_nowExistIconDatas.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc3_ = m_nowExistIconDatas[_loc6_];
            if(_loc3_.getIndex() >= param1)
            {
               _loc4_ = _loc3_.getIndex();
               _loc3_.setIndex(_loc4_ + param2);
               if(m_nowExistIconDataObjByIndex[_loc4_] == _loc3_)
               {
                  m_nowExistIconDataObjByIndex[_loc4_] = null;
               }
               m_nowExistIconDataObjByIndex[_loc3_.getIndex()] = _loc3_;
            }
            _loc6_++;
         }
      }
      
      private function reLocationCaret(param1:int) : void
      {
         m_textField.setSelection(param1,param1);
      }
      
      private function resetRefeCaretData() : void
      {
         m_refeCaretData = new CaretData(m_textField.selectionBeginIndex,m_textField.selectionBeginIndex,m_textField.length - m_textField.selectionBeginIndex,m_textField.selectionBeginIndex,m_textField.selectionEndIndex);
      }
      
      private function getCurCaretData() : CaretData
      {
         return new CaretData(m_textField.caretIndex,m_textField.caretIndex,m_textField.length - m_textField.caretIndex,m_textField.selectionBeginIndex,m_textField.selectionEndIndex);
      }
      
      private function drawRectGraphics(param1:Sprite, param2:int = 10, param3:int = 10, param4:Boolean = false, param5:int = 1) : void
      {
         if(param4)
         {
            param1.graphics.clear();
         }
         param1.graphics.beginFill(0,param5);
         param1.graphics.drawRect(0,0,param2,param3);
         param1.graphics.endFill();
      }
      
      private function drawErrGraphics(param1:Sprite) : void
      {
         param1.graphics.clear();
         param1.graphics.lineStyle(1,16711680);
         param1.graphics.beginFill(16777215);
         param1.graphics.drawRect(0,0,10,10);
         param1.graphics.moveTo(0,0);
         param1.graphics.lineTo(10,10);
         param1.graphics.moveTo(0,10);
         param1.graphics.lineTo(10,0);
         param1.graphics.endFill();
      }
      
      private function addListeners(param1:TextField) : void
      {
         if(param1)
         {
            param1.addEventListener("keyDown",keyHandler,false,0,true);
            param1.addEventListener("change",changeHandler,false,0,true);
            param1.addEventListener("scroll",scrollHandler,false,0,true);
            param1.addEventListener("focusIn",foucusInHandler,false,0,true);
         }
      }
      
      private function removeListeners(param1:TextField) : void
      {
         if(param1)
         {
            param1.removeEventListener("keyDown",keyHandler,false);
            param1.removeEventListener("change",changeHandler,false);
            param1.removeEventListener("scroll",scrollHandler,false);
            param1.removeEventListener("focusIn",foucusInHandler,false);
         }
      }
      
      private function cloneDefaultTextFormat() : TextFormat
      {
         var _loc2_:TextField = new TextField();
         _loc2_.defaultTextFormat = m_defaultFormat;
         var _loc1_:TextFormat = _loc2_.defaultTextFormat;
         _loc2_ = null;
         return _loc1_;
      }
      
      private function searchRNum(param1:String) : int
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:Array = param1.match("\r");
         _loc5_ = _loc4_ ? _loc4_.length : 0;
         var _loc2_:Array = param1.match("\\\\r");
         _loc3_ = _loc2_ ? _loc2_.length : 0;
         return _loc5_ - _loc3_;
      }
   }
}

