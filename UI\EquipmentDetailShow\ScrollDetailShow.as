package UI.EquipmentDetailShow
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ScrollDetailShow extends EquipmentDetailShow
   {
      
      private var m_scrollCell:Sprite;
      
      private var m_makeTargetCell:Sprite;
      
      private var m_needMoneyText:TextField;
      
      private var m_scrollEq:Equipment;
      
      private var m_makeTargetEq:Equipment;
      
      private var m_materialCells:Vector.<Sprite>;
      
      private var m_needMaterialNumTexts:Vector.<TextField>;
      
      private var m_haveMaterialNumTexts:Vector.<TextField>;
      
      private var m_xianXianTexts:Vector.<TextField>;
      
      private var m_materials:Vector.<Equipment>;
      
      private var m_arrowMc:MovieClipPlayLogicShell;
      
      public function ScrollDetailShow()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_scrollCell = null;
         m_makeTargetCell = null;
         m_needMoneyText = null;
         ClearUtil.clearObject(m_scrollEq);
         m_scrollEq = null;
         ClearUtil.clearObject(m_makeTargetEq);
         m_makeTargetEq = null;
         ClearUtil.nullArr(m_materialCells);
         m_materialCells = null;
         ClearUtil.nullArr(m_needMaterialNumTexts);
         m_needMaterialNumTexts = null;
         ClearUtil.nullArr(m_haveMaterialNumTexts);
         m_haveMaterialNumTexts = null;
         ClearUtil.nullArr(m_xianXianTexts);
         m_xianXianTexts = null;
         ClearUtil.nullArr(m_materials);
         m_materials = null;
         ClearUtil.clearObject(m_arrowMc);
         m_arrowMc = null;
      }
      
      override protected function initShow() : void
      {
         var _loc1_:TextFormat = null;
         var _loc6_:* = 0;
         var _loc5_:Equipment = null;
         var _loc4_:int = 0;
         super.initShow();
         m_showMC.gotoAndStop("scroll");
         m_scrollCell = m_show["scrollCell"];
         m_makeTargetCell = m_show["makeTargetCell"];
         m_needMoneyText = m_show["needMoneyText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_needMoneyText);
         m_arrowMc = new MovieClipPlayLogicShell();
         m_arrowMc.setShow(m_show["arrowShow"]);
         m_materialCells = new Vector.<Sprite>();
         m_needMaterialNumTexts = new Vector.<TextField>();
         m_haveMaterialNumTexts = new Vector.<TextField>();
         m_xianXianTexts = new Vector.<TextField>();
         _loc6_ = 0;
         while(_loc6_ < 4)
         {
            m_materialCells.push(m_show["materialCell" + (_loc6_ + 1)]);
            m_haveMaterialNumTexts.push(m_show["haveNumText" + (_loc6_ + 1)]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_haveMaterialNumTexts[_loc6_]);
            m_needMaterialNumTexts.push(m_show["needNumText" + (_loc6_ + 1)]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_needMaterialNumTexts[_loc6_]);
            m_xianXianTexts.push(m_show["xianXian" + (_loc6_ + 1)]);
            _loc6_++;
         }
         m_scrollEq = MyFunction2.sheatheEquipmentShell(m_equipmentVO);
         m_scrollEq.addEventListener("rollOver",onOverForNormal,false,0,true);
         m_scrollEq.addEventListener("rollOut",onOutForNormal,false,0,true);
         m_scrollCell.addChild(m_scrollEq);
         m_makeTargetEq = MyFunction2.sheatheEquipmentShell((m_equipmentVO as ScrollEquipmentVO).compositeEquipmentVO);
         m_makeTargetEq.addEventListener("rollOver",onOverForShop,false,0,true);
         m_makeTargetEq.addEventListener("rollOut",onOutForShop,false,0,true);
         m_makeTargetCell.addChild(m_makeTargetEq);
         var _loc3_:int = int((m_equipmentVO as ScrollEquipmentVO).materialVOs.length);
         m_materials = new Vector.<Equipment>();
         var _loc2_:Boolean = true;
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc5_ = MyFunction2.sheatheEquipmentShell((m_equipmentVO as ScrollEquipmentVO).materialVOs[_loc6_]);
            _loc5_.addEventListener("rollOver",onOverForShop,false,0,true);
            _loc5_.addEventListener("rollOut",onOutForShop,false,0,true);
            m_materialCells[_loc6_].visible = true;
            m_needMaterialNumTexts[_loc6_].visible = true;
            m_haveMaterialNumTexts[_loc6_].visible = true;
            m_xianXianTexts[_loc6_].visible = true;
            m_materialCells[_loc6_].addChild(_loc5_);
            m_needMaterialNumTexts[_loc6_].text = (m_equipmentVO as ScrollEquipmentVO).requiredNums[_loc6_].toString();
            _loc1_ = m_haveMaterialNumTexts[_loc6_].defaultTextFormat;
            _loc4_ = m_player.playerVO.getHaveEquipmentNumInPackage(_loc5_.equipmentVO);
            if(_loc4_ < (m_equipmentVO as ScrollEquipmentVO).requiredNums[_loc6_])
            {
               _loc1_.color = 16711680;
               _loc2_ = false;
            }
            else
            {
               _loc1_.color = 65331;
            }
            m_haveMaterialNumTexts[_loc6_].defaultTextFormat = _loc1_;
            m_haveMaterialNumTexts[_loc6_].text = _loc4_.toString();
            m_materials.push(_loc5_);
            _loc6_++;
         }
         _loc6_ = _loc6_;
         while(_loc6_ < 4)
         {
            m_needMaterialNumTexts[_loc6_].visible = false;
            m_haveMaterialNumTexts[_loc6_].visible = false;
            m_xianXianTexts[_loc6_].visible = false;
            _loc6_++;
         }
         _loc1_ = m_needMoneyText.defaultTextFormat;
         if(m_player.playerVO.money < (m_equipmentVO as ScrollEquipmentVO).requiredMoney)
         {
            _loc1_.color = 16711680;
            _loc2_ = false;
         }
         else
         {
            _loc1_.color = 65331;
         }
         m_needMoneyText.defaultTextFormat = _loc1_;
         m_needMoneyText.text = (m_equipmentVO as ScrollEquipmentVO).requiredMoney.toString();
         if(_loc2_)
         {
            m_arrowMc.gotoAndStop("able");
         }
         else
         {
            m_arrowMc.gotoAndStop("unAble");
         }
      }
   }
}

