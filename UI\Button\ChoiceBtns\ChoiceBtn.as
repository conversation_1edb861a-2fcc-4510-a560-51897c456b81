package UI.Button.ChoiceBtns
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   
   public class ChoiceBtn extends MySprite
   {
      
      protected var m_btn:ButtonLogicShell;
      
      public function ChoiceBtn()
      {
         super();
         init();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         super.clear();
         ClearUtil.clearObject(m_btn);
         m_btn = null;
      }
      
      protected function init() : void
      {
      }
      
      protected function clickButton(param1:ButtonEvent) : void
      {
      }
   }
}

