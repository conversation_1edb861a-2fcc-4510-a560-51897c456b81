package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.GameEntity.XydzjsPlayerAndPet.ZiXia;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneBullet;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   import flash.display.DisplayObject;
   
   public class Skill_ZiXiaSkill1 extends CuboidAreaAttackSkill_OneBullet
   {
      
      private const m_const_skill1AnimationDefId:String = "zixiaSkill1Body";
      
      private const m_const_juQiingAnimationDefId:String = "zixiaJuQiing";
      
      private const m_const_releaseJianGuangAnimationDefId:String = "zixiaJianGuang";
      
      private const m_const_releaseJianqiEffectAnimationDefId:String = "zixiaQianqiEffect";
      
      private const m_const_juQiCompleteAnimationDefId:String = "zixiaJuQiComplete";
      
      private const m_const_juQiCompleteLabel:String = "juQi^Stop^";
      
      private const m_const_juQiLabel:String = "juQi";
      
      private const m_const_juQiCompleteZiStopLabel:String = "zi^stop^";
      
      private const m_const_attackAnimationJuQiFrame:String = "juQiFrame";
      
      private const m_const_attackReachAnimationFrame:String = "skillReach";
      
      private var m_createBulletFrameLabel:String = "createJianQi";
      
      private const m_const_attackReach1AnimationFrame:String = "skill1Reach";
      
      private const m_const_releaseJianGuangAnimationFrame:String = "releaseJianGuang";
      
      private const m_const_releaseJianGuangEndAnimationFrame:String = "releaseJianGuang^Stop^";
      
      private const m_const_JianqiEffectAnimationFrame:String = "JianqiEffect^Stop^";
      
      private const m_const_skill1EndFrame:String = "qianqianJEnd^stop^";
      
      private var m_bIsAbleJuQi:Boolean;
      
      private var m_qianqianJAnimationShowPlay:AnimationShowPlayLogicShell;
      
      private var m_juQiingAnimationShowPlay:AnimationShowPlayLogicShell;
      
      private var m_juQiCompleteAnimationShowPlay:AnimationShowPlayLogicShell;
      
      private var m_juQiCompleteEffectAnimationShowPlay:AnimationShowPlayLogicShell;
      
      private var m_qianqianJAnimationShowPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_juQiingAnimationShowPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_juQiCompleteEffectListener:AnimationPlayFrameLabelListener;
      
      private var m_zixiaEntityAnimationPlayFrameLableListener:AnimationPlayFrameLabelListener;
      
      private var m_zixiaSkill1Listener:ZiXiaSkill1Listener;
      
      private var m_nTime:Number = 0;
      
      private var m_nStartPressTime:Number = 0;
      
      private var m_bIsPress:Boolean;
      
      private const MIN_ATTACK_TIME:Number = 200;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_bIsCompleteJuQi:Boolean;
      
      public function Skill_ZiXiaSkill1()
      {
         super();
         m_bIsAbleJuQi = true;
         m_bIsCompleteJuQi = false;
         m_qianqianJAnimationShowPlay = new AnimationShowPlayLogicShell();
         m_juQiingAnimationShowPlay = new AnimationShowPlayLogicShell();
         m_juQiCompleteAnimationShowPlay = new AnimationShowPlayLogicShell();
         m_juQiCompleteEffectAnimationShowPlay = new AnimationShowPlayLogicShell();
         m_qianqianJAnimationShowPlayListener = new AnimationPlayFrameLabelListener();
         m_qianqianJAnimationShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_qianqianJAnimationShowPlay.addFrameLabelListener(m_qianqianJAnimationShowPlayListener);
         m_juQiingAnimationShowPlayListener = new AnimationPlayFrameLabelListener();
         m_juQiingAnimationShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_juQiingAnimationShowPlay.addFrameLabelListener(m_juQiingAnimationShowPlayListener);
         m_juQiCompleteEffectListener = new AnimationPlayFrameLabelListener();
         m_juQiCompleteEffectListener.reachFrameLabelFun = reachFrameLabel;
         m_juQiCompleteEffectAnimationShowPlay.addFrameLabelListener(m_juQiCompleteEffectListener);
         m_zixiaEntityAnimationPlayFrameLableListener = new AnimationPlayFrameLabelListener();
         m_zixiaEntityAnimationPlayFrameLableListener.reachFrameLabelFun = reachFrameLabel;
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = attackSuccess;
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun == false)
         {
            return;
         }
         if(!ZiXia.m_bIsPress)
         {
            m_owner.removeOtherAnimation(m_juQiCompleteAnimationShowPlay);
            m_owner.removeOtherAnimation(m_juQiingAnimationShowPlay);
         }
      }
      
      public function removeAnimation() : void
      {
         m_owner.removeOtherAnimation(m_juQiCompleteAnimationShowPlay);
         m_owner.removeOtherAnimation(m_juQiingAnimationShowPlay);
         m_owner.removeOtherAnimation(m_qianqianJAnimationShowPlay);
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_juQiCompleteEffectAnimationShowPlay);
         m_juQiCompleteEffectAnimationShowPlay = null;
         ClearUtil.clearObject(m_qianqianJAnimationShowPlay);
         m_qianqianJAnimationShowPlay = null;
         ClearUtil.clearObject(m_juQiingAnimationShowPlay);
         m_juQiingAnimationShowPlay = null;
         ClearUtil.clearObject(m_juQiCompleteAnimationShowPlay);
         m_juQiCompleteAnimationShowPlay = null;
         ClearUtil.clearObject(m_juQiingAnimationShowPlayListener);
         m_juQiingAnimationShowPlayListener = null;
         ClearUtil.clearObject(m_juQiCompleteEffectListener);
         m_juQiCompleteEffectListener = null;
         ClearUtil.clearObject(m_zixiaEntityAnimationPlayFrameLableListener);
         m_zixiaEntityAnimationPlayFrameLableListener = null;
         ClearUtil.clearObject(m_zixiaSkill1Listener);
         m_zixiaSkill1Listener = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         if(m_isOutWorldTime)
         {
            m_endTime = param1.getWorldExistTime() + m_timeOfDuration;
         }
         else
         {
            m_endTime = param1.getWorldTime() + m_timeOfDuration;
         }
         listenerRealseSkill();
         releaseSkill_in();
         m_owner.hideShadow();
      }
      
      private function juQiing() : void
      {
         m_juQiingAnimationShowPlay.setShow(m_owner.getAnimationByDefId("zixiaJuQiing"),true);
         (m_juQiingAnimationShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         m_juQiingAnimationShowPlay.gotoAndPlay("1");
         m_owner.addOtherAniamtion(m_juQiingAnimationShowPlay);
      }
      
      private function releaseJianGuang() : void
      {
         m_qianqianJAnimationShowPlay.setShow(m_owner.getAnimationByDefId("zixiaJianGuang"),true);
         (m_qianqianJAnimationShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         m_qianqianJAnimationShowPlay.gotoAndPlay("1");
         m_owner.addOtherAniamtion(m_qianqianJAnimationShowPlay);
      }
      
      private function releaseJianqiEffect() : void
      {
         m_juQiCompleteEffectAnimationShowPlay.setShow(m_owner.getAnimationByDefId("zixiaQianqiEffect"),true);
         (m_juQiCompleteEffectAnimationShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         m_juQiCompleteEffectAnimationShowPlay.gotoAndPlay("1");
         m_owner.addOtherAniamtion(m_juQiCompleteEffectAnimationShowPlay);
      }
      
      private function juQiComplete() : void
      {
         m_owner.removeOtherAnimation(m_juQiingAnimationShowPlay);
         m_juQiCompleteAnimationShowPlay.setShow(m_owner.getAnimationByDefId("zixiaJuQiComplete"),true);
         m_juQiCompleteAnimationShowPlay.gotoAndPlay("1");
         (m_juQiCompleteAnimationShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         m_owner.addOtherAniamtion(m_juQiCompleteAnimationShowPlay);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         if(param1 == "releaseJianGuang^Stop^")
         {
            m_owner.removeOtherAnimation(m_qianqianJAnimationShowPlay);
         }
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "juQiFrame":
               if(ZiXia.m_bIsAbleJuQi)
               {
                  juQiing();
                  m_owner.currentAnimationStop();
               }
               break;
            case "juQi":
               m_bIsCompleteJuQi = false;
               break;
            case "releaseJianGuang":
               if(!m_bIsCompleteJuQi)
               {
                  this.releaseJianGuang();
               }
               break;
            case "JianqiEffect^Stop^":
               m_owner.removeOtherAnimation(m_juQiCompleteEffectAnimationShowPlay);
               break;
            case "juQi^Stop^":
               m_bIsCompleteJuQi = true;
               juQiComplete();
               break;
            case m_createBulletFrameLabel:
               if(m_bIsCompleteJuQi)
               {
                  this.releaseJianqiEffect();
                  createBulletEntity();
                  m_bIsCompleteJuQi = false;
               }
               break;
            case "qianqianJEnd^stop^":
               endSkill2();
               break;
            case "skill1Reach":
               attackReach(m_owner.getWorld());
         }
      }
   }
}

