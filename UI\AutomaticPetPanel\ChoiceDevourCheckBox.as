package UI.AutomaticPetPanel
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.ShowLogicShell.CheckBox.CheckBoxLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.InteractiveObject;
   import flash.text.TextField;
   
   public class ChoiceDevourCheckBox extends CheckBoxLogicShell
   {
      
      private var m_petInforShowMC:MovieClipPlayLogicShell;
      
      private var m_pictruecontainerMC:MovieClipPlayLogicShell;
      
      private var m_devourExpText:TextField;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      public function ChoiceDevourCheckBox()
      {
         super();
         m_petInforShowMC = new MovieClipPlayLogicShell();
         m_pictruecontainerMC = new MovieClipPlayLogicShell();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_petInforShowMC);
         m_petInforShowMC = null;
         ClearUtil.clearObject(m_pictruecontainerMC);
         m_pictruecontainerMC = null;
         m_devourExpText = null;
         m_automaticPetVO = null;
         super.clear();
      }
      
      override public function setShow(param1:InteractiveObject) : void
      {
         super.setShow(param1);
         initShow();
         initShow2();
      }
      
      public function setData(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         initShow2();
      }
      
      public function getAutomaticPetVO() : AutomaticPetVO
      {
         return m_automaticPetVO;
      }
      
      private function initShow() : void
      {
         m_petInforShowMC.setShow(m_show["petInforShow"]);
         m_pictruecontainerMC.setShow(m_petInforShowMC.getShow()["petShow"]);
         m_devourExpText = m_petInforShowMC.getShow()["devourExpText"];
         m_devourExpText.mouseEnabled = false;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_devourExpText);
      }
      
      private function initShow2() : void
      {
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_pictruecontainerMC.gotoAndStop(m_automaticPetVO.getId());
         m_devourExpText.text = m_automaticPetVO.getDevourExp().toString();
      }
   }
}

