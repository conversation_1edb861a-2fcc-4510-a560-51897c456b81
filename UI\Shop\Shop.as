package UI.Shop
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.InitUI;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Shop.Button.MoneyShopSwitchBtn;
   import UI.Shop.Button.PKShopSwitchBtn;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class Shop extends MySprite
   {
      
      public var moneyShopSwitchBtn:MoneyShopSwitchBtn;
      
      public var pKShopSwitchBtn:PKShopSwitchBtn;
      
      private var _btnArr:Vector.<SwitchBtn> = new Vector.<SwitchBtn>();
      
      private var _shopArr:Vector.<ShopOne> = new Vector.<ShopOne>();
      
      private var _moneyShop:ShopOne;
      
      private var _pkShop:ShopOne;
      
      public function Shop()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         if(moneyShopSwitchBtn)
         {
            moneyShopSwitchBtn.clear();
         }
         moneyShopSwitchBtn = null;
         if(pKShopSwitchBtn)
         {
            pKShopSwitchBtn.clear();
         }
         pKShopSwitchBtn = null;
         var _loc3_:int = 0;
         if(_btnArr)
         {
            _loc1_ = int(_btnArr.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _btnArr[_loc3_] = null;
               _loc3_++;
            }
            _btnArr = null;
         }
         if(_shopArr)
         {
            _loc1_ = int(_shopArr.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _shopArr[_loc3_] = null;
               _loc3_++;
            }
         }
         _shopArr = null;
         if(_moneyShop)
         {
            _moneyShop.clear();
         }
         if(pkShop)
         {
            _pkShop.clear();
         }
         _moneyShop = null;
         _pkShop = null;
      }
      
      public function get moneyShop() : ShopOne
      {
         return _moneyShop;
      }
      
      public function get pkShop() : ShopOne
      {
         return _pkShop;
      }
      
      public function addGoodsToShop() : void
      {
         if(_moneyShop.equipmentsArr == null || _pkShop.equipmentsArr == null || _moneyShop.equipmentsArr.length == 0 || _pkShop.equipmentsArr.length == 0)
         {
            MyFunction2.loadXMLFunction("Shop",function(param1:XML):void
            {
               var _loc2_:String = MyFunction2.getLocalTime();
               var _loc3_:Vector.<Vector.<Vector.<EquipmentVO>>> = initShopGoods(param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,_loc2_);
               _moneyShop.addGoods(_loc3_[0]);
               _pkShop.addGoods(_loc3_[1]);
               _loc3_[0] = null;
               _loc3_[1] = null;
               _loc3_ = null;
            },function(param1:String, param2:int):void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":param1,
                  "flag":0
               }));
            },true);
         }
      }
      
      private function initShopGoods(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : Vector.<Vector.<Vector.<EquipmentVO>>>
      {
         var _loc8_:Vector.<Vector.<Vector.<EquipmentVO>>> = new Vector.<Vector.<Vector.<EquipmentVO>>>();
         var _loc6_:Vector.<Vector.<EquipmentVO>> = new Vector.<Vector.<EquipmentVO>>();
         _loc6_[0] = null;
         _loc6_[1] = getEquipmentVOs(param1.MoneyShop.Equipments[0],param2,param3,param4,param5);
         _loc6_[2] = getEquipmentVOs(param1.MoneyShop.Gems[0],param2,param3,param4,param5);
         _loc6_[3] = getEquipmentVOs(param1.MoneyShop.Other[0],param2,param3,param4,param5);
         _loc6_[0] = _loc6_[1].concat(_loc6_[2]);
         _loc6_[0] = _loc6_[0].concat(_loc6_[3]);
         var _loc7_:Vector.<Vector.<EquipmentVO>> = new Vector.<Vector.<EquipmentVO>>();
         _loc7_[0] = getEquipmentVOs(param1.PKShop.Scrolls[0],param2,param3,param4,param5);
         _loc7_[1] = getEquipmentVOs(param1.PKShop.Materials[0],param2,param3,param4,param5);
         _loc7_[2] = getEquipmentVOs(param1.PKShop.Other[0],param2,param3,param4,param5);
         _loc8_.push(_loc6_);
         _loc8_.push(_loc7_);
         return _loc8_;
      }
      
      public function getEquipmentVOs(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : Vector.<EquipmentVO>
      {
         var _loc7_:int = 0;
         var _loc6_:EquipmentVO = null;
         var _loc8_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc10_:XMLList = param1.children();
         for each(var _loc9_ in _loc10_)
         {
            _loc6_ = InitUI.getInstance().getBaseEquipmentVO(_loc9_,param2,param3,param4,param5);
            if(_loc6_ && _loc6_.isOverdue && _loc6_.isOverdue == 1)
            {
               _loc8_.push(null);
            }
            else
            {
               _loc8_.push(_loc6_);
            }
         }
         MyFunction.getInstance().dealWithDataEquipmentVOFromEquipmentVOs(_loc8_,param5);
         return _loc8_;
      }
      
      private function init() : void
      {
         moneyShopSwitchBtn.init(false);
         pKShopSwitchBtn.init(true);
         _btnArr.push(moneyShopSwitchBtn);
         _btnArr.push(pKShopSwitchBtn);
         _moneyShop = new ShopOne("moneyShop",6);
         _moneyShop.x = 37.5;
         _moneyShop.y = 51;
         addChild(_moneyShop);
         _pkShop = new ShopOne("pk点商店",5);
         _pkShop.x = 37.5;
         _pkShop.y = 51;
         _shopArr.push(_moneyShop);
         _shopArr.push(_pkShop);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchShop",switchShop,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchShop",switchShop,true);
      }
      
      private function switchShop(param1:UIBtnEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(_btnArr.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_btnArr[_loc3_] == param1.target)
            {
               addChild(_shopArr[_loc3_]);
            }
            else
            {
               removeChild(_shopArr[_loc3_]);
               _btnArr[_loc3_].gotoTwoFrame();
            }
            _loc3_++;
         }
      }
   }
}

