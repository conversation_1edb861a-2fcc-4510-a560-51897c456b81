package YJFY.Logic
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Skill.Skill;
   import YJFY.Utils.ClearUtil;
   
   public class SkillContainer_Sort_RunAndAbleRunInHurt
   {
      
      protected var m_skills_ablerunInHurt_sort:Vector.<Skill>;
      
      protected var m_skills_run:Vector.<Skill>;
      
      public function SkillContainer_Sort_RunAndAbleRunInHurt()
      {
         super();
         m_skills_ablerunInHurt_sort = new Vector.<Skill>();
         m_skills_run = new Vector.<Skill>();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_skills_ablerunInHurt_sort,false,false,false);
         m_skills_ablerunInHurt_sort = null;
         ClearUtil.nullArr(m_skills_run,false,false,false);
         m_skills_run = null;
      }
      
      public function statAndSortSkill(param1:IAnimalEntity, param2:IAnimalEntity = null) : void
      {
         sortPrioritySkills(param1,param2);
      }
      
      public function getActiveSkillNumOfAbleRunInHurt() : uint
      {
         return m_skills_ablerunInHurt_sort.length;
      }
      
      public function getActiveSkillOfAbleRunInHurtByIndex(param1:uint) : Skill
      {
         return m_skills_ablerunInHurt_sort[param1];
      }
      
      public function getActiveSkillNumOfRun() : uint
      {
         return m_skills_run.length;
      }
      
      public function getActiveSkillOfRunByIndex(param1:uint) : Skill
      {
         return m_skills_run[param1];
      }
      
      private function sortPrioritySkills(param1:IAnimalEntity, param2:IAnimalEntity) : void
      {
         var _loc6_:int = 0;
         var _loc3_:Skill = null;
         var _loc5_:IActiveSkillVO_HaveAiNeedSkillInfor = null;
         m_skills_ablerunInHurt_sort.length = 0;
         m_skills_run.length = 0;
         if(param1 == null)
         {
            return;
         }
         var _loc4_:int = param1.getSkillNum();
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_ = param1.getSkillByIndex(_loc6_);
            _loc5_ = _loc3_.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor;
            if(_loc5_ && _loc5_.getIsAbleRun())
            {
               if(_loc5_.getIsAbleRunInHurt())
               {
                  m_skills_ablerunInHurt_sort.push(_loc3_);
               }
               if(_loc5_ && _loc5_.getPriorityForRun() < 1000)
               {
                  m_skills_run.push(_loc3_);
               }
            }
            _loc6_++;
         }
         if(param2)
         {
            _loc4_ = param2.getSkillNum();
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               _loc3_ = param2.getSkillByIndex(_loc6_);
               _loc5_ = _loc3_.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor;
               if(_loc5_)
               {
                  if(_loc5_.getIsAbleRunInHurt())
                  {
                     m_skills_ablerunInHurt_sort.push(_loc3_);
                  }
                  m_skills_run.push(_loc3_);
               }
               _loc6_++;
            }
         }
         m_skills_ablerunInHurt_sort.sort(sortActiveSkillVOsOfAbleRunInHurt);
         m_skills_run.sort(sortActiveSkillVOs);
      }
      
      private function sortActiveSkillVOsOfAbleRunInHurt(param1:Skill, param2:Skill) : Number
      {
         return (param2.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor).getPriorityForRunInHurt() - (param1.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor).getPriorityForRunInHurt();
      }
      
      private function sortActiveSkillVOs(param1:Skill, param2:Skill) : Number
      {
         return (param2.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor).getPriorityForRun() - (param1.getExtra() as IActiveSkillVO_HaveAiNeedSkillInfor).getPriorityForRun();
      }
   }
}

