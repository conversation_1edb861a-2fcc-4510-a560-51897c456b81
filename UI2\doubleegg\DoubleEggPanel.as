package UI2.doubleegg
{
   import UI.EnterFrameTime;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.GetEquipmentVOsLogic.EquipmentVOsData;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import YJFY.ActivityMode.ActivityManager;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class DoubleEggPanel extends MovieClip
   {
      
      private var m_myloader:YJFYLoader;
      
      private var m_show:MovieClip;
      
      private var m_choosePanel:MovieClip;
      
      private var m_btnOK:ButtonLogicShell2;
      
      private var m_eqCells:Vector.<MovieClip>;
      
      private var m_checkList:Vector.<MovieClip>;
      
      private var m_index:int = -1;
      
      private var m_num:int;
      
      private var m_btnGetlist:Vector.<ButtonLogicShell2>;
      
      private var m_closebtn:ButtonLogicShell2;
      
      private var equipmentVOsData:EquipmentVOsData;
      
      private var chooseEquipmentVOsData:EquipmentVOsData;
      
      private var equipments:Vector.<Equipment>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var equipDayData:EquipmentVOsData;
      
      private var equipVos:Vector.<EquipmentVO>;
      
      private var m_isInTime:Boolean;
      
      public function DoubleEggPanel()
      {
         super();
         Part1.getInstance().showGameWaitShow();
         m_btnGetlist = new Vector.<ButtonLogicShell2>();
         equipmentVOsData = new EquipmentVOsData();
         chooseEquipmentVOsData = new EquipmentVOsData();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_btnOK = new ButtonLogicShell2();
         m_eqCells = new Vector.<MovieClip>();
         m_checkList = new Vector.<MovieClip>();
         this.name = "DoubleEggPanel";
         m_myloader = new YJFYLoader();
         m_myloader.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myloader.getXML("EmbedXMLs/doubleegg.xml",getXMLSuccess,getFail);
         m_myloader.getClass("UISprite2/DoubleEgg.swf","DoubleEggPanel",getShowSuccess,getFail);
         m_myloader.load();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var loaderData:YJFYLoaderData = param1;
         XMLSingle.getInstance().doubleegg = XML(loaderData.resultXML);
         m_num++;
         if(m_num >= 2)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               initPanel(param1);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
               Part1.getInstance().hideGameWaitShow();
            },true);
         }
      }
      
      private function initPanel(param1:String) : void
      {
         var _loc6_:int = 0;
         var _loc2_:ButtonLogicShell2 = null;
         var _loc4_:Equipment = null;
         var _loc7_:XML = XMLSingle.getInstance().doubleegg.date[0];
         if(TimeUtil.getTimeUtil().getInTime(_loc7_.@startTime,_loc7_.@endTime,param1))
         {
            m_isInTime = true;
         }
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_closebtn = new ButtonLogicShell2();
         m_closebtn.setShow(m_show["closebtn"]);
         m_closebtn.setTipString("点击关闭");
         m_choosePanel = m_show["choosePanel"] as MovieClip;
         m_choosePanel.visible = false;
         m_btnOK.setShow(m_choosePanel["btnOK"] as MovieClip);
         m_btnOK.setTipString("确定");
         m_eqCells.length = 0;
         _loc6_ = 0;
         while(_loc6_ < 7)
         {
            m_eqCells.push(m_choosePanel["eqcell_" + (_loc6_ + 1)] as MovieClip);
            m_checkList.push(m_eqCells[_loc6_]["checkbg"] as MovieClip);
            m_checkList[_loc6_].visible = false;
            m_eqCells[_loc6_].addEventListener("click",callback);
            _loc6_++;
         }
         var _loc3_:Boolean = TimeUtil.getTimeUtil().newDateIsNewDay(ActivityManager.getInstance().getDoubleEggTime,param1);
         _loc6_ = 1;
         while(_loc6_ <= 8)
         {
            _loc2_ = new ButtonLogicShell2();
            _loc2_.setShow(m_show["btn_" + _loc6_]);
            m_btnGetlist.push(_loc2_);
            if(ActivityManager.getInstance().getDoubleEggNum() >= _loc6_)
            {
               _loc2_.lock();
               (_loc2_.getShow() as MovieClip).gotoAndStop("geted");
            }
            else if(_loc6_ - ActivityManager.getInstance().getDoubleEggNum() == 1 && _loc3_ && m_isInTime)
            {
               (_loc2_.getShow() as MovieClip).gotoAndStop(1);
            }
            else
            {
               _loc2_.lock();
               (_loc2_.getShow() as MovieClip).gotoAndStop("weidabiao");
            }
            _loc6_++;
         }
         ClearUtil.clearObject(equipments);
         equipments = null;
         equipments = new Vector.<Equipment>();
         var _loc5_:XML = XMLSingle.getInstance().doubleegg.loginAward[0];
         equipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(_loc5_,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
         _loc6_ = 0;
         while(_loc6_ < 7)
         {
            _loc4_ = MyFunction2.sheatheEquipmentShell(equipmentVOsData.getEquipmentVOByIndex(_loc6_).clone());
            m_show["cell_" + (_loc6_ + 1)].addChild(_loc4_);
            equipments.push(_loc4_);
            _loc4_.addEventListener("rollOver",onOver2,false,0,true);
            _loc4_.addEventListener("rollOut",onOut2,false,0,true);
            _loc6_++;
         }
         Part1.getInstance().hideGameWaitShow();
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var loaderData:YJFYLoaderData = param1;
         var cla:Class = loaderData.resultClass;
         m_show = new cla();
         this.addChild(m_show);
         m_num++;
         if(m_num >= 2)
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               initPanel(param1);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("服务器时间加载失败");
            },true);
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      public function CloseUI(param1:MouseEvent = null) : void
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         clear();
         GamingUI.getInstance().clearDoubleEggPanel();
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         m_show.removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_myloader);
         m_myloader = null;
         ClearUtil.clearObject(m_closebtn);
         m_closebtn = null;
         ClearUtil.clearObject(m_btnGetlist);
         m_btnGetlist = null;
         ClearUtil.clearObject(equipmentVOsData);
         equipmentVOsData = null;
         ClearUtil.clearObject(chooseEquipmentVOsData);
         chooseEquipmentVOsData = null;
         ClearUtil.clearObject(equipments);
         equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_btnOK);
         m_btnOK = null;
         _loc1_ = 0;
         while(_loc1_ < 7)
         {
            m_eqCells[_loc1_].removeEventListener("click",callback);
            m_eqCells[_loc1_] = null;
            _loc1_++;
         }
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         ClearUtil.clearObject(m_checkList);
         m_checkList = null;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var spStr:Array;
         var e:ButtonEvent = param1;
         if(e.target.name == "closebtn")
         {
            this.CloseUI();
            return;
         }
         if(e.target.name == "btnOK")
         {
            if(m_index > 0)
            {
               if(ActivityManager.getInstance().getDoubleEggNum() != 7)
               {
                  showWarningBox("领取条件不满足",0);
                  return;
               }
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  var _loc2_:SaveTaskInfo = null;
                  if(!TimeUtil.getTimeUtil().newDateIsNewDay(ActivityManager.getInstance().getDoubleEggTime,param1))
                  {
                     showWarningBox("不是新的一天，请关闭活动页面重试",0);
                     return;
                  }
                  if(!m_isInTime)
                  {
                     showWarningBox("活动未开启",0);
                     return;
                  }
                  equipDayData = new EquipmentVOsData();
                  equipVos = new Vector.<EquipmentVO>();
                  equipVos.push(chooseEquipmentVOsData.getEquipmentVOByIndex(m_index - 1).clone());
                  equipDayData.setEquipmentVOs(equipVos);
                  if(m_getEquipmentVOsLogic.getEquipmentVOs(equipDayData,GamingUI.getInstance().player1))
                  {
                     showWarningBox("获取礼包成功",0);
                     (m_btnGetlist[7] as ButtonLogicShell2).lock();
                     ((m_btnGetlist[7] as ButtonLogicShell2).getShow() as MovieClip).gotoAndStop("geted");
                     ActivityManager.getInstance().addDoubleEgg();
                     ActivityManager.getInstance().getDoubleEggTime = param1;
                     m_choosePanel.visible = false;
                     _loc2_ = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  }
                  else
                  {
                     showWarningBox("背包已满, 不能获取礼包",0);
                  }
               },showWarningBox);
            }
            else
            {
               GamingUI.getInstance().showMessageTip("请选择其中一个奖励!");
            }
         }
         else
         {
            spStr = String(e.target.name).split("_");
            if(spStr.length > 0 && spStr[0] == "btn")
            {
               if(ActivityManager.getInstance().getDoubleEggNum() < 0 || ActivityManager.getInstance().getDoubleEggNum() >= 8)
               {
                  showWarningBox("您已领取完所有礼包",0);
                  return;
               }
               if(!m_isInTime)
               {
                  showWarningBox("活动未开启",0);
                  return;
               }
               MyFunction2.getServerTimeFunction(function(param1:String):void
               {
                  var _loc2_:SaveTaskInfo = null;
                  var _loc4_:XML = null;
                  var _loc3_:Equipment = null;
                  var _loc5_:int = 0;
                  if(!TimeUtil.getTimeUtil().newDateIsNewDay(ActivityManager.getInstance().getDoubleEggTime,param1))
                  {
                     showWarningBox("不是新的一天，请关闭活动页面重试",0);
                     return;
                  }
                  equipDayData = new EquipmentVOsData();
                  equipVos = new Vector.<EquipmentVO>();
                  if(ActivityManager.getInstance().getDoubleEggNum() < 7)
                  {
                     equipVos.push(equipmentVOsData.getEquipmentVOByIndex(ActivityManager.getInstance().getDoubleEggNum()).clone());
                     equipDayData.setEquipmentVOs(equipVos);
                     if(!m_getEquipmentVOsLogic.getEquipmentVOs(equipDayData,GamingUI.getInstance().player1))
                     {
                        showWarningBox("背包已满, 不能获取礼包",0);
                        return;
                     }
                     showWarningBox("获取礼包成功",0);
                     (e.button as ButtonLogicShell2).lock();
                     ((e.button as ButtonLogicShell2).getShow() as MovieClip).gotoAndStop("geted");
                     ActivityManager.getInstance().addDoubleEgg();
                     ActivityManager.getInstance().getDoubleEggTime = param1;
                     _loc2_ = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  }
                  else if(ActivityManager.getInstance().getDoubleEggNum() == 7)
                  {
                     m_choosePanel.visible = true;
                     equipments = null;
                     equipments = new Vector.<Equipment>();
                     _loc4_ = XMLSingle.getInstance().doubleegg.fullAttendanceAward[0];
                     chooseEquipmentVOsData.setEquipmentVOs(XMLSingle.getEquipmentVOs(_loc4_,XMLSingle.getInstance().equipmentXML,true,GamingUI.getInstance().getNewestTimeStrFromSever()));
                     _loc5_ = 0;
                     while(_loc5_ < 7)
                     {
                        _loc3_ = MyFunction2.sheatheEquipmentShell(chooseEquipmentVOsData.getEquipmentVOByIndex(_loc5_).clone());
                        m_choosePanel["eqcell_" + (_loc5_ + 1)].addChild(_loc3_);
                        equipments.push(_loc3_);
                        _loc3_.addEventListener("rollOver",onOver2,false,0,true);
                        _loc3_.addEventListener("rollOut",onOut2,false,0,true);
                        _loc5_++;
                     }
                  }
               },showWarningBox);
            }
         }
      }
      
      private function callback(param1:MouseEvent) : void
      {
         m_index = int(String(param1.currentTarget.name).split("_")[1]);
         if(m_index > 7)
         {
            m_index = -1;
            return;
         }
         hideAll();
         if(m_checkList[m_index - 1])
         {
            m_checkList[m_index - 1].visible = true;
         }
      }
      
      private function hideAll() : void
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < m_checkList.length)
         {
            m_checkList[_loc1_].visible = false;
            _loc1_++;
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
   }
}

