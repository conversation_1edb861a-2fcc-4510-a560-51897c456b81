package UI.SocietySystem.MySocietyPanel
{
   import UI.GamingUI;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.SocietySystem.MemberListPanel.MemberListPanel;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SocialChatPanel.SocietyChatPanel;
   import UI.SocietySystem.SocietySystem;
   import YJFY.Loader.YJFYLoader;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MySocietyFunArea
   {
      
      private var m_clear:ClearHelper;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_memberListBtn:MySwitchBtnLogicShell;
      
      private var m_societyActivityBtn:MySwitchBtnLogicShell;
      
      private var m_societyWelfareBtn:MySwitchBtnLogicShell;
      
      private var m_funSwitchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_memberListPanel:MemberListPanel;
      
      private var m_societyActivityPanel:SocietyActivityPanel;
      
      private var m_societyWelfarePanel:SocietyWelfarePanel;
      
      private var m_societyChatPanel:SocietyChatPanel;
      
      private var m_show:MovieClip;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_societySystem:SocietySystem;
      
      private var m_societySystemXML:XML;
      
      private var m_memberDataList:DOWN_TheSocietyMemberList;
      
      private var m_mySocietyPanel:MySocietyPanel;
      
      private var m_gamingUI:GamingUI;
      
      public function MySocietyFunArea()
      {
         super();
         m_clear = new ClearHelper();
      }
      
      public function clear() : void
      {
         if(m_clear)
         {
            m_clear.clear();
         }
         m_clear = null;
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_memberListBtn);
         m_memberListBtn = null;
         ClearUtil.clearObject(m_societyActivityBtn);
         m_societyActivityBtn = null;
         ClearUtil.clearObject(m_societyWelfareBtn);
         m_societyWelfareBtn = null;
         ClearUtil.clearObject(m_funSwitchBtnGroup);
         m_funSwitchBtnGroup = null;
         ClearUtil.clearObject(m_memberListPanel);
         m_memberListPanel = null;
         ClearUtil.clearObject(m_societyActivityPanel);
         m_societyActivityPanel = null;
         ClearUtil.clearObject(m_societyWelfarePanel);
         m_societyWelfarePanel = null;
         ClearUtil.clearObject(m_societyChatPanel);
         m_societyChatPanel = null;
         m_show = null;
         m_myLoader = null;
         m_societySystem = null;
         m_societySystemXML = null;
         m_memberDataList = null;
         m_mySocietyPanel = null;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setMySocietyPanel(param1:MySocietyPanel) : void
      {
         m_mySocietyPanel = param1;
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
      }
      
      public function setMemberDataList(param1:DOWN_TheSocietyMemberList) : void
      {
         ClearUtil.clearObject(m_memberDataList);
         m_memberDataList = null;
         if(param1 == null)
         {
            return;
         }
         m_memberDataList = param1;
         if(m_memberListPanel)
         {
            m_memberListPanel.setMemberDataList(m_memberDataList);
         }
         if(Boolean(m_memberListBtn) && m_memberListBtn.isActive && m_memberListPanel == null)
         {
            initMemberListPanel();
         }
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_showMC = new MovieClipPlayLogicShell();
         m_showMC.setShow(m_show);
         m_funSwitchBtnGroup = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
         m_memberListBtn = new MySwitchBtnLogicShell();
         m_memberListBtn.setShow(m_show["memberListBtn"]);
         m_memberListBtn.setTipString("查看帮会成员");
         m_societyActivityBtn = new MySwitchBtnLogicShell();
         m_societyActivityBtn.setShow(m_show["societyActivityBtn"]);
         m_societyActivityBtn.setTipString("查看帮会活动");
         m_societyWelfareBtn = new MySwitchBtnLogicShell();
         m_societyWelfareBtn.setShow(m_show["societyWelfareBtn"]);
         m_societyWelfareBtn.setTipString("查看帮会福利");
         m_funSwitchBtnGroup.addSwitchBtn(m_societyActivityBtn);
         m_funSwitchBtnGroup.addSwitchBtn(m_societyWelfareBtn);
         m_funSwitchBtnGroup.addSwitchBtn(m_memberListBtn);
         m_funSwitchBtnGroup.addEnd();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         switch(_loc2_)
         {
            case m_memberListBtn:
               initMemberListPanel();
               break;
            case m_societyActivityBtn:
               initSocietyActivityPanel();
               break;
            case m_societyWelfareBtn:
               initSocietyWelfarePanel();
               break;
            default:
               if((m_memberListPanel ? m_memberListPanel.getPageBtnGroup() : null) !== _loc2_)
               {
                  break;
               }
               m_memberDataList = null;
               m_mySocietyPanel.getMemberListData(m_memberListPanel.getPageBtnGroup().pageNum - 1,m_memberListPanel.getOnePageColumeNum());
               break;
         }
      }
      
      private function clearFunArea() : void
      {
         ClearUtil.clearObject(m_memberListPanel);
         m_memberListPanel = null;
         ClearUtil.clearObject(m_societyActivityPanel);
         m_societyActivityPanel = null;
         ClearUtil.clearObject(m_societyWelfarePanel);
         m_societyWelfarePanel = null;
         ClearUtil.clearObject(m_societyChatPanel);
         m_societyChatPanel = null;
      }
      
      private function initMemberListPanel() : void
      {
         clearFunArea();
         m_showMC.gotoAndStop("memberList");
         m_memberListPanel = new MemberListPanel();
         if(m_memberDataList)
         {
            m_memberListPanel.setMemberDataList(m_memberDataList);
         }
         m_memberListPanel.setShow(m_show["memberListPanel"],m_societySystemXML,"点击对该玩家进行其他操作",true);
         if(m_memberDataList == null)
         {
            m_mySocietyPanel.getMemberListData(m_memberListPanel.getPageBtnGroup().pageNum - 1,m_memberListPanel.getOnePageColumeNum());
         }
      }
      
      private function initSocietyActivityPanel() : void
      {
         clearFunArea();
         m_showMC.gotoAndStop("societyActivity");
         m_societyActivityPanel = new SocietyActivityPanel();
         m_societyActivityPanel.setShow(m_show["societyActivityPanel"]);
      }
      
      private function initSocietyWelfarePanel() : void
      {
         clearFunArea();
         m_showMC.gotoAndStop("societyWelfare");
         m_societyWelfarePanel = new SocietyWelfarePanel();
         m_societyWelfarePanel.setShow(m_show["societyWelfarePanel"]);
      }
      
      private function initSocietyChatPanel() : void
      {
         clearFunArea();
         m_showMC.gotoAndStop("societyChat");
         m_societyChatPanel = new SocietyChatPanel();
         m_societyChatPanel.set4399API(m_gamingUI.getAPI4399());
         m_societyChatPanel.setLoader(m_myLoader);
         m_societyChatPanel.setSocietySystem(m_societySystem);
         m_societyChatPanel.setSocietySystemXML(m_societySystemXML);
         m_societyChatPanel.setShow(m_show["societyChatPanel"],"in");
      }
      
      public function getMemberListPanel() : MemberListPanel
      {
         return m_memberListPanel;
      }
      
      public function getSocietyActivityPanel() : SocietyActivityPanel
      {
         return m_societyActivityPanel;
      }
      
      public function getSocietyWelfarePanel() : SocietyWelfarePanel
      {
         return m_societyWelfarePanel;
      }
      
      public function getSocietyChatPanel() : SocietyChatPanel
      {
         return m_societyChatPanel;
      }
      
      public function getMemberListBtn() : SwitchBtnLogicShell
      {
         return m_memberListBtn;
      }
      
      public function getMemberDataList() : DOWN_TheSocietyMemberList
      {
         return m_memberDataList;
      }
   }
}

