package YJFY.RichTextArea.IconData
{
   public class IconData_MovieClip extends IconData
   {
      
      private var m_swfPath:String;
      
      private var m_className:String;
      
      public function IconData_MovieClip(param1:String, param2:String, param3:String, param4:String)
      {
         super(param1,param2);
         m_iconType = "movieClip";
         m_iconStr = param1;
         m_swfPath = param3;
         m_className = param4;
      }
      
      public function getSwfPath() : String
      {
         return m_swfPath;
      }
      
      public function getClassName() : String
      {
         return m_className;
      }
   }
}

