package YJFY.Skill.FoxSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.World.World;
   
   public class Skill_FoxSkill2 extends CuboidAreaAttackSkill_OneSkillShow
   {
      
      private var m_skillMoveSpeed:Number;
      
      public function Skill_FoxSkill2()
      {
         super();
         m_isAbleChangeDirection = true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillMoveSpeed = Number(param1.@skillMoveSpeed);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(m_skillMoveSpeed);
      }
   }
}

