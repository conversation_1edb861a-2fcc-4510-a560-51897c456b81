package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.IEntity;
   
   public class MoYuYingBulletEntityListener
   {
      
      public var pushEntityFun:Function;
      
      public function MoYuYingBulletEntityListener()
      {
         super();
      }
      
      public function clear() : void
      {
         pushEntityFun = null;
      }
      
      public function pushEntity(param1:MoYuYingBulletEntity, param2:IEntity) : void
      {
         if(Boolean(pushEntityFun))
         {
            pushEntityFun(param1,param2);
         }
      }
   }
}

