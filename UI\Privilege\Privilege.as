package UI.Privilege
{
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class Privilege extends MySprite
   {
      
      private static const path:String = "";
      
      private var _privilegeVO:PrivilegeVO;
      
      private var _privilegeSprite:Sprite;
      
      private var listenerList:Array = [];
      
      protected var _wanLoadSources:Array = ["privilege"];
      
      public function Privilege(param1:PrivilegeVO)
      {
         super();
         _privilegeVO = param1;
         setShow(_privilegeVO);
         buttonMode = true;
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         destory();
         _privilegeSprite = null;
         _privilegeVO = null;
         ClearUtil.nullArr(_wanLoadSources);
         _wanLoadSources = null;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      protected function setShow(param1:PrivilegeVO) : void
      {
         var loadListener:LoadFinishListener1;
         var privilegeVO:PrivilegeVO = param1;
         if(privilegeVO == null)
         {
            return;
         }
         loadListener = new LoadFinishListener1(function():void
         {
            setmg(privilegeVO.className);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSources,loadListener);
      }
      
      override public function get width() : Number
      {
         if(_privilegeSprite == null)
         {
            return 34;
         }
         return super.width;
      }
      
      override public function get height() : Number
      {
         if(_privilegeSprite == null)
         {
            return 34;
         }
         return super.height;
      }
      
      protected function setmg(param1:String) : void
      {
         _privilegeSprite = MyFunction2.returnShowByClassName("" + param1) as Sprite;
         while(numChildren > 0)
         {
            removeChildAt(numChildren - 1);
         }
         if(_privilegeSprite)
         {
            addChild(_privilegeSprite);
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",rollOver,false,0,true);
         addEventListener("rollOut",rollOut,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("rollOver",rollOver,false);
         removeEventListener("rollOut",rollOut,false);
      }
      
      protected function rollOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":this}));
      }
      
      protected function rollOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      public function get privilegeVO() : PrivilegeVO
      {
         return _privilegeVO;
      }
      
      public function set privilegeVO(param1:PrivilegeVO) : void
      {
         _privilegeVO = param1;
         setShow(_privilegeVO);
      }
      
      public function get privilegeSprite() : Sprite
      {
         return _privilegeSprite;
      }
   }
}

