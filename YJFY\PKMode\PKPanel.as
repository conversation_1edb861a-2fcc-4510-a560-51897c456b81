package YJFY.PKMode
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.NicknameSystem.NicknameData;
   import UI.PKUI.PlayerDataForPK;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.GameData;
   import YJFY.PKMode.PKData.PKSaveDataOne;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PKPanel extends MySprite
   {
      
      private var m_quitBtn:ButtonLogicShell;
      
      private var m_myHeadShow:MovieClipPlayLogicShell;
      
      private var m_myNickNameTxt:TextField;
      
      private var m_myRankTxt:TextField;
      
      private var m_myMonthRankTxt:TextField;
      
      private var m_myWinMatchTxt:TextField;
      
      private var m_myWinMonthMatchTxt:TextField;
      
      private var m_myPKPointTxt:TextField;
      
      private var m_PktargetShowContainers:Vector.<PKTargetShowContainer>;
      
      private var m_normalResetBtn:ButtonLogicShell;
      
      private var m_oneKeyBtn:ButtonLogicShell;
      
      private var m_pkShopBtn:ButtonLogicShell;
      
      private var m_pkRankListBtn:ButtonLogicShell;
      
      private var m_pkType:String;
      
      private var m_myRank:int;
      
      private var m_normalResetPopBox:NormalResetPopBox;
      
      private var m_vipResetPopBox:VipResetPopBox;
      
      private var m_show:MovieClip;
      
      private var m_pk:PK;
      
      private var m_pkSaveDataOne:PKSaveDataOne;
      
      private var m_myMonthRankData:UserDataInRankList;
      
      private var m_nFlag:int = 1;
      
      public function PKPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell();
         m_myHeadShow = new MovieClipPlayLogicShell();
         m_PktargetShowContainers = new Vector.<PKTargetShowContainer>();
         m_normalResetBtn = new ButtonLogicShell();
         m_oneKeyBtn = new ButtonLogicShell();
         m_pkShopBtn = new ButtonLogicShell();
         m_pkRankListBtn = new ButtonLogicShell();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_myHeadShow);
         m_myHeadShow = null;
         m_myNickNameTxt = null;
         m_myRankTxt = null;
         m_myMonthRankTxt = null;
         m_myWinMatchTxt = null;
         m_myWinMonthMatchTxt = null;
         m_myPKPointTxt = null;
         ClearUtil.clearObject(m_PktargetShowContainers);
         m_PktargetShowContainers = null;
         ClearUtil.clearObject(m_normalResetBtn);
         m_normalResetBtn = null;
         ClearUtil.clearObject(m_oneKeyBtn);
         m_oneKeyBtn = null;
         ClearUtil.clearObject(m_pkShopBtn);
         m_pkShopBtn = null;
         ClearUtil.clearObject(m_pkRankListBtn);
         m_pkRankListBtn = null;
         m_pkType = null;
         ClearUtil.clearObject(m_normalResetPopBox);
         m_normalResetPopBox = null;
         ClearUtil.clearObject(m_vipResetPopBox);
         m_vipResetPopBox = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_pk = null;
         m_pkSaveDataOne = null;
         super.clear();
      }
      
      public function init(param1:MovieClip, param2:String, param3:PK, param4:int, param5:PKSaveDataOne, param6:UserDataInRankList = null) : void
      {
         m_myRank = param4;
         m_myMonthRankData = param6;
         m_show = param1;
         addChild(m_show);
         m_pkType = param2;
         m_pk = param3;
         m_pkSaveDataOne = param5;
         initShow();
         initShow2();
         m_pkSaveDataOne.checknewday();
      }
      
      public function refreshShow() : void
      {
         initShow2();
      }
      
      private function initShow() : void
      {
         var _loc1_:PKTargetShowContainer = null;
         var _loc3_:int = 0;
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_quitBtn.setShow(m_show["backBtn"]);
         m_myHeadShow.setShow(m_show["myHeadShow"]);
         m_myNickNameTxt = m_show["myNickNameTxt"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myNickNameTxt);
         m_myRankTxt = m_show["myRankTxt"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myRankTxt);
         m_myWinMatchTxt = m_show["myWinMatchTxt"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myWinMatchTxt);
         m_myPKPointTxt = m_show["myPKPointTxt"];
         MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myPKPointTxt);
         if(m_pkType == "onePK")
         {
            m_myMonthRankTxt = m_show["myMonthRankTxt"];
            MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myMonthRankTxt);
            m_myWinMonthMatchTxt = m_show["myWinMonthMatchTxt"];
            MyFunction2.changeTextFieldFont(_loc2_.fontName,m_myWinMonthMatchTxt);
         }
         m_normalResetBtn.setShow(m_show["resetPK"]);
         m_oneKeyBtn.setShow(m_show["onKeyChallenge"]);
         m_pkShopBtn.setShow(m_show["pkShopLinkBtn"]);
         m_pkRankListBtn.setShow(m_show["pkRankListLinkBtn"]);
         _loc3_ = 0;
         while(_loc3_ < 8)
         {
            _loc1_ = new PKTargetShowContainer();
            _loc1_.setShow(m_show["container_" + (_loc3_ + 1)]);
            m_PktargetShowContainers.push(_loc1_);
            _loc3_++;
         }
      }
      
      public function initShow2() : void
      {
         var _loc2_:int = 0;
         m_myHeadShow.gotoAndStop(GamingUI.getInstance().player1.playerVO.playerType);
         m_myNickNameTxt.text = Boolean(NicknameData.getInstance().myDataInNicknameRankList) && Boolean(NicknameData.getInstance().myDataInNicknameRankList.extra) ? NicknameData.getInstance().myDataInNicknameRankList.extra : "";
         m_myRankTxt.text = m_myRank ? m_myRank.toString() : "暂无";
         switch(m_pkType)
         {
            case "onePK":
               m_myWinMatchTxt.text = PlayerDataForPK.getInstance().winMatch.toString();
               m_myMonthRankTxt.text = m_myMonthRankData ? m_myMonthRankData.getRank().toString() : "暂无";
               m_myWinMonthMatchTxt.text = PlayerDataForPK.getInstance().winMonthMatch.toString();
               break;
            case "twoPK":
               m_myWinMatchTxt.text = PlayerDataForPK.getInstance().winMatchForTwoPlayer.toString();
         }
         m_myPKPointTxt.text = PlayerDataForPK.getInstance().pkPoint.toString();
         var _loc1_:int = int(m_PktargetShowContainers.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_PktargetShowContainers[_loc2_].setPKTargetPlayerData(null);
            _loc2_++;
         }
         _loc1_ = m_pkSaveDataOne.getPKTargetPlayerNum();
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_PktargetShowContainers[m_pkSaveDataOne.getPKTargetPlayerDataByIndex(_loc2_).getPKIndex()].setPKTargetPlayerData(m_pkSaveDataOne.getPKTargetPlayerDataByIndex(_loc2_));
            _loc2_++;
         }
      }
      
      private function OneKeyPk() : void
      {
         var _loc2_:int = 0;
         m_pk.m_OneKeyIndex.length = 0;
         var _loc1_:int = m_PktargetShowContainers ? m_PktargetShowContainers.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_PktargetShowContainers[_loc2_].getPKBtn())
            {
               m_pk.m_OneKeyIndex.push(_loc2_);
            }
            _loc2_++;
         }
         if(m_pk.m_OneKeyIndex.length <= 0)
         {
            showWarningBox("请重置对手后再来！",0);
            return;
         }
         if(getFreeOneKeyPKNum() > 0)
         {
            showWarningBox("尊贵的VIP用户：你有" + getFreeOneKeyPKNum() + "次免费一键PK机会",1 | 2,{
               "type":"FreeOneKeyPK",
               "okFunction":DoOneKeyPK
            });
         }
         else
         {
            showWarningBox("是否花费20点券一键PK，VIP用户每天最高有3次免费一键PK",1 | 2,{
               "type":"FreeOneKeyPK",
               "okFunction":buyOneKeyPK
            });
         }
      }
      
      private function DoOneKeyPK() : void
      {
         m_pkSaveDataOne.oneKeyPK();
         m_pk.showOneKeyWait(true);
         m_pk.startPK(m_pk.m_OneKeyIndex[0]);
      }
      
      private function DoResetPK() : void
      {
         m_pkSaveDataOne.resetData(GamingUI.getInstance().getNewestTimeStrFromSever());
         refreshShow();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function resetPK() : void
      {
         var _loc4_:int = 0;
         var _loc2_:XML = null;
         var _loc3_:* = undefined;
         var _loc1_:int = 0;
         var _loc5_:XML = XMLSingle.getInstance().dataXML.ZhouNianHuoDong[0];
         if(int(_loc5_.@isWeekday) == 1)
         {
            if(TimeUtil.getTimeUtil().timeIntervalBySecond(String(_loc5_.@StartData),TimeUtil.timeStr) > 0 && TimeUtil.getTimeUtil().timeIntervalBySecond(TimeUtil.timeStr,String(_loc5_.@EndData)) > 0)
            {
               if(TimeUtil.getTimeUtil().isInTimeOfOneDay(TimeUtil.timeStr,String(_loc5_.@StartTime),String(_loc5_.@EndTime)))
               {
                  _loc4_ = TimeUtil.getTimeUtil().getDistanceWeek(null);
                  trace("距离本周结束还有多少个小时:",_loc4_);
                  if(_loc4_ < int(_loc5_.@hour))
                  {
                     m_pkSaveDataOne.setresetType(3);
                     showWarningBox("周末活动！免费重置PK对手",1,{
                        "type":"FreeResetPK",
                        "okFunction":DoResetPK
                     });
                     return;
                  }
               }
            }
         }
         else if(TimeUtil.getTimeUtil().timeIntervalBySecond(String(_loc5_.@StartData),TimeUtil.timeStr) > 0 && TimeUtil.getTimeUtil().timeIntervalBySecond(TimeUtil.timeStr,String(_loc5_.@EndData)) > 0)
         {
            if(TimeUtil.getTimeUtil().isInTimeOfOneDay(TimeUtil.timeStr,String(_loc5_.@StartTime),String(_loc5_.@EndTime)))
            {
               m_pkSaveDataOne.setresetType(3);
               showWarningBox("周年庆活动！免费重置PK对手",1,{
                  "type":"FreeResetPK",
                  "okFunction":DoResetPK
               });
               return;
            }
         }
         m_pkSaveDataOne.setresetType(1);
         if(getFreeResetPKNum())
         {
            if(m_vipResetPopBox == null)
            {
               m_vipResetPopBox = new VipResetPopBox();
               m_vipResetPopBox.init();
            }
            addChild(m_vipResetPopBox);
            m_vipResetPopBox.setData(getFreeResetPKNum());
         }
         else
         {
            if(m_normalResetPopBox == null)
            {
               m_normalResetPopBox = new NormalResetPopBox();
               m_normalResetPopBox.init();
            }
            addChild(m_normalResetPopBox);
            _loc2_ = XMLSingle.getInstance().dataXML.PKData[0];
            _loc3_ = MyFunction.getInstance().excreteString(_loc2_.@resetPKPointTickets);
            _loc1_ = _loc3_[Math.min(_loc3_.length - 1,m_pkSaveDataOne.getResetNum())];
            m_normalResetPopBox.setData(m_pkSaveDataOne.getResetNum() + 1,_loc1_,CurrentTicketPointManager.getInstance().getCurrentTicketPoint());
            ClearUtil.clearObject(_loc3_);
            _loc3_ = null;
         }
      }
      
      private function getFreeResetPKNum() : uint
      {
         return Math.max(GamingUI.getInstance().player1.vipVO.freePKResetOppNum - m_pkSaveDataOne.getResetNum(),0);
      }
      
      private function getFreeOneKeyPKNum() : uint
      {
         return Math.max(GamingUI.getInstance().player1.vipVO.freeOneKeyPkOppNum - m_pkSaveDataOne.getOneKeyPKNum(),0);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:String = null;
         var _loc2_:SaveTaskInfo = null;
         var _loc5_:int = 0;
         if(m_pk.m_OneKeyPK)
         {
            return;
         }
         if(Boolean(param1.button) == false)
         {
            return;
         }
         var _loc6_:* = param1.button;
         switch(_loc6_)
         {
            case m_quitBtn:
               Part1.getInstance().closePK();
               Part1.getInstance().returnCity();
               Part1.getInstance().continueGame();
               break;
            case m_pkShopBtn:
               Part1.getInstance().closePK();
               Part1.getInstance().returnCity();
               Part1.getInstance().continueGame();
               GamingUI.getInstance().openShop();
               break;
            case m_pkRankListBtn:
               _loc3_ = m_pkType;
               Part1.getInstance().closePK();
               Part1.getInstance().returnCity();
               Part1.getInstance().continueGame();
               if(_loc3_ == "onePK")
               {
                  GamingUI.getInstance().openOnePlayerRankListPanel();
               }
               else
               {
                  if(_loc3_ != "twoPK")
                  {
                     throw new Error();
                  }
                  GamingUI.getInstance().openTwoPlayerRankListPanel();
               }
               break;
            case m_normalResetBtn:
               resetPK();
               break;
            case m_oneKeyBtn:
               OneKeyPk();
               break;
            default:
               if((m_normalResetPopBox ? m_normalResetPopBox.getSureBtn() : null) !== _loc6_)
               {
                  if((m_normalResetPopBox ? m_normalResetPopBox.getCancelBtn() : null) !== _loc6_)
                  {
                     if((m_vipResetPopBox ? m_vipResetPopBox.getSureBtn() : null) !== _loc6_)
                     {
                        if((m_vipResetPopBox ? m_vipResetPopBox.getCancelBtn() : null) !== _loc6_)
                        {
                           if((m_normalResetPopBox ? m_normalResetPopBox.getRechBtn() : null) !== _loc6_)
                           {
                              break;
                           }
                           AnalogServiceHoldFunction.getInstance().payMoney_As3();
                           ClearUtil.clearObject(m_normalResetPopBox);
                           m_normalResetPopBox = null;
                           break;
                        }
                        ClearUtil.clearObject(m_vipResetPopBox);
                        m_vipResetPopBox = null;
                        break;
                     }
                     DoResetPK();
                     ClearUtil.clearObject(m_vipResetPopBox);
                     m_vipResetPopBox = null;
                     _loc2_ = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                     break;
                  }
                  ClearUtil.clearObject(m_normalResetPopBox);
                  m_normalResetPopBox = null;
                  break;
               }
               buyResetPK();
               ClearUtil.clearObject(m_normalResetPopBox);
               m_normalResetPopBox = null;
               break;
         }
         var _loc4_:int = m_PktargetShowContainers ? m_PktargetShowContainers.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(param1.button == m_PktargetShowContainers[_loc5_].getPKBtn())
            {
               m_pk.startPK(_loc5_);
               return;
            }
            if(param1.button == m_PktargetShowContainers[_loc5_].getLookUpBtn())
            {
               m_pk.lookUpPlayerInfor(m_PktargetShowContainers[_loc5_].getPKTargetPlayerData().getUid(),m_PktargetShowContainers[_loc5_].getPKTargetPlayerData().getIndexOfSaveXML());
               return;
            }
            _loc5_++;
         }
      }
      
      private function buyOneKeyPK() : void
      {
         var resetPKXML:XML = XMLSingle.getInstance().dataXML.PKData[0];
         var price:int = int(resetPKXML.@OneKeyTickets);
         var ticketId:String = String(resetPKXML.@OneKeyTicketsId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买一键PK";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_pk.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            DoOneKeyPK();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
         resetPKXML = null;
      }
      
      private function buyResetPK() : void
      {
         var resetPKXML:XML = XMLSingle.getInstance().dataXML.PKData[0];
         var prices:Vector.<int> = MyFunction.getInstance().excreteString(resetPKXML.@resetPKPointTickets);
         var ticketIds:Vector.<String> = MyFunction.getInstance().excreteStringToString(resetPKXML.@resetPKPointTicketIds);
         var price:int = prices[Math.min(prices.length - 1,m_pkSaveDataOne.getResetNum())];
         var ticketId:String = ticketIds[Math.min(m_pkSaveDataOne.getResetNum(),ticketIds.length - 1)];
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "重置pk";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               m_pk.showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            DoResetPK();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },m_pk ? m_pk.showWarningBox : null,WarningBoxSingle.getInstance().getTextFontSize());
         ClearUtil.clearObject(prices);
         resetPKXML = null;
      }
   }
}

