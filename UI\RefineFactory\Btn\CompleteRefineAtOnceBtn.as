package UI.RefineFactory.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class CompleteRefineAtOnceBtn extends Btn
   {
      
      public function CompleteRefineAtOnceBtn()
      {
         super();
         setTipString("点击完成炼制");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickCompleteRefineAtOnceBtn"));
      }
   }
}

