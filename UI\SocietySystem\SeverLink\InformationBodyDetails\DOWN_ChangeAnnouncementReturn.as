package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_ChangeAnnouncementReturn extends InformationBodyDetail
   {
      
      protected var m_isSuccess:int;
      
      public function DOWN_ChangeAnnouncementReturn()
      {
         super();
         m_informationBodyId = 3032;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_isSuccess = param1.readInt();
      }
      
      public function getIsSuccess() : Boolean
      {
         return Boolean(m_isSuccess);
      }
   }
}

