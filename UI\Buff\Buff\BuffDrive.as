package UI.Buff.Buff
{
   import UI.GamingUI;
   import UI.PKUI.PlayerDataForPK;
   import UI.TimeEngine.TimeEngine;
   
   public class BuffDrive
   {
      
      private var _buffVO:BuffVO;
      
      private var _timeEngine:TimeEngine;
      
      public function BuffDrive(param1:BuffVO)
      {
         super();
         _buffVO = param1;
         init();
      }
      
      public function clear() : void
      {
         _buffVO = null;
         _timeEngine.clear();
         _timeEngine = null;
      }
      
      public function start() : void
      {
         _timeEngine.start();
      }
      
      public function initBuffDrive() : void
      {
         _timeEngine.initFun();
      }
      
      public function stop() : void
      {
         _timeEngine.stop();
      }
      
      public function get buffVO() : BuffVO
      {
         return _buffVO;
      }
      
      public function set buffVO(param1:BuffVO) : void
      {
         _buffVO = param1;
      }
      
      private function init() : void
      {
         _timeEngine = new TimeEngine();
         addInitFun();
         addStartFun();
         addProcessFun();
         addEndFun();
      }
      
      private function addInitFun() : void
      {
         switch(_buffVO.className)
         {
            case "Buff_ProtectPrivileges":
               _timeEngine.addInitFun("ProtectPrivileges_InitProtectBuffFun",{
                  "fun":BuffFunction.getInstance().startProtectBuffFun,
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleExperienceBuff":
            case "Buff_WubeiExperienceBuff":
               _timeEngine.addInitFun("ExperienceBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.extraAddExperienceRate = int(_buffVO.xml.@addValue);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.extraAddExperienceRate = int(_buffVO.xml.@addValue);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleGoldBuff":
               _timeEngine.addInitFun("GoldBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.extraAddMoneyRate = int(_buffVO.xml.@addValue);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.extraAddMoneyRate = int(_buffVO.xml.@addValue);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoublePKBuff":
               _timeEngine.addInitFun("PKBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     PlayerDataForPK.getInstance().extraAddPkPointRate = int(_buffVO.xml.@addValue);
                  },
                  "params":[_buffVO]
               });
               break;
            case "DaShengBuff":
               _timeEngine.addInitFun("DaShengBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     _buffVO.player.playerVO.suitAddBloodVolume = int(_buffVO.xml.@addBloodVolume);
                     GamingUI.getInstance().refresh(0x80 | 1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_ForceDanBuff":
               _timeEngine.addInitFun("ForceDanBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.forceDanAttack = GamingUI.getInstance().player1.playerVO.forceDanAttack + int(_buffVO.xml.@addAttack);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.forceDanAttack = GamingUI.getInstance().player2.playerVO.forceDanAttack + int(_buffVO.xml.@addAttack);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_RenPinDanBuff":
               _timeEngine.addInitFun("RenpinDanBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.renPinDan = GamingUI.getInstance().player1.playerVO.renPinDan + int(_buffVO.xml.@addRenpin);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.renPinDan = GamingUI.getInstance().player2.playerVO.renPinDan + int(_buffVO.xml.@addRenpin);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_TurnUpBuff":
               _timeEngine.addInitFun("TurnUpBuff_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addRate = int(_buffVO.xml.@addRate);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addRate = int(_buffVO.xml.@addRate);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_ActHitUp":
               _timeEngine.addInitFun("ActHitUp_InitBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addHit = int(_buffVO.xml.@addHit);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addHit = int(_buffVO.xml.@addHit);
                     }
                  },
                  "params":[_buffVO]
               });
         }
      }
      
      private function addStartFun() : void
      {
         switch(_buffVO.className)
         {
            case "Buff_ProtectPrivileges":
               _timeEngine.addStartFun("ProtectPrivileges_startProtectBuffFun",{
                  "fun":BuffFunction.getInstance().startProtectBuffFun,
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleExperienceBuff":
            case "Buff_WubeiExperienceBuff":
               _timeEngine.addStartFun("ExperienceBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.extraAddExperienceRate = int(_buffVO.xml.@addValue);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.extraAddExperienceRate = int(_buffVO.xml.@addValue);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleGoldBuff":
               _timeEngine.addStartFun("GoldBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.extraAddMoneyRate = int(_buffVO.xml.@addValue);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.extraAddMoneyRate = int(_buffVO.xml.@addValue);
                     }
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoublePKBuff":
               _timeEngine.addStartFun("PKBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     PlayerDataForPK.getInstance().extraAddPkPointRate = int(_buffVO.xml.@addValue);
                  },
                  "params":[_buffVO]
               });
               break;
            case "DaShengBuff":
               _timeEngine.addStartFun("DaShengBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     _buffVO.player.playerVO.suitAddBloodVolume = int(_buffVO.xml.@addBloodVolume);
                     _buffVO.player.playerVO.bloodPercent = _buffVO.data.currentBloodVolume / _buffVO.player.playerVO.bloodVolume;
                     GamingUI.getInstance().refresh(0x80 | 1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_ForceDanBuff":
               _timeEngine.addStartFun("ForceDanBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.forceDanAttack = GamingUI.getInstance().player1.playerVO.forceDanAttack + int(_buffVO.xml.@addAttack);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.forceDanAttack = GamingUI.getInstance().player2.playerVO.forceDanAttack + int(_buffVO.xml.@addAttack);
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_RenPinDanBuff":
               _timeEngine.addStartFun("RenpinDanBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.renPinDan = GamingUI.getInstance().player1.playerVO.renPinDan + int(_buffVO.xml.@addRenpin);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.renPinDan = GamingUI.getInstance().player2.playerVO.renPinDan + int(_buffVO.xml.@addRenpin);
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_TurnUpBuff":
               _timeEngine.addStartFun("TurnUpBuff_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addRate = GamingUI.getInstance().player1.playerVO.addRate + int(_buffVO.xml.@addRate);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addRate = GamingUI.getInstance().player2.playerVO.addRate + int(_buffVO.xml.@addRate);
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_ActHitUp":
               _timeEngine.addStartFun("ActHitUp_startBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addHit = int(_buffVO.xml.@addHit);
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addHit = GamingUI.getInstance().player2.playerVO.addHit + int(_buffVO.xml.@addHit);
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
         }
      }
      
      private function addProcessFun() : void
      {
         if(_buffVO.type == "noTimeBuff")
         {
            return;
         }
         switch(_buffVO.className)
         {
            case "Buff_ProtectPrivileges":
               _timeEngine.addProcessFun("ProtectPrivileges_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               _timeEngine.addProcessFun("ProtectPRivileges_processProtect",{
                  "fun":BuffFunction.getInstance().processProtect,
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleExperienceBuff":
            case "Buff_WubeiExperienceBuff":
               _timeEngine.addProcessFun("ExperienceBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoubleGoldBuff":
               _timeEngine.addProcessFun("GoldBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_DoublePKBuff":
               _timeEngine.addProcessFun("PKBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_ForceDanBuff":
               _timeEngine.addProcessFun("ForceDanBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_RenPinDanBuff":
               _timeEngine.addProcessFun("RenpinDanBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_TurnUpBuff":
               _timeEngine.addProcessFun("TurnUpBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
               break;
            case "Buff_ActHitUp":
               _timeEngine.addProcessFun("HitUpBuff_decRemainTime",{
                  "fun":BuffFunction.getInstance().decRemainTime,
                  "params":[_buffVO]
               });
         }
      }
      
      private function addEndFun() : void
      {
         switch(_buffVO.className)
         {
            case "Buff_ProtectPrivileges":
               _timeEngine.addEndFun("ProtectPrivileges_endProtectBuffFun",{"fun":BuffFunction.getInstance().endProtectBuffFun});
               break;
            case "Buff_DoubleExperienceBuff":
            case "Buff_WubeiExperienceBuff":
               _timeEngine.addEndFun("ExperienceBuff_endExperienceBuffFun",{"fun":function():void
               {
                  GamingUI.getInstance().player1.playerVO.extraAddExperienceRate = 0;
                  if(GamingUI.getInstance().player2)
                  {
                     GamingUI.getInstance().player2.playerVO.extraAddExperienceRate = 0;
                  }
               }});
               break;
            case "Buff_DoubleGoldBuff":
               _timeEngine.addEndFun("GoldBuff_endExperienceBuffFun",{"fun":function():void
               {
                  GamingUI.getInstance().player1.playerVO.extraAddMoneyRate = 0;
                  if(GamingUI.getInstance().player2)
                  {
                     GamingUI.getInstance().player2.playerVO.extraAddMoneyRate = 0;
                  }
               }});
               break;
            case "Buff_DoublePKBuff":
               _timeEngine.addEndFun("PKBuff_endExperienceBuffFun",{"fun":function():void
               {
                  PlayerDataForPK.getInstance().extraAddPkPointRate = 0;
               }});
               break;
            case "DaShengBuff":
               _timeEngine.addEndFun("DaShengBuff_endBuffFun",{"fun":function():void
               {
                  _buffVO.player.playerVO.suitAddBloodVolume = 0;
               }});
               break;
            case "Buff_ForceDanBuff":
               _timeEngine.addEndFun("ForceDanBuff_endBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.forceDanAttack = 0;
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.forceDanAttack = 0;
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_RenPinDanBuff":
               _timeEngine.addEndFun("ForceDanBuff_endBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.renPinDan = 0;
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.renPinDan = 0;
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_TurnUpBuff":
               _timeEngine.addEndFun("TurnUpBuff_endBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addRate = 0;
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addRate = 0;
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
               break;
            case "Buff_ActHitUp":
               _timeEngine.addEndFun("HitUpBuff_endBuffFun",{
                  "fun":function(param1:BuffVO):void
                  {
                     GamingUI.getInstance().player1.playerVO.addHit = 0;
                     if(GamingUI.getInstance().player2)
                     {
                        GamingUI.getInstance().player2.playerVO.addHit = 0;
                     }
                     GamingUI.getInstance().refresh(1);
                  },
                  "params":[_buffVO]
               });
         }
      }
   }
}

