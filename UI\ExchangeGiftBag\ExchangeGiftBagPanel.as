package UI.ExchangeGiftBag
{
   import Json.MyJSON;
   import UI.Button.QuitBtn3;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.MyHaveLockSwitchBtnLogicShell;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PlayerDataForPK;
   import UI.TextTrace.traceText;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.utils.MD5;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ExchangeGiftBagPanel extends MySprite
   {
      
      private const const_BBPputong:String = "bbpputong";
      
      private const const_BBPzizhun:String = "bbpzizhun";
      
      private const const_kaixueBuChange:String = "kaixueBuChangLiBao20170830";
      
      private const const_shuangrenBuChange:String = "shuangrenBuChange";
      
      private const const_kapaiputonglibao:String = "kapaiputonglibao2017";
      
      private const const_kapaihaohualibao:String = "kapaihaohualibao2017";
      
      private const const_shiwuzhounian:String = "shiwuzhounian2017";
      
      private const const_xiarilibao:String = "xiarilibao2017";
      
      private const const_qixinxieli:String = "shuangrenBuChange";
      
      private const const_Dujialibao:String = "Dujialibao8";
      
      private const const_BaoxiaoGift:String = "baoxiaoGift";
      
      private const const_ShuangdanjingxiGiftBagBtn:String = "ShuangdanjingxiGiftBagBtn";
      
      private const const_heSui:String = "hesuilibao20140428";
      
      private const const_yangnian:String = "yangnian20150225";
      
      private const const_quanmindianzhan:String = "quanmindianzhan";
      
      private const const_Baoxiaochunjie:* = "const_Baoxiaochunjie2018";
      
      private const m_const_zhuanpan20150108:String = "zhuanpan20150108";
      
      private var yaYaLeURL:String = "http://my.4399.com/jifen/api-apply";
      
      private var youXiHeURL1:String = "http://huodong2.4399.com/comm/millionpack/api.php?game=5&type=1&code=";
      
      private var youXiHeURL2:String = "http://huodong2.4399.com/comm/millionpack/api.php?game=5&type=2&code=";
      
      private var btnArr:Vector.<MyHaveLockSwitchBtnLogicShell>;
      
      private var page:int = 1;
      
      private var _show:MovieClip;
      
      private var _activationCodeValueText:TextField;
      
      private var txtPage:TextField;
      
      private var _exchangeGiftBagBtn:ButtonLogicShell;
      
      private var _getExchangeNumberBtn:ButtonLogicShell;
      
      private var btnNextPage:ButtonLogicShell;
      
      private var btnPrePage:ButtonLogicShell;
      
      private var _gotoGroupBtn:ButtonLogicShell;
      
      private var _gotoGroup2Btn:ButtonLogicShell;
      
      private var _fillBox:Sprite;
      
      public var Xinchun2019libao:MyHaveLockSwitchBtnLogicShell;
      
      private var _backgroupLayer:MovieClipPlayLogicShell;
      
      public var quitBtn:QuitBtn3;
      
      private var _currentGiftBagBtn:MyHaveLockSwitchBtnLogicShell;
      
      private var _exchangeGiftBagXML:XML;
      
      private var _switchBtns:Array;
      
      private var _lockMessages:Array;
      
      private var _newEquipmentVOs:Vector.<EquipmentVO>;
      
      private var _mountData:GiftMountData;
      
      private var _currentServeTime:String;
      
      private var m_giftBagChoicePanel:GiftBagChoicePanel;
      
      public function ExchangeGiftBagPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         _show = null;
         _activationCodeValueText = null;
         if(_exchangeGiftBagBtn)
         {
            _exchangeGiftBagBtn.clear();
         }
         _exchangeGiftBagBtn = null;
         if(_getExchangeNumberBtn)
         {
            _getExchangeNumberBtn.clear();
         }
         _getExchangeNumberBtn = null;
         if(btnNextPage)
         {
            btnNextPage.clear();
         }
         btnNextPage = null;
         if(btnPrePage)
         {
            btnPrePage.clear();
         }
         btnPrePage = null;
         ClearUtil.clearObject(Xinchun2019libao);
         Xinchun2019libao = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _exchangeGiftBagXML = null;
         btnArr.length = 0;
         var _loc3_:int = 0;
         if(_switchBtns)
         {
            _loc1_ = int(_switchBtns.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               _switchBtns[_loc3_] = null;
               _loc3_++;
            }
         }
         _switchBtns = null;
         if(_newEquipmentVOs)
         {
            _loc1_ = int(_newEquipmentVOs.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_newEquipmentVOs[_loc3_])
               {
                  _newEquipmentVOs[_loc3_].clear();
               }
               _newEquipmentVOs[_loc3_] = null;
               _loc3_++;
            }
         }
         _newEquipmentVOs = null;
         ClearUtil.clearObject(_mountData);
         _mountData = null;
         if(_backgroupLayer)
         {
            _backgroupLayer.clear();
         }
         _backgroupLayer = null;
         _currentServeTime = null;
         ClearUtil.clearObject(m_giftBagChoicePanel);
         m_giftBagChoicePanel = null;
      }
      
      private function init() : void
      {
         var i:int;
         var panel:ExchangeGiftBagPanel;
         _show = MyFunction2.returnShowByClassName("ExchangeGiftBagPanel") as MovieClip;
         addChild(_show);
         i = ExchangeGiftData.getInstance().exchangeGiftNames.length - 1;
         while(i >= 0)
         {
            if(ExchangeGiftData.getInstance().exchangeGiftNames[i].substr(0,"bbpputong".length) == "bbpputong" && ExchangeGiftData.getInstance().exchangeGiftNames[i].substr("bbpputong".length) != TimeUtil.getTimeUtil().splitTimeString())
            {
               ExchangeGiftData.getInstance().exchangeGiftNames.splice(i,1);
            }
            else if(ExchangeGiftData.getInstance().exchangeGiftNames[i].substr(0,"bbpzizhun".length) == "bbpzizhun" && ExchangeGiftData.getInstance().exchangeGiftNames[i].substr("bbpzizhun".length) != TimeUtil.getTimeUtil().splitTimeString())
            {
               ExchangeGiftData.getInstance().exchangeGiftNames.splice(i,1);
            }
            i--;
         }
         _activationCodeValueText = _show["activationCodeValueText"];
         _exchangeGiftBagBtn = new ButtonLogicShell();
         _exchangeGiftBagBtn.setShow(_show["exchangeGiftBagBtn"]);
         _exchangeGiftBagBtn.setTipString("点击获取礼包");
         _getExchangeNumberBtn = new ButtonLogicShell();
         _getExchangeNumberBtn.setShow(_show["getExchangeNumberBtn"]);
         _getExchangeNumberBtn.setTipString("点击获取兑换码");
         _gotoGroupBtn = new ButtonLogicShell();
         _gotoGroupBtn.setShow(_show["gotoGroupBtn"]);
         _gotoGroupBtn.setTipString("点击进入群组页面");
         _gotoGroup2Btn = new ButtonLogicShell();
         _gotoGroup2Btn.setShow(_show["gotoGroup2Btn"]);
         _gotoGroup2Btn.setTipString("点击进入群组页面");
         btnNextPage = new ButtonLogicShell();
         btnNextPage.setShow(_show["btnNextPage"]);
         btnNextPage.setTipString("点击下一页");
         btnPrePage = new ButtonLogicShell();
         btnPrePage.setShow(_show["btnPrePage"]);
         btnPrePage.setTipString("点击上一页");
         _fillBox = _show["fillBox"];
         btnArr = new Vector.<MyHaveLockSwitchBtnLogicShell>();
         Xinchun2019libao = new MyHaveLockSwitchBtnLogicShell();
         Xinchun2019libao.setShow(_show["Xinchun2019libao"]);
         btnArr.push(Xinchun2019libao);
         page = 1;
         txtPage = _show["txtPage"] as TextField;
         resetPage();
         _backgroupLayer = new MovieClipPlayLogicShell();
         _backgroupLayer.setShow(_show["backgroupLayer"]);
         _activationCodeValueText.defaultTextFormat = new TextFormat(null,18,16711680);
         _activationCodeValueText.type = "input";
         _activationCodeValueText.selectable = true;
         _activationCodeValueText.multiline = true;
         _activationCodeValueText.wordWrap = true;
         _switchBtns = [];
         _lockMessages = [];
         _exchangeGiftBagBtn.getShow().visible = false;
         _gotoGroupBtn.getShow().visible = false;
         _gotoGroup2Btn.getShow().visible = false;
         _getExchangeNumberBtn.getShow().visible = false;
         _fillBox.visible = false;
         _activationCodeValueText.visible = false;
         _gotoGroupBtn.getShow().visible = false;
         _gotoGroup2Btn.getShow().visible = false;
         Xinchun2019libao.lock();
         _switchBtns.push(Xinchun2019libao);
         Xinchun2019libao.switchBtns = _switchBtns;
         quitBtn = new QuitBtn3();
         quitBtn.x = 772;
         quitBtn.y = 150;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         panel = this;
         MyFunction2.loadXMLAndGetServerTimeFunction("ExchangeGiftBag",function(param1:XML, param2:String):void
         {
            var _loc11_:int = 0;
            var _loc6_:MyHaveLockSwitchBtnLogicShell = null;
            var _loc5_:String = null;
            var _loc3_:String = null;
            var _loc10_:String = null;
            var _loc4_:int = 0;
            _exchangeGiftBagXML = param1;
            _currentServeTime = param2;
            var _loc9_:XMLList = _exchangeGiftBagXML.children();
            var _loc7_:int = int(_loc9_.length());
            _loc11_ = 0;
            while(_loc11_ < _loc7_)
            {
               _loc5_ = String(_loc9_[_loc11_].@btnName);
               if(_loc5_)
               {
                  _loc6_ = panel.hasOwnProperty(_loc5_) ? panel[_loc5_] : null;
                  if(_loc6_ != null)
                  {
                     _loc4_ = int(_switchBtns.indexOf(_loc6_));
                     if(_loc4_ != -1)
                     {
                        _lockMessages[_loc4_] = String(_loc9_[_loc11_].@showLockMessage);
                     }
                     _loc3_ = String(_loc9_[_loc11_].@startTime);
                     _loc10_ = String(_loc9_[_loc11_].@endTime);
                     if((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_currentServeTime) > 0) && (!Boolean(_loc10_) || new TimeUtil().timeInterval(_currentServeTime,_loc10_) > 0))
                     {
                        _loc6_.unLock();
                     }
                  }
               }
               _loc11_++;
            }
            var _loc8_:MyHaveLockSwitchBtnLogicShell = MyHaveLockSwitchBtnLogicShell.setDefaultActivateBtnFromBtns(_switchBtns);
         },showWarningBox,true);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",DoUIEvent,true,0,true);
         addEventListener("clickLockBtn",DoUIEvent,true,0,true);
         addEventListener("showWarningBox",onShowWarningBoxEvent,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",DoUIEvent,true);
         removeEventListener("clickLockBtn",DoUIEvent,true);
         removeEventListener("showWarningBox",onShowWarningBoxEvent,true);
      }
      
      private function resetPage() : void
      {
         var _loc1_:int = 0;
         txtPage.text = String(page) + "/" + (String(int((btnArr.length - 1) / 4) + 1));
         _loc1_ = 0;
         while(_loc1_ < btnArr.length)
         {
            if(_loc1_ >= (page - 1) * 4 && _loc1_ < page * 4)
            {
               btnArr[_loc1_].getShow().visible = true;
               btnArr[_loc1_].getShow().x = 247;
               btnArr[_loc1_].getShow().y = 225 + (_loc1_ - (page - 1) * 4) * 60;
            }
            else
            {
               btnArr[_loc1_].getShow().visible = false;
            }
            _loc1_++;
         }
      }
      
      private function onShowWarningBoxEvent(param1:UIPassiveEvent) : void
      {
         showWarningBox(param1.data.text,param1.data.flag);
      }
      
      private function DoUIEvent(param1:Event) : void
      {
         var length:int;
         var buttonEvent:ButtonEvent;
         var index:int;
         var getUrl:String;
         var id:int;
         var giftXML:XML;
         var ids:Vector.<int>;
         var nums:Vector.<int>;
         var e:Event = param1;
         var i:int = 0;
         if(e is ButtonEvent)
         {
            buttonEvent = e as ButtonEvent;
            if(buttonEvent.type == "clickLockBtn")
            {
               index = int(_switchBtns.indexOf(buttonEvent.button));
               if(index != -1)
               {
                  showWarningBox(_lockMessages[index],0);
               }
               return;
            }
            switch(buttonEvent.button)
            {
               case "wu":
               case Xinchun2019libao:
                  Sprite(_exchangeGiftBagBtn.getShow()).mouseChildren = true;
                  Sprite(_exchangeGiftBagBtn.getShow()).mouseEnabled = true;
                  MyFunction.getInstance().changeSaturation(_exchangeGiftBagBtn.getShow(),0);
                  var _loc3_:* = buttonEvent.button;
                  if(Xinchun2019libao === _loc3_)
                  {
                     _currentGiftBagBtn = Xinchun2019libao;
                     _backgroupLayer.gotoAndStop("Xinchun2019libao");
                     _fillBox.visible = true;
                     _activationCodeValueText.visible = true;
                     _exchangeGiftBagBtn.getShow().visible = true;
                     _getExchangeNumberBtn.getShow().visible = true;
                     _gotoGroupBtn.getShow().visible = false;
                     _gotoGroup2Btn.getShow().visible = false;
                  }
                  break;
               case btnNextPage:
                  if(page < int((btnArr.length - 1) / 4) + 1)
                  {
                     page++;
                     resetPage();
                  }
                  break;
               case btnPrePage:
                  if(page > 1)
                  {
                     page--;
                     resetPage();
                  }
                  break;
               case _getExchangeNumberBtn:
               case _gotoGroupBtn:
               case _gotoGroup2Btn:
                  _loc3_ = _currentGiftBagBtn;
                  if(Xinchun2019libao === _loc3_)
                  {
                     getUrl = "http://my.4399.com/forums/thread-59569108";
                  }
                  navigateToURL(new URLRequest(getUrl),"_blank");
                  break;
               case _exchangeGiftBagBtn:
                  if(Boolean(_activationCodeValueText.text) || !_activationCodeValueText.visible)
                  {
                     GamingUI.getInstance().mouseChildren = false;
                     GamingUI.getInstance().mouseEnabled = false;
                     ids = new Vector.<int>();
                     nums = new Vector.<int>();
                     _loc3_ = _currentGiftBagBtn;
                     if(Xinchun2019libao !== _loc3_)
                     {
                        throw new Error();
                     }
                     giftXML = _exchangeGiftBagXML.Xinchun2019libao[0];
                     ClearUtil.clearObject(_newEquipmentVOs);
                     _newEquipmentVOs = null;
                     ClearUtil.clearObject(_mountData);
                     _mountData = null;
                     if(giftXML)
                     {
                        length = int(giftXML.children().length());
                        i = 0;
                        while(i < length)
                        {
                           id = int(giftXML.children()[i].@id);
                           ids.push(id);
                           nums.push(int(giftXML.children()[i].@num));
                           i++;
                        }
                        _newEquipmentVOs = XMLSingle.getEquipmentVOsIDs(ids,XMLSingle.getInstance().equipmentXML,nums,true);
                        MyFunction2.falseAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,function():void
                        {
                           showWarningBox("玩家1背包放不下礼包！",0);
                           GamingUI.getInstance().mouseChildren = true;
                           GamingUI.getInstance().mouseEnabled = true;
                        },sendRequest,null,null);
                        _mountData = new GiftMountData();
                        _mountData.initByXML(giftXML);
                     }
                     else
                     {
                        sendRequest();
                     }
                  }
            }
         }
         else
         {
            switch(e.type)
            {
               case "complete":
                  complete(e.target.data);
                  break;
               case "ioError":
                  traceText("流错误！");
                  traceText((e as IOErrorEvent).text);
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
                  break;
               case "securityError":
                  traceText("出现安全错误！系统尝试从安全沙箱外部的服务器加载数据！");
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
                  break;
               case "httpStatus":
                  trace((e as HTTPStatusEvent).status,(e as HTTPStatusEvent).target);
            }
         }
      }
      
      private function complete(param1:Object) : void
      {
         var _loc4_:Object = null;
         var _loc2_:SaveTaskInfo = null;
         var _loc3_:String = param1 as String;
         switch(_currentGiftBagBtn)
         {
            case "wu":
            case Xinchun2019libao:
               _loc4_ = MyJSON.decode(_loc3_);
               if(_loc4_.code == 100)
               {
                  if(Boolean(_newEquipmentVOs) || _mountData)
                  {
                     if(_newEquipmentVOs)
                     {
                        MyFunction2.trueAddEquipmentVOs(_newEquipmentVOs,GamingUI.getInstance().player1,showWarningBox,["礼包领取成功！",0]);
                     }
                     if(_mountData)
                     {
                        GamingUI.getInstance().player1.getMountsVO().addGetMaterialPointNum(_mountData.getZhaoHuanNum());
                        GamingUI.getInstance().player1.getMountsVO().addStrengthenPointNum(_mountData.getLingShouShiNum());
                        GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money + _mountData.getYuanbaoNum();
                        PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint + _mountData.getPkNum();
                     }
                     _loc2_ = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  }
               }
               showWarningBox(_loc4_.msg,0);
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
      }
      
      private function sendRequest() : void
      {
         MyFunction2.getUserStateIsRightFunction(function():void
         {
            sendRequest2();
         },function():void
         {
            showWarningBox("网络连接出问题了",0);
         },true);
      }
      
      private function sendRequest2() : void
      {
         var _loc8_:String = null;
         var _loc6_:URLVariables = null;
         var _loc7_:String = null;
         var _loc3_:String = null;
         var _loc2_:String = null;
         var _loc1_:String = null;
         var _loc5_:URLRequest = new URLRequest();
         _loc5_.method = "POST";
         var _loc9_:MyHaveLockSwitchBtnLogicShell = _currentGiftBagBtn;
         if(Xinchun2019libao === _loc9_)
         {
            _loc7_ = GameData.getInstance().getLoginReturnData().getUid();
            _loc3_ = _activationCodeValueText.text;
            _loc2_ = "33620";
            _loc1_ = "1";
            _loc6_ = new URLVariables();
            _loc6_.uid = _loc7_;
            _loc6_.activation = _loc3_;
            _loc6_.product_id = _loc2_;
            _loc6_.type = _loc1_;
            _loc6_.token = MD5.hash(_loc3_ + _loc2_ + _loc1_ + _loc7_ + "42c6bab1099ecf974e4b314e71e6470b");
            _loc5_.data = _loc6_;
            _loc8_ = yaYaLeURL;
         }
         _loc5_.url = _loc8_;
         var _loc4_:URLLoader = new URLLoader();
         _loc4_.addEventListener("complete",DoUIEvent);
         _loc4_.addEventListener("ioError",DoUIEvent);
         _loc4_.addEventListener("securityError",DoUIEvent);
         _loc4_.load(_loc5_);
         _activationCodeValueText.text = "";
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function judeIsHaveTheBag(param1:String) : Boolean
      {
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = ExchangeGiftData.getInstance().exchangeGiftNames ? ExchangeGiftData.getInstance().exchangeGiftNames.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(ExchangeGiftData.getInstance().exchangeGiftNames[_loc4_] == param1)
            {
               _loc3_ = true;
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      private function openGiftChoicePanel() : void
      {
         m_giftBagChoicePanel = new GiftBagChoicePanel();
         m_giftBagChoicePanel.init(this);
         addChild(m_giftBagChoicePanel);
      }
      
      public function closeGiftBagChoicePanel() : void
      {
         ClearUtil.clearObject(m_giftBagChoicePanel);
         m_giftBagChoicePanel = null;
      }
   }
}

