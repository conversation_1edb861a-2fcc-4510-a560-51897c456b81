package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PKMode2Data;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PKPanel extends MySprite
   {
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_MultiInforMC:MovieClipPlayLogicShell;
      
      private var m_myHeadShow:MovieClipPlayLogicShell;
      
      private var m_myRankText:TextField;
      
      private var m_myWinNumText:TextField;
      
      private var m_myRemainPKNumText:TextField;
      
      private var m_gotoRankListBtn:ButtonLogicShell2;
      
      private var m_pkInforBtn:MySwitchBtnLogicShell;
      
      private var m_topPlayersShowBtn:MySwitchBtnLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_PKInforPanel:PKInforPanel;
      
      private var m_topPlayersShow:TopPlayersShow;
      
      private var m_show:MovieClip;
      
      private var m_pk:PK2;
      
      private var m_myRank:uint;
      
      private var m_pkMode2Data:PKMode2Data;
      
      private var m_myPkMode2VO:PKMode2VO;
      
      private var m_myPlayer1:Player;
      
      private var m_myPlayer2:Player;
      
      private var m_pkType:String;
      
      private var m_rankId:uint;
      
      public function PKPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_MultiInforMC = new MovieClipPlayLogicShell();
         m_myHeadShow = new MovieClipPlayLogicShell();
         m_gotoRankListBtn = new ButtonLogicShell2();
         m_pkInforBtn = new MySwitchBtnLogicShell();
         m_topPlayersShowBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_switchBtnGroup.addSwitchBtn(m_pkInforBtn);
         m_switchBtnGroup.addSwitchBtn(m_topPlayersShowBtn);
      }
      
      override public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            ClearUtil.clearObject(m_show);
            m_show = null;
         }
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_MultiInforMC);
         m_MultiInforMC = null;
         ClearUtil.clearObject(m_myHeadShow);
         m_myHeadShow = null;
         m_myRankText = null;
         m_myWinNumText = null;
         m_myRemainPKNumText = null;
         ClearUtil.clearObject(m_gotoRankListBtn);
         m_gotoRankListBtn = null;
         ClearUtil.clearObject(m_pkInforBtn);
         m_pkInforBtn = null;
         ClearUtil.clearObject(m_topPlayersShowBtn);
         m_topPlayersShowBtn = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_PKInforPanel);
         m_PKInforPanel = null;
         ClearUtil.clearObject(m_topPlayersShow);
         m_topPlayersShow = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_pk = null;
         m_pkMode2Data = null;
         m_myPkMode2VO = null;
         m_myPlayer1 = null;
         m_myPlayer2 = null;
         m_pkType = null;
         super.clear();
      }
      
      public function init(param1:MovieClip, param2:PK2, param3:Player, param4:Player, param5:uint, param6:PKMode2VO, param7:PKMode2Data, param8:String, param9:uint) : void
      {
         m_pk = param2;
         m_myPlayer1 = param3;
         m_myPlayer2 = param4;
         m_myRank = param5;
         m_pkMode2Data = param7;
         m_myPkMode2VO = param6;
         m_rankId = param9;
         m_pkType = param8;
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            ClearUtil.clearObject(m_show);
            m_show = null;
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
            addChild(m_show);
         }
         initShow();
         initShow2();
         m_pkInforBtn.turnActiveAndDispatchEvent();
      }
      
      public function refreshShow() : void
      {
         initShow2();
         var _loc1_:SwitchBtnLogicShell = m_switchBtnGroup.currentActivateBtn();
         _loc1_.turnUnActivate();
         _loc1_.turnActiveAndDispatchEvent();
      }
      
      private function initShow2() : void
      {
         m_myHeadShow.gotoAndStop(m_myPlayer1.playerVO.playerType);
         m_myRankText.text = m_myRank ? m_myRank.toString() : " 暂无排名";
         m_myWinNumText.text = m_myPkMode2VO.getWinPKResultNum().toString();
         m_myRemainPKNumText.text = Math.max(0,m_pkMode2Data.getPKNumOneDay() - m_myPkMode2VO.getPKNum()).toString();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:String = null;
         var _loc3_:* = 0;
         switch(param1.button)
         {
            case m_pkInforBtn:
               initPKInforFrame();
               break;
            case m_topPlayersShowBtn:
               initTopPlayersShowFrame();
               break;
            case m_quitBtn:
               Part1.getInstance().closePK();
               Part1.getInstance().returnCity();
               Part1.getInstance().continueGame();
               break;
            case m_gotoRankListBtn:
               _loc2_ = m_pkType;
               _loc3_ = m_myPkMode2VO.getWinPKResultNum();
               Part1.getInstance().closePK();
               Part1.getInstance().returnCity();
               Part1.getInstance().continueGame();
               var _loc4_:* = _loc2_;
               if("onePK" !== _loc4_)
               {
                  throw new Error("出错了");
               }
               GamingUI.getInstance().openPKMode2RankList1Panel(m_rankId,_loc3_);
               break;
         }
      }
      
      private function initShow() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_myHeadShow.setShow(m_show["myHeadShow"]);
         m_myRankText = m_show["myRankText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_myRankText);
         m_myWinNumText = m_show["myWinNumText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_myWinNumText);
         m_myRemainPKNumText = m_show["remainPKNumText"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_myRemainPKNumText);
         m_gotoRankListBtn.setShow(m_show["gotoRankListBtn"]);
         m_pkInforBtn.setShow(m_show["PKBtn"]);
         m_topPlayersShowBtn.setShow(m_show["topPlayersShowBtn"]);
         m_MultiInforMC.setShow(m_show["MultiInforMC"]);
      }
      
      private function initPKInforFrame() : void
      {
         frameClear();
         m_MultiInforMC.gotoAndStop("pkInforShow");
         m_PKInforPanel = new PKInforPanel();
         m_PKInforPanel.init(m_MultiInforMC.getShow()["PKInforShow"],m_pk,m_pkType,m_myPlayer1,m_myPlayer2,m_myRank,m_myPkMode2VO);
      }
      
      private function initTopPlayersShowFrame() : void
      {
         frameClear();
         m_MultiInforMC.gotoAndStop("topPlayersShow");
         if(m_topPlayersShow == null)
         {
            m_topPlayersShow = new TopPlayersShow();
         }
         m_topPlayersShow.init(m_MultiInforMC.getShow()["topPlayersShow"],m_pk,m_rankId,m_pkType);
      }
      
      private function frameClear() : void
      {
         ClearUtil.clearObject(m_PKInforPanel);
         m_PKInforPanel = null;
         if(m_topPlayersShow)
         {
            m_topPlayersShow.clearPlayerShow();
         }
      }
   }
}

