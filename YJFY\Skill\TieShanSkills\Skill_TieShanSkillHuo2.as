package YJFY.Skill.TieShanSkills
{
   import UI.GamingUI;
   import UI.Players.PlayerVO;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.PlayerAISkillVO;
   
   public class Skill_TieShanSkillHuo2 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      public var isStart:Boolean = false;
      
      private var costMp:Number = 0;
      
      private var costMpIndex:int = 0;
      
      private var effectAnimationShowPlay:AnimationShowPlayLogicShell;
      
      private var effectAnimationShowPlay2:AnimationShowPlayLogicShell;
      
      public function Skill_TieShanSkillHuo2()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(effectAnimationShowPlay);
         effectAnimationShowPlay = null;
         ClearUtil.clearObject(effectAnimationShowPlay2);
         effectAnimationShowPlay2 = null;
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         if(isStart)
         {
            endSkill2();
            endBuff();
            return false;
         }
         (m_owner.getExtra() as TieShan).removeSkillBuff();
         releaseSkill2(param1);
         return true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         costMp = int(param1.@costMp) / 100;
         var _loc3_:String = String(param1.@effectAddtoTargetId);
         effectAnimationShowPlay = new AnimationShowPlayLogicShell();
         effectAnimationShowPlay.setShow(m_owner.getAnimationByDefId(_loc3_),true);
         var _loc2_:String = String(param1.@effectAddtoTargetId2);
         effectAnimationShowPlay2 = new AnimationShowPlayLogicShell();
         effectAnimationShowPlay2.setShow(m_owner.getAnimationByDefId(_loc2_),true);
      }
      
      override public function setExtra(param1:Object) : void
      {
         super.setExtra(param1);
         if(param1 && (param1 as PlayerAISkillVO).getSkillVO())
         {
            (param1 as PlayerAISkillVO).getSkillVO().isUntileUse = false;
            GamingUI.getInstance().refresh(64);
         }
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
      }
      
      public function renderFromPlayer() : void
      {
         var _loc2_:PlayerVO = null;
         if(!isStart)
         {
            return;
         }
         if(!m_world)
         {
            return;
         }
         var _loc1_:Number = 0;
         if(m_isOutWorldTime)
         {
            _loc1_ = m_world.getWorldExistTime();
         }
         else
         {
            _loc1_ = m_world.getWorldTime();
         }
         if(!isNaN(m_nextAttackReachTime) && Boolean(m_attackInterval) && m_nextAttackReachTime < _loc1_)
         {
            getCuboidRangeToWorld();
            attackReach(m_world);
         }
         costMpIndex++;
         if(costMpIndex >= 50)
         {
            costMpIndex = 0;
            _loc2_ = (this.m_owner.getExtra() as PlayerXydzjs).getUiPlayer().playerVO;
            if(_loc2_.magicPercent > costMp)
            {
               _loc2_.magicPercent -= costMp;
            }
            else
            {
               endBuff();
            }
         }
      }
      
      public function endBuff() : void
      {
         if(!isStart)
         {
            return;
         }
         (getExtra() as PlayerAISkillVO).getSkillVO().isUntileUse = false;
         isStart = false;
         GamingUI.getInstance().refresh(64);
         this.m_owner.removeOtherAnimation(effectAnimationShowPlay);
         this.m_owner.removeOtherAnimation(effectAnimationShowPlay2);
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         if(!isStart)
         {
            addBuff();
            m_nextAttackReachTime = 0;
         }
      }
      
      private function addBuff() : void
      {
         (getExtra() as PlayerAISkillVO).getSkillVO().isUntileUse = true;
         this.isStart = true;
         GamingUI.getInstance().refresh(64);
         effectAnimationShowPlay.gotoAndPlay("start");
         this.m_owner.addOtherAniamtion(effectAnimationShowPlay);
         effectAnimationShowPlay2.gotoAndPlay("start");
         this.m_owner.addOtherAniamtion(effectAnimationShowPlay2,false);
      }
   }
}

