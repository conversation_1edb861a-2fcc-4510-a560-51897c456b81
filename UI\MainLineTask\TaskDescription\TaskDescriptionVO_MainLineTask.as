package UI.MainLineTask.TaskDescription
{
   public class TaskDescriptionVO_MainLineTask
   {
      
      public static const TYPE_taskDescription1:String = "taskDescription1";
      
      public static const TYPE_taskDescription2:String = "taskDescription2";
      
      public static const TYPE_taskDescription3:String = "taskDescription3";
      
      public var id:String;
      
      public var type:String;
      
      public var taskName:String;
      
      public var finishConditionDescription:String;
      
      public function TaskDescriptionVO_MainLineTask()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function initByXML(param1:XML) : void
      {
         id = String(param1.@id);
         type = String(param1.@type);
         taskName = String(param1.taskName[0].@value);
         finishConditionDescription = String(param1.completeConditionDescription[0].@value);
      }
   }
}

