package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class SocietyShopSaveData extends DataManagerParent
   {
      
      private var m_shopDatas:Vector.<OneShopSaveDataInSocietyShop>;
      
      private var m_dataTime:String;
      
      public function SocietyShopSaveData()
      {
         super();
         m_shopDatas = new Vector.<OneShopSaveDataInSocietyShop>();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_shopDatas);
         m_shopDatas = null;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc4_:OneShopSaveDataInSocietyShop = null;
         if(param1 == null)
         {
            return;
         }
         dataTime = String(param1.@dataTime);
         var _loc2_:XMLList = param1.shop;
         var _loc3_:int = int(_loc2_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = new OneShopSaveDataInSocietyShop();
            _loc4_.initFromSaveXML(_loc2_[_loc5_]);
            m_shopDatas.push(_loc4_);
            _loc5_++;
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:XML = <shop />;
         if(dataTime)
         {
            _loc2_.@dataTime = dataTime;
         }
         _loc1_ = int(m_shopDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_.appendChild(m_shopDatas[_loc3_].exportSaveXML());
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getOneShopDataByShopLevel(param1:int) : OneShopSaveDataInSocietyShop
      {
         var _loc4_:int = 0;
         var _loc2_:int = int(m_shopDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_shopDatas[_loc4_].getShopLevel() == param1)
            {
               return m_shopDatas[_loc4_];
            }
            _loc4_++;
         }
         var _loc3_:OneShopSaveDataInSocietyShop = new OneShopSaveDataInSocietyShop();
         _loc3_.init2(param1);
         m_shopDatas.push(_loc3_);
         return _loc3_;
      }
      
      public function isReSetAndReSetData(param1:String, param2:int) : void
      {
         var _loc3_:int = new TimeUtil().getDayNumBetweenTwoTime(dataTime,param1);
         if(_loc3_ == -1 || _loc3_ >= param2)
         {
            dataTime = param1;
            ClearUtil.clearObject(m_shopDatas);
            m_shopDatas = new Vector.<OneShopSaveDataInSocietyShop>();
         }
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.dataTime = m_dataTime;
      }
      
      private function get dataTime() : String
      {
         return _antiwear.dataTime;
      }
      
      private function set dataTime(param1:String) : void
      {
         _antiwear.dataTime = param1;
      }
   }
}

