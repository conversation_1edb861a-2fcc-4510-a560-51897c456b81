package YJFY.ShowLogicShell.NumShowLogicShell
{
   import flash.display.MovieClip;
   
   public class OnePlaceNumLogicShell
   {
      
      private var _show:MovieClip;
      
      public function OnePlaceNumLogicShell()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         _show = param1;
         _show.gotoAndStop(1);
      }
      
      public function getShow() : MovieClip
      {
         return _show;
      }
      
      public function clear() : void
      {
         _show = null;
      }
      
      public function showNum(param1:int) : void
      {
         _show.gotoAndStop(param1 + 1);
      }
   }
}

