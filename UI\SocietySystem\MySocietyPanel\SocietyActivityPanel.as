package UI.SocietySystem.MySocietyPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteDirection;
   import YJFY.ShowLogicShell.ScrollSpriteLogicShell.ScrollSpriteLogicShell;
   import flash.display.MovieClip;
   
   public class SocietyActivityPanel
   {
      
      private var m_show:MovieClip;
      
      private var m_scrollLS:ScrollSpriteLogicShell;
      
      private var m_signColume:ButtonLogicShell2;
      
      private var m_signJoinBtn:ButtonLogicShell2;
      
      private var m_contriColume:ButtonLogicShell2;
      
      private var m_contriBtn:ButtonLogicShell2;
      
      public function SocietyActivityPanel()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
      }
      
      private function initShow() : void
      {
         m_scrollLS = new ScrollSpriteLogicShell();
         var _loc1_:ScrollSpriteDirection = new ScrollSpriteDirection();
         _loc1_.setDirection("L");
         m_scrollLS.setShow(m_show,_loc1_);
         m_signColume = new ButtonLogicShell2();
         m_signColume.setShow(m_scrollLS.getDataLayer()["signColume"]);
         m_signJoinBtn = new ButtonLogicShell2();
         m_signJoinBtn.setShow(m_signColume.getShow()["joinBtn"]);
         m_signJoinBtn.setTipString("点击参加该活动");
         m_contriColume = new ButtonLogicShell2();
         m_contriColume.setShow(m_scrollLS.getDataLayer()["societyContriColume"]);
         m_contriBtn = new ButtonLogicShell2();
         m_contriBtn.setShow(m_contriColume.getShow()["joinBtn"]);
         m_contriBtn.setTipString("点击参加该活动");
      }
      
      public function getSignJoinBtn() : ButtonLogicShell2
      {
         return m_signJoinBtn;
      }
      
      public function getContributionBtn() : ButtonLogicShell2
      {
         return m_contriBtn;
      }
   }
}

