package YJFY.LevelMode5
{
   import UI.GamingUI;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Part1;
   import YJFY.World.World;
   
   public class EndDancerBossMoveToAI
   {
      
      private var m_endDancer:EndDancerBoss;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      private var m_px:Number;
      
      private var m_py:Number;
      
      private var m_tagx:Number;
      
      private var m_tagy:Number;
      
      private var m_nEntityNum:int;
      
      private var m_id:String;
      
      private var m_nAvg:Number = 3;
      
      private var m_nangle:Number;
      
      private var m_dx:Number;
      
      private var m_dy:Number;
      
      public function EndDancerBossMoveToAI()
      {
         super();
      }
      
      public function clear() : void
      {
      }
      
      public function init(param1:IAnimalEntity, param2:World, param3:EndDancerBoss, param4:int, param5:int) : void
      {
         m_endDancer = param3;
         m_owner = param1;
         m_world = param2;
         m_endDancer.getAnimalEntity().setNewPosition(param4,param5,m_endDancer.getAnimalEntity().getZ());
      }
      
      public function render() : void
      {
         if(m_world == null)
         {
            return;
         }
         updateMove();
      }
      
      public function updateMove() : void
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false && m_owner.isInSkill() == false && m_owner.isInHurt() == false && m_owner.isInAttack() == false)
         {
            m_endDancer.getAnimalEntity().walk();
            loadPosition();
         }
      }
      
      public function isTwo() : Boolean
      {
         if(GamingUI.getInstance().player2 && GamingUI.getInstance().player2.playerVO)
         {
            return true;
         }
         return false;
      }
      
      private function isAliveOne() : Boolean
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            if(Part1.getInstance().getLevelEndDancerWorld().getPlayer1() && !Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().isInDie())
            {
               return true;
            }
         }
         return false;
      }
      
      private function isAliveTwo() : Boolean
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            if(Part1.getInstance().getLevelEndDancerWorld().getPlayer2() && !Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().isInDie())
            {
               return true;
            }
         }
         return false;
      }
      
      private function loadPosition() : void
      {
         if(isTwo())
         {
            if(isAliveOne())
            {
               loadOnePos();
               loadTagPos();
            }
            else if(isAliveTwo())
            {
               loadTwoPos();
               loadTagPos();
            }
         }
         else if(isAliveOne())
         {
            loadOnePos();
            loadTagPos();
         }
      }
      
      private function loadTagPos() : void
      {
         m_tagx = m_owner.getX();
         m_tagy = m_owner.getY();
         if(m_px > m_tagx)
         {
            m_owner.setShowDirection(1);
         }
         else
         {
            m_owner.setShowDirection(-1);
         }
         loadEq();
         load1();
         load2();
         load3();
         load4();
      }
      
      private function loadEq() : void
      {
         if(m_tagx == m_px && m_tagy != m_py)
         {
            if(m_tagy > m_py)
            {
               m_owner.setNewPosition(m_owner.getX(),m_owner.getY() - m_nAvg,m_owner.getZ());
            }
            else
            {
               m_owner.setNewPosition(m_owner.getX(),m_owner.getY() + m_nAvg,m_owner.getZ());
            }
         }
         if(m_tagy == m_py && m_tagx != m_tagx)
         {
            if(m_tagx > m_tagx)
            {
               m_owner.setNewPosition(m_owner.getX() - m_nAvg,m_owner.getY(),m_owner.getZ());
            }
            else
            {
               m_owner.setNewPosition(m_owner.getX() + m_nAvg,m_owner.getY(),m_owner.getZ());
            }
         }
      }
      
      private function load1() : void
      {
         if(m_tagx > m_px && m_tagy > m_py)
         {
            m_nangle = Math.atan(Math.abs(m_tagx - m_px) / Math.abs(m_tagy - m_py));
            m_dy = Math.cos(m_nangle) * m_nAvg;
            m_dx = Math.sin(m_nangle) * m_nAvg;
            m_owner.setNewPosition(m_owner.getX() - m_dx,m_owner.getY() - m_dy,m_owner.getZ());
         }
      }
      
      private function load2() : void
      {
         if(m_tagx < m_px && m_tagy > m_py)
         {
            m_nangle = Math.atan(Math.abs(m_tagx - m_px) / Math.abs(m_tagy - m_py));
            m_dy = Math.cos(m_nangle) * m_nAvg;
            m_dx = Math.sin(m_nangle) * m_nAvg;
            m_owner.setNewPosition(m_owner.getX() + m_dx,m_owner.getY() - m_dy,m_owner.getZ());
         }
      }
      
      private function load3() : void
      {
         if(m_tagx < m_px && m_tagy < m_py)
         {
            m_nangle = Math.atan(Math.abs(m_tagx - m_px) / Math.abs(m_tagy - m_py));
            m_dy = Math.cos(m_nangle) * m_nAvg;
            m_dx = Math.sin(m_nangle) * m_nAvg;
            m_owner.setNewPosition(m_owner.getX() + m_dx,m_owner.getY() + m_dy,m_owner.getZ());
         }
      }
      
      private function load4() : void
      {
         if(m_tagx > m_px && m_tagy < m_py)
         {
            m_nangle = Math.atan(Math.abs(m_tagx - m_px) / Math.abs(m_tagy - m_py));
            m_dy = Math.cos(m_nangle) * m_nAvg;
            m_dx = Math.sin(m_nangle) * m_nAvg;
            m_owner.setNewPosition(m_owner.getX() - m_dx,m_owner.getY() + m_dy,m_owner.getZ());
         }
      }
      
      private function loadOnePos() : void
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            m_px = Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().getX();
            m_py = Part1.getInstance().getLevelEndDancerWorld().getPlayer1().getAnimalEntity().getY();
            return;
         }
      }
      
      private function loadTwoPos() : void
      {
         if(m_owner.isInDie() == false && m_owner.getWorld() && m_owner.getWorld().isStop() == false)
         {
            m_px = Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().getX();
            m_py = Part1.getInstance().getLevelEndDancerWorld().getPlayer2().getAnimalEntity().getY();
            return;
         }
      }
   }
}

