package UI.MiragePanel.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class GotoProtectBtn extends Btn
   {
      
      public function GotoProtectBtn()
      {
         super();
         setTipString("点击前往如来保佑界面");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickGotoProtectMirageBtn"));
      }
   }
}

