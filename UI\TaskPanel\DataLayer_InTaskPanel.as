package UI.TaskPanel
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.filters.DropShadowFilter;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class DataLayer_InTaskPanel extends MySprite
   {
      
      public var titleText1:TextField;
      
      public var titleText2:TextField;
      
      public var taskDescriptionText:TextField;
      
      public var resultText:TextField;
      
      private var _equipmentLayer:Sprite;
      
      public function DataLayer_InTaskPanel()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         _equipmentLayer = null;
         titleText1 = null;
         titleText2 = null;
         taskDescriptionText = null;
         resultText = null;
      }
      
      public function refreshDataLayer(param1:String, param2:String, param3:Vector.<EquipmentVO>, param4:Vector.<TextField>, ... rest) : void
      {
         var _loc10_:DisplayObject = null;
         var _loc7_:Equipment = null;
         var _loc6_:int = 0;
         var _loc8_:int = 0;
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         if(param1 == "")
         {
            titleText1.text = "";
         }
         else
         {
            titleText1.text = "任务描述";
         }
         taskDescriptionText.text = param1;
         taskDescriptionText.x = titleText1.x;
         taskDescriptionText.y = titleText1.y + titleText1.height;
         taskDescriptionText.height = taskDescriptionText.textHeight + 5;
         resultText.text = param2;
         resultText.x = taskDescriptionText.x;
         resultText.y = taskDescriptionText.y + taskDescriptionText.height;
         resultText.height = resultText.textHeight + 5;
         if(rest[0])
         {
            rest[0].x = resultText.x;
            rest[0].y = resultText.y + resultText.height;
         }
         if(rest && rest.length && Boolean(rest[rest.length - 1]))
         {
            titleText2.y = rest[rest.length - 1].y + rest[rest.length - 1].height;
         }
         else
         {
            titleText2.y = resultText.y + resultText.height;
         }
         if((!Boolean(param3) || !param3.length) && (!Boolean(param4) || !param4.length))
         {
            titleText2.text = "";
         }
         else
         {
            titleText2.text = "任务奖励";
         }
         while(numChildren > 4)
         {
            _loc10_ = getChildAt(4);
            if(_loc10_.hasOwnProperty("clear"))
            {
               _loc10_["clear"]();
            }
            removeChildAt(4);
         }
         if(rest[0])
         {
            addChild(rest[0]);
         }
         var _loc9_:int = 0;
         if(param3)
         {
            _loc6_ = int(param3.length);
            _loc9_ = 0;
            while(_loc9_ < _loc6_)
            {
               _loc7_ = MyFunction2.sheatheEquipmentShell(param3[_loc9_]);
               _loc7_.filters = [new DropShadowFilter(5,45,0,1,5,5,1)];
               _loc7_.x = 100 + _loc9_ * _loc7_.width;
               _loc7_.y = titleText2.y + titleText2.height + _loc7_.height / 2;
               _loc7_.addEventListener("rollOver",equipmentInfor,false,0,true);
               _loc7_.addEventListener("rollOut",equipmentInfor,false,0,true);
               addChild(_loc7_);
               _loc9_++;
            }
         }
         if(param4)
         {
            _loc6_ = int(param4.length);
            _loc9_ = 0;
            while(_loc9_ < _loc6_)
            {
               _loc8_ = int(param3.length);
               if(!_loc9_ && _loc8_)
               {
                  param4[_loc9_].x = titleText1.x;
                  param4[_loc9_].y = _loc7_.y + _loc7_.height + _loc9_ * param4[_loc9_].height;
                  addChild(param4[_loc9_] as TextField);
               }
               if(_loc9_)
               {
                  param4[_loc9_].x = param4[_loc9_ - 1].x;
                  param4[_loc9_].y = param4[_loc9_ - 1].y + param4[_loc9_ - 1].height;
                  addChild(param4[_loc9_] as TextField);
               }
               _loc9_++;
            }
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      private function init() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         titleText1.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText2.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText1.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText2.filters = [new GlowFilter(0,1,2,2,10,3)];
         taskDescriptionText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16766337);
         taskDescriptionText.filters = [new GlowFilter(0,1,2,2,10,3)];
         resultText.defaultTextFormat = new TextFormat(_loc1_.fontName,18,52224);
         resultText.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText1.embedFonts = true;
         titleText2.embedFonts = true;
         taskDescriptionText.embedFonts = true;
         resultText.embedFonts = true;
         titleText1.selectable = false;
         titleText2.selectable = false;
         taskDescriptionText.selectable = false;
         resultText.selectable = false;
         taskDescriptionText.mouseWheelEnabled = false;
         resultText.mouseWheelEnabled = false;
         taskDescriptionText.width = this.width - 25;
         resultText.width = this.width - 25;
         _equipmentLayer = new Sprite();
         addChild(_equipmentLayer);
      }
   }
}

