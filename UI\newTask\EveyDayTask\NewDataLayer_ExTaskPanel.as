package UI.newTask.EveyDayTask
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.newTask.NewTaskPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.filters.DropShadowFilter;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class NewDataLayer_ExTaskPanel
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_neweverydaypanel:NewEveryDayPanel;
      
      private var m_neweverydetail:NewEveryDetail;
      
      private var m_show:MovieClip;
      
      public var m_mc:MovieClip;
      
      public var titleText1:TextField;
      
      public var titleText2:TextField;
      
      public var taskDescriptionText:TextField;
      
      public var resultText:TextField;
      
      private var _equipmentLayer:Sprite;
      
      private var m_list:Vector.<Equipment>;
      
      private var m_txtlist:Vector.<TextField>;
      
      public function NewDataLayer_ExTaskPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_list);
         m_list = null;
         ClearUtil.clearObject(m_txtlist);
         m_txtlist = null;
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         _equipmentLayer = null;
         titleText1 = null;
         titleText2 = null;
         taskDescriptionText = null;
         resultText = null;
      }
      
      public function refreshDataLayer(param1:String, param2:String, param3:Vector.<EquipmentVO>, param4:Vector.<TextField>) : void
      {
         var _loc9_:* = null;
         var _loc6_:Equipment = null;
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         ClearUtil.clearDisplayObjectInContainer(_equipmentLayer);
         if(param1 == "")
         {
            titleText1.text = "";
         }
         else
         {
            titleText1.text = "任务描述";
         }
         taskDescriptionText.text = param1;
         taskDescriptionText.x = titleText1.x;
         taskDescriptionText.y = titleText1.y + titleText1.height;
         taskDescriptionText.height = taskDescriptionText.textHeight + 5;
         resultText.text = param2;
         resultText.x = taskDescriptionText.x;
         resultText.y = taskDescriptionText.y + taskDescriptionText.height;
         resultText.height = resultText.textHeight + 5;
         titleText2.y = resultText.y + resultText.height;
         if((!Boolean(param3) || !param3.length) && (!Boolean(param4) || !param4.length))
         {
            titleText2.text = "";
         }
         else
         {
            titleText2.text = "任务奖励";
         }
         ClearUtil.clearObject(m_list);
         m_list = null;
         m_list = new Vector.<Equipment>();
         ClearUtil.clearObject(m_txtlist);
         m_txtlist = null;
         m_txtlist = new Vector.<TextField>();
         var _loc8_:int = 0;
         if(param3)
         {
            _loc5_ = int(param3.length);
            _loc8_ = 0;
            while(_loc8_ < _loc5_)
            {
               _loc6_ = MyFunction2.sheatheEquipmentShell(param3[_loc8_]);
               _loc6_.filters = [new DropShadowFilter(5,45,0,1,5,5,1)];
               _loc6_.x = 100 + _loc8_ * _loc6_.width;
               _loc6_.y = titleText2.y + titleText2.height + _loc6_.height / 2;
               _loc6_.addEventListener("rollOver",equipmentInfor,false,0,true);
               _loc6_.addEventListener("rollOut",equipmentInfor,false,0,true);
               m_mc.addChild(_loc6_);
               m_list.push(_loc6_);
               _loc8_++;
            }
         }
         if(param4)
         {
            _loc5_ = int(param4.length);
            _loc8_ = 0;
            while(_loc8_ < _loc5_)
            {
               _loc7_ = int(param3.length);
               if(!_loc8_ && _loc7_)
               {
                  param4[_loc8_].x = titleText1.x;
                  param4[_loc8_].y = _loc6_.y + _loc6_.height + _loc8_ * param4[_loc8_].height;
                  m_mc.addChild(param4[_loc8_] as TextField);
                  m_txtlist.push(param4[_loc8_]);
               }
               if(_loc8_)
               {
                  param4[_loc8_].x = param4[_loc8_ - 1].x;
                  param4[_loc8_].y = param4[_loc8_ - 1].y + param4[_loc8_ - 1].height;
                  m_mc.addChild(param4[_loc8_] as TextField);
                  m_txtlist.push(param4[_loc8_]);
               }
               _loc8_++;
            }
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               (m_show["everydaypanel"] as MovieClip).dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
               break;
            case "rollOut":
               (m_show["everydaypanel"] as MovieClip).dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
      
      public function show() : void
      {
      }
      
      public function hide() : void
      {
         ClearUtil.clearObject(m_list);
         m_list = null;
         ClearUtil.clearObject(m_txtlist);
         m_txtlist = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewEveryDayPanel, param4:NewEveryDetail) : void
      {
         m_newtaskpanel = param1;
         m_show = param2;
         m_neweverydaypanel = param3;
         m_neweverydetail = param4;
         m_mc = (m_show["everydaypanel"] as MovieClip)["dataLayer"] as MovieClip;
         titleText1 = m_mc["titleText1"] as TextField;
         titleText2 = m_mc["titleText2"] as TextField;
         taskDescriptionText = m_mc["taskDescriptionText"] as TextField;
         resultText = m_mc["resultText"] as TextField;
         loadUI();
      }
      
      private function loadUI() : void
      {
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         titleText1.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText2.defaultTextFormat = new TextFormat(_loc1_.fontName,30,16737792);
         titleText1.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText2.filters = [new GlowFilter(0,1,2,2,10,3)];
         taskDescriptionText.defaultTextFormat = new TextFormat(_loc1_.fontName,20,16766337);
         taskDescriptionText.filters = [new GlowFilter(0,1,2,2,10,3)];
         resultText.defaultTextFormat = new TextFormat(_loc1_.fontName,18,52224);
         resultText.filters = [new GlowFilter(0,1,2,2,10,3)];
         titleText1.embedFonts = true;
         titleText2.embedFonts = true;
         taskDescriptionText.embedFonts = true;
         resultText.embedFonts = true;
         titleText1.selectable = false;
         titleText2.selectable = false;
         taskDescriptionText.selectable = false;
         resultText.selectable = false;
         taskDescriptionText.mouseWheelEnabled = false;
         resultText.mouseWheelEnabled = false;
         taskDescriptionText.width = m_mc.width - 25;
         resultText.width = m_mc.width - 25;
         _equipmentLayer = new Sprite();
         m_mc.addChild(_equipmentLayer);
      }
   }
}

