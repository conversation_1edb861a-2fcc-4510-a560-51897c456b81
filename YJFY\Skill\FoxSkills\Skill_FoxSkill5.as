package YJFY.Skill.FoxSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_DogBigSkill;
   import YJFY.Skill.SkillLogic.EffectShowAddToTargetInHurt;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_FoxSkill5 extends CuboidAreaAttackSkill2_DogBigSkill
   {
      
      private const m_const_startDianFrameLabel:String = "startDian";
      
      private const m_const_endDianFrameLabel:String = "endDian";
      
      private const m_const_shake_frameLabel1:String = "shake1";
      
      private const m_const_shake_frameLabel2:String = "shake2";
      
      private const m_const_shake_frameLabel3:String = "shake3";
      
      private const m_const_shake_frameLabel4:String = "shake4";
      
      private var m_effectAddtoTargetId:String;
      
      private var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      private var m_effectShowAddToTargetInHurt:EffectShowAddToTargetInHurt;
      
      private var m_isDian:Boolean;
      
      private var m_shakeViewDataOfPoints1:DataOfPoints;
      
      private var m_shakeViewDataOfPoints2:DataOfPoints;
      
      public function Skill_FoxSkill5()
      {
         super();
         m_effectShowAddToTargetInHurt = new EffectShowAddToTargetInHurt();
         m_effectShowAddToTargetInHurt.setIsHideTargetBodyShow(true);
         m_shakeViewDataOfPoints1 = new DataOfPoints();
         m_shakeViewDataOfPoints2 = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         m_effectAddtoTargetId = null;
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowAddToTargetInHurt);
         m_effectShowAddToTargetInHurt = null;
         ClearUtil.clearObject(m_shakeViewDataOfPoints1);
         m_shakeViewDataOfPoints1 = null;
         ClearUtil.clearObject(m_shakeViewDataOfPoints2);
         m_shakeViewDataOfPoints2 = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_effectAddtoTargetId = String(param1.@effectAddtoTargetId);
         if(m_effectAddtoTargetId)
         {
            m_effectAddtoTargetDefinitionData = new AnimationDefinitionData();
            m_effectAddtoTargetDefinitionData.initByXML(param1.animationDefinition.(@id == m_effectAddtoTargetId)[0]);
            m_effectShowAddToTargetInHurt.setEffectAddToTargetDefinitionData(m_effectAddtoTargetDefinitionData);
         }
         var _loc3_:String = String(param1.shakeView1[0].@swfPath);
         var _loc2_:String = String(param1.shakeView1[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess1,getShakeViewMovieClipFailFun);
         _loc3_ = String(param1.shakeView2[0].@swfPath);
         _loc2_ = String(param1.shakeView2[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess2,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_effectShowAddToTargetInHurt.setWorld(param1);
         super.releaseSkill2(param1);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && m_attackData.getHurtDuration())
         {
            if(m_isDian)
            {
               m_effectShowAddToTargetInHurt.addEffectShowAddToTarget(param1);
            }
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "startDian":
               m_isDian = true;
               break;
            case "endDian":
               m_isDian = false;
               break;
            case "shake1":
               m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints2);
               break;
            case "shake2":
               m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints1);
               break;
            case "shake3":
               m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints2);
               break;
            case "shake4":
               m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints1);
         }
      }
      
      private function getShakeViewMovieClipSuccess1(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints1.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipSuccess2(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints2.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

