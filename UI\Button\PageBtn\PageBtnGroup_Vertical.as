package UI.Button.PageBtn
{
   public class PageBtnGroup_Vertical extends PageBtnGroup_Parent
   {
      
      public function PageBtnGroup_Vertical()
      {
         super();
      }
      
      override protected function init() : void
      {
         _showSprite = new PageBtnGroup_V_ShowPart();
         super.init();
      }
      
      public function changeBtnCoord(param1:Number = 0) : void
      {
         (_showSprite as PageBtnGroup_V_ShowPart).pageBtnDown.y += param1;
         (_showSprite as PageBtnGroup_V_ShowPart).pageBtnUp.y -= param1;
      }
   }
}

