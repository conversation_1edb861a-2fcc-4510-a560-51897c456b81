package UI.XiangMoLevelPanel
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.LoadUI2;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import YJFY.ShowLogicShell.SwitchBtn.HaveLockSwitchBtnLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class XiangMoLevelPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_fuBenDataShows:Vector.<FuBenDataShow>;
      
      private var m_fuBenNames:Vector.<MovieClip>;
      
      private var m_fuBenbtnDataShowSwitchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_descriptionText:TextField;
      
      private var m_easySwitchBtn:MySwitchBtnLogicShell;
      
      private var m_difficultySwitchBtn:MySwitchBtnLogicShell;
      
      private var m_hellSwitchBtn:MySwitchBtnLogicShell;
      
      private var m_nanDuSwitchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_startBtn:ButtonLogicShell2;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_rewardEquipments:Vector.<Equipment>;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_xiangMoLevelData:XiangMoLevelData;
      
      private var m_xiangMoLevelSaveData:XiangMoLevelSaveData;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_gamingUI:GamingUI;
      
      public function XiangMoLevelPanel()
      {
         super();
         m_fuBenDataShows = new Vector.<FuBenDataShow>();
         m_fuBenNames = new Vector.<MovieClip>();
         m_fuBenbtnDataShowSwitchBtnGroup = new SwitchBtnGroupLogicShell(HaveLockSwitchBtnLogicShell);
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_easySwitchBtn = new MySwitchBtnLogicShell();
         m_difficultySwitchBtn = new MySwitchBtnLogicShell();
         m_hellSwitchBtn = new MySwitchBtnLogicShell();
         m_nanDuSwitchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_nanDuSwitchBtnGroup.addSwitchBtn(m_easySwitchBtn);
         m_nanDuSwitchBtnGroup.addSwitchBtn(m_difficultySwitchBtn);
         m_nanDuSwitchBtnGroup.addSwitchBtn(m_hellSwitchBtn);
         m_startBtn = new ButtonLogicShell2();
         m_eqCells = new Vector.<Sprite>();
         m_rewardEquipments = new Vector.<Equipment>();
         m_quitBtn = new ButtonLogicShell2();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_fuBenDataShows);
         m_fuBenDataShows = null;
         ClearUtil.clearObject(m_fuBenNames);
         m_fuBenNames = null;
         ClearUtil.clearObject(m_fuBenbtnDataShowSwitchBtnGroup);
         m_fuBenbtnDataShowSwitchBtnGroup = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         m_descriptionText = null;
         ClearUtil.clearObject(m_easySwitchBtn);
         m_easySwitchBtn = null;
         ClearUtil.clearObject(m_difficultySwitchBtn);
         m_difficultySwitchBtn = null;
         ClearUtil.clearObject(m_hellSwitchBtn);
         m_hellSwitchBtn = null;
         ClearUtil.clearObject(m_nanDuSwitchBtnGroup);
         m_nanDuSwitchBtnGroup = null;
         ClearUtil.clearObject(m_startBtn);
         m_startBtn = null;
         ClearUtil.clearObject(m_eqCells);
         m_eqCells = null;
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_xiangMoLevelData);
         m_xiangMoLevelData = null;
         m_xiangMoLevelSaveData = null;
         m_versionControl = null;
         m_loadUI = null;
         m_gamingUI = null;
         super.clear();
      }
      
      public function init(param1:IVersionControl, param2:IProgressShow, param3:GamingUI) : void
      {
         var versionControl:IVersionControl = param1;
         var loadUI:IProgressShow = param2;
         var gamingUI:GamingUI = param3;
         m_versionControl = versionControl;
         m_loadUI = loadUI;
         m_gamingUI = gamingUI;
         m_xiangMoLevelSaveData = XiangMoLevelSaveData.getInstance();
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            init2();
         },fail,true);
      }
      
      private function init2() : void
      {
         if(m_xiangMoLevelSaveData.isResetData(GamingUI.getInstance().getNewestTimeStrFromSever()))
         {
            m_xiangMoLevelSaveData.resetData(GamingUI.getInstance().getNewestTimeStrFromSever());
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         (m_loadUI as LoadUI2).tranToTransparentcy();
         m_myLoader.getXML("NewGameFolder/LevelMode2/levelMode2.xml",getXMLSuccess,getFail);
         m_myLoader.getClass("UISprite2/XiangMoLevelPanel.swf","XiangMoLevelPanel",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function initShow() : void
      {
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:DisplayObject = null;
         var _loc1_:FuBenDataShow = null;
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_quitBtn.setTipString("点击关闭");
         var _loc2_:uint = uint(m_show.numChildren);
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc3_ = m_show.getChildAt(_loc6_);
            if(_loc3_.name.substr(0,"fuBenSwitchBtn_".length) == "fuBenSwitchBtn_")
            {
               _loc5_++;
            }
            if(_loc3_.name.substr(0,"eqCell_".length) == "eqCell_")
            {
               _loc4_++;
            }
            _loc6_++;
         }
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc1_ = new FuBenDataShow();
            _loc1_.setShow(m_show["fuBenSwitchBtn_" + (_loc6_ + 1)]);
            m_fuBenDataShows.push(_loc1_);
            m_fuBenbtnDataShowSwitchBtnGroup.addSwitchBtn(_loc1_);
            m_fuBenNames.push(m_show["fuBenSwitchName_" + (_loc6_ + 1)]);
            _loc6_++;
         }
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_easySwitchBtn.setShow(m_show["easyBtn"]);
         m_easySwitchBtn.setTipString("点击选择简单副本");
         m_difficultySwitchBtn.setShow(m_show["difficultyBtn"]);
         m_difficultySwitchBtn.setTipString("点击选择困难副本");
         m_hellSwitchBtn.setShow(m_show["hellBtn"]);
         m_hellSwitchBtn.setTipString("点击选择地狱般难度的副本");
         m_startBtn.setShow(m_show["startBtn"]);
         m_startBtn.setTipString("点击开始愉快的副本之旅吧！");
         m_descriptionText = m_show["descriptionText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_descriptionText);
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc6_ + 1)]);
            _loc6_++;
         }
      }
      
      private function initShow2() : void
      {
         if(m_xiangMoLevelData == null || m_show == null)
         {
            return;
         }
         setPageBtn(1);
         arrangeFuBenShow((m_pageBtnGroup.pageNum - 1) * m_fuBenDataShows.length);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(m_fuBenDataShows.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_fuBenDataShows[_loc3_])
            {
               m_easySwitchBtn.turnActiveAndDispatchEvent();
            }
            _loc3_++;
         }
         switch(param1.button)
         {
            case m_easySwitchBtn:
            case m_difficultySwitchBtn:
            case m_hellSwitchBtn:
               showFuBenData(param1.button);
               break;
            case m_pageBtnGroup:
               arrangeFuBenShow((m_pageBtnGroup.pageNum - 1) * m_fuBenDataShows.length);
               break;
            case m_quitBtn:
               m_gamingUI.closeXiangMoLevelPanel();
               break;
            case m_startBtn:
               startGame();
         }
      }
      
      private function startGame() : void
      {
         var _loc4_:NanDuData = null;
         if(m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() == null)
         {
            return;
         }
         if((m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getRemainNum() <= 0)
         {
            m_gamingUI.showMessageTip("剩余次数不足");
            return;
         }
         if(m_nanDuSwitchBtnGroup.currentActivateBtn() == m_easySwitchBtn)
         {
            _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getEasyNanDuData();
         }
         else if(m_nanDuSwitchBtnGroup.currentActivateBtn() == m_difficultySwitchBtn)
         {
            _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getDifficultyNanDuData();
         }
         else
         {
            _loc4_ = _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getHellNanDuData();
         }
         var _loc3_:String = _loc4_.getLevelXMLPath();
         var _loc2_:XiangMoLevelSaveOneData = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getXiangMoLevelSaveOneData();
         m_gamingUI.closeXiangMoLevelPanel();
         Part1.getInstance().closeCityMap();
         Part1.getInstance().openLevel2(_loc3_);
         _loc2_.addOneChanllengeNum();
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_xiangMoLevelData == null || m_xiangMoLevelData.getFuBenDataNum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_xiangMoLevelData.getFuBenDataNum() % m_fuBenDataShows.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_xiangMoLevelData.getFuBenDataNum() / m_fuBenDataShows.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_xiangMoLevelData.getFuBenDataNum() / m_fuBenDataShows.length) + 1);
         }
      }
      
      private function arrangeFuBenShow(param1:int) : void
      {
         var _loc6_:* = 0;
         var _loc3_:FuBenData = null;
         var _loc5_:int = param1 + m_fuBenDataShows.length;
         var _loc2_:int = m_xiangMoLevelData ? m_xiangMoLevelData.getFuBenDataNum() : 0;
         var _loc4_:int = 0;
         _loc6_ = param1;
         while(_loc6_ < _loc5_ && _loc6_ < _loc2_)
         {
            _loc3_ = m_xiangMoLevelData.getFuBenDataByIndex(_loc6_);
            m_fuBenDataShows[_loc4_].setData(_loc3_,m_xiangMoLevelSaveData.getSaveOneDataByFuBenId(_loc3_.getFuBenId()));
            m_fuBenNames[_loc4_].gotoAndStop(_loc3_.getShowFrameLabel());
            _loc4_++;
            _loc6_++;
         }
         while(_loc4_ < m_fuBenDataShows.length)
         {
            m_fuBenDataShows[_loc4_].setEmpty();
            m_fuBenNames[_loc4_].gotoAndStop("empty");
            _loc4_++;
         }
         m_fuBenbtnDataShowSwitchBtnGroup.addEnd();
      }
      
      private function showFuBenData(param1:IButton) : void
      {
         var _loc4_:NanDuData = null;
         var _loc5_:int = 0;
         var _loc3_:Equipment = null;
         if(m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() == null)
         {
            return;
         }
         if(param1 == m_easySwitchBtn)
         {
            _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getEasyNanDuData();
         }
         else if(param1 == m_difficultySwitchBtn)
         {
            _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getDifficultyNanDuData();
         }
         else
         {
            _loc4_ = _loc4_ = (m_fuBenbtnDataShowSwitchBtnGroup.currentActivateBtn() as FuBenDataShow).getFuBenData().getHellNanDuData();
         }
         m_descriptionText.text = _loc4_.getDescription();
         ClearUtil.clearObject(m_rewardEquipments);
         m_rewardEquipments.length = 0;
         var _loc2_:int = Math.min(m_eqCells.length,_loc4_.getEquipmentVONum());
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(_loc4_.getEquipmentVOByIndex(_loc5_));
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_rewardEquipments.push(_loc3_);
            m_eqCells[_loc5_].addChild(_loc3_);
            _loc5_++;
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         m_show = new param1.resultClass();
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:XML = param1.resultXML;
         ClearUtil.clearObject(m_xiangMoLevelData);
         m_xiangMoLevelData = new XiangMoLevelData();
         m_xiangMoLevelData.initByXML(_loc2_);
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         fail("资源加载失败");
      }
      
      private function fail(param1:String) : void
      {
         if(param1)
         {
            m_gamingUI.showMessageTip(param1);
         }
         m_gamingUI.closeXiangMoLevelPanel();
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

