package UI
{
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class MySprite extends Sprite
   {
      
      public function MySprite()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearDisplayObjectInContainer(this);
         if(parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

