package UI.SocietySystem
{
   import UI.DataManagerParent;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerTheSocietyData;
   import UI.SocietySystem.SocietyShop.SocietyShopSaveData;
   import YJFY.Utils.ClearUtil;
   
   public class SocietyDataVO extends DataManagerParent
   {
      
      private var m_societyId:int;
      
      private var m_societyName:String;
      
      private var m_personalTotalConValue:int;
      
      private var m_personalReConValue:int;
      
      private var m_societyLevel:int;
      
      private var m_playerNumInSociety:int;
      
      private var m_playerMaxNumInSociety:int;
      
      private var m_uid_leader:Number;
      
      private var m_idx_leader:int;
      
      private var m_name_leader:String;
      
      private var m_societyTotalConValue:int;
      
      private var m_societyRank:int;
      
      private var m_announcementOfSociety:String = "";
      
      private var m_dissovleRemainTime:int;
      
      private var m_signVO:SocietySignVO;
      
      private var m_societyContriVO:SocietyContriVO;
      
      private var m_societyShopVO:SocietyShopSaveData;
      
      public function SocietyDataVO()
      {
         super();
         m_signVO = new SocietySignVO();
         m_societyContriVO = new SocietyContriVO();
         m_societyShopVO = new SocietyShopSaveData();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_signVO);
         m_signVO = null;
         ClearUtil.clearObject(m_societyContriVO);
         m_societyContriVO = null;
         ClearUtil.clearObject(m_societyShopVO);
         m_societyShopVO = null;
      }
      
      public function initDataFromSaveXML(param1:XML) : void
      {
         if(param1.hasOwnProperty("Society") == false)
         {
            return;
         }
         var _loc5_:XML = param1.Society[0];
         var _loc4_:XML = _loc5_.sign[0];
         if(_loc4_)
         {
            m_signVO.initFromSaveXML(_loc4_);
         }
         var _loc3_:XML = _loc5_.contri[0];
         if(_loc3_)
         {
            m_societyContriVO.initFromSaveXML(_loc3_);
         }
         var _loc2_:XML = _loc5_.shop[0];
         if(_loc2_)
         {
            m_societyShopVO.initFromSaveXML(_loc2_);
         }
      }
      
      public function exportSaveXML() : XML
      {
         var _loc2_:XML = <Society />
;
         var _loc4_:XML = m_signVO.exportSaveXML();
         _loc2_.appendChild(_loc4_);
         var _loc3_:XML = m_societyContriVO.exportSaveXML();
         _loc2_.appendChild(_loc3_);
         var _loc1_:XML = m_societyShopVO.exportSaveXML();
         _loc2_.appendChild(_loc1_);
         return _loc2_;
      }
      
      public function getDataFromDownSocietyData(param1:DOWN_PlayerTheSocietyData) : void
      {
         m_societyId = param1.getSocietyId();
         m_societyName = param1.getSocietyName();
         m_personalTotalConValue = param1.getPersonalTotalConValue();
         m_personalReConValue = param1.getPersonalReConValue();
         m_societyLevel = param1.getSocietyLevel();
         m_playerNumInSociety = param1.getPlayerNumInSociety();
         m_playerMaxNumInSociety = param1.getPlayerMaxNumINSociety();
         m_uid_leader = param1.getUid_leader();
         m_idx_leader = param1.getIdx_leader();
         m_name_leader = param1.getName_leader();
         m_societyTotalConValue = param1.getSocietyTotalConValue();
         m_announcementOfSociety = param1.getAnnouncementOfSociety();
         m_societyRank = param1.getSocietyRank();
         m_dissovleRemainTime = param1.getDissolveSocietyRemainTime();
         trace("dissovleRemainTime 解散帮会剩余时间",m_dissovleRemainTime);
      }
      
      public function clearSocietyData() : void
      {
         m_societyId = 0;
         m_societyName = null;
         m_personalTotalConValue = 0;
         m_personalReConValue = 0;
         m_societyLevel = 0;
         m_playerNumInSociety = 0;
         m_playerMaxNumInSociety = 0;
         m_uid_leader = 0;
         m_idx_leader = 0;
         m_name_leader = null;
         m_societyTotalConValue = 0;
         m_announcementOfSociety = null;
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getSocietyName() : String
      {
         return m_societyName;
      }
      
      public function getPersonalTotalConValue() : int
      {
         return m_personalTotalConValue;
      }
      
      public function getPersonalReConValue() : int
      {
         return m_personalReConValue;
      }
      
      public function getSocietyLevel() : int
      {
         return m_societyLevel;
      }
      
      public function getPlayerNumInSociety() : int
      {
         return m_playerNumInSociety;
      }
      
      public function getPlayerMaxNumINSociety() : int
      {
         return m_playerMaxNumInSociety;
      }
      
      public function getUid_leader() : Number
      {
         return m_uid_leader;
      }
      
      public function getIdx_leader() : int
      {
         return m_idx_leader;
      }
      
      public function getName_leader() : String
      {
         return m_name_leader;
      }
      
      public function getSocietyTotalConValue() : int
      {
         return m_societyTotalConValue;
      }
      
      public function getSocietyRank() : int
      {
         return m_societyRank;
      }
      
      public function getAnnouncementOfSociety() : String
      {
         return m_announcementOfSociety;
      }
      
      public function getDissolveRemainTime() : int
      {
         return m_dissovleRemainTime;
      }
      
      public function setDissolveRematinTimeToZero() : void
      {
         m_dissovleRemainTime = 0;
      }
      
      public function getSocietySignVO() : SocietySignVO
      {
         return m_signVO;
      }
      
      public function getSocietyContributionVO() : SocietyContriVO
      {
         return m_societyContriVO;
      }
      
      public function getSocietyShopVO() : SocietyShopSaveData
      {
         return m_societyShopVO;
      }
   }
}

