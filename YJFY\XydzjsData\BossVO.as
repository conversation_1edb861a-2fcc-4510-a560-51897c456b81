package YJFY.XydzjsData
{
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.AISkillVO.AIActiveSkillVO;
   import YJFY.XydzjsData.AISkillVO.AIAttackSkillVO;
   import YJFY.XydzjsData.AISkillVO.AISkillVOFactory;
   
   public class BossVO
   {
      
      private var m_bossSkillVOs:Vector.<AIActiveSkillVO>;
      
      private var m_hpSegment:uint;
      
      public function BossVO()
      {
         super();
         m_bossSkillVOs = new Vector.<AIActiveSkillVO>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_bossSkillVOs);
         m_bossSkillVOs = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc6_:int = 0;
         var _loc5_:AIActiveSkillVO = null;
         hpSegment = uint(param1.@hpSegment);
         var _loc3_:AISkillVOFactory = new AISkillVOFactory();
         var _loc2_:XMLList = param1.skill;
         var _loc4_:int = int(_loc2_.length());
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = _loc3_.createBossSkillVOByClassName(_loc2_[_loc6_].@className);
            _loc5_.initFromXML(_loc2_[_loc6_]);
            m_bossSkillVOs.push(_loc5_);
            _loc6_++;
         }
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
      }
      
      public function isAbleRunSkill(param1:String, param2:World, param3:EnemyVO) : Boolean
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(m_bossSkillVOs.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(m_bossSkillVOs[_loc5_].getSkillId() == param1)
            {
               return m_bossSkillVOs[_loc5_].isAbleRunSkill(param2,param3);
            }
            _loc5_++;
         }
         throw new Error("没有技能" + param1 + "出错了");
      }
      
      public function runSkill(param1:String, param2:World, param3:EnemyVO) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(m_bossSkillVOs.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(m_bossSkillVOs[_loc5_].getSkillId() == param1)
            {
               m_bossSkillVOs[_loc5_].runSkill(param2,param3);
            }
            _loc5_++;
         }
      }
      
      public function getSkillVOBySkillId(param1:String) : AIAttackSkillVO
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_bossSkillVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_bossSkillVOs[_loc3_].getSkillId() == param1)
            {
               if(!(m_bossSkillVOs[_loc3_] is AIAttackSkillVO))
               {
                  throw new Error("skill is not BossAttackSkillVO");
               }
               return m_bossSkillVOs[_loc3_] as AIAttackSkillVO;
            }
            _loc3_++;
         }
         return null;
      }
      
      public function getBossSkillVOById(param1:String) : AIActiveSkillVO
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_bossSkillVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_bossSkillVOs[_loc3_].getSkillId() == param1)
            {
               return m_bossSkillVOs[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function getHpSegment() : uint
      {
         return m_hpSegment;
      }
      
      private function set hpSegment(param1:uint) : void
      {
         m_hpSegment = param1;
      }
   }
}

