package UI.MainLineTask.TaskRewardVO
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class EquipmentRewardVO extends TaskRewardVO
   {
      
      private var m_equipmentVOs:Vector.<EquipmentVO>;
      
      public function EquipmentRewardVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_equipmentVOs);
         m_equipmentVOs = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_equipmentVOs = XMLSingle.getEquipmentVOs(param1,XMLSingle.getInstance().equipmentXML,false);
      }
      
      public function getEquipmentVOs() : Vector.<EquipmentVO>
      {
         return m_equipmentVOs;
      }
   }
}

