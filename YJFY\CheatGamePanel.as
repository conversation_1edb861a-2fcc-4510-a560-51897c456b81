package YJFY
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class CheatGamePanel extends UI.MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_appealBtn:ButtonLogicShell;
      
      public function CheatGamePanel()
      {
         super();
         m_appealBtn = new ButtonLogicShell();
         addEventListener("clickButton",clickButton,true,9,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_appealBtn);
         m_appealBtn = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("CheatGameTip") as MovieClip;
         addChild(m_show);
         m_appealBtn.setShow(m_show["btn"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_appealBtn !== _loc2_)
         {
         }
      }
   }
}

