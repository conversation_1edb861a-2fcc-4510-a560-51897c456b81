package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.IFriend;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   
   public class TimeLimitEntity_MonkeySkill5 extends TimeLimitEntity implements IFriend
   {
      
      public function TimeLimitEntity_MonkeySkill5()
      {
         super();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(param1.substr(0,"attackReach".length) == "attackReach")
         {
            attackReach();
         }
      }
   }
}

