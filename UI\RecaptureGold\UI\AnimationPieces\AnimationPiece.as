package UI.RecaptureGold.UI.AnimationPieces
{
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.filters.GlowFilter;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class AnimationPiece extends MySprite
   {
      
      public static const SHOW:String = "show";
      
      public static const DRAW:String = "draw";
      
      private static var _drawTarget:Sprite;
      
      private static var _showTarget:Sprite;
      
      public static var maskTarget:Sprite;
      
      public static var maskOption:String = "show";
      
      public function AnimationPiece()
      {
         super();
         if(maskOption == "show")
         {
            if(<PERSON><PERSON><PERSON>(_showTarget) && maskTarget)
            {
               _showTarget.mask = maskTarget;
            }
         }
         else if(<PERSON><PERSON><PERSON>(_drawTarget) && maskTarget)
         {
            _drawTarget.mask = maskTarget;
         }
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public static function set drawTarget(param1:Sprite) : void
      {
         if(_drawTarget)
         {
            _drawTarget.mouseChildren = true;
            _drawTarget.mouseEnabled = true;
         }
         _drawTarget = param1;
         if(_drawTarget)
         {
            _drawTarget.mouseChildren = false;
            _drawTarget.mouseEnabled = false;
         }
      }
      
      public static function set showTarget(param1:Sprite) : void
      {
         if(_showTarget)
         {
            _showTarget.mouseChildren = true;
            _showTarget.mouseEnabled = true;
         }
         _showTarget = param1;
         if(_showTarget)
         {
            _showTarget.mouseChildren = false;
            _showTarget.mouseEnabled = false;
         }
      }
      
      public static function get showTarget() : Sprite
      {
         return _showTarget;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(_drawTarget);
         ClearUtil.clearDisplayObjectInContainer(_showTarget);
         ClearUtil.clearDisplayObjectInContainer(maskTarget);
         _drawTarget = null;
         _showTarget = null;
         maskTarget = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         var _loc5_:BitmapData = null;
         var _loc2_:BitmapData = null;
         var _loc4_:BitmapData = null;
         var _loc7_:Point = null;
         var _loc8_:Rectangle = null;
         var _loc6_:Bitmap = null;
         var _loc3_:Sprite = null;
         if(_drawTarget)
         {
            _loc5_ = new BitmapData(this.width,this.height,true,0);
            _loc2_ = new BitmapData(this.width,this.height,true,0);
            _loc2_.draw(this);
            _loc4_ = new BitmapData(_drawTarget.width,_drawTarget.height,true,0);
            _loc7_ = parent.localToGlobal(new Point(this.x,this.y));
            _loc8_ = new Rectangle(_loc7_.x,_loc7_.y,this.width,this.height);
            _loc4_.draw(_drawTarget);
            _loc5_.copyPixels(_loc4_,_loc8_,new Point(0,0),_loc2_,new Point(0,0));
            _loc6_ = new Bitmap(_loc5_);
            addChild(_loc6_);
            if(maskOption == "show")
            {
               if(_showTarget)
               {
                  _showTarget.filters = [];
               }
            }
            else if(_drawTarget)
            {
               _drawTarget.filters = [];
            }
            _loc3_ = this.getChildAt(0) as Sprite;
            _loc3_.x = _loc7_.x;
            _loc3_.y = _loc7_.y;
            maskTarget.addChild(_loc3_);
            if(maskOption == "show")
            {
               if(_showTarget)
               {
                  _showTarget.filters = [new GlowFilter(0,1,10,10,1,3,true)];
               }
            }
            else if(_drawTarget)
            {
               _drawTarget.filters = [new GlowFilter(0,1,10,10,1,3,false)];
            }
         }
      }
   }
}

