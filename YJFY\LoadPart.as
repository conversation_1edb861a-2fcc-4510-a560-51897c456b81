package YJFY
{
   import YJFY.Loader.IProgressShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import com.greensock.loading.LoaderMax;
   
   public class LoadPart
   {
      
      private static var m_instance:LoadPart;
      
      private var m_loaderPartOnesObj:Object;
      
      private var m_loaderPartOnes:Vector.<LoadPartOne>;
      
      private var m_isLoadParts:Boolean;
      
      private var m_configXML:XML;
      
      private var m_versionControl:IVersionControl;
      
      private var m_progressShow:IProgressShow;
      
      public function LoadPart()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            m_loaderPartOnes = new Vector.<LoadPartOne>();
            m_loaderPartOnesObj = {};
            return;
         }
         throw new Error("实例已经存在了");
      }
      
      public static function getInstance() : LoadPart
      {
         if(m_instance == null)
         {
            m_instance = new LoadPart();
         }
         return m_instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_loaderPartOnesObj);
         m_loaderPartOnesObj = null;
         ClearUtil.clearObject(m_loaderPartOnes);
         m_loaderPartOnes = null;
         m_configXML = null;
         m_versionControl = null;
         m_progressShow = null;
         m_instance = null;
      }
      
      public function init(param1:XML, param2:IVersionControl, param3:IProgressShow) : void
      {
         m_configXML = param1;
         m_versionControl = param2;
         m_progressShow = param3;
      }
      
      public function loadOnePart1(param1:Function, param2:String) : void
      {
         (m_progressShow as LoadUI2).tranToOpacity();
         loadOnePart_in(param1,param2);
      }
      
      public function loadOnePart3(param1:Function, param2:String, param3:String) : void
      {
         (m_progressShow as LoadUI2).tranToOpacity();
         loadOnePart_in2(param1,param2,param3);
      }
      
      public function loadOnePart2(param1:Function, param2:String) : void
      {
         (m_progressShow as LoadUI2).tranToTransparentcy();
         loadOnePart_in(param1,param2);
      }
      
      public function loadParts1(param1:Function, param2:Array) : void
      {
         (m_progressShow as LoadUI2).tranToOpacity();
         loadParts_in(param1,param2);
      }
      
      public function loadParts2(param1:Function, param2:Array) : void
      {
         (m_progressShow as LoadUI2).tranToTransparentcy();
         loadParts_in(param1,param2);
      }
      
      private function loadParts_in(param1:Function, param2:Array) : void
      {
         if(m_isLoadParts)
         {
            throw new Error();
         }
         var _loc3_:int = int(param2.length);
         loaderPartsOne(_loc3_,param2,param1);
      }
      
      private function loaderPartsOne(param1:uint, param2:Array, param3:Function) : void
      {
         var remaineNum:uint = param1;
         var partNames:Array = param2;
         var completeFun:Function = param3;
         if(remaineNum)
         {
            --remaineNum;
            loadOnePart_in(function():void
            {
               loaderPartsOne(remaineNum,partNames,completeFun);
            },partNames[remaineNum]);
         }
         else
         {
            m_isLoadParts = false;
            if(Boolean(completeFun))
            {
               completeFun();
            }
         }
      }
      
      private function loadOnePart_in(param1:Function, param2:String) : void
      {
         if(m_loaderPartOnesObj[param2] == null)
         {
            m_loaderPartOnesObj[param2] = new LoadPartOne();
            m_loaderPartOnes.push(m_loaderPartOnesObj[param2]);
            (m_loaderPartOnesObj[param2] as LoadPartOne).init(m_progressShow,m_versionControl);
            getAndAddPart(param2,m_loaderPartOnesObj[param2]);
         }
         (m_loaderPartOnesObj[param2] as LoadPartOne).addCompleteFun(param1);
         (m_loaderPartOnesObj[param2] as LoadPartOne).load();
      }
      
      private function loadOnePart_in2(param1:Function, param2:String, param3:String) : void
      {
         if(m_loaderPartOnesObj[param2] == null)
         {
            m_loaderPartOnesObj[param2] = new LoadPartOne();
            m_loaderPartOnes.push(m_loaderPartOnesObj[param2]);
            (m_loaderPartOnesObj[param2] as LoadPartOne).init(m_progressShow,m_versionControl);
            (m_loaderPartOnesObj[param2] as LoadPartOne).addLoadSwfPath(param3);
         }
         (m_loaderPartOnesObj[param2] as LoadPartOne).addCompleteFun(param1);
         (m_loaderPartOnesObj[param2] as LoadPartOne).load();
      }
      
      public function returnClass(param1:String) : Class
      {
         return LoaderMax.getClass(param1);
      }
      
      public function unLoadOnePart(param1:String) : void
      {
         if(m_loaderPartOnesObj[param1])
         {
            m_loaderPartOnes.splice(m_loaderPartOnes.indexOf(m_loaderPartOnesObj[param1]),1);
            ClearUtil.clearObject(m_loaderPartOnesObj[param1]);
            m_loaderPartOnesObj[param1] = null;
         }
      }
      
      private function getAndAddPart(param1:String, param2:LoadPartOne) : void
      {
         var _loc11_:int = 0;
         var _loc9_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:String = null;
         var _loc3_:String = null;
         var _loc5_:* = null;
         var _loc10_:XMLList = m_configXML[param1][0].item;
         var _loc6_:int = int(_loc10_.length());
         var _loc7_:XMLList = m_configXML.swf;
         _loc8_ = int(_loc7_ ? _loc7_.length() : 0);
         _loc11_ = 0;
         while(_loc11_ < _loc6_)
         {
            _loc4_ = XML(_loc10_[_loc11_]);
            _loc3_ = null;
            if(Boolean(_loc3_) == false)
            {
               _loc3_ = _loc4_ + ".swf";
            }
            param2.addLoadSwfPath(_loc3_);
            _loc11_++;
         }
      }
   }
}

