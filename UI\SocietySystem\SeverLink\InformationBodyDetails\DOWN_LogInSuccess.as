package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class DOWN_LogInSuccess extends InformationBodyDetail
   {
      
      protected var m_uid:Number;
      
      protected var m_idx:int;
      
      protected var m_isHaveSociety:int;
      
      public function DOWN_LogInSuccess()
      {
         super();
         m_informationBodyId = 3001;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid = _loc2_.uid;
         m_idx = _loc2_.idx;
         m_isHaveSociety = param1.readInt();
      }
      
      public function getUid() : Number
      {
         return m_uid;
      }
      
      public function getIdx() : int
      {
         return m_idx;
      }
      
      public function getIsHaveSociety() : Boolean
      {
         return Boolean(m_isHaveSociety);
      }
   }
}

