package YJFY.Skill.FoxSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   
   public class Skill_FoxSkill4 extends CuboidAreaAttackSkill_OneSkillShow implements IPushSkill
   {
      
      private const m_const_skillAttackReachFrameLabel:String = "skillAttackReach";
      
      private const m_const_skillEndFrameLabel:String = "skillEnd^stop^";
      
      private const m_const_createImageFrameLabel:String = "createImage";
      
      private var m_foxSkill4Listeners:Vector.<IFoxSkill4Listener>;
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_skillDirectionX:int;
      
      private var m_pushForce:P3DVector3D;
      
      private var m_isPush:Boolean;
      
      private var m_isPushEntity:Boolean;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_pushForce2:P3DVector3D;
      
      private var m_imageAnimalEntity:AnimalEntity;
      
      public function Skill_FoxSkill4()
      {
         super();
         m_isAttackReachWhenRelease = false;
         m_foxSkill4Listeners = new Vector.<IFoxSkill4Listener>();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
         m_pushForce = new P3DVector3D();
         m_pushForce2 = new P3DVector3D();
         m_pushForce.setTo(5000,0,0);
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = pushEntity;
      }
      
      override public function clear() : void
      {
         ClearUtil.nullArr(m_foxSkill4Listeners,false,false,false);
         m_foxSkill4Listeners = null;
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         ClearUtil.clearObject(m_pushForce);
         m_pushForce = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         ClearUtil.clearObject(m_pushForce2);
         m_pushForce2 = null;
         m_imageAnimalEntity = null;
         super.clear();
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function addFoxSkill4Listener(param1:IFoxSkill4Listener) : void
      {
         ListenerUtil.addListener(m_foxSkill4Listeners,param1);
      }
      
      public function removeFoxSkill4Listener(param1:IFoxSkill4Listener) : void
      {
         ListenerUtil.removeListener(m_foxSkill4Listeners,param1);
      }
      
      public function setImageAnimalEntity(param1:AnimalEntity) : void
      {
         if(m_imageAnimalEntity)
         {
            throw new Error("出错了, imageAnimalEntity 不为null");
         }
         m_imageAnimalEntity = param1;
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      public function setPushData(param1:P3DVector3D) : void
      {
         if(param1)
         {
            m_pushForce.copy(param1);
         }
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_skillDirectionX = m_owner.getShowDirection();
         m_owner.forceSetDirection(m_owner.getShowDirection(),0);
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun && m_isPush)
         {
            renderPushEntity();
         }
      }
      
      override protected function ownerSetDirection(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
         if(m_isRun)
         {
            if(m_owner)
            {
               m_owner.forceSetDirection(m_skillDirectionX,0);
            }
         }
      }
      
      private function renderPushEntity() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      private function pushEntity2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
      
      private function pushEntity(param1:IEntity) : void
      {
         pushEntity2(param1);
         if(m_isPushEntity)
         {
            m_pushForce2.multi2(m_owner.getShowDirection(),m_pushForce);
            m_pushForce2.multi2(param1.getBody().getMass(),m_pushForce2);
            param1.applyForce(m_pushForce2);
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "skillAttackReach":
               attackReach(m_world);
               break;
            case "skillEnd^stop^":
               m_isPush = false;
               endSkill2();
               break;
            case "createImage":
               dispatchCreateImage();
               moveImage();
               m_isPush = true;
         }
      }
      
      override protected function endSkill2() : void
      {
         m_imageAnimalEntity = null;
         super.endSkill2();
      }
      
      private function moveImage() : void
      {
      }
      
      private function dispatchCreateImage() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IFoxSkill4Listener> = m_foxSkill4Listeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].createImage(this);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
   }
}

