package UI.WorldBoss
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   
   public class ValueAnimationManager
   {
      
      private static var m_instance:ValueAnimationManager;
      
      private var m_allAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      public function ValueAnimationManager()
      {
         super();
         if(m_instance == null)
         {
            m_allAnimations = new Vector.<AnimationShowPlayLogicShell>();
            m_instance = this;
            return;
         }
         throw new Error("实例已经存在");
      }
      
      public static function getInstance() : ValueAnimationManager
      {
         if(m_instance == null)
         {
            m_instance = new ValueAnimationManager();
         }
         return m_instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_allAnimations);
         m_allAnimations = null;
         m_instance = null;
      }
      
      public function addAnimation(param1:AnimationShowPlayLogicShell) : void
      {
         m_allAnimations.push(param1);
      }
   }
}

