package UI.SocietySystem.SocietyShop
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.SocietySystem.MySocietyPanel.MySocietyPanel;
   import UI.SocietySystem.SocietySystem;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BuyPopUpBox
   {
      
      private var m_dragLs:AbleDragSpriteLogicShell;
      
      private var m_eqContainer:Sprite;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_nameText:TextField;
      
      private var m_priceText:TextField;
      
      private var m_numBtnGroup:NumberBtnGroupLogicShell;
      
      private var m_descriptionText:TextField;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_equipment:Equipment;
      
      private var m_maxBuyNum:int;
      
      private var m_show:MovieClip;
      
      private var m_goodData:GoodData;
      
      private var m_shopLevel:int;
      
      private var m_oneBuyEqData:OneEqSaveDataInSocietyShop;
      
      private var m_societySystem:SocietySystem;
      
      private var m_mySocietyPanel:MySocietyPanel;
      
      public function BuyPopUpBox()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            ClearUtil.clearDisplayObjectInContainer(m_show);
            if(m_show.parent)
            {
               m_show.parent.removeChild(m_show);
            }
            m_show = null;
         }
         ClearUtil.clearObject(m_dragLs);
         m_dragLs = null;
         ClearUtil.clearObject(m_eqContainer);
         m_eqContainer = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_nameText = null;
         m_priceText = null;
         ClearUtil.clearObject(m_numBtnGroup);
         m_numBtnGroup = null;
         m_descriptionText = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         ClearUtil.clearObject(m_equipment);
         m_equipment = null;
         m_goodData = null;
         m_oneBuyEqData = null;
         m_societySystem = null;
         m_mySocietyPanel = null;
      }
      
      public function setSocietySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setMySocietyPanel(param1:MySocietyPanel) : void
      {
         m_mySocietyPanel = param1;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_mySocietyPanel.addChild(m_show);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         initShow();
      }
      
      public function setData(param1:GoodData, param2:OneEqSaveDataInSocietyShop, param3:int) : void
      {
         ClearUtil.clearObject(m_equipment);
         m_goodData = param1;
         m_equipment = MyFunction2.sheatheEquipmentShell(m_goodData.getEquipmentVO());
         m_equipment.addEventListener("rollOver",onOver,false,0,true);
         m_equipment.addEventListener("rollOut",onOut,false,0,true);
         m_oneBuyEqData = param2;
         m_shopLevel = param3;
         m_maxBuyNum = param2 ? param1.getMaxNum() - param2.getNum() : param1.getMaxNum();
         initShow2();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow() : void
      {
         m_dragLs = new AbleDragSpriteLogicShell();
         m_dragLs.setShow(m_show);
         m_eqContainer = m_show["eqContainer"];
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
         m_nameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_nameText);
         m_nameText.mouseEnabled = false;
         m_priceText = m_show["priceText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_priceText);
         m_priceText.mouseEnabled = false;
         m_numBtnGroup = new NumberBtnGroupLogicShell();
         m_numBtnGroup.setShow(m_show["numBtnGroup"]);
         m_numBtnGroup.isOpenInput = true;
         m_numBtnGroup.num = 1;
         m_descriptionText = m_show["descriptionText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_descriptionText);
         m_descriptionText.mouseEnabled = false;
         m_sureBtn = new ButtonLogicShell2();
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_sureBtn.setTipString("点击购买");
         m_cancelBtn = new ButtonLogicShell2();
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         m_cancelBtn.setTipString("点击取消购买");
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_show == null || m_goodData == null)
         {
            return;
         }
         m_numBtnGroup.maxNum = m_maxBuyNum;
         m_numBtnGroup.minNum = 1;
         m_eqContainer.addChild(m_equipment);
         m_nameText.text = m_equipment.equipmentVO.name;
         m_priceText.text = m_goodData.getConValue().toString();
         m_descriptionText.text = m_equipment.equipmentVO.description;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
      }
      
      public function getSureBtn() : ButtonLogicShell2
      {
         return m_sureBtn;
      }
      
      public function getCancelBtn() : ButtonLogicShell2
      {
         return m_cancelBtn;
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
      
      public function buyGood() : BuyingGoodData
      {
         var _loc1_:int = m_numBtnGroup.num;
         if(_loc1_ > m_goodData.getMaxNum() || Boolean(m_oneBuyEqData) && _loc1_ > m_goodData.getMaxNum() - m_oneBuyEqData.getNum())
         {
            return null;
         }
         if(GamingUI.getInstance().player1.getSocietyDataVO().getPersonalReConValue() < m_goodData.getConValue())
         {
            m_mySocietyPanel.showWarningBox("帮贡不足，无法购买",0);
            return null;
         }
         _loc1_ = MyFunction.getInstance().faseAddOneNumEquipmentVO(GamingUI.getInstance().player1.playerVO.packageEquipmentVOs,m_goodData.getEquipmentVO(),_loc1_,0);
         if(_loc1_ == 0)
         {
            m_mySocietyPanel.showWarningBox("玩家1背包放不下购买的物品",0);
            return null;
         }
         m_societySystem.decConValue(m_goodData.getConValue() * _loc1_);
         return new BuyingGoodData(m_goodData,_loc1_,m_shopLevel);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

