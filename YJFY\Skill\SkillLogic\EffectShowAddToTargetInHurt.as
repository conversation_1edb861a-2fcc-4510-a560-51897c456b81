package YJFY.Skill.SkillLogic
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class EffectShowAddToTargetInHurt
   {
      
      private var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      private var m_effectShowsAddtoTarget:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_wasteEffectShowsAddToTarget:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_effectShowsAddtoTargetPool:ObjectsPool;
      
      private var m_animalEntitys_addEffect:Vector.<IAnimalEntity>;
      
      private var m_animalEntityListener:AnimalEntityListener;
      
      private var m_isHideTargetBodyShow:Boolean;
      
      private var m_world:World;
      
      public function EffectShowAddToTargetInHurt()
      {
         super();
         m_effectShowsAddtoTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteEffectShowsAddToTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_animalEntitys_addEffect = new Vector.<IAnimalEntity>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.changeStateFun = entityChangeState;
         m_effectShowsAddtoTargetPool = new ObjectsPool(m_effectShowsAddtoTarget,m_wasteEffectShowsAddToTarget,createEffectShowAddToTarget,null);
      }
      
      public function clear() : void
      {
         clear2();
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowsAddtoTarget);
         m_effectShowsAddtoTarget = null;
         ClearUtil.clearObject(m_wasteEffectShowsAddToTarget);
         m_wasteEffectShowsAddToTarget = null;
         ClearUtil.clearObject(m_effectShowsAddtoTargetPool);
         m_effectShowsAddtoTargetPool = null;
         ClearUtil.nullArr(m_animalEntitys_addEffect,false,false,false);
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         m_world = null;
      }
      
      public function setEffectAddToTargetDefinitionData(param1:AnimationDefinitionData) : void
      {
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         if(param1)
         {
            m_effectAddtoTargetDefinitionData = param1.clone();
         }
      }
      
      public function setWorld(param1:World) : void
      {
         m_world = param1;
      }
      
      public function setIsHideTargetBodyShow(param1:Boolean) : void
      {
         m_isHideTargetBodyShow = param1;
      }
      
      public function addEffectShowAddToTarget(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:AnimationShowPlayLogicShell = null;
         var _loc2_:int = int(m_animalEntitys_addEffect.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_animalEntitys_addEffect[_loc4_] == param1)
            {
               return;
            }
            _loc4_++;
         }
         if(m_effectAddtoTargetDefinitionData && param1 is IAnimalEntity)
         {
            if((param1 as AnimalEntity).notShowBeattack)
            {
               return;
            }
            if(m_isHideTargetBodyShow && param1 is IAnimalEntity)
            {
               (param1 as IAnimalEntity).hideBodyShow();
            }
            _loc3_ = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc3_.extra = param1;
            (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect.push(param1);
            if((param1 as AnimalEntity).isFly)
            {
               (_loc3_.getShow() as AnimationShow).y = 0 - param1.getBodyZRange();
            }
            if((_loc3_.getShow() as AnimationShow).getID() == "debuff")
            {
               if((param1 as AnimalEntity).isFly)
               {
                  (_loc3_.getShow() as AnimationShow).y = 0 - param1.getShow().height - param1.getBodyZRange();
               }
               else
               {
                  (_loc3_.getShow() as AnimationShow).y = 0 - param1.getShow().height;
               }
            }
            else if((_loc3_.getShow() as AnimationShow).getID() == "jiansu")
            {
               if((param1 as AnimalEntity).isFly)
               {
                  (_loc3_.getShow() as AnimationShow).y = 0;
               }
            }
            _loc3_.gotoAndPlay("start");
            param1.addOtherAniamtion(_loc3_);
         }
      }
      
      private function createEffectShowAddToTarget() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_effectAddtoTargetDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      private function entityChangeState(param1:IAnimalEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1.isInHurt() == false && !param1.getBuffState())
         {
            _loc3_ = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_effectShowsAddtoTarget[_loc4_].extra == param1)
               {
                  param1.removeOtherAnimation(m_effectShowsAddtoTarget[_loc4_]);
                  m_effectShowsAddtoTargetPool.wasteOneObj(m_effectShowsAddtoTarget[_loc4_]);
                  _loc4_--;
                  _loc3_--;
               }
               _loc4_++;
            }
            param1.removeAnimalEntityListener(m_animalEntityListener);
            _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1));
            m_animalEntitys_addEffect.splice(_loc2_,1);
            if(m_isHideTargetBodyShow)
            {
               param1.showBodyShow();
            }
         }
      }
      
      public function removeAllEffect() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            (m_effectShowsAddtoTarget[_loc2_].extra as AnimalEntity).removeOtherAnimation(m_effectShowsAddtoTarget[_loc2_]);
            (m_effectShowsAddtoTarget[_loc2_].extra as AnimalEntity).removeAnimalEntityListener(m_animalEntityListener);
            (m_effectShowsAddtoTarget[_loc2_].extra as AnimalEntity).setMoveSpeed((m_effectShowsAddtoTarget[_loc2_].extra as AnimalEntity).m_walkSpeed);
            if(m_isHideTargetBodyShow)
            {
               (m_effectShowsAddtoTarget[_loc2_].extra as AnimalEntity).showBodyShow();
            }
            _loc2_++;
         }
         m_effectShowsAddtoTargetPool.removeAll();
         m_animalEntitys_addEffect.length = 0;
      }
      
      private function clear2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_effectShowsAddtoTarget[_loc2_].extra)
            {
               (m_effectShowsAddtoTarget[_loc2_].extra as IEntity).removeOtherAnimation(m_effectShowsAddtoTarget[_loc2_]);
            }
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_].getShow());
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_]);
            m_effectShowsAddtoTarget[_loc2_] = null;
            _loc2_++;
         }
         m_effectShowsAddtoTarget.length = 0;
         _loc1_ = m_animalEntitys_addEffect ? m_animalEntitys_addEffect.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_animalEntitys_addEffect[_loc2_].removeAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect[_loc2_] = null;
            _loc2_++;
         }
         m_animalEntitys_addEffect.length = 0;
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
      }
   }
}

