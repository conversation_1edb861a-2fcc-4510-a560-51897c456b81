package UI
{
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.Buff.NotTimeBuff.NotTimeBuffVO;
   import UI.Buff.Buff.OnlyOnLineBuff.OnlyOnLineBuffVO;
   import UI.DetectionClass.DetectionClass;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.DropEquipmentVO;
   import UI.Equipments.EquipmentVO.ContractEquipmntVO;
   import UI.Equipments.EquipmentVO.DanMedicineEquipmentVO;
   import UI.Equipments.EquipmentVO.EggEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.EquipmentVO.GetServerTimeListenerForLimitEquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Equipments.EquipmentVO.PetSkillBookEquipmentVO;
   import UI.Equipments.EquipmentVO.ScrollEquipmentVO;
   import UI.Equipments.EquipmentXMLPartData.EquipmentXMLPartData;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.BuffEquipmentVO;
   import UI.Equipments.StackEquipments.ForceDanEquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.MaterialEquipmentVO;
   import UI.Equipments.StackEquipments.PlantEquipmentVO;
   import UI.Equipments.StackEquipments.PocketEquipmentVO;
   import UI.Equipments.StackEquipments.PotionEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Farm.Land.LandVO;
   import UI.Pets.Talents.TalentVO;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.Players.VipVO;
   import UI.Privilege.PrivilegeVO;
   import UI.ShiTu.PromoteValueObject;
   import UI.ShiTu.XiuLianContent;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetAwakeSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.Task.TaskGoalVO;
   import UI.Task.TaskReward.TaskRewardVO;
   import UI.Task.TaskReward.TaskRewardVO_Equipment;
   import UI.Task.TaskReward.TaskRewardVO_Experience;
   import UI.Task.TaskReward.TaskRewardVO_LSHSHI;
   import UI.Task.TaskReward.TaskRewardVO_Money;
   import UI.Task.TaskReward.TaskRewardVO_ZHHJZH;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.EveryDayTaskVO;
   import UI.Task.TaskVO.LimitingTimeTaskVO;
   import UI.Task.TaskVO.LimtingTimeAccumulatedTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.UIConstant.UIConstantData;
   import UI.UIInterface.ILimitEquipmentVO;
   import UI.newTask.NewMainTask.NewGotoData;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class XMLSingle extends DataManagerParent
   {
      
      private static var _instance:XMLSingle = null;
      
      public var calculationAllAddValueInUpgradeEquipment:Function;
      
      private var _monkeyXML:XML;
      
      private var _dragonXML:XML;
      
      private var _erLangShenXML:XML;
      
      private var _changEXML:XML;
      
      private var _foxXML:XML;
      
      private var _tieShanXML:XML;
      
      private var _houyiXML:XML;
      
      private var _ziXiaXML:XML;
      
      private var _equipmentXML:XML;
      
      private var _skillXML:XML;
      
      private var _talentXML:XML;
      
      private var _dataXML:XML;
      
      private var _mainLineTaskDescription:XML;
      
      private var _broadcast:XML;
      
      private var _privilegeXML:XML;
      
      private var _vipXML:XML;
      
      private var _taskXML:XML;
      
      private var _textXML:XML;
      
      private var _farmXML:XML;
      
      private var _buffXML:XML;
      
      private var _xiaoKongXML:XML;
      
      private var _xiaoBaXML:XML;
      
      private var _xiuLianContentXML:XML;
      
      private var _NewRank:XML;
      
      private var _lingge:XML;
      
      private var _tuDiSkillXML:XML;
      
      private var _tuDiDataXML:XML;
      
      private var _onLineGiftBagXML:XML;
      
      private var _mainLineTaskGoalsXML:XML;
      
      private var _mainLineTaskXML:XML;
      
      private var _everyDayTask:XML;
      
      private var _mainLineTaskListXML:XML;
      
      private var _signXML:XML;
      
      private var _tehui:XML;
      
      private var _weekpay:XML;
      
      private var _buchang:XML;
      
      private var _wuyi:XML;
      
      private var _consumer:XML;
      
      private var _automaticPetsXML:XML;
      
      private var _automaticPetSkillsXML:XML;
      
      private var _automaticPetOtherDataXML:XML;
      
      private var _mountsXML:XML;
      
      private var _mountSkillsXML:XML;
      
      private var _activeTaskListXML:XML;
      
      private var _smallAssistantXML:XML;
      
      private var m_equipmentXMLPartData:EquipmentXMLPartData;
      
      public function XMLSingle()
      {
         if(_instance == null)
         {
            super();
            init();
            _instance = this;
            return;
         }
         throw new Error("fuck you!没看到有实例存在么？！");
      }
      
      public static function getInstance() : XMLSingle
      {
         if(_instance == null)
         {
            _instance = new XMLSingle();
         }
         return _instance;
      }
      
      public static function getVipVO(param1:Number, param2:XML, param3:XML, param4:String) : VipVO
      {
         var _loc8_:XML = null;
         var _loc10_:int = 0;
         var _loc5_:XML = XMLSingle.getInstance().vipXML;
         var _loc9_:XMLList = XMLSingle.getInstance().vipXML.item;
         var _loc7_:int = int(_loc9_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc7_ - 1)
         {
            if(param1 < int(_loc9_[_loc10_ + 1].@pointTicketValue))
            {
               break;
            }
            _loc10_++;
         }
         if(TimeUtil.getTimeUtil().timeIntervalBySecond(String(_instance.dataXML.ZhouNianHuoDong[0].@StartData),param4) > 0 && TimeUtil.getTimeUtil().timeIntervalBySecond(param4,String(_instance.dataXML.ZhouNianHuoDong[0].@EndData)) > 0)
         {
            if(_loc10_ < 6)
            {
               _loc10_ = 6;
            }
         }
         _loc8_ = _loc9_[_loc10_];
         var _loc6_:VipVO = new VipVO();
         _loc6_.totalPointTicketValue = param1;
         _loc6_.vipLevel = int(_loc8_.@vipLevel);
         _loc6_.giftBagVOs = getEquipmentVOs(_loc8_,param2,true);
         _loc6_.privilegeVOs = getPrivilegeVOs(_loc8_.@privileges,param3);
         return _loc6_;
      }
      
      public static function getVipVOByLevel(param1:int) : VipVO
      {
         var _loc3_:XML = XMLSingle.getInstance().vipXML.item.(@vipLevel == param1)[0];
         var _loc2_:VipVO = new VipVO();
         _loc2_.vipLevel = int(_loc3_.@vipLevel);
         _loc2_.giftBagVOs = getEquipmentVOs(_loc3_,XMLSingle.getInstance().equipmentXML,true);
         _loc2_.privilegeVOs = getPrivilegeVOs(_loc3_.@privileges,XMLSingle.getInstance().privilegeXML);
         return _loc2_;
      }
      
      public static function getPointTicketValueByVipLevel(param1:int) : int
      {
         var _loc2_:XML = XMLSingle.getInstance().vipXML.item.(@vipLevel == param1)[0];
         return Number(_loc2_.@pointTicketValue);
      }
      
      public static function getVipMaxLevel() : int
      {
         var _loc1_:XMLList = XMLSingle.getInstance().vipXML.item;
         return int(_loc1_[_loc1_.length() - 1].@vipLevel);
      }
      
      public static function setPlayerVO(param1:PlayerVO, param2:int = 0) : void
      {
         var _loc3_:XML = null;
         switch(param1.playerType)
         {
            case "SunWuKong":
               _loc3_ = XMLSingle.getInstance().monkeyXML.item.(@level == param1.level)[0];
               break;
            case "BaiLongMa":
               _loc3_ = XMLSingle.getInstance().dragonXML.item.(@level == param1.level)[0];
               break;
            case "ErLangShen":
               _loc3_ = XMLSingle.getInstance().erLangShenXML.item.(@level == param1.level)[0];
               break;
            case "ChangE":
               _loc3_ = XMLSingle.getInstance().changEXML.item.(@level == param1.level)[0];
               break;
            case "Fox":
               _loc3_ = XMLSingle.getInstance().foxXML.item.(@level == param1.level)[0];
               break;
            case "TieShan":
               _loc3_ = XMLSingle.getInstance().tieShanXML.item.(@level == param1.level)[0];
               break;
            case "Houyi":
               _loc3_ = XMLSingle.getInstance().houyiXML.item.(@level == param1.level)[0];
               break;
            case "ZiXia":
               _loc3_ = XMLSingle.getInstance().ziXiaXML.item.(@level == param1.level)[0];
               break;
            default:
               throw new Error("不存在该类型玩家角色");
         }
         param1.baseBloodVolume = int(_loc3_.@bloodVolume);
         param1.experienceVolume = int(_loc3_.@experienceVolume);
         param1.baseAttack = int(_loc3_.@attack);
         param1.baseDefence = int(_loc3_.@defence);
         param1.baseCriticalRate = int(_loc3_.@criticalRate);
         param1.baseMaxMagic = int(_loc3_.@maxMagic);
         param1.baseRegMp = Number(_loc3_.@regMp);
         param1.baseRegHp = Number(_loc3_.@regHp);
         param1.baseHit = Number(_loc3_.@hit);
         param1.baseDodge = Number(_loc3_.@dodge);
         param1.baseRiotValue = Number(_loc3_.@riotValue);
         param1.baseOffensiveValue = int(_loc3_.@offensiveValue);
      }
      
      public static function getBaseEquipment(param1:int, param2:XML, param3:String = null) : EquipmentVO
      {
         var _loc5_:int = 0;
         var _loc9_:int = 0;
         var _loc14_:int = 0;
         var _loc15_:int = 0;
         var _loc7_:EquipmentVO = null;
         var _loc13_:int = 0;
         var _loc17_:int = 0;
         var _loc8_:* = undefined;
         var _loc12_:* = undefined;
         var _loc16_:String = null;
         var _loc4_:int = 0;
         var _loc6_:XML = null;
         var _loc18_:InsetGemEquipmentVO = null;
         XMLSingle.getInstance().checkEquipmentId(param1);
         var _loc11_:XML = param2.item.(@id == param1)[0];
         var _loc10_:String = String(_loc11_.@equipmentType);
         _loc7_ = new EquipmentVO();
         switch(_loc10_)
         {
            case "pet":
               _loc6_ = XMLSingle.getInstance().skillXML;
               _loc16_ = String(_loc11_.@petType);
               if(_loc16_ == "advancePet")
               {
                  _loc7_ = new AdvancePetEquipmentVO();
               }
               else
               {
                  _loc7_ = new PetEquipmentVO();
               }
               break;
            case "material":
            case "potion":
            case "pocket":
            case "grass":
            case "buffEquipment":
            case "forceDan":
            case "insetGem":
               switch(_loc10_)
               {
                  case "material":
                     _loc7_ = new MaterialEquipmentVO();
                     _loc14_ = int(_loc11_.@num);
                     _loc4_ = int(_loc11_.@overdue);
                     if(_loc14_)
                     {
                        (_loc7_ as MaterialEquipmentVO).num = _loc14_;
                     }
                     else
                     {
                        (_loc7_ as MaterialEquipmentVO).num = 1;
                     }
                     if(_loc4_)
                     {
                        _loc7_.isOverdue = _loc4_;
                     }
                     break;
                  case "potion":
                     _loc7_ = new PotionEquipmentVO();
                     (_loc7_ as PotionEquipmentVO).value = String(_loc11_.@value);
                     break;
                  case "buffEquipment":
                     _loc7_ = new BuffEquipmentVO();
                     (_loc7_ as BuffEquipmentVO).value = String(_loc11_.@value);
                     break;
                  case "forceDan":
                     _loc7_ = new ForceDanEquipmentVO();
                     (_loc7_ as ForceDanEquipmentVO).value = String(_loc11_.@value);
                     break;
                  case "pocket":
                     _loc7_ = new PocketEquipmentVO();
                     (_loc7_ as PocketEquipmentVO).value = String(_loc11_.@value);
                     break;
                  case "insetGem":
                     _loc7_ = new InsetGemEquipmentVO();
                     _loc18_ = _loc7_ as InsetGemEquipmentVO;
                     _loc18_.showColor = uint(_loc11_.@showColor);
                     _loc18_.canInsetPos = _loc11_.@canInsetPos;
                     break;
                  case "grass":
                     _loc7_ = new PlantEquipmentVO();
                     break;
                  default:
                     throw new Error();
               }
               break;
            case "scroll":
               _loc7_ = new ScrollEquipmentVO();
               break;
            case "precious":
            case "weapon":
            case "necklace":
            case "gourd":
            case "clothes":
            case "forever_fashion":
               switch(_loc10_)
               {
                  case "precious":
                     _loc7_ = new PreciousEquipmentVO();
                     break;
                  case "weapon":
                     _loc7_ = new WeaponEquipmentVO();
                     break;
                  case "necklace":
                     _loc7_ = new NecklaceEquipmentVO();
                     break;
                  case "gourd":
                     _loc7_ = new GourdEquipmentVO();
                     break;
                  case "clothes":
                     _loc7_ = new ClothesEquipmentVO();
                     break;
                  case "forever_fashion":
                     _loc7_ = new ForeverFashionEquipmentVO();
                     break;
                  default:
                     throw new Error("存在不是ableEquipmentVO类型的装备初始化ableEquipmentVO");
               }
               break;
            case "egg":
               _loc7_ = new EggEquipmentVO();
               (_loc7_ as EggEquipmentVO).targetPetIdsStr = String(_loc11_.@targetPet);
               break;
            case "contract":
               _loc7_ = new ContractEquipmntVO();
               (_loc7_ as ContractEquipmntVO).targetAutomaticPetId = String(_loc11_.@targetAutomaticPetId);
               break;
            case "fashion":
               _loc7_ = new FashionEquipmentVO();
               (_loc7_ as FashionEquipmentVO).TimeLimit = Number(_loc11_.@TimeLimit);
               break;
            case "danMedicine":
               _loc7_ = new DanMedicineEquipmentVO();
               (_loc7_ as DanMedicineEquipmentVO).maxValue = int(_loc11_.@maxValue);
               (_loc7_ as DanMedicineEquipmentVO).reduceValue = int(_loc11_.@reduceValue);
               (_loc7_ as DanMedicineEquipmentVO).danMedicineType = String(_loc11_.@danMedicineType);
               (_loc7_ as DanMedicineEquipmentVO).enablePutLine = int(_loc11_.@enablePutLine);
               break;
            case "petSkillBook":
               _loc7_ = new PetSkillBookEquipmentVO();
               (_loc7_ as PetSkillBookEquipmentVO).value = String(_loc11_.@value);
               break;
            case "medal":
               _loc7_ = new MedalEquipmentVO();
               break;
            case "dropEq":
               _loc7_ = new DropEquipmentVO();
               break;
            case "undeterminded":
               _loc7_ = new EquipmentVO();
               break;
            default:
               throw new Error("初始化未知类型装备。");
         }
         _loc7_.id = param1;
         _loc7_.className = String(_loc11_.@className);
         _loc7_.name = String(_loc11_.@name);
         _loc7_.level = int(_loc11_.@level);
         _loc7_.description = String(_loc11_.@description);
         _loc7_.ticketPrice = Number(_loc11_.@ticketPrice);
         _loc7_.ticketId = String(_loc11_.@ticketId);
         _loc7_.owner = String(_loc11_.@owner);
         _loc7_.equipmentType = _loc10_;
         _loc7_.isAbleSell = Boolean(int(_loc11_.@isAbleSell));
         _loc7_.isAbleAKeySell = Boolean(int(_loc11_.@isAbleAKeySell));
         if(!<EMAIL>())
         {
            _loc7_.messageBoxColor = 16777215;
         }
         else
         {
            _loc7_.messageBoxColor = int(_loc11_.@messageBoxColor);
         }
         return _loc7_;
      }
      
      public static function getEquipment(param1:int, param2:XML, param3:String = null) : EquipmentVO
      {
         var _loc40_:int = 0;
         var _loc42_:int = 0;
         var _loc53_:int = 0;
         var _loc8_:int = 0;
         var _loc19_:EquipmentVO = null;
         var _loc51_:int = 0;
         var _loc37_:int = 0;
         var _loc41_:* = undefined;
         var _loc45_:* = undefined;
         var _loc13_:String = null;
         var _loc4_:int = 0;
         var _loc6_:XML = null;
         var _loc9_:XMLList = null;
         var _loc44_:int = 0;
         var _loc35_:int = 0;
         var _loc39_:InsetGemEquipmentVO = null;
         var _loc15_:XMLList = null;
         var _loc38_:* = null;
         var _loc55_:XMLList = null;
         var _loc52_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc43_:int = 0;
         var _loc46_:int = 0;
         var _loc31_:XMLList = null;
         var _loc5_:* = 0;
         var _loc14_:int = 0;
         var _loc23_:Number = NaN;
         var _loc29_:Number = NaN;
         var _loc36_:Number = NaN;
         var _loc12_:Boolean = false;
         var _loc47_:int = 0;
         var _loc21_:int = 0;
         var _loc30_:* = undefined;
         var _loc18_:XMLList = null;
         var _loc27_:int = 0;
         var _loc16_:* = 0;
         var _loc26_:* = undefined;
         var _loc20_:int = 0;
         var _loc50_:int = 0;
         var _loc7_:Boolean = false;
         var _loc34_:Number = NaN;
         var _loc17_:Number = NaN;
         var _loc32_:Number = NaN;
         var _loc49_:int = 0;
         var _loc25_:Number = NaN;
         var _loc28_:Number = NaN;
         var _loc54_:XMLList = null;
         var _loc48_:XMLList = null;
         var _loc33_:XMLList = null;
         XMLSingle.getInstance().checkEquipmentId(param1);
         var _loc22_:String;
         var _loc24_:XML;
         switch(_loc22_ = String((_loc24_ = param2.item.(@id == param1)[0]).@equipmentType))
         {
            case "pet":
               _loc6_ = XMLSingle.getInstance().skillXML;
               _loc13_ = String(_loc24_.@petType);
               if(_loc13_ == "advancePet")
               {
                  _loc19_ = new AdvancePetEquipmentVO();
                  (_loc19_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs = new Vector.<SkillVO>();
                  _loc9_ = _loc24_.awakeSkill;
                  _loc44_ = 0;
                  _loc35_ = int(_loc9_ ? _loc9_.length() : 0);
                  _loc44_ = 0;
                  while(_loc44_ < _loc35_)
                  {
                     (_loc19_ as AdvancePetEquipmentVO).petAwakePassiveSkillVOs.push(getSkill(_loc9_[_loc44_].@id,_loc6_));
                     _loc44_++;
                  }
               }
               else
               {
                  _loc19_ = new PetEquipmentVO();
               }
               if(_loc19_.id == 10400120 || _loc19_.id == 10400220 || _loc19_.id == 10400320 || _loc19_.id == 10400421)
               {
                  (_loc19_ as PetEquipmentVO).addHitPet = Number(_loc24_.@addHitPet);
               }
               (_loc19_ as PetEquipmentVO).upgradeLevelNum = int(_loc24_.@upgradeLevelNum);
               (_loc19_ as PetEquipmentVO).petLevel = 1;
               (_loc19_ as PetEquipmentVO).petType = _loc13_;
               (_loc19_ as PetEquipmentVO).maxLevel = int(_loc24_.@maxLevel);
               (_loc19_ as PetEquipmentVO).activeSkillVO = getSkill(int(_loc24_.@activeSkill),_loc6_);
               (_loc19_ as PetEquipmentVO).petSeries = String(_loc24_.@petSeries);
               (_loc19_ as PetEquipmentVO).initPassiveSkillNum = int(_loc24_.@initPassiveSkillNum);
               (_loc19_ as PetEquipmentVO).promoteXMLId = int(_loc24_.@promoteXMLId);
               (_loc19_ as PetEquipmentVO).essentialPercent = 1;
               break;
            case "material":
            case "potion":
            case "pocket":
            case "grass":
            case "buffEquipment":
            case "forceDan":
            case "insetGem":
               switch(_loc22_)
               {
                  case "material":
                     _loc19_ = new MaterialEquipmentVO();
                     _loc53_ = int(_loc24_.@num);
                     _loc4_ = int(_loc24_.@overdue);
                     if(_loc53_)
                     {
                        (_loc19_ as MaterialEquipmentVO).num = _loc53_;
                     }
                     else
                     {
                        (_loc19_ as MaterialEquipmentVO).num = 1;
                     }
                     if(_loc4_)
                     {
                        _loc19_.isOverdue = _loc4_;
                     }
                     break;
                  case "potion":
                     _loc19_ = new PotionEquipmentVO();
                     (_loc19_ as PotionEquipmentVO).value = String(_loc24_.@value);
                     break;
                  case "buffEquipment":
                     _loc19_ = new BuffEquipmentVO();
                     (_loc19_ as BuffEquipmentVO).value = String(_loc24_.@value);
                     break;
                  case "forceDan":
                     _loc19_ = new ForceDanEquipmentVO();
                     (_loc19_ as ForceDanEquipmentVO).value = String(_loc24_.@value);
                     break;
                  case "pocket":
                     _loc19_ = new PocketEquipmentVO();
                     (_loc19_ as PocketEquipmentVO).value = String(_loc24_.@value);
                     break;
                  case "insetGem":
                     _loc19_ = new InsetGemEquipmentVO();
                     _loc39_ = _loc19_ as InsetGemEquipmentVO;
                     _loc39_.showColor = uint(_loc24_.@showColor);
                     _loc39_.canInsetPos = _loc24_.@canInsetPos;
                     _loc15_ = _loc24_.promote;
                     _loc37_ = int(_loc15_ ? _loc15_.length() : 0);
                     _loc39_.attributes = new Vector.<String>();
                     _loc39_.attributeValues = new Vector.<PromoteValueObject>();
                     _loc39_.descriptions = new Vector.<String>();
                     _loc51_ = 0;
                     while(_loc51_ < _loc37_)
                     {
                        _loc39_.attributes.push(String(_loc15_[_loc51_].@attribute));
                        _loc39_.attributeValues.push(XMLSingle.getInstance().initPromoteValueObject(_loc15_[_loc51_]));
                        _loc39_.descriptions.push(String(_loc15_[_loc51_].@description));
                        _loc51_++;
                     }
                     break;
                  case "grass":
                     _loc19_ = new PlantEquipmentVO();
                     break;
                  default:
                     throw new Error();
               }
               _loc8_ = int(_loc24_.@maxSuperposition);
               if(_loc8_)
               {
                  (_loc19_ as StackEquipmentVO).maxSuperposition = _loc8_;
               }
               else
               {
                  (_loc19_ as StackEquipmentVO).maxSuperposition = 100;
               }
               break;
            case "scroll":
               _loc19_ = new ScrollEquipmentVO();
               (_loc19_ as ScrollEquipmentVO).successRate = Number(_loc24_.@successRate);
               (_loc19_ as ScrollEquipmentVO).requiredMoney = int(_loc24_.@requiredMoney);
               (_loc19_ as ScrollEquipmentVO).compositeEquipmentVO = getEquipmentVOByID(int(_loc24_.@compositeEquipment),param2,param3);
               (_loc19_ as ScrollEquipmentVO).requiredNums = setScrollRequiredNum(_loc24_);
               (_loc19_ as ScrollEquipmentVO).materialXml = _loc24_;
               break;
            case "precious":
            case "weapon":
            case "necklace":
            case "gourd":
            case "clothes":
            case "forever_fashion":
               switch(_loc22_)
               {
                  case "precious":
                     _loc19_ = new PreciousEquipmentVO();
                     (_loc19_ as PreciousEquipmentVO).materialid = int(_loc24_.@material);
                     _loc46_ = 0;
                     if(_loc24_.hasOwnProperty("addWeight"))
                     {
                        _loc55_ = _loc24_.addWeight;
                        _loc52_ = int(_loc55_[0].@totalweight);
                        _loc43_ = 1 + Math.random() * (_loc52_ - 1 + 1);
                        _loc51_ = 0;
                        while(_loc51_ < _loc55_.length())
                        {
                           if(_loc43_ >= int(_loc55_[_loc51_].@weightmin) && _loc43_ <= int(_loc55_[_loc51_].@weightmax))
                           {
                              _loc46_ = int(_loc55_[_loc51_].@value);
                              break;
                           }
                           _loc51_++;
                        }
                     }
                     if(_loc24_.hasOwnProperty("addAttr"))
                     {
                        _loc31_ = _loc24_.addAttr;
                        _loc5_ = _loc46_;
                        _loc12_ = false;
                        _loc47_ = int(_loc31_.length());
                        _loc21_ = 0;
                        _loc30_ = new Vector.<int>();
                        while(_loc30_.length < _loc5_)
                        {
                           if(_loc21_ >= _loc47_)
                           {
                              _loc21_ = 0;
                           }
                           _loc14_ = 1 + Math.random() * (Number(_loc31_[_loc21_].@totalweight) - 1 + 1);
                           if(_loc14_ <= Number(_loc31_[_loc21_].@weight))
                           {
                              _loc12_ = false;
                              _loc51_ = 0;
                              while(_loc51_ < _loc30_.length)
                              {
                                 if(_loc21_ == _loc30_[_loc51_])
                                 {
                                    _loc12_ = true;
                                 }
                                 _loc51_++;
                              }
                              if(_loc12_ == false)
                              {
                                 _loc30_.push(_loc21_);
                              }
                           }
                           _loc21_++;
                        }
                        _loc51_ = 0;
                        while(_loc51_ < _loc5_)
                        {
                           (_loc19_ as PreciousEquipmentVO).basisAttr.push(String(_loc31_[_loc30_[_loc51_]].@addAttName));
                           (_loc19_ as PreciousEquipmentVO).basisAttrValue.push(Number(_loc31_[_loc30_[_loc51_]].@addAttValue));
                           (_loc19_ as PreciousEquipmentVO).basisMinUp.push(Number(_loc31_[_loc30_[_loc51_]].@minupgrade));
                           (_loc19_ as PreciousEquipmentVO).basisMaxUp.push(Number(_loc31_[_loc30_[_loc51_]].@maxupgrade));
                           (_loc19_ as PreciousEquipmentVO).basisWeight.push(Number(_loc31_[_loc30_[_loc51_]].@weight));
                           (_loc19_ as PreciousEquipmentVO).basisTotalWeight.push(Number(_loc31_[_loc30_[_loc51_]].@totalweight));
                           _loc29_ = Number(_loc31_[_loc30_[_loc51_]].@minupgrade) * 100;
                           _loc36_ = Number(_loc31_[_loc30_[_loc51_]].@maxupgrade) * 100;
                           _loc23_ = _loc29_ + Math.random() * (_loc36_ - _loc29_ + 1);
                           _loc23_ = Math.min(_loc36_,_loc23_);
                           (_loc19_ as PreciousEquipmentVO).basisUpValue.push(_loc23_ / 100);
                           _loc51_++;
                        }
                        ClearUtil.clearObject(_loc30_);
                        _loc30_ = null;
                     }
                     _loc46_ = 0;
                     if(_loc24_.hasOwnProperty("saddWeight"))
                     {
                        _loc55_ = _loc24_.saddWeight;
                        _loc52_ = int(_loc55_[0].@totalweight);
                        _loc43_ = 1 + Math.random() * (_loc52_ - 1 + 1);
                        _loc51_ = 0;
                        while(_loc51_ < _loc55_.length())
                        {
                           if(_loc43_ >= int(_loc55_[_loc51_].@weightmin) && _loc43_ <= int(_loc55_[_loc51_].@weightmax))
                           {
                              _loc46_ = int(_loc55_[_loc51_].@value);
                              break;
                           }
                           _loc51_++;
                        }
                     }
                     if(_loc24_.hasOwnProperty("sAddAttr"))
                     {
                        _loc18_ = _loc24_.sAddAttr;
                        _loc27_ = int(_loc18_.length());
                        _loc16_ = _loc46_;
                        _loc26_ = new Vector.<int>();
                        while(_loc26_.length < _loc16_)
                        {
                           if(_loc20_ >= _loc27_)
                           {
                              _loc20_ = 0;
                           }
                           _loc50_ = 1 + Math.random() * (Number(_loc18_[_loc20_].@totalweight) - 1 + 1);
                           if(_loc50_ <= Number(_loc18_[_loc20_].@weight))
                           {
                              _loc7_ = false;
                              _loc49_ = 0;
                              while(_loc49_ < _loc26_.length)
                              {
                                 if(_loc26_[_loc49_] == _loc20_)
                                 {
                                    _loc7_ = true;
                                 }
                                 _loc49_++;
                              }
                              if(_loc7_ == false)
                              {
                                 _loc26_.push(_loc20_);
                              }
                           }
                           _loc20_++;
                        }
                        _loc51_ = 0;
                        while(_loc51_ < _loc16_)
                        {
                           (_loc19_ as PreciousEquipmentVO).sAttrName.push(String(_loc18_[_loc26_[_loc51_]].@addAttName));
                           (_loc19_ as PreciousEquipmentVO).sAvgValue.push(int(_loc18_[_loc26_[_loc51_]].@avgValue));
                           (_loc19_ as PreciousEquipmentVO).sMaxValue.push(Number(_loc18_[_loc26_[_loc51_]].@maxvalue));
                           (_loc19_ as PreciousEquipmentVO).sMinValue.push(Number(_loc18_[_loc26_[_loc51_]].@minvalue));
                           (_loc19_ as PreciousEquipmentVO).sWeight.push(Number(_loc18_[_loc26_[_loc51_]].@weight));
                           (_loc19_ as PreciousEquipmentVO).sTotalWeight.push(Number(_loc18_[_loc26_[_loc51_]].@totalweight));
                           _loc17_ = Number(_loc18_[_loc26_[_loc51_]].@minvalue) * 100;
                           _loc34_ = Number(_loc18_[_loc26_[_loc51_]].@maxvalue) * 100;
                           _loc32_ = _loc17_ + Math.random() * (_loc34_ - _loc17_ + 1);
                           _loc32_ = Math.min(_loc34_,_loc32_);
                           (_loc19_ as PreciousEquipmentVO).sAttrValue.push(_loc32_ / 100);
                           _loc51_++;
                        }
                        ClearUtil.clearObject(_loc26_);
                        _loc26_ = null;
                        (_loc19_ as PreciousEquipmentVO).materialid = int(_loc24_.@material);
                     }
                     break;
                  case "weapon":
                     _loc19_ = new WeaponEquipmentVO();
                     _loc40_ = int(_loc24_.@minAttack);
                     _loc42_ = int(_loc24_.@maxAttack);
                     (_loc19_ as WeaponEquipmentVO).minAttack = _loc40_;
                     (_loc19_ as WeaponEquipmentVO).maxAttack = _loc42_;
                     (_loc19_ as WeaponEquipmentVO).attack = _loc40_ + Math.random() * (_loc42_ - _loc40_ + 1);
                     break;
                  case "necklace":
                     _loc19_ = new NecklaceEquipmentVO();
                     _loc40_ = int(_loc24_.@minCriticalRate);
                     _loc42_ = int(_loc24_.@maxCriticalRate);
                     (_loc19_ as NecklaceEquipmentVO).maxCriticalRate = _loc42_;
                     (_loc19_ as NecklaceEquipmentVO).minCriticalRate = _loc40_;
                     (_loc19_ as NecklaceEquipmentVO).criticalRate = _loc40_ + Math.random() * (_loc42_ - _loc40_ + 1);
                     break;
                  case "gourd":
                     _loc19_ = new GourdEquipmentVO();
                     _loc40_ = int(_loc24_.@minMaxMagic);
                     _loc42_ = int(_loc24_.@maxMaxMagic);
                     (_loc19_ as GourdEquipmentVO).maxMaxMagic = _loc42_;
                     (_loc19_ as GourdEquipmentVO).minMaxMagic = _loc40_;
                     (_loc19_ as GourdEquipmentVO).maxMagic = _loc40_ + Math.random() * (_loc42_ - _loc40_ + 1);
                     break;
                  case "clothes":
                     _loc19_ = new ClothesEquipmentVO();
                     _loc40_ = int(_loc24_.@minDefence);
                     _loc42_ = int(_loc24_.@maxDefence);
                     (_loc19_ as ClothesEquipmentVO).maxDefence = _loc42_;
                     (_loc19_ as ClothesEquipmentVO).minDefence = _loc40_;
                     (_loc19_ as ClothesEquipmentVO).defence = _loc40_ + Math.random() * (_loc42_ - _loc40_ + 1);
                     _loc25_ = Number(_loc24_.@minRiot);
                     _loc28_ = Number(_loc24_.@maxRiot);
                     (_loc19_ as ClothesEquipmentVO).maxRiot = _loc28_;
                     (_loc19_ as ClothesEquipmentVO).minRiot = _loc25_;
                     (_loc19_ as ClothesEquipmentVO).riot = _loc28_ ? _loc25_ + Math.round(Math.random() * (_loc28_ - _loc25_) * 100) / 100 : 0;
                     break;
                  case "forever_fashion":
                     _loc19_ = new ForeverFashionEquipmentVO();
                     _loc54_ = _loc24_.value;
                     _loc37_ = int(_loc54_.length());
                     _loc41_ = new Vector.<String>();
                     _loc45_ = new Vector.<Number>();
                     _loc51_ = 0;
                     while(_loc51_ < _loc37_)
                     {
                        _loc41_.push(String(_loc54_[_loc51_].@addPlayerAttribute));
                        _loc45_.push(Number(_loc54_[_loc51_].@addPlayerAttributeValue));
                        _loc51_++;
                     }
                     (_loc19_ as ForeverFashionEquipmentVO).addPlayerAttributes = _loc41_;
                     (_loc19_ as ForeverFashionEquipmentVO).addPlayerAttributeValues = _loc45_;
                     break;
                  default:
                     throw new Error("存在不是ableEquipmentVO类型的装备初始化ableEquipmentVO");
               }
               _loc40_ = int(_loc24_.@minRenPin);
               _loc42_ = int(_loc24_.@maxRenPin);
               (_loc19_ as AbleEquipmentVO).minRenPin = _loc40_;
               (_loc19_ as AbleEquipmentVO).maxRenPin = _loc42_;
               (_loc19_ as AbleEquipmentVO).rengPin = _loc40_ + Math.random() * (_loc42_ - _loc40_ + 1);
               (_loc19_ as AbleEquipmentVO).maxLevel = int(_loc24_.@maxLevel);
               (_loc19_ as AbleEquipmentVO).upgradePrice = int(_loc24_.@upgradePrice);
               (_loc19_ as AbleEquipmentVO).upgradeGemNum = int(_loc24_.@upgradeGemNum);
               (_loc19_ as AbleEquipmentVO).upgradeValue = int(_loc24_.@upgradeValue);
               (_loc19_ as AbleEquipmentVO).upgradeValue2 = Number(_loc24_.@upgradeValue2);
               (_loc19_ as AbleEquipmentVO).upgradeSuccessRate = Number(_loc24_.@upgradeSuccessRate);
               (_loc19_ as AbleEquipmentVO).implictProPlayerId = String(_loc24_.@implictProPlayerId);
               (_loc19_ as AbleEquipmentVO).suitName = String(_loc24_.@suitName);
               (_loc19_ as AbleEquipmentVO).maxHoleNum = int(_loc24_.@maxHoleNum);
               break;
            case "egg":
               _loc19_ = new EggEquipmentVO();
               (_loc19_ as EggEquipmentVO).targetPetIdsStr = String(_loc24_.@targetPet);
               break;
            case "contract":
               _loc19_ = new ContractEquipmntVO();
               (_loc19_ as ContractEquipmntVO).targetAutomaticPetId = String(_loc24_.@targetAutomaticPetId);
               break;
            case "fashion":
               _loc19_ = new FashionEquipmentVO();
               (_loc19_ as FashionEquipmentVO).TimeLimit = Number(_loc24_.@TimeLimit);
               _loc48_ = _loc24_.value;
               _loc37_ = int(_loc48_.length());
               _loc41_ = new Vector.<String>();
               _loc45_ = new Vector.<Number>();
               _loc51_ = 0;
               while(_loc51_ < _loc37_)
               {
                  _loc41_.push(String(_loc48_[_loc51_].@addPlayerAttribute));
                  _loc45_.push(Number(_loc48_[_loc51_].@addPlayerAttributeValue));
                  _loc51_++;
               }
               (_loc19_ as FashionEquipmentVO).addPlayerAttributes = _loc41_;
               (_loc19_ as FashionEquipmentVO).addPlayerAttributeValues = _loc45_;
               break;
            case "danMedicine":
               _loc19_ = new DanMedicineEquipmentVO();
               (_loc19_ as DanMedicineEquipmentVO).maxValue = int(_loc24_.@maxValue);
               (_loc19_ as DanMedicineEquipmentVO).reduceValue = int(_loc24_.@reduceValue);
               (_loc19_ as DanMedicineEquipmentVO).danMedicineType = String(_loc24_.@danMedicineType);
               (_loc19_ as DanMedicineEquipmentVO).enablePutLine = int(_loc24_.@enablePutLine);
               break;
            case "petSkillBook":
               _loc19_ = new PetSkillBookEquipmentVO();
               (_loc19_ as PetSkillBookEquipmentVO).value = String(_loc24_.@value);
               break;
            case "medal":
               _loc19_ = new MedalEquipmentVO();
               _loc33_ = _loc24_.value;
               _loc37_ = int(_loc33_.length());
               _loc41_ = new Vector.<String>();
               _loc45_ = new Vector.<Number>();
               if(String(_loc24_.@timeLimit) == "week")
               {
                  (_loc19_ as MedalEquipmentVO).TimeLimit = 168;
               }
               else
               {
                  (_loc19_ as MedalEquipmentVO).TimeLimit = Number(_loc24_.@timeLimit);
               }
               (_loc19_ as MedalEquipmentVO).medalType = String(_loc24_.@medalType);
               _loc51_ = 0;
               while(_loc51_ < _loc37_)
               {
                  _loc41_.push(String(_loc33_[_loc51_].@addPlayerAttribute));
                  _loc45_.push(Number(_loc33_[_loc51_].@addPlayerAttributeValue));
                  _loc51_++;
               }
               (_loc19_ as MedalEquipmentVO).addPlayerAttributes = _loc41_;
               (_loc19_ as MedalEquipmentVO).addPlayerAttributeValues = _loc45_;
               break;
            case "dropEq":
               _loc19_ = new DropEquipmentVO();
               break;
            case "undeterminded":
               _loc19_ = new EquipmentVO();
               break;
            default:
               throw new Error("初始化未知类型装备。");
         }
         _loc19_.id = param1;
         _loc19_.className = String(_loc24_.@className);
         _loc19_.name = String(_loc24_.@name);
         _loc19_.level = int(_loc24_.@level);
         _loc19_.description = String(_loc24_.@description);
         _loc19_.ticketPrice = Number(_loc24_.@ticketPrice);
         _loc19_.ticketId = String(_loc24_.@ticketId);
         _loc19_.owner = String(_loc24_.@owner);
         _loc19_.equipmentType = _loc22_;
         _loc19_.isAbleSell = Boolean(int(_loc24_.@isAbleSell));
         _loc19_.isAbleAKeySell = Boolean(int(_loc24_.@isAbleAKeySell));
         DetectionClass.getInstance().addEquipmentVOFix(_loc19_);
         if(!<EMAIL>())
         {
            _loc19_.messageBoxColor = 16777215;
         }
         else
         {
            _loc19_.messageBoxColor = int(_loc24_.@messageBoxColor);
         }
         return _loc19_;
      }
      
      public static function getEquipmentVOByClassName(param1:String, param2:XML, param3:String = null, param4:Boolean = false) : EquipmentVO
      {
         var _loc5_:int = int(XMLSingle.getIDByClassName(MyFunction.getInstance().unweaveClassNameString(param1),param2));
         return getEquipmentVOByID(_loc5_,param2,param3,param4);
      }
      
      public static function getEquipmentVOByID(param1:int, param2:XML, param3:String = null, param4:Boolean = false) : EquipmentVO
      {
         var _loc11_:PetActiveSkillVO = null;
         var _loc7_:ILimitEquipmentVO = null;
         var _loc9_:Number = NaN;
         var _loc10_:GetServerTimeListenerForLimitEquipmentVO = null;
         var _loc8_:XML = null;
         var _loc5_:Number = NaN;
         var _loc6_:EquipmentVO = XMLSingle.getEquipment(param1,param2);
         _loc6_.isBinding = param4;
         switch(_loc6_.equipmentType)
         {
            case "pet":
               (_loc6_ as PetEquipmentVO).petLevel = 1;
               _loc11_ = (_loc6_ as PetEquipmentVO).activeSkillVO as PetActiveSkillVO;
               _loc11_.originalHurt = (_loc6_ as PetEquipmentVO).petLevel * _loc11_.hurCoefficient + _loc11_.additionHurt;
               _loc11_.originalPkHurt = (_loc6_ as PetEquipmentVO).petLevel * _loc11_.pkHurtCoefficient + _loc11_.additionPkHurt;
               XMLSingle.getInstance().setPetData(param1,(_loc6_ as PetEquipmentVO).petLevel,_loc6_ as PetEquipmentVO);
               (_loc6_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(0);
               if(PetEquipmentVO(_loc6_).initPassiveSkillNum)
               {
                  (_loc6_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomFixedNumPassiveSkillVO(_loc6_ as PetEquipmentVO,(_loc6_ as PetEquipmentVO).initPassiveSkillNum);
               }
               else
               {
                  (_loc6_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomOnePassiveSkillVOInCreatePet(_loc6_ as PetEquipmentVO);
               }
               (_loc6_ as PetEquipmentVO).essentialPercent = 1;
               MyFunction.getInstance().refreshPet(_loc6_ as PetEquipmentVO);
               (_loc6_ as PetEquipmentVO).experiencePercent = 0;
               break;
            case "medal":
            case "fashion":
               if(!Boolean(param3))
               {
                  _loc7_ = _loc6_ as ILimitEquipmentVO;
                  if(_loc7_.TimeLimit == -1)
                  {
                     _loc9_ = **********;
                  }
                  else
                  {
                     _loc9_ = _loc7_.TimeLimit;
                  }
                  if(_loc9_ <= 0)
                  {
                     throw new Error();
                  }
                  _loc7_.remainingTime = Math.ceil(_loc9_ / 24);
                  _loc10_ = new GetServerTimeListenerForLimitEquipmentVO();
                  _loc10_.addLimitEquipmentVO(_loc7_);
                  _loc7_.setGetServerTimeListener(_loc10_);
                  GamingUI.getInstance().getServerTime.addGetServerTimeListener(_loc10_);
                  GamingUI.getInstance().getServerTime.getServerTime(true);
               }
               else
               {
                  _loc8_ = param2.item.(@id == param1)[0];
                  if(param3)
                  {
                     if(String(_loc8_.@timeLimit) == "week")
                     {
                        (_loc6_ as ILimitEquipmentVO).initTime = new TimeUtil().getFirstWeekDay(param3);
                     }
                     else
                     {
                        (_loc6_ as ILimitEquipmentVO).initTime = param3;
                     }
                  }
                  else
                  {
                     (_loc6_ as ILimitEquipmentVO).initTime = param3;
                  }
                  _loc5_ = (_loc6_ as ILimitEquipmentVO).TimeLimit - new TimeUtil().timeInterval((_loc6_ as ILimitEquipmentVO).initTime,param3);
                  (_loc6_ as ILimitEquipmentVO).remainingTime = Math.ceil(_loc5_ / 24);
               }
               break;
            case "buffEquipment":
            case "forceDan":
            case "grass":
            case "potion":
            case "pocket":
            case "material":
            case "insetGem":
               (_loc6_ as StackEquipmentVO).num = 1;
         }
         return _loc6_;
      }
      
      public static function getBuyEquipmentVO(param1:EquipmentVO) : EquipmentVO
      {
         var _loc4_:WeaponEquipmentVO = null;
         var _loc6_:NecklaceEquipmentVO = null;
         var _loc9_:GourdEquipmentVO = null;
         var _loc2_:ClothesEquipmentVO = null;
         var _loc5_:ILimitEquipmentVO = null;
         var _loc8_:Number = NaN;
         var _loc10_:GetServerTimeListenerForLimitEquipmentVO = null;
         var _loc3_:EquipmentVO = null;
         var _loc7_:EquipmentVO;
         switch((_loc7_ = param1.clone()).equipmentType)
         {
            case "weapon":
               _loc4_ = _loc7_ as WeaponEquipmentVO;
               _loc4_.attack = _loc4_.minAttack + Math.random() * (_loc4_.maxAttack - _loc4_.minAttack + 1);
               _loc4_.rengPin = _loc4_.minRenPin + Math.random() * (_loc4_.maxRenPin - _loc4_.minRenPin + 1);
               break;
            case "necklace":
               _loc6_ = _loc7_ as NecklaceEquipmentVO;
               _loc6_.criticalRate = _loc6_.minCriticalRate + Math.random() * (_loc6_.maxCriticalRate - _loc6_.minCriticalRate + 1);
               _loc6_.rengPin = _loc6_.minRenPin + Math.random() * (_loc6_.maxRenPin - _loc6_.minRenPin + 1);
               break;
            case "gourd":
               _loc9_ = _loc7_ as GourdEquipmentVO;
               _loc9_.maxMagic = _loc9_.minMaxMagic + Math.random() * (_loc9_.maxMaxMagic - _loc9_.minMaxMagic + 1);
               _loc9_.rengPin = _loc9_.minRenPin + Math.random() * (_loc9_.maxRenPin - _loc9_.minRenPin + 1);
               break;
            case "clothes":
               _loc2_ = _loc7_ as ClothesEquipmentVO;
               _loc2_.defence = _loc2_.minDefence + Math.random() * (_loc2_.maxDefence - _loc2_.minDefence + 1);
               _loc2_.riot = _loc2_.maxRiot ? _loc2_.minRiot + Math.round(Math.random() * (_loc2_.maxRiot - _loc2_.minRiot) * 100) / 100 : 0;
               _loc2_.rengPin = _loc2_.minRenPin + Math.random() * (_loc2_.maxRenPin - _loc2_.minRenPin + 1);
               break;
            case "pet":
               break;
            case "fashion":
               _loc5_ = _loc7_ as ILimitEquipmentVO;
               if(_loc5_.TimeLimit == -1)
               {
                  _loc8_ = **********;
               }
               else
               {
                  _loc8_ = _loc5_.TimeLimit;
               }
               if(_loc8_ <= 0)
               {
                  throw new Error();
               }
               _loc5_.remainingTime = Math.ceil(_loc8_ / 24);
               _loc10_ = new GetServerTimeListenerForLimitEquipmentVO();
               _loc10_.addLimitEquipmentVO(_loc5_);
               _loc5_.setGetServerTimeListener(_loc10_);
               GamingUI.getInstance().getServerTime.addGetServerTimeListener(_loc10_);
               GamingUI.getInstance().getServerTime.getServerTime(true);
               break;
            case "scroll":
               _loc3_ = getEquipment((_loc7_ as ScrollEquipmentVO).compositeEquipmentVO.id,XMLSingle.getInstance().equipmentXML);
               (_loc7_ as ScrollEquipmentVO).compositeEquipmentVO.clear();
               (_loc7_ as ScrollEquipmentVO).compositeEquipmentVO = null;
               (_loc7_ as ScrollEquipmentVO).compositeEquipmentVO = _loc3_;
         }
         return _loc7_;
      }
      
      public static function getSkill(param1:*, param2:XML) : SkillVO
      {
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc14_:PlayerActiveSkillVO = null;
         var _loc6_:PlayerPassiveSkillVO = null;
         var _loc5_:PetActiveSkillVO = null;
         var _loc3_:PetPassiveSkillVO = null;
         var _loc4_:PetAwakeSkillVO = null;
         var _loc12_:XMLList = null;
         var _loc13_:* = null;
         param1 = param1;
         var _loc7_:XML = param2.item.(@id == param1)[0];
         var _loc8_:String = String(_loc7_.@type);
         if(_loc8_ == "playerActive")
         {
            _loc14_ = new PlayerActiveSkillVO();
            _loc14_.id = param1;
            _loc14_.className = String(_loc7_.@className);
            _loc14_.name = String(_loc7_.@name);
            _loc14_.level = int(_loc7_.@level);
            _loc14_.description = String(_loc7_.@description);
            _loc14_.upgradePrice = int(_loc7_.@upgradePrice);
            _loc14_.owner = String(_loc7_.@owner);
            _loc14_.type = _loc8_;
            _loc14_.maxLevel = int(_loc7_.@maxLevel);
            _loc14_.serialNumber = int(_loc7_.@serialNumber);
            _loc14_.initLevel = int(_loc7_.@initLevel);
            _loc14_.originalCoolDown = int(_loc7_.@coolDown);
            _loc14_.originalNextCoolDown = int(_loc7_.@nextCoolDown);
            _loc14_.coolDown = int(_loc7_.@coolDown);
            _loc14_.nextCoolDown = int(_loc7_.@nextCoolDown);
            _loc14_.manaCost = int(_loc7_.@manaCost);
            _loc14_.nextManaCost = int(_loc7_.@nextManaCost);
            _loc14_.hurt = int(_loc7_.@hurt);
            _loc14_.nextHurt = int(_loc7_.@nextHurt);
            _loc14_.lengthOfTime = int(_loc7_.@lengthOfTime);
            _loc14_.nextLengthOfTime = int(_loc7_.@nextLengthOfTime);
            _loc14_.additionalAttack = int(_loc7_.@additionalAttack);
            _loc14_.nextAdditionalAttack = int(_loc7_.@nextAdditionalAttack);
            return _loc14_;
         }
         if(_loc8_ == "playerPassive")
         {
            _loc6_ = new PlayerPassiveSkillVO();
            _loc6_.id = param1;
            _loc6_.className = String(_loc7_.@className);
            _loc6_.name = String(_loc7_.@name);
            _loc6_.level = int(_loc7_.@level);
            _loc6_.description = String(_loc7_.@description);
            _loc6_.upgradePrice = int(_loc7_.@upgradePrice);
            _loc6_.owner = String(_loc7_.@owner);
            _loc6_.type = _loc8_;
            _loc6_.maxLevel = int(_loc7_.@maxLevel);
            _loc6_.serialNumber = int(_loc7_.@serialNumber);
            _loc6_.initLevel = int(_loc7_.@initLevel);
            return _loc6_;
         }
         if(_loc8_ == "petActive")
         {
            _loc5_ = new PetActiveSkillVO();
            _loc5_.id = param1;
            _loc5_.className = String(_loc7_.@className);
            _loc5_.name = String(_loc7_.@name);
            _loc5_.level = int(_loc7_.@level);
            _loc5_.description = String(_loc7_.@description);
            _loc5_.upgradePrice = int(_loc7_.@upgradePrice);
            _loc5_.owner = String(_loc7_.@owner);
            _loc5_.type = _loc8_;
            _loc5_.maxLevel = int(_loc7_.@maxLevel);
            _loc5_.serialNumber = int(_loc7_.@serialNumber);
            _loc5_.initLevel = int(_loc7_.@initLevel);
            _loc5_.hurCoefficient = int(_loc7_.@hurCoefficient);
            _loc5_.originalCoolDown = int(_loc7_.@coolDown);
            _loc5_.originalManaCost = int(_loc7_.@manaCost);
            _loc5_.additionHurt = int(_loc7_.@additionHurt);
            _loc5_.originalEssenceCost = int(_loc7_.@essenceCost);
            _loc5_.pkHurtCoefficient = int(_loc7_.@pkHurtCoefficient);
            _loc5_.additionPkHurt = int(_loc7_.@additionPkHurt);
            _loc5_.subOrangeWalkSpeed = int(_loc7_.@subWalkSpeed);
            _loc5_.subOrangeDefence = int(_loc7_.@subWalkSpeed);
            _loc5_.diePrecentBase = int(_loc7_.@diePrecentBase);
            _loc5_.buffTime = int(_loc7_.@buffTime);
            return _loc5_;
         }
         if(_loc8_ == "petPassive")
         {
            _loc3_ = new PetPassiveSkillVO();
            _loc3_.id = param1;
            _loc3_.className = String(_loc7_.@className);
            _loc3_.name = String(_loc7_.@name);
            _loc3_.level = int(_loc7_.@level);
            _loc3_.description = String(_loc7_.@description);
            _loc3_.upgradePrice = int(_loc7_.@upgradePrice);
            _loc3_.owner = String(_loc7_.@owner);
            _loc3_.type = _loc8_;
            _loc3_.maxLevel = int(_loc7_.@maxLevel);
            _loc3_.serialNumber = int(_loc7_.@serialNumber);
            _loc3_.initLevel = int(_loc7_.@initLevel);
            _loc3_.passiveType = String(_loc7_.@passiveType);
            _loc3_.originalValue = int(_loc7_.@value);
            _loc3_.value = _loc3_.originalValue;
            _loc3_.unit = String(_loc7_.@unit);
            _loc3_.bloodPerLow = int(_loc7_.@bloodPerLow);
            return _loc3_;
         }
         if(_loc8_ == "petAwakePassive")
         {
            _loc4_ = new PetAwakeSkillVO();
            _loc4_.id = param1;
            _loc4_.className = String(_loc7_.@className);
            _loc4_.name = String(_loc7_.@name);
            _loc4_.level = int(_loc7_.@level);
            _loc4_.description = String(_loc7_.@description);
            _loc4_.owner = String(_loc7_.@owner);
            _loc4_.type = _loc8_;
            _loc4_.maxLevel = int(_loc7_.@maxLevel);
            _loc12_ = _loc7_.promote;
            _loc10_ = int(_loc12_ ? _loc12_.length() : 0);
            _loc4_.attributes = new Vector.<String>();
            _loc4_.attributeValues = new Vector.<PromoteValueObject>();
            _loc4_.descriptions = new Vector.<String>();
            _loc9_ = 0;
            while(_loc9_ < _loc10_)
            {
               _loc4_.attributes.push(String(_loc12_[_loc9_].@attribute));
               _loc4_.attributeValues.push(XMLSingle.getInstance().initPromoteValueObject(_loc12_[_loc9_]));
               _loc4_.descriptions.push(String(_loc12_[_loc9_].@description));
               _loc9_++;
            }
            return _loc4_;
         }
         var _loc11_:SkillVO = new SkillVO();
         _loc11_.id = param1;
         _loc11_.className = String(_loc7_.@className);
         _loc11_.name = String(_loc7_.@name);
         _loc11_.level = int(_loc7_.@level);
         _loc11_.description = String(_loc7_.@description);
         _loc11_.upgradePrice = int(_loc7_.@upgradePrice);
         _loc11_.owner = String(_loc7_.@owner);
         _loc11_.type = _loc8_;
         _loc11_.maxLevel = int(_loc7_.@maxLevel);
         _loc11_.serialNumber = int(_loc7_.@serialNumber);
         _loc11_.initLevel = int(_loc7_.@initLevel);
         return _loc11_;
      }
      
      public static function getTalentVO(param1:int, param2:XML) : TalentVO
      {
         var _loc4_:XML = param2.item.(@id == param1)[0];
         var _loc3_:TalentVO = new TalentVO();
         _loc3_.id = param1;
         _loc3_.className = String(_loc4_.@className);
         _loc3_.name = String(_loc4_.@name);
         _loc3_.level = int(_loc4_.@level);
         _loc3_.maxLevel = int(_loc4_.@maxLevel);
         _loc3_.owner = String(_loc4_.@owner);
         _loc3_.talentType = String(_loc4_.@talentType);
         _loc3_.promoteValue = int(_loc4_.@promoteValue);
         _loc3_.upgradePill = int(_loc4_.@upgradePill);
         _loc3_.description = String(_loc4_.@description);
         return _loc3_;
      }
      
      public static function getPrivilegeVO(param1:int, param2:XML) : PrivilegeVO
      {
         var _loc3_:XML = param2.item.(@id == param1)[0];
         var _loc4_:PrivilegeVO = new PrivilegeVO();
         _loc4_.id = param1;
         _loc4_.className = String(_loc3_.@className);
         _loc4_.name = String(_loc3_.@name);
         _loc4_.value = int(_loc3_.@value);
         _loc4_.description = String(_loc3_.@description);
         return _loc4_;
      }
      
      public static function getPrivilegeVOs(param1:String, param2:XML) : Vector.<PrivilegeVO>
      {
         var _loc7_:PrivilegeVO = null;
         var _loc4_:* = null;
         var _loc3_:Vector.<int> = MyFunction.getInstance().excreteString(param1);
         var _loc5_:int = int(_loc3_.length);
         var _loc8_:int = 0;
         var _loc6_:Vector.<PrivilegeVO> = new Vector.<PrivilegeVO>();
         _loc8_ = 0;
         while(_loc8_ < _loc5_)
         {
            _loc7_ = getPrivilegeVO(_loc3_[_loc8_],param2);
            _loc6_.push(_loc7_);
            _loc8_++;
         }
         return _loc6_;
      }
      
      public static function getTaskGoal(param1:int, param2:XML) : TaskGoalVO
      {
         var _loc4_:XML = param2.TaskGoal.item.(@id == param1)[0];
         var _loc3_:TaskGoalVO = new TaskGoalVO();
         _loc3_.id = param1;
         _loc3_.name = String(_loc4_.@name);
         _loc3_.str = String(_loc4_.@str);
         _loc3_.num = 1;
         return _loc3_;
      }
      
      public static function getTaskGoals(param1:Vector.<int>, param2:XML) : Vector.<TaskGoalVO>
      {
         var _loc4_:int = int(param1.length);
         var _loc5_:int = 0;
         var _loc3_:Vector.<TaskGoalVO> = new Vector.<TaskGoalVO>();
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_.push(getTaskGoal(param1[_loc5_],param2));
            _loc5_++;
         }
         return _loc3_;
      }
      
      public static function getTask(param1:int, param2:XML) : MTaskVO
      {
         var _loc11_:MTaskVO = null;
         var _loc7_:String = null;
         var _loc4_:TaskRewardVO = null;
         var _loc8_:String = null;
         var _loc5_:XML = param2.Task.item.(@id == param1)[0];
         if(_loc5_ == null)
         {
            return null;
         }
         switch(MyFunction.getInstance().getTaskTypeFromID(param1))
         {
            case 0:
               _loc11_ = new EveryDayTaskVO();
               break;
            case 1:
               _loc11_ = new LimitingTimeTaskVO();
               (_loc11_ as LimitingTimeTaskVO).formerData = String(_loc5_.@formerData);
               (_loc11_ as LimitingTimeTaskVO).latterData = String(_loc5_.@latterData);
               break;
            case 2:
               _loc11_ = new AccumulatedTaskVO();
               (_loc11_ as AccumulatedTaskVO).taskCount = int(_loc5_.@taskCount);
            case 3:
               _loc11_ = new LimtingTimeAccumulatedTaskVO();
               (_loc11_ as LimtingTimeAccumulatedTaskVO).taskCount = int(_loc5_.@taskCount);
               (_loc11_ as LimtingTimeAccumulatedTaskVO).formerData = String(_loc5_.@formerData);
               (_loc11_ as LimtingTimeAccumulatedTaskVO).latterData = String(_loc5_.@latterData);
               break;
            default:
               new Error("任务id错误！");
         }
         _loc11_.id = param1;
         _loc11_.isGoto = int(_loc5_.@isGoto);
         _loc11_.name = String(_loc5_.@name);
         _loc11_.description = String(_loc5_.@description);
         _loc11_.type = String(_loc5_.@type);
         _loc11_.isNew = Boolean(int(_loc5_.@isNew));
         _loc11_.resetType = String(_loc5_.@resetType);
         _loc11_.isGoto = int(_loc5_.@isGoto);
         _loc11_.taskGoalVO_ids = MyFunction.getInstance().excreteString(String(_loc5_.taskGoal.@ids));
         _loc11_.taskGoalVO_nums = MyFunction.getInstance().excreteString(String(_loc5_.taskGoal.@nums));
         var _loc9_:XML = _loc5_.everygototask[0];
         if(_loc9_)
         {
            _loc7_ = String(_loc9_.@type);
            if(_loc7_ == "1")
            {
               if(_loc11_.gotoInfo == null)
               {
                  _loc11_.gotoInfo = new NewGotoData();
               }
               _loc11_.gotoInfo.type = String(_loc9_.@type);
            }
            else if(_loc7_ == "9" || _loc7_ == "10" || _loc7_ == "2" || _loc7_ == "3" || _loc7_ == "4" || _loc7_ == "5" || _loc7_ == "6" || _loc7_ == "7" || _loc7_ == "8")
            {
               if(_loc11_.gotoInfo == null)
               {
                  _loc11_.gotoInfo = new NewGotoData();
               }
               _loc11_.gotoInfo.type = String(_loc9_.@type);
               _loc11_.gotoInfo.gotobtnname = String(_loc9_.gotoname.@value);
               _loc11_.gotoInfo.showClassName = String(_loc9_.classname.@value);
               _loc11_.gotoInfo.swfpath = String(_loc9_.swfpath.@value);
               _loc11_.gotoInfo.xmlpath = String(_loc9_.xmlpath.@value);
            }
         }
         _loc9_ = _loc5_.activitytask[0];
         if(_loc9_)
         {
            _loc7_ = String(_loc9_.@type);
            if(_loc7_ == "1")
            {
               if(_loc11_.gotoInfo == null)
               {
                  _loc11_.gotoInfo = new NewGotoData();
               }
               _loc11_.gotoInfo.type = String(_loc9_.@type);
               _loc11_.gotoInfo.gotobtnname = String(_loc9_.gotoname.@value);
               _loc11_.gotoInfo.showClassName = String(_loc9_.classname.@value);
               _loc11_.gotoInfo.swfpath = String(_loc9_.swfpath.@value);
               _loc11_.gotoInfo.xmlpath = String(_loc9_.xmlpath.@value);
            }
            else if(_loc7_ == "2" || _loc7_ == "3" || _loc7_ == "4" || _loc7_ == "5" || _loc7_ == "6" || _loc7_ == "7")
            {
               if(_loc11_.gotoInfo == null)
               {
                  _loc11_.gotoInfo = new NewGotoData();
               }
               _loc11_.gotoInfo.type = String(_loc9_.@type);
            }
         }
         var _loc6_:XMLList = _loc5_.taskReward;
         var _loc10_:int = 0;
         var _loc3_:int = int(_loc11_.taskGoalVO_nums.length);
         _loc10_ = 0;
         while(_loc10_ < _loc3_)
         {
            _loc11_.currentTaskGoalVO_nums.push(0);
            _loc10_++;
         }
         _loc3_ = int(_loc6_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc3_)
         {
            switch(_loc8_ = String(_loc6_[_loc10_].@type))
            {
               case "equipmentReward":
                  _loc4_ = new TaskRewardVO_Equipment();
                  (_loc4_ as TaskRewardVO_Equipment).setDataXML(_loc6_[_loc10_]);
                  break;
               case "experienceReward":
                  _loc4_ = new TaskRewardVO_Experience();
                  (_loc4_ as TaskRewardVO_Experience).value = int(_loc6_[_loc10_].@value);
                  break;
               case "moneyReward":
                  _loc4_ = new TaskRewardVO_Money();
                  (_loc4_ as TaskRewardVO_Money).value = int(_loc6_[_loc10_].@value);
                  break;
               case "zhHJZHRward":
                  _loc4_ = new TaskRewardVO_ZHHJZH();
                  (_loc4_ as TaskRewardVO_ZHHJZH).value = int(_loc6_[_loc10_].@value);
                  break;
               case "lSHSHRward":
                  _loc4_ = new TaskRewardVO_LSHSHI();
                  (_loc4_ as TaskRewardVO_LSHSHI).value = int(_loc6_[_loc10_].@value);
                  break;
               default:
                  throw new Error("暂无此类型的任务奖励， 任务奖励类型出错！");
            }
            _loc4_.type = _loc8_;
            _loc4_.description = String(_loc6_[_loc10_].@description);
            _loc11_.taskRewardVOs.push(_loc4_);
            _loc10_++;
         }
         return _loc11_;
      }
      
      public static function getBuff(param1:int, param2:XML) : BuffVO
      {
         var _loc3_:BuffVO = null;
         var _loc5_:String;
         var _loc4_:XML;
         switch(_loc5_ = String((_loc4_ = param2.item.(@id == param1)[0]).@type))
         {
            case "allTimeBuff":
               _loc3_ = new AllTimeBuffVO();
               break;
            case "onlyOnLineBuff":
               _loc3_ = new OnlyOnLineBuffVO();
               break;
            case "noTimeBuff":
               _loc3_ = new NotTimeBuffVO();
               break;
            default:
               throw new Error("类型错误！");
         }
         _loc3_.id = param1;
         _loc3_.className = String(_loc4_.@className);
         _loc3_.type = _loc5_;
         _loc3_.field = String(_loc4_.@field);
         _loc3_.xml = _loc4_;
         return _loc3_;
      }
      
      public static function getEquipmentVOsIDs(param1:Vector.<int>, param2:XML, param3:Vector.<int>, param4:Boolean, param5:String = null) : Vector.<EquipmentVO>
      {
         var _loc9_:int = 0;
         var _loc6_:EquipmentVO = null;
         var _loc8_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc11_:int = 0;
         var _loc10_:int = 0;
         var _loc7_:int = int(param1.length);
         _loc11_ = 0;
         while(_loc11_ < _loc7_)
         {
            if(param1[_loc11_])
            {
               _loc6_ = getEquipment(param1[_loc11_],param2,param5);
               _loc6_.isBinding = param4;
               if(_loc6_ is StackEquipmentVO)
               {
                  switch(_loc6_.equipmentType)
                  {
                     case "material":
                     case "potion":
                     case "pocket":
                     case "buffEquipment":
                     case "forceDan":
                     case "insetGem":
                        break;
                     default:
                        throw new Error("还没为该类型装备做好装备");
                  }
                  (_loc6_ as StackEquipmentVO).num = param3[_loc11_];
                  _loc8_.push(_loc6_);
               }
               else
               {
                  _loc9_ = param3[_loc11_];
                  _loc10_ = 0;
                  while(_loc10_ < _loc9_)
                  {
                     _loc6_ = getEquipmentVOByID(param1[_loc11_],param2,param5,param4);
                     _loc8_.push(_loc6_);
                     _loc10_++;
                  }
               }
            }
            _loc11_++;
         }
         return _loc8_;
      }
      
      public static function getIDByClassName(param1:String, param2:XML) : int
      {
         var _loc4_:* = param2.item.(@className == param1);
         var _loc3_:XML = _loc4_[0];
         if(_loc3_ == null)
         {
            return 0;
         }
         return int(_loc3_.@id);
      }
      
      public static function setScrollRequiredEquipmentVOs(param1:XML, param2:XML, param3:String = null) : Vector.<EquipmentVO>
      {
         var _loc4_:EquipmentVO = null;
         var _loc6_:XMLList = param1.children();
         var _loc5_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         for each(var _loc7_ in _loc6_)
         {
            _loc4_ = getEquipmentVOByID(_loc7_.@id,param2,param3);
            _loc5_.push(_loc4_);
         }
         return _loc5_;
      }
      
      public static function getEquipmentVOs(param1:XML, param2:XML, param3:Boolean, param4:String = null) : Vector.<EquipmentVO>
      {
         var _loc5_:* = null;
         var _loc9_:XMLList = param1.children();
         var _loc6_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc8_:Vector.<int> = new Vector.<int>();
         var _loc7_:Vector.<int> = new Vector.<int>();
         for each(var _loc10_ in _loc9_)
         {
            _loc8_.push(int(_loc10_.@id));
            _loc7_.push(int(_loc10_.@num));
         }
         return getEquipmentVOsIDs(_loc8_,param2,_loc7_,param3,param4);
      }
      
      private static function setScrollRequiredNum(param1:XML) : Vector.<int>
      {
         var _loc2_:int = 0;
         var _loc4_:XMLList = param1.children();
         var _loc3_:Vector.<int> = new Vector.<int>();
         for each(var _loc5_ in _loc4_)
         {
            _loc2_ = int(_loc5_.@num);
            _loc3_.push(_loc2_);
         }
         return _loc3_;
      }
      
      public static function getAutomaticPetVOByID(param1:String, param2:XML) : AutomaticPetVO
      {
         var _loc3_:AutomaticPetVO = new AutomaticPetVO();
         _loc3_.initFromXML(param1,1,param2);
         return _loc3_;
      }
      
      override protected function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         dataXML = _dataXML;
         taskXML = _taskXML;
         mainLineTaskDescription = _mainLineTaskDescription;
         broadcast = _broadcast;
         xiuLianContentXML = _xiuLianContentXML;
         NewRank = _NewRank;
         Lingge = _lingge;
         onLineGiftBagXML = _onLineGiftBagXML;
         mainLineTaskGoalsXML = _mainLineTaskGoalsXML;
         mainLineTaskXML = _mainLineTaskXML;
         everyDayTask = _everyDayTask;
         mainLineTaskListXML = _mainLineTaskListXML;
         signXML = _signXML;
         tehui = _tehui;
         weekpay = _weekpay;
         buchang = _buchang;
         wuyi = _wuyi;
         consumer = _consumer;
         buffXML = _buffXML;
         activeTaskListXML = _activeTaskListXML;
         smallAssistantXML = _smallAssistantXML;
         monkeyXML = _monkeyXML;
         dragonXML = _dragonXML;
         erLangShenXML = _erLangShenXML;
         changEXML = _changEXML;
         foxXML = _foxXML;
         tieShanXML = _tieShanXML;
         houyiXML = _houyiXML;
         ziXiaXML = _ziXiaXML;
         equipmentXML = _equipmentXML;
         skillXML = _skillXML;
         talentXML = _talentXML;
         privilegeXML = _privilegeXML;
         vipXML = _vipXML;
         textXML = _textXML;
         farmXML = _farmXML;
         xiaoKongXML = _xiaoKongXML;
         xiaoBaXML = _xiaoBaXML;
         tuDiSkillXML = _tuDiSkillXML;
         tuDiDataXML = _tuDiDataXML;
         automaticPetsXML = _automaticPetsXML;
         automaticPetSkillsXML = _automaticPetSkillsXML;
         automaticPetOtherDataXML = _automaticPetOtherDataXML;
         mountsXML = _mountsXML;
         mountSkillsXML = _mountSkillsXML;
         m_equipmentXMLPartData = new EquipmentXMLPartData();
      }
      
      public function clear2() : void
      {
         calculationAllAddValueInUpgradeEquipment = null;
      }
      
      override public function clear() : void
      {
         calculationAllAddValueInUpgradeEquipment = null;
         monkeyXML = null;
         dragonXML = null;
         erLangShenXML = null;
         changEXML = null;
         foxXML = null;
         equipmentXML = null;
         skillXML = null;
         talentXML = null;
         dataXML = null;
         privilegeXML = null;
         vipXML = null;
         taskXML = null;
         textXML = null;
         farmXML = null;
         buffXML = null;
         xiaoKongXML = null;
         xiaoBaXML = null;
         xiuLianContentXML = null;
         tuDiSkillXML = null;
         tuDiDataXML = null;
         onLineGiftBagXML = null;
         mainLineTaskGoalsXML = null;
         mainLineTaskXML = null;
         everyDayTask = null;
         mainLineTaskDescription = null;
         broadcast = null;
         mainLineTaskListXML = null;
         signXML = null;
         tehui = null;
         weekpay = null;
         buchang = null;
         wuyi = null;
         consumer = null;
         automaticPetsXML = null;
         automaticPetSkillsXML = null;
         automaticPetOtherDataXML = null;
         mountsXML = null;
         mountSkillsXML = null;
         activeTaskListXML = null;
         smallAssistantXML = null;
         super.clear();
         ClearUtil.clearObject(m_equipmentXMLPartData);
         m_equipmentXMLPartData = null;
         _instance = null;
      }
      
      public function getXiuLianContent0LevelByContent(param1:String, param2:XML, param3:String) : XiuLianContent
      {
         var _loc4_:String = null;
         switch(param3)
         {
            case "ShiFu":
               _loc4_ = param1 + "_sF_0";
               break;
            case "TuDi":
               _loc4_ = param1 + "_tD_0";
               break;
            default:
               throw new Error();
         }
         return getXiuLianContent(_loc4_,param2,param3);
      }
      
      public function getXiuLianContent(param1:String, param2:XML, param3:String) : XiuLianContent
      {
         var _loc4_:XiuLianContent = new XiuLianContent();
         _loc4_.id = param1;
         setXiuLianContent(_loc4_,param2,param3);
         return _loc4_;
      }
      
      public function setXiuLianContent(param1:XiuLianContent, param2:XML, param3:String) : void
      {
         var _loc8_:int = 0;
         var _loc6_:* = null;
         var _loc5_:XML = param2[param3][0].item.(@id == param1.id)[0];
         param1.level = int(_loc5_.@level);
         param1.name = String(_loc5_.@name);
         param1.name2 = String(_loc5_.@name2);
         param1.description = String(_loc5_.@description);
         param1.needXiuLianValue = int(_loc5_.@needXiuLianValue);
         param1.content = String(_loc5_.@content);
         param1.owner = String(_loc5_.@owner);
         param1.addAttributes = new Vector.<String>();
         param1.addAttributeValueObjects = new Vector.<PromoteValueObject>();
         var _loc7_:XMLList = _loc5_.promote;
         var _loc4_:int = int(_loc7_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            param1.addAttributes.push(String(_loc7_[_loc8_].@attribute));
            param1.addAttributeValueObjects.push(initPromoteValueObject(_loc7_[_loc8_]));
            _loc8_++;
         }
      }
      
      private function initPromoteValueObject(param1:XML) : PromoteValueObject
      {
         var _loc3_:PromoteValueObject = null;
         var _loc5_:int = 0;
         _loc3_ = new PromoteValueObject();
         _loc3_.value = String(param1.@value);
         var _loc4_:XMLList = param1.promote;
         var _loc2_:int = int(_loc4_ ? _loc4_.length() : 0);
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            if(_loc3_.addAttributes == null)
            {
               _loc3_.addAttributes = new Vector.<String>();
            }
            _loc3_.addAttributes.push(String(_loc4_[_loc5_].@attribute));
            if(_loc3_.addAttributeValueObjects == null)
            {
               _loc3_.addAttributeValueObjects = new Vector.<PromoteValueObject>();
            }
            _loc3_.addAttributeValueObjects.push(initPromoteValueObject(_loc4_[_loc5_]));
            if(_loc3_.descriptions == null)
            {
               _loc3_.descriptions = new Vector.<String>();
            }
            _loc3_.descriptions.push(String(_loc4_[_loc5_].@description));
            if(_loc3_.extras == null)
            {
               _loc3_.extras = new Vector.<Object>();
            }
            _loc3_.extras.push(String(_loc4_[_loc5_].extra));
            _loc5_++;
         }
         return _loc3_;
      }
      
      public function getLandVO(param1:int, param2:XML) : LandVO
      {
         var _loc3_:LandVO = new LandVO();
         var _loc4_:XML = param2.land.(@id == param1)[0];
         _loc3_.H_width = int(_loc4_.@H_width);
         _loc3_.V_height = int(_loc4_.@V_height);
         _loc3_.isEnableMove = Boolean(int(_loc4_.@isEnableMove));
         _loc3_.id = param1;
         return _loc3_;
      }
      
      public function setPetData(param1:int, param2:int, param3:PetEquipmentVO) : void
      {
         var _loc4_:XML = XMLSingle.getInstance().equipmentXML.item.(@id == param1)[0].data.(@petLevel == param2)[0];
         param3.experienceVolume = int(_loc4_.@experienceVolume);
         param3.essentialVolume = int(_loc4_.@essentialVolume);
      }
      
      public function get monkeyXML() : XML
      {
         return _antiwear._monkeyXML;
      }
      
      public function set monkeyXML(param1:XML) : void
      {
         _antiwear._monkeyXML = param1;
      }
      
      public function get dragonXML() : XML
      {
         return _antiwear._dragonXML;
      }
      
      public function set dragonXML(param1:XML) : void
      {
         _antiwear._dragonXML = param1;
      }
      
      public function get erLangShenXML() : XML
      {
         return _antiwear._erLangShenXML;
      }
      
      public function set erLangShenXML(param1:XML) : void
      {
         _antiwear._erLangShenXML = param1;
      }
      
      public function get changEXML() : XML
      {
         return _antiwear._changEXML;
      }
      
      public function set changEXML(param1:XML) : void
      {
         _antiwear._changEXML = param1;
      }
      
      public function get foxXML() : XML
      {
         return _antiwear._foxXML;
      }
      
      public function set foxXML(param1:XML) : void
      {
         _antiwear._foxXML = param1;
      }
      
      public function get tieShanXML() : XML
      {
         return _antiwear._tieShanXML;
      }
      
      public function set tieShanXML(param1:XML) : void
      {
         _antiwear._tieShanXML = param1;
      }
      
      public function get houyiXML() : XML
      {
         return _antiwear._houyiXML;
      }
      
      public function set houyiXML(param1:XML) : void
      {
         _antiwear._houyiXML = param1;
      }
      
      public function get ziXiaXML() : XML
      {
         return _antiwear._ziXiaXML;
      }
      
      public function set ziXiaXML(param1:XML) : void
      {
         _antiwear._ziXiaXML = param1;
      }
      
      public function get equipmentXML() : XML
      {
         return _equipmentXML;
      }
      
      public function set equipmentXML(param1:XML) : void
      {
         _equipmentXML = param1;
      }
      
      public function get skillXML() : XML
      {
         return _antiwear._skillXML;
      }
      
      public function set skillXML(param1:XML) : void
      {
         _antiwear._skillXML = param1;
      }
      
      public function get talentXML() : XML
      {
         return _antiwear._talentXML;
      }
      
      public function set talentXML(param1:XML) : void
      {
         _antiwear._talentXML = param1;
      }
      
      public function get dataXML() : XML
      {
         return _dataXML;
      }
      
      public function set dataXML(param1:XML) : void
      {
         _dataXML = param1;
      }
      
      public function get privilegeXML() : XML
      {
         return _antiwear._privilegeXML;
      }
      
      public function set privilegeXML(param1:XML) : void
      {
         _antiwear._privilegeXML = param1;
      }
      
      public function get vipXML() : XML
      {
         return _antiwear._vipXML;
      }
      
      public function set vipXML(param1:XML) : void
      {
         _antiwear._vipXML = param1;
      }
      
      public function get taskXML() : XML
      {
         return _antiwear.taskXML;
      }
      
      public function set taskXML(param1:XML) : void
      {
         _antiwear.taskXML = param1;
      }
      
      public function get textXML() : XML
      {
         return _antiwear._textXML;
      }
      
      public function set textXML(param1:XML) : void
      {
         _antiwear._textXML = param1;
      }
      
      public function get farmXML() : XML
      {
         return _antiwear._farmXML;
      }
      
      public function set farmXML(param1:XML) : void
      {
         _antiwear._farmXML = param1;
      }
      
      public function get buffXML() : XML
      {
         return _antiwear.buffXML;
      }
      
      public function set buffXML(param1:XML) : void
      {
         _antiwear.buffXML = param1;
      }
      
      public function get xiaoKongXML() : XML
      {
         return _antiwear._xiaoKongXML;
      }
      
      public function set xiaoKongXML(param1:XML) : void
      {
         _antiwear._xiaoKongXML = param1;
      }
      
      public function get xiaoBaXML() : XML
      {
         return _antiwear._xiaoBaXML;
      }
      
      public function set xiaoBaXML(param1:XML) : void
      {
         _antiwear._xiaoBaXML = param1;
      }
      
      public function get xiuLianContentXML() : XML
      {
         return _antiwear.xiuLianContentXML;
      }
      
      public function set xiuLianContentXML(param1:XML) : void
      {
         _antiwear.xiuLianContentXML = param1;
      }
      
      public function get NewRank() : XML
      {
         return _antiwear.NewRank;
      }
      
      public function set NewRank(param1:XML) : void
      {
         _antiwear.NewRank = param1;
      }
      
      public function get Lingge() : XML
      {
         return _antiwear.Lingge;
      }
      
      public function set Lingge(param1:XML) : void
      {
         _antiwear.Lingge = param1;
      }
      
      public function get tuDiSkillXML() : XML
      {
         return _antiwear._tuDiSkillXML;
      }
      
      public function set tuDiSkillXML(param1:XML) : void
      {
         _antiwear._tuDiSkillXML = param1;
      }
      
      public function get tuDiDataXML() : XML
      {
         return _antiwear._tuDiDataXML;
      }
      
      public function set tuDiDataXML(param1:XML) : void
      {
         _antiwear._tuDiDataXML = param1;
      }
      
      public function get onLineGiftBagXML() : XML
      {
         return _antiwear.onLineGiftBagXML;
      }
      
      public function set onLineGiftBagXML(param1:XML) : void
      {
         _antiwear.onLineGiftBagXML = param1;
      }
      
      public function get mainLineTaskGoalsXML() : XML
      {
         return _antiwear.mainLineTaskGoalsXML;
      }
      
      public function set mainLineTaskGoalsXML(param1:XML) : void
      {
         _antiwear.mainLineTaskGoalsXML = param1;
      }
      
      public function get mainLineTaskXML() : XML
      {
         return _antiwear.mainLineTaskXML;
      }
      
      public function set mainLineTaskXML(param1:XML) : void
      {
         _antiwear.mainLineTaskXML = param1;
      }
      
      public function get everyDayTask() : XML
      {
         return _antiwear.everyDayTask;
      }
      
      public function set everyDayTask(param1:XML) : void
      {
         _antiwear.everyDayTask = param1;
      }
      
      public function get mainLineTaskDescription() : XML
      {
         return _antiwear.mainLineTaskDescription;
      }
      
      public function set mainLineTaskDescription(param1:XML) : void
      {
         _antiwear.mainLineTaskDescription = param1;
      }
      
      public function get broadcast() : XML
      {
         return _antiwear.broadcast;
      }
      
      public function set broadcast(param1:XML) : void
      {
         _antiwear.broadcast = param1;
      }
      
      public function get mainLineTaskListXML() : XML
      {
         return _antiwear.mainLineTaskListXML;
      }
      
      public function set mainLineTaskListXML(param1:XML) : void
      {
         _antiwear.mainLineTaskListXML = param1;
      }
      
      public function get activeTaskListXML() : XML
      {
         return _antiwear.activeTaskListXML;
      }
      
      public function set activeTaskListXML(param1:XML) : void
      {
         _antiwear.activeTaskListXML = param1;
      }
      
      public function get signXML() : XML
      {
         return _antiwear.signXML;
      }
      
      public function set signXML(param1:XML) : void
      {
         _antiwear.signXML = param1;
      }
      
      public function get tehui() : XML
      {
         return _antiwear.tehui;
      }
      
      public function set tehui(param1:XML) : void
      {
         _antiwear.tehui = param1;
      }
      
      public function get weekpay() : XML
      {
         return _antiwear.weekpay;
      }
      
      public function set weekpay(param1:XML) : void
      {
         _antiwear.weekpay = param1;
      }
      
      public function get buchang() : XML
      {
         return _antiwear.buchang;
      }
      
      public function set buchang(param1:XML) : void
      {
         _antiwear.buchang = param1;
      }
      
      public function get wuyi() : XML
      {
         return _antiwear.wuyi;
      }
      
      public function set wuyi(param1:XML) : void
      {
         _antiwear.wuyi = param1;
      }
      
      public function get doubleegg() : XML
      {
         return _antiwear.doubleegg;
      }
      
      public function set doubleegg(param1:XML) : void
      {
         _antiwear.doubleegg = param1;
      }
      
      public function get cdKeyMonth() : XML
      {
         return _antiwear.cdKeyMonth;
      }
      
      public function set cdKeyMonth(param1:XML) : void
      {
         _antiwear.cdKeyMonth = param1;
      }
      
      public function get consumer() : XML
      {
         return _antiwear.consumer;
      }
      
      public function set consumer(param1:XML) : void
      {
         _antiwear.consumer = param1;
      }
      
      public function get smallAssistantXML() : XML
      {
         return _antiwear.smallAssistantXML;
      }
      
      public function set smallAssistantXML(param1:XML) : void
      {
         _antiwear.smallAssistantXML = param1;
      }
      
      public function get automaticPetsXML() : XML
      {
         return _automaticPetsXML;
      }
      
      public function set automaticPetsXML(param1:XML) : void
      {
         _automaticPetsXML = param1;
      }
      
      public function set subEquipmentXML(param1:XML) : void
      {
         if(equipmentXML == null)
         {
            equipmentXML = <Equipment></Equipment>;
         }
         for each(var _loc2_ in param1.item)
         {
            _antiwear["equipmentId_" + _loc2_.@id] = int(_loc2_.@id);
         }
         equipmentXML.appendChild(param1.children());
      }
      
      public function checkEquipmentId(param1:int) : void
      {
         try
         {
            if(_antiwear["equipmentId_" + param1] != param1)
            {
               _antiwear.errorHandler();
            }
         }
         catch(error:Error)
         {
            _antiwear.errorHandler();
         }
      }
      
      public function set subAutomaticPetsXML(param1:XML) : void
      {
         if(automaticPetsXML == null)
         {
            automaticPetsXML = <automaticPets />;
         }
         automaticPetsXML.appendChild(param1.children());
      }
      
      public function get automaticPetSkillsXML() : XML
      {
         return _automaticPetSkillsXML;
      }
      
      public function set automaticPetSkillsXML(param1:XML) : void
      {
         _automaticPetSkillsXML = param1;
      }
      
      public function get automaticPetOtherDataXML() : XML
      {
         return _automaticPetOtherDataXML;
      }
      
      public function set automaticPetOtherDataXML(param1:XML) : void
      {
         _automaticPetOtherDataXML = param1;
      }
      
      public function get mountsXML() : XML
      {
         return _mountsXML;
      }
      
      public function set mountsXML(param1:XML) : void
      {
         _mountsXML = param1;
      }
      
      public function get mountSkillsXML() : XML
      {
         return _mountSkillsXML;
      }
      
      public function set mountSkillsXML(param1:XML) : void
      {
         _mountSkillsXML = param1;
      }
      
      public function set subMountsXML(param1:XML) : void
      {
         if(mountsXML == null)
         {
            mountsXML = <mounts />;
         }
         mountsXML.appendChild(param1.children());
      }
      
      public function randomOnePassiveSkillVOInCreatePet(param1:PetEquipmentVO) : Vector.<SkillVO>
      {
         var _loc9_:int = 0;
         var _loc8_:* = null;
         var _loc3_:PetPassiveSkillVO = null;
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:Array = null;
         var _loc4_:Array = null;
         var _loc2_:Vector.<SkillVO> = new Vector.<SkillVO>();
         var _loc6_:int = 100 * Math.random();
         if(_loc6_ < Number(dataXML.PetOddsOnePassiveSkillInCreate.@odd))
         {
            _loc8_ = skillXML.item.(@type == "petPassive" && @level == param1.level && @passiveType == param1.talentVO.talentType);
            _loc7_ = int(_loc8_.length());
            _loc5_ = [];
            _loc10_ = 0;
            while(_loc10_ < _loc7_)
            {
               _loc4_ = String(_loc8_[_loc10_].@useSeriesType).split(",");
               if(_loc4_.indexOf(param1.petSeries) != -1)
               {
                  _loc5_.push(_loc8_[_loc10_]);
               }
               _loc10_++;
            }
            _loc9_ = Math.floor(Math.random() * _loc5_.length);
            _loc3_ = getSkill(_loc5_[_loc9_].@id,skillXML) as PetPassiveSkillVO;
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      public function randomFixedNumPassiveSkillVO(param1:PetEquipmentVO, param2:int) : Vector.<SkillVO>
      {
         var _loc12_:* = null;
         var _loc3_:PetPassiveSkillVO = null;
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc11_:* = undefined;
         var _loc5_:Array = null;
         var _loc4_:Boolean = false;
         var _loc14_:int = 0;
         var _loc13_:int = 0;
         var _loc8_:Vector.<SkillVO> = new Vector.<SkillVO>();
         _loc12_ = skillXML.item.(@type == "petPassive" && @level == param1.level);
         var _loc10_:int = int(_loc12_.length());
         var _loc6_:Array = [];
         if(param1.passiveSkillVOs.length > param2)
         {
            _loc11_ = param1.passiveSkillVOs.slice(param2,-1);
         }
         _loc7_ = 0;
         while(_loc7_ < _loc10_)
         {
            _loc5_ = String(_loc12_[_loc7_].@useSeriesType).split(",");
            if(_loc5_.indexOf(param1.petSeries) != -1)
            {
               if(!_loc11_)
               {
                  _loc6_.push(_loc12_[_loc7_]);
               }
               else
               {
                  _loc14_ = int(_loc11_.length);
                  _loc13_ = 0;
                  while(_loc13_ < _loc14_)
                  {
                     if(_loc12_[_loc7_].@id == _loc11_[_loc13_].id)
                     {
                        _loc4_ = true;
                     }
                     _loc13_++;
                  }
                  if(!_loc4_)
                  {
                     _loc6_.push(_loc12_[_loc7_]);
                  }
               }
            }
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < param2)
         {
            _loc9_ = Math.floor(Math.random() * _loc6_.length);
            _loc3_ = getSkill(_loc6_[_loc9_].@id,skillXML) as PetPassiveSkillVO;
            _loc8_.push(_loc3_);
            _loc6_.splice(_loc9_,1);
            _loc7_++;
         }
         if(_loc11_)
         {
            _loc8_ = _loc8_.concat(_loc11_);
         }
         return _loc8_;
      }
      
      public function randomGetPassiveSkillVO(param1:PetEquipmentVO) : Vector.<SkillVO>
      {
         var _loc3_:PetPassiveSkillVO = null;
         var _loc4_:int = 0;
         var _loc2_:Vector.<SkillVO> = new Vector.<SkillVO>();
         var _loc5_:* = skillXML.item.(@type == "petPassive" && @level == param1.level);
         for each(var _loc6_ in _loc5_)
         {
            _loc4_ = 100 * Math.random();
            if(_loc6_.@passiveType == param1.talentVO.talentType)
            {
               if(_loc4_ <= Number(dataXML.PetOddsPassiveSkills.item.(@level == param1.level)[0].@thePassiveOdds))
               {
                  _loc3_ = getSkill(_loc6_.@id,skillXML) as PetPassiveSkillVO;
                  _loc2_.push(_loc3_);
               }
            }
            else if(_loc4_ <= Number(dataXML.PetOddsPassiveSkills.item.(@level == param1.level)[0].otherPassiveOdds))
            {
               _loc3_ = getSkill(_loc6_.@id,skillXML) as PetPassiveSkillVO;
               _loc2_.push(_loc3_);
            }
         }
         if(_loc2_.length > 5)
         {
            _loc2_ = _loc2_.slice(0,4);
         }
         return _loc2_;
      }
      
      public function randomGetOnePassiveSkillVO(param1:PetEquipmentVO, param2:int, param3:Number = 0) : SkillVO
      {
         var _loc18_:SkillVO = null;
         var _loc5_:* = NaN;
         var _loc11_:int = 0;
         var _loc19_:* = null;
         var _loc8_:int = 0;
         var _loc7_:Array = null;
         var _loc6_:Array = null;
         var _loc15_:int = 0;
         var _loc17_:int = 0;
         var _loc4_:Boolean = false;
         var _loc13_:int = 0;
         var _loc10_:int = 0;
         var _loc9_:int = 0;
         var _loc14_:Number = NaN;
         var _loc12_:Array = [];
         var _loc16_:Number = Math.random();
         switch(param2)
         {
            case 0:
               _loc5_ = int(dataXML.PetOddsUpgrade.@oddValue) / 100;
               break;
            case 1:
               _loc5_ = Number(dataXML.PetOddsEvolution.item.(@level == param1.level)[0].@oddValue) / 100;
               break;
            case 2:
               _loc5_ = param3;
         }
         if(_loc16_ <= _loc5_)
         {
            _loc19_ = skillXML.item.(@type == "petPassive" && @level == param1.level);
            _loc8_ = int(_loc19_.length());
            _loc7_ = [];
            _loc11_ = 0;
            while(_loc11_ < _loc8_)
            {
               _loc6_ = String(_loc19_[_loc11_].@useSeriesType).split(",");
               if(_loc6_.indexOf(param1.petSeries) != -1)
               {
                  _loc7_.push(_loc19_[_loc11_]);
               }
               _loc11_++;
            }
            _loc15_ = int(_loc7_.length);
            _loc17_ = int(param1.passiveSkillVOs.length);
            _loc4_ = false;
            _loc13_ = 0;
            while(_loc13_ < _loc15_)
            {
               _loc4_ = false;
               _loc10_ = 0;
               while(_loc10_ < _loc17_)
               {
                  if(_loc7_[_loc13_].@id == param1.passiveSkillVOs[_loc10_].id)
                  {
                     _loc4_ = true;
                     break;
                  }
                  _loc10_++;
               }
               if(!_loc4_)
               {
                  _loc12_.push(XMLSingle.getSkill(_loc7_[_loc13_].@id,skillXML));
               }
               _loc13_++;
            }
            _loc9_ = int(_loc12_.length);
            _loc14_ = Math.floor(Math.random() * _loc9_);
            _loc18_ = _loc12_[_loc14_];
            if(param2 == 0 || param2 == 1)
            {
               if(param1.passiveSkillVOs.length < 3)
               {
                  param1.passiveSkillVOs.push(_loc18_);
                  return _loc18_;
               }
            }
            else if(param1.passiveSkillVOs.length < 5 || (UIConstantData.ZHULONG_ID_ARRAY.indexOf(param1.id) != -1 || UIConstantData.QIONGQI_ID_ARRAY.indexOf(param1.id) != -1) && param1.passiveSkillVOs.length == 5)
            {
               param1.passiveSkillVOs.push(_loc18_);
               return _loc18_;
            }
         }
         return null;
      }
      
      public function randomResetOnePassiveSkillVO(param1:PetEquipmentVO, param2:int) : SkillVO
      {
         var _loc14_:SkillVO = null;
         var _loc9_:int = 0;
         var _loc16_:* = null;
         var _loc4_:Array = null;
         var _loc11_:int = 0;
         var _loc8_:int = 0;
         var _loc10_:Array = [];
         _loc16_ = skillXML.item.(@type == "petPassive" && @level == param1.level);
         var _loc6_:int = int(_loc16_.length());
         var _loc5_:Array = [];
         _loc9_ = 0;
         while(_loc9_ < _loc6_)
         {
            _loc4_ = String(_loc16_[_loc9_].@useSeriesType).split(",");
            if(_loc4_.indexOf(param1.petSeries) != -1)
            {
               _loc5_.push(_loc16_[_loc9_]);
            }
            _loc9_++;
         }
         var _loc13_:int = int(_loc5_.length);
         var _loc15_:int = int(param1.passiveSkillVOs.length);
         var _loc3_:Boolean = false;
         _loc11_ = 0;
         while(_loc11_ < _loc13_)
         {
            _loc3_ = false;
            _loc8_ = 0;
            while(_loc8_ < _loc15_)
            {
               if(_loc5_[_loc11_].@id == param1.passiveSkillVOs[_loc8_].id && _loc8_ != param2)
               {
                  _loc3_ = true;
                  break;
               }
               _loc8_++;
            }
            if(!_loc3_)
            {
               _loc10_.push(XMLSingle.getSkill(_loc5_[_loc11_].@id,skillXML));
            }
            _loc11_++;
         }
         var _loc7_:int = int(_loc10_.length);
         var _loc12_:Number = Math.floor(Math.random() * _loc7_);
         _loc14_ = _loc10_[_loc12_];
         if((UIConstantData.ZHULONG_ID_ARRAY.indexOf(param1.id) != -1 || UIConstantData.QIONGQI_ID_ARRAY.indexOf(param1.id) != -1) && param1.passiveSkillVOs.length == 6)
         {
            param1.passiveSkillVOs[param2] = _loc14_;
            return _loc14_;
         }
         return null;
      }
      
      public function randomUpgradeTalent(param1:PetEquipmentVO, param2:int, param3:Number = 0) : Boolean
      {
         var _loc5_:* = NaN;
         var _loc7_:int = 0;
         var _loc4_:TalentVO = null;
         var _loc6_:Number = Math.random();
         switch(param2)
         {
            case 0:
               _loc5_ = int(dataXML.PetOddsUpgrade.@oddUpgradeTalent) / 100;
               break;
            case 1:
               _loc5_ = Number(dataXML.PetOddsEvolution.item.(@level == param1.level)[0].@oddUpgradeTalent) / 100;
               break;
            case 2:
               _loc5_ = param3;
               break;
            default:
               throw new Error("提供的option参数不符合要求！");
         }
         if(_loc6_ <= _loc5_ && param1.talentVO.level < param1.talentVO.maxLevel)
         {
            _loc7_ = MyFunction.getInstance().calculateNextLevelID(param1.talentVO.id);
            _loc4_ = XMLSingle.getTalentVO(_loc7_,talentXML);
            param1.talentVO = _loc4_;
            return true;
         }
         return false;
      }
      
      public function randomGetTalentVO(param1:int, param2:int = 1) : TalentVO
      {
         var _loc6_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc7_:XML = null;
         var _loc3_:TalentVO = null;
         var _loc5_:Number = Math.random();
         var _loc8_:Number = Math.random();
         switch(param1)
         {
            case 0:
               _loc6_ = 1;
               _loc4_ = 0;
               _loc9_ = 0;
               break;
            case 1:
               _loc7_ = dataXML;
               _loc6_ = int(_loc7_.PetOddsTalentInNormalHatch.@oneTalent) / 100;
               _loc4_ = int(_loc7_.PetOddsTalentInNormalHatch.@twoTalent) / 100;
               _loc9_ = int(_loc7_.PetOddsTalentInNormalHatch.@threeTalent) / 100;
               break;
            case 2:
               _loc7_ = dataXML;
               _loc6_ = int(_loc7_.PetOddsTalentInFineHatch.@oneTalent) / 100;
               _loc4_ = int(_loc7_.PetOddsTalentInFineHatch.@twoTalent) / 100;
               _loc9_ = int(_loc7_.PetOddsTalentInFineHatch.@threeTalent) / 100;
               break;
            case 3:
               switch(param2 - 1)
               {
                  case 0:
                     _loc6_ = 1;
                     _loc4_ = -1;
                     _loc9_ = -1;
                     break;
                  case 1:
                     _loc6_ = 0;
                     _loc4_ = 1;
                     _loc9_ = -1;
                     break;
                  case 2:
                     _loc6_ = 0;
                     _loc4_ = 0;
                     _loc9_ = 1;
                     break;
                  default:
                     throw new Error("不存在的宠物等级!");
               }
               break;
            default:
               throw new Error("提供的option参数不符合要求！");
         }
         if(_loc5_ <= 0.3333333333333333)
         {
            if(_loc8_ <= _loc6_)
            {
               _loc3_ = getTalentVO(10103100,talentXML);
            }
            else if(_loc8_ > _loc6_ && _loc8_ <= _loc6_ + _loc4_)
            {
               _loc3_ = getTalentVO(10103200,talentXML);
            }
            else
            {
               _loc3_ = getTalentVO(10103300,talentXML);
            }
         }
         else if(_loc5_ > 0.3333333333333333 && _loc5_ <= 0.6666666666666666)
         {
            if(_loc8_ <= _loc6_)
            {
               _loc3_ = getTalentVO(10203100,talentXML);
            }
            else if(_loc8_ > _loc6_ && _loc8_ <= _loc6_ + _loc4_)
            {
               _loc3_ = getTalentVO(10203200,talentXML);
            }
            else
            {
               _loc3_ = getTalentVO(10203300,talentXML);
            }
         }
         else if(_loc8_ <= _loc6_)
         {
            _loc3_ = getTalentVO(10303100,talentXML);
         }
         else if(_loc8_ > _loc6_ && _loc8_ <= _loc6_ + _loc4_)
         {
            _loc3_ = getTalentVO(10303200,talentXML);
         }
         else
         {
            _loc3_ = getTalentVO(10303300,talentXML);
         }
         return _loc3_;
      }
      
      public function calculationAllAddValueInUpgradeEquipmentFun(param1:XML) : Function
      {
         var xml:XML = param1;
         var equipXML:XML = xml;
         return (function():*
         {
            var allAddValue:Function;
            return allAddValue = function(param1:AbleEquipmentVO, param2:String = "upgradeValue"):Number
            {
               var _loc4_:String = param1.id.toString().substr(0,1 + 2 + 2);
               var _loc3_:String = param1.id.toString().substring(1 + 2 + 2 + 1);
               var _loc7_:* = equipXML.item.(String(@id).match("^" + _loc4_ + "\\d{" + 1 + "}" + _loc3_ + "$"));
               var _loc5_:Number = 0;
               for each(var _loc6_ in _loc7_)
               {
                  _loc5_ += Number(_loc6_[param2]);
               }
               return _loc5_;
            };
         })();
      }
      
      public function returnLockCellTicket(param1:int) : int
      {
         switch(param1)
         {
            case 0:
               return int(dataXML.LockCellPrice.@packageTicket);
            case 1:
               return int(dataXML.LockCellPrice.@storageTicket);
            case 2:
               return int(dataXML.LockCellPrice.@publicStorageTicket);
            default:
               throw new Error("提供的参数不符合要求！");
         }
      }
      
      public function returnLockCellTicketId(param1:int) : String
      {
         switch(param1)
         {
            case 0:
               return String(dataXML.LockCellPrice.@packageTicketId);
            case 1:
               return String(dataXML.LockCellPrice.@storageTicketId);
            case 2:
               return String(dataXML.LockCellPrice.@publicStorageTicketId);
            default:
               throw new Error("提供的参数不符合要求！");
         }
      }
      
      public function createTaskGoalByStr(param1:String) : TaskGoalVO
      {
         var _loc2_:* = null;
         var _loc3_:XML = taskXML.TaskGoal.item.(@str == param1)[0];
         if(_loc3_)
         {
            return XMLSingle.getTaskGoal(int(_loc3_.@id),taskXML);
         }
         return null;
      }
      
      public function get everyDayTaskMaxNum() : int
      {
         return int(dataXML.TaskLimit.@everyDayTaskNum);
      }
      
      public function get activityTaskMaxNum() : int
      {
         return int(dataXML.TaskLimit.@activityTaskNum);
      }
      
      public function get maxMoney() : int
      {
         return int(dataXML.Money.@maxMoney);
      }
      
      public function get maxDxValue_Money() : int
      {
         return int(dataXML.Money.@maxDxValue);
      }
      
      public function get maxPKPoint() : int
      {
         return int(dataXML.PKPoint.@maxPKPoint);
      }
      
      public function get maxDxValue_PKPoint() : int
      {
         return int(dataXML.PKPoint.@maxDxValue);
      }
      
      public function get maxBuyNumInPKShop() : int
      {
         return int(dataXML.PKShop.@maxBuyNum);
      }
      
      public function getPetPotionUseRestrictionXML() : XML
      {
         return dataXML.PetPotionUseRestriction[0];
      }
      
      public function getPlayerMaxLevel(param1:Player) : int
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               return int(monkeyXML.levelLimit.@maxLevel);
            case "BaiLongMa":
               return int(dragonXML.levelLimit.@maxLevel);
            case "ErLangShen":
               return int(erLangShenXML.levelLimit.@maxLevel);
            case "ChangE":
               return int(changEXML.levelLimit.@maxLevel);
            case "Fox":
               return int(foxXML.levelLimit.@maxLevel);
            case "TieShan":
               return int(tieShanXML.levelLimit.@maxLevel);
            case "Houyi":
               return int(houyiXML.levelLimit.@maxLevel);
            case "ZiXia":
               return int(ziXiaXML.levelLimit.@maxLevel);
            default:
               throw new Error("错误!");
         }
      }
      
      public function getPlayerMaxShengling(param1:Player) : int
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               return int(monkeyXML.levelLimit.@shenglingLevel);
            case "BaiLongMa":
               return int(dragonXML.levelLimit.@shenglingLevel);
            case "ErLangShen":
               return int(erLangShenXML.levelLimit.@shenglingLevel);
            case "ChangE":
               return int(changEXML.levelLimit.@shenglingLevel);
            case "Fox":
               return int(foxXML.levelLimit.@shenglingLevel);
            case "TieShan":
               return int(tieShanXML.levelLimit.@shenglingLevel);
            case "Houyi":
               return int(houyiXML.levelLimit.@shenglingLevel);
            case "ZiXia":
               return int(ziXiaXML.levelLimit.@shenglingLevel);
            default:
               throw new Error("错误!");
         }
      }
      
      public function getPlayerTuPoMaxLevel(param1:Player) : int
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               return int(monkeyXML.levelLimit.@tuPoMaxLevel);
            case "BaiLongMa":
               return int(dragonXML.levelLimit.@tuPoMaxLevel);
            case "ErLangShen":
               return int(erLangShenXML.levelLimit.@tuPoMaxLevel);
            case "ChangE":
               return int(changEXML.levelLimit.@tuPoMaxLevel);
            case "Fox":
               return int(foxXML.levelLimit.@tuPoMaxLevel);
            case "TieShan":
               return int(tieShanXML.levelLimit.@tuPoMaxLevel);
            case "Houyi":
               return int(houyiXML.levelLimit.@tuPoMaxLevel);
            case "ZiXia":
               return int(ziXiaXML.levelLimit.@tuPoMaxLevel);
            default:
               throw new Error("错误!");
         }
      }
      
      public function getPressKey_One(param1:XML) : Vector.<String>
      {
         var _loc3_:XMLList = param1.Key[0].Key[0].item;
         var _loc2_:Vector.<String> = new Vector.<String>();
         for each(param1 in _loc3_)
         {
            _loc2_.push(String(param1.@key));
         }
         return _loc2_;
      }
      
      public function getPressKey_Two(param1:XML) : Vector.<String>
      {
         var _loc3_:XMLList = param1.Key[0].Key[1].item;
         var _loc2_:Vector.<String> = new Vector.<String>();
         for each(param1 in _loc3_)
         {
            _loc2_.push(String(param1.@key));
         }
         return _loc2_;
      }
      
      public function getPackageCellNum() : int
      {
         return int(dataXML.CellNum[0]["package"][0].@num);
      }
      
      public function getStorageCellNum() : int
      {
         return int(dataXML.CellNum[0].storage[0].@num);
      }
      
      public function getPublicStorageCellNum() : int
      {
         return int(dataXML.CellNum[0].publicStorage[0].@num);
      }
      
      public function getEqupipmentXMLPartData() : EquipmentXMLPartData
      {
         return m_equipmentXMLPartData;
      }
   }
}

