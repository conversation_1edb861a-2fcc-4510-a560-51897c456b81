package UI.newGuide
{
   import flash.display.MovieClip;
   
   public class NewGuideData
   {
      
      public static var _instance:NewGuideData = null;
      
      public var isLoad:Boolean = false;
      
      public var isShow:Boolean = true;
      
      public var mc:MovieClip;
      
      public function NewGuideData()
      {
         super();
      }
      
      public static function getInstance() : NewGuideData
      {
         if(!_instance)
         {
            _instance = new NewGuideData();
         }
         return _instance;
      }
   }
}

