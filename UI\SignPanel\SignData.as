package UI.SignPanel
{
   import UI.DataManagerParent;
   
   public class SignData extends DataManagerParent
   {
      
      private static var _instance:SignData = null;
      
      public var maxSignNum:int = 0;
      
      private var _currentSignNum:int;
      
      private var _currentSignDate:String;
      
      private var _getSignAwardDate:String;
      
      public function SignData()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : SignData
      {
         if(!_instance)
         {
            _instance = new SignData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         _currentSignDate = null;
         _getSignAwardDate = null;
         _instance = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currentSignNum = _currentSignNum;
         _antiwear.currentSignDate = _currentSignDate;
         _antiwear.getSignAwardDate = _getSignAwardDate;
      }
      
      public function get currentSignNum() : int
      {
         return Math.min(_antiwear.currentSignNum,maxSignNum);
      }
      
      public function set currentSignNum(param1:int) : void
      {
         _antiwear.currentSignNum = param1;
      }
      
      public function get currentSignDate() : String
      {
         return _antiwear.currentSignDate;
      }
      
      public function set currentSignDate(param1:String) : void
      {
         _antiwear.currentSignDate = param1;
      }
      
      public function get getSignAwardDate() : String
      {
         return _antiwear.getSignAwardDate;
      }
      
      public function set getSignAwardDate(param1:String) : void
      {
         _antiwear.getSignAwardDate = param1;
      }
   }
}

