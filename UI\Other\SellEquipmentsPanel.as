package UI.Other
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.PlayerVO;
   import UI.Shop.GetEquipmentSellMoney;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.CheckBox.CheckBoxLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ShowFunction;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SellEquipmentsPanel extends MySprite
   {
      
      private var m_show:Sprite;
      
      private var m_quiBtn:ButtonLogicShell2;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_cancelBtn:ButtonLogicShell2;
      
      private var m_selectAllBtn:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_eqChoiceCheckBoxs:Vector.<CheckBoxLogicShell>;
      
      private var m_eqCellBorders:Vector.<MovieClipPlayLogicShell>;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_packageEquipmentVOs:Vector.<EquipmentVO>;
      
      private var m_choiceEquipmentVOs:Vector.<EquipmentVO>;
      
      private var m_playerVO:PlayerVO;
      
      private var m_myControlPanel:MyControlPanel;
      
      public function SellEquipmentsPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
         m_quiBtn = new ButtonLogicShell2();
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_sureBtn = new ButtonLogicShell2();
         m_cancelBtn = new ButtonLogicShell2();
         m_selectAllBtn = new ButtonLogicShell2();
         m_eqChoiceCheckBoxs = new Vector.<CheckBoxLogicShell>();
         m_eqCellBorders = new Vector.<MovieClipPlayLogicShell>();
         m_equipments = new Vector.<Equipment>();
         m_choiceEquipmentVOs = new Vector.<EquipmentVO>();
         m_packageEquipmentVOs = new Vector.<EquipmentVO>();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quiBtn);
         m_quiBtn = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_selectAllBtn);
         m_selectAllBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_eqChoiceCheckBoxs);
         m_eqChoiceCheckBoxs = null;
         ClearUtil.clearObject(m_eqCellBorders);
         m_eqCellBorders = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.nullArr(m_choiceEquipmentVOs,false,false,false);
         m_choiceEquipmentVOs = null;
         ClearUtil.nullArr(m_packageEquipmentVOs,false,false,false);
         m_packageEquipmentVOs = null;
         m_playerVO = null;
         m_myControlPanel = null;
         super.clear();
      }
      
      public function init(param1:PlayerVO, param2:MyControlPanel) : void
      {
         m_playerVO = param1;
         m_myControlPanel = param2;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(Part1.getInstance().getLoadUI());
         m_myLoader.setVersionControl(Part1.getInstance().getVersionControl());
         Part1.getInstance().getLoadUI().tranToTransparentcy();
         m_myLoader.getClass("UseEquipmentPanel.swf","SellEquipmentsPanel",getShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         m_myControlPanel.showWarning("加载素材失败",0);
         m_myControlPanel.closeSellEquipmentsPanel();
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc3_:CheckBoxLogicShell = null;
         var _loc2_:MovieClipPlayLogicShell = null;
         m_quiBtn.setShow(m_show["quitBtn"]);
         m_sureBtn.setShow(m_show["sureBtn"]);
         m_cancelBtn.setShow(m_show["cancelBtn"]);
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_selectAllBtn.setShow(m_show["selectAllbtn"]);
         var _loc1_:uint = ShowFunction.getDisplayObjectNumByNameInShow(m_show,"eqChoiceCheckBox_");
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = new CheckBoxLogicShell();
            _loc3_.setShow(m_show["eqChoiceCheckBox_" + (_loc4_ + 1)]);
            m_eqChoiceCheckBoxs.push(_loc3_);
            _loc2_ = new MovieClipPlayLogicShell();
            _loc2_.setShow(_loc3_.getShow()["btn"]);
            m_eqCellBorders.push(_loc2_);
            _loc4_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc2_:int = 0;
         ClearUtil.nullArr(m_choiceEquipmentVOs,false,false,false);
         m_choiceEquipmentVOs.length = 0;
         ClearUtil.nullArr(m_packageEquipmentVOs,false,false,false);
         m_packageEquipmentVOs.length = 0;
         var _loc1_:int = int(m_playerVO.packageEquipmentVOs.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_playerVO.packageEquipmentVOs[_loc2_])
            {
               m_packageEquipmentVOs.push(m_playerVO.packageEquipmentVOs[_loc2_]);
            }
            _loc2_++;
         }
         setPageBtn(1);
         arrangeEqShow((m_pageBtnGroup.pageNum - 1) * m_eqChoiceCheckBoxs.length);
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_packageEquipmentVOs == null || m_packageEquipmentVOs.length == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_packageEquipmentVOs.length % m_eqChoiceCheckBoxs.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_packageEquipmentVOs.length / m_eqChoiceCheckBoxs.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_packageEquipmentVOs.length / m_eqChoiceCheckBoxs.length) + 1);
         }
      }
      
      private function arrangeEqShow(param1:int) : void
      {
         var _loc7_:* = 0;
         var _loc2_:EquipmentVO = null;
         var _loc3_:Equipment = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments.length = 0;
         var _loc6_:int = param1 + m_eqChoiceCheckBoxs.length;
         var _loc4_:int = m_packageEquipmentVOs ? m_packageEquipmentVOs.length : 0;
         var _loc5_:int = 0;
         _loc7_ = param1;
         while(_loc7_ < _loc6_ && _loc7_ < _loc4_)
         {
            _loc2_ = m_packageEquipmentVOs[_loc7_];
            if(_loc2_)
            {
               _loc3_ = MyFunction2.sheatheEquipmentShell(_loc2_);
               _loc3_.addEventListener("rollOver",onOver2,false,0,true);
               _loc3_.addEventListener("rollOut",onOut2,false,0,true);
               m_equipments.push(_loc3_);
               _loc3_.setExtra(m_eqCellBorders[_loc5_]);
               (m_eqChoiceCheckBoxs[_loc5_].getShow()["eqCell"] as DisplayObjectContainer).addChild(_loc3_);
               m_eqCellBorders[_loc5_].setExtra(_loc3_);
            }
            else
            {
               m_eqCellBorders[_loc5_].setExtra(null);
            }
            m_eqChoiceCheckBoxs[_loc5_].setExtra(_loc2_);
            _loc5_++;
            _loc7_++;
         }
         while(_loc5_ < m_eqChoiceCheckBoxs.length)
         {
            m_eqChoiceCheckBoxs[_loc5_].setExtra(null);
            m_eqCellBorders[_loc5_].setExtra(null);
            _loc5_++;
         }
         initShow3();
      }
      
      private function initShow3() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = int(m_choiceEquipmentVOs.length);
         var _loc2_:int = int(m_eqChoiceCheckBoxs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_eqChoiceCheckBoxs[_loc3_].isCheck = false;
            _loc3_++;
         }
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(m_eqChoiceCheckBoxs[_loc3_].getExtra() == m_choiceEquipmentVOs[_loc4_])
               {
                  m_eqChoiceCheckBoxs[_loc3_].isCheck = true;
               }
               _loc3_++;
            }
            _loc4_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         switch(param1.button)
         {
            case m_quiBtn:
            case m_cancelBtn:
            case m_sureBtn:
               break;
            case m_pageBtnGroup:
               arrangeEqShow((m_pageBtnGroup.pageNum - 1) * m_eqChoiceCheckBoxs.length);
               return;
            case m_selectAllBtn:
               selectAllEquipments();
               var _loc3_:int = m_eqChoiceCheckBoxs ? m_eqChoiceCheckBoxs.length : 0;
               _loc4_ = 0;
               §§goto(addr57);
            default:
               addr57:
               while(_loc4_ < _loc3_)
               {
                  if(param1.button == m_eqChoiceCheckBoxs[_loc4_])
                  {
                     if(m_eqChoiceCheckBoxs[_loc4_].isCheck == false)
                     {
                        _loc2_ = int(m_choiceEquipmentVOs.indexOf(m_eqChoiceCheckBoxs[_loc4_].getExtra() as EquipmentVO));
                        m_choiceEquipmentVOs.splice(_loc2_,1);
                     }
                     else if(m_eqChoiceCheckBoxs[_loc4_].getExtra())
                     {
                        m_choiceEquipmentVOs.push(m_eqChoiceCheckBoxs[_loc4_].getExtra());
                     }
                     else
                     {
                        m_eqChoiceCheckBoxs[_loc4_].isCheck = false;
                     }
                  }
                  _loc4_++;
               }
               return;
         }
         if(param1.button == m_sureBtn)
         {
            sellChoiceEquipmentVOs();
         }
         m_myControlPanel.closeSellEquipmentsPanel();
      }
      
      private function selectAllEquipments() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = m_eqChoiceCheckBoxs ? m_eqChoiceCheckBoxs.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_choiceEquipmentVOs.indexOf(m_eqChoiceCheckBoxs[_loc3_].getExtra() as EquipmentVO) == -1)
            {
               if(m_eqChoiceCheckBoxs[_loc3_].getExtra())
               {
                  m_eqChoiceCheckBoxs[_loc3_].isCheck = true;
                  m_choiceEquipmentVOs.push(m_eqChoiceCheckBoxs[_loc3_].getExtra());
               }
               else
               {
                  m_eqChoiceCheckBoxs[_loc3_].isCheck = false;
               }
            }
            _loc3_++;
         }
      }
      
      private function sellChoiceEquipmentVOs() : void
      {
         var _loc6_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:* = 0;
         var _loc2_:int = 0;
         var _loc3_:int = int(m_choiceEquipmentVOs.length);
         var _loc5_:GetEquipmentSellMoney = new GetEquipmentSellMoney();
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc2_ = _loc5_.getEquipmentSellMoney(m_choiceEquipmentVOs[_loc6_]);
            m_playerVO.money += _loc2_;
            _loc4_ += _loc2_;
            _loc1_ = int(m_playerVO.packageEquipmentVOs.indexOf(m_choiceEquipmentVOs[_loc6_]));
            m_playerVO.packageEquipmentVOs[_loc1_] = null;
            ClearUtil.clearObject(m_choiceEquipmentVOs[_loc6_]);
            m_choiceEquipmentVOs[_loc6_] = null;
            _loc6_++;
         }
         ClearUtil.clearObject(_loc5_);
         _loc5_ = null;
         if(_loc4_)
         {
            m_myControlPanel.showWarning("成功批量出售物品，共获得" + _loc4_ + "元宝.",0);
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         var _loc2_:MovieClipPlayLogicShell = (param1.currentTarget as Equipment).getExtra() as MovieClipPlayLogicShell;
         if(_loc2_)
         {
            _loc2_.gotoAndStop("2");
         }
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":0,
            "player":m_playerVO
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         var _loc2_:MovieClipPlayLogicShell = (param1.currentTarget as Equipment).getExtra() as MovieClipPlayLogicShell;
         if(_loc2_)
         {
            _loc2_.gotoAndStop("1");
         }
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

