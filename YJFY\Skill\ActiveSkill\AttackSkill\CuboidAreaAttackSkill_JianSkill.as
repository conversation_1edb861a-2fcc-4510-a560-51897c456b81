package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Utils.ClearUtil;
   
   public class CuboidAreaAttackSkill_JianSkill extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      private var m_disappearBodyId:String;
      
      private var m_releaseSkillFrameLabel:String;
      
      private var m_skillAttackReachFrameLabel:String;
      
      private var m_replaceFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_jianSkillListeners:Vector.<IJianSkillListener>;
      
      public function CuboidAreaAttackSkill_JianSkill()
      {
         super();
         m_isAttackReachWhenRelease = false;
         m_jianSkillListeners = new Vector.<IJianSkillListener>();
      }
      
      override public function clear() : void
      {
         m_disappearBodyId = null;
         m_releaseSkillFrameLabel = null;
         m_skillAttackReachFrameLabel = null;
         m_replaceFrameLabel = null;
         m_skillEndFrameLabel = null;
         ClearUtil.nullArr(m_jianSkillListeners,false,false,false);
         m_jianSkillListeners = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_disappearBodyId = String(param1.@disappearBodyId);
         m_releaseSkillFrameLabel = String(param1.@releaseSkillFrameLabel);
         m_skillAttackReachFrameLabel = String(param1.@skillAttackReachFrameLabel);
         m_replaceFrameLabel = String(param1.@replaceFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
      }
      
      public function addJianSkillListener(param1:IJianSkillListener) : void
      {
         m_jianSkillListeners.push(param1);
      }
      
      public function removeJianSkillListener(param1:IJianSkillListener) : void
      {
         var _loc2_:int = int(m_jianSkillListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_jianSkillListeners.splice(_loc2_,1);
            _loc2_ = int(m_jianSkillListeners.indexOf(param1));
         }
      }
      
      protected function playDisappear() : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_disappearBodyId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      override protected function endSkill2() : void
      {
         recover();
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case m_releaseSkillFrameLabel:
               releaseSkill2(m_world);
               break;
            case m_skillAttackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_replaceFrameLabel:
               replace();
               break;
            case m_skillEndFrameLabel:
               endSkill1();
         }
      }
      
      private function replace() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<IJianSkillListener> = m_jianSkillListeners.slice(0);
         var _loc1_:int = int(_loc2_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_[_loc3_].replace(this,m_owner);
            _loc3_++;
         }
      }
      
      private function recover() : void
      {
         var _loc3_:int = 0;
         var _loc2_:Vector.<IJianSkillListener> = m_jianSkillListeners.slice(0);
         var _loc1_:int = int(_loc2_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc2_[_loc3_].recover(this,m_owner);
            _loc3_++;
         }
      }
   }
}

