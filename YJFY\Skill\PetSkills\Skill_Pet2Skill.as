package YJFY.Skill.PetSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.World.World;
   
   public class Skill_Pet2Skill extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      public function Skill_Pet2Skill()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
   }
}

