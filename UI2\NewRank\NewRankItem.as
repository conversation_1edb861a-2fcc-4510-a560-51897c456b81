package UI2.NewRank
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class NewRankItem
   {
      
      public var maxrank:int;
      
      public var minrank:int;
      
      public var gradeid:int;
      
      public var role:String;
      
      public var itemlist:Vector.<EquipmentVO>;
      
      public function NewRankItem()
      {
         super();
         itemlist = new Vector.<EquipmentVO>();
      }
      
      public function addItem(param1:RewardItem) : void
      {
         itemlist.push(param1);
      }
   }
}

