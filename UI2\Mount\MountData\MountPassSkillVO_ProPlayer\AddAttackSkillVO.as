package UI2.Mount.MountData.MountPassSkillVO_ProPlayer
{
   public class AddAttackSkillVO extends MountPassSkillVO_ProPlayer
   {
      
      private var m_addAttack:uint;
      
      public function AddAttackSkillVO()
      {
         super();
      }
      
      override protected function recoverPlayerData() : void
      {
         super.recoverPlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_targetPlayer.playerVO.set2("attack_mountAdd1",m_targetPlayer.playerVO.get2("attack_mountAdd1") - m_addValue);
         m_addValue = 0;
      }
      
      override protected function changePlayerData() : void
      {
         super.changePlayerData();
         if(m_targetPlayer == null)
         {
            return;
         }
         m_addValue = addAttack;
         m_targetPlayer.playerVO.set2("attack_mountAdd1",m_targetPlayer.playerVO.get2("attack_mountAdd1") + m_addValue);
      }
      
      public function getAddAttack() : uint
      {
         return addAttack;
      }
      
      override protected function initSkillLevelData(param1:XML) : void
      {
         this.addAttack = uint(param1.data.(@att == "addAttack")[0].@value);
         super.initSkillLevelData(param1);
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addAttack = m_addAttack;
      }
      
      private function get addAttack() : uint
      {
         return _antiwear.addAttack;
      }
      
      private function set addAttack(param1:uint) : void
      {
         _antiwear.addAttack = param1;
      }
   }
}

