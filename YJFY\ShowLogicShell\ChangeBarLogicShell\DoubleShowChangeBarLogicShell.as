package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class DoubleShowChangeBarLogicShell
   {
      
      private var m_upBar:CMSXChangeBarLogicShell;
      
      private var m_down_upShow:Sprite;
      
      private var m_down_mask:Sprite;
      
      private var m_changeDelay:uint;
      
      private var m_toPercents:Vector.<Number>;
      
      private var m_toTimes:Vector.<Number>;
      
      private var m_currentPercent:Number;
      
      private var m_show:MovieClip;
      
      private var m_elapsedTimeData:IElapsedTimeData;
      
      private var m_upLength:Number;
      
      private var m_downEndPercent:Number;
      
      public function DoubleShowChangeBarLogicShell()
      {
         super();
         m_upBar = new CMSXChangeBarLogicShell();
         m_toPercents = new Vector.<Number>();
         m_toTimes = new Vector.<Number>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_upBar);
         m_upBar = null;
         ClearUtil.clearObject(m_toPercents);
         m_toPercents = null;
         ClearUtil.clearObject(m_toTimes);
         m_toTimes = null;
         m_show = null;
         m_elapsedTimeData = null;
      }
      
      public function init(param1:MovieClip, param2:IElapsedTimeData, param3:uint) : void
      {
         m_show = param1;
         m_elapsedTimeData = param2;
         m_changeDelay = param3;
         initShow();
      }
      
      public function render() : void
      {
         if(m_toPercents.length)
         {
            if(m_elapsedTimeData.getElapsedTime() > m_toTimes[0])
            {
               drawDownMask(m_currentPercent,m_toPercents[0]);
               m_toPercents.splice(0,1);
               m_toTimes.splice(0,1);
            }
         }
      }
      
      public function change(param1:Number) : void
      {
         param1 = Math.max(0,Math.min(1,param1));
         if(param1 > m_currentPercent || m_toPercents.length && param1 > m_toPercents[m_toPercents.length - 1])
         {
            m_toPercents.length = 0;
            m_toTimes.length = 0;
            drawDownMask(param1,param1);
         }
         else
         {
            m_toPercents.push(param1);
            m_toTimes.push(m_elapsedTimeData.getElapsedTime() + m_changeDelay);
         }
         m_upBar.change(param1);
         drawDownMask(param1,m_downEndPercent);
         m_currentPercent = param1;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow() : void
      {
         m_upBar.setShow(m_show["upBar"]);
         m_down_upShow = m_show["downShow"];
         m_down_mask = new Sprite();
         m_show.addChild(m_down_mask);
         m_down_upShow.mask = m_down_mask;
         m_upLength = m_upBar.getShow().width;
         drawDownMask(0,1);
      }
      
      private function drawDownMask(param1:Number, param2:Number) : void
      {
         m_down_mask.graphics.clear();
         m_down_mask.graphics.beginFill(0,1);
         m_down_mask.graphics.drawRect(0,0,(param2 - param1) * m_upLength,m_down_upShow.height);
         m_down_mask.graphics.endFill();
         m_down_mask.x = m_upBar.getShow().x + param1 * m_upLength;
         m_down_mask.y = m_down_upShow.y;
         m_down_upShow.mask = m_down_mask;
         m_downEndPercent = param2;
      }
   }
}

