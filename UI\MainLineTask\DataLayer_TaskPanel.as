package UI.MainLineTask
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.SceneEquipment;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarsLogicShell;
   import UI.MainLineTask.TaskDescription.TaskDescription1;
   import UI.MainLineTask.TaskDescription.TaskDescription2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class DataLayer_TaskPanel
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell2;
      
      private var m_taskBar:CMSXChangeBarLogicShell;
      
      private var m_taskLevelShow:CMSXChangeBarsLogicShell;
      
      private var m_taskFinishConditionText:TextField;
      
      private var m_enemyNameText:TextField;
      
      private var m_mapNameText:TextField;
      
      private var m_bossDropOutContainer:MovieClipPlayLogicShell;
      
      private var m_enemyDropOutContainer:MovieClipPlayLogicShell;
      
      private var m_npcNameText:TextField;
      
      private var m_npcFunctionDesText:TextField;
      
      private var m_taskVO:MainLineTaskVO;
      
      private var m_eqCs:Vector.<Sprite>;
      
      private var m_enemyDropOutEqShows:Array;
      
      private var m_bossDropOutEqShows:Array;
      
      public function DataLayer_TaskPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.nullObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_taskBar);
         m_taskBar = null;
         ClearUtil.clearObject(m_taskLevelShow);
         m_taskLevelShow = null;
         ClearUtil.clearObject(m_taskFinishConditionText);
         m_taskFinishConditionText = null;
         m_enemyNameText = null;
         m_mapNameText = null;
         ClearUtil.clearObject(m_bossDropOutContainer);
         m_bossDropOutContainer = null;
         ClearUtil.clearObject(m_enemyDropOutContainer);
         m_enemyDropOutContainer = null;
         m_npcNameText = null;
         m_npcFunctionDesText = null;
         m_taskVO = null;
         ClearUtil.nullArr(m_eqCs);
         m_eqCs = null;
         ClearUtil.nullArr(m_enemyDropOutEqShows);
         m_enemyDropOutEqShows = null;
         ClearUtil.nullArr(m_bossDropOutEqShows);
         m_bossDropOutEqShows = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMC = new MovieClipPlayLogicShell2();
         m_showMC.setShow(m_show);
      }
      
      public function setTaskVO(param1:MainLineTaskVO) : void
      {
         m_taskVO = param1;
         initShow(m_taskVO);
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow(param1:MainLineTaskVO) : void
      {
         var _loc4_:TaskDescription1 = null;
         var _loc11_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:* = undefined;
         var _loc2_:Sprite = null;
         var _loc5_:Equipment = null;
         var _loc8_:Sprite = null;
         var _loc7_:TaskDescription2 = null;
         clearFrameShow();
         ClearUtil.nullArr(m_eqCs);
         ClearUtil.nullArr(m_enemyDropOutEqShows);
         ClearUtil.nullArr(m_bossDropOutEqShows);
         switch(param1.description.type)
         {
            case "taskDescription1":
               initFrame1();
               _loc4_ = param1.description as TaskDescription1;
               m_enemyNameText.text = _loc4_.enemyName;
               m_mapNameText.text = _loc4_.mapName;
               _loc6_ = _loc4_.dropOutEquipmentVOs_enemy;
               _loc3_ = _loc6_ ? _loc6_.length : 0;
               _loc2_ = m_enemyDropOutContainer.getShow()["container"];
               m_enemyDropOutContainer.getShow().y = m_taskFinishConditionText.y + m_taskFinishConditionText.textHeight + 30;
               m_enemyDropOutEqShows = [];
               _loc11_ = 0;
               while(_loc11_ < _loc3_)
               {
                  _loc5_ = MyFunction2.sheatheEquipmentShell(_loc6_[_loc11_]);
                  _loc5_.addEventListener("rollOver",onOver,false,0,true);
                  _loc5_.addEventListener("rollOut",onOut,false,0,true);
                  _loc8_ = MyFunction2.returnShowByClassName("EqC") as Sprite;
                  _loc8_.addChild(_loc5_);
                  if(m_eqCs == null)
                  {
                     m_eqCs = new Vector.<Sprite>();
                  }
                  m_eqCs.push(_loc8_);
                  _loc8_.x = 30 + _loc11_ % 5 * 50;
                  _loc8_.y = 30 + int(_loc11_ / 5) * 50;
                  _loc2_.addChild(_loc8_);
                  m_enemyDropOutEqShows.push(_loc5_);
                  _loc11_++;
               }
               _loc6_ = _loc4_.dropOutEquipmentVOs_boss;
               _loc2_ = m_bossDropOutContainer.getShow()["container"];
               m_bossDropOutContainer.getShow().y = m_enemyDropOutContainer.getShow().y + m_enemyDropOutContainer.getShow().height + 20;
               _loc3_ = _loc6_ ? _loc6_.length : 0;
               m_bossDropOutEqShows = [];
               _loc11_ = 0;
               while(_loc11_ < _loc3_)
               {
                  _loc5_ = MyFunction2.sheatheEquipmentShell(_loc6_[_loc11_]);
                  _loc5_.addEventListener("rollOver",onOver,false,0,true);
                  _loc5_.addEventListener("rollOut",onOut,false,0,true);
                  _loc8_ = MyFunction2.returnShowByClassName("EqC") as Sprite;
                  _loc8_.addChild(_loc5_);
                  if(m_eqCs == null)
                  {
                     m_eqCs = new Vector.<Sprite>();
                  }
                  m_eqCs.push(_loc8_);
                  _loc8_.x = 30 + _loc11_ % 5 * 50;
                  _loc8_.y = 30 + int(_loc11_ / 5) * 50;
                  _loc2_.addChild(_loc8_);
                  m_bossDropOutEqShows.push(_loc5_);
                  _loc11_++;
               }
               break;
            case "taskDescription2":
               initFrame2();
               _loc7_ = param1.description as TaskDescription2;
               m_npcNameText.text = _loc7_.npcName;
               m_mapNameText.text = _loc7_.mapName;
               m_npcFunctionDesText.text = _loc7_.npcFunctionDescription;
               break;
            case "taskDescription3":
               initFrame3();
               break;
            default:
               throw new Error("位置类型！");
         }
         var _loc10_:* = param1.getCurrentTaskGoalByIndex(0) ? param1.getCurrentTaskGoalByIndex(0).num : 0;
         var _loc9_:int = param1.getNeedTaskGoalByIndex(0).num;
         if(param1.isGotReward)
         {
            _loc10_ = _loc9_;
         }
         m_taskBar.change(_loc10_ / _loc9_);
         m_taskBar.setDataShow(_loc10_ + "/" + _loc9_);
         m_taskLevelShow.change(param1.taskLevel);
         m_taskFinishConditionText.text = param1.description.finishConditionDescription;
      }
      
      private function clearFrameShow() : void
      {
         if(m_taskBar)
         {
            m_taskBar.clear();
         }
         m_taskBar = null;
         if(m_taskLevelShow)
         {
            m_taskLevelShow.clear();
         }
         m_taskLevelShow = null;
         m_taskFinishConditionText = null;
         m_enemyNameText = null;
         m_mapNameText = null;
         ClearUtil.nullArr(m_enemyDropOutEqShows);
         m_enemyDropOutEqShows = null;
         ClearUtil.nullArr(m_bossDropOutEqShows);
         m_bossDropOutEqShows = null;
         ClearUtil.clearObject(m_enemyDropOutContainer);
         m_enemyDropOutContainer = null;
         ClearUtil.clearObject(m_bossDropOutContainer);
         m_bossDropOutContainer = null;
         m_npcNameText = null;
         m_npcFunctionDesText = null;
      }
      
      private function initFrame1() : void
      {
         m_showMC.gotoAndStop("taskDescription1");
         m_taskBar = new CMSXChangeBarLogicShell();
         m_taskBar.setShow(m_show["taskBar"]);
         m_taskLevelShow = new CMSXChangeBarsLogicShell();
         m_taskLevelShow.setShow(m_show["taskLevelShow"]);
         m_taskFinishConditionText = m_show["finishConditionTxt"];
         m_enemyNameText = m_show["enemyNameText"];
         m_mapNameText = m_show["mapNameText"];
         m_enemyDropOutContainer = new MovieClipPlayLogicShell();
         m_enemyDropOutContainer.setShow(m_show["enemyDropOutContainer"]);
         m_enemyDropOutContainer.gotoAndStop("enemy");
         m_bossDropOutContainer = new MovieClipPlayLogicShell();
         m_bossDropOutContainer.setShow(m_show["bossDropOutContainer"]);
         m_bossDropOutContainer.gotoAndStop("boss");
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_taskFinishConditionText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_enemyNameText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_mapNameText);
      }
      
      private function initFrame2() : void
      {
         m_showMC.gotoAndStop("taskDescription2");
         m_taskBar = new CMSXChangeBarLogicShell();
         m_taskBar.setShow(m_show["taskBar"]);
         m_taskLevelShow = new CMSXChangeBarsLogicShell();
         m_taskLevelShow.setShow(m_show["taskLevelShow"]);
         m_taskFinishConditionText = m_show["finishConditionTxt"];
         m_npcNameText = m_show["npcNameText"];
         m_mapNameText = m_show["mapNameText"];
         m_npcFunctionDesText = m_show["npcFunctionDesText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_taskFinishConditionText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_mapNameText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_npcNameText);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_npcFunctionDesText);
      }
      
      private function initFrame3() : void
      {
         m_showMC.gotoAndStop("taskDescription3");
         m_taskBar = new CMSXChangeBarLogicShell();
         m_taskBar.setShow(m_show["taskBar"]);
         m_taskLevelShow = new CMSXChangeBarsLogicShell();
         m_taskLevelShow.setShow(m_show["taskLevelShow"]);
         m_taskFinishConditionText = m_show["finishConditionTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_taskFinishConditionText);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         if(param1.currentTarget is SceneEquipment)
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
               "equipment":(param1.currentTarget as SceneEquipment).getEquipment(),
               "messageBoxMode":1
            }));
         }
         else
         {
            m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
               "equipment":param1.currentTarget,
               "messageBoxMode":1
            }));
         }
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

