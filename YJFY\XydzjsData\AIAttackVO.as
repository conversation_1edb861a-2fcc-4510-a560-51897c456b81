package YJFY.XydzjsData
{
   public class AIAttackVO
   {
      
      private var m_unableAttackMinInterval:uint;
      
      private var m_unableAttackMaxInterval:uint;
      
      private var m_rebuildAllFoeInterval:uint;
      
      public function AIAttackVO()
      {
         super();
         m_rebuildAllFoeInterval = 2000;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc4_:int = 0;
         var _loc2_:XMLList = param1.data;
         var _loc3_:int = int(_loc2_.length());
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            this[String(_loc2_[_loc4_].@att)] = String(_loc2_[_loc4_].@value);
            _loc4_++;
         }
         if(rebuildAllFoeInterval == 0)
         {
            rebuildAllFoeInterval = 2000;
         }
      }
      
      public function getUnableAttackMinInterval() : uint
      {
         return unableAttackMinInterval;
      }
      
      public function getUnableAttackMaxInterval() : uint
      {
         return unableAttackMaxInterval;
      }
      
      public function getRebuildAllFoeInterval() : uint
      {
         return rebuildAllFoeInterval;
      }
      
      private function get unableAttackMinInterval() : uint
      {
         return m_unableAttackMinInterval;
      }
      
      private function set unableAttackMinInterval(param1:uint) : void
      {
         m_unableAttackMinInterval = param1;
      }
      
      private function get unableAttackMaxInterval() : uint
      {
         return m_unableAttackMaxInterval;
      }
      
      private function set unableAttackMaxInterval(param1:uint) : void
      {
         m_unableAttackMaxInterval = param1;
      }
      
      private function get rebuildAllFoeInterval() : uint
      {
         return m_rebuildAllFoeInterval;
      }
      
      private function set rebuildAllFoeInterval(param1:uint) : void
      {
         m_rebuildAllFoeInterval = param1;
      }
   }
}

