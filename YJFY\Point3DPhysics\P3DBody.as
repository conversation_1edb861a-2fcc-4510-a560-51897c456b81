package YJFY.Point3DPhysics
{
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.ClearUtil;
   
   public class P3DBody
   {
      
      public static const s_const_p3d_dynamicBody:String = "d";
      
      public static const s_const_p3d_staticBody:String = "s";
      
      private var m_type:String;
      
      private var m_mass:Number;
      
      private var m_friction:Number;
      
      private var m_restitution:Number;
      
      private var m_velocity:P3DVector3D;
      
      private var m_location:P3DVector3D;
      
      private var m_force:P3DVector3D;
      
      private var m_isOutGravity:Boolean;
      
      private var m_world:P3DWorld;
      
      private var m_userData:Object;
      
      public function P3DBody()
      {
         super();
         m_type = "d";
         m_mass = 1;
         m_friction = 1;
         m_restitution = 0;
         m_velocity = new P3DVector3D();
         m_location = new P3DVector3D();
         m_force = new P3DVector3D();
      }
      
      public function clear() : void
      {
         m_type = null;
         ClearUtil.clearObject(m_velocity);
         m_velocity = null;
         ClearUtil.clearObject(m_location);
         m_location = null;
         ClearUtil.clearObject(m_force);
         m_force = null;
         m_world = null;
         m_userData = null;
      }
      
      public function initByDef(param1:P3DBodyDef) : void
      {
         m_type = param1.getType();
         m_mass = param1.getMass();
         m_velocity = param1.getVelocity();
         m_location = param1.getPosition();
         m_friction = param1.getFriction();
         m_restitution = param1.getRestitution();
      }
      
      public function setWorld(param1:P3DWorld) : void
      {
         m_world = param1;
      }
      
      public function render(param1:IP3DTime) : void
      {
         if(m_type == "d")
         {
            m_velocity.add(m_force.multi(param1.getAddTimeOneFrame() / 1000 / m_mass));
            m_location.add(m_velocity.multi(param1.getAddTimeOneFrame() / 1000));
         }
         m_force.setTo(0,0,0);
      }
      
      public function applyForce(param1:P3DVector3D) : void
      {
         m_force.add(param1);
      }
      
      public function applyImpulse(param1:P3DVector3D) : void
      {
         m_velocity.add(param1.multi(1 / m_mass));
      }
      
      public function setNewPosition(param1:P3DVector3D) : void
      {
         m_location.copy(param1);
      }
      
      public function setNewPosition2(param1:Number, param2:Number, param3:Number) : void
      {
         m_location.setTo(param1,param2,param3);
      }
      
      public function setVelocity(param1:P3DVector3D) : void
      {
         if(isNaN(param1.getZ()))
         {
            throw new Error();
         }
         m_velocity.copy(param1);
      }
      
      public function setVelocity2(param1:Number, param2:Number, param3:Number) : void
      {
         m_velocity.setTo(param1,param2,param3);
      }
      
      public function setFriction(param1:Number) : void
      {
         if(param1 < 0 || param1 > 1)
         {
            throw new Error("摩擦率的值必须在0-1之间");
         }
         m_friction = param1;
      }
      
      public function setUserData(param1:Object) : void
      {
         m_userData = param1;
      }
      
      public function getType() : String
      {
         return m_type;
      }
      
      public function getMass() : Number
      {
         return m_mass;
      }
      
      public function setMass(param1:Number) : void
      {
         if(isNaN(param1) || param1 <= 0)
         {
            throw new Error("设置的新的BodyDef质量为NaN 或 小于等于0");
         }
         m_mass = param1;
      }
      
      public function getIsOutGravity() : Boolean
      {
         return m_isOutGravity;
      }
      
      public function setIsOUtGravity(param1:Boolean) : void
      {
         m_isOutGravity = param1;
      }
      
      public function getFriction() : Number
      {
         return m_friction;
      }
      
      public function getRestitution() : Number
      {
         return m_restitution;
      }
      
      public function getVelocity() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_velocity);
         return _loc1_;
      }
      
      public function getVelocityX() : Number
      {
         return m_velocity.getX();
      }
      
      public function getVelocityY() : Number
      {
         return m_velocity.getY();
      }
      
      public function getVelocityZ() : Number
      {
         return m_velocity.getZ();
      }
      
      public function getPosition() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_location);
         return _loc1_;
      }
      
      public function getForce() : P3DVector3D
      {
         return m_force.clone();
      }
      
      public function getForceX() : Number
      {
         return m_force.getX();
      }
      
      public function getForceY() : Number
      {
         return m_force.getY();
      }
      
      public function getForceZ() : Number
      {
         return m_force.getZ();
      }
      
      public function setForce(param1:Number, param2:Number, param3:Number) : void
      {
         m_force.setTo(param1,param2,param3);
      }
      
      public function getPositionX() : Number
      {
         return m_location.getX();
      }
      
      public function getPositionY() : Number
      {
         return m_location.getY();
      }
      
      public function getPositionZ() : Number
      {
         return m_location.getZ();
      }
      
      public function getWorld() : P3DWorld
      {
         return m_world;
      }
      
      public function getUserData() : Object
      {
         return m_userData;
      }
   }
}

