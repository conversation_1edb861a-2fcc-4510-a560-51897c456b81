package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Skill.ActiveSkill.ActiveSkill;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class AttackSkill extends ActiveSkill
   {
      
      protected var m_attackInterval:int = 100;
      
      protected var m_nextAttackReachTime:Number;
      
      protected var m_attackData:AttackData;
      
      protected var m_hurtDuration:uint;
      
      protected var m_attackSkillListeners:Vector.<IAttackSkillListener>;
      
      protected var dontAttacktangsheng:Boolean = false;
      
      public function AttackSkill()
      {
         super();
         m_attackSkillListeners = new Vector.<IAttackSkillListener>();
         m_attackData = new AttackData(true,0,false,false,0);
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_attackData);
         m_attackData = null;
         ClearUtil.nullArr(m_attackSkillListeners,false,false,false);
         m_attackSkillListeners = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_attackInterval = int(param1.@attackInterval);
         m_hurtDuration = uint(param1.@hurtDuration);
         dontAttacktangsheng = Boolean(param1.@dontAttacktangsheng);
      }
      
      override protected function startSkill2(param1:World) : Boolean
      {
         var _loc2_:Boolean = super.startSkill2(param1);
         if(_loc2_)
         {
            m_nextAttackReachTime = NaN;
         }
         return _loc2_;
      }
      
      protected function attackReach(param1:World) : void
      {
         if(m_isOutWorldTime)
         {
            if(m_attackInterval)
            {
               m_nextAttackReachTime = param1.getWorldExistTime() + m_attackInterval;
            }
         }
         else if(m_attackInterval)
         {
            m_nextAttackReachTime = param1.getWorldTime() + m_attackInterval;
         }
      }
      
      public function attackSuccess(param1:IEntity) : void
      {
         if(param1 is AnimalEntity && (param1 as AnimalEntity).isInDie())
         {
            return;
         }
         attackSuccess2(param1,this,m_owner);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            if(param1 is IAnimalEntity)
            {
               (param1 as IAnimalEntity).beAttack(m_owner,m_attackData,this,param1);
            }
         }
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun == false)
         {
            return;
         }
         if(m_isOutWorldTime)
         {
            if(!isNaN(m_nextAttackReachTime) && Boolean(m_attackInterval) && m_nextAttackReachTime < param1.getWorldExistTime())
            {
               attackReach(param1);
            }
         }
         else if(!isNaN(m_nextAttackReachTime) && Boolean(m_attackInterval) && m_nextAttackReachTime < param1.getWorldTime())
         {
            attackReach(param1);
         }
      }
      
      public function setAttackData(param1:AttackData) : void
      {
         m_attackData.copy(param1);
         if(m_hurtDuration > m_attackData.getHurtDuration())
         {
            m_attackData.setHurtDuration(m_hurtDuration);
         }
      }
      
      public function addAttackSkillListener(param1:IAttackSkillListener) : void
      {
         m_attackSkillListeners.push(param1);
      }
      
      public function removeAttackSkillListener(param1:IAttackSkillListener) : void
      {
         var _loc2_:int = int(m_attackSkillListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_attackSkillListeners.splice(_loc2_,1);
            _loc2_ = int(m_attackSkillListeners.indexOf(param1));
         }
      }
      
      protected function attackSuccess2(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         var _loc6_:int = 0;
         var _loc4_:Vector.<IAttackSkillListener> = m_attackSkillListeners.slice(0);
         var _loc5_:int = int(_loc4_.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc4_[_loc6_].attackSuccess(param1,param2,param3);
            _loc4_[_loc6_] = null;
            _loc6_++;
         }
         _loc4_.length = 0;
         _loc4_ = null;
      }
   }
}

