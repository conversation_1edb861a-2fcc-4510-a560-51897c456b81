package YJFY.LevelMode5
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.EntityAI.AutomationAI.SearchAttackTargetAI;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class EndDancerBossAI
   {
      
      private var m_boss:EndDancerBoss;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      private var m_startX:int;
      
      private var m_startY:int;
      
      private var m_bossMoveToAI:EndDancerBossMoveToAI;
      
      private var m_interAttackAI:EndDancerBossAttackAI;
      
      private var m_searchAttackTargetAI:SearchAttackTargetAI;
      
      private var m_endDancerBossSkillAI:EndDancerBossSkillAI;
      
      public function EndDancerBossAI()
      {
         super();
         m_bossMoveToAI = new EndDancerBossMoveToAI();
         m_interAttackAI = new EndDancerBossAttackAI();
         m_searchAttackTargetAI = new SearchAttackTargetAI();
         m_endDancerBossSkillAI = new EndDancerBossSkillAI();
      }
      
      public function clear() : void
      {
         m_boss = null;
         m_owner = null;
         m_world = null;
         ClearUtil.clearObject(m_interAttackAI);
         m_interAttackAI = null;
         ClearUtil.clearObject(m_bossMoveToAI);
         m_bossMoveToAI = null;
         ClearUtil.clearObject(m_searchAttackTargetAI);
         m_searchAttackTargetAI = null;
         ClearUtil.clearObject(m_endDancerBossSkillAI);
         m_endDancerBossSkillAI = null;
      }
      
      public function init(param1:IAnimalEntity, param2:World, param3:EndDancerBoss, param4:int, param5:int) : void
      {
         m_owner = param1 as IAnimalEntity;
         m_world = param2;
         m_boss = param3;
         m_startX = param4;
         m_startY = param5;
         m_bossMoveToAI.init(m_owner,m_world,m_boss,m_startX,m_startY);
         m_searchAttackTargetAI.init(m_owner,m_world,m_boss,m_boss.getRebuildAllFoeInterval());
         m_interAttackAI.init(m_owner,m_world,m_boss.getUnableAttackMinInterval(),m_boss.getUnableAttackMaxInterval(),m_searchAttackTargetAI);
         m_endDancerBossSkillAI.init(m_owner,m_world,m_boss);
      }
      
      public function render() : void
      {
         if(m_world.isStop())
         {
            return;
         }
         if(LevelEndDancerWorld.m_wave == 3 && LevelEndDancerWorld.m_bPlay3 && LevelEndDancerWorld.m_end3 == false)
         {
            m_searchAttackTargetAI.render();
            m_bossMoveToAI.render();
            m_interAttackAI.render();
            m_endDancerBossSkillAI.render();
         }
      }
      
      public function resetPos() : void
      {
         m_owner.setNewPosition(m_startX,m_startY,m_owner.getZ());
         m_owner.walk();
         m_endDancerBossSkillAI.resetPos();
      }
      
      public function setSkill_2_2(param1:Boolean) : void
      {
         m_endDancerBossSkillAI.setSkill_2_2(param1);
      }
      
      public function setSkill_4(param1:Boolean) : void
      {
         m_endDancerBossSkillAI.setSkill_4(param1);
      }
   }
}

