package YJFY.ShowLogicShell.CheckBox
{
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.IButton;
   import flash.display.InteractiveObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class CheckBoxLogicShell implements IButton
   {
      
      protected var m_show:InteractiveObject;
      
      protected var m_extra:Object;
      
      private var _isCheck:Boolean;
      
      public function CheckBoxLogicShell()
      {
         super();
      }
      
      public function setShow(param1:InteractiveObject) : void
      {
         this.m_show = param1;
         _isCheck = true;
         MovieClip(param1).buttonMode = true;
         MovieClip(param1).gotoAndStop(1);
         param1.addEventListener("click",onClick,false,0,true);
      }
      
      public function clear() : void
      {
         m_show.removeEventListener("click",onClick,false);
         m_show = null;
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         isCheck = !isCheck;
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
      
      public function get isCheck() : Boolean
      {
         return _isCheck;
      }
      
      public function set isCheck(param1:Boolean) : void
      {
         _isCheck = param1;
         if(_isCheck)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
         else
         {
            MovieClip(m_show).gotoAndStop(2);
         }
      }
      
      public function getShow() : InteractiveObject
      {
         return m_show;
      }
      
      public function setExtra(param1:Object) : void
      {
         m_extra = param1;
      }
      
      public function getExtra() : Object
      {
         return m_extra;
      }
   }
}

