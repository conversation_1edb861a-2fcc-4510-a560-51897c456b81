package YJFY.XydzjsData.AISkillVO
{
   import YJFY.Logic.IActiveSkillVO_HaveAiNeedSkillInfor;
   import YJFY.World.World;
   import YJFY.XydzjsData.EnemyVO;
   
   public class AIActiveSkillVO implements IActiveSkillVO_HaveAiNeedSkillInfor
   {
      
      private var m_skillId:String;
      
      private var m_skillCostMp:uint;
      
      private var m_skillCdTime:uint;
      
      private var m_isAbleRunInHurt:Boolean;
      
      private var m_isInvincibleInRun:Boolean;
      
      private var m_priorityForRunInHurt:uint;
      
      private var m_priorityForRun:uint;
      
      private var m_nextAbleRunTime:Number = 0;
      
      public var m_TimeForHurt:int;
      
      public var m_hpPercentage:int;
      
      public var m_TimeForBianshen:int;
      
      private var m_skillNoCDRun:Boolean;
      
      public function AIActiveSkillVO()
      {
         super();
      }
      
      public function clear() : void
      {
         m_skillId = null;
      }
      
      public function initFromXML(param1:XML) : void
      {
         m_skillId = String(param1.@skillId);
         m_skillCostMp = uint(param1.@costMp);
         m_skillCdTime = uint(param1.@cdTime);
         m_skillNoCDRun = m_skillCdTime == 0;
         this.isAbleRunInHurt = Boolean(int(param1.@isAbleRunInHurt));
         this.isInvincibleInRun = Boolean(int(param1.@isInvincibleInRun));
         this.priorityForRunInHurt = uint(param1.@priorityForRunInHurt);
         this.priorityForRun = uint(param1.@priorityForRun);
         if(int(param1.@timeForHurt))
         {
            m_TimeForHurt = int(param1.@timeForHurt) * 20;
         }
         if(int(param1.@hpPercentage))
         {
            m_hpPercentage = int(param1.@hpPercentage);
         }
         if(int(param1.@timeForBianshen))
         {
            m_TimeForBianshen = int(param1.@timeForBianshen) * 20;
         }
      }
      
      public function getSkillId() : String
      {
         return m_skillId;
      }
      
      public function getIsAbleRunInHurt() : Boolean
      {
         return isAbleRunInHurt;
      }
      
      public function getIsInvincibleInRun() : Boolean
      {
         return isInvincibleInRun;
      }
      
      public function getPriorityForRunInHurt() : uint
      {
         return priorityForRunInHurt;
      }
      
      public function getPriorityForRun() : uint
      {
         return priorityForRun;
      }
      
      public function getIsAbleRun() : Boolean
      {
         return true;
      }
      
      public function runSkill(param1:World, param2:EnemyVO) : void
      {
         m_nextAbleRunTime = param1.getWorldTime() + m_skillCdTime;
         param2.decMp(m_skillCostMp);
      }
      
      public function isAbleRunSkill(param1:World, param2:EnemyVO) : Boolean
      {
         if(m_skillNoCDRun)
         {
            return true;
         }
         if(m_nextAbleRunTime > param1.getWorldTime())
         {
            return false;
         }
         if(param2.getCurrentMp() < m_skillCostMp)
         {
            return false;
         }
         return true;
      }
      
      protected function get isAbleRunInHurt() : Boolean
      {
         return m_isAbleRunInHurt;
      }
      
      protected function set isAbleRunInHurt(param1:Boolean) : void
      {
         m_isAbleRunInHurt = param1;
      }
      
      protected function get isInvincibleInRun() : Boolean
      {
         return m_isInvincibleInRun;
      }
      
      protected function set isInvincibleInRun(param1:Boolean) : void
      {
         m_isInvincibleInRun = param1;
      }
      
      protected function get priorityForRunInHurt() : uint
      {
         return m_priorityForRunInHurt;
      }
      
      protected function set priorityForRunInHurt(param1:uint) : void
      {
         m_priorityForRunInHurt = param1;
      }
      
      protected function get priorityForRun() : uint
      {
         return m_priorityForRun;
      }
      
      protected function set priorityForRun(param1:uint) : void
      {
         m_priorityForRun = param1;
      }
   }
}

