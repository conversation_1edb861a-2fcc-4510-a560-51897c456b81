package UI.ExternalUI
{
   import UI.Event.UIBtnEvent;
   import UI.ExchangeGiftBag.ExchangeGiftData;
   import UI.ExternalUI.Button.ExOnLineGiftBagBtn;
   import UI.ExternalUI.Button.ExShopBtn;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import UI.newGuide.NewGuidePanel;
   import UI2.Consumer.ConsumerData;
   import UI2.TipDataShow.TipDataShow;
   import UI2.broadcast.BroadDataManager;
   import UI2.firstPay.FirstPayData;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class MiddlePanel
   {
      
      public static const NORMAL:String = "normal";
      
      public static const FIGHT:String = "fight";
      
      private var _show:MovieClip;
      
      private var m_rewardNum:MovieClip;
      
      private var m_txtNum:TextField;
      
      private var _showMc:MovieClipPlayLogicShell;
      
      private var _protectBtn:ButtonLogicShell;
      
      private var _packageBtn:ButtonLogicShell;
      
      private var _tryAutomaticPetBtn:ButtonLogicShell;
      
      private var _systemBtn:ButtonLogicShell;
      
      private var _signBtn:ButtonLogicShell;
      
      private var _exchangeBtn:ButtonLogicShell;
      
      private var _realNameBtn:ButtonLogicShell;
      
      private var _qiandaolibaoBtn:ButtonLogicShell;
      
      private var _weekshouchongBtn:ButtonLogicShell;
      
      private var _activityRewardBtn:ButtonLogicShell;
      
      private var _bbBtn:ButtonLogicShell;
      
      private var _btnLiuyi:ButtonLogicShell;
      
      private var _hlzpBtn:ButtonLogicShell;
      
      private var _jhsbBtn:ButtonLogicShell;
      
      private var _lyhdBtn:ButtonLogicShell;
      
      private var _exFarmBtn:ButtonLogicShell;
      
      private var _onLineGiftBagBtn:ExOnLineGiftBagBtn;
      
      private var _tipDataShow:TipDataShow;
      
      private var _guidepanel:NewGuidePanel;
      
      private var _activityBtn:ButtonLogicShell2;
      
      private var _collectTimeBtn:ButtonLogicShell2;
      
      private var _automaticPetEnterBtn:ButtonLogicShell2;
      
      private var _mountEnterBtn:ButtonLogicShell2;
      
      private const SHOP_BTN_X:Number = 120;
      
      private const SHOP_BTN_Y:Number = -35;
      
      private const SHOP_SCALE_X:Number = 0.65;
      
      private const SHOP_SCALE_Y:Number = 0.65;
      
      private var _shopBtn:ExShopBtn;
      
      private var _num1:MultiPlaceNumLogicShell;
      
      private var _num2:MultiPlaceNumLogicShell;
      
      private var _fenHao:Sprite;
      
      private var m_middlePanelListeners:Vector.<IMiddlePanelListener>;
      
      private var _state:String = "";
      
      public function MiddlePanel()
      {
         super();
         m_middlePanelListeners = new Vector.<IMiddlePanelListener>();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         _show = param1;
         _showMc = new MovieClipPlayLogicShell();
         _showMc.setShow(_show);
         init();
         hideWave();
         _show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function getShow() : MovieClip
      {
         return _show;
      }
      
      public function addMiddlePanelListener(param1:IMiddlePanelListener) : void
      {
         ListenerUtil.addListener(m_middlePanelListeners,param1);
      }
      
      public function removeMiddlePanelListener(param1:IMiddlePanelListener) : void
      {
         ListenerUtil.removeListener(m_middlePanelListeners,param1);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:AutomaticPetVO = null;
         switch(param1.button)
         {
            case _protectBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoProtect"));
               break;
            case _packageBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoPackage"));
               break;
            case _tryAutomaticPetBtn:
               _tryAutomaticPetBtn.getShow().visible = false;
               _loc2_ = new AutomaticPetVO();
               if(_show["btnTryAutomaticPet"].currentFrame == 1)
               {
                  FirstPayData.getInstance().isUseLongwang = true;
                  _loc2_.initFromSaveML(XMLSingle.getInstance().automaticPetOtherDataXML.tryouts[0],XMLSingle.getInstance().automaticPetsXML);
               }
               else if(_show["btnTryAutomaticPet"].currentFrame == 2)
               {
                  FirstPayData.getInstance().isUseDuowen = true;
                  _loc2_.initFromSaveML(XMLSingle.getInstance().automaticPetOtherDataXML.tryouts[1],XMLSingle.getInstance().automaticPetsXML);
               }
               else if(_show["btnTryAutomaticPet"].currentFrame == 3)
               {
                  FirstPayData.getInstance().isUseDabai = true;
                  _loc2_.initFromSaveML(XMLSingle.getInstance().automaticPetOtherDataXML.tryouts[2],XMLSingle.getInstance().automaticPetsXML);
               }
               GamingUI.getInstance().getAutomaticPetsData().setSkinsForAutomaticPet(_loc2_);
               GamingUI.getInstance().player1.playerVO.automaticPetVO = _loc2_;
               break;
            case _systemBtn:
               Part1.getInstance().openSystemPanel();
               break;
            case _signBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoSignPanel"));
               break;
            case _realNameBtn:
               _show.dispatchEvent(new UIBtnEvent("enterinforealname"));
               break;
            case _exchangeBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoExchangePanel"));
               break;
            case _qiandaolibaoBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoCodekeyPanel"));
               break;
            case _weekshouchongBtn:
               GamingUI.getInstance().enterToWeekPay();
               break;
            case _activityRewardBtn:
               GamingUI.getInstance().enterToConsumer();
               break;
            case _bbBtn:
               _show.dispatchEvent(new UIBtnEvent("enterIntoBBSPanel"));
               break;
            case _btnLiuyi:
               GamingUI.getInstance().enterToWuyiView();
               break;
            case _hlzpBtn:
               _show.dispatchEvent(new UIBtnEvent("enterinfohuanlezp"));
               break;
            case _jhsbBtn:
               break;
            case _lyhdBtn:
               GamingUI.getInstance().enterToLiuyiPoster();
               break;
            case _exFarmBtn:
               _show.dispatchEvent(new UIBtnEvent(" enterIntoFarm"));
               break;
            case _onLineGiftBagBtn.getBtn():
               GamingUI.getInstance().openOnLineGiftBagPanel();
               break;
            case _activityBtn:
               GamingUI.getInstance().openActivityPanel();
               break;
            case _collectTimeBtn:
               GamingUI.getInstance().openCollectTimePanel();
               break;
            case _automaticPetEnterBtn:
               GamingUI.getInstance().openAutomaticPetPanel();
               break;
            case _mountEnterBtn:
               GamingUI.getInstance().openMountPanel();
         }
      }
      
      private function init() : void
      {
         _protectBtn = new ButtonLogicShell();
         _protectBtn.setShow(_show["protectBtn"]);
         _protectBtn.setTipString("打开佛祖保佑界面");
         _packageBtn = new ButtonLogicShell();
         _packageBtn.setShow(_show["packageBtn"]);
         _packageBtn.setTipString("打开背包");
         _systemBtn = new ButtonLogicShell();
         _systemBtn.setShow(_show["systemBtn"]);
         _systemBtn.setTipString("打开系统设置");
         _exFarmBtn = new ButtonLogicShell();
         _exFarmBtn.setShow(_show["exFarmBtn"]);
         _exFarmBtn.setTipString("打开农场");
         _shopBtn = new ExShopBtn();
         _shopBtn.x = 120;
         _shopBtn.y = -35;
         _shopBtn.scaleX = 0.65;
         _shopBtn.scaleY = 0.65;
         _shopBtn.name = "shopBtn";
         _show.addChildAt(_shopBtn,1);
         tranToNormalState();
      }
      
      private function initNormalFrame() : void
      {
         if(_state == "normal")
         {
            return;
         }
         _state = "normal";
         clearNormalFrame();
         clearInFightFrame();
         _showMc.gotoAndStop("normal");
         if(_guidepanel)
         {
            _guidepanel.show();
         }
         Part1.getInstance().setTipXY(250,150);
         _exchangeBtn = new ButtonLogicShell();
         _exchangeBtn.setShow(_show["exchangeBtn"]);
         _exchangeBtn.setTipString("兑换礼包");
         _qiandaolibaoBtn = new ButtonLogicShell();
         _qiandaolibaoBtn.setShow(_show["zhuanshubtn"]);
         _qiandaolibaoBtn.setTipString("专属礼包");
         _weekshouchongBtn = new ButtonLogicShell();
         _show["btnweekChongzhi"].gotoAndStop("weekshouchong");
         _weekshouchongBtn.setShow(_show["btnweekChongzhi"]["btn"]);
         _weekshouchongBtn.setTipString("每周首冲");
         _activityRewardBtn = new ButtonLogicShell();
         _activityRewardBtn.setShow(_show["btnReward"]);
         _activityRewardBtn.setTipString("活动奖励");
         m_rewardNum = (_activityRewardBtn.getShow() as MovieClip)["tipmc"] as MovieClip;
         m_txtNum = m_rewardNum["txtrewardnum"] as TextField;
         m_txtNum.mouseEnabled = false;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtNum,true);
         _bbBtn = new ButtonLogicShell();
         _bbBtn.setShow(_show["bbsBtn"]);
         _bbBtn.setTipString("论坛");
         _signBtn = new ButtonLogicShell();
         _signBtn.setShow(_show["signBtn"]);
         _signBtn.setTipString("前去签到");
         _realNameBtn = new ButtonLogicShell();
         _realNameBtn.setShow(_show["realNameBtn"]);
         _realNameBtn.setTipString("完善身份信息");
         if(judeIsHaveTheBag("realName2019"))
         {
            _realNameBtn.getShow().visible = false;
         }
         _onLineGiftBagBtn = new ExOnLineGiftBagBtn();
         _onLineGiftBagBtn.setShow(_show["onLineGiftBagBtn"]);
         _onLineGiftBagBtn.getShow().visible = false;
         if(_guidepanel == null)
         {
            _guidepanel = new NewGuidePanel();
            _guidepanel.setShow(_show["tiplistpanel"]);
         }
         _activityBtn = new ButtonLogicShell2();
         _activityBtn.setShow(_show["activityBtn"]);
         _collectTimeBtn = new ButtonLogicShell2();
         _collectTimeBtn.setShow(_show["collectTimeBtn"]);
         _automaticPetEnterBtn = new ButtonLogicShell2();
         _automaticPetEnterBtn.setShow(_show["automaticPetEnterBtn"]);
         _mountEnterBtn = new ButtonLogicShell2();
         _mountEnterBtn.setShow(_show["mountEnterBtn"]);
         _tipDataShow = new TipDataShow();
         _tipDataShow.setEnterFrameTime(GamingUI.getInstance().getEnterFrameTime());
         _tipDataShow.setShow(_show["tipDataShow"]);
      }
      
      private function clearNormalFrame() : void
      {
         if(_exchangeBtn)
         {
            _exchangeBtn.clear();
         }
         _exchangeBtn = null;
         if(_qiandaolibaoBtn)
         {
            _qiandaolibaoBtn.clear();
         }
         _qiandaolibaoBtn = null;
         ClearUtil.clearObject(_weekshouchongBtn);
         _weekshouchongBtn = null;
         ClearUtil.clearObject(_activityRewardBtn);
         _activityRewardBtn = null;
         if(_bbBtn)
         {
            _bbBtn.clear();
         }
         _bbBtn = null;
         if(_btnLiuyi)
         {
            _btnLiuyi.clear();
         }
         _btnLiuyi = null;
         if(_hlzpBtn)
         {
            _hlzpBtn.clear();
         }
         _hlzpBtn = null;
         if(_signBtn)
         {
            _signBtn.clear();
         }
         _signBtn = null;
         if(_realNameBtn)
         {
            _realNameBtn.clear();
         }
         _realNameBtn = null;
         if(_onLineGiftBagBtn)
         {
            _onLineGiftBagBtn.clear();
         }
         _onLineGiftBagBtn = null;
         ClearUtil.clearObject(_activityBtn);
         _activityBtn = null;
         ClearUtil.clearObject(_collectTimeBtn);
         _collectTimeBtn = null;
         ClearUtil.clearObject(_automaticPetEnterBtn);
         _automaticPetEnterBtn = null;
         ClearUtil.clearObject(_mountEnterBtn);
         _mountEnterBtn = null;
         ClearUtil.clearObject(_tipDataShow);
         _tipDataShow = null;
      }
      
      private function initInFightFrame() : void
      {
         if(_state == "fight")
         {
            return;
         }
         _state = "fight";
         clearNormalFrame();
         clearInFightFrame();
         _showMc.gotoAndStop("inFight");
         _num1 = new MultiPlaceNumLogicShell();
         _num1.setShow(_show["num1"]);
         _num2 = new MultiPlaceNumLogicShell();
         _num2.setShow(_show["num2"]);
         _fenHao = _show["fenHao"];
         _guidepanel.hide();
         _tryAutomaticPetBtn = null;
         if(GamingUI.getInstance().player1.playerVO.level < 50 && !FirstPayData.getInstance().isUseLongwang)
         {
            _tryAutomaticPetBtn = new ButtonLogicShell();
            _show["btnTryAutomaticPet"].gotoAndStop(1);
            _tryAutomaticPetBtn.setTipString("50级以下玩家可以召唤一次龙王助阵");
            _tryAutomaticPetBtn.setShow(_show["btnTryAutomaticPet"]["btn"]);
         }
         else if(!FirstPayData.getInstance().isUseDuowen)
         {
            _tryAutomaticPetBtn = new ButtonLogicShell();
            _show["btnTryAutomaticPet"].gotoAndStop(2);
            _tryAutomaticPetBtn.setTipString("玩家可以召唤一次多闻助阵");
            _tryAutomaticPetBtn.setShow(_show["btnTryAutomaticPet"]["btn"]);
         }
         else if(!FirstPayData.getInstance().isUseDabai)
         {
            _tryAutomaticPetBtn = new ButtonLogicShell();
            _show["btnTryAutomaticPet"].gotoAndStop(3);
            _tryAutomaticPetBtn.setTipString("玩家可以召唤一次大白助阵");
            _tryAutomaticPetBtn.setShow(_show["btnTryAutomaticPet"]["btn"]);
         }
         else
         {
            _show["btnTryAutomaticPet"].stop();
            _show["btnTryAutomaticPet"].visible = false;
         }
      }
      
      private function judeIsHaveTheBag(param1:String) : Boolean
      {
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = ExchangeGiftData.getInstance().exchangeGiftNames ? ExchangeGiftData.getInstance().exchangeGiftNames.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(ExchangeGiftData.getInstance().exchangeGiftNames[_loc4_] == param1)
            {
               _loc3_ = true;
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function hideAutomaticBtn() : void
      {
         if(_tryAutomaticPetBtn)
         {
            _tryAutomaticPetBtn.getShow().visible = false;
         }
      }
      
      public function hideRealNameBtn() : void
      {
         if(_realNameBtn)
         {
            _realNameBtn.getShow().visible = false;
         }
      }
      
      private function clearInFightFrame() : void
      {
         if(_num1)
         {
            _num1.clear();
         }
         _num1 = null;
         if(_num2)
         {
            _num2.clear();
         }
         _num2 = null;
         _fenHao = null;
      }
      
      public function get shopBtn() : ExShopBtn
      {
         return _shopBtn;
      }
      
      public function clear() : void
      {
         var _loc1_:* = null;
         _show.removeEventListener("clickButton",clickButton,true);
         _show = null;
         if(_showMc)
         {
            _showMc.clear();
         }
         _showMc = null;
         if(_exFarmBtn)
         {
            _exFarmBtn.clear();
         }
         _exFarmBtn = null;
         if(_protectBtn)
         {
            _protectBtn.clear();
         }
         _protectBtn = null;
         if(_packageBtn)
         {
            _packageBtn.clear();
         }
         _packageBtn = null;
         if(_systemBtn)
         {
            _systemBtn.clear();
         }
         _systemBtn = null;
         clearNormalFrame();
         clearInFightFrame();
         if(shopBtn)
         {
            shopBtn.clear();
         }
         _shopBtn = null;
         if(_num1)
         {
            _num1.clear();
         }
         _num1 = null;
         if(_num2)
         {
            _num2.clear();
         }
         _num2 = null;
         _fenHao = null;
         ClearUtil.clearObject(_activityBtn);
         _activityBtn = null;
         ClearUtil.clearObject(_guidepanel);
         _guidepanel = null;
         ClearUtil.clearObject(_collectTimeBtn);
         _collectTimeBtn = null;
         ClearUtil.clearObject(_automaticPetEnterBtn);
         _automaticPetEnterBtn = null;
         ClearUtil.clearObject(_mountEnterBtn);
         _mountEnterBtn = null;
         if(_tryAutomaticPetBtn)
         {
            ClearUtil.clearObject(_tryAutomaticPetBtn);
            _tryAutomaticPetBtn = null;
         }
      }
      
      public function showWave() : void
      {
         initInFightFrame();
      }
      
      public function hideWave() : void
      {
         initNormalFrame();
      }
      
      public function hideTotalWave() : void
      {
         if(_num2 == null)
         {
            initInFightFrame();
         }
         _num2.getShow().visible = false;
      }
      
      public function showTotalWave() : void
      {
         _num2.getShow().visible = true;
      }
      
      public function setWave(param1:int) : void
      {
         _num1.showNum(param1);
      }
      
      public function setAllWave(param1:int) : void
      {
         if(_num2 == null)
         {
            initInFightFrame();
         }
         _num2.showNum(param1);
      }
      
      public function tranToFightState() : void
      {
         initInFightFrame();
         dispatchTranToFightState();
      }
      
      public function tranToNormalState() : void
      {
         initNormalFrame();
         dispatchTranToNormalState();
      }
      
      private function dispatchTranToFightState() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IMiddlePanelListener> = m_middlePanelListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].tranToFightState(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function dispatchTranToNormalState() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IMiddlePanelListener> = m_middlePanelListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].tranToNormalState(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      public function render(param1:Number) : void
      {
         if(_onLineGiftBagBtn)
         {
            _onLineGiftBagBtn.setState();
         }
         if(BroadDataManager.getInstance().isShowOld && _tipDataShow)
         {
            _tipDataShow.getShow().visible = true;
            _tipDataShow.render(GamingUI.getInstance().getEnterFrameTime());
         }
         else if(_tipDataShow)
         {
            _tipDataShow.getShow().visible = false;
         }
         if(_activityRewardBtn && m_rewardNum && _activityRewardBtn.getShow())
         {
            if(ConsumerData.getInstance().getAllNum() > 0)
            {
               m_rewardNum.visible = true;
               m_txtNum.text = String(ConsumerData.getInstance().getAllNum());
               (_activityRewardBtn.getShow() as MovieClip).gotoAndStop("canget");
            }
            else
            {
               m_rewardNum.visible = false;
               (_activityRewardBtn.getShow() as MovieClip).gotoAndStop("nocan");
            }
         }
      }
      
      public function continueGame() : void
      {
      }
      
      public function stopGame() : void
      {
      }
   }
}

