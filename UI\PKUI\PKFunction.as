package UI.PKUI
{
   import UI.Buff.Buff.BuffDrive;
   import UI.GamingUI;
   import UI.InitPlayerData.InitCompleteListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.NicknameSystem.NicknameRankListFunction;
   import UI.Players.Player;
   import UI.RankListFunction;
   import UI.TextTrace.traceText;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import unit4399.events.RankListEvent;
   
   public class PKFunction
   {
      
      private static var _instance:PKFunction = null;
      
      private var _players:Vector.<Player>;
      
      private var _buffss:Vector.<Vector.<BuffDrive>>;
      
      private var rankArr:Array = [{
         "userName":"#天生一对#",
         "score":100,
         "rank":1,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":2,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":3,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":4,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":5,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":6,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":7,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":8,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":9,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":10,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":11,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":12,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":13,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":14,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":15,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":16,
         "index":1
      }];
      
      public function PKFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在啦！ fuck you !");
      }
      
      public static function getInstance() : PKFunction
      {
         if(!_instance)
         {
            _instance = new PKFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         Part1.getInstance().stage.removeEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
         ClearUtil.clearObject(_players);
         _players = null;
         ClearUtil.clearObject(_buffss);
         _buffss = null;
         _instance = null;
      }
      
      public function initPlayerInRankList(param1:XML, param2:uint, param3:Function) : void
      {
         var i:int;
         var j:int;
         var length2:int;
         var length:int;
         var initPlayersData:InitPlayersData;
         var initCompleteListener:InitCompleteListener;
         var xml:XML = param1;
         var idx:uint = param2;
         var fun:Function = param3;
         if(PlayerDataForPK.getInstance().playersInRankListBuffs)
         {
            length = int(PlayerDataForPK.getInstance().playersInRankListBuffs.length);
            i = 0;
            while(i < length)
            {
               if(PlayerDataForPK.getInstance().playersInRankListBuffs[i])
               {
                  length2 = int(PlayerDataForPK.getInstance().playersInRankListBuffs[i].length);
                  j = 0;
                  while(j < length2)
                  {
                     if(PlayerDataForPK.getInstance().playersInRankListBuffs[i][j])
                     {
                        PlayerDataForPK.getInstance().playersInRankListBuffs[i][j].clear();
                     }
                     PlayerDataForPK.getInstance().playersInRankListBuffs[i][j] = null;
                     j++;
                  }
               }
               PlayerDataForPK.getInstance().playersInRankListBuffs[i] = null;
               i++;
            }
            PlayerDataForPK.getInstance().playersInRankListBuffs = null;
         }
         if(PlayerDataForPK.getInstance().playersInRankList)
         {
            length = int(PlayerDataForPK.getInstance().playersInRankList.length);
            i = 0;
            while(i < length)
            {
               if(PlayerDataForPK.getInstance().playersInRankList[i])
               {
                  PlayerDataForPK.getInstance().playersInRankList[i].clear();
               }
               PlayerDataForPK.getInstance().playersInRankList[i] = null;
               i++;
            }
            PlayerDataForPK.getInstance().playersInRankList = null;
         }
         PlayerDataForPK.getInstance().playersInRankListNicknameData = null;
         initPlayersData = new InitPlayersData();
         initCompleteListener = new InitCompleteListener();
         initCompleteListener.addTarget = initPlayersData;
         initCompleteListener.initCompleteFun = function():void
         {
            PlayerDataForPK.getInstance().playersInRankList = new Vector.<Player>();
            PlayerDataForPK.getInstance().playersInRankList.push(initCompleteListener.addTarget.player1);
            initCompleteListener.addTarget.player1 = null;
            PlayerDataForPK.getInstance().playersInRankList.push(initCompleteListener.addTarget.player2);
            initCompleteListener.addTarget.player2 = null;
            PlayerDataForPK.getInstance().playersInRankListBuffs = new Vector.<Vector.<BuffDrive>>();
            PlayerDataForPK.getInstance().playersInRankListBuffs.push(initCompleteListener.addTarget.player1BuffDrives);
            initCompleteListener.addTarget.player1BuffDrives = null;
            PlayerDataForPK.getInstance().playersInRankListBuffs.push(initCompleteListener.addTarget.player2BuffDrives);
            initCompleteListener.addTarget.player2BuffDrives = null;
            PlayerDataForPK.getInstance().playersInRankListNicknameData = initCompleteListener.addTarget.nickNameData;
            initCompleteListener.addTarget.nickNameData = null;
            fun();
         };
         initPlayersData.addInitCompleteListener(initCompleteListener);
         initPlayersData.initPlayerData(xml,idx,1 | 8 | 0x10 | 0x40 | 0x0200 | 0x80 | 0x0800,true);
      }
      
      public function getUserData(param1:String, param2:uint, param3:Function, param4:Function) : void
      {
         var isWantRequestServerData:Boolean;
         var userID:String = param1;
         var idx:uint = param2;
         var getUserDataBeforeFun:Function = param3;
         var getUserDataAfterFun:Function = param4;
         if(Boolean(getUserDataBeforeFun))
         {
            getUserDataBeforeFun();
         }
         isWantRequestServerData = false;
         traceText(!PlayerDataForPK.getInstance().userData[userID] || !PlayerDataForPK.getInstance().userData[userID][idx - 1]);
         if(!PlayerDataForPK.getInstance().userData[userID] || !PlayerDataForPK.getInstance().userData[userID][idx - 1])
         {
            isWantRequestServerData = true;
         }
         if(isWantRequestServerData)
         {
            RankListFunction.getInstance().getUserData(userID,idx,null,function(param1:*):void
            {
               var _loc2_:Function = getUserDataAfterFun;
               if(param1 == null)
               {
                  return;
               }
               if(userID)
               {
                  param1.uId = userID;
                  if(!PlayerDataForPK.getInstance().userData[param1.uId])
                  {
                     PlayerDataForPK.getInstance().userData[param1.uId] = new Vector.<Object>(6);
                  }
                  if(!PlayerDataForPK.getInstance().userData[param1.uId][param1.index - 1])
                  {
                     PlayerDataForPK.getInstance().userData[param1.uId][param1.index - 1] = param1;
                  }
               }
               if(Boolean(_loc2_))
               {
                  _loc2_(param1);
               }
            },null,null,null,null);
            if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                  "apiName":"5",
                  "data":{
                     "data":testXML.copy(),
                     "index":1,
                     "title":"",
                     "datetime":""
                  }
               }));
            }
         }
         else
         {
            getUserDataAfterFun(PlayerDataForPK.getInstance().userData[userID][idx - 1]);
         }
      }
      
      public function getNicknameOneDataByUserName(param1:String, param2:uint, param3:String, param4:uint, param5:Function, param6:Function, param7:Array, param8:Array, param9:Function, param10:Array) : void
      {
         var isWantRequestServerData:Boolean;
         var nickNameType:String = param1;
         var rankListId:uint = param2;
         var userName:String = param3;
         var idx:uint = param4;
         var getOneDataBeforeFun:Function = param5;
         var getOneDataAfterFun:Function = param6;
         var getOneDataBeforeFunParams:Array = param7;
         var getOneDataAfterFunParams:Array = param8;
         var getOneDataFailFun:Function = param9;
         var getOneDataFailFunParams:Array = param10;
         if(Boolean(getOneDataBeforeFun))
         {
            getOneDataBeforeFun.apply(null,getOneDataBeforeFunParams);
         }
         isWantRequestServerData = false;
         traceText(!PlayerDataForPK.getInstance().userNicknameData[rankListId] || !PlayerDataForPK.getInstance().userNicknameData[rankListId][userName] || !PlayerDataForPK.getInstance().userNicknameData[rankListId][userName][idx - 1]);
         if(!PlayerDataForPK.getInstance().userNicknameData[rankListId] || !PlayerDataForPK.getInstance().userNicknameData[rankListId][userName] || !PlayerDataForPK.getInstance().userNicknameData[rankListId][userName][idx - 1])
         {
            isWantRequestServerData = true;
         }
         if(isWantRequestServerData)
         {
            NicknameRankListFunction.getInstance().getOneRankListDataByUserName(nickNameType,rankListId,userName,idx,null,function(param1:*):void
            {
               if(getOneDataAfterFunParams == null)
               {
                  getOneDataAfterFunParams = [];
               }
               getOneDataAfterFunParams.push(param1);
               if(!PlayerDataForPK.getInstance().userNicknameData[rankListId])
               {
                  PlayerDataForPK.getInstance().userNicknameData[rankListId] = {};
               }
               if(!PlayerDataForPK.getInstance().userNicknameData[rankListId][userName])
               {
                  PlayerDataForPK.getInstance().userNicknameData[rankListId][userName] = new Vector.<Object>(6);
               }
               if(!PlayerDataForPK.getInstance().userNicknameData[rankListId][userName][idx - 1])
               {
                  PlayerDataForPK.getInstance().userNicknameData[rankListId][userName][idx - 1] = param1;
               }
               if(Boolean(getOneDataAfterFun))
               {
                  getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
               }
            },null,null,getOneDataFailFun,getOneDataFailFunParams);
         }
         else
         {
            if(getOneDataAfterFunParams == null)
            {
               getOneDataAfterFunParams = [];
            }
            getOneDataAfterFunParams.push(PlayerDataForPK.getInstance().userNicknameData[rankListId][userName][idx - 1]);
            getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
         }
      }
      
      public function getOneRankListDataByUserName(param1:int, param2:String, param3:Function, param4:Function) : void
      {
         if(Boolean(param3))
         {
            param3();
         }
         RankListFunction.getInstance().getOneRankListDataByUserName(param1,param2,null,param4,null,null,null,null);
         if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "data":[{
                  "userName":GameData.getInstance().getLoginReturnData().getName(),
                  "uId":"test123",
                  "score":100,
                  "rank":int(XMLSingle.getInstance().dataXML.testPKRankListRank[0].@rank),
                  "index":GameData.getInstance().getSaveFileData().index
               }],
               "apiName":"1"
            }));
         }
      }
      
      public function getRankListData(param1:int, param2:Function, param3:Function, param4:int, param5:int) : void
      {
         var i:int;
         var length:int;
         var isWantRequestServerData:Boolean;
         var arr:Array;
         var object:Object;
         var arr2:Array;
         var rId:int = param1;
         var getBeforeFun:Function = param2;
         var getAfterFun:Function = param3;
         var pageNum:int = param4;
         var pageSize:int = param5;
         if(Boolean(getBeforeFun))
         {
            getBeforeFun();
         }
         length = pageSize;
         isWantRequestServerData = false;
         i = 0;
         while(i < length)
         {
            if(!PlayerDataForPK.getInstance().rankListInfo[(pageNum - 1) * pageSize + i])
            {
               isWantRequestServerData = true;
            }
            i++;
         }
         traceText(isWantRequestServerData);
         if(isWantRequestServerData)
         {
            RankListFunction.getInstance().getRankListData(rId,pageNum,pageSize,null,function(param1:*):void
            {
               var _loc2_:Function = getAfterFun;
               if(param1 == null || param1.length == 0)
               {
                  _loc2_([]);
                  return;
               }
               length = param1.length;
               i = 0;
               while(i < length)
               {
                  PlayerDataForPK.getInstance().rankListInfo[(pageNum - 1) * pageSize + i] = param1[i];
                  traceText("第" + (pageNum - 1) * pageSize + i + "的扩展数据:","分数：" + param1[i].score,"扩展字段：" + param1[i].extra);
                  i++;
               }
               if(Boolean(_loc2_))
               {
                  _loc2_(param1);
               }
            },null,null,null,null);
            if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               arr = [];
               length = pageSize;
               i = 0;
               while(i < length)
               {
                  object = rankArr[(pageNum - 1) * pageSize + i];
                  if(object)
                  {
                     arr.push({
                        "userName":object.userName,
                        "score":object.score,
                        "rank":object.rank,
                        "index":object.index,
                        "uId":"test123"
                     });
                     object = null;
                  }
                  i++;
               }
               Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                  "data":arr,
                  "apiName":"4"
               }));
            }
         }
         else
         {
            arr2 = [];
            i = 0;
            while(i < length)
            {
               arr2.push(PlayerDataForPK.getInstance().rankListInfo[(pageNum - 1) * pageSize + i]);
               i++;
            }
            getAfterFun(arr2);
         }
      }
      
      public function get testXML() : XML
      {
         try
         {
            return XMLSingle.getInstance().dataXML.testSave[0].root[0];
         }
         catch(error:Error)
         {
            var _loc3_:* = null;
         }
         return _loc3_;
      }
   }
}

