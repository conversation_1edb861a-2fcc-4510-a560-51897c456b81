package YJFY.World
{
   import YJFY.Entity.IEntity;
   
   public class WorldListener implements IWorldListener
   {
      
      public var addAEntityToWorldFun:Function;
      
      public function WorldListener()
      {
         super();
      }
      
      public function clear() : void
      {
         addAEntityToWorldFun = null;
      }
      
      public function addAEntityToWorld(param1:World, param2:IEntity) : void
      {
         if(<PERSON><PERSON><PERSON>(addAEntityToWorldFun))
         {
            addAEntityToWorldFun(param1,param2);
         }
      }
   }
}

