package UI
{
   import UI.AutomaticPetPanel.AutomaticPetInforPanel;
   import UI.AutomaticPetPanel.UseContractSuccessInforPanel;
   import UI.Button.QuitBtn4;
   import UI.Button.SwitchBtn.ChangEBtn;
   import UI.Button.SwitchBtn.DragonBtn;
   import UI.Button.SwitchBtn.ErLangShenBtn;
   import UI.Button.SwitchBtn.FoxBtn;
   import UI.Button.SwitchBtn.HouyiBtn;
   import UI.Button.SwitchBtn.MonkeyBtn;
   import UI.Button.SwitchBtn.PackageBtn;
   import UI.Button.SwitchBtn.ShopBtn;
   import UI.Button.SwitchBtn.SkillBtn;
   import UI.Button.SwitchBtn.StorageBtn;
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Button.SwitchBtn.TaskBtn;
   import UI.Button.SwitchBtn.TieShanBtn;
   import UI.Button.SwitchBtn.ZiXiaBtn;
   import UI.ConsumptionGuide.GuideBuyPopUpBox;
   import UI.DanMedicinePanel.DanMedicineFunction;
   import UI.EquipEquipmentFuns.EquipEquipmentFunction;
   import UI.EquipEquipmentFuns.IEquipEqListener;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.EquipmentCells.UsedEquipmentCell;
   import UI.EquipmentDetailShow.EquipmentDetailShow;
   import UI.EquipmentDetailShow.EquipmentDetailShowFactory;
   import UI.EquipmentMakeAndUpgrade.EquipMagicInherit;
   import UI.EquipmentMakeAndUpgrade.EquipmentMagicCreat;
   import UI.EquipmentMakeAndUpgrade.IsCanPutInfo;
   import UI.EquipmentMakeAndUpgrade.MakeAndUpgradePanel;
   import UI.Equipments.EquipmentVO.ContractEquipmntVO;
   import UI.Equipments.EquipmentVO.EggEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.MedalEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.ExternalUI.PlayerPanel;
   import UI.HatchPanel.HatchPanel;
   import UI.InformationPanel.InformationPanel;
   import UI.InformationPanel.PlayerInformationPanels.PlayerInformationPanel_Inner;
   import UI.MessageBox.MessageBoxEngine;
   import UI.Other.LockBitmapData;
   import UI.Other.SellEquipmentsPanel;
   import UI.Other.UseMultiEqPanel;
   import UI.PKUI.PlayerDataForPK;
   import UI.PackageAndStorage.StorageArea;
   import UI.Pets.PetPanel;
   import UI.Players.Player;
   import UI.Protect.ProtectData;
   import UI.ShiTu.TuDiPanel;
   import UI.Shop.GetEquipmentSellMoney;
   import UI.Shop.Shop;
   import UI.SkillSurface.SkillPanel;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Task.TaskFunction;
   import UI.Task.TaskVO.LimtingTimeAccumulatedTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.Task.TasksManager;
   import UI.TaskPanel.InTaskPanel;
   import UI.TidyEquipments.TidyEquipmentEvent;
   import UI.TidyEquipments.TidyEquipments;
   import UI.UIBackground.UIBackground;
   import UI.UIInterface.IOneValueEquipmentVO;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.Mount.MountUI.MountSmallPanel;
   import UI2.broadcast.SubmitFunction;
   import UI2.medalPanel.MedalFunction;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.GameData;
   import YJFY.LevelMode1.Level39.Castellan;
   import YJFY.PKMode.LocalPKRankListAPI;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class MyControlPanel extends MySprite implements IEquipEqListener
   {
      
      public var shopBtn:ShopBtn;
      
      public var storageBtn:StorageBtn;
      
      public var packageBtn:PackageBtn;
      
      public var skillPanelBtn:SkillBtn;
      
      public var taskBtn:TaskBtn;
      
      public var currentPlayer:Player;
      
      public var _currentState:int;
      
      public var npcNameBitmap:Bitmap;
      
      private var m_loadfirst:Boolean = false;
      
      public var quitBtn:QuitBtn4;
      
      private var _player1:Player;
      
      private var _player2:Player;
      
      private var _topLayer:Sprite;
      
      private var _UIBackground:UIBackground;
      
      private const SHOP_STATE:int = 0;
      
      private const PACKAGE_STATE:int = 1;
      
      private const STORAGE_STATE:int = 2;
      
      private const SKILL_PANEL_STATE:int = 3;
      
      private const PET_PANEL_STATE:int = 4;
      
      private const PLAYER_PANEL_STATE:int = 5;
      
      private const TASK_PANEL_STATE:int = 6;
      
      private const TUDI_PANEL_STATE:int = 7;
      
      private const AUTO_PET_PANEL_STATE:int = 8;
      
      private const MOUNT_PANEL_STATE:int = 9;
      
      private var _playerInfomationPanel:PlayerInformationPanel_Inner;
      
      private var _package:StorageArea;
      
      private var _storage:StorageArea;
      
      private var _skillPanel:SkillPanel;
      
      private var _shop:Shop;
      
      private var _owner:String;
      
      private var _informationPanel:InformationPanel;
      
      private var _petPanel:PetPanel;
      
      private var _tuDiPanel:TuDiPanel;
      
      private var _autoPetPanel:AutomaticPetInforPanel;
      
      private var _mountSmallPanel:MountSmallPanel;
      
      private var _makeAndUpgradePanel:MakeAndUpgradePanel;
      
      private var _equipMagicCreate:EquipmentMagicCreat;
      
      private var _eqMagicInheritPanel:EquipMagicInherit;
      
      private var _hatchPanel:HatchPanel;
      
      private var _publicStorage:StorageArea;
      
      private var _inTaskPanel:InTaskPanel;
      
      private var _oneLayer:Sprite;
      
      private var _player1_Btn:SwitchBtn;
      
      private var _player2_Btn:SwitchBtn;
      
      private var _popUpBox:GuideBuyPopUpBox;
      
      private var _tiDyEquipments:TidyEquipments;
      
      private var _equipmentDetailShow:EquipmentDetailShow;
      
      private var _useContractSuccessInforPanel:UseContractSuccessInforPanel;
      
      private var _useMultiEqPanel:UseMultiEqPanel;
      
      private var _sellEquipmentsPanel:SellEquipmentsPanel;
      
      private var _equipEquipmentFunction:EquipEquipmentFunction;
      
      public var _havegoods:Boolean;
      
      private var m_delayTime:int;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_localRankListAPI:LocalPKRankListAPI;
      
      public function MyControlPanel()
      {
         super();
         m_loadfirst = false;
         addEventListener("addedToStage",addToStage,false,0,true);
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
      }
      
      public function initControlPanel(param1:Player = null, param2:Player = null) : void
      {
         if(!param1)
         {
            throw new Error("第一角色不能为空！");
         }
         _player1 = param1;
         currentPlayer = _player1;
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               _currentState = 0;
               break;
            case "BaiLongMa":
               _currentState = 1;
               break;
            case "ErLangShen":
               _currentState = 2;
               break;
            case "ChangE":
               _currentState = 3;
               break;
            case "Fox":
               _currentState = 4;
               break;
            case "TieShan":
               _currentState = 5;
               break;
            case "Houyi":
               _currentState = 6;
               break;
            case "ZiXia":
               _currentState = 7;
               break;
            default:
               throw new Error("不存在的人物类型！");
         }
         if(param2)
         {
            _player2 = param2;
            switch(param1.playerVO.playerType)
            {
               case "SunWuKong":
                  _player1_Btn = new MonkeyBtn();
                  _player1_Btn.addEventListener("switchToMonkey",switchToPlayer_one,false,0,true);
                  break;
               case "BaiLongMa":
                  _player1_Btn = new DragonBtn();
                  _player1_Btn.addEventListener("switchToDragon",switchToPlayer_one,false,0,true);
                  break;
               case "ErLangShen":
                  _player1_Btn = new ErLangShenBtn();
                  _player1_Btn.addEventListener("switchToErLangShen",switchToPlayer_one,false,0,true);
                  break;
               case "ChangE":
                  _player1_Btn = new ChangEBtn();
                  _player1_Btn.addEventListener("switchToChangE",switchToPlayer_one,false,0,true);
                  break;
               case "Fox":
                  _player1_Btn = new FoxBtn();
                  _player1_Btn.addEventListener("switchToFox",switchToPlayer_one,false,0,true);
                  break;
               case "TieShan":
                  _player1_Btn = new TieShanBtn();
                  _player1_Btn.addEventListener("switchToTieShan",switchToPlayer_one,false,0,true);
                  break;
               case "Houyi":
                  _player1_Btn = new HouyiBtn();
                  _player1_Btn.addEventListener("switchToHouyi",switchToPlayer_one,false,0,true);
                  break;
               case "ZiXia":
                  _player1_Btn = new ZiXiaBtn();
                  _player1_Btn.addEventListener("switchToZiXia",switchToPlayer_one,false,0,true);
                  break;
               default:
                  throw new Error("不存在的人物类型！");
            }
            switch(param2.playerVO.playerType)
            {
               case "SunWuKong":
                  _player2_Btn = new MonkeyBtn();
                  _player2_Btn.addEventListener("switchToMonkey",switchToPlayer_two,false,0,true);
                  break;
               case "BaiLongMa":
                  _player2_Btn = new DragonBtn();
                  _player2_Btn.addEventListener("switchToDragon",switchToPlayer_two,false,0,true);
                  break;
               case "ErLangShen":
                  _player2_Btn = new ErLangShenBtn();
                  _player2_Btn.addEventListener("switchToErLangShen",switchToPlayer_two,false,0,true);
                  break;
               case "ChangE":
                  _player2_Btn = new ChangEBtn();
                  _player2_Btn.addEventListener("switchToChangE",switchToPlayer_two,false,0,true);
                  break;
               case "Fox":
                  _player2_Btn = new FoxBtn();
                  _player2_Btn.addEventListener("switchToFox",switchToPlayer_two,false,0,true);
                  break;
               case "TieShan":
                  _player2_Btn = new TieShanBtn();
                  _player2_Btn.addEventListener("switchToTieShan",switchToPlayer_two,false,0,true);
                  break;
               case "Houyi":
                  _player2_Btn = new HouyiBtn();
                  _player2_Btn.addEventListener("switchToHouyi",switchToPlayer_two,false,0,true);
                  break;
               case "ZiXia":
                  _player2_Btn = new ZiXiaBtn();
                  _player2_Btn.addEventListener("switchToZiXia",switchToPlayer_two,false,0,true);
                  break;
               default:
                  throw new Error("不存在的人物类型！");
            }
            _player1_Btn.x = 37.85;
            _player1_Btn.y = 29.4;
            _player2_Btn.x = 183.85;
            _player2_Btn.y = 29.3;
            addChild(_player1_Btn);
            addChild(_player2_Btn);
            _player1_Btn.init(false);
            _player2_Btn.init(true);
         }
         quitBtn = new QuitBtn4();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn4";
         quitBtn.isLoaded = false;
         _oneLayer = new Sprite();
         addChild(_oneLayer);
         _topLayer = new Sprite();
         addChild(_topLayer);
         _hatchPanel = new HatchPanel();
         _hatchPanel.x = 0;
         _hatchPanel.y = 0;
         addChild(_hatchPanel);
         _hatchPanel.init(this);
         _package = new StorageArea(0);
         _package.x = 500;
         _package.y = 150;
         _package.name = "package";
         addChild(_package);
         _storage = new StorageArea(1);
         _storage.x = 60;
         _storage.y = 143;
         _storage.name = "storage";
         addChild(_storage);
         _shop = new Shop();
         _shop.x = 10;
         _shop.y = 115;
         _shop.name = "shop";
         addChild(_shop);
         _informationPanel = new InformationPanel();
         _informationPanel.x = -30;
         _informationPanel.y = 110;
         addChild(_informationPanel);
         _playerInfomationPanel = new PlayerInformationPanel_Inner();
         _playerInfomationPanel.x = 20;
         _playerInfomationPanel.y = 55;
         _playerInfomationPanel.name = "playerInformationPanel";
         addChild(_playerInfomationPanel);
         _playerInfomationPanel.mouseEnabled = false;
         _petPanel = new PetPanel();
         _petPanel.x = 30;
         _petPanel.y = 58.15;
         _petPanel.name = "petPanel";
         addChild(_petPanel);
         _tuDiPanel = new TuDiPanel();
         _tuDiPanel.x = 63;
         _tuDiPanel.y = 140;
         _tuDiPanel.name = "tuDiPanel";
         addChild(_tuDiPanel);
         _autoPetPanel = new AutomaticPetInforPanel();
         _autoPetPanel.init();
         _autoPetPanel.x = 40;
         _autoPetPanel.y = 110;
         _autoPetPanel.name = "autoPetPanel";
         addChild(_autoPetPanel);
         _mountSmallPanel = new MountSmallPanel();
         _mountSmallPanel.init();
         _mountSmallPanel.x = 40;
         _mountSmallPanel.y = 110;
         _mountSmallPanel.name = "mountSmallPanel";
         addChild(_mountSmallPanel);
         _makeAndUpgradePanel = new MakeAndUpgradePanel();
         _makeAndUpgradePanel.x = 0;
         _makeAndUpgradePanel.y = 50;
         addChild(_makeAndUpgradePanel);
         _makeAndUpgradePanel.init(this);
         _equipMagicCreate = new EquipmentMagicCreat();
         _equipMagicCreate.x = 0;
         _equipMagicCreate.y = 50;
         addChild(_equipMagicCreate);
         _equipMagicCreate.init(this);
         _eqMagicInheritPanel = new EquipMagicInherit();
         _eqMagicInheritPanel.x = 0;
         _eqMagicInheritPanel.y = 50;
         addChild(_eqMagicInheritPanel);
         _eqMagicInheritPanel.init(currentPlayer);
         _skillPanel = new SkillPanel();
         _skillPanel.x = 35.25;
         _skillPanel.y = 60;
         _skillPanel.name = "skillPanel";
         addChild(_skillPanel);
         _publicStorage = new StorageArea(2);
         _publicStorage.x = 60;
         _publicStorage.y = 143;
         addChild(_publicStorage);
         _inTaskPanel = new InTaskPanel();
         _inTaskPanel.x = 60;
         _inTaskPanel.y = 110;
         addChild(_inTaskPanel);
         addStorageDrag(_topLayer);
         addPackageDrag(_topLayer);
         _tiDyEquipments = new TidyEquipments();
         GamingUI.getInstance().refresh(0x0200 | 1 | 2 | 8 | 4 | 0x10 | 0x20 | 0x1000 | 0x040000 | 0x200000);
         packageBtn.init(false);
         packageBtn.x += 90;
         shopBtn.init(true);
         taskBtn.init(false);
         taskBtn.visible = false;
         storageBtn.init(true);
         skillPanelBtn.init(true);
         skillPanelBtn.x += 90;
         _package.visible = true;
         _playerInfomationPanel.visible = true;
         _informationPanel.visible = true;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _makeAndUpgradePanel.visible = false;
         _equipMagicCreate.visible = false;
         _eqMagicInheritPanel.visible = false;
         _hatchPanel.visible = false;
         _publicStorage.visible = false;
         _UIBackground = new UIBackground();
         addChildAt(_UIBackground,0);
         _havegoods = false;
         npcNameBitmap = new Bitmap();
         npcNameBitmap.x = 736;
         npcNameBitmap.y = -10;
         addChild(npcNameBitmap);
         addChildAt(_topLayer,numChildren);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(_package)
         {
            _package.render();
         }
         if(_storage)
         {
            _storage.render();
         }
         if(_publicStorage)
         {
            _publicStorage.render();
         }
         if(_hatchPanel)
         {
            _hatchPanel.render(param1);
         }
         if(_equipMagicCreate)
         {
            _equipMagicCreate.render(param1);
         }
         if(_package.isLoaded() && m_loadfirst == false)
         {
            if(m_delayTime > 0)
            {
               clearTimeout(m_delayTime);
            }
            m_delayTime = setTimeout(calldelay,1000);
            m_loadfirst = true;
         }
      }
      
      private function calldelay() : void
      {
         quitBtn.isLoaded = _package.isLoaded();
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
      }
      
      public function addInterimTask() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc6_:Boolean = false;
            var _loc8_:MTaskVO = null;
            var _loc4_:DisplayObject = null;
            var _loc10_:Vector.<MTaskVO> = TasksManager.getInstance().acceptedEveryDayTaskVOs.concat(TasksManager.getInstance().acceptedActivityTaskVOs).concat(TasksManager.getInstance().wasteTaskVOs);
            var _loc9_:int = 0;
            var _loc5_:int = int(_loc10_.length);
            var _loc7_:Boolean = false;
            _loc6_ = false;
            _loc9_ = 0;
            while(_loc9_ < _loc5_)
            {
               if(_loc10_[_loc9_].id == 13002)
               {
                  _loc6_ = true;
                  break;
               }
               _loc9_++;
            }
            var _loc3_:Number = new TimeUtil().timeInterval(XMLSingle.getInstance().taskXML.Task.item.(@id == 13002)[0].@formerData,param1);
            var _loc2_:Number = new TimeUtil().timeInterval(param1,XMLSingle.getInstance().taskXML.Task.item.(@id == 13002)[0].@latterData);
            if(_loc3_ < 0 || _loc2_ < 0)
            {
               _loc7_ = true;
            }
            if(!_loc6_ && !_loc7_)
            {
               _loc8_ = XMLSingle.getTask(13002,XMLSingle.getInstance().taskXML);
               _loc8_.state = 0;
               (_loc8_ as LimtingTimeAccumulatedTaskVO).deteDate = MyFunction.getInstance().splitTimeString(param1);
               TasksManager.getInstance().acceptedActivityTaskVOs.push(_loc8_);
            }
            if(!_loc7_)
            {
               _loc4_ = MyFunction2.returnShowByClassName("HolidayTaskArrows");
               _loc4_.x = -681.05;
               _loc4_.y = -22;
            }
            _inTaskPanel.refreshTaskList();
         },showWarningBox);
      }
      
      public function initPublicStorage(param1:Vector.<EquipmentVO>) : void
      {
         _publicStorage.refreshEquipments(param1);
      }
      
      public function addGoodsToShop() : void
      {
         _shop.addGoodsToShop();
      }
      
      public function setSkillPanelMoney(param1:int) : void
      {
         _skillPanel.moneyText.text = param1.toString();
      }
      
      public function addPackageDrag(param1:Sprite) : void
      {
         _package.addDragEquipment(param1);
         _package.addDragTarget(_playerInfomationPanel);
         _package.addDragTarget(_shop);
         _package.addDragTarget(_petPanel);
         _package.addDragTarget(_storage);
         _package.addDragTarget(_publicStorage);
         _package.addDragTarget(_makeAndUpgradePanel);
         _package.addDragTarget(_equipMagicCreate);
         _package.addDragTarget(_eqMagicInheritPanel);
      }
      
      public function addStorageDrag(param1:Sprite) : void
      {
         _storage.addDragEquipment(param1);
         _storage.addDragTarget(_package);
         _publicStorage.addDragEquipment(param1);
         _publicStorage.addDragTarget(_package);
      }
      
      public function get oneLayer() : Sprite
      {
         return _oneLayer;
      }
      
      public function get topLayer() : Sprite
      {
         return _topLayer;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(m_rankListAPIListener)
         {
            ClearUtil.clearObject(m_rankListAPIListener);
         }
         m_rankListAPIListener = null;
         if(m_localRankListAPI)
         {
            ClearUtil.clearObject(m_localRankListAPI);
         }
         m_localRankListAPI = null;
         _player1 = null;
         _player2 = null;
         currentPlayer = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         if(m_delayTime > 0)
         {
            clearTimeout(m_delayTime);
         }
         if(shopBtn)
         {
            shopBtn.clear();
         }
         shopBtn = null;
         if(storageBtn)
         {
            storageBtn.clear();
         }
         storageBtn = null;
         if(packageBtn)
         {
            packageBtn.clear();
         }
         packageBtn = null;
         if(skillPanelBtn)
         {
            skillPanelBtn.clear();
         }
         skillPanelBtn = null;
         if(taskBtn)
         {
            taskBtn.clear();
         }
         taskBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(npcNameBitmap)
         {
            if(npcNameBitmap.bitmapData)
            {
               npcNameBitmap.bitmapData.dispose();
            }
            npcNameBitmap.bitmapData = null;
         }
         npcNameBitmap = null;
         _topLayer = null;
         if(_UIBackground)
         {
            _UIBackground.clear();
         }
         _UIBackground = null;
         if(_playerInfomationPanel)
         {
            _playerInfomationPanel.clear();
         }
         _playerInfomationPanel = null;
         if(_package)
         {
            _package.clear();
         }
         _package = null;
         if(_storage)
         {
            _storage.clear();
         }
         _storage = null;
         if(_skillPanel)
         {
            _skillPanel.clear();
         }
         _skillPanel = null;
         if(_shop)
         {
            _shop.clear();
         }
         _shop = null;
         if(_informationPanel)
         {
            _informationPanel.clear();
         }
         _informationPanel = null;
         if(_petPanel)
         {
            _petPanel.clear();
         }
         _petPanel = null;
         if(_tuDiPanel)
         {
            _tuDiPanel.clear();
         }
         _tuDiPanel = null;
         ClearUtil.clearObject(_autoPetPanel);
         _autoPetPanel = null;
         ClearUtil.clearObject(_mountSmallPanel);
         _mountSmallPanel = null;
         if(_makeAndUpgradePanel)
         {
            _makeAndUpgradePanel.clear();
         }
         _makeAndUpgradePanel = null;
         if(_equipMagicCreate)
         {
            _equipMagicCreate.clear();
         }
         _equipMagicCreate = null;
         if(_eqMagicInheritPanel)
         {
            _eqMagicInheritPanel.clear();
         }
         _eqMagicInheritPanel = null;
         if(_publicStorage)
         {
            _publicStorage.clear();
         }
         _publicStorage = null;
         if(_inTaskPanel)
         {
            _inTaskPanel.clear();
         }
         _inTaskPanel = null;
         ClearUtil.clearObject(_hatchPanel);
         _hatchPanel = null;
         ClearUtil.clearObject(_useContractSuccessInforPanel);
         _useContractSuccessInforPanel = null;
         _oneLayer = null;
         if(_player1_Btn)
         {
            _player1_Btn.clear();
         }
         _player1_Btn = null;
         if(_player2_Btn)
         {
            _player2_Btn.clear();
         }
         _player2_Btn = null;
         if(_popUpBox)
         {
            _popUpBox.clear();
         }
         _popUpBox = null;
         WarningBoxSingle.getInstance().clear();
         if(_equipEquipmentFunction)
         {
            _equipEquipmentFunction.clear();
         }
         _equipEquipmentFunction = null;
         if(_tiDyEquipments)
         {
            _tiDyEquipments.clear();
         }
         _tiDyEquipments = null;
         ClearUtil.clearObject(_equipmentDetailShow);
         _equipmentDetailShow = null;
         ClearUtil.clearObject(_useMultiEqPanel);
         _useMultiEqPanel = null;
         ClearUtil.clearObject(_sellEquipmentsPanel);
         _sellEquipmentsPanel = null;
      }
      
      public function removeEquipmentFromPackageCell(param1:IEquipmentCell) : void
      {
         var _loc2_:int = int(_package.area.equipmentCells.indexOf(param1));
         var _loc3_:EquipmentVO = _package.removeEquipmentVO(_loc2_);
         GamingUI.getInstance().refresh(2);
      }
      
      public function addEquipmentToPackage(param1:EquipmentVO, param2:String = null, param3:Function = null) : void
      {
         var _loc4_:Player = null;
         if(_player1.playerVO.playerType == param2)
         {
            _loc4_ = _player1;
         }
         else if(Boolean(_player2) && _player2.playerVO.playerType == param2)
         {
            _loc4_ = _player2;
         }
         else
         {
            _loc4_ = currentPlayer;
         }
         if(MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_loc4_.playerVO.packageEquipmentVOs,param1,2))
         {
            if(param3 != null)
            {
               param3();
            }
            GamingUI.getInstance().refresh(2);
         }
         else
         {
            showWarningBox("背包已满！",0);
         }
      }
      
      public function refresh(param1:int) : void
      {
         if(param1 & 1)
         {
            _playerInfomationPanel.setPlayerPanel(currentPlayer);
            _autoPetPanel.setAutomaticPetVO(currentPlayer.playerVO.automaticPetVO);
            _mountSmallPanel.setPlayerVO(currentPlayer.playerVO);
         }
         if(param1 & 2)
         {
            _package.refreshEquipments(currentPlayer.playerVO.packageEquipmentVOs);
         }
         if(param1 & 4)
         {
            _storage.refreshEquipments(currentPlayer.playerVO.storageEquipmentVOs);
         }
         if(param1 & 0x10)
         {
            _petPanel.refresh(currentPlayer.playerVO.pet);
         }
         if(param1 & 0x040000)
         {
            _tuDiPanel.refresh(currentPlayer.playerVO.tuDiVO);
            _tuDiPanel.currentPlayer = currentPlayer;
         }
         if(!(param1 & 0x100000))
         {
         }
         if(param1 & 8)
         {
            _skillPanel.refreshSkills(currentPlayer);
         }
         if(param1 & 0x20)
         {
            _package.setPKPointText(PlayerDataForPK.getInstance().pkPoint);
            _package.setMoneyText(currentPlayer.playerVO.money);
            _storage.setMoneyText(currentPlayer.playerVO.money);
            _skillPanel.moneyText.text = MyFunction2.transformMoneyConcise1(currentPlayer.playerVO.money);
         }
         if(param1 & 0x0400)
         {
            storageBtn.setIsLock(!currentPlayer.vipVO.isOpenStorage && !ProtectData.getInstance().isOpenStorage,LockBitmapData);
            shopBtn.setIsLock(!currentPlayer.vipVO.isOpenShop && !ProtectData.getInstance().isOpenShop,LockBitmapData);
            PackageEquipmentCell.packageBtnSate = !currentPlayer.vipVO.isOpenStorage && !ProtectData.getInstance().isOpenStorage ? 2 : 2 | 4;
            PackageEquipmentCell.packageBtnSate = PackageEquipmentCell.packageBtnSate | 1 | 0x80 | 0x0100;
            if(storageBtn.isLock && !storageBtn.isClickAble && packageBtn.visible)
            {
               switchToPackage();
            }
            else if(shopBtn.isLock && !shopBtn.isClickAble && packageBtn.visible)
            {
               switchToPackage();
            }
         }
         if(param1 & 0x1000)
         {
            _makeAndUpgradePanel.player = currentPlayer;
         }
         if(param1 & 0x200000)
         {
            _equipMagicCreate.player = currentPlayer;
         }
         if(param1 & 0x200000)
         {
            _eqMagicInheritPanel.player = currentPlayer;
         }
         if(param1 & 0x020000)
         {
            if(_playerInfomationPanel.danMedicinePanel)
            {
               _playerInfomationPanel.danMedicinePanel.player = currentPlayer;
            }
            if(_playerInfomationPanel.danMedicinePanel)
            {
               _playerInfomationPanel.danMedicinePanel.refreshPanel(currentPlayer.playerVO.attackDanMedicineEquipmentVOGrid,currentPlayer.playerVO.defenceDanMedicineEquipmentVOGrid);
            }
         }
      }
      
      public function switchToEqMakeAndUpgradePanel(param1:MouseEvent = null) : void
      {
         _havegoods = false;
         _package.visible = true;
         _playerInfomationPanel.visible = false;
         _informationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _publicStorage.visible = false;
         _makeAndUpgradePanel.visible = true;
         _equipMagicCreate.visible = false;
         _eqMagicInheritPanel.visible = false;
         _hatchPanel.visible = false;
         shopBtn.visible = false;
         packageBtn.visible = false;
         storageBtn.visible = false;
         skillPanelBtn.visible = false;
         taskBtn.visible = false;
         PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
      }
      
      public function switchToEqMagicPanel(param1:MouseEvent = null) : void
      {
         _havegoods = false;
         _package.visible = true;
         _playerInfomationPanel.visible = false;
         _informationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _publicStorage.visible = false;
         _makeAndUpgradePanel.visible = false;
         _equipMagicCreate.visible = true;
         _eqMagicInheritPanel.visible = false;
         _hatchPanel.visible = false;
         shopBtn.visible = false;
         packageBtn.visible = false;
         storageBtn.visible = false;
         skillPanelBtn.visible = false;
         taskBtn.visible = false;
         PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
      }
      
      public function switchToEqInheritPanel(param1:MouseEvent = null) : void
      {
         _havegoods = false;
         _package.visible = false;
         _playerInfomationPanel.visible = false;
         _informationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _publicStorage.visible = false;
         _makeAndUpgradePanel.visible = false;
         _equipMagicCreate.visible = false;
         _eqMagicInheritPanel.visible = true;
         _hatchPanel.visible = false;
         shopBtn.visible = false;
         packageBtn.visible = false;
         storageBtn.visible = false;
         skillPanelBtn.visible = false;
         taskBtn.visible = false;
         PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
      }
      
      public function switchToPublicStorage(param1:MouseEvent = null) : void
      {
         _package.visible = true;
         _playerInfomationPanel.visible = false;
         _informationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _makeAndUpgradePanel.visible = false;
         _equipMagicCreate.visible = false;
         _eqMagicInheritPanel.visible = false;
         _hatchPanel.visible = false;
         _publicStorage.visible = true;
         shopBtn.visible = false;
         packageBtn.visible = false;
         storageBtn.visible = false;
         skillPanelBtn.visible = false;
         taskBtn.visible = false;
         PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
      }
      
      public function switchToHatchPanel() : void
      {
         _havegoods = false;
         _package.visible = true;
         _playerInfomationPanel.visible = false;
         _informationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _shop.visible = false;
         _storage.visible = false;
         _skillPanel.visible = false;
         _inTaskPanel.visible = false;
         _makeAndUpgradePanel.visible = false;
         _equipMagicCreate.visible = false;
         _eqMagicInheritPanel.visible = false;
         _hatchPanel.visible = true;
         _publicStorage.visible = false;
         shopBtn.visible = false;
         packageBtn.visible = false;
         storageBtn.visible = false;
         skillPanelBtn.visible = false;
         taskBtn.visible = false;
         PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
         if(npcNameBitmap.bitmapData)
         {
            npcNameBitmap.bitmapData.dispose();
         }
         npcNameBitmap.bitmapData = null;
      }
      
      public function takeOutEquipment() : void
      {
         var _loc3_:* = undefined;
         var _loc2_:* = undefined;
         var _loc1_:* = undefined;
         if(_makeAndUpgradePanel.visible)
         {
            _loc3_ = _makeAndUpgradePanel.putOutEquipmentVO();
            ClearUtil.nullArr(_loc3_,false,false,false);
            GamingUI.getInstance().refresh(2);
         }
         if(_equipMagicCreate.visible)
         {
            _loc2_ = _equipMagicCreate.putOutEquipmentVO();
            ClearUtil.nullArr(_loc2_,false,false,false);
            GamingUI.getInstance().refresh(2);
         }
         if(_eqMagicInheritPanel.visible)
         {
            _loc1_ = _eqMagicInheritPanel.putOutEquipmentVO();
            ClearUtil.nullArr(_loc1_,false,false,false);
            GamingUI.getInstance().refresh(2);
         }
      }
      
      public function set isVIPState(param1:Boolean) : void
      {
         _playerInfomationPanel.isVIPState = param1;
      }
      
      public function get player1_Btn() : SwitchBtn
      {
         return _player1_Btn;
      }
      
      public function get player2_Btn() : SwitchBtn
      {
         return _player2_Btn;
      }
      
      public function get myPackage() : StorageArea
      {
         return _package;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("switchToPackage",switchToPackage,true,0,true);
         addEventListener("switchToShop",switchToShop,true,0,true);
         addEventListener("switchToStorage",switchToStorage,true,0,true);
         addEventListener("switchToSkill",switchToSkillPanel,true,0,true);
         addEventListener("switchToTask",switchToTask,true,0,true);
         addEventListener("clickEquipBtn",equipEquipment,true,0,true);
         addEventListener("clickSellBtn",sellEquipment,true,0,true);
         addEventListener("clickSaveEquipmentBtn",saveEquipment,true,0,true);
         addEventListener("clickTakeOutBtn",getOutEquipment,true,0,true);
         addEventListener("clickPutInBtn",putInEquipment,true,0,true);
         addEventListener("clickTakeDownBtn",takeDownEquipment,true,0,true);
         addEventListener("clickFastUseBtn",fastUseEquipment,true,0,true);
         addEventListener("clickDetailBtn",detailShow,true,0,true);
         addEventListener("buyEquipment",buyEquipment,true,0,true);
         addEventListener("dragSellEquipment",dragSellEquipment,true,0,true);
         addEventListener("clickUpgradeBtn",upGrade,true,0,true);
         addEventListener("clickPlayerBtn",switchToPlayer,true,0,true);
         addEventListener("clickPetBtn",switchToPet,true,0,true);
         addEventListener("clickTuDiBtn",switchToTuDi,true,0,true);
         addEventListener("clickAutoPetBtn",switchToAutoPet,true,0,true);
         addEventListener("clickMountBtn",switchToMount,true,0,true);
         addEventListener("clickTakeBackPetBtn",takeBackpet,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("showWarningBox",showWarningBox2,true,0,true);
         addEventListener("showGuideBox",showBox,true,0,true);
         addEventListener("tidyEquipments",tiDyEquipments,true,0,true);
         addEventListener("clickAKeySoldBtn",aKeySell,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("switchToPackage",switchToPackage,true);
         removeEventListener("switchToShop",switchToShop,true);
         removeEventListener("switchToStorage",switchToStorage,true);
         removeEventListener("switchToTask",switchToTask,true);
         removeEventListener("clickEquipBtn",equipEquipment,true);
         removeEventListener("clickSellBtn",sellEquipment,true);
         removeEventListener("clickSaveEquipmentBtn",saveEquipment,true);
         removeEventListener("clickTakeOutBtn",getOutEquipment,true);
         removeEventListener("clickPutInBtn",putInEquipment,true);
         removeEventListener("clickTakeDownBtn",takeDownEquipment,true);
         removeEventListener("clickFastUseBtn",fastUseEquipment,true);
         removeEventListener("clickDetailBtn",detailShow,true);
         removeEventListener("buyEquipment",buyEquipment,true);
         removeEventListener("dragSellEquipment",dragSellEquipment,true);
         removeEventListener("switchToSkill",switchToSkillPanel,true);
         removeEventListener("clickUpgradeBtn",upGrade,true);
         removeEventListener("clickPlayerBtn",switchToPlayer,true);
         removeEventListener("clickPetBtn",switchToPet,true);
         removeEventListener("clickTuDiBtn",switchToTuDi,true);
         removeEventListener("clickAutoPetBtn",switchToAutoPet,true);
         removeEventListener("clickMountBtn",switchToMount,true);
         removeEventListener("clickTakeBackPetBtn",takeBackpet,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("showWarningBox",showWarningBox2,true);
         removeEventListener("showGuideBox",showBox,true);
         removeEventListener("tidyEquipments",tiDyEquipments,true);
         removeEventListener("clickAKeySoldBtn",aKeySell,true);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,currentPlayer);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function switchToPackage(param1:UIBtnEvent = null) : void
      {
         switchToPlayer(null);
      }
      
      public function switchToShop(param1:UIBtnEvent) : void
      {
         setPanel(0);
         if(!param1)
         {
            PackageEquipmentCell.packageBtnSate = 2 | 0x0100;
         }
      }
      
      public function switchToStorage(param1:UIBtnEvent) : void
      {
         setPanel(2);
         if(!param1)
         {
            PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
         }
      }
      
      protected function switchToSkillPanel(param1:UIBtnEvent) : void
      {
         setPanel(3);
      }
      
      protected function switchToTask(param1:UIBtnEvent) : void
      {
         DoPKTask();
      }
      
      private function goTaskPanel() : void
      {
         _inTaskPanel.refreshTaskList();
         setPanel(6);
      }
      
      private function DoPKTask() : void
      {
         if(TaskFunction.getInstance().getTaskIsReceive(13029) || TaskFunction.getInstance().getTaskIsReceive(13030) || TaskFunction.getInstance().getTaskIsReceive(13031))
         {
            Part1.getInstance().showGameWaitShow();
            m_localRankListAPI = new LocalPKRankListAPI();
            Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(m_localRankListAPI);
            m_rankListAPIListener = new RankListAPIListener();
            m_rankListAPIListener.rankListErrorFun = rankListError1;
            m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerRankInfoSuccess;
            Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
            Part1.getInstance().getApi4399().rankListAPI.getOneRankInfo(GameData.MonthCicly ? 1576 : 1583,GameData.getInstance().getLoginReturnData().getName());
         }
         else
         {
            goTaskPanel();
         }
      }
      
      private function getPlayerRankInfoSuccess(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         m_rankListAPIListener.getOneRankInfoSuccessFun = null;
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().hideGameWaitShow();
         var _loc3_:int = param1 ? param1.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1[_loc4_].getUid() == GameData.getInstance().getLoginReturnData().getUid() && param1[_loc4_].getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               _loc2_ = int(param1[_loc4_].getRank());
               if(_loc2_ <= 50 && _loc2_ != 0)
               {
                  TaskFunction.getInstance().addTaskGoalVOByString("PKmakeup1");
               }
               else if(_loc2_ > 50 && _loc2_ <= 500)
               {
                  TaskFunction.getInstance().addTaskGoalVOByString("PKmakeup2");
               }
               else if(_loc2_ > 500 && _loc2_ <= 10000)
               {
                  TaskFunction.getInstance().addTaskGoalVOByString("PKmakeup3");
               }
               break;
            }
            _loc4_++;
         }
         goTaskPanel();
      }
      
      private function rankListError1(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("请检查网络、不能完成PK任务请求");
      }
      
      protected function switchToPlayer(param1:UIBtnEvent = null) : void
      {
         setPanel(5);
         _informationPanel.switchToPlayer(null);
      }
      
      public function switchToPet(param1:UIBtnEvent = null) : void
      {
         setPanel(4);
         _informationPanel.switchToPet(null);
      }
      
      public function switchToTuDi(param1:UIBtnEvent = null) : void
      {
         setPanel(7);
         _informationPanel.switchToTuDi(null);
      }
      
      public function switchToAutoPet(param1:UIBtnEvent = null) : void
      {
         setPanel(8);
      }
      
      public function switchToMount(param1:UIBtnEvent = null) : void
      {
         setPanel(9);
      }
      
      protected function equipEquipment(param1:UIBtnEvent) : void
      {
         var arr:Array;
         var useEqVO:EquipmentVO;
         var usePlayer:Player;
         var automaticPetVO:AutomaticPetVO;
         var automaticPetBackVO:AutomaticPetVO;
         var curUid:String;
         var petVO:PetEquipmentVO;
         var bUse:Boolean;
         var bIn:Boolean;
         var id:int;
         var cell:UsedEquipmentCell;
         var index:int;
         var player:String;
         var cloneEquipmentVO:EquipmentVO;
         var equipmentVO1:EquipmentVO;
         var equipmentVO2:EquipmentVO;
         var equipmentVO3:EquipmentVO;
         var e:UIBtnEvent = param1;
         var equipmentCell:IEquipmentCell = e.target.parent.parent;
         var position:int = int(_package.area.equipmentCells.indexOf(e.target.parent.parent));
         var myControlPanel:MyControlPanel = this;
         if(equipmentCell.child is IOneValueEquipmentVO)
         {
            useEqVO = equipmentCell.child;
            usePlayer = currentPlayer;
            if(useEqVO is StackEquipmentVO && (useEqVO as StackEquipmentVO).num > 1 && useEqVO.id != 11100064)
            {
               if(_useMultiEqPanel == null)
               {
                  _useMultiEqPanel = new UseMultiEqPanel();
               }
               addChildAt(_useMultiEqPanel,getChildIndex(topLayer));
               if(_equipEquipmentFunction == null)
               {
                  _equipEquipmentFunction = new EquipEquipmentFunction();
               }
               _useMultiEqPanel.init(useEqVO,usePlayer,myControlPanel,_equipEquipmentFunction);
            }
            else
            {
               showWarningBox("确定要使用<font size=\'20\' color=\'#ff0000\'>" + useEqVO.name + "</font>吗？",1 | 2,{
                  "type":"useEq",
                  "okFunction":function():void
                  {
                     if(_equipEquipmentFunction == null)
                     {
                        _equipEquipmentFunction = new EquipEquipmentFunction();
                     }
                     _equipEquipmentFunction.EquipEquipmentVOAction(useEqVO,usePlayer,myControlPanel);
                  }
               });
            }
            return;
         }
         if(equipmentCell.child.equipmentType == "material" || equipmentCell.child.equipmentType == "scroll")
         {
            showWarningBox("此物品不能直接使用！",0);
            return;
         }
         if(equipmentCell.child.equipmentType == "contract")
         {
            automaticPetVO = XMLSingle.getAutomaticPetVOByID((equipmentCell.child as ContractEquipmntVO).targetAutomaticPetId,XMLSingle.getInstance().automaticPetsXML);
            if(automaticPetVO.partnerName)
            {
               automaticPetBackVO = XMLSingle.getAutomaticPetVOByID(automaticPetVO.partnerName,XMLSingle.getInstance().automaticPetsXML);
               curUid = String(GamingUI.getInstance().getAutomaticPetsData().addDoubleNumForUid());
               automaticPetVO.partnerUid = curUid;
               automaticPetBackVO.partnerUid = curUid;
               GamingUI.getInstance().getAutomaticPetsData().addAutomaticPetVO(automaticPetBackVO);
            }
            GamingUI.getInstance().getAutomaticPetsData().addAutomaticPetVO(automaticPetVO);
            _package.removeEquipmentVO(position);
            GamingUI.getInstance().refresh(2);
            showWarningBox("契约使用成功",0);
            SubmitFunction.getInstance().setData1(4,automaticPetVO.getId(),automaticPetVO.getName());
            openUseContractSuccessInforPanel();
            _useContractSuccessInforPanel.setAutomaticPetVO(automaticPetVO);
            return;
         }
         if(equipmentCell.child.equipmentType == "pet")
         {
            if(_petPanel.isHavePet)
            {
               if((_petPanel.pet.petEquipmentVO.activeSkillVO as PetActiveSkillVO).isCD)
               {
                  showWarningBox("已装备的宠物技能正在CD中，暂不能装备新的宠物！",1);
                  return;
               }
               if((Part1.getInstance().getLevel() && Part1.getInstance().getLevel().getPlayer1().getAnimalEntity().isInDie() || Part1.getInstance().m_level2 && Part1.getInstance().m_level2.getPlayer1().getAnimalEntity().isInDie() || Part1.getInstance().getBossWorld() && Part1.getInstance().getBossWorld().getPlayer1().getAnimalEntity().isInDie()) && currentPlayer.playerVO == _player1.playerVO)
               {
                  showWarningBox("人物1处于死亡阶段，暂不能装备新的宠物！",1);
                  return;
               }
               if(Part1.getInstance().getLevel() && Part1.getInstance().getLevel().getPlayer2() && Part1.getInstance().getLevel().getPlayer2().getAnimalEntity().isInDie() || (Part1.getInstance().m_level2 && Part1.getInstance().m_level2.getPlayer2().getAnimalEntity().isInDie() || Part1.getInstance().getBossWorld() && Part1.getInstance().getBossWorld().getPlayer2().getAnimalEntity().isInDie()) && currentPlayer.playerVO == _player2.playerVO)
               {
                  showWarningBox("人物2处于死亡阶段，暂不能装备新的宠物！",1);
                  return;
               }
               petVO = _petPanel.changePetVO(equipmentCell.child as PetEquipmentVO);
               _package.removeEquipmentVO(position);
               _package.addEquipmentVO(petVO,position);
            }
            else
            {
               if((Part1.getInstance().getLevel() && Part1.getInstance().getLevel().getPlayer1().getAnimalEntity().isInDie() || Part1.getInstance().m_level2 && Part1.getInstance().m_level2.getPlayer1().getAnimalEntity().isInDie() || Part1.getInstance().getBossWorld() && Part1.getInstance().getBossWorld().getPlayer1().getAnimalEntity().isInDie()) && currentPlayer.playerVO == _player1.playerVO)
               {
                  showWarningBox("人物1处于死亡阶段，暂不能装备新的宠物！",1);
                  return;
               }
               if(Part1.getInstance().getLevel() && Part1.getInstance().getLevel().getPlayer2() && Part1.getInstance().getLevel().getPlayer2().getAnimalEntity().isInDie() || (Part1.getInstance().m_level2 && Part1.getInstance().m_level2.getPlayer2().getAnimalEntity().isInDie() || Part1.getInstance().getBossWorld() && Part1.getInstance().getBossWorld().getPlayer2().getAnimalEntity().isInDie()) && currentPlayer.playerVO == _player2.playerVO)
               {
                  showWarningBox("人物2处于死亡阶段，暂不能装备新的宠物！",1);
                  return;
               }
               _petPanel.changePetVO(equipmentCell.child as PetEquipmentVO);
               _package.removeEquipmentVO(position);
            }
            GamingUI.getInstance().refresh(2 | 0x40 | 0x80 | 0x10 | 1 | 8 | 0x0200);
            return;
         }
         if(equipmentCell.child.equipmentType == "danMedicine")
         {
            arr = DanMedicineFunction.getInstance().addEquipmentVOToDanMedicineEquipmentGrid(equipmentCell.child.clone(),currentPlayer);
            if(arr[0])
            {
               _package.removeEquipmentVO(position);
            }
            showWarningBox(arr[1],0);
            GamingUI.getInstance().refresh(2 | 1 | 0x020000);
            return;
         }
         if(equipmentCell.child.equipmentType == "medal")
         {
            bUse = true;
            bIn = false;
            id = (equipmentCell.child.clone() as MedalEquipmentVO).id;
            if(id == 11600036 || id == 11600037 || id == 11600038 || id == 11600039 || id == 11600040 || id == 11600041 || id == 11600042 || id == 11600043 || id == 11600048)
            {
               bIn = true;
            }
            if(bIn)
            {
               trace(currentPlayer.playerVO.playerType);
               switch(currentPlayer.playerVO.playerType)
               {
                  case "SunWuKong":
                     if(id == 11600036 || id == 11600037)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "BaiLongMa":
                     if(id == 11600036 || id == 11600038)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "ErLangShen":
                     if(id == 11600036 || id == 11600039)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "ChangE":
                     if(id == 11600036 || id == 11600040)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "Fox":
                     if(id == 11600036 || id == 11600041)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "TieShan":
                     if(id == 11600036 || id == 11600042)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "Houyi":
                     if(id == 11600036 || id == 11600043)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  case "ZiXia":
                     if(id == 11600036 || id == 11600048)
                     {
                        bUse = true;
                     }
                     else
                     {
                        bUse = false;
                        GamingUI.getInstance().showMessageTip("角色没对应");
                     }
                     break;
                  default:
                     bUse = false;
               }
            }
            else
            {
               bUse = true;
            }
            if(bUse)
            {
               arr = MedalFunction.getInstance().addMedalToPlayer(equipmentCell.child.clone() as MedalEquipmentVO,currentPlayer.playerVO);
               if(arr[0])
               {
                  _package.removeEquipmentVO(position);
               }
               showWarningBox(arr[1],0);
               dispatchEvent(new UIPassiveEvent("refreshAtt",128));
               GamingUI.getInstance().refresh(2 | 1 | 0x020000 | 0x0200);
            }
            return;
         }
         for each(cell in _playerInfomationPanel.useEquipmentCellContainer.equipmentCells)
         {
            index = int(_playerInfomationPanel.useEquipmentCellContainer.equipmentCells.indexOf(cell));
            if(equipmentCell.child.equipmentType.match(cell.cellType))
            {
               if(!(cell.owner != equipmentCell.child.owner && equipmentCell.child.owner != ""))
               {
                  if(!cell.isHaveChild)
                  {
                     equipmentVO1 = _package.removeEquipmentVO(position);
                     _playerInfomationPanel.addEquipmentVO(equipmentVO1,index);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     dispatchEvent(new UIPassiveEvent("refreshAtt",128));
                  }
                  else
                  {
                     equipmentVO2 = _playerInfomationPanel.removeEquipmentVO(index);
                     equipmentVO3 = _package.removeEquipmentVO(position);
                     _package.addEquipmentVO(equipmentVO2,position);
                     _playerInfomationPanel.addEquipmentVO(equipmentVO3,index);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     dispatchEvent(new UIPassiveEvent("refreshAtt",128));
                  }
                  break;
               }
               player = "";
               switch(currentPlayer.playerVO.playerType)
               {
                  case "SunWuKong":
                     player = "孙悟空";
                     break;
                  case "BaiLongMa":
                     player = "白龙马";
                     break;
                  case "ErLangShen":
                     player = "二郎神";
                     break;
                  case "ChangE":
                     player = "嫦娥";
                     break;
                  case "Fox":
                     player = "灵狐";
                     break;
                  case "TieShan":
                     player = "铁扇公主";
                     break;
                  case "Houyi":
                     player = "后羿";
                     break;
                  case "ZiXia":
                     player = "紫霞";
                     break;
                  default:
                     player = "";
               }
               showWarningBox(player + "不能用该装备！",0);
            }
         }
      }
      
      protected function sellEquipment(param1:UIBtnEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:EquipmentVO = null;
         var _loc4_:EquipmentVO = param1.target.parent.parent.child;
         if(_loc4_ == null)
         {
            return;
         }
         var _loc5_:int = new GetEquipmentSellMoney().getEquipmentSellMoney(_loc4_);
         if(_loc4_.isAbleSell)
         {
            currentPlayer.playerVO.money += _loc5_;
            _loc3_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc2_ = _package.removeEquipmentVO(_loc3_);
            GamingUI.getInstance().refresh(0x20 | 2);
         }
         else
         {
            showWarningBox("出售可获得<font size=\'20\' color=\'#ff0000\'>" + _loc5_ + "</font>元宝。确定要出售 <font size=\'20\' color=\'#ff0000\'>" + _loc4_.name + "</font> 吗？",1 | 2,{
               "type":"sellValuableEquipment",
               "equipment":_loc4_,
               "player":currentPlayer
            });
         }
      }
      
      protected function dragSellEquipment(param1:UIDataEvent) : void
      {
         var _loc2_:int = new GetEquipmentSellMoney().getEquipmentSellMoney(param1.data.equipment);
         currentPlayer.playerVO.money += _loc2_;
         GamingUI.getInstance().refresh(32);
      }
      
      protected function saveEquipment(param1:UIBtnEvent) : void
      {
         var _loc2_:int = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
         var _loc3_:EquipmentVO = _package.removeEquipmentVO(_loc2_);
         if(!_storage.addEquipmentVOs(_loc3_))
         {
            _package.addEquipmentVO(_loc3_,_loc2_);
            showWarningBox("仓库已满！",0);
         }
         GamingUI.getInstance().refresh(2 | 4);
      }
      
      protected function getOutEquipment(param1:UIBtnEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:EquipmentVO = null;
         if(_storage.visible)
         {
            _loc2_ = int(_storage.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc3_ = _storage.removeEquipmentVO(_loc2_);
            _storage.removeEquipmentVOFromCell(_loc2_);
            if(!_package.addEquipmentVOs(_loc3_,1))
            {
               _storage.addEquipmentVO(_loc3_,_loc2_);
               showWarningBox("背包已满！",0);
            }
            GamingUI.getInstance().refresh(2 | 4);
         }
         if(_publicStorage.visible)
         {
            _loc2_ = int(_publicStorage.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc3_ = _publicStorage.removeEquipmentVO(_loc2_);
            _publicStorage.removeEquipmentVOFromCell(_loc2_);
            if(!_package.addEquipmentVOs(_loc3_))
            {
               _publicStorage.addEquipmentVO(_loc3_,_loc2_);
               showWarningBox("背包已满！",0);
            }
            GamingUI.getInstance().refresh(2 | 0x2000);
         }
      }
      
      protected function putInEquipment(param1:UIBtnEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:EquipmentVO = null;
         var _loc4_:IEquipmentCell = null;
         var _loc9_:IsCanPutInfo = null;
         var _loc5_:EquipmentVO = null;
         var _loc7_:* = null;
         var _loc14_:* = undefined;
         var _loc10_:IEquipmentCell = null;
         var _loc15_:IsCanPutInfo = null;
         var _loc8_:EquipmentVO = null;
         var _loc12_:* = null;
         var _loc13_:* = undefined;
         var _loc6_:IEquipmentCell = null;
         var _loc11_:int = 0;
         if(_makeAndUpgradePanel.visible)
         {
            _loc4_ = param1.target.parent.parent;
            _loc2_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc9_ = _makeAndUpgradePanel.isCanPutEquipmentVO(_loc4_.child);
            if(_loc9_.isCanPut == false)
            {
               if(_loc9_.message)
               {
                  showWarningBox(_loc9_.message,0);
               }
               return;
            }
            if(WarningBoxSingle.getInstance().parent)
            {
               showWarningBox("已经有放入的装备!",1);
               return;
            }
            _havegoods = true;
            _loc5_ = _package.getEquipmentVO(_loc2_);
            if(_loc5_ is StackEquipmentVO)
            {
               _loc7_ = _loc5_.clone();
               (_loc7_ as StackEquipmentVO).num = 1;
            }
            else
            {
               _loc7_ = _loc5_;
            }
            if(_loc5_ is StackEquipmentVO && (_loc5_ as StackEquipmentVO).num)
            {
               MyFunction.getInstance().resethEquipmentVOVectorToPutOut(currentPlayer.playerVO.packageEquipmentVOs,_loc5_);
            }
            else
            {
               MyFunction.getInstance().resethEquipmentVOVectorToPutOut(currentPlayer.playerVO.packageEquipmentVOs);
            }
            _loc5_.isPutInOperate = true;
            _loc14_ = _makeAndUpgradePanel.putInEquipmentVO(_loc7_);
            ClearUtil.nullArr(_loc14_,false,false,false);
         }
         if(_equipMagicCreate.visible)
         {
            _loc10_ = param1.target.parent.parent;
            _loc2_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc15_ = _equipMagicCreate.isCanPutEquipmentVO(_loc10_.child);
            if(_loc15_.isCanPut == false)
            {
               if(_loc15_.message)
               {
                  showWarningBox(_loc15_.message,0);
               }
               return;
            }
            _havegoods = true;
            _loc8_ = _package.getEquipmentVO(_loc2_);
            if(_loc8_ is StackEquipmentVO)
            {
               _loc12_ = _loc8_.clone();
               (_loc12_ as StackEquipmentVO).num = 1;
               (_loc8_ as StackEquipmentVO).num--;
            }
            else
            {
               _loc12_ = _loc8_;
            }
            _loc13_ = _equipMagicCreate.putInEquipmentVO(_loc12_);
            ClearUtil.nullArr(_loc13_,false,false,false);
            if(_loc8_ is StackEquipmentVO && (_loc8_ as StackEquipmentVO).num)
            {
               MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(currentPlayer.playerVO.packageEquipmentVOs,_loc8_.clone());
            }
            else
            {
               MyFunction.getInstance().resethEquipmentVOVectorToPutOut(currentPlayer.playerVO.packageEquipmentVOs);
            }
            GamingUI.getInstance().refresh(2);
            _loc8_.isPutInOperate = true;
         }
         if(_eqMagicInheritPanel.visible)
         {
            _havegoods = true;
            _eqMagicInheritPanel.putInEquipment(param1);
         }
         if(_publicStorage.visible)
         {
            _havegoods = true;
            _loc2_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc3_ = _package.removeEquipmentVO(_loc2_);
            _package.removeEquipmentVOFromCell(_loc2_);
            if(!_publicStorage.addEquipmentVOs(_loc3_))
            {
               _package.addEquipmentVO(_loc3_,_loc2_);
               showWarningBox("仓库已满！",0);
            }
            GamingUI.getInstance().refresh(2 | 0x2000);
         }
         else if(_storage.visible)
         {
            _havegoods = true;
            _loc2_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc3_ = _package.removeEquipmentVO(_loc2_);
            _package.removeEquipmentVOFromCell(_loc2_);
            if(!_storage.addEquipmentVOs(_loc3_,1))
            {
               _package.addEquipmentVO(_loc3_,_loc2_);
               showWarningBox("仓库已满！",0);
            }
            GamingUI.getInstance().refresh(2 | 4);
         }
         else if(_hatchPanel.visible)
         {
            if(_hatchPanel.getIsAblePutInEgg() == false)
            {
               return;
            }
            _loc6_ = param1.target.parent.parent;
            if(!(_loc6_.child is EggEquipmentVO))
            {
               return;
            }
            _loc2_ = int(_package.area.equipmentCells.indexOf(param1.target.parent.parent));
            _loc3_ = _package.removeEquipmentVO(_loc2_);
            _hatchPanel.putInEgg(_loc3_ as EggEquipmentVO);
            _havegoods = true;
            GamingUI.getInstance().refresh(2);
         }
      }
      
      protected function takeDownEquipment(param1:UIBtnEvent) : void
      {
         var _loc2_:int = int(_playerInfomationPanel.useEquipmentCellContainer.equipmentCells.indexOf(param1.target.parent.parent));
         var _loc3_:EquipmentVO = _playerInfomationPanel.removeEquipmentVO(_loc2_);
         if(!_package.addEquipmentVOs(_loc3_,1))
         {
            _playerInfomationPanel.addEquipmentVO(_loc3_,_loc2_);
            showWarningBox("背包已满！",0);
         }
         GamingUI.getInstance().refresh(1 | 0x0200 | 2);
         dispatchEvent(new UIPassiveEvent("refreshAtt",128));
      }
      
      protected function fastUseEquipment(param1:UIBtnEvent) : void
      {
         var _loc3_:PlayerPanel = null;
         var _loc2_:EquipmentVO = param1.target.parent.parent.child;
         if(_loc2_ == null)
         {
            return;
         }
         switch(currentPlayer)
         {
            case GamingUI.getInstance().player1:
               _loc3_ = GamingUI.getInstance().externalPanel.player1Panel;
               break;
            case GamingUI.getInstance().player2:
               _loc3_ = GamingUI.getInstance().externalPanel.player2Panel;
         }
         if(_loc3_ == null)
         {
            return;
         }
         switch(_loc2_.id - 11000007)
         {
            case 0:
            case 9:
               _loc3_.preferFastUse_magicEq = _loc2_;
               break;
            case 1:
            case 10:
               _loc3_.preferFastUse_bloodEq = _loc2_;
         }
      }
      
      protected function detailShow(param1:UIBtnEvent) : void
      {
         var _loc2_:EquipmentVO = param1.target.parent.parent.child;
         if(_loc2_ == null)
         {
            return;
         }
         var _loc3_:EquipmentDetailShowFactory = new EquipmentDetailShowFactory();
         var _loc4_:String = String(XMLSingle.getInstance().dataXML.ChoiceBtns[0].DetailBtn[0].show.(@equipmentType == _loc2_.equipmentType)[0].@detailShowClassName);
         ClearUtil.clearObject(_equipmentDetailShow);
         _equipmentDetailShow = _loc3_.createByClassName(_loc4_);
         _loc3_.clear();
         addChildAt(_equipmentDetailShow,getChildIndex(_topLayer));
         _equipmentDetailShow.setPlayer(currentPlayer);
         _equipmentDetailShow.initDetailShow(_loc2_);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var length:int;
         var equipmentVOs:Vector.<EquipmentVO>;
         var num:int;
         var buyDataObj:Object;
         var equipmentVO:EquipmentVO;
         var player:Player;
         var index:int;
         var getMoney:int;
         var position:int;
         var str:String;
         var e:UIPassiveEvent = param1;
         var i:int = 0;
         if(e.data.detail == 1)
         {
            if(e.data.task.type != "aKeySoldTask")
            {
               if(e.data.task.type == "buyCellTask")
               {
                  num = int(e.data.task.buyNum);
                  buyDataObj = e.data.task.buyDataObj;
                  equipmentVOs = e.data.task.equipments;
                  AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
                  {
                     var _loc3_:int = 0;
                     if(param1["propId"] != buyDataObj["propId"])
                     {
                        showWarningBox("购买物品id前后端不相同！",0);
                        throw new Error("购买物品id前后端不相同！");
                     }
                     _loc3_ = 0;
                     while(_loc3_ < num)
                     {
                        equipmentVOs.push(null);
                        _loc3_++;
                     }
                     dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 4 | 0x2000));
                     var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                     _loc2_.type = "4399";
                     _loc2_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc2_);
                     MyFunction2.saveGame();
                  },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
               }
               else if(e.data.task.type != "buyTeHuiLiBao")
               {
                  if(e.data.task.type == "sellValuableEquipment")
                  {
                     equipmentVO = e.data.task.equipment;
                     player = e.data.task.player;
                     equipmentVOs = e.data.task.equipments;
                     if(equipmentVOs == null && player != null)
                     {
                        equipmentVOs = player.playerVO.packageEquipmentVOs;
                     }
                     if(equipmentVOs == null)
                     {
                        setTimeout(showWarningBox,100,"出错了！",0);
                        return;
                     }
                     index = int(equipmentVOs.indexOf(equipmentVO));
                     if(index == -1)
                     {
                        setTimeout(showWarningBox,100,"装备已不存在， 出售失败！",0);
                        return;
                     }
                     getMoney = new GetEquipmentSellMoney().getEquipmentSellMoney(equipmentVO);
                     player.playerVO.money += getMoney;
                     player.playerVO.packageEquipmentVOs[index] = null;
                     position = int(_package.area.equipmentCells.indexOf(e.target.parent.parent));
                     GamingUI.getInstance().refresh(0x20 | 2);
                  }
                  else if(e.data.task.type == "freeChangeNickname" || e.data.task.type == "changeRedNickname")
                  {
                     e.data.task.okFunction(e);
                     for(str in e.data.task)
                     {
                        e.data.task[str] = null;
                     }
                     e.data.task = null;
                  }
                  else if(e.data && Boolean(e.data.task) && Boolean(e.data.task.okFunction))
                  {
                     (e.data.task.okFunction as Function).apply(null,e.data.task.okFunctionParams);
                     ClearUtil.nullArr(e.data.task.okFunctionParams,false,false);
                     e.data.task.okFunction = null;
                  }
               }
            }
         }
      }
      
      protected function aKeySell(param1:Event) : void
      {
         if(_havegoods)
         {
            showWarning("炼造炉有东西，不能去出售",0);
            return;
         }
         openSellEquipmentsPanel();
      }
      
      protected function buyEquipment(param1:UIDataEvent) : void
      {
         _shop.moneyShop.hideBox();
         _shop.pkShop.hideBox();
         MyFunction.getInstance().buyEquipmentVO(currentPlayer,param1.data.equipment,param1.data.num,showWarningBox,param1.data.buyMode);
         GamingUI.getInstance().refresh(0x20 | 2);
      }
      
      protected function upGrade(param1:UIBtnEvent) : void
      {
         if(param1.target.parent.skillCell.child.level < param1.target.parent.skillCell.child.maxLevel)
         {
            if(currentPlayer.playerVO.level >= param1.target.parent.skillCell.child.initLevel)
            {
               if(param1.target.parent.getUpgradePrice() <= currentPlayer.playerVO.money)
               {
                  currentPlayer.playerVO.money -= param1.target.parent.getUpgradePrice();
                  param1.target.parent.upgrade();
                  GamingUI.getInstance().refresh(32);
               }
               else
               {
                  showWarningBox("金额不足，无法升级！",1);
               }
            }
            else
            {
               showWarningBox("等级不足，无法升级！",1);
            }
         }
         else
         {
            showWarningBox("此技能已经是最大等级,或是等级不够!",1);
         }
      }
      
      public function switchToPlayer_one(param1:UIBtnEvent) : void
      {
         WarningBoxSingle.getInstance().clear();
         _havegoods = false;
         takeOutEquipment();
         _player2_Btn.gotoTwoFrame();
         currentPlayer = _player1;
         switch(currentPlayer.playerVO.playerType)
         {
            case "SunWuKong":
               _currentState = 0;
               break;
            case "BaiLongMa":
               _currentState = 1;
               break;
            case "ErLangShen":
               _currentState = 2;
               break;
            case "ChangE":
               _currentState = 3;
               break;
            case "Fox":
               _currentState = 4;
               break;
            case "TieShan":
               _currentState = 5;
               break;
            case "Houyi":
               _currentState = 6;
               break;
            case "ZiXia":
               _currentState = 7;
         }
         _hatchPanel.swapPlayerVO(currentPlayer);
         if(_equipMagicCreate.visible)
         {
            _equipMagicCreate.swapPlayerVO(currentPlayer);
         }
         if(_eqMagicInheritPanel.visible)
         {
            _eqMagicInheritPanel.swapPlayerVO(currentPlayer,1);
         }
         GamingUI.getInstance().refresh(1 | 2 | 8 | 4 | 0x10 | 0x20 | 0x1000 | 0x040000 | 0x200000);
      }
      
      public function switchToPlayer_two(param1:UIBtnEvent) : void
      {
         WarningBoxSingle.getInstance().clear();
         _havegoods = false;
         takeOutEquipment();
         _player1_Btn.gotoTwoFrame();
         currentPlayer = _player2;
         switch(currentPlayer.playerVO.playerType)
         {
            case "SunWuKong":
               _currentState = 0;
               break;
            case "BaiLongMa":
               _currentState = 1;
               break;
            case "ErLangShen":
               _currentState = 2;
               break;
            case "ChangE":
               _currentState = 3;
               break;
            case "Fox":
               _currentState = 4;
               break;
            case "TieShan":
               _currentState = 5;
               break;
            case "Houyi":
               _currentState = 6;
               break;
            case "ZiXia":
               _currentState = 7;
         }
         _hatchPanel.swapPlayerVO(currentPlayer);
         if(_equipMagicCreate.visible)
         {
            _equipMagicCreate.swapPlayerVO(currentPlayer);
         }
         if(_eqMagicInheritPanel.visible)
         {
            _eqMagicInheritPanel.swapPlayerVO(currentPlayer,2);
         }
         GamingUI.getInstance().refresh(1 | 2 | 8 | 4 | 0x10 | 0x20 | 0x1000 | 0x040000 | 0x200000);
      }
      
      protected function takeBackpet(param1:UIBtnEvent) : void
      {
         var _loc2_:EquipmentVO = null;
         var _loc3_:PetEquipmentVO = null;
         if((currentPlayer.playerVO.pet.petEquipmentVO.activeSkillVO as PetActiveSkillVO).isCD)
         {
            showWarningBox("宠物技能CD中，不能回收！",1);
            return;
         }
         if(Castellan.m_Pet1 && currentPlayer.playerVO == _player1.playerVO)
         {
            showWarningBox("宠物失去控制，不能回收！",1);
            return;
         }
         if(Castellan.m_Pet2 && currentPlayer.playerVO == _player2.playerVO)
         {
            showWarningBox("宠物失去控制，不能回收！",1);
            return;
         }
         _loc3_ = currentPlayer.playerVO.pet.petEquipmentVO;
         _loc2_ = _loc3_;
         currentPlayer.playerVO.pet.petEquipmentVO = null;
         if(_package.addEquipmentVOs(_loc2_))
         {
            dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x40 | 0x80 | 0x10 | 1 | 8 | 0x0200));
         }
         else
         {
            currentPlayer.playerVO.pet.petEquipmentVO = _loc3_;
            _loc2_ = null;
            showWarningBox("背包已满！",0);
         }
         currentPlayer.changeData();
      }
      
      private function showWarningBox2(param1:UIPassiveEvent) : void
      {
         showWarningBox(param1.data.text,param1.data.flag,param1.data.task);
         if(param1.data.hasOwnProperty("task"))
         {
            switch(param1.data.task.type)
            {
               case "aKeySoldTask":
                  WarningBoxSingle.getInstance().task.type = param1.data.task.type;
                  break;
               case "buyCellTask":
                  WarningBoxSingle.getInstance().task.type = param1.data.task.type;
                  WarningBoxSingle.getInstance().task.buyNum = param1.data.task.buyNum;
                  WarningBoxSingle.getInstance().task.buyDataObj = param1.data.task.buyDataObj;
                  WarningBoxSingle.getInstance().task.equipments = param1.data.task.equipments;
            }
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      public function hideWarningBox() : void
      {
         if(WarningBoxSingle.getInstance().parent)
         {
            WarningBoxSingle.getInstance().parent.removeChild(WarningBoxSingle.getInstance());
         }
      }
      
      protected function setPanel(param1:int) : void
      {
         switch(param1)
         {
            case 0:
               packageBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _shop.visible = true;
               _package.visible = true;
               _storage.visible = false;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               addGoodsToShop();
               break;
            case 1:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               packageBtn.gotoOneFrame();
               _shop.visible = false;
               _package.visible = true;
               _storage.visible = false;
               _informationPanel.visible = true;
               if(_informationPanel.currentState == 0)
               {
                  _playerInfomationPanel.visible = true;
                  _petPanel.visible = false;
                  _tuDiPanel.visible = false;
                  _autoPetPanel.visible = false;
                  _mountSmallPanel.visible = false;
               }
               else if(_informationPanel.currentState == 1)
               {
                  _petPanel.visible = true;
                  _playerInfomationPanel.visible = false;
                  _tuDiPanel.visible = false;
                  _autoPetPanel.visible = false;
                  _mountSmallPanel.visible = false;
               }
               else if(_informationPanel.currentState == 2)
               {
                  _tuDiPanel.visible = true;
                  _playerInfomationPanel.visible = false;
                  _petPanel.visible = false;
                  _autoPetPanel.visible = false;
                  _mountSmallPanel.visible = false;
               }
               else if(_informationPanel.currentState == 3)
               {
                  _autoPetPanel.visible = true;
                  _playerInfomationPanel.visible = false;
                  _petPanel.visible = false;
                  _tuDiPanel.visible = false;
                  _mountSmallPanel.visible = false;
               }
               else if(_informationPanel.currentState == 4)
               {
                  _mountSmallPanel.visible = true;
                  _playerInfomationPanel.visible = false;
                  _petPanel.visible = false;
                  _tuDiPanel.visible = false;
                  _autoPetPanel.visible = false;
               }
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 2:
               shopBtn.gotoTwoFrame();
               packageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = true;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = false;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 3:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               packageBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = false;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = false;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = true;
               _inTaskPanel.visible = false;
               break;
            case 4:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = true;
               _petPanel.visible = true;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 5:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = true;
               _informationPanel.visible = true;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 6:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               packageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = false;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = false;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = true;
               break;
            case 7:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = true;
               _petPanel.visible = false;
               _tuDiPanel.visible = true;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 8:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = true;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = true;
               _mountSmallPanel.visible = false;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
               break;
            case 9:
               shopBtn.gotoTwoFrame();
               storageBtn.gotoTwoFrame();
               skillPanelBtn.gotoTwoFrame();
               _storage.visible = false;
               _package.visible = true;
               _shop.visible = false;
               _playerInfomationPanel.visible = false;
               _informationPanel.visible = true;
               _petPanel.visible = false;
               _tuDiPanel.visible = false;
               _autoPetPanel.visible = false;
               _mountSmallPanel.visible = true;
               _skillPanel.visible = false;
               _inTaskPanel.visible = false;
         }
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(_popUpBox)
         {
            if(getChildByName(_popUpBox.name))
            {
               removeChild(_popUpBox);
            }
            _popUpBox = null;
         }
      }
      
      private function showBox(param1:UIDataEvent) : void
      {
         var _loc2_:EquipmentVO = param1.data.equipmentVO;
         if(Boolean(_popUpBox) && getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc2_,"shopWallPrice");
            _popUpBox.fun = param1.data.fun;
            _popUpBox.player = currentPlayer;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
         }
         else if(!_popUpBox)
         {
            _popUpBox = new GuideBuyPopUpBox(_loc2_,currentPlayer,"shopWallPrice");
            _popUpBox.fun = param1.data.fun;
            _popUpBox.player = currentPlayer;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            _popUpBox.x = 350;
            _popUpBox.y = 170;
            addChildAt(_popUpBox,numChildren - 2);
         }
         else if(Boolean(_popUpBox) && !getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc2_,"shopWallPrice");
            _popUpBox.fun = param1.data.fun;
            _popUpBox.player = currentPlayer;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            addChildAt(_popUpBox,numChildren - 2);
         }
      }
      
      private function tiDyEquipments(param1:TidyEquipmentEvent) : void
      {
         var oldEquipmentVOs:Vector.<EquipmentVO>;
         var e:TidyEquipmentEvent = param1;
         if(_havegoods)
         {
            showWarning("炼造炉有东西，不能去整理",0);
            return;
         }
         if(e.target == _package)
         {
            oldEquipmentVOs = currentPlayer.playerVO.packageEquipmentVOs;
            _tiDyEquipments.loadXML(function():void
            {
               currentPlayer.playerVO.packageEquipmentVOs = _tiDyEquipments.tiDyEquipments(oldEquipmentVOs);
               ClearUtil.nullArr(oldEquipmentVOs,false,false,false);
               GamingUI.getInstance().refresh(2);
            },showWarningBox);
         }
         else if(e.target == _storage)
         {
            oldEquipmentVOs = currentPlayer.playerVO.storageEquipmentVOs;
            _tiDyEquipments.loadXML(function():void
            {
               currentPlayer.playerVO.storageEquipmentVOs = _tiDyEquipments.tiDyEquipments(oldEquipmentVOs);
               ClearUtil.nullArr(oldEquipmentVOs,false,false,false);
               GamingUI.getInstance().refresh(4);
            },showWarningBox);
         }
         else if(e.target == _publicStorage)
         {
            oldEquipmentVOs = GamingUI.getInstance().publicStorageEquipmentVOs;
            _tiDyEquipments.loadXML(function():void
            {
               GamingUI.getInstance().publicStorageEquipmentVOs = _tiDyEquipments.tiDyEquipments(oldEquipmentVOs);
               ClearUtil.nullArr(oldEquipmentVOs,false,false,false);
               GamingUI.getInstance().refresh(8192);
            },showWarningBox);
         }
      }
      
      public function showWarning(param1:String, param2:int) : void
      {
         showWarningBox(param1,param2);
      }
      
      public function actionAfterUse() : void
      {
      }
      
      public function actionAfterUnableUse() : void
      {
      }
      
      public function getWarningTextFontSize() : int
      {
         return WarningBoxSingle.getInstance().getTextFontSize();
      }
      
      public function closeUseMultiEqPanel() : void
      {
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
         ClearUtil.clearObject(_useMultiEqPanel);
         _useMultiEqPanel = null;
      }
      
      private function openUseContractSuccessInforPanel() : void
      {
         if(_useContractSuccessInforPanel == null)
         {
            _useContractSuccessInforPanel = new UseContractSuccessInforPanel();
            _useContractSuccessInforPanel.init(this,Part1.getInstance().getLoadUI(),Part1.getInstance().getVersionControl());
         }
         addChildAt(_useContractSuccessInforPanel,getChildIndex(topLayer));
      }
      
      public function closeUseContractSuccessInforPanel() : void
      {
         ClearUtil.clearObject(_useContractSuccessInforPanel);
         _useContractSuccessInforPanel = null;
      }
      
      private function openSellEquipmentsPanel() : void
      {
         if(_sellEquipmentsPanel == null)
         {
            _sellEquipmentsPanel = new SellEquipmentsPanel();
            _sellEquipmentsPanel.init(currentPlayer.playerVO,this);
         }
         addChildAt(_sellEquipmentsPanel,getChildIndex(topLayer));
         hideWarningBox();
      }
      
      public function closeSellEquipmentsPanel() : void
      {
         ClearUtil.clearObject(_sellEquipmentsPanel);
         _sellEquipmentsPanel = null;
         refresh(2 | 0x20);
      }
   }
}

