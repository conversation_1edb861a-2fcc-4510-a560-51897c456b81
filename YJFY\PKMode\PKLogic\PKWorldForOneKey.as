package YJFY.PKMode.PKLogic
{
   import UI.Players.Player;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dog;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dragon;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Fox;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Houyi;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Monkey;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Rabbit;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.GameEntity.XydzjsPlayerAndPet.ZiXia;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.PK;
   import YJFY.Skill.ActiveSkill.AttackSkill.AreaAttackSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.ICuboidAreaAttackSkill;
   import YJFY.Skill.Skill;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.StepAttackGame.IJudgeStepAttackGameIsEnd;
   import YJFY.StepAttackGame.NextEntities;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   
   public class PKWorldForOneKey implements IGetNextStepActionEntities, IJudgeStepAttackGameIsEnd
   {
      
      private var m_myPlayer1:PKPlayer;
      
      private var m_myPlayer2:PKPlayer;
      
      private var m_foePlayer1:PKPlayer;
      
      private var m_foePlayer2:PKPlayer;
      
      private var m_pkStepWorld:StepAttackGameWorld;
      
      private var m_pkIsStart:Boolean;
      
      private var m_pkIsEnd:Boolean;
      
      private var m_isStartPlayPkProgress:Boolean;
      
      private var m_isStartPlayPKProgress2:Boolean;
      
      private var m_isEndPlayPkProgress:Boolean;
      
      private var m_stepDataManager:StepDataManager;
      
      private var m_isTwoMode:Boolean;
      
      private var m_isFristGet:Boolean = true;
      
      private var m_isMyFirst:Boolean;
      
      private var m_isMyWin:Boolean;
      
      private var m_myUiPlayer1:UI.Players.Player;
      
      private var m_myUiPlayer2:UI.Players.Player;
      
      private var m_foeUiPlayer1:UI.Players.Player;
      
      private var m_foeUiPlayer2:UI.Players.Player;
      
      private var m_pk:PK;
      
      private var m_thisload:YJFYLoader;
      
      public function PKWorldForOneKey()
      {
         super();
         m_stepDataManager = new StepDataManager();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_myPlayer1);
         m_myPlayer1 = null;
         ClearUtil.clearObject(m_myPlayer2);
         m_myPlayer2 = null;
         ClearUtil.clearObject(m_foePlayer1);
         m_foePlayer1 = null;
         ClearUtil.clearObject(m_foePlayer2);
         m_foePlayer2 = null;
         ClearUtil.clearObject(m_pkStepWorld);
         m_pkStepWorld = null;
         ClearUtil.clearObject(m_stepDataManager);
         m_stepDataManager = null;
         m_thisload = null;
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         m_foeUiPlayer1 = null;
         m_foeUiPlayer2 = null;
         m_pk = null;
      }
      
      public function setPk(param1:PK) : void
      {
         m_pk = param1;
      }
      
      public function setLoad(param1:YJFYLoader) : void
      {
         m_thisload = param1;
      }
      
      public function setIsTwoMode(param1:Boolean) : void
      {
         m_isTwoMode = param1;
      }
      
      public function initUiPlayerData(param1:UI.Players.Player, param2:UI.Players.Player, param3:UI.Players.Player, param4:UI.Players.Player) : void
      {
         if(m_myUiPlayer1)
         {
            throw new Error("不能重复初始化");
         }
         m_myUiPlayer1 = param1;
         m_myUiPlayer2 = param2;
         m_foeUiPlayer1 = param3;
         m_foeUiPlayer2 = param4;
         m_myPlayer1 = new PKPlayer();
         m_myPlayer1.setStepDataManager(m_stepDataManager);
         if(Boolean(m_myUiPlayer2) && m_isTwoMode)
         {
            m_myPlayer2 = new PKPlayer();
            m_myPlayer2.setStepDataManager(m_stepDataManager);
         }
         m_foePlayer1 = new PKPlayer();
         m_foePlayer1.setStepDataManager(m_stepDataManager);
         if(Boolean(m_foeUiPlayer2) && m_isTwoMode)
         {
            m_foePlayer2 = new PKPlayer();
            m_foePlayer2.setStepDataManager(m_stepDataManager);
         }
         mapInitSuccess();
      }
      
      protected function mapInitSuccess() : void
      {
         initPlayerOne(m_myUiPlayer1);
         if(m_myUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_myUiPlayer2);
         }
         initPlayerOne(m_foeUiPlayer1);
         if(m_foeUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_foeUiPlayer2);
         }
      }
      
      private function initPlayerOne(param1:UI.Players.Player) : void
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               m_thisload.getXML("NewGameFolder/Players/Monkey.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "BaiLongMa":
               m_thisload.getXML("NewGameFolder/Players/Dragon.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ErLangShen":
               m_thisload.getXML("NewGameFolder/Players/Dog.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ChangE":
               m_thisload.getXML("NewGameFolder/Players/Rabbit.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Fox":
               m_thisload.getXML("NewGameFolder/Players/Fox.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "TieShan":
               m_thisload.getXML("NewGameFolder/Players/TieShan.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Houyi":
               m_thisload.getXML("NewGameFolder/Players/Houyi.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ZiXia":
               m_thisload.getXML("NewGameFolder/Players/ZiXia.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            default:
               throw new Error();
         }
         m_thisload.load();
      }
      
      protected function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error(param1.swfPath + param1.xmlPath);
      }
      
      private function getPlayerXMLSuccess(param1:YJFYLoaderData, param2:UI.Players.Player) : void
      {
         var _loc3_:YJFY.GameEntity.PlayerAndPet.Player = null;
         var _loc4_:PKPlayer = null;
         if(param1.xmlPath == "NewGameFolder/Players/Monkey.xml")
         {
            _loc3_ = new Monkey();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dragon.xml")
         {
            _loc3_ = new Dragon();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dog.xml")
         {
            _loc3_ = new Dog();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Rabbit.xml")
         {
            _loc3_ = new Rabbit();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Fox.xml")
         {
            _loc3_ = new Fox();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/TieShan.xml")
         {
            _loc3_ = new TieShan();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Houyi.xml")
         {
            _loc3_ = new Houyi();
         }
         else
         {
            if(param1.xmlPath != "NewGameFolder/Players/ZiXia.xml")
            {
               throw new Error();
            }
            _loc3_ = new ZiXia();
         }
         _loc3_.donotloadswf = true;
         switch(param2)
         {
            case m_myUiPlayer1:
               _loc4_ = m_myPlayer1;
               m_myPlayer1.setEntity(_loc3_ as PlayerXydzjs);
               break;
            case m_myUiPlayer2:
               _loc4_ = m_myPlayer2;
               m_myPlayer2.setEntity(_loc3_ as PlayerXydzjs);
               break;
            case m_foeUiPlayer1:
               _loc4_ = m_foePlayer1;
               m_foePlayer1.setEntity(_loc3_ as PlayerXydzjs);
               break;
            case m_foeUiPlayer2:
               _loc4_ = m_foePlayer2;
               m_foePlayer2.setEntity(_loc3_ as PlayerXydzjs);
               break;
            default:
               throw new Error();
         }
         (_loc3_ as PlayerXydzjs).setIsCreateAutoAttackPet(false);
         (_loc3_ as PlayerXydzjs).setUiPlayer(param2);
         _loc4_.setPet((_loc3_ as PlayerXydzjs).getPet() as PetXydzjs);
      }
      
      public function render() : void
      {
         startStepGame();
      }
      
      public function getMyPlayer1() : PKPlayer
      {
         return m_myPlayer1;
      }
      
      public function getMyPlayer2() : PKPlayer
      {
         return m_myPlayer2;
      }
      
      public function getFoePlayer1() : PKPlayer
      {
         return m_foePlayer1;
      }
      
      public function getFoePlayer2() : PKPlayer
      {
         return m_foePlayer2;
      }
      
      private function startStepGame() : void
      {
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Number = NaN;
         if(!m_pkIsStart && isReadyStartStepGame())
         {
            trace("开始pk，进行pk计算");
            m_pkIsStart = true;
            m_myPlayer1.readyStartFight();
            if(m_myPlayer2)
            {
               m_myPlayer2.readyStartFight();
            }
            m_foePlayer1.readyStartFight();
            if(m_foePlayer2)
            {
               m_foePlayer2.readyStartFight();
            }
            m_pkIsEnd = false;
            ClearUtil.clearObject(m_pkStepWorld);
            m_pkStepWorld = new StepAttackGameWorld();
            m_pkStepWorld.setGetNextStepActionEntities(this);
            m_pkStepWorld.setJudgeStepAttackGameIsEnd(this);
            m_pkStepWorld.addFriend(m_myPlayer1);
            if(m_myPlayer2)
            {
               m_pkStepWorld.addFriend(m_myPlayer2);
            }
            m_pkStepWorld.addEnemy(m_foePlayer1);
            if(m_myPlayer2)
            {
               m_pkStepWorld.addEnemy(m_foePlayer2);
            }
            _loc1_ = m_myPlayer1.getOffensiveValue();
            if(m_myPlayer2)
            {
               _loc1_ += m_myPlayer2.getOffensiveValue();
            }
            _loc3_ = m_foePlayer1.getOffensiveValue();
            if(m_foePlayer2)
            {
               _loc3_ += m_foePlayer2.getOffensiveValue();
            }
            _loc2_ = Math.random() + 0.5 + (_loc1_ - _loc3_) / 1000;
            if(_loc2_ > 0)
            {
               m_isMyFirst = Boolean(int(_loc2_));
            }
            else
            {
               m_isMyFirst = false;
            }
            m_pkStepWorld.startAttack();
         }
      }
      
      private function isReadyStartStepGame() : Boolean
      {
         if(m_myPlayer1 == null)
         {
            return false;
         }
         if(m_myPlayer1.getPlayer() == null)
         {
            return false;
         }
         if(m_foePlayer1 == null)
         {
            return false;
         }
         if(m_foePlayer1.getPlayer() == null)
         {
            return false;
         }
         if(m_isTwoMode)
         {
            if(m_myUiPlayer2)
            {
               if(m_myPlayer2 == null)
               {
                  return false;
               }
               if(m_myPlayer2.getPlayer() == null)
               {
                  return false;
               }
            }
            if(m_foeUiPlayer2)
            {
               if(m_foePlayer2 == null)
               {
                  return false;
               }
               if(m_foePlayer2.getPlayer() == null)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      public function getNextAttackAndBeAttackedEntities() : NextEntities
      {
         var _loc2_:IEntity = null;
         var _loc4_:NextEntities = new NextEntities();
         var _loc3_:IEntity = m_pkStepWorld.getCurrentAttackEntity();
         var _loc1_:Vector.<IEntity> = new Vector.<IEntity>();
         if(_loc3_ == null)
         {
            if(m_isFristGet == false)
            {
               throw new Error("出错了， 不是第一次获取居然为null");
            }
            if(m_isMyFirst)
            {
               _loc2_ = m_myPlayer1;
               _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
            }
            else
            {
               _loc2_ = m_foePlayer1;
               _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
            }
            m_isFristGet = false;
         }
         else
         {
            var _loc5_:* = _loc3_;
            loop0:
            switch(_loc5_)
            {
               case m_myPlayer1:
                  if(m_myPlayer1.getPet())
                  {
                     _loc2_ = m_myPlayer1.getPkPet();
                     getPetAttackTargets(m_myPlayer1.getPkPet(),m_foePlayer1,m_foePlayer2,_loc1_);
                  }
                  else if(m_myPlayer2 && m_myPlayer2.isDie() == false)
                  {
                     _loc2_ = m_myPlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                  }
                  else if(m_foePlayer1.isDie() == false)
                  {
                     _loc2_ = m_foePlayer1;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                  }
                  else
                  {
                     if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                     {
                        throw new Error("出错了");
                     }
                     _loc2_ = m_foePlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                  }
                  break;
               case m_myPlayer1.getPkPet():
                  if(m_myPlayer2 && m_myPlayer2.isDie() == false)
                  {
                     _loc2_ = m_myPlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                  }
                  else if(m_foePlayer1.isDie() == false)
                  {
                     _loc2_ = m_foePlayer1;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                  }
                  else
                  {
                     if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                     {
                        throw new Error("出错了");
                     }
                     _loc2_ = m_foePlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                  }
                  break;
               default:
                  if((m_myPlayer2 ? m_myPlayer2 : null) === _loc5_)
                  {
                     if(m_myPlayer2.getPet())
                     {
                        _loc2_ = m_myPlayer2.getPkPet();
                        getPetAttackTargets(m_myPlayer2.getPkPet(),m_foePlayer1,m_foePlayer2,_loc1_);
                     }
                     else if(m_foePlayer1.isDie() == false)
                     {
                        _loc2_ = m_foePlayer1;
                        _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                     }
                     else
                     {
                        if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                        {
                           throw new Error("出错了");
                        }
                        _loc2_ = m_foePlayer2;
                        _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                     }
                     break;
                  }
                  switch(_loc5_)
                  {
                     case m_myPlayer2 ? m_myPlayer2.getPkPet() : null:
                        if(m_foePlayer1.isDie() == false)
                        {
                           _loc2_ = m_foePlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        break loop0;
                     case m_foePlayer1:
                        if(m_foePlayer1.getPet())
                        {
                           _loc2_ = m_foePlayer1.getPkPet();
                           getPetAttackTargets(m_foePlayer1.getPkPet(),m_myPlayer1,m_myPlayer2,_loc1_);
                        }
                        else if(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false)
                        {
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                     case m_foePlayer1.getPkPet():
                        if(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false)
                        {
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                     default:
                        if((m_foePlayer2 ? m_foePlayer2 : null) !== _loc5_)
                        {
                           if((m_foePlayer2 ? m_foePlayer2.getPkPet() : null) !== _loc5_)
                           {
                              throw new Error();
                           }
                           if(m_myPlayer1.isDie() == false)
                           {
                              _loc2_ = m_myPlayer1;
                              _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                           }
                           else
                           {
                              if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                              {
                                 throw new Error("出错了");
                              }
                              _loc2_ = m_myPlayer2;
                              _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                           }
                           break loop0;
                        }
                        if(m_foePlayer2.getPet())
                        {
                           _loc2_ = m_foePlayer2.getPkPet();
                           getPetAttackTargets(m_foePlayer2.getPkPet(),m_myPlayer1,m_myPlayer2,_loc1_);
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                  }
            }
         }
         _loc4_.nextAttackEntity = _loc2_;
         _loc4_.nextBeAttackedEntities = _loc1_;
         if(_loc1_.length > 2 || _loc1_.length <= 0)
         {
            throw new Error("nextBeAttackEntities.length出错:" + _loc1_.length);
         }
         return _loc4_;
      }
      
      public function getRoundNum() : int
      {
         return 0;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
      }
      
      private function getPetAttackTargets(param1:PkPet, param2:PKPlayer, param3:PKPlayer, param4:Vector.<IEntity>) : void
      {
         switch(int(getPetAttackTargetNum(param1,param2,param3)) - 1)
         {
            case 0:
               param4.push(getPetAttackTarget(param1,param2,param3));
               break;
            case 1:
               param4.push(param2);
               param4.push(param3);
               break;
            default:
               throw new Error();
         }
      }
      
      private function getPlayerAttackTarget(param1:PKPlayer, param2:PKPlayer, param3:PKPlayer) : PKPlayer
      {
         if(param2.isDie() == false)
         {
            return param2;
         }
         if(param3.isDie() == false)
         {
            return param3;
         }
         throw new Error("出错了");
      }
      
      private function getPetAttackTarget(param1:PkPet, param2:PKPlayer, param3:PKPlayer) : PKPlayer
      {
         if(param2.isDie() == false)
         {
            return param2;
         }
         if(param3.isDie() == false)
         {
            return param3;
         }
         throw new Error("出错了");
      }
      
      private function getPetAttackTargetNum(param1:PkPet, param2:PKPlayer, param3:PKPlayer) : uint
      {
         var _loc5_:Skill = param1.getPet().getAnimalEntity().getSkillByIndex(0);
         var _loc4_:uint = 2;
         if(param2.isDie())
         {
            _loc4_--;
         }
         if(param3 == null || param3.isDie())
         {
            _loc4_--;
         }
         if(param3 == null)
         {
            return Math.min(_loc4_,1);
         }
         if(_loc5_ is AttackSkill)
         {
            if(_loc5_ is AreaAttackSkill)
            {
               if(_loc5_ is ICuboidAreaAttackSkill)
               {
                  if(Math.abs(param2.getPlayer().getAnimalEntity().getY() - param3.getPlayer().getAnimalEntity().getY()) < (_loc5_ as ICuboidAreaAttackSkill).getCuboidRange().getYRange())
                  {
                     return Math.min(_loc4_,2);
                  }
                  return Math.min(_loc4_,1);
               }
               throw new Error("还没为其他种类的区域攻击技能做好准备");
            }
            throw new Error("还没为该类型的攻击技能做好装备");
         }
         throw new Error("宠物技能为非攻击技能，代码Pk功能还未非攻击技能做好准备");
      }
      
      public function nowOverGame() : void
      {
         m_pk.reFreshPanel();
      }
      
      public function judeIsEnd() : Boolean
      {
         if(m_myPlayer1.getPkBlood1() <= 0 && (Boolean(m_myPlayer2) == false || m_myPlayer2.getPkBlood1() <= 0))
         {
            m_pkIsEnd = true;
            m_isMyWin = false;
            end();
            return true;
         }
         if(m_foePlayer1.getPkBlood1() <= 0 && (Boolean(m_foePlayer2) == false || m_foePlayer2.getPkBlood1() <= 0))
         {
            m_pkIsEnd = true;
            m_isMyWin = true;
            end();
            return true;
         }
         m_pkIsEnd = false;
         return false;
      }
      
      private function end() : void
      {
         m_pk.addPKDataAndSubmitToRankList(m_isMyWin);
      }
      
      protected function initCompletePlayer(param1:YJFY.GameEntity.PlayerAndPet.Player) : void
      {
         (param1 as PlayerXydzjs).setMedalIsDynamic(false);
      }
   }
}

