package YJFY.ShowLogicShell.NumShowLogicShell
{
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class MultiPlaceNumLogicShell
   {
      
      private var _show:MovieClip;
      
      private var _onePlaceNum:OnePlaceNumLogicShell;
      
      private var _tenPlaceNum:OnePlaceNumLogicShell;
      
      private var _hundredPlaceNum:OnePlaceNumLogicShell;
      
      private var _thousandPlaceNum:OnePlaceNumLogicShell;
      
      private var _tenThousandPlaceNum:OnePlaceNumLogicShell;
      
      private var _isShowZero:Boolean;
      
      private var _num:int;
      
      public function MultiPlaceNumLogicShell()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         _show = param1;
         initOnePlaceNum();
      }
      
      public function getShow() : MovieClip
      {
         return _show;
      }
      
      public function clear() : void
      {
         _show = null;
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
         if(_tenPlaceNum)
         {
            _tenPlaceNum.clear();
         }
         _tenPlaceNum = null;
         if(_hundredPlaceNum)
         {
            _hundredPlaceNum.clear();
         }
         _hundredPlaceNum = null;
         ClearUtil.clearObject(_thousandPlaceNum);
         _thousandPlaceNum = null;
         ClearUtil.clearObject(_tenThousandPlaceNum);
         _tenThousandPlaceNum = null;
      }
      
      public function showNum(param1:int) : void
      {
         _num = param1;
         if(param1 < 0)
         {
            throw new Error();
         }
         if(param1 < 10)
         {
            initOnePlaceNum();
         }
         else if(param1 < 100)
         {
            initTwoPlaceNum();
         }
         else if(param1 < 1000)
         {
            initThreePlaceNum();
         }
         else if(param1 < 10000)
         {
            initFourPlaceNum();
         }
         else
         {
            if(param1 >= 100000)
            {
               throw new Error();
            }
            initFivePlaceNum();
         }
         var _loc3_:* = param1;
         param1 -= int(param1 / 100000) * 100000;
         var _loc6_:int = param1 / 10000;
         param1 -= _loc6_ * 10000;
         var _loc7_:int = param1 / 1000;
         param1 -= _loc7_ * 1000;
         var _loc5_:int = param1 / 100;
         param1 -= _loc5_ * 100;
         var _loc2_:int = param1 / 10;
         param1 -= _loc2_ * 10;
         var _loc4_:* = param1;
         if(_tenThousandPlaceNum)
         {
            _tenThousandPlaceNum.showNum(_loc6_);
         }
         if(_thousandPlaceNum)
         {
            _thousandPlaceNum.showNum(_loc7_);
         }
         if(_hundredPlaceNum)
         {
            _hundredPlaceNum.showNum(_loc5_);
         }
         if(_tenPlaceNum)
         {
            _tenPlaceNum.showNum(_loc2_);
         }
         if(_onePlaceNum)
         {
            _onePlaceNum.showNum(_loc4_);
         }
         if(_loc3_ < 10000 && _isShowZero == false)
         {
            if(_tenThousandPlaceNum)
            {
               _tenThousandPlaceNum.getShow().visible = false;
            }
         }
         else if(_tenThousandPlaceNum)
         {
            _tenThousandPlaceNum.getShow().visible = true;
         }
         if(_loc3_ < 1000 && _isShowZero == false)
         {
            if(_thousandPlaceNum)
            {
               _thousandPlaceNum.getShow().visible = false;
            }
         }
         else if(_thousandPlaceNum)
         {
            _thousandPlaceNum.getShow().visible = true;
         }
         if(_loc3_ < 100 && _isShowZero == false)
         {
            if(_hundredPlaceNum)
            {
               _hundredPlaceNum.getShow().visible = false;
            }
         }
         else if(_hundredPlaceNum)
         {
            _hundredPlaceNum.getShow().visible = true;
         }
         if(_loc3_ < 10 && _isShowZero == false)
         {
            if(_tenPlaceNum)
            {
               _tenPlaceNum.getShow().visible = false;
            }
         }
         else if(_tenPlaceNum)
         {
            _tenPlaceNum.getShow().visible = true;
         }
         _onePlaceNum.getShow().visible = true;
      }
      
      public function getNum() : int
      {
         return _num;
      }
      
      public function setIsShowZero(param1:Boolean) : void
      {
         _isShowZero = param1;
      }
      
      private function initOnePlaceNum() : void
      {
         clearOnePlaceNum();
         clearTwoPlaceNum();
         clearThreePlaceNum();
         clearFourPlaceNum();
         clearFivePlaceNum();
         try
         {
            _show.gotoAndPlay("one");
         }
         catch(error:Error)
         {
            _show.gotoAndStop(1);
         }
         if(_show["tenThousand"])
         {
            _tenThousandPlaceNum = new OnePlaceNumLogicShell();
            _tenThousandPlaceNum.setShow(_show["tenThousand"]);
         }
         if(_show["thousand"])
         {
            _thousandPlaceNum = new OnePlaceNumLogicShell();
            _thousandPlaceNum.setShow(_show["thousand"]);
         }
         if(_show["hundred"])
         {
            _hundredPlaceNum = new OnePlaceNumLogicShell();
            _hundredPlaceNum.setShow(_show["hundred"]);
         }
         if(_show["ten"])
         {
            _tenPlaceNum = new OnePlaceNumLogicShell();
            _tenPlaceNum.setShow(_show["ten"]);
         }
         _onePlaceNum = new OnePlaceNumLogicShell();
         _onePlaceNum.setShow(_show["one"]);
      }
      
      private function clearOnePlaceNum() : void
      {
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
      }
      
      private function initTwoPlaceNum() : void
      {
         clearOnePlaceNum();
         clearTwoPlaceNum();
         clearThreePlaceNum();
         clearFourPlaceNum();
         clearFivePlaceNum();
         try
         {
            _show.gotoAndStop("two");
         }
         catch(error:Error)
         {
            _show.gotoAndStop(2);
         }
         if(_show["tenThousand"])
         {
            _tenThousandPlaceNum = new OnePlaceNumLogicShell();
            _tenThousandPlaceNum.setShow(_show["tenThousand"]);
         }
         if(_show["thousand"])
         {
            _thousandPlaceNum = new OnePlaceNumLogicShell();
            _thousandPlaceNum.setShow(_show["thousand"]);
         }
         if(_show["hundred"])
         {
            _hundredPlaceNum = new OnePlaceNumLogicShell();
            _hundredPlaceNum.setShow(_show["hundred"]);
         }
         _tenPlaceNum = new OnePlaceNumLogicShell();
         _tenPlaceNum.setShow(_show["ten"]);
         _onePlaceNum = new OnePlaceNumLogicShell();
         _onePlaceNum.setShow(_show["one"]);
      }
      
      private function clearTwoPlaceNum() : void
      {
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
         if(_tenPlaceNum)
         {
            _tenPlaceNum.clear();
         }
         _tenPlaceNum = null;
      }
      
      private function initThreePlaceNum() : void
      {
         clearOnePlaceNum();
         clearTwoPlaceNum();
         clearThreePlaceNum();
         clearFourPlaceNum();
         clearFivePlaceNum();
         try
         {
            _show.gotoAndStop("three");
         }
         catch(error:Error)
         {
            _show.gotoAndStop(3);
         }
         if(_show["tenThousand"])
         {
            _tenThousandPlaceNum = new OnePlaceNumLogicShell();
            _tenThousandPlaceNum.setShow(_show["tenThousand"]);
         }
         if(_show["thousand"])
         {
            _thousandPlaceNum = new OnePlaceNumLogicShell();
            _thousandPlaceNum.setShow(_show["thousand"]);
         }
         _hundredPlaceNum = new OnePlaceNumLogicShell();
         _hundredPlaceNum.setShow(_show["hundred"]);
         _tenPlaceNum = new OnePlaceNumLogicShell();
         _tenPlaceNum.setShow(_show["ten"]);
         _onePlaceNum = new OnePlaceNumLogicShell();
         _onePlaceNum.setShow(_show["one"]);
      }
      
      private function clearThreePlaceNum() : void
      {
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
         if(_tenPlaceNum)
         {
            _tenPlaceNum.clear();
         }
         _tenPlaceNum = null;
         if(_hundredPlaceNum)
         {
            _hundredPlaceNum.clear();
         }
         _hundredPlaceNum = null;
      }
      
      private function initFourPlaceNum() : void
      {
         clearOnePlaceNum();
         clearTwoPlaceNum();
         clearThreePlaceNum();
         clearFourPlaceNum();
         clearFivePlaceNum();
         try
         {
            _show.gotoAndStop("four");
         }
         catch(error:Error)
         {
            _show.gotoAndStop(4);
         }
         if(_show["tenThousand"])
         {
            _tenThousandPlaceNum = new OnePlaceNumLogicShell();
            _tenThousandPlaceNum.setShow(_show["tenThousand"]);
         }
         _thousandPlaceNum = new OnePlaceNumLogicShell();
         _thousandPlaceNum.setShow(_show["thousand"]);
         _hundredPlaceNum = new OnePlaceNumLogicShell();
         _hundredPlaceNum.setShow(_show["hundred"]);
         _tenPlaceNum = new OnePlaceNumLogicShell();
         _tenPlaceNum.setShow(_show["ten"]);
         _onePlaceNum = new OnePlaceNumLogicShell();
         _onePlaceNum.setShow(_show["one"]);
      }
      
      private function clearFourPlaceNum() : void
      {
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
         if(_tenPlaceNum)
         {
            _tenPlaceNum.clear();
         }
         _tenPlaceNum = null;
         if(_hundredPlaceNum)
         {
            _hundredPlaceNum.clear();
         }
         _hundredPlaceNum = null;
         ClearUtil.clearObject(_thousandPlaceNum);
         _thousandPlaceNum = null;
      }
      
      private function initFivePlaceNum() : void
      {
         clearOnePlaceNum();
         clearTwoPlaceNum();
         clearThreePlaceNum();
         clearFourPlaceNum();
         clearFivePlaceNum();
         try
         {
            _show.gotoAndStop("five");
         }
         catch(error:Error)
         {
            _show.gotoAndStop(5);
         }
         _tenThousandPlaceNum = new OnePlaceNumLogicShell();
         _tenThousandPlaceNum.setShow(_show["tenThousand"]);
         _thousandPlaceNum = new OnePlaceNumLogicShell();
         _thousandPlaceNum.setShow(_show["thousand"]);
         _hundredPlaceNum = new OnePlaceNumLogicShell();
         _hundredPlaceNum.setShow(_show["hundred"]);
         _tenPlaceNum = new OnePlaceNumLogicShell();
         _tenPlaceNum.setShow(_show["ten"]);
         _onePlaceNum = new OnePlaceNumLogicShell();
         _onePlaceNum.setShow(_show["one"]);
      }
      
      private function clearFivePlaceNum() : void
      {
         if(_onePlaceNum)
         {
            _onePlaceNum.clear();
         }
         _onePlaceNum = null;
         if(_tenPlaceNum)
         {
            _tenPlaceNum.clear();
         }
         _tenPlaceNum = null;
         if(_hundredPlaceNum)
         {
            _hundredPlaceNum.clear();
         }
         _hundredPlaceNum = null;
         ClearUtil.clearObject(_thousandPlaceNum);
         _thousandPlaceNum = null;
         ClearUtil.clearObject(_tenThousandPlaceNum);
         _tenThousandPlaceNum = null;
      }
   }
}

