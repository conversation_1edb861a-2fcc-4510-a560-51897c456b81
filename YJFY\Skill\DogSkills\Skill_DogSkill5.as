package YJFY.Skill.DogSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_DogBigSkill;
   import YJFY.World.World;
   
   public class Skill_DogSkill5 extends CuboidAreaAttackSkill2_DogBigSkill
   {
      
      public function Skill_DogSkill5()
      {
         super();
      }
      
      override public function startSkill(param1:World) : <PERSON><PERSON>an
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

