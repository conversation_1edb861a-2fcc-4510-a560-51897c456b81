package YJFY.XydzjsLogic
{
   import UI.GamingUI;
   import YJFY.EndlessMode.EndlessManage;
   
   public class RecoverState_endLevel
   {
      
      public function RecoverState_endLevel()
      {
         super();
      }
      
      public function recoverState_endLevel1() : void
      {
         if(EndlessManage.getInstance().IsEndlessMode)
         {
            return;
         }
         GamingUI.getInstance().addHp(GamingUI.getInstance().player1.playerVO.bloodVolume,GamingUI.getInstance().player1);
         if(<PERSON>olean(GamingUI.getInstance().player2) && Boolean(GamingUI.getInstance().player2.playerVO))
         {
            GamingUI.getInstance().addHp(GamingUI.getInstance().player2.playerVO.bloodVolume,GamingUI.getInstance().player2);
         }
         GamingUI.getInstance().getAutomaticPetsData().recoverAllAutomaticPetsHp();
      }
   }
}

