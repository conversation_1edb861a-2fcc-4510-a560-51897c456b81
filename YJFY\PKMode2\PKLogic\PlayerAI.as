package YJFY.PKMode2.PKLogic
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAI.AILogic.IsOneAliveEntity;
   import YJFY.EntityAI.AILogic.IsOneEffectiveTarget;
   import YJFY.EntityAI.AutomationAI.EvasionLogicAI.RunSkillEvasionAI;
   import YJFY.EntityAI.AutomationAI.InterAttackAI;
   import YJFY.EntityAI.AutomationAI.MoveForRunSkillOrAttack.MoveForRunSkillOrAttack;
   import YJFY.EntityAI.AutomationAI.RunSkillAI;
   import YJFY.EntityAI.AutomationAI.RunSkillAIListener;
   import YJFY.EntityAI.AutomationAI.SearchAttackTargetAI;
   import YJFY.Skill.Skill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class PlayerAI
   {
      
      private var m_searchAttackTargetAI:SearchAttackTargetAI;
      
      private var m_interAttackAI:InterAttackAI;
      
      private var m_runSkillAI:RunSkillAI;
      
      private var m_runSkillAIListener:RunSkillAIListener;
      
      private var m_moveForRunSkillOrAttackAI:MoveForRunSkillOrAttack;
      
      private var m_runSkillEvasionAI:RunSkillEvasionAI;
      
      private var m_isOneAliveEntity:IsOneAliveEntity;
      
      private var m_isOneEffectiveTarget:IsOneEffectiveTarget;
      
      private var m_playerAIEntity:PlayerAIEntity;
      
      private var m_owner:IAnimalEntity;
      
      private var m_world:World;
      
      public function PlayerAI()
      {
         super();
         m_searchAttackTargetAI = new SearchAttackTargetAI();
         m_interAttackAI = new InterAttackAI();
         m_runSkillAI = new RunSkillAI();
         m_runSkillAIListener = new RunSkillAIListener();
         m_runSkillAIListener.preRunSkillFun = preRunSkill;
         m_runSkillAI.addRunSkillAIListener(m_runSkillAIListener);
         m_moveForRunSkillOrAttackAI = new MoveForRunSkillOrAttack();
         m_runSkillEvasionAI = new RunSkillEvasionAI();
         m_isOneAliveEntity = new IsOneAliveEntity();
         m_isOneEffectiveTarget = new IsOneEffectiveTarget();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_moveForRunSkillOrAttackAI);
         m_moveForRunSkillOrAttackAI = null;
         ClearUtil.clearObject(m_runSkillAI);
         m_runSkillAI = null;
         ClearUtil.clearObject(m_runSkillAIListener);
         m_runSkillAIListener = null;
         ClearUtil.clearObject(m_interAttackAI);
         m_interAttackAI = null;
         ClearUtil.clearObject(m_searchAttackTargetAI);
         m_searchAttackTargetAI = null;
         ClearUtil.clearObject(m_runSkillEvasionAI);
         m_runSkillEvasionAI = null;
         ClearUtil.clearObject(m_isOneAliveEntity);
         m_isOneAliveEntity = null;
         ClearUtil.clearObject(m_isOneEffectiveTarget);
         m_isOneEffectiveTarget = null;
         m_playerAIEntity = null;
         m_owner = null;
         m_world = null;
      }
      
      public function init(param1:IEntity, param2:World, param3:PlayerAIEntity) : void
      {
         m_owner = param1 as IAnimalEntity;
         m_world = param2;
         m_playerAIEntity = param3;
         m_searchAttackTargetAI.init(m_owner,m_world,m_playerAIEntity.getPlayerXydzjs(),m_playerAIEntity.getRebuildAllFoeInterval());
         m_interAttackAI.init(m_owner,m_world,0,0,m_searchAttackTargetAI);
         m_runSkillAI.init(m_owner,m_world,m_playerAIEntity,m_searchAttackTargetAI);
         m_moveForRunSkillOrAttackAI.init(m_owner,m_world,m_searchAttackTargetAI,m_interAttackAI,m_runSkillAI);
         m_runSkillEvasionAI.setRunSkillEntity(m_playerAIEntity);
         m_runSkillEvasionAI.init(m_owner,m_world);
      }
      
      public function render() : void
      {
         if(m_isOneAliveEntity.isOneAliveEntity(m_owner) == false)
         {
            return;
         }
         if(m_playerAIEntity.getPlayerXydzjs().getIsInRideState())
         {
            m_playerAIEntity.getPlayerXydzjs().downFromMount_directly();
         }
         m_searchAttackTargetAI.render();
         m_moveForRunSkillOrAttackAI.render();
         m_playerAIEntity.runSuper();
         m_runSkillAI.render();
         m_interAttackAI.render();
         m_runSkillEvasionAI.render();
      }
      
      private function preRunSkill(param1:RunSkillAI, param2:Skill) : void
      {
      }
   }
}

