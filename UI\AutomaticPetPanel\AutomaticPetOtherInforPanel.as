package UI.AutomaticPetPanel
{
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetSkillVO;
   import YJFY.Loader.IProgressShow;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class AutomaticPetOtherInforPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_ableDragLg:AbleDragSpriteLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_hpText:TextField;
      
      private var m_mpText:TextField;
      
      private var m_attackText:TextField;
      
      private var m_defenceText:TextField;
      
      private var m_hitRateText:TextField;
      
      private var m_dogdeRateText:TextField;
      
      private var m_criticalRateText:TextField;
      
      private var m_decriticalRateText:TextField;
      
      private var m_activeSkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_passiveSkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_auxiliarySkillCells:Vector.<AutomaticPetSkillCellLogic>;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_automaticPetVO:AutomaticPetVO;
      
      private var m_automaticPetInforPanel:AutomaticPetInforPanel;
      
      public function AutomaticPetOtherInforPanel()
      {
         super();
         m_ableDragLg = new AbleDragSpriteLogicShell();
         m_font = new FangZhengKaTongJianTi();
         m_loadUI = Part1.getInstance().getLoadUI();
         m_versionControl = Part1.getInstance().getVersionControl();
         m_quitBtn = new ButtonLogicShell2();
         m_activeSkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         m_passiveSkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         m_auxiliarySkillCells = new Vector.<AutomaticPetSkillCellLogic>();
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         m_hpText = null;
         m_mpText = null;
         m_attackText = null;
         m_defenceText = null;
         m_hitRateText = null;
         m_dogdeRateText = null;
         m_criticalRateText = null;
         m_decriticalRateText = null;
         ClearUtil.clearObject(m_activeSkillCells);
         m_activeSkillCells = null;
         ClearUtil.clearObject(m_passiveSkillCells);
         m_passiveSkillCells = null;
         ClearUtil.clearObject(m_auxiliarySkillCells);
         m_auxiliarySkillCells = null;
         m_font = null;
         ClearUtil.clearObject(m_ableDragLg);
         m_ableDragLg = null;
         m_loadUI = null;
         m_versionControl = null;
         m_automaticPetVO = null;
         m_automaticPetInforPanel = null;
         super.clear();
      }
      
      public function init(param1:AutomaticPetInforPanel) : void
      {
         m_automaticPetInforPanel = param1;
         m_show = MyFunction2.returnShowByClassName("AutoPetOtherInforPanel") as MovieClip;
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      public function setAutomaticPetVO(param1:AutomaticPetVO) : void
      {
         m_automaticPetVO = param1;
         initShow2();
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         if(m_automaticPetVO == null)
         {
            return;
         }
         m_hpText.text = m_automaticPetVO.getTotalHp().toString();
         m_mpText.text = m_automaticPetVO.getTotalMp().toString();
         m_attackText.text = m_automaticPetVO.getAttack().toString();
         m_defenceText.text = m_automaticPetVO.getDefence().toString();
         m_hitRateText.text = (m_automaticPetVO.getHitRate() * 100).toFixed(2) + "%";
         m_dogdeRateText.text = (m_automaticPetVO.getDogdeRate() * 100).toFixed(2) + "%";
         m_criticalRateText.text = (m_automaticPetVO.getCriticalRate() * 100).toFixed(2) + "%";
         m_decriticalRateText.text = (m_automaticPetVO.getDeCriticalRate() * 100).toFixed(2) + "%";
         var _loc2_:int = int(m_automaticPetVO.getActiveSkillVONum());
         var _loc1_:int = int(m_activeSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_activeSkillCells[_loc3_].setData(null,null);
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_activeSkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getActiveSkillVOByIndex(_loc3_));
            _loc3_++;
         }
         _loc2_ = int(m_automaticPetVO.getAuxiliarySkillVONum());
         _loc1_ = int(m_auxiliarySkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_auxiliarySkillCells[_loc3_].setData(null,null);
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_auxiliarySkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getAuxiliarySkillVOByIndex(_loc3_));
            _loc3_++;
         }
         _loc2_ = int(m_automaticPetVO.getPassiveSkillVONum());
         _loc1_ = int(m_passiveSkillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_passiveSkillCells[_loc3_].setData(null,null);
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_ && _loc3_ < _loc1_)
         {
            m_passiveSkillCells[_loc3_].setData(m_automaticPetVO,m_automaticPetVO.getPassiveSkillVOByIndex(_loc3_) as AutomaticPetSkillVO);
            _loc3_++;
         }
      }
      
      private function initShow() : void
      {
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc5_:AutomaticPetSkillCellLogic = null;
         m_ableDragLg.setShow(m_show);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_hpText = m_show["hpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hpText);
         m_mpText = m_show["mpText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_mpText);
         m_attackText = m_show["attackText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_attackText);
         m_defenceText = m_show["defenceText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_defenceText);
         m_hitRateText = m_show["hitRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_hitRateText);
         m_dogdeRateText = m_show["dogdeRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_dogdeRateText);
         m_criticalRateText = m_show["criticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_criticalRateText);
         m_decriticalRateText = m_show["decriticalRateText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_decriticalRateText);
         var _loc4_:int = m_show.numChildren;
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc1_ = m_show.getChildAt(_loc7_);
            if(_loc1_.name.substr(0,"activeSkillContainer_".length) == "activeSkillContainer_")
            {
               _loc3_++;
            }
            else if(_loc1_.name.substr(0,"passiveSkillContainer_".length) == "passiveSkillContainer_")
            {
               _loc6_++;
            }
            else if(_loc1_.name.substr(0,"auxiliarySkillContainer_".length) == "auxiliarySkillContainer_")
            {
               _loc2_++;
            }
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc5_ = new AutomaticPetSkillCellLogic();
            _loc5_.setLoadUI(m_loadUI);
            _loc5_.setVersionControl(m_versionControl);
            _loc5_.setShow(m_show["activeSkillContainer_" + (_loc7_ + 1)],null);
            m_activeSkillCells.push(_loc5_);
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc5_ = new AutomaticPetSkillCellLogic();
            _loc5_.setLoadUI(m_loadUI);
            _loc5_.setVersionControl(m_versionControl);
            _loc5_.setShow(m_show["passiveSkillContainer_" + (_loc7_ + 1)],null);
            m_passiveSkillCells.push(_loc5_);
            _loc7_++;
         }
         _loc7_ = 0;
         while(_loc7_ < _loc2_)
         {
            _loc5_ = new AutomaticPetSkillCellLogic();
            _loc5_.setLoadUI(m_loadUI);
            _loc5_.setVersionControl(m_versionControl);
            _loc5_.setShow(m_show["auxiliarySkillContainer_" + (_loc7_ + 1)],null);
            m_auxiliarySkillCells.push(_loc5_);
            _loc7_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_quitBtn === _loc2_)
         {
            m_automaticPetInforPanel.closeAutomaticPetOtherInforPanel();
         }
      }
   }
}

