package UI.SocietySystem.MemberListPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class MemberColume extends ButtonLogicShell2
   {
      
      private const m_const_titleId_bangZhong:String = "bangZhong";
      
      private var m_clear:ClearHelper;
      
      private var m_playerNameText:TextField;
      
      private var m_playerLevelText:TextField;
      
      private var m_conValueText:TextField;
      
      private var m_titleNameText:TextField;
      
      private var m_isOnLineShow:MovieClipPlayLogicShell;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_titleIconShow:MovieClipPlayLogicShell;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_titleName:String;
      
      private var m_titleId:String;
      
      private var m_memberData:MemberDataInMemberList;
      
      public function MemberColume()
      {
         super();
         m_clear = new ClearHelper();
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         super.clear();
         m_playerNameText = null;
         m_playerLevelText = null;
         m_conValueText = null;
         m_titleNameText = null;
         ClearUtil.clearObject(m_isOnLineShow);
         m_isOnLineShow = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_titleIconShow);
         m_titleIconShow = null;
         m_memberData = null;
      }
      
      public function setMemberDataInList(param1:MemberDataInMemberList, param2:String, param3:String) : void
      {
         m_memberData = param1;
         m_titleName = param3;
         m_titleId = param2;
         initShow();
         initShow2();
      }
      
      private function initShow() : void
      {
         m_playerNameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerNameText);
         m_playerNameText.mouseEnabled = false;
         m_playerLevelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerLevelText);
         m_playerLevelText.mouseEnabled = false;
         m_conValueText = m_show["conValueText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_conValueText);
         m_conValueText.mouseEnabled = false;
         m_titleNameText = m_show["titleText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_titleNameText);
         m_titleNameText.mouseEnabled = false;
         m_isOnLineShow = new MovieClipPlayLogicShell();
         m_isOnLineShow.setShow(m_show["isOnLineShow"]);
         m_titleIconShow = new MovieClipPlayLogicShell();
         m_titleIconShow.setShow(m_show["titleIconShow"]);
         if(m_show["lookUpBtn"])
         {
            m_lookUpBtn = new ButtonLogicShell2();
            m_lookUpBtn.setShow(m_show["lookUpBtn"]);
            m_lookUpBtn.setTipString("点击查看该玩家装备信息");
         }
      }
      
      private function initShow2() : void
      {
         m_playerNameText.text = m_memberData.getName_member() ? m_memberData.getName_member() : m_memberData.getUid_member().toString();
         m_playerLevelText.text = m_memberData.getLevel_member().toString();
         m_isOnLineShow.gotoAndStop((m_memberData.getIsOnLineMember() + 1).toString());
         m_conValueText.text = m_memberData.getPersonalReConValue() + "/" + m_memberData.getPersonalTotalConValue();
         m_titleNameText.text = m_titleName;
         setTitleIconShow();
      }
      
      public function getMemberData() : MemberDataInMemberList
      {
         return m_memberData;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      override protected function onDown(param1:MouseEvent) : void
      {
         super.onDown(param1);
         changeTextField();
      }
      
      override protected function onUp(param1:MouseEvent) : void
      {
         super.onUp(param1);
         changeTextField();
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         super.onOut(param1);
         changeTextField();
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         super.onOver(param1);
         changeTextField();
      }
      
      private function changeTextField() : void
      {
         var _loc1_:String = m_playerNameText.text;
         m_playerNameText = m_show["nameText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerNameText);
         m_playerNameText.mouseEnabled = false;
         m_playerNameText.text = _loc1_;
         _loc1_ = m_playerLevelText.text;
         m_playerLevelText = m_show["levelText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_playerLevelText);
         m_playerLevelText.mouseEnabled = false;
         m_playerLevelText.text = _loc1_;
         _loc1_ = m_conValueText.text;
         m_conValueText = m_show["conValueText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_conValueText);
         m_conValueText.mouseEnabled = false;
         m_conValueText.text = _loc1_;
         _loc1_ = m_titleNameText.text;
         m_titleNameText = m_show["titleText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_titleNameText);
         m_titleNameText.mouseEnabled = false;
         m_titleNameText.text = _loc1_;
         setTitleIconShow();
      }
      
      private function changeTextFieldColor(param1:TextField, param2:uint) : void
      {
         var _loc3_:TextFormat = param1.defaultTextFormat;
         _loc3_.color = param2;
         param1.defaultTextFormat = _loc3_;
      }
      
      private function setTitleIconShow() : void
      {
         if(m_titleId == "bangZhong")
         {
            m_titleNameText.visible = true;
            m_titleIconShow.getShow().visible = false;
         }
         else
         {
            m_titleNameText.visible = false;
            m_titleIconShow.getShow().visible = true;
         }
         m_titleIconShow.gotoAndStop(m_titleId);
      }
   }
}

