package YJFY.XydzjsData.AISkillVO
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class AISkillVOFactory
   {
      
      private var m_classes:Array;
      
      public function AISkillVOFactory()
      {
         super();
         m_classes = [AIActiveSkillVO,AIAttackSkillVO,AIRecoverHpSkillVO];
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_classes);
         m_classes = null;
      }
      
      public function createBossSkillVOByClassName(param1:String) : AIActiveSkillVO
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
   }
}

