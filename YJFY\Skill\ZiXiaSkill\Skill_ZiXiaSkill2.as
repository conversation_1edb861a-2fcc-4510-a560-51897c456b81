package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.GameEntity.Boss;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.GameEntity;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   public class Skill_ZiXiaSkill2 extends CuboidAreaAttackSkill_OneSkillShow
   {
      
      private var m_skillStartFrameLabel:String;
      
      private var m_skillAttackReachFrameLabel:String;
      
      private var m_skillEffectReachFrameLabel:String;
      
      private var m_currentFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_nOutOfControlDuration:Number;
      
      private var m_nEndTime:Number;
      
      private var m_nTime:Number = 0;
      
      private var m_nRenderOutOfControl:int;
      
      private var m_nVector:Vector.<Number>;
      
      private var m_nMoveSpeed:Number = 0;
      
      private var m_Timer:Timer;
      
      protected var m_skillEffectDefinitionData:AnimationDefinitionData;
      
      protected var m_skillEffectId:String;
      
      protected var m_skillEffectAnimationShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_skillEffectAnimationShowPlaysPool:ObjectsPool;
      
      protected var m_skillWasteEffecAnimationShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      public function Skill_ZiXiaSkill2()
      {
         var _loc1_:int = 0;
         super();
         ClearUtil.clearObject(m_nVector);
         m_nVector = null;
         m_nVector = new Vector.<Number>();
         _loc1_ = 0;
         while(_loc1_ < 4)
         {
            m_nVector.push(2 * Math.random() - 1);
            _loc1_++;
         }
         m_isAttackReachWhenRelease = false;
         m_currentFrameLabel = "";
         m_skillEffectAnimationShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_skillWasteEffecAnimationShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_skillEffectAnimationShowPlaysPool = new ObjectsPool(m_skillEffectAnimationShowPlays,m_skillWasteEffecAnimationShowPlays,createEffectAniamtionShowPlay,null);
      }
      
      private function createEffectAniamtionShowPlay() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_skillEffectDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      override public function clear() : void
      {
         this.destroy();
         clearSkillEffectAnimationPlays();
         clearSkillWasteEffectAnimationPlays();
         m_skillEffectId = null;
         ClearUtil.clearObject(m_skillEffectDefinitionData);
         m_skillEffectDefinitionData = null;
         ClearUtil.clearObject(m_skillEffectAnimationShowPlays);
         m_skillEffectAnimationShowPlays = null;
         ClearUtil.clearObject(m_skillWasteEffecAnimationShowPlays);
         m_skillWasteEffecAnimationShowPlays = null;
         ClearUtil.clearObject(m_effectAnimationShowPlaysPool);
         m_effectAnimationShowPlaysPool = null;
         super.clear();
      }
      
      private function destroy() : void
      {
         this.clearEffectAnimationPlays();
         ClearUtil.clearObject(m_nVector);
         m_nVector = null;
         if(m_Timer)
         {
            m_Timer.stop();
            m_Timer.removeEventListener("timer",onTimer);
            m_Timer = null;
         }
      }
      
      protected function onTimer(param1:TimerEvent) : void
      {
         var _loc4_:AnimationShowPlayLogicShell = null;
         var _loc5_:int = 0;
         var _loc2_:IEntity = null;
         if(m_world && m_world.isStop())
         {
            return;
         }
         if(Part1.getInstance().getGameIsStop())
         {
            return;
         }
         if(Part1.getInstance().getLevel() && Part1.getInstance().getLevel().m_gameIsEnd)
         {
            return;
         }
         if(Part1.getInstance().getIsInCityMap())
         {
            if(m_Timer)
            {
               m_Timer.stop();
               m_Timer.removeEventListener("timer",onTimer);
               m_Timer = null;
               m_nTime = 0;
            }
            return;
         }
         m_nTime += 20;
         if(m_nTime > m_nOutOfControlDuration)
         {
            m_nTime = 0;
            clearEffectAnimationPlays();
         }
         m_nRenderOutOfControl++;
         if(m_nRenderOutOfControl >= 25)
         {
            m_nRenderOutOfControl = 0;
         }
         var _loc3_:int = m_skillEffectAnimationShowPlays ? m_skillEffectAnimationShowPlays.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = m_skillEffectAnimationShowPlays[_loc5_];
            _loc2_ = _loc4_.extra as IEntity;
            if(_loc2_ && !(_loc2_ as AnimalEntity).isInDie() && (_loc2_ as AnimalEntity).isOutOfControl)
            {
               this.outOfControl(_loc4_.extra as IEntity);
            }
            else
            {
               (_loc4_.extra as IEntity).removeOtherAnimation(_loc4_);
               m_skillEffectAnimationShowPlaysPool.wasteOneObj(_loc4_);
               _loc5_--;
               _loc3_--;
            }
            _loc5_++;
         }
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         m_Timer = new Timer(20);
         m_Timer.addEventListener("timer",onTimer,false,0,true);
         releaseSkill2(param1);
         return true;
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillStartFrameLabel = String(param1.@skillStartFrameLabel);
         m_skillAttackReachFrameLabel = String(param1.@skillAttackReachFrameLabel);
         m_skillEffectReachFrameLabel = String(param1.@skillEffectReachFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_nOutOfControlDuration = Number(param1.@outOfControl);
         m_skillEffectId = String(param1.@skillEffectDefId);
         if(m_skillEffectId)
         {
            m_skillEffectDefinitionData = new AnimationDefinitionData();
            m_skillEffectDefinitionData.initByXML(param1.animationDefinition.(@id == m_skillEffectId)[0]);
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case m_skillEffectReachFrameLabel:
               m_currentFrameLabel = m_skillEffectReachFrameLabel;
               attackReach(m_owner.getWorld());
               break;
            case m_skillAttackReachFrameLabel:
               m_currentFrameLabel = m_skillAttackReachFrameLabel;
               attackReach(m_owner.getWorld());
               break;
            case m_skillEndFrameLabel:
               endSkill1();
         }
      }
      
      protected function addEffect(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if((param1.getExtra() as EnemyXydzjs).getCurrentHp() <= 0)
         {
            (param1.getExtra() as EnemyXydzjs).getAnimalEntity().die();
            return;
         }
         if(!(param1 as AnimalEntity).isFear && !(param1 as AnimalEntity).isOutOfControl)
         {
            (param1 as AnimalEntity).isOutOfControl = true;
         }
         else
         {
            if(!(param1 as AnimalEntity).isFear)
            {
               return;
            }
            (param1 as AnimalEntity).isFear = false;
            (param1 as AnimalEntity).isFear1 = false;
            (param1 as IEntity).removeFearAnimation();
            (param1 as AnimalEntity).isOutOfControl = true;
         }
         if(!(param1 as AnimalEntity).notShowBeattack)
         {
            (param1 as AnimalEntity).isOutOfControlEffect = true;
         }
         m_Timer.start();
         m_nTime = 0;
         if(m_skillEffectDefinitionData)
         {
            _loc2_ = m_skillEffectAnimationShowPlaysPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc2_.gotoAndPlay("start");
            _loc2_.extra = param1;
            _loc2_.getDisplayShow().name = "zixiaSkill2Effect";
            _loc2_.getDisplayShow().scaleX = _loc2_.getDisplayShow().scaleY = 0.8;
            _loc2_.getDisplayShow().x = _loc2_.getDisplayShow().x - 5;
            _loc2_.getDisplayShow().y = 0 - param1.getBodyZRange();
            param1.addOtherAniamtion(_loc2_);
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         if(m_currentFrameLabel == m_skillEffectReachFrameLabel)
         {
            if(dontAttacktangsheng && param1 is TangMonkEntity)
            {
               return;
            }
            if(param1 is AnimalEntity && (param1 as AnimalEntity).isInDie())
            {
               return;
            }
            if(!(param1.getExtra() is EnemyXydzjs) || (param1 as AnimalEntity).isInGold)
            {
               return;
            }
            if((param1.getExtra() as EnemyXydzjs).isInvincible())
            {
               return;
            }
            this.addEffect(param1);
            if(param1.getExtra() is EnemyXydzjs)
            {
               if((param1 as AnimalEntity).isinWalk())
               {
                  m_nMoveSpeed = (param1 as AnimalEntity).m_walkSpeed;
               }
               if((param1 as AnimalEntity).isInRun())
               {
                  m_nMoveSpeed = (param1 as AnimalEntity).m_runSpeed;
               }
               (param1 as AnimalEntity).setMoveSpeed(m_nMoveSpeed * 0.3);
            }
         }
         else
         {
            super.attackSuccess(param1);
         }
      }
      
      private function outOfControl(param1:IEntity) : void
      {
         var _loc4_:Object = null;
         var _loc3_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc5_:int = 0;
         if(!(param1 as IAnimalEntity).isInHurt() || !(param1 as IAnimalEntity).isInDie())
         {
            _loc4_ = param1.getExtra();
            _loc3_ = Number((_loc4_ as GameEntity).getAnimalEntity().getX());
            _loc2_ = Number((_loc4_ as GameEntity).getAnimalEntity().getY());
            if(m_nRenderOutOfControl == 0)
            {
               ClearUtil.clearObject(m_nVector);
               m_nVector.length = 0;
               _loc5_ = 0;
               while(_loc5_ < 4)
               {
                  m_nVector.push(2 * Math.random() - 1);
                  _loc5_++;
               }
            }
            if(_loc4_ is Boss)
            {
               (_loc4_ as EnemyXydzjs).getAnimalEntity().walk();
               (_loc4_ as EnemyXydzjs).getAnimalEntity().setDirection(m_nVector[0],m_nVector[1]);
               randPosition(param1,m_nVector[0],m_nVector[1],_loc3_,_loc2_,2);
            }
            else
            {
               (_loc4_ as EnemyXydzjs).getAnimalEntity().walk();
               (_loc4_ as EnemyXydzjs).getAnimalEntity().setDirection(m_nVector[2],m_nVector[3]);
               randPosition(param1,m_nVector[2],m_nVector[3],_loc3_,_loc2_,0.03);
            }
         }
      }
      
      private function randPosition(param1:IEntity, param2:Number, param3:Number, param4:Number, param5:Number, param6:Number) : void
      {
         var _loc7_:Object = param1.getExtra();
         if(param2 > 0 && param3 > 0)
         {
            if(param5 + param6 <= 400)
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 + param6,param5 + param6,0);
            }
            else
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 + param6,param5,0);
            }
         }
         else if(param2 < 0 && param3 < 0)
         {
            if(param5 - param6 >= 0)
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 - param6,param5 - param6,0);
            }
            else
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 - param6,param5,0);
            }
         }
         else if(param2 < 0 && param3 > 0)
         {
            if(param5 + param6 <= 400)
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 - param6,param5 + param6,0);
            }
            else
            {
               (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 - param6,param5,0);
            }
         }
         else if(param5 - param6 >= 0)
         {
            (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 + param6,param5 - param6,0);
         }
         else
         {
            (_loc7_ as GameEntity).getAnimalEntity().setNewPosition(param4 + param6,param5,0);
         }
      }
      
      override protected function clearEffectAnimationPlays() : void
      {
         var _loc3_:AnimationShowPlayLogicShell = null;
         var _loc2_:IEntity = null;
         var _loc4_:int = 0;
         if(m_Timer)
         {
            m_Timer.stop();
         }
         var _loc1_:int = m_skillEffectAnimationShowPlays ? m_skillEffectAnimationShowPlays.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = m_skillEffectAnimationShowPlays[_loc4_];
            _loc2_ = _loc3_.extra as IEntity;
            if(_loc2_)
            {
               if((_loc2_ as AnimalEntity).m_entityLogicVO && !(_loc2_ as AnimalEntity).isInDie() && (_loc2_ as AnimalEntity).isOutOfControl)
               {
                  (_loc2_ as AnimalEntity).isOutOfControl = false;
                  (_loc2_ as AnimalEntity).isOutOfControlEffect = false;
                  if(m_nMoveSpeed == 0)
                  {
                     if((_loc2_ as AnimalEntity).isinWalk())
                     {
                        m_nMoveSpeed = (_loc2_ as AnimalEntity).m_walkSpeed;
                     }
                     if((_loc2_ as AnimalEntity).isInRun())
                     {
                        m_nMoveSpeed = (_loc2_ as AnimalEntity).m_runSpeed;
                     }
                  }
                  (_loc2_ as AnimalEntity).getBodyShowContainer().scaleX = 1;
                  (_loc2_ as AnimalEntity).setMoveSpeed(m_nMoveSpeed);
               }
               (_loc3_.extra as IEntity).removeOtherAnimation(_loc3_);
               m_skillEffectAnimationShowPlaysPool.wasteOneObj(_loc3_);
               _loc1_--;
               _loc4_--;
            }
            _loc4_++;
         }
      }
      
      protected function clearSkillWasteEffectAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_skillWasteEffecAnimationShowPlays ? m_skillWasteEffecAnimationShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_skillWasteEffecAnimationShowPlays[0];
            m_skillWasteEffecAnimationShowPlays.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_.getShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
      }
      
      protected function clearSkillEffectAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_skillEffectAnimationShowPlays ? m_skillEffectAnimationShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_effectAnimationShowPlays[0];
            m_skillEffectAnimationShowPlays.splice(0,1);
            _loc1_--;
            if(_loc2_.extra)
            {
               (_loc2_.extra as IEntity).removeOtherAnimation(_loc2_);
            }
            ClearUtil.clearObject(_loc2_.getShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
      }
   }
}

