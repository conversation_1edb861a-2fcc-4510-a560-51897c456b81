package UI.LogicShell
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.SoundData;
   import flash.events.MouseEvent;
   
   public class ButtonLogicShell extends YJFY.ShowLogicShell.ButtonLogicShell
   {
      
      protected var m_buttonSoundData:SoundData;
      
      public function ButtonLogicShell()
      {
         super();
         m_buttonSoundData = new SoundData("buttonSound","buttonSound","NewGameFolder/FirstEnterSource.swf","ButtonSound");
         Part1.getInstance().getSoundManager().addSound2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_buttonSoundData);
         m_buttonSoundData = null;
         super.clear();
      }
      
      override protected function onClick(param1:MouseEvent) : void
      {
         Part1.getInstance().getSoundManager().play2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
      
      override protected function createSmallToolTip() : void
      {
         super.createSmallToolTip();
         m_smallToolTip.setFont(new FangZhengKaTongJianTi());
      }
   }
}

