package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.World.World;
   
   public class CuboidAreaAttackSkill_OnlyPlayBody2 extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      protected var m_skillAttackReachFrameLabel:String;
      
      protected var m_skillEndFrameLabel:String;
      
      public function CuboidAreaAttackSkill_OnlyPlayBody2()
      {
         super();
         m_isAttackReachWhenRelease = false;
      }
      
      override public function clear() : void
      {
         m_skillAttackReachFrameLabel = null;
         m_skillEndFrameLabel = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillAttackReachFrameLabel = String(param1.@skillAttackReachFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_skillAttackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_skillEndFrameLabel:
               endSkill1();
         }
      }
   }
}

