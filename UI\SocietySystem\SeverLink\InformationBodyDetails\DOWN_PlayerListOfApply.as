package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class DOWN_PlayerListOfApply extends InformationBodyDetail
   {
      
      protected var m_playerNumOfApply:int;
      
      protected var m_playerDatasOfApply:Vector.<PlayerDataOfApply>;
      
      public function DOWN_PlayerListOfApply()
      {
         super();
         m_informationBodyId = 3020;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_playerDatasOfApply);
         m_playerDatasOfApply = null;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:PlayerDataOfApply = null;
         super.initFromByteArray(param1);
         m_playerNumOfApply = param1.readInt();
         if(m_playerNumOfApply)
         {
            m_playerDatasOfApply = new Vector.<PlayerDataOfApply>();
            _loc2_ = m_playerNumOfApply;
            trace("申请人数量：",_loc2_);
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc3_ = new PlayerDataOfApply();
               _loc3_.initFromByteArray(param1);
               m_playerDatasOfApply.push(_loc3_);
               _loc4_++;
            }
         }
      }
      
      public function getPlayerNumOfApply() : int
      {
         return m_playerDatasOfApply ? m_playerDatasOfApply.length : 0;
      }
      
      public function getPlayerDataOfApplyByIndex(param1:int) : PlayerDataOfApply
      {
         if(m_playerDatasOfApply == null || param1 < 0 || param1 > m_playerDatasOfApply.length - 1)
         {
            throw new Error("index:" + param1 + "出错了！");
         }
         return m_playerDatasOfApply[param1];
      }
      
      public function spliceOnePlayerDataOfApply(param1:int) : void
      {
         m_playerDatasOfApply.splice(param1,1);
      }
   }
}

