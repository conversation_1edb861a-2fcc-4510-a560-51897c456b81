package UI.WorldBoss
{
   import UI.WorldBoss.AnimationQueueData.AnimationData.CriticalAndDodgeData;
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.Boss;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IFrameLabelListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class ReachAttackFrameLabelListener implements IFrameLabelListener
   {
      
      private var m_normalAttackHurtValueShowClassName:String = "NormalAttackValueAnimation";
      
      private var m_criticalAttackHurtValueShowClassName:String = "CriticalAttackValueAnimation";
      
      private var m_shanBiShowClassName:String = "ShanBiAnimation";
      
      public var fightStage:Sprite;
      
      public var mc:AnimationShowPlayLogicShell;
      
      public var m_myLoader:YJFYLoader;
      
      public var attackEntity:Entity;
      
      public var beAttackedEnttity:Entity;
      
      public var hurt:int;
      
      public var criticalAndDodgeData:CriticalAndDodgeData;
      
      public var currentBlood:int;
      
      public var totalBlood:int;
      
      public var bloodPercent:Number;
      
      public var bloodShowIndex:int;
      
      public var level:int;
      
      public function ReachAttackFrameLabelListener()
      {
         super();
      }
      
      public function clear() : void
      {
         mc = null;
         m_myLoader = null;
         beAttackedEnttity = null;
         criticalAndDodgeData = null;
         attackEntity = null;
         fightStage = null;
         m_normalAttackHurtValueShowClassName = null;
         m_criticalAttackHurtValueShowClassName = null;
         m_shanBiShowClassName = null;
      }
      
      public function reachFrameLabel(param1:String) : void
      {
         var playEndListener:MyPlayEndListener;
         var numAnimation:AnimationShowPlayLogicShell;
         var numLogicShell:MultiPlaceNumLogicShell2;
         var point:Point;
         var maxWidth:Number;
         var frameLabel:String = param1;
         if(frameLabel == "attackReach" || frameLabel == "daZhaoReach")
         {
            numAnimation = new AnimationShowPlayLogicShell();
            ValueAnimationManager.getInstance().addAnimation(numAnimation);
            numLogicShell = new MultiPlaceNumLogicShell2();
            playEndListener = new MyPlayEndListener();
            playEndListener.mc = numAnimation;
            playEndListener.numLS = numLogicShell;
            numAnimation.addNextStopListener(playEndListener);
            point = beAttackedEnttity.getPositionSprite() ? fightStage.localToGlobal(new Point(beAttackedEnttity.getPositionSprite().x,beAttackedEnttity.getPositionSprite().y)) : new Point(fightStage.stage.stageWidth / 2,fightStage.stage.stageHeight / 2);
            maxWidth = fightStage.stage.stageWidth - 20;
            if(criticalAndDodgeData.isBeDodge)
            {
               m_myLoader.getClass("WorldBoss.swf","ShanBiAnimation",function(param1:YJFYLoaderData):void
               {
                  var _loc2_:Class = param1.resultClass;
                  numAnimation.setShow(new _loc2_() as MovieClip);
                  maxWidth -= numAnimation.getShow().width;
                  numAnimation.getShow().x = Math.min(point.x,maxWidth);
                  numAnimation.getShow().y = point.y;
                  fightStage.addChild(numAnimation.getShow() as MovieClip);
                  numAnimation.gotoAndPlay("start");
               },null);
               m_myLoader.load();
            }
            else
            {
               if(criticalAndDodgeData.isCritical)
               {
                  m_myLoader.getClass("WorldBoss.swf","CriticalAttackValueAnimation",function(param1:YJFYLoaderData):void
                  {
                     var _loc2_:Class = param1.resultClass;
                     numAnimation.setShow(new _loc2_() as MovieClip);
                     numLogicShell.setShow(numAnimation.getShow()["clip"]["num"]);
                     numLogicShell.showNum(hurt);
                     maxWidth -= numAnimation.getShow().width;
                     numAnimation.getShow().x = Math.min(point.x,maxWidth);
                     numAnimation.getShow().y = point.y;
                     fightStage.addChild(numAnimation.getShow() as MovieClip);
                     numAnimation.gotoAndPlay("start");
                  },null);
                  m_myLoader.load();
               }
               else
               {
                  m_myLoader.getClass("WorldBoss.swf","NormalAttackValueAnimation",function(param1:YJFYLoaderData):void
                  {
                     var _loc2_:Class = param1.resultClass;
                     numAnimation.setShow(new _loc2_() as MovieClip);
                     numLogicShell.setShow(numAnimation.getShow()["clip"]["num"]);
                     numLogicShell.showNum(hurt);
                     maxWidth -= numAnimation.getShow().width;
                     numAnimation.getShow().x = Math.min(point.x,maxWidth);
                     numAnimation.getShow().y = point.y;
                     fightStage.addChild(numAnimation.getShow() as MovieClip);
                     numAnimation.gotoAndPlay("start");
                  },null);
                  m_myLoader.load();
               }
               if(beAttackedEnttity is PlayerEntity)
               {
                  (beAttackedEnttity as PlayerEntity).setPlayerBar(bloodPercent,currentBlood + "/" + totalBlood);
               }
               else if(beAttackedEnttity is Boss)
               {
                  (beAttackedEnttity as AbleAttackedBoss).setBossBar(bloodPercent,currentBlood.toString(),bloodShowIndex);
               }
               beAttackedEnttity.setLevelStr(level);
               beAttackedEnttity.playHurtAnimation(null);
               beAttackedEnttity.playBeAttackedSound();
               if(frameLabel == "attackReach")
               {
                  if(attackEntity)
                  {
                     attackEntity.playAttackViewChange();
                  }
               }
               else if(attackEntity is Boss && frameLabel == "daZhaoReach")
               {
                  (attackEntity as Boss).playDaZhaoViewChange();
               }
            }
            mc.removeFrameLabelListener(this);
            clear();
         }
      }
      
      public function reachFrameLabel2(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
   }
}

import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
import YJFY.ShowLogicShell.MovieClipPlayLogicShell.INextStopListener;
import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
import YJFY.Utils.ClearUtil;

class MyPlayEndListener implements INextStopListener
{
   
   public var mc:AnimationShowPlayLogicShell;
   
   public var numLS:MultiPlaceNumLogicShell2;
   
   public function MyPlayEndListener()
   {
      super();
   }
   
   public function clear() : void
   {
      mc = null;
   }
   
   public function stop() : void
   {
      if(mc && mc.getShow().parent)
      {
         mc.getShow().parent.removeChild(mc.getShow());
      }
      mc.removeNextStopListener(this);
      ClearUtil.clearObject(mc);
      mc = null;
      clear();
   }
   
   public function stop2(param1:AnimationShowPlayLogicShell) : void
   {
   }
}
