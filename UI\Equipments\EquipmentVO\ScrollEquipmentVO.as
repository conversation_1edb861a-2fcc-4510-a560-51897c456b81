package UI.Equipments.EquipmentVO
{
   import UI.GamingUI;
   import UI.XMLSingle;
   
   public class ScrollEquipmentVO extends EquipmentVO
   {
      
      public var compositeEquipmentVO:EquipmentVO;
      
      public var requiredNums:Vector.<int> = new Vector.<int>();
      
      private var _materialVOs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
      
      private var _successRate:Number = 0;
      
      private var _requiredMoney:int;
      
      public var materialXml:XML;
      
      public function ScrollEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.successRate = 0.1;
         _antiwear.requiredMoney = _requiredMoney;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         if(compositeEquipmentVO)
         {
            compositeEquipmentVO.clear();
         }
         if(requiredNums)
         {
            _loc1_ = int(requiredNums.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               requiredNums[_loc2_] = null;
               _loc2_++;
            }
            requiredNums = null;
         }
         if(materialVOs)
         {
            _loc1_ = int(materialVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               materialVOs[_loc2_] = null;
               _loc2_++;
            }
            materialVOs = null;
         }
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:ScrollEquipmentVO = new ScrollEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as ScrollEquipmentVO).successRate = this.successRate;
         (param1 as ScrollEquipmentVO).requiredMoney = this.requiredMoney;
         (param1 as ScrollEquipmentVO).compositeEquipmentVO = this.compositeEquipmentVO.clone();
         var _loc3_:Vector.<int> = new Vector.<int>();
         for each(var _loc2_ in this.requiredNums)
         {
            _loc3_.push(_loc2_);
         }
         (param1 as ScrollEquipmentVO).requiredNums = _loc3_;
         var _loc4_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         for each(var _loc5_ in this.materialVOs)
         {
            _loc4_.push(_loc5_.clone());
         }
         (param1 as ScrollEquipmentVO).materialVOs = _loc4_;
      }
      
      public function get successRate() : Number
      {
         return _antiwear.successRate;
      }
      
      public function set successRate(param1:Number) : void
      {
         _antiwear.successRate = param1;
      }
      
      public function get requiredMoney() : int
      {
         return _antiwear.requiredMoney;
      }
      
      public function set requiredMoney(param1:int) : void
      {
         _antiwear.requiredMoney = param1;
      }
      
      public function get materialVOs() : Vector.<EquipmentVO>
      {
         if(materialXml)
         {
            _materialVOs = XMLSingle.setScrollRequiredEquipmentVOs(materialXml,XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
            materialXml = null;
         }
         return _materialVOs;
      }
      
      public function set materialVOs(param1:Vector.<EquipmentVO>) : void
      {
         _materialVOs = param1;
      }
   }
}

