package YJFY
{
   import UI.LogicShell.ChangeBarLogicShell.HaveSpotCMSXLogicShell;
   import UI.MySprite;
   import UI.VersionControl;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class LoadUI2 extends UI.MySprite implements IProgressShow
   {
      
      private var m_show:Sprite;
      
      private var m_loadRateText:TextField;
      
      private var m_smallNumText:TextField;
      
      private var m_bigNumText:TextField;
      
      private var m_progressBar:HaveSpotCMSXLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:VersionControl;
      
      private var m_isTransparentcy:Boolean;
      
      public function LoadUI2(param1:VersionControl, param2:YJFYLoader)
      {
         super();
         m_versionControl = param1;
         m_myLoader = param2;
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(m_show);
         m_show = null;
         m_loadRateText = null;
         m_smallNumText = null;
         m_bigNumText = null;
         ClearUtil.clearObject(m_progressBar);
         m_progressBar = null;
         m_myLoader = null;
         m_versionControl = null;
      }
      
      public function tranToTransparentcy() : void
      {
         if(m_isTransparentcy)
         {
            return;
         }
         m_myLoader.getClass("NewGameFolder/StartGameEnterSource.swf","PartLoadShowSpriteTransparent2",getShowSuccess,getShowFail,null,null,null,null,false);
         m_myLoader.load();
      }
      
      public function tranToOpacity() : void
      {
         if(m_isTransparentcy == false)
         {
            return;
         }
         m_myLoader.getClass("NewGameFolder/FirstEnterSource.swf","PartLoadShowSprite2",getShowSuccess,getShowFail,null,null,null,null,false);
         m_myLoader.load();
      }
      
      private function init() : void
      {
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         m_isTransparentcy = true;
         tranToOpacity();
      }
      
      private function init2() : void
      {
         ClearUtil.clearObject(m_progressBar);
         m_smallNumText = m_show["smallNum"];
         m_bigNumText = m_show["bigNum"];
         m_progressBar = new HaveSpotCMSXLogicShell();
         m_progressBar.setShow(m_show["progess"]);
      }
      
      private function initTranToTransparentcy() : void
      {
         ClearUtil.clearObject(m_progressBar);
         m_smallNumText = m_show["smallNum"];
         m_bigNumText = m_show["bigNum"];
         m_progressBar = new HaveSpotCMSXLogicShell();
         m_progressBar.setShow(m_show["progess"]);
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         switch(param1.wantClassName)
         {
            case "PartLoadShowSprite2":
               ClearUtil.clearObject(m_show);
               m_show = new param1.resultClass();
               m_show.name = "loadUIMC";
               init2();
               m_isTransparentcy = false;
               break;
            case "PartLoadShowSpriteTransparent2":
               ClearUtil.clearObject(m_show);
               m_show = new param1.resultClass();
               m_show.name = "loadUIMC";
               if(getChildByName("loadUIMC") == null)
               {
                  m_show.name = "loadUIMC";
                  addChild(m_show);
               }
               initTranToTransparentcy();
               m_isTransparentcy = true;
         }
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      public function setProgress(param1:Number, param2:Number, param3:Number, param4:Number, param5:int, param6:int) : void
      {
         if(m_show)
         {
            m_progressBar.change(param1);
            if(getChildByName("loadUIMC") == null && param1 < 1)
            {
               m_show.name = "loadUIMC";
               addChild(m_show);
            }
            m_smallNumText.text = param5.toString();
            m_bigNumText.text = param6.toString();
         }
      }
      
      public function startLoad() : void
      {
         if(m_progressBar)
         {
            m_progressBar.change(0);
         }
         if(getChildByName("loadUIMC") == null && m_show)
         {
            m_show.name = "loadUIMC";
            addChild(m_show);
         }
      }
      
      public function completeLoad() : void
      {
         if(m_progressBar)
         {
            m_progressBar.change(0);
         }
         if(getChildByName("loadUIMC") != null)
         {
            removeChild(getChildByName("loadUIMC"));
         }
      }
      
      public function error() : void
      {
         trace("error.......");
      }
   }
}

