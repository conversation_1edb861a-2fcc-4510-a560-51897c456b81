package UI.DirtyWordFilter
{
   import UI.SocietySystem.SocialChatPanel.NewGotChatInfor;
   import YJFY.API_4399.Open4399ToolsAPI.ErrorData;
   import YJFY.API_4399.Open4399ToolsAPI.Open4399ToolsAPIListener;
   import YJFY.API_4399.Open4399ToolsAPI.SuccessData;
   import YJFY.Part1;
   
   public class DirtyWordFilterEngine
   {
      
      private var _dirtyWordData:DirtyWordData;
      
      private var m_open4399ToolsAPIListener:Open4399ToolsAPIListener;
      
      private var m_sendStr:String;
      
      private var _checkBadReturn:Function;
      
      public function DirtyWordFilterEngine(param1:Function, param2:Array)
      {
         super();
         init(param1,param2);
      }
      
      public function clear() : void
      {
         if(_dirtyWordData)
         {
            _dirtyWordData.clear();
         }
         _dirtyWordData = null;
      }
      
      public function filterWords(param1:String, param2:Function) : void
      {
         m_sendStr = param1;
         _checkBadReturn = param2;
         Part1.getInstance().getApi4399().open4399ToolsAPI.addOpen4399ToolsAPIListener(m_open4399ToolsAPIListener);
         Part1.getInstance().getApi4399().open4399ToolsAPI.checkBadWords(m_sendStr);
      }
      
      private function checkBadWordsError(param1:ErrorData) : void
      {
         trace("check bad words Error");
      }
      
      private function checkBadWordsSuccess(param1:SuccessData) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         if(param1.getIsHaveBadWord())
         {
            _loc2_ = int(param1.getBadWordsNum());
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               m_sendStr = m_sendStr.replace(param1.getBadWordsByIndex(_loc4_),"***");
               _loc4_++;
            }
         }
         var _loc3_:NewGotChatInfor = new NewGotChatInfor();
         Part1.getInstance().getApi4399().open4399ToolsAPI.removeOpen4399ToolsAPIListener(m_open4399ToolsAPIListener);
         _checkBadReturn(m_sendStr);
      }
      
      private function matchStrInWords(param1:String, param2:Array) : Boolean
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(param2.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1 == param2[_loc4_])
            {
               return true;
            }
            _loc4_++;
         }
         return false;
      }
      
      private function getDirtyWordMaxLength(param1:Array) : int
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:* = int(param1[0].length);
         var _loc4_:int = int(param1.length);
         _loc5_ = 1;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = int(param1[_loc5_].length);
            _loc3_ = _loc3_ > _loc2_ ? _loc3_ : _loc2_;
            _loc5_++;
         }
         return _loc3_;
      }
      
      private function init(param1:Function, param2:Array) : void
      {
         _dirtyWordData = new DirtyWordData();
         m_open4399ToolsAPIListener = new Open4399ToolsAPIListener();
         m_open4399ToolsAPIListener.checkBadWordsErrorFun = checkBadWordsError;
         m_open4399ToolsAPIListener.checkBadWordsSuccessFun = checkBadWordsSuccess;
         m_open4399ToolsAPIListener.serviceInitCompleteFun = null;
      }
   }
}

