package YJFY.Skill.RabbitSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OnlyPlayBody;
   import YJFY.World.World;
   
   public class Skill_RabbitSkill3 extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      public function Skill_RabbitSkill3()
      {
         super();
      }
      
      override public function startSkill(param1:World) : <PERSON><PERSON><PERSON>
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

