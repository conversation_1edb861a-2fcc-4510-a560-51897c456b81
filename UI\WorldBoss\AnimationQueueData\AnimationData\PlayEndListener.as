package UI.WorldBoss.AnimationQueueData.AnimationData
{
   public class PlayEndListener implements IPlayEndListener
   {
      
      public var animationData:AnimationData;
      
      public var playEndFun:Function;
      
      public function PlayEndListener()
      {
         super();
      }
      
      public function playEnd() : void
      {
         if(<PERSON><PERSON><PERSON>(playEndFun))
         {
            playEndFun();
         }
         animationData.removePlayEndListener(this);
      }
      
      public function clear() : void
      {
         animationData = null;
         playEndFun = null;
      }
   }
}

