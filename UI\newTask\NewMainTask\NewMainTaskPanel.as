package UI.newTask.NewMainTask
{
   import UI.MainLineTask.MainLineTaskVO;
   import UI.newTask.NewTaskPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class NewMainTaskPanel
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mainlist:NewMainList;
      
      private var m_maindetail:NewMainDetail;
      
      private var m_mainreward:NewMainReward;
      
      private var m_newmainbtnshell:NewMainBtnShell;
      
      private var m_mc:MovieClip;
      
      public function NewMainTaskPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_mainlist);
         m_mainlist = null;
         ClearUtil.clearObject(m_maindetail);
         m_maindetail = null;
         ClearUtil.clearObject(m_mainreward);
         m_mainreward = null;
         ClearUtil.clearObject(m_newmainbtnshell);
         m_newmainbtnshell = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip) : void
      {
         m_newtaskpanel = param1;
         m_show = param2;
         m_mc = m_show["tasklistmc"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_newmainbtnshell = new NewMainBtnShell();
         m_newmainbtnshell.init(m_newtaskpanel,m_show,this);
         m_mainreward = new NewMainReward();
         m_mainreward.init(m_newtaskpanel,m_show,this);
         m_maindetail = new NewMainDetail();
         m_maindetail.init(m_newtaskpanel,m_show,this);
         m_mainlist = new NewMainList();
         m_mainlist.init(m_newtaskpanel,m_show,this);
      }
      
      public function refreshScript(param1:NewMainListItem) : void
      {
         m_maindetail.refreshScript(param1);
         m_newmainbtnshell.refreshScript(param1.getData());
      }
      
      public function showReward(param1:MainLineTaskVO) : void
      {
         m_mainreward.showReward(param1);
      }
      
      public function getMainList() : NewMainList
      {
         return m_mainlist;
      }
      
      public function show(param1:String) : void
      {
         m_mainlist.show(param1);
         m_maindetail.show();
         m_mainreward.show();
         m_newmainbtnshell.show();
      }
      
      public function hide() : void
      {
         m_mainlist.hide();
         m_maindetail.hide();
         m_mainreward.hide();
         m_newmainbtnshell.hide();
      }
      
      private function registerEvent() : void
      {
      }
   }
}

