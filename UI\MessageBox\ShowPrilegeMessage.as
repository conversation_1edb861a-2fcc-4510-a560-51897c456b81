package UI.MessageBox
{
   import UI.Privilege.Privilege;
   
   public class ShowPrilegeMessage
   {
      
      public function ShowPrilegeMessage()
      {
         super();
      }
      
      public function showMessage(param1:Privilege, param2:MessageBox) : void
      {
         if(param1)
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText((param1 as Privilege).privilegeVO.description,15);
         }
      }
   }
}

