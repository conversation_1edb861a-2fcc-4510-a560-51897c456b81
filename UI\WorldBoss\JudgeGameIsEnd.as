package UI.WorldBoss
{
   import YJFY.StepAttackGame.IJudgeStepAttackGameIsEnd;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   
   public class JudgeGameIsEnd implements IJudgeStepAttackGameIsEnd
   {
      
      private var m_world:StepAttackGameWorld;
      
      public function JudgeGameIsEnd()
      {
         super();
      }
      
      public function clear() : void
      {
         m_world = null;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_world = param1;
      }
      
      public function judeIsEnd() : Boolean
      {
         var _loc1_:Boolean = false;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:PlayerEntity = null;
         _loc3_ = m_world.getFriendNum();
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = m_world.getFriendByIndex(_loc4_) as PlayerEntity;
            if(_loc2_)
            {
               _loc1_ = true;
            }
            _loc4_++;
         }
         if(_loc1_)
         {
            return false;
         }
         return true;
      }
   }
}

