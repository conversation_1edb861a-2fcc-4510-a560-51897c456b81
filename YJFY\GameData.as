package YJFY
{
   import UI.LastSaveData;
   import YJFY.API_4399.LogAPI.LoginReturnData;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   
   public class GameData
   {
      
      public static const m_s_game_id:int = 100021366;
      
      public static const PKLIST_ID:int = 139;
      
      public static const PKMONTHLIST_ID1:int = 1576;
      
      public static const PKMONTHLIST_ID2:int = 1583;
      
      public static const FREE_PKLIST_ID:int = 1580;
      
      public static const MINGREN_PKLIST_ID:int = 1575;
      
      public static var MonthCicly:Boolean = false;
      
      private static var m_instance:GameData;
      
      private var m_loginReturnData:LoginReturnData;
      
      private var m_saveFileData:SaveFileData;
      
      private var m_lastSaveTime:Number;
      
      private var m_lastSaveData:LastSaveData;
      
      public var isNewGame:Boolean;
      
      public function GameData()
      {
         super();
         if(m_instance == null)
         {
            m_instance = this;
            m_lastSaveTime = 0;
            return;
         }
         throw new Error("实例已经存在");
      }
      
      public static function getInstance() : GameData
      {
         if(m_instance == null)
         {
            m_instance = new GameData();
         }
         return m_instance;
      }
      
      public function clear() : void
      {
         m_loginReturnData = null;
         m_saveFileData = null;
         m_lastSaveData = null;
         m_instance = null;
      }
      
      public function setLoginReturnData(param1:LoginReturnData) : void
      {
         m_loginReturnData = param1;
      }
      
      public function setSaveFileData(param1:SaveFileData) : void
      {
         m_saveFileData = param1;
      }
      
      public function setLastSaveData(param1:LastSaveData) : void
      {
         m_lastSaveData = param1;
      }
      
      public function getLoginReturnData() : LoginReturnData
      {
         if(m_loginReturnData == null && Part1.getInstance().getApi4399().logAPI.getLogInfo(null))
         {
            m_loginReturnData = new LoginReturnData(null,null,null);
            Part1.getInstance().getApi4399().logAPI.getLogInfo(m_loginReturnData);
         }
         return m_loginReturnData;
      }
      
      public function getSaveFileData() : SaveFileData
      {
         return m_saveFileData;
      }
      
      public function getLastSaveData() : LastSaveData
      {
         return m_lastSaveData;
      }
      
      public function getLastSaveTime() : Number
      {
         return m_lastSaveTime;
      }
      
      public function setLastSaveTime(param1:Number) : void
      {
         m_lastSaveTime = param1;
      }
   }
}

