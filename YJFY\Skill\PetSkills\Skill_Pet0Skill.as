package YJFY.Skill.PetSkills
{
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_EveryTargetAddShow;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_Pet0Skill extends CuboidAreaAttackSkill2_EveryTargetAddShow
   {
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      public function Skill_Pet0Skill()
      {
         super();
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function endSkill1() : void
      {
         super.endSkill1();
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_bodyAttackReachFrameLabel === _loc2_)
         {
            m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints);
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

