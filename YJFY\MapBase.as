package YJFY
{
   import UI.EnterFrameTime;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.EntityListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationDefinitionManager;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.EntityAnimation.IAnimationDefintion;
   import YJFY.KeyManager.KeyManager;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ISkill;
   import YJFY.SoundManager.SoundManager2;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.World.ProjectLayer.ProjectLayer;
   import YJFY.World.ProjectLayer.ProjectLayerData;
   import YJFY.World.SoundData;
   import YJFY.World.World;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.media.SoundTransform;
   import flash.system.System;
   
   public class MapBase extends MySprite
   {
      
      protected var m_id:String;
      
      protected var m_worldLayer:MySprite;
      
      protected var m_uiLayer:MySprite;
      
      protected var m_mapXML:XML;
      
      protected var m_levelMC:MovieClip;
      
      protected var m_mainProjectLayer:ProjectLayer;
      
      protected var m_otherProjectLayers:Vector.<ProjectLayer>;
      
      protected var m_world:World;
      
      public var m_myLoader:YJFYLoader;
      
      protected var m_loadUI:LoadUI2;
      
      protected var m_animationDefinitionManager:AnimationDefinitionManager;
      
      protected var m_keyManager:KeyManager;
      
      protected var m_soundManager:SoundManager2;
      
      protected var m_entityListener:EntityListener;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      protected var m_backGroundMusicSoundData:SoundData;
      
      protected var m_dancerMusicSoundData:SoundData;
      
      protected var m_loaderIsEx:Boolean;
      
      protected var m_enterFrameTime:EnterFrameTime;
      
      protected var m_versionControl:IVersionControl;
      
      protected var m_curMapXMLPath:String;
      
      public function MapBase()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         m_otherProjectLayers = new Vector.<ProjectLayer>();
         m_animationDefinitionManager = new AnimationDefinitionManager();
         m_entityListener = new EntityListener();
         m_entityListener.addToWorldFun = addToWorld;
         m_entityListener.removeFromWorldFun = removeFromWorld;
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.attackEndFun = attackEnd;
         m_animalEntityListener.attackReachFun = attackReach;
         m_animalEntityListener.attackSuccessFun = attackSuccess;
         m_animalEntityListener.beAttackFun = beAttack;
         m_animalEntityListener.changeStateFun = changeState;
      }
      
      override public function clear() : void
      {
         if(m_backGroundMusicSoundData != null && m_soundManager)
         {
            m_soundManager.stop(m_backGroundMusicSoundData.getName());
         }
         if(m_dancerMusicSoundData != null && m_soundManager)
         {
            m_soundManager.stop(m_dancerMusicSoundData.getName());
         }
         System.disposeXML(m_mapXML);
         m_mapXML = null;
         ClearUtil.clearObject(m_mainProjectLayer);
         m_mainProjectLayer = null;
         ClearUtil.clearObject(m_otherProjectLayers);
         m_otherProjectLayers = null;
         ClearUtil.clearObject(m_world);
         m_world = null;
         ClearUtil.clearObject(m_worldLayer);
         m_worldLayer = null;
         ClearUtil.clearObject(m_uiLayer);
         m_uiLayer = null;
         if(m_loaderIsEx == false)
         {
            ClearUtil.clearObject(m_myLoader);
         }
         m_myLoader = null;
         m_loadUI = null;
         ClearUtil.clearObject(m_animationDefinitionManager);
         m_animationDefinitionManager = null;
         ClearUtil.clearObject(m_keyManager);
         m_keyManager = null;
         ClearUtil.clearObject(m_soundManager);
         m_soundManager = null;
         ClearUtil.clearObject(m_entityListener);
         m_entityListener = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         ClearUtil.clearObject(m_backGroundMusicSoundData);
         m_backGroundMusicSoundData = null;
         ClearUtil.clearObject(m_dancerMusicSoundData);
         m_dancerMusicSoundData = null;
         ClearUtil.clearObject(m_levelMC);
         m_levelMC = null;
         m_enterFrameTime = null;
         m_versionControl = null;
         super.clear();
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setLoadUI(param1:LoadUI2) : void
      {
         m_loadUI = param1;
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
         if(param1)
         {
            m_loaderIsEx = true;
         }
      }
      
      public function getMyLoader() : YJFYLoader
      {
         return m_myLoader;
      }
      
      public function init() : void
      {
         m_keyManager = new KeyManager();
         m_keyManager.addKeyEventListener(stage);
         if(m_myLoader == null)
         {
            m_myLoader = new YJFYLoader();
            m_myLoader.setVersionControl(m_versionControl);
            m_myLoader.setProgressShow(m_loadUI);
         }
         m_world = new World();
         m_worldLayer = new MySprite();
         m_worldLayer.mouseChildren = true;
         m_worldLayer.mouseEnabled = true;
         m_uiLayer = new MySprite();
         m_uiLayer.mouseChildren = true;
         m_uiLayer.mouseEnabled = true;
         addChild(m_worldLayer);
         addChild(m_uiLayer);
         m_soundManager = new SoundManager2();
         m_soundManager.setMyLoader(m_myLoader);
         m_animationDefinitionManager.setLoader(m_myLoader);
         m_animationDefinitionManager.addSubAnimationDefinitionManager(Part1.getInstance().getXydzjSelfAnimationManager());
         m_worldLayer.addChild(m_world.getCamera().getView());
         m_world.setAnimationDefinitionManager(m_animationDefinitionManager);
         m_world.setSoundManger(m_soundManager);
         m_world.setMyLoader(m_myLoader);
         m_world.setEnterFrameTime(m_enterFrameTime);
      }
      
      public function initByXMLPath(param1:String) : void
      {
         m_curMapXMLPath = param1;
         m_myLoader.getXML(param1,getMapXMLSuccess,getFail);
         m_myLoader.load();
      }
      
      public function initByXML(param1:XML) : void
      {
         m_mapXML = param1;
         m_id = String(m_mapXML.@id);
         initBackgroundMusic();
         initSharedAnimations();
         init2();
      }
      
      public function addUIShow(param1:Sprite) : void
      {
         m_uiLayer.addChild(param1);
      }
      
      public function removeUIShow(param1:Sprite) : void
      {
         if(m_uiLayer.contains(param1))
         {
            m_uiLayer.removeChild(param1);
         }
      }
      
      protected function init2() : void
      {
         m_world.initWoldArea(m_mapXML.@x,m_mapXML.@y,m_mapXML.@z,m_mapXML.@xRange,m_mapXML.@yRange,m_mapXML.@zRange);
         m_myLoader.getClass(m_mapXML.@swfPath,m_mapXML.@className,getMapSuccess,getFail);
         m_myLoader.load();
      }
      
      protected function initSharedAnimations() : void
      {
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:IAnimationDefintion = null;
         var _loc1_:* = null;
         var _loc5_:XMLList = m_mapXML.sharedAnimationDefinitions[0].animationDefinition;
         _loc3_ = int(_loc5_ ? _loc5_.length() : 0);
         var _loc4_:AnimationDefinitionData = new AnimationDefinitionData();
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc4_.initByXML(_loc5_[_loc6_]);
            _loc2_ = m_animationDefinitionManager.getOrAddDefinitionById(_loc4_);
            _loc6_++;
         }
         ClearUtil.clearObject(_loc4_);
      }
      
      protected function initBackgroundMusic() : void
      {
         if(m_mapXML.hasOwnProperty("backgroundMusic") == false)
         {
            return;
         }
         m_backGroundMusicSoundData = new SoundData(m_mapXML.backgroundMusic[0].@id,m_mapXML.backgroundMusic[0].@name,m_mapXML.backgroundMusic[0].@swfPath,m_mapXML.backgroundMusic[0].@className);
         m_soundManager.addSound2(m_backGroundMusicSoundData.getName(),m_backGroundMusicSoundData.getSwfPath(),m_backGroundMusicSoundData.getClassName());
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         m_world.render(param1);
         m_animationDefinitionManager.render();
      }
      
      public function showRender() : void
      {
         m_world.showRender();
      }
      
      protected function getMapXMLSuccess(param1:YJFYLoaderData) : void
      {
         initByXML(param1.resultXML);
      }
      
      protected function initProjectLayer(param1:MovieClip) : void
      {
         var _loc7_:int = 0;
         var _loc3_:ProjectLayer = null;
         var _loc4_:MovieClip = param1[String(m_mapXML.mainGameProjectLayerData[0].@name)];
         _loc4_.parent.removeChild(_loc4_);
         var _loc2_:ProjectLayerData = new ProjectLayerData();
         _loc2_.init(m_mapXML.mainGameProjectLayerData[0].@sx,m_mapXML.mainGameProjectLayerData[0].@sy,m_mapXML.mainGameProjectLayerData[0].@sz,_loc4_,m_world.getTransformCoordinate());
         m_mainProjectLayer = new ProjectLayer();
         m_mainProjectLayer.init(_loc2_);
         var _loc6_:XMLList = m_mapXML.projectLayer;
         var _loc5_:int = int(_loc6_ ? _loc6_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc3_ = new ProjectLayer();
            _loc4_ = param1[String(_loc6_[_loc7_].@name)];
            _loc4_.parent.removeChild(_loc4_);
            _loc2_.init(_loc6_[_loc7_].@sx,_loc6_[_loc7_].@sy,_loc6_[_loc7_].@sz,_loc4_,m_world.getTransformCoordinate());
            _loc3_.init(_loc2_);
            _loc4_.mouseChildren = false;
            _loc4_.mouseEnabled = false;
            m_otherProjectLayers.push(_loc3_);
            _loc7_++;
         }
         m_world.initProjectLayers(m_mainProjectLayer,m_otherProjectLayers);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
      }
      
      protected function getMapSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:MovieClip = new param1.resultClass();
         m_levelMC = _loc2_;
         initProjectLayer(_loc2_);
         mapInitSuccess();
      }
      
      protected function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error(param1.swfPath + param1.xmlPath);
      }
      
      protected function mapInitSuccess() : void
      {
         var _loc1_:SoundTransform = null;
         if(m_backGroundMusicSoundData)
         {
            _loc1_ = new SoundTransform(1);
            m_soundManager.play2(m_backGroundMusicSoundData.getName(),m_backGroundMusicSoundData.getSwfPath(),m_backGroundMusicSoundData.getClassName(),0,-1,_loc1_);
         }
      }
      
      public function getUiLayer() : Sprite
      {
         return m_uiLayer;
      }
      
      protected function attackReach(param1:AnimalEntity) : void
      {
      }
      
      protected function attackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
      }
      
      protected function attackEnd(param1:IAnimalEntity) : void
      {
      }
      
      protected function beAttack(param1:IEntity, param2:AttackData, param3:ISkill, param4:IEntity) : void
      {
      }
      
      protected function changeState(param1:AnimalEntity) : void
      {
      }
      
      public function getAnimalEntityListener() : AnimalEntityListener
      {
         return m_animalEntityListener;
      }
      
      public function getEntityListener() : EntityListener
      {
         return this.m_entityListener;
      }
      
      protected function addToWorld(param1:IEntity, param2:World) : void
      {
         Part1.getInstance().dispatchEvent(new Event("openSvActivityEvent"));
      }
      
      protected function removeFromWorld(param1:IEntity, param2:World) : void
      {
      }
   }
}

