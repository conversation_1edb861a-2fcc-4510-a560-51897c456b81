package UI.EquipmentMakeAndUpgrade
{
   import UI.DataManagerParent;
   import UI.GamingUI;
   import UI.Players.PlayerVO;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class EqMagicVO extends DataManagerParent
   {
      
      private var m_const_normalHatchNeedTime:uint = 10800000;
      
      private var m_startHatchTime:String;
      
      private var m_buyTime:uint;
      
      private var m_useBlessTime:String;
      
      private var m_remainTimeToCompleteEnergy:uint;
      
      private var m_onLineTimeForCalRemainTime:Number;
      
      public var playerVO:PlayerVO;
      
      public function EqMagicVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.startHatchTime = m_startHatchTime;
         _antiwear.buyTime = m_buyTime;
         _antiwear.useBlessTime = m_useBlessTime;
      }
      
      public function initFromSaveXML(param1:XML, param2:String) : void
      {
         startHatchTime = String(param1.@sT);
         buyTime = uint(param1.@bT);
         useBlessTime = String(param1.@uT);
         calculateRemainTime(param2);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <EqMagicT />;
         if(startHatchTime)
         {
            _loc1_.@sT = startHatchTime;
            if(this.buyTime)
            {
               _loc1_.@bT = this.buyTime;
            }
            if(this.useBlessTime)
            {
               _loc1_.@uT = this.useBlessTime;
            }
         }
         return _loc1_;
      }
      
      public function setUseBlessTime(param1:String) : void
      {
         this.useBlessTime = param1;
      }
      
      public function getUseBlessTime() : String
      {
         return this.useBlessTime;
      }
      
      public function setDataForStartHatch(param1:String) : void
      {
         startHatchTime = param1;
         this.buyTime = 0;
         calculateRemainTime(param1);
      }
      
      public function buyHatchTime(param1:uint, param2:String) : void
      {
         this.buyTime += param1;
         calculateRemainTime(param2);
      }
      
      public function getRemainTimeForCompleteEnergy() : uint
      {
         return m_remainTimeToCompleteEnergy;
      }
      
      public function getOnLineTimeForCalRemainTime() : Number
      {
         return m_onLineTimeForCalRemainTime;
      }
      
      private function calculateRemainTime(param1:String) : void
      {
         var _loc2_:* = 0;
         if(startHatchTime)
         {
            _loc2_ = new TimeUtil().timeInterval(startHatchTime,param1) * 3600000;
            m_remainTimeToCompleteEnergy = Math.max(0,m_const_normalHatchNeedTime - buyTime - _loc2_);
         }
         else
         {
            m_remainTimeToCompleteEnergy = 0;
         }
         try
         {
            m_onLineTimeForCalRemainTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
         }
         catch(error:Error)
         {
            m_onLineTimeForCalRemainTime = 0;
         }
      }
      
      private function set startHatchTime(param1:String) : void
      {
         _antiwear.startHatchTime = param1;
      }
      
      private function get startHatchTime() : String
      {
         return _antiwear.startHatchTime;
      }
      
      private function set buyTime(param1:uint) : void
      {
         _antiwear.buyTime = param1;
      }
      
      private function get buyTime() : uint
      {
         return _antiwear.buyTime;
      }
      
      private function set useBlessTime(param1:String) : void
      {
         _antiwear.useBlessTime = param1;
      }
      
      private function get useBlessTime() : String
      {
         return _antiwear.useBlessTime;
      }
   }
}

