package UI.MainLineTask.TaskDescription
{
   import GM_UI.GMData;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Utils.LoadXML.LoadXMLListener1;
   import UI.Utils.LoadXML.MyLoadXML;
   import YJFY.LevelMode1.LevelPreInforShow.LevelDropData;
   import YJFY.Utils.ClearUtil;
   
   public class TaskDescription1 extends TaskDescriptionVO_MainLineTask
   {
      
      public var enemyName:String;
      
      public var mapName:String;
      
      public var levelXMLPath:String;
      
      public var levelNum:int;
      
      public var dropOutEquipmentVOs_enemy:Vector.<EquipmentVO>;
      
      public var dropOutEquipmentVOs_boss:Vector.<EquipmentVO>;
      
      private var m_levelDropData:LevelDropData;
      
      public var loadXML:MyLoadXML;
      
      private var m_loadXMLListener:LoadXMLListener1;
      
      private var m_levelModeXML:XML;
      
      public function TaskDescription1()
      {
         super();
         m_levelDropData = new LevelDropData();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(dropOutEquipmentVOs_boss);
         dropOutEquipmentVOs_boss = null;
         ClearUtil.nullArr(dropOutEquipmentVOs_enemy);
         dropOutEquipmentVOs_enemy = null;
         ClearUtil.clearObject(loadXML);
         loadXML = null;
         ClearUtil.clearObject(m_loadXMLListener);
         m_loadXMLListener = null;
         m_levelModeXML = null;
         ClearUtil.clearObject(m_levelDropData);
         m_levelDropData = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         var xml:XML = param1;
         super.initByXML(xml);
         enemyName = String(xml.enemyName[0].@value);
         mapName = String(xml.mapName[0].@value);
         levelXMLPath = String(xml.dropOut[0].@levelXMLPath);
         levelNum = int(xml.dropOut[0].@levelNum);
         if(m_levelModeXML == null && GMData.getInstance().isGMApplication == false)
         {
            if(loadXML == null)
            {
               loadXML = new MyLoadXML();
            }
            loadXML.setXMLPath(levelXMLPath);
            if(m_loadXMLListener == null)
            {
               m_loadXMLListener = new LoadXMLListener1();
            }
            m_loadXMLListener.loadSuccessFun = function(param1:XML):void
            {
               m_levelModeXML = param1;
               initDropOutEquipmentVOs(m_levelModeXML);
            };
            loadXML.addLoadXMLListener(m_loadXMLListener);
            loadXML.load(true,true);
         }
      }
      
      private function initDropOutEquipmentVOs(param1:XML) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(dropOutEquipmentVOs_boss != null && dropOutEquipmentVOs_enemy != null)
         {
            return;
         }
         m_levelDropData.init(param1);
         dropOutEquipmentVOs_enemy = new Vector.<EquipmentVO>();
         dropOutEquipmentVOs_boss = new Vector.<EquipmentVO>();
         _loc2_ = int(m_levelDropData.getEnemyDropEquipmentVONum());
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            dropOutEquipmentVOs_enemy.push(m_levelDropData.getEnemyDropEquipmentVOByIndex(_loc3_).clone());
            _loc3_++;
         }
         _loc2_ = int(m_levelDropData.getBossDropEquipmentVONum());
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            dropOutEquipmentVOs_boss.push(m_levelDropData.getBossDropEquipmentVOByIndex(_loc3_).clone());
            _loc3_++;
         }
      }
   }
}

