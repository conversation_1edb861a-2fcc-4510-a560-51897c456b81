package YJFY.XydzjsData
{
   import UI.Players.Player;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.World.World;
   
   public class PlayerFightAttriBufferXydzjs
   {
      
      private const m_const_updateInterval:uint = 30000;
      
      private var m_attack:Number;
      
      private var m_petLowXueNu:Number;
      
      private var m_hppercent:Number;
      
      private var m_PetPressSiveXueNu:Number;
      
      private var m_defence:Number;
      
      private var m_dogdeRate:Number;
      
      private var m_criticalRate:Number;
      
      private var m_criticalMulti:Number;
      
      private var m_deCriticalRate:Number;
      
      private var m_hitRate:Number;
      
      private var m_PetPassiveSkillDecHurt:Number;
      
      private var m_lastUpdateTime:uint;
      
      private var m_uiPlayer:Player;
      
      public function PlayerFightAttriBufferXydzjs()
      {
         super();
         m_attack = 0;
         m_defence = 0;
         m_dogdeRate = 0;
         m_criticalRate = 0;
         m_criticalMulti = 0;
         m_deCriticalRate = 0;
         m_hitRate = 0;
         m_petLowXueNu = 0;
         m_hppercent = 0;
         m_PetPressSiveXueNu = 0;
         m_lastUpdateTime = 0;
         m_PetPassiveSkillDecHurt = 0;
      }
      
      public function clear() : void
      {
         m_uiPlayer = null;
      }
      
      public function setPlayer(param1:Player) : void
      {
         m_uiPlayer = param1;
         m_lastUpdateTime = 0;
         if(m_uiPlayer)
         {
            upDateData();
         }
      }
      
      public function render(param1:World) : void
      {
         if(param1 && m_uiPlayer)
         {
            if(param1.getWorldExistTime() - m_lastUpdateTime > 30000)
            {
               m_lastUpdateTime = param1.getWorldExistTime();
               upDateData();
            }
         }
      }
      
      private function upDateData() : void
      {
         m_attack = m_uiPlayer.playerVO.attack;
         m_petLowXueNu = m_uiPlayer.playerVO.petLowXueNu;
         m_hppercent = m_uiPlayer.playerVO.bloodPercent;
         m_PetPressSiveXueNu = m_uiPlayer.playerVO.PetPressSiveXueNu;
         m_defence = m_uiPlayer.playerVO.defence;
         m_dogdeRate = m_uiPlayer.playerVO.dodge;
         var _loc2_:Number = 100 - int(m_hppercent * 100) < m_uiPlayer.playerVO.PetPressSiveKuangBao ? 100 - int(m_hppercent * 100) : m_uiPlayer.playerVO.PetPressSiveKuangBao;
         m_criticalRate = m_uiPlayer.playerVO.criticalRate / ProgramStartData.getInstance().getPercent_oneHundred();
         m_criticalMulti = ProgramStartData.getInstance().getNormalCriticalMulti();
         m_deCriticalRate = m_uiPlayer.playerVO.riotValue;
         m_hitRate = m_uiPlayer.playerVO.hit;
         if(m_uiPlayer.playerVO.isHavePet())
         {
            for each(var _loc1_ in m_uiPlayer.playerVO.pet.petEquipmentVO.passiveSkillVOs)
            {
               if(_loc1_)
               {
                  if(_loc1_.className == "PetSkill_ZhiShang")
                  {
                     m_PetPassiveSkillDecHurt = (_loc1_ as PetPassiveSkillVO).value;
                     break;
                  }
               }
            }
         }
      }
      
      public function getAttack() : Number
      {
         return m_attack;
      }
      
      public function getPetLowXue() : Number
      {
         return m_petLowXueNu;
      }
      
      public function getHpPercent() : Number
      {
         return m_hppercent;
      }
      
      public function getXueNu() : Number
      {
         return m_PetPressSiveXueNu;
      }
      
      public function getDefence() : Number
      {
         return m_defence;
      }
      
      public function getPetPassiveSkillDecHurt() : Number
      {
         return m_PetPassiveSkillDecHurt;
      }
      
      public function getDogdeRate() : Number
      {
         return m_dogdeRate;
      }
      
      public function getCriticalRate() : Number
      {
         return m_criticalRate;
      }
      
      public function getCriticalMulti() : Number
      {
         return m_criticalMulti;
      }
      
      public function getDeCriticalRate() : Number
      {
         return m_deCriticalRate;
      }
      
      public function getHitRate() : Number
      {
         return m_hitRate;
      }
   }
}

