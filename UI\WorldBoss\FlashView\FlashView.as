package UI.WorldBoss.FlashView
{
   import UI.WorldBoss.View;
   
   public class FlashView
   {
      
      protected var _isFlash:Boolean;
      
      protected var _isMyFlash:Boolean;
      
      protected var _view:View;
      
      public function FlashView()
      {
         super();
      }
      
      public function clear() : void
      {
         recover();
         _view = null;
      }
      
      public function flashView(param1:View) : void
      {
         if(_isMyFlash)
         {
            return;
         }
         _isFlash = param1.getIsFlash();
         _view = param1;
      }
      
      protected function recover() : void
      {
         if(_isMyFlash)
         {
            _view.setIsFlash(false);
         }
         _isMyFlash = false;
      }
      
      public function getIsFlash() : Boolean
      {
         return _isFlash;
      }
      
      public function getIsMyFlash() : Boolean
      {
         return _isMyFlash;
      }
   }
}

