package YJFY.World.Camera
{
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Camera.View.View;
   import YJFY.World.Coordinate;
   import YJFY.World.TransformCoordinate;
   import YJFY.World.World;
   
   public class Camera
   {
      
      public const const_cameraViewAngle:Number = 0.5235987755982988;
      
      public const const_cameraDepth:Number = 1;
      
      public const const_cameraViewWidth:int = 960;
      
      public const const_cameraViewHeight:int = 560;
      
      private var m_coordinate:Coordinate;
      
      private var m_screenCoordinate:Coordinate;
      
      private var m_view:View;
      
      private var m_worldMainRenderLayerScreenZ:Number;
      
      private var m_cameraMaxX:Number;
      
      private var m_cameraMidX:Number;
      
      private var m_transformCoordinate:TransformCoordinate;
      
      private var m_world:World;
      
      public function Camera()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_coordinate);
         m_coordinate = null;
         ClearUtil.clearObject(m_screenCoordinate);
         m_screenCoordinate = null;
         ClearUtil.clearObject(m_view);
         m_view = null;
         m_transformCoordinate = null;
         m_world = null;
      }
      
      public function setTransformcoordinate(param1:TransformCoordinate) : void
      {
         m_transformCoordinate = param1;
      }
      
      public function setWorld(param1:World) : void
      {
         m_world = param1;
      }
      
      public function init() : void
      {
         m_screenCoordinate = new Coordinate();
         m_coordinate = new Coordinate();
         m_view = new View();
         m_view.setWorld(m_world);
         m_view.setCamera(this);
         m_view.init();
         getCameraMaxX();
         getCameraMidX();
      }
      
      public function addX() : void
      {
      }
      
      public function setWorldMainRenderLayerScreenZ(param1:Number) : void
      {
         m_worldMainRenderLayerScreenZ = param1;
      }
      
      public function setNewScreenCoordinate(param1:Number, param2:Number) : void
      {
         m_screenCoordinate.setX(param1);
         m_screenCoordinate.setY(param2);
         getCameraMaxX();
         getCameraMidX();
      }
      
      public function getView() : View
      {
         return m_view;
      }
      
      public function getMinX() : Number
      {
         return getScreenX();
      }
      
      public function getMaxX() : Number
      {
         return m_cameraMaxX;
      }
      
      public function getMidX() : Number
      {
         return m_cameraMidX;
      }
      
      public function getX() : Number
      {
         return getScreenX();
      }
      
      public function getScreenX() : Number
      {
         return m_screenCoordinate.getX();
      }
      
      public function getScreenY() : Number
      {
         return m_screenCoordinate.getY();
      }
      
      public function getWorldMainRenderLayerScreenZ() : Number
      {
         return m_worldMainRenderLayerScreenZ;
      }
      
      public function getScreenZ() : Number
      {
         if(m_screenCoordinate.getZ() != 0)
         {
            throw new Error("二维游戏的摄像头坐标z必须为0");
         }
         return m_screenCoordinate.getZ();
      }
      
      private function getCameraMaxX() : void
      {
         m_cameraMaxX = m_screenCoordinate.getX() + 960;
      }
      
      private function getCameraMidX() : void
      {
         m_cameraMidX = m_screenCoordinate.getX() + 960 / 2;
      }
   }
}

