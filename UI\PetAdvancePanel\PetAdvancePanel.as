package UI.PetAdvancePanel
{
   import UI.Button.QuitBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.ConsumptionGuide.GuideBuyPopUpBox;
   import UI.EquipmentCells.PackageEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Skills.Skill;
   import UI.SmallPackage.SmallPackage;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.Utils.CreateAdvancePet.CreateAdvancePet;
   import UI.Utils.CreateAdvancePet.CreateAdvancePetReturn;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.broadcast.SubmitFunction;
   import YJFY.EntityShowContainer;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PetAdvancePanel extends MySprite implements IChoiceListener
   {
      
      public var quitBtn:QuitBtn;
      
      private var _show:MovieClip;
      
      private var _showMovieClip:MovieClipPlayLogicShell;
      
      private var _originalPetContainer:MovieClipPlayLogicShell;
      
      private var _advancePetContainer:MovieClipPlayLogicShell;
      
      private var _advanceFruitContainer:MovieClipPlayLogicShell;
      
      private var _advanceBtn:ButtonLogicShell2;
      
      private var _arrow1:MovieClipPlayLogicShell;
      
      private var _arrow2:MovieClipPlayLogicShell;
      
      private var _haveNumText:TextField;
      
      private var _slashText:TextField;
      
      private var _needNumText:TextField;
      
      private var _oldPetNameShow:MovieClipPlayLogicShell;
      
      private var _fruitNameShow:MovieClipPlayLogicShell;
      
      private var _fruitAnimation:MovieClipPlayLogicShell;
      
      private var _activeSkillContainer:Sprite;
      
      private var _awakeSkillContainer:Sprite;
      
      private var _passiveSkillContainer1:Sprite;
      
      private var _passiveSkillContainer2:Sprite;
      
      private var _passiveSkillContainer3:Sprite;
      
      private var _passiveSkillContainer4:Sprite;
      
      private var _passiveSkillContainer5:Sprite;
      
      private var _multiChocieAdvancePetPanel:MultiAdvancePetChoicePanel;
      
      private var _petDataXML:XML;
      
      private var _originalPet:Pet;
      
      private var _advancePet:Pet;
      
      private var _advancePetEquipmentVO:EquipmentVO;
      
      private var _originalPetShowContainer:EntityShowContainer;
      
      private var _advancePetShowContaner:EntityShowContainer;
      
      private var _advanceXML:XML;
      
      private var _needMaterialVOs:Vector.<EquipmentVO>;
      
      private var _needMaterialNums:Vector.<int>;
      
      private var _buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var _popUpBox:GuideBuyPopUpBox;
      
      private var _isLock:Boolean;
      
      public function PetAdvancePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         _show = null;
         if(_showMovieClip)
         {
            _showMovieClip.clear();
         }
         _showMovieClip = null;
         if(_originalPetContainer)
         {
            _originalPetContainer.clear();
         }
         _originalPetContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advanceBtn)
         {
            _advanceBtn.clear();
         }
         _advanceBtn = null;
         if(_arrow1)
         {
            _arrow1.clear();
         }
         _arrow1 = null;
         if(_arrow2)
         {
            _arrow2.clear();
         }
         _arrow2 = null;
         _haveNumText = null;
         _slashText = null;
         _needNumText = null;
         if(_oldPetNameShow)
         {
            _oldPetNameShow.clear();
         }
         _oldPetNameShow = null;
         if(_fruitNameShow)
         {
            _fruitNameShow.clear();
         }
         _fruitNameShow = null;
         if(_fruitAnimation)
         {
            _fruitAnimation.clear();
         }
         _fruitAnimation = null;
         ClearUtil.clearDisplayObjectInContainer(_activeSkillContainer);
         _activeSkillContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_awakeSkillContainer);
         _awakeSkillContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer1);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer2);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer3);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer4);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer5);
         _passiveSkillContainer1 = null;
         _passiveSkillContainer2 = null;
         _passiveSkillContainer3 = null;
         _passiveSkillContainer4 = null;
         _passiveSkillContainer5 = null;
         _petDataXML = null;
         if(_originalPet)
         {
            _originalPet.petEquipmentVO = null;
            _originalPet.clear();
         }
         _originalPet = null;
         if(_advancePet)
         {
            _advancePet.clear();
         }
         _advancePet = null;
         if(_advancePetEquipmentVO)
         {
            _advancePetEquipmentVO.clear();
         }
         _advancePetEquipmentVO = null;
         ClearUtil.clearObject(_originalPetContainer);
         _originalPetContainer = null;
         ClearUtil.clearObject(_advancePetContainer);
         _advancePetContainer = null;
         _advanceXML = null;
         ClearUtil.nullArr(_needMaterialVOs);
         _needMaterialVOs = null;
         ClearUtil.nullArr(_needMaterialNums);
         _needMaterialNums = null;
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_popUpBox)
         {
            _popUpBox.clear();
         }
         _popUpBox = null;
         ClearUtil.clearObject(_multiChocieAdvancePetPanel);
         _multiChocieAdvancePetPanel = null;
      }
      
      private function clearPanel() : void
      {
         initPutBeforeFrame();
      }
      
      private function putInEquipment(param1:UIBtnEvent) : void
      {
         var _loc7_:* = null;
         var _loc6_:EquipmentVO = null;
         var _loc10_:EquipmentVO = null;
         var _loc3_:EquipmentVO = null;
         var _loc5_:CreateAdvancePetReturn = null;
         var _loc4_:IEquipmentCell = param1.target.parent.parent;
         var _loc8_:Pet = _originalPet;
         var _loc2_:int = int(SmallPackage.getInstance().currentSmallPackage.area.equipmentCells.indexOf(param1.target.parent.parent));
         if(_loc8_.petEquipmentVO)
         {
            _loc6_ = _loc8_.petEquipmentVO;
            _loc10_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
            if(_loc10_)
            {
               SmallPackage.getInstance().currentSmallPackage.addEquipmentVO(_loc6_,_loc2_);
               SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
               _loc8_.petEquipmentVO = _loc10_ as PetEquipmentVO;
            }
         }
         else
         {
            _loc3_ = SmallPackage.getInstance().currentSmallPackage.removeEquipmentVO(_loc2_);
            SmallPackage.getInstance().currentSmallPackage.refreshEquipments();
            _loc8_.petEquipmentVO = _loc3_ as PetEquipmentVO;
         }
         _loc8_.petEquipmentVO = _loc8_.petEquipmentVO;
         hideSmallPackage();
         clearAdvancePet();
         clearAdvanceMaterialAndNums();
         _advanceXML = null;
         refreshAdvanceDataAndShow();
         var _loc9_:XMLList = getAdvanceXMLList();
         if(_loc9_.length() > 1)
         {
            showMultiChoiceAdvancePetShow(_loc9_);
         }
         else
         {
            if(_loc9_.length() == 1)
            {
               _advanceXML = _loc9_[0];
            }
            if(_advanceXML)
            {
               _loc5_ = new CreateAdvancePet().createAdvancePet(int(_advanceXML.@newId),_originalPet);
               _advancePetEquipmentVO = _loc5_.getAdvancePetEquipmentVO();
               _advancePet = _loc5_.getAdvancePet();
               _loc5_.nullQuotes();
               ClearUtil.clearObject(_loc5_);
               _loc5_ = null;
            }
            getAdvancePet(_advancePet);
         }
      }
      
      private function showMultiChoiceAdvancePetShow(param1:XMLList) : void
      {
         initMutliAdvancePetChoiceFrame();
         _multiChocieAdvancePetPanel.setData(param1,_originalPet,this);
      }
      
      public function choice(param1:Pet, param2:XML) : void
      {
         _advanceXML = param2;
         clearAdvancePet();
         getAdvancePet(param1);
         ClearUtil.clearObject(_multiChocieAdvancePetPanel);
         _multiChocieAdvancePetPanel = null;
      }
      
      private function getAdvancePet(param1:Pet) : void
      {
         _advancePet = param1;
         _advancePetEquipmentVO = param1.petEquipmentVO;
         if(Boolean(_advancePet) && _advancePet.petEquipmentVO)
         {
            if(_advancePet)
            {
               _advancePet.petEquipmentVO = _advancePet.petEquipmentVO;
            }
            getAdvanceMaterialsAndNums();
            refreshAdvanceDataAndShow();
         }
         else
         {
            refreshAdvanceDataAndShow();
         }
      }
      
      private function refreshAdvanceDataAndShow() : void
      {
         var _loc1_:DisplayObject = null;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         clearPanel();
         if(Boolean(_originalPet) && Boolean(_originalPet.petEquipmentVO))
         {
            if(Boolean(_advancePet) && _advancePet.petEquipmentVO)
            {
               initputFrame();
            }
            else
            {
               initUnAbleAdvanceFrame();
            }
            _originalPetShowContainer.refreshPetShow(_originalPet.petEquipmentVO);
            _originalPetShowContainer.getShow().x = 155;
            _originalPetShowContainer.getShow().y = 194;
            _originalPetShowContainer.getShow().scaleX = _originalPetShowContainer.getShow().scaleY = 1.5;
            _originalPetContainer.gotoAndStop("normal");
            ClearUtil.clearDisplayObjectInContainer(_originalPetContainer.getShow()["petContainer"]);
            _originalPetContainer.getShow()["petContainer"].addChild(_originalPetShowContainer.getShow());
            _oldPetNameShow.gotoAndStop(_originalPet.petEquipmentVO.className);
            if(Boolean(_advancePet) && Boolean(_advancePet.petEquipmentVO))
            {
               _advancePetShowContaner.refreshPetShow(_advancePet.petEquipmentVO);
               _advancePetShowContaner.getShow().x = 80;
               _advancePetShowContaner.getShow().y = 140;
               _advancePetShowContaner.getShow().scaleX = _advancePetShowContaner.getShow().scaleY = 3;
               _advancePetContainer.gotoAndStop("normal");
               ClearUtil.clearDisplayObjectInContainer(_advancePetContainer.getShow()["newPetContainer"]);
               _advancePetContainer.getShow()["newPetContainer"].addChild(_advancePetShowContaner.getShow());
               ClearUtil.clearDisplayObjectInContainer(_activeSkillContainer);
               ClearUtil.clearDisplayObjectInContainer(_awakeSkillContainer);
               ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer1);
               ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer2);
               ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer3);
               ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer4);
               ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer5);
               if(_advancePet.petEquipmentVO.activeSkillVO)
               {
                  _loc1_ = new Skill(_advancePet.petEquipmentVO.activeSkillVO.clone()) as DisplayObject;
                  _loc1_.addEventListener("rollOver",onOver,false,0,true);
                  _loc1_.addEventListener("rollOut",onOut,false,0,true);
                  _loc1_.scaleX = _loc1_.scaleY = 0.7;
                  _activeSkillContainer.addChild(_loc1_);
               }
               if((_advancePet.petEquipmentVO as AdvancePetEquipmentVO).petAwakePassiveSkillVOs[0])
               {
                  _loc1_ = new Skill((_advancePet.petEquipmentVO as AdvancePetEquipmentVO).petAwakePassiveSkillVOs[0].clone());
                  _loc1_.addEventListener("rollOver",onOver,false,0,true);
                  _loc1_.addEventListener("rollOut",onOut,false,0,true);
                  _loc1_.scaleX = _loc1_.scaleY = 0.7;
                  _awakeSkillContainer.addChild(_loc1_);
               }
               _loc2_ = _advancePet.petEquipmentVO.passiveSkillVOs ? _advancePet.petEquipmentVO.passiveSkillVOs.length : 0;
               _loc3_ = 0;
               while(_loc3_ < _loc2_)
               {
                  if(_loc3_ < 5 && _advancePet.petEquipmentVO.passiveSkillVOs[_loc3_])
                  {
                     _loc1_ = new Skill(_advancePet.petEquipmentVO.passiveSkillVOs[_loc3_].clone());
                     _loc1_.addEventListener("rollOver",onOver,false,0,true);
                     _loc1_.addEventListener("rollOut",onOut,false,0,true);
                     _loc1_.scaleX = _loc1_.scaleY = 0.5;
                     (this["_passiveSkillContainer" + (_loc3_ + 1)] as Sprite).addChild(_loc1_);
                  }
                  _loc3_++;
               }
            }
            else
            {
               _advancePetContainer.gotoAndStop("unableAdvance");
            }
            if(_needMaterialVOs)
            {
               refreshShowMaterial();
            }
         }
      }
      
      private function refreshShowMaterial() : void
      {
         _needNumText.visible = true;
         _slashText.visible = true;
         _haveNumText.visible = true;
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
            _buyMaterialBtn = null;
         }
         if(_fruitAnimation)
         {
            _fruitAnimation.clear();
         }
         _fruitAnimation = null;
         _advanceFruitContainer.gotoAndStop(_needMaterialVOs[0].className);
         _fruitAnimation = new MovieClipPlayLogicShell();
         _fruitAnimation.setShow(_advanceFruitContainer.getShow()["fruitAnimation"]);
         if(_fruitNameShow)
         {
            _fruitNameShow.clear();
         }
         _fruitNameShow = new MovieClipPlayLogicShell();
         _fruitNameShow.setShow(_fruitAnimation.getShow()["fruitNameShow"]);
         _fruitNameShow.gotoAndStop(_needMaterialVOs[0].className);
         var _loc2_:int = getHaveMaterialNum(_needMaterialVOs[0]);
         var _loc1_:TextFormat = _haveNumText.defaultTextFormat;
         if(_loc2_ >= _needMaterialNums[0])
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyMaterialBtn = new BuyMaterialGuideBtn(_needMaterialVOs[0].id,refreshShowMaterial);
            _buyMaterialBtn.x = 826;
            _buyMaterialBtn.y = 387;
            addChild(_buyMaterialBtn);
         }
         _haveNumText.defaultTextFormat = _loc1_;
         _needNumText.text = _needMaterialNums[0].toString();
         _haveNumText.text = _loc2_.toString();
      }
      
      private function getAdvanceXMLList() : XMLList
      {
         if(_originalPet == null || _originalPet.petEquipmentVO == null)
         {
            throw new Error();
         }
         var _loc2_:int = _originalPet.petEquipmentVO.id;
         return _petDataXML.advanceData[0].advance.(@oldId == _loc2_);
      }
      
      private function getAdvanceXML() : void
      {
         if(_originalPet == null || _originalPet.petEquipmentVO == null)
         {
            return;
         }
         var _loc1_:int = _originalPet.petEquipmentVO.id;
         _advanceXML = _petDataXML.advanceData[0].advance.(@oldId == _loc1_)[0];
      }
      
      private function clearAdvancePet() : void
      {
         if(Boolean(_advancePet) && _advancePet.petEquipmentVO)
         {
            _advancePet.clear();
            _advancePet = new Pet(null);
            _advancePetEquipmentVO.clear();
            _advancePetEquipmentVO = null;
         }
      }
      
      private function clearAdvancePetShow() : void
      {
         if(_advancePetShowContaner.getShow())
         {
            if(_advancePetShowContaner.getShow().parent)
            {
               _advancePetShowContaner.getShow().parent.removeChild(_advancePetShowContaner.getShow());
            }
         }
      }
      
      private function clearAdvanceMaterialAndNums() : void
      {
         ClearUtil.nullArr(_needMaterialVOs);
         _needMaterialVOs = null;
         ClearUtil.nullArr(_needMaterialNums);
         _needMaterialNums = null;
      }
      
      private function getAdvanceMaterialsAndNums() : void
      {
         var _loc4_:XMLList = null;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:EquipmentVO = null;
         var _loc1_:XML = null;
         if(_advanceXML)
         {
            _needMaterialVOs = new Vector.<EquipmentVO>();
            _needMaterialNums = new Vector.<int>();
            _loc4_ = _advanceXML.needMaterial;
            _loc1_ = XMLSingle.getInstance().equipmentXML;
            _loc2_ = int(_loc4_.length());
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc3_ = XMLSingle.getEquipmentVOByID(_loc4_[_loc5_].@id,_loc1_);
               _needMaterialVOs.push(_loc3_);
               _needMaterialNums.push(int(_loc4_[_loc5_].@num));
               _loc5_++;
            }
         }
      }
      
      private function getHaveMaterialNum(param1:EquipmentVO) : int
      {
         var _loc2_:int = 0;
         return currentPlayer.playerVO.getHaveEquipmentNumInPackage(param1);
      }
      
      private function get currentPlayer() : Player
      {
         return SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1 : GamingUI.getInstance().player2;
      }
      
      private function startAdvance() : void
      {
         deleEquipmentsAndAdvance();
         playAdvanceAnimation();
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame2();
      }
      
      private function playAdvanceAnimation() : void
      {
         _isLock = true;
         hideSmallPackage();
         initStartFrame();
         GamingUI.getInstance().addMainLineTaskGoalGameEventStr("petAdvance");
         _originalPetContainer.gotoAndPlay("startAd",null,null,function():void
         {
            _arrow1.gotoAndPlay("start",null,null,function():void
            {
               _advancePetContainer.gotoAndPlay("start",null,null,function():void
               {
                  clearAdvancePet();
                  clearAdvanceMaterialAndNums();
                  clearPanel();
                  var _loc1_:EqIsShow = new EqIsShow();
                  _loc1_.petxml = _petDataXML;
                  SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc1_);
                  _isLock = false;
               },null);
            },null);
            _arrow2.gotoAndPlay("start");
         },null);
         if(_fruitAnimation)
         {
            _fruitAnimation.gotoAndPlay("startAd");
         }
      }
      
      private function deleEquipmentsAndAdvance() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<EquipmentVO> = currentPlayer.playerVO.packageEquipmentVOs;
         MyFunction.getInstance().minusEquipmentVOs(_loc1_,_needMaterialNums[0],_needMaterialVOs[0].id);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(Boolean(_loc1_[_loc3_]) && _loc1_[_loc3_] == _originalPet.petEquipmentVO)
            {
               _loc1_[_loc3_] = _advancePet.petEquipmentVO;
               SubmitFunction.getInstance().setData1(2,String(_loc1_[_loc3_].id),_loc1_[_loc3_].name);
               _advancePet.petEquipmentVO = null;
               if(_advancePetEquipmentVO)
               {
                  _advancePetEquipmentVO = null;
               }
               _advancePetEquipmentVO = null;
               _originalPet.petEquipmentVO.clear();
               _originalPet.petEquipmentVO = null;
               return;
            }
            _loc3_++;
         }
         throw new Error();
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickPutInBtn",putInEquipment,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("switchPlayerInSmallPackage",switchPlayerPanel,true,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showGuideBox",showBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickPutInBtn",putInEquipment,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("switchPlayerInSmallPackage",switchPlayerPanel,true);
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showGuideBox",showBox,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var materialIsEnough:Boolean;
         var e:ButtonEvent = param1;
         var _loc3_:* = e.button;
         if(_advanceBtn === _loc3_)
         {
            materialIsEnough = _needMaterialNums[0] <= getHaveMaterialNum(_needMaterialVOs[0]);
            if(Boolean(_advancePet) && Boolean(_advancePet.petEquipmentVO) && materialIsEnough)
            {
               MyFunction2.getUserStateIsRightFunction(function():void
               {
                  startAdvance();
               },showWarningBox,true);
            }
            else if(materialIsEnough == false)
            {
               showWarningBox("材料不足！不能超进化！",0);
            }
         }
      }
      
      private function showSmallPackage(param1:MouseEvent) : void
      {
         if(param1.currentTarget == _originalPetContainer.getShow() && !_isLock)
         {
            if(!getChildByName(SmallPackage.getInstance().name))
            {
               SmallPackage.getInstance().x = 300;
               SmallPackage.getInstance().y = 200;
               addChild(SmallPackage.getInstance());
            }
            SmallPackage.getInstance().visible = true;
         }
      }
      
      private function hideSmallPackage() : void
      {
         SmallPackage.getInstance().visible = false;
      }
      
      private function switchPlayerPanel(param1:UIBtnEvent) : void
      {
         _originalPet.petEquipmentVO = null;
         clearPanel();
         clearAdvancePet();
         var _loc2_:EqIsShow = new EqIsShow();
         _loc2_.petxml = _petDataXML;
         SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc2_);
      }
      
      private function onOver(param1:Event) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:Event) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 300;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function init() : void
      {
         _advancePetShowContaner = new EntityShowContainer();
         _advancePetShowContaner.init();
         _originalPetShowContainer = new EntityShowContainer();
         _originalPetShowContainer.init();
         _show = MyFunction2.returnShowByClassName("PetAdvancePanel") as MovieClip;
         addChild(_show);
         _showMovieClip = new MovieClipPlayLogicShell();
         _showMovieClip.setShow(_show);
         initPutBeforeFrame();
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _originalPet = new Pet(null);
         _advancePet = new Pet(null);
         MyFunction2.loadXMLFunction("petData",function(param1:XML):void
         {
            _petDataXML = param1;
            PackageEquipmentCell.packageBtnSate = 0x10 | 0x0100;
            var _loc2_:EqIsShow = new EqIsShow();
            _loc2_.petxml = _petDataXML;
            SmallPackage.getInstance().refreshSmallPackage("doublePackage",_loc2_);
         },null,true);
      }
      
      private function initPutBeforeFrame() : void
      {
         clearPutBeforeFrame();
         clearMultiAdvancePetChoiceFrame();
         clearUnAbleAdvanceFrame();
         clearPutFrame();
         clearStartFrame();
         _showMovieClip.gotoAndStop("putBefore");
         _originalPetContainer = new MovieClipPlayLogicShell();
         _originalPetContainer.setShow(_show["originalPetContainer"]);
         _originalPetContainer.gotoAndStop("pointOutPutInPet");
         _originalPetContainer.getShow().addEventListener("click",showSmallPackage,false,0,true);
         _advancePetContainer = new MovieClipPlayLogicShell();
         _advancePetContainer.setShow(_show["advancePetContainer"]);
         _advanceFruitContainer = new MovieClipPlayLogicShell();
         _advanceFruitContainer.setShow(_show["fruitContainer"]);
         _advanceFruitContainer.gotoAndStop("0");
      }
      
      private function clearPutBeforeFrame() : void
      {
         if(_originalPetContainer)
         {
            if(_originalPetContainer.getShow())
            {
               _originalPetContainer.getShow().removeEventListener("click",showSmallPackage,false);
            }
            _originalPetContainer.clear();
         }
         _originalPetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
      }
      
      private function initMutliAdvancePetChoiceFrame() : void
      {
         clearPutBeforeFrame();
         clearMultiAdvancePetChoiceFrame();
         clearUnAbleAdvanceFrame();
         clearPutFrame();
         clearStartFrame();
         _showMovieClip.gotoAndStop("multiAdvancePet");
         _originalPetContainer = new MovieClipPlayLogicShell();
         _originalPetContainer.setShow(_show["originalPetContainer"]);
         _originalPetContainer.gotoAndStop("normal");
         _originalPetContainer.getShow().addEventListener("click",showSmallPackage,false,0,true);
         _oldPetNameShow = new MovieClipPlayLogicShell();
         _oldPetNameShow.setShow(_originalPetContainer.getShow()["oldPetNameShow"]);
         _advanceFruitContainer = new MovieClipPlayLogicShell();
         _advanceFruitContainer.setShow(_show["fruitContainer"]);
         _advancePetContainer = new MovieClipPlayLogicShell();
         _advancePetContainer.setShow(_show["advancePetContainer"]);
         _advancePetContainer.gotoAndStop("unableAdvance");
         _activeSkillContainer = _show["activeSkillContainer"];
         _awakeSkillContainer = _show["awakeSkillContainer"];
         _passiveSkillContainer1 = _show["passiveSkillContainer1"];
         _passiveSkillContainer2 = _show["passiveSkillContainer2"];
         _passiveSkillContainer3 = _show["passiveSkillContainer3"];
         _passiveSkillContainer4 = _show["passiveSkillContainer4"];
         _passiveSkillContainer5 = _show["passiveSkillContainer5"];
         _multiChocieAdvancePetPanel = new MultiAdvancePetChoicePanel();
         _multiChocieAdvancePetPanel.setShow(_show["multiAdvancePetChoicePanel"]);
         ClearUtil.clearDisplayObjectInContainer(_activeSkillContainer);
         ClearUtil.clearDisplayObjectInContainer(_awakeSkillContainer);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer1);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer2);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer3);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer4);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer5);
      }
      
      private function clearMultiAdvancePetChoiceFrame() : void
      {
         if(_originalPetContainer)
         {
            if(_originalPetContainer.getShow())
            {
               _originalPetContainer.getShow().removeEventListener("click",showSmallPackage,false);
            }
            _originalPetContainer.clear();
         }
         _originalPetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
         _activeSkillContainer = null;
         _awakeSkillContainer = null;
         _passiveSkillContainer1 = null;
         _passiveSkillContainer2 = null;
         _passiveSkillContainer3 = null;
         _passiveSkillContainer4 = null;
         _passiveSkillContainer5 = null;
         ClearUtil.clearObject(_multiChocieAdvancePetPanel);
         _multiChocieAdvancePetPanel = null;
      }
      
      private function initUnAbleAdvanceFrame() : void
      {
         clearPutBeforeFrame();
         clearMultiAdvancePetChoiceFrame();
         clearUnAbleAdvanceFrame();
         clearPutFrame();
         clearStartFrame();
         _showMovieClip.gotoAndStop("unAbleAdvance");
         _originalPetContainer = new MovieClipPlayLogicShell();
         _originalPetContainer.setShow(_show["originalPetContainer"]);
         _originalPetContainer.gotoAndStop("normal");
         _originalPetContainer.getShow().addEventListener("click",showSmallPackage,false,0,true);
         _oldPetNameShow = new MovieClipPlayLogicShell();
         _oldPetNameShow.setShow(_originalPetContainer.getShow()["oldPetNameShow"]);
         _advanceFruitContainer = new MovieClipPlayLogicShell();
         _advanceFruitContainer.setShow(_show["fruitContainer"]);
         _advancePetContainer = new MovieClipPlayLogicShell();
         _advancePetContainer.setShow(_show["advancePetContainer"]);
         _advancePetContainer.gotoAndStop("normal");
         _activeSkillContainer = _show["activeSkillContainer"];
         _awakeSkillContainer = _show["awakeSkillContainer"];
         _passiveSkillContainer1 = _show["passiveSkillContainer1"];
         _passiveSkillContainer2 = _show["passiveSkillContainer2"];
         _passiveSkillContainer3 = _show["passiveSkillContainer3"];
         _passiveSkillContainer4 = _show["passiveSkillContainer4"];
         _passiveSkillContainer5 = _show["passiveSkillContainer5"];
         ClearUtil.clearDisplayObjectInContainer(_activeSkillContainer);
         ClearUtil.clearDisplayObjectInContainer(_awakeSkillContainer);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer1);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer2);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer3);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer4);
         ClearUtil.clearDisplayObjectInContainer(_passiveSkillContainer5);
      }
      
      private function clearUnAbleAdvanceFrame() : void
      {
         if(_originalPetContainer)
         {
            if(_originalPetContainer.getShow())
            {
               _originalPetContainer.getShow().removeEventListener("click",showSmallPackage,false);
            }
            _originalPetContainer.clear();
         }
         _originalPetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
         _activeSkillContainer = null;
         _awakeSkillContainer = null;
         _passiveSkillContainer1 = null;
         _passiveSkillContainer2 = null;
         _passiveSkillContainer3 = null;
         _passiveSkillContainer4 = null;
         _passiveSkillContainer5 = null;
      }
      
      private function initputFrame() : void
      {
         clearPutBeforeFrame();
         clearMultiAdvancePetChoiceFrame();
         clearUnAbleAdvanceFrame();
         clearPutFrame();
         clearStartFrame();
         _showMovieClip.gotoAndStop("put");
         _originalPetContainer = new MovieClipPlayLogicShell();
         _originalPetContainer.setShow(_show["originalPetContainer"]);
         _originalPetContainer.gotoAndStop("normal");
         _originalPetContainer.getShow().addEventListener("click",showSmallPackage,false,0,true);
         _oldPetNameShow = new MovieClipPlayLogicShell();
         _oldPetNameShow.setShow(_originalPetContainer.getShow()["oldPetNameShow"]);
         _advanceFruitContainer = new MovieClipPlayLogicShell();
         _advanceFruitContainer.setShow(_show["fruitContainer"]);
         _advancePetContainer = new MovieClipPlayLogicShell();
         _advancePetContainer.setShow(_show["advancePetContainer"]);
         _advancePetContainer.gotoAndStop("normal");
         _advanceBtn = new ButtonLogicShell2();
         _advanceBtn.setShow(_show["advanceBtn"]);
         _advanceBtn.setTipString("点击开始超进化");
         _haveNumText = _show["haveNumText"];
         _slashText = _show["slashText"];
         _needNumText = _show["needNumText"];
         _activeSkillContainer = _show["activeSkillContainer"];
         _awakeSkillContainer = _show["awakeSkillContainer"];
         _passiveSkillContainer1 = _show["passiveSkillContainer1"];
         _passiveSkillContainer2 = _show["passiveSkillContainer2"];
         _passiveSkillContainer3 = _show["passiveSkillContainer3"];
         _passiveSkillContainer4 = _show["passiveSkillContainer4"];
         _passiveSkillContainer5 = _show["passiveSkillContainer5"];
      }
      
      private function clearPutFrame() : void
      {
         if(_originalPetContainer)
         {
            _originalPetContainer.clear();
            if(_originalPetContainer.getShow())
            {
               _originalPetContainer.getShow().removeEventListener("click",showSmallPackage,false);
            }
         }
         _originalPetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
         if(_advanceBtn)
         {
            _advanceBtn.clear();
         }
         _advanceBtn = null;
         _haveNumText = null;
         _slashText = null;
         _needNumText = null;
         _activeSkillContainer = null;
         _awakeSkillContainer = null;
         _passiveSkillContainer1 = null;
         _passiveSkillContainer2 = null;
         _passiveSkillContainer3 = null;
         _passiveSkillContainer4 = null;
         _passiveSkillContainer5 = null;
      }
      
      private function initStartFrame() : void
      {
         clearPutBeforeFrame();
         clearMultiAdvancePetChoiceFrame();
         clearUnAbleAdvanceFrame();
         clearPutFrame();
         clearStartFrame();
         _showMovieClip.gotoAndStop("start");
         _originalPetContainer = new MovieClipPlayLogicShell();
         _originalPetContainer.setShow(_show["originalPetContainer"]);
         _originalPetContainer.gotoAndStop("normal");
         _advanceFruitContainer = new MovieClipPlayLogicShell();
         _advanceFruitContainer.setShow(_show["fruitContainer"]);
         _advancePetContainer = new MovieClipPlayLogicShell();
         _advancePetContainer.setShow(_show["advancePetContainer"]);
         _advancePetContainer.gotoAndStop("normal");
         _arrow1 = new MovieClipPlayLogicShell();
         _arrow1.setShow(_show["arrow1"]);
         _arrow2 = new MovieClipPlayLogicShell();
         _arrow2.setShow(_show["arrow2"]);
         _activeSkillContainer = _show["activeSkillContainer"];
         _awakeSkillContainer = _show["awakeSkillContainer"];
         _passiveSkillContainer1 = _show["passiveSkillContainer1"];
         _passiveSkillContainer2 = _show["passiveSkillContainer2"];
         _passiveSkillContainer3 = _show["passiveSkillContainer3"];
         _passiveSkillContainer4 = _show["passiveSkillContainer4"];
         _passiveSkillContainer5 = _show["passiveSkillContainer5"];
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
            _buyMaterialBtn = null;
         }
      }
      
      private function clearStartFrame() : void
      {
         if(_originalPetContainer)
         {
            _originalPetContainer.clear();
         }
         _originalPetContainer = null;
         if(_advanceFruitContainer)
         {
            _advanceFruitContainer.clear();
         }
         _advanceFruitContainer = null;
         if(_advancePetContainer)
         {
            _advancePetContainer.clear();
         }
         _advancePetContainer = null;
         if(_arrow1)
         {
            _arrow1.clear();
         }
         _arrow1 = null;
         if(_arrow2)
         {
            _arrow2.clear();
         }
         _arrow2 = null;
         _activeSkillContainer = null;
         _awakeSkillContainer = null;
         _passiveSkillContainer1 = null;
         _passiveSkillContainer2 = null;
         _passiveSkillContainer3 = null;
         _passiveSkillContainer4 = null;
         _passiveSkillContainer5 = null;
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(_popUpBox)
         {
            if(getChildByName(_popUpBox.name))
            {
               removeChild(_popUpBox);
            }
            _popUpBox = null;
         }
      }
      
      private function showBox(param1:UIDataEvent) : void
      {
         var _loc3_:EquipmentVO = param1.data.equipmentVO;
         var _loc2_:Player = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1 : GamingUI.getInstance().player2;
         if(Boolean(_popUpBox) && getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc3_,"shopWallPrice");
            _popUpBox.player = _loc2_;
            _popUpBox.fun = param1.data.fun;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
         }
         else if(!_popUpBox)
         {
            _popUpBox = new GuideBuyPopUpBox(_loc3_,_loc2_,"shopWallPrice");
            _popUpBox.fun = param1.data.fun;
            _popUpBox.player = _loc2_;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            _popUpBox.x = (width - _popUpBox.width) / 2;
            _popUpBox.y = (height - _popUpBox.height) / 2;
            addChildAt(_popUpBox,numChildren - 2);
         }
         else if(Boolean(_popUpBox) && !getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc3_,"shopWallPrice");
            _popUpBox.player = _loc2_;
            _popUpBox.fun = param1.data.fun;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            _popUpBox.x = (width - _popUpBox.width) / 2;
            _popUpBox.y = (height - _popUpBox.height) / 2;
            addChildAt(_popUpBox,numChildren - 2);
         }
      }
   }
}

import UI.Equipments.EquipmentVO.EquipmentVO;
import UI.Equipments.PetEquipments.PetEquipmentVO;
import UI.SmallPackage.IEqIsShowInSmallPackage;

class EqIsShow implements IEqIsShowInSmallPackage
{
   
   public var petxml:XML;
   
   public function EqIsShow()
   {
      super();
   }
   
   public function clear() : void
   {
   }
   
   public function judeIsShow(param1:EquipmentVO) : Boolean
   {
      var _loc3_:XMLList = null;
      var _loc2_:Boolean = false;
      var _loc4_:int = 0;
      if(!(param1 is PetEquipmentVO))
      {
         return false;
      }
      if(petxml)
      {
         _loc3_ = petxml.advanceData[0].advance;
         _loc2_ = false;
         _loc4_ = 0;
         while(_loc4_ < _loc3_.length())
         {
            if(int(_loc3_[_loc4_].@oldId) == param1.id)
            {
               _loc2_ = true;
            }
            _loc4_++;
         }
         return _loc2_;
      }
      return true;
   }
}
