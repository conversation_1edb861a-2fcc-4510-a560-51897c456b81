package YJFY.Skill.PetSkills
{
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_PiKaQiuSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfColorTransforms;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_Pet13_14Skill extends CuboidAreaAttackSkill2_PiKaQiuSkill
   {
      
      private const m_const_createRandomShowFrameLabel:String = "startCreate";
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      private var m_flashViewDataOfColorTransforms:DataOfColorTransforms;
      
      public function Skill_Pet13_14Skill()
      {
         super();
         m_shakeViewDataOfPoints = new DataOfPoints();
         m_flashViewDataOfColorTransforms = new DataOfColorTransforms();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         ClearUtil.clearObject(m_flashViewDataOfColorTransforms);
         m_flashViewDataOfColorTransforms = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc5_:String = String(param1.shakeView[0].@swfPath);
         var _loc3_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc5_,_loc3_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
         var _loc2_:String = String(param1.flashView[0].@swfPath);
         var _loc4_:String = String(param1.flashView[0].@className);
         m_myLoader.getClass(_loc2_,_loc4_,getFlashViewMovieClipSuccess,getFlashViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if("startCreate" === _loc2_)
         {
            if(isNaN(m_nextCreateRandomShowTime))
            {
               m_nextCreateRandomShowTime = m_world.getWorldTime();
            }
         }
      }
      
      override protected function randomEntityReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         super.randomEntityReachFrame(param1,param2);
         var _loc3_:* = param2;
         if(m_randomEntityReachGroundFrameLabel === _loc3_)
         {
            if(!m_world)
            {
               return;
            }
            m_world.shakeView(m_shakeViewDataOfPoints);
            m_world.flashView(m_flashViewDataOfColorTransforms);
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function getFlashViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_flashViewDataOfColorTransforms.initByMovieClip(new _loc2_());
      }
      
      private function getFlashViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

