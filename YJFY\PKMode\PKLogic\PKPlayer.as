package YJFY.PKMode.PKLogic
{
   import YJFY.Entity.AttackData;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import YJFY.XydzjsData.AttackDataCalculate;
   
   public class PKPlayer extends PKEntity implements IEntity
   {
      
      private var m_pkBlood1:uint;
      
      private var m_pkBlood2:uint;
      
      private var m_pkPet:PkPet;
      
      private var m_attackDataCalculate:AttackDataCalculate;
      
      private var m_pkworld:PKWorld;
      
      private var m_player:PlayerXydzjs;
      
      private var m_pkStepWorld:StepAttackGameWorld;
      
      private var m_stepDataManager:StepDataManager;
      
      public function PKPlayer()
      {
         super();
         m_pkPet = new PkPet();
         m_attackDataCalculate = new AttackDataCalculate();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pkPet);
         m_pkPet = null;
         ClearUtil.clearObject(m_attackDataCalculate);
         m_attackDataCalculate = null;
         ClearUtil.clearObject(m_player);
         m_player = null;
         m_pkStepWorld = null;
         m_stepDataManager = null;
         super.clear();
      }
      
      override public function setEntity(param1:GameEntity) : void
      {
         super.setEntity(param1);
         m_player = param1 as PlayerXydzjs;
      }
      
      public function setPet(param1:PetXydzjs) : void
      {
         m_pkPet.setEntity(param1);
      }
      
      public function setPKWorld(param1:PKWorld) : void
      {
         m_pkworld = param1;
      }
      
      public function setStepDataManager(param1:StepDataManager) : void
      {
         m_stepDataManager = param1;
         m_pkPet.setStepDataManager(m_stepDataManager);
      }
      
      public function readyStartFight() : void
      {
         m_pkBlood1 = m_player.getUiPlayer().playerVO.bloodVolume;
         m_pkBlood2 = m_player.getUiPlayer().playerVO.bloodVolume;
      }
      
      override public function render() : void
      {
         super.render();
         if(m_player)
         {
            m_player.render();
         }
      }
      
      public function getPlayer() : PlayerXydzjs
      {
         return m_player;
      }
      
      public function getPet() : PetXydzjs
      {
         return m_pkPet.getPet();
      }
      
      public function getPkPet() : PkPet
      {
         return m_pkPet;
      }
      
      public function getPkBlood1() : uint
      {
         return m_pkBlood1;
      }
      
      public function getPkBlood2() : uint
      {
         return m_pkBlood2;
      }
      
      public function getTotalPkBlood() : uint
      {
         return m_player.getUiPlayer().playerVO.bloodVolume;
      }
      
      public function getOffensiveValue() : int
      {
         return m_player.getUiPlayer().playerVO.offensiveValue;
      }
      
      public function attack(param1:Vector.<IEntity>) : void
      {
         var _loc6_:int = 0;
         var _loc2_:BeAttackDataOfOneStepData = null;
         var _loc4_:AttackData = null;
         var _loc3_:OneStepData = new OneStepData(this);
         var _loc5_:int = int(param1.length);
         if(_loc5_ == 0)
         {
            throw new Error();
         }
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc4_ = new AttackData(true,0,false,false,m_player.getAttackHurtDuration());
            m_attackDataCalculate.caculateHurt(m_player,(param1[_loc6_] as PKPlayer).getPlayer(),_loc4_);
            (param1[_loc6_] as PKPlayer).decPkBlood1(_loc4_.getHurt());
            AttackData.hurtNum1++;
            _loc2_ = new BeAttackDataOfOneStepData(param1[_loc6_],_loc4_);
            _loc2_.testIndex = AttackData.hurtNum1;
            _loc3_.addBeAttackData(_loc2_);
            _loc6_++;
         }
         m_stepDataManager.addOneStepData(_loc3_);
      }
      
      public function decPkBlood1(param1:uint) : void
      {
         m_pkBlood1 = Math.max(0,m_pkBlood1 - param1);
      }
      
      public function decPkBlood2(param1:uint) : void
      {
         m_pkBlood2 = Math.max(0,m_pkBlood2 - param1);
      }
      
      public function isDie() : Boolean
      {
         if(m_pkBlood1 <= 0)
         {
            return true;
         }
         return false;
      }
      
      public function Die() : void
      {
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_pkStepWorld = param1;
      }
   }
}

