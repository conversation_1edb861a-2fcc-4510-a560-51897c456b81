package YJFY.PKMode
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PKTargetShowContainer
   {
      
      private var m_pkBtn:ButtonLogicShell;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_pkStateShow:MovieClipPlayLogicShell2;
      
      private var m_pkPlayerHeadShow:MovieClipPlayLogicShell2;
      
      private var m_nameText:TextField;
      
      private var m_pkTargetPlayerData:PKTargetPlayerData;
      
      private var m_show:MovieClip;
      
      public function PKTargetShowContainer()
      {
         super();
         m_pkStateShow = new MovieClipPlayLogicShell2();
         m_pkPlayerHeadShow = new MovieClipPlayLogicShell2();
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(m_pkBtn);
         m_pkBtn = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_pkStateShow);
         m_pkStateShow = null;
         ClearUtil.clearObject(m_pkPlayerHeadShow);
         m_pkPlayerHeadShow = null;
         m_nameText = null;
         m_pkTargetPlayerData = null;
      }
      
      public function setPKTargetPlayerData(param1:PKTargetPlayerData) : void
      {
         m_pkTargetPlayerData = param1;
         initShow2();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getPKBtn() : ButtonLogicShell
      {
         return m_pkBtn;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      public function getPKTargetPlayerData() : PKTargetPlayerData
      {
         return m_pkTargetPlayerData;
      }
      
      private function initShow() : void
      {
         m_pkStateShow.setShow(m_show["word"]);
         m_pkPlayerHeadShow.setShow(m_show["playerHead"]);
      }
      
      private function initShow2() : void
      {
         if(m_show == null)
         {
            return;
         }
         var _loc1_:int = m_pkTargetPlayerData ? m_pkTargetPlayerData.getPKState() : 0;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_pkBtn);
         m_pkBtn = null;
         switch(_loc1_)
         {
            case 0:
               m_pkStateShow.gotoAndStop("nothing");
               m_pkBtn = new ButtonLogicShell();
               m_pkBtn.setShow(m_pkStateShow.getShow()["chanllegeBtn"]);
               break;
            case 1:
               m_pkStateShow.gotoAndStop("win");
               m_lookUpBtn = new ButtonLogicShell2();
               m_lookUpBtn.setShow(m_pkStateShow.getShow()["lookUpBtn"]);
               break;
            case 2:
               m_pkStateShow.gotoAndStop("fail");
               m_lookUpBtn = new ButtonLogicShell2();
               m_lookUpBtn.setShow(m_pkStateShow.getShow()["lookUpBtn"]);
               break;
            default:
               throw new Error();
         }
         var _loc2_:String = m_pkTargetPlayerData ? m_pkTargetPlayerData.getPlayer1Type() : "";
         switch(_loc2_)
         {
            case "SunWuKong":
            case "BaiLongMa":
            case "ErLangShen":
            case "ChangE":
            case "Fox":
            case "TieShan":
            case "Houyi":
            case "ZiXia":
               m_pkPlayerHeadShow.gotoAndStop(m_pkTargetPlayerData.getPlayer1Type());
               m_nameText = m_pkPlayerHeadShow.getShow()["nameText"];
               MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_nameText);
               break;
            default:
               m_pkPlayerHeadShow.gotoAndStop("nothing");
         }
         if(m_nameText)
         {
            if(m_pkTargetPlayerData)
            {
               m_nameText.text = m_pkTargetPlayerData.getUid();
            }
            else
            {
               m_nameText.text = "";
            }
         }
      }
   }
}

