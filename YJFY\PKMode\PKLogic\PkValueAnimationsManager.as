package YJFY.PKMode.PKLogic
{
   import UI.MyFunction2;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IEntity;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell2;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   
   public class PkValueAnimationsManager
   {
      
      private var m_normalAttackHurtValueShowClassName:String = "NormalAttackValueAnimation_pk";
      
      private var m_criticalAttackHurtValueShowClassName:String = "CriticalAttackValueAnimation_pk";
      
      private var m_shanBiShowClassName:String = "ShanBiAnimation_pk";
      
      private var m_useNormalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_wasteNormalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_normalAttackValueAnimationsPool:ObjectsPool;
      
      private var m_needRemoveNormalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_normalAttackValueAnimationStopListener:StopListener;
      
      private var m_useCriticalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_wasteCriticalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_criticalAttackValueAnimationsPool:ObjectsPool;
      
      private var m_needRemoveCriticalAttackValueAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_criticalAttackValueAnimationStopListener:StopListener;
      
      private var m_useShanBiAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_wasteShanBiAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_shanBiAnimationsPool:ObjectsPool;
      
      private var m_needRemoveShanBiAnimations:Vector.<AnimationShowPlayLogicShell>;
      
      private var m_shanBiAnimatoinStopListener:StopListener;
      
      public function PkValueAnimationsManager()
      {
         super();
         m_useNormalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteNormalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_needRemoveNormalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_normalAttackValueAnimationsPool = new ObjectsPool(m_useNormalAttackValueAnimations,m_wasteNormalAttackValueAnimations,createNormalAttackValueAnimation,null);
         m_normalAttackValueAnimationStopListener = new StopListener();
         m_normalAttackValueAnimationStopListener.stop2Fun = normalAttackValueAnimationEnd;
         m_useCriticalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteCriticalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_needRemoveCriticalAttackValueAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_criticalAttackValueAnimationsPool = new ObjectsPool(m_useCriticalAttackValueAnimations,m_wasteCriticalAttackValueAnimations,createCriticalAttackValueAnimation,null);
         m_criticalAttackValueAnimationStopListener = new StopListener();
         m_criticalAttackValueAnimationStopListener.stop2Fun = criticalAttackValueAnimationEnd;
         m_useShanBiAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteShanBiAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_needRemoveShanBiAnimations = new Vector.<AnimationShowPlayLogicShell>();
         m_shanBiAnimationsPool = new ObjectsPool(m_useShanBiAnimations,m_wasteShanBiAnimations,createShanBiAnimation,null);
         m_shanBiAnimatoinStopListener = new StopListener();
         m_shanBiAnimatoinStopListener.stop2Fun = shanBiAnimationEnd;
      }
      
      public function clear() : void
      {
         clearValueAnimations();
         m_normalAttackHurtValueShowClassName = null;
         m_criticalAttackHurtValueShowClassName = null;
         m_shanBiShowClassName = null;
         ClearUtil.clearObject(m_normalAttackValueAnimationStopListener);
         m_normalAttackValueAnimationStopListener = null;
         ClearUtil.nullArr(m_needRemoveNormalAttackValueAnimations,false,false,false);
         m_needRemoveNormalAttackValueAnimations = null;
         ClearUtil.clearObject(m_criticalAttackValueAnimationStopListener);
         m_criticalAttackValueAnimationStopListener = null;
         ClearUtil.nullArr(m_needRemoveCriticalAttackValueAnimations,false,false,false);
         m_needRemoveCriticalAttackValueAnimations = null;
         ClearUtil.clearObject(m_shanBiAnimatoinStopListener);
         m_shanBiAnimatoinStopListener = null;
         ClearUtil.clearObject(m_needRemoveShanBiAnimations);
         m_needRemoveShanBiAnimations = null;
      }
      
      public function render() : void
      {
         renderValueAnimations();
      }
      
      public function addValueAnimationToTarget(param1:IEntity, param2:AttackData) : void
      {
         var _loc3_:AnimationShowPlayLogicShell = null;
         if(param2.getIsBeDodge())
         {
            _loc3_ = getOneShanBiAnimation();
            (_loc3_.getShow() as DisplayObject).x = 0 - param1.getBodyXRange() / 2;
            (_loc3_.getShow() as DisplayObject).y = 0 - param1.getBodyZRange();
            param1.getShow().addChild(_loc3_.getShow() as DisplayObject);
            _loc3_.gotoAndPlay("1");
         }
         else if(param2.getIsCritical())
         {
            _loc3_ = getOneCriticalAttackValueAnimation();
            (_loc3_.getShow() as DisplayObject).x = 0 - param1.getBodyXRange() / 2;
            (_loc3_.getShow() as DisplayObject).y = 0 - param1.getBodyZRange();
            param1.getShow().addChild(_loc3_.getShow() as DisplayObject);
            (_loc3_.extra as MultiPlaceNumLogicShell2).showNum(param2.getHurt());
            _loc3_.gotoAndPlay("1");
         }
         else
         {
            _loc3_ = getOneNormalAttackValueAnimation();
            (_loc3_.getShow() as DisplayObject).x = 0 - param1.getBodyXRange() / 2;
            (_loc3_.getShow() as DisplayObject).y = 0 - param1.getBodyZRange();
            param1.getShow().addChild(_loc3_.getShow() as DisplayObject);
            (_loc3_.extra as MultiPlaceNumLogicShell2).showNum(param2.getHurt());
            _loc3_.gotoAndPlay("1");
         }
      }
      
      private function getOneNormalAttackValueAnimation() : AnimationShowPlayLogicShell
      {
         return m_normalAttackValueAnimationsPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
      }
      
      private function getOneCriticalAttackValueAnimation() : AnimationShowPlayLogicShell
      {
         return m_criticalAttackValueAnimationsPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
      }
      
      private function getOneShanBiAnimation() : AnimationShowPlayLogicShell
      {
         return m_shanBiAnimationsPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
      }
      
      private function renderValueAnimations() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = m_useNormalAttackValueAnimations ? m_useNormalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_useNormalAttackValueAnimations[_loc2_].render();
            _loc2_++;
         }
         _loc1_ = int(m_needRemoveNormalAttackValueAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_normalAttackValueAnimationsPool.wasteOneObj(m_needRemoveNormalAttackValueAnimations[_loc2_]);
            _loc2_++;
         }
         _loc1_ = m_useCriticalAttackValueAnimations ? m_useCriticalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_useCriticalAttackValueAnimations[_loc2_].render();
            _loc2_++;
         }
         _loc1_ = int(m_needRemoveCriticalAttackValueAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_criticalAttackValueAnimationsPool.wasteOneObj(m_needRemoveCriticalAttackValueAnimations[_loc2_]);
            _loc2_++;
         }
         _loc1_ = m_useShanBiAnimations ? m_useShanBiAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_useShanBiAnimations[_loc2_].render();
            _loc2_++;
         }
         _loc1_ = int(m_needRemoveShanBiAnimations.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_shanBiAnimationsPool.wasteOneObj(m_needRemoveShanBiAnimations[_loc2_]);
            _loc2_++;
         }
         m_needRemoveNormalAttackValueAnimations.length = 0;
         m_needRemoveCriticalAttackValueAnimations.length = 0;
         m_needRemoveShanBiAnimations.length = 0;
      }
      
      private function createNormalAttackValueAnimation() : AnimationShowPlayLogicShell
      {
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_normalAttackValueAnimationStopListener);
         _loc1_.setShow(MyFunction2.returnShowByClassName(m_normalAttackHurtValueShowClassName),true);
         var _loc2_:MultiPlaceNumLogicShell2 = new MultiPlaceNumLogicShell2();
         _loc1_.extra = _loc2_;
         _loc2_.setShow(_loc1_.getShow()["clip"]["num"]);
         return _loc1_;
      }
      
      private function createCriticalAttackValueAnimation() : AnimationShowPlayLogicShell
      {
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_criticalAttackValueAnimationStopListener);
         _loc1_.setShow(MyFunction2.returnShowByClassName(m_criticalAttackHurtValueShowClassName),true);
         var _loc2_:MultiPlaceNumLogicShell2 = new MultiPlaceNumLogicShell2();
         _loc1_.extra = _loc2_;
         _loc2_.setShow(_loc1_.getShow()["clip"]["num"]);
         return _loc1_;
      }
      
      private function createShanBiAnimation() : AnimationShowPlayLogicShell
      {
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_shanBiAnimatoinStopListener);
         _loc1_.setShow(MyFunction2.returnShowByClassName(m_shanBiShowClassName),true);
         return _loc1_;
      }
      
      private function normalAttackValueAnimationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         if((param1.getShow() as DisplayObject).parent)
         {
            (param1.getShow() as DisplayObject).parent.removeChild(param1.getShow() as DisplayObject);
         }
         m_needRemoveNormalAttackValueAnimations.push(param1);
      }
      
      private function criticalAttackValueAnimationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         if((param1.getShow() as DisplayObject).parent)
         {
            (param1.getShow() as DisplayObject).parent.removeChild(param1.getShow() as DisplayObject);
         }
         m_needRemoveCriticalAttackValueAnimations.push(param1);
      }
      
      private function shanBiAnimationEnd(param1:AnimationShowPlayLogicShell) : void
      {
         if((param1.getShow() as DisplayObject).parent)
         {
            (param1.getShow() as DisplayObject).parent.removeChild(param1.getShow() as DisplayObject);
         }
         m_needRemoveShanBiAnimations.push(param1);
      }
      
      private function clearValueAnimations() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = m_useNormalAttackValueAnimations ? m_useNormalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_useNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_useNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_useNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_useNormalAttackValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_useNormalAttackValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_useNormalAttackValueAnimations[_loc2_]);
            m_useNormalAttackValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_useNormalAttackValueAnimations.length = 0;
         m_useNormalAttackValueAnimations = null;
         _loc1_ = m_wasteNormalAttackValueAnimations ? m_wasteNormalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_wasteNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_wasteNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_wasteNormalAttackValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_wasteNormalAttackValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteNormalAttackValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_wasteNormalAttackValueAnimations[_loc2_]);
            m_wasteNormalAttackValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_wasteNormalAttackValueAnimations.length = 0;
         m_wasteNormalAttackValueAnimations = null;
         ClearUtil.clearObject(m_normalAttackValueAnimationsPool);
         m_normalAttackValueAnimationsPool = null;
         _loc1_ = m_useCriticalAttackValueAnimations ? m_useCriticalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_useCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_useCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_useCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_useCriticalAttackValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_useCriticalAttackValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_useCriticalAttackValueAnimations[_loc2_]);
            m_useCriticalAttackValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_useCriticalAttackValueAnimations.length = 0;
         m_useCriticalAttackValueAnimations = null;
         _loc1_ = m_wasteCriticalAttackValueAnimations ? m_wasteCriticalAttackValueAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_wasteCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_wasteCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_wasteCriticalAttackValueAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_wasteCriticalAttackValueAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteCriticalAttackValueAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_wasteCriticalAttackValueAnimations[_loc2_]);
            m_wasteCriticalAttackValueAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_wasteCriticalAttackValueAnimations.length = 0;
         m_wasteCriticalAttackValueAnimations = null;
         ClearUtil.clearObject(m_criticalAttackValueAnimationsPool);
         m_criticalAttackValueAnimationsPool = null;
         _loc1_ = m_useShanBiAnimations ? m_useShanBiAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_useShanBiAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_useShanBiAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_useShanBiAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_useShanBiAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_useShanBiAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_useShanBiAnimations[_loc2_]);
            m_useShanBiAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_useShanBiAnimations.length = 0;
         m_useShanBiAnimations = null;
         _loc1_ = m_wasteShanBiAnimations ? m_wasteShanBiAnimations.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if((m_wasteShanBiAnimations[_loc2_].getShow() as DisplayObject).parent)
            {
               (m_wasteShanBiAnimations[_loc2_].getShow() as DisplayObject).parent.removeChild(m_wasteShanBiAnimations[_loc2_].getShow() as DisplayObject);
            }
            ClearUtil.clearObject(m_wasteShanBiAnimations[_loc2_].getShow());
            ClearUtil.clearObject(m_wasteShanBiAnimations[_loc2_].extra);
            ClearUtil.clearObject(m_wasteShanBiAnimations[_loc2_]);
            m_wasteShanBiAnimations[_loc2_] = null;
            _loc2_++;
         }
         m_wasteShanBiAnimations.length = 0;
         m_wasteShanBiAnimations = null;
         ClearUtil.clearObject(m_shanBiAnimationsPool);
         m_shanBiAnimationsPool = null;
      }
   }
}

