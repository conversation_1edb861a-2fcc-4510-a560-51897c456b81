package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.BulletEntity.BeatBackEntity;
   import YJFY.Entity.IEntity;
   
   public class CuboidAreaAttackSkill_OneBulletListener implements ICuboidAreaAttackSkill_OneBulletListener
   {
      
      public var beatBackEntityFun:Function;
      
      public function CuboidAreaAttackSkill_OneBulletListener()
      {
         super();
      }
      
      public function clear() : void
      {
         beatBackEntityFun = null;
      }
      
      public function beatBackEntity(param1:CuboidAreaAttackSkill_OneBullet, param2:BeatBackEntity, param3:IEntity) : void
      {
         if(Boolean(beatBackEntityFun))
         {
            beatBackEntityFun(param1,param2,param3);
         }
      }
   }
}

