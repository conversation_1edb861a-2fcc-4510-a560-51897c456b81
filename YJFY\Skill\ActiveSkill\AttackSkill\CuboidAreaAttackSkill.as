package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Utils.ClearUtil;
   import YJFY.World.CoordinateFunction;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   import YJFY.geom.Area3D.Cuboid;
   
   public class CuboidAreaAttackSkill extends AreaAttackSkill implements ICuboidAreaAttackSkill
   {
      
      protected var m_cuboidRange:Cuboid;
      
      protected var m_cuboidRangeToWorld:Cuboid;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      public function CuboidAreaAttackSkill()
      {
         super();
         m_cuboidRange = new Cuboid();
         m_cuboidRangeToWorld = new Cuboid();
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = attackSuccess;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_cuboidRange);
         m_cuboidRange = null;
         ClearUtil.clearObject(m_cuboidRangeToWorld);
         m_cuboidRangeToWorld = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_cuboidRange.setTo(param1.@x,param1.@y,param1.@z,param1.@xRange,param1.@yRange,param1.@zRange);
      }
      
      public function getCuboidRange() : Cuboid
      {
         return m_cuboidRange.clone();
      }
      
      public function getCuboidRangeToWorld() : Cuboid
      {
         getCuboidRangeToWorld2();
         return m_cuboidRangeToWorld;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
      }
      
      override protected function attackReach(param1:World) : void
      {
         super.attackReach(param1);
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         getCuboidRangeToWorld2();
         if(m_cuboidRangeToWorld.contains(param1,param2,param3))
         {
            return true;
         }
         return false;
      }
      
      protected function getCuboidRangeToWorld2() : void
      {
         CoordinateFunction.cuboidRangeTranToWorld2(m_owner.getShowDirection(),m_owner.getX(),m_owner.getY(),m_owner.getZ(),m_cuboidRange,m_cuboidRangeToWorld);
      }
   }
}

