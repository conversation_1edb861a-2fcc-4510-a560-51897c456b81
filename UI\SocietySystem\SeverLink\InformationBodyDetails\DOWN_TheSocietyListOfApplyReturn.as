package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import YJFY.Utils.ClearUtil;
   import flash.utils.ByteArray;
   
   public class DOWN_TheSocietyListOfApplyReturn extends InformationBodyDetail
   {
      
      protected var m_societyNum:int;
      
      protected var m_societyIds:Vector.<int>;
      
      public function DOWN_TheSocietyListOfApplyReturn()
      {
         super();
         m_informationBodyId = 3037;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_societyIds);
         m_societyIds = null;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.initFromByteArray(param1);
         m_societyNum = param1.readInt();
         if(m_societyNum)
         {
            m_societyIds = new Vector.<int>();
            _loc3_ = m_societyNum;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               _loc2_ = param1.readInt();
               m_societyIds.push(_loc2_);
               _loc4_++;
            }
         }
      }
      
      public function getSocietyNumOfApply() : int
      {
         return m_societyIds ? m_societyIds.length : 0;
      }
      
      public function getSocietyIdByIndex(param1:int) : int
      {
         return m_societyIds[param1];
      }
      
      public function decOneSocietyIdOfApply(param1:int) : void
      {
         if(m_societyIds == null)
         {
            return;
         }
         var _loc2_:int = int(m_societyIds.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_societyIds.splice(_loc2_,1);
            _loc2_ = int(m_societyIds.indexOf(param1));
         }
      }
      
      public function addOneSocietyIdOfApply(param1:int) : void
      {
         if(m_societyIds == null)
         {
            m_societyIds = new Vector.<int>();
         }
         m_societyIds.push(param1);
      }
      
      public function clearSocietyIdsOfApply() : void
      {
         ClearUtil.nullArr(m_societyIds,false,false,false);
      }
   }
}

