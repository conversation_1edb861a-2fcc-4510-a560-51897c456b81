package UI.AutomaticPetPanel
{
   import UI.DataManagerParent;
   import UI.Other.NeedEquipmentsData;
   import YJFY.Utils.ClearUtil;
   
   public class AutoPetUpgradePingJieData extends DataManagerParent
   {
      
      private var m_needEqupmentsData:NeedEquipmentsData;
      
      private var m_successRate:Number;
      
      public function AutoPetUpgradePingJieData()
      {
         super();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_needEqupmentsData);
         m_needEqupmentsData = null;
         super.clear();
      }
      
      public function setData(param1:NeedEquipmentsData, param2:Number) : void
      {
         ClearUtil.clearObject(m_needEqupmentsData);
         m_needEqupmentsData = param1;
         this.successRate = param2;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.successRate = m_successRate;
      }
      
      public function getSuccessRate() : Number
      {
         return successRate;
      }
      
      public function getNeedEquipmentsData() : NeedEquipmentsData
      {
         return m_needEqupmentsData;
      }
      
      private function get successRate() : Number
      {
         return _antiwear.successRate;
      }
      
      private function set successRate(param1:Number) : void
      {
         _antiwear.successRate = param1;
      }
   }
}

