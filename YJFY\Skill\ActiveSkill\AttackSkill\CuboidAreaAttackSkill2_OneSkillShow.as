package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class CuboidAreaAttackSkill2_OneSkillShow extends CuboidAreaAttackSkill2_OnlyPlayBody
   {
      
      protected var m_skillShowDefId:String;
      
      protected var m_skillShowAnimationEntity:AnimationEntityLogicShell;
      
      protected var m_skillShowAnimationPlayListener:AnimationPlayFrameLabelListener;
      
      public function CuboidAreaAttackSkill2_OneSkillShow()
      {
         super();
         m_skillShowAnimationEntity = new AnimationEntityLogicShell();
         m_skillShowAnimationEntity.init(m_attackPoint.getX(),m_attackPoint.getY(),m_attackPoint.getZ());
         m_skillShowAnimationPlayListener = new AnimationPlayFrameLabelListener();
         m_skillShowAnimationPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_skillShowAnimationEntity.getAniamtionShowPlay().addFrameLabelListener(m_skillShowAnimationPlayListener);
      }
      
      override public function clear() : void
      {
         m_skillShowDefId = null;
         ClearUtil.clearObject(m_skillShowAnimationEntity);
         m_skillShowAnimationEntity = null;
         ClearUtil.clearObject(m_skillShowAnimationPlayListener);
         m_skillShowAnimationPlayListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillShowDefId = String(param1.@skillShowDefId);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_skillShowAnimationEntity.setShow(m_owner.getAnimationByDefId(m_skillShowDefId));
         m_skillShowAnimationEntity.setNewPosition(m_attackPoint.getX(),m_attackPoint.getY(),m_attackPoint.getZ());
         m_world.addEntity(m_skillShowAnimationEntity);
         m_skillShowAnimationEntity.getAniamtionShowPlay().gotoAndPlay("1");
         (m_skillShowAnimationEntity.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
      }
      
      override protected function endSkill2() : void
      {
         m_world.removeEntity(m_skillShowAnimationEntity);
         super.endSkill2();
      }
      
      override public function showRender() : void
      {
         super.showRender();
      }
   }
}

