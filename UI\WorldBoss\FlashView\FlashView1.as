package UI.WorldBoss.FlashView
{
   import UI.WorldBoss.View;
   import com.greensock.TweenMax;
   import com.greensock.easing.Linear;
   import flash.geom.ColorTransform;
   
   public class FlashView1 extends FlashView
   {
      
      private var _intervalFlash1:uint;
      
      private var _tweenMax:TweenMax;
      
      private var _color:int;
      
      private var _tintAmount:Number;
      
      private var _appearDuration:Number;
      
      private var _disappearDuration:Number;
      
      public function FlashView1()
      {
         super();
      }
      
      override public function clear() : void
      {
         if(_tweenMax)
         {
            _tweenMax.kill();
            _tweenMax = null;
         }
         super.clear();
      }
      
      override public function flashView(param1:View) : void
      {
         var view:View = param1;
         var complete:* = function():void
         {
            _tweenMax = TweenMax.to(view.getShow(),_disappearDuration,{
               "colorTransform":{
                  "tint":_color,
                  "tintAmount":0
               },
               "ease":Linear.easeNone,
               "onComplete":complete2,
               "onCompleteParams":[]
            });
         };
         var complete2:* = function():void
         {
            _isFlash = false;
            recover();
            view.setIsFlash(false);
         };
         super.flashView(view);
         _isFlash = true;
         _isMyFlash = true;
         view.setIsFlash(true);
         if(_tweenMax)
         {
            _tweenMax.kill();
         }
         view.getShow().transform.colorTransform = new ColorTransform();
         _tweenMax = TweenMax.to(view.getShow(),_appearDuration,{
            "colorTransform":{
               "tint":_color,
               "tintAmount":_tintAmount
            },
            "ease":Linear.easeNone,
            "onComplete":complete,
            "onCompleteParams":[]
         });
      }
      
      public function setColor(param1:int) : void
      {
         _color = param1;
      }
      
      public function setTintAmount(param1:Number) : void
      {
         _tintAmount = param1;
      }
      
      public function setAppearDuration(param1:Number) : void
      {
         _appearDuration = param1;
      }
      
      public function setDisappearDuration(param1:Number) : void
      {
         _disappearDuration = param1;
      }
      
      override protected function recover() : void
      {
         if(_isMyFlash)
         {
            TweenMax.to(_view.getShow(),0,{
               "colorTransform":{
                  "tint":_color,
                  "tintAmount":0
               },
               "ease":Linear.easeNone
            });
         }
         super.recover();
      }
   }
}

