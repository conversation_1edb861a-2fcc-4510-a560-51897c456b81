package UI.ExternalUI
{
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Players.PlayerListener;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class ExternalPanel extends MySprite
   {
      
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _player1Panel:PlayerPanel;
      
      private var _player2Panel:PlayerPanel;
      
      private var _automaticPetPanel1:AutomaticPetPanel;
      
      private var _automaticPetPanel2:AutomaticPetPanel;
      
      private var _mountShow1:MountShow;
      
      private var _mountShow2:MountShow;
      
      private var _middlePanel:MiddlePanel;
      
      private var _buffPanel:BuffPanel;
      
      private var m_playerListener:PlayerListener;
      
      private var m_player1:Player;
      
      private var m_player2:Player;
      
      private var m_tX_mountShow_automaticPetPanel_1:Number;
      
      private var m_tX_mountShow_automaticPetPanel_2:Number;
      
      public function ExternalPanel()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
         m_playerListener = new PlayerListener();
         m_playerListener.changeDataFun = playerChangeData;
      }
      
      public function initPanel(param1:Player, param2:Player, param3:String) : void
      {
         m_player1 = param1;
         m_player2 = param2;
         m_player1.addPlayerListener(m_playerListener);
         if(m_player2)
         {
            m_player2.addPlayerListener(m_playerListener);
         }
         _show = MyFunction2.returnShowByClassName("ExternalPanel") as MovieClip;
         addChild(_show);
         _showMC = new MovieClipPlayLogicShell();
         _showMC.setShow(_show);
         if(param2)
         {
            _showMC.gotoAndStop("two");
         }
         if(!param1)
         {
            throw new Error("第一角色不能为空！");
         }
         _player1Panel = new PlayerPanel();
         _player1Panel.setShow(_show["playerPanel1"]);
         _player1Panel.initPanel();
         _player1Panel.player = param1;
         _automaticPetPanel1 = new AutomaticPetPanel();
         _automaticPetPanel1.setShow(_show["automaticPetPanel1"]);
         if(_show["mountShow1"])
         {
            _mountShow1 = new MountShow();
            _mountShow1.setShow(_show["mountShow1"]);
            mountShowShowOrNot(m_player1,_mountShow1);
            m_tX_mountShow_automaticPetPanel_1 = _mountShow1.getShow().x - _automaticPetPanel1.getShow().x;
         }
         automaticPetPanelShowOrNot(m_player1,_automaticPetPanel1);
         if(param2)
         {
            _player2Panel = new PlayerPanel();
            _player2Panel.setShow(_show["playerPanel2"]);
            _player2Panel.initPanel();
            _player2Panel.player = param2;
            _automaticPetPanel2 = new AutomaticPetPanel();
            _automaticPetPanel2.setShow(_show["automaticPetPanel2"]);
            if(_show["mountShow2"])
            {
               _mountShow2 = new MountShow();
               _mountShow2.setShow(_show["mountShow2"]);
               mountShowShowOrNot(m_player2,_mountShow2);
               m_tX_mountShow_automaticPetPanel_2 = _mountShow2.getShow().x - _automaticPetPanel2.getShow().x;
            }
            automaticPetPanelShowOrNot(m_player2,_automaticPetPanel2);
         }
         updateShow();
         _middlePanel = new MiddlePanel();
         _middlePanel.setShow(_show["middlePanel"]);
         _buffPanel = new BuffPanel();
         _buffPanel.x = 0;
         _buffPanel.y = 0;
         addChild(_buffPanel);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         _player1Panel.render(param1);
         if(_player2Panel)
         {
            _player2Panel.render(param1);
         }
         if(_middlePanel)
         {
            _middlePanel.render(param1.getGameTimeForThisInit());
         }
         if(Boolean(_automaticPetPanel1) && _automaticPetPanel1.getShow().parent)
         {
            _automaticPetPanel1.render(Part1.getInstance().getEnterFrameTime());
         }
         if(Boolean(_automaticPetPanel2) && _automaticPetPanel2.getShow().parent)
         {
            _automaticPetPanel2.render(Part1.getInstance().getEnterFrameTime());
         }
      }
      
      public function continueGame() : void
      {
         _player1Panel.continueGame();
         if(_player2Panel)
         {
            _player2Panel.continueGame();
         }
      }
      
      public function stopGame() : void
      {
         _player1Panel.stopGame();
         if(_player2Panel)
         {
            _player2Panel.stopGame();
         }
      }
      
      public function resetPlayerType() : void
      {
         _player1Panel.player = m_player1;
         if(m_player2)
         {
            _player2Panel.player = m_player2;
         }
      }
      
      public function get player1Panel() : PlayerPanel
      {
         return _player1Panel;
      }
      
      public function get player2Panel() : PlayerPanel
      {
         return _player2Panel;
      }
      
      public function get middlePanel() : MiddlePanel
      {
         return _middlePanel;
      }
      
      public function get buffPanel() : BuffPanel
      {
         return _buffPanel;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
            removeChildAt(0);
         }
         if(_player1Panel)
         {
            _player1Panel.clear();
         }
         if(_player2Panel)
         {
            _player2Panel.clear();
         }
         if(_middlePanel)
         {
            _middlePanel.clear();
         }
         if(_buffPanel)
         {
            _buffPanel.clear();
         }
         _player1Panel = null;
         _player2Panel = null;
         _middlePanel = null;
         _buffPanel = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         _show = null;
         ClearUtil.clearObject(_automaticPetPanel1);
         _automaticPetPanel1 = null;
         ClearUtil.clearObject(_automaticPetPanel2);
         _automaticPetPanel2 = null;
         ClearUtil.clearObject(_mountShow1);
         _mountShow1 = null;
         ClearUtil.clearObject(_mountShow2);
         _mountShow2 = null;
         if(m_player1)
         {
            m_player1.removePlayerListener(m_playerListener);
         }
         if(m_player2)
         {
            m_player2.removePlayerListener(m_playerListener);
         }
         ClearUtil.clearObject(m_playerListener);
         m_playerListener = null;
         m_player1 = null;
         m_player2 = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         if(!getChildByName(MessageBoxEngine.getInstance().name))
         {
            addChildAt(MessageBoxEngine.getInstance(),numChildren);
         }
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function refreshCountFastUseEquip() : void
      {
         if(_player1Panel)
         {
            _player1Panel.refreshFastUseEq();
         }
         if(_player2Panel)
         {
            _player2Panel.refreshFastUseEq();
         }
      }
      
      public function hideFastUseEq() : void
      {
         if(_player1Panel)
         {
            _player1Panel.HideFastUseEq();
         }
         if(_player2Panel)
         {
            _player2Panel.HideFastUseEq();
         }
      }
      
      private function playerChangeData(param1:Player) : void
      {
         if(param1 == m_player1)
         {
            automaticPetPanelShowOrNot(m_player1,_automaticPetPanel1);
            mountShowShowOrNot(m_player1,_mountShow1);
         }
         else
         {
            automaticPetPanelShowOrNot(m_player2,_automaticPetPanel2);
            mountShowShowOrNot(m_player2,_mountShow2);
         }
         updateShow();
      }
      
      private function automaticPetPanelShowOrNot(param1:Player, param2:AutomaticPetPanel) : void
      {
         if(param1.playerVO.automaticPetVO)
         {
            _show.addChild(param2.getShow());
            param2.setAutomaticPetVO(param1.playerVO.automaticPetVO,param1.playerVO.automaticBackPetVO);
         }
         else
         {
            param2.setAutomaticPetVO(null);
            if(param2.getShow().parent)
            {
               param2.getShow().parent.removeChild(param2.getShow());
            }
         }
      }
      
      private function mountShowShowOrNot(param1:Player, param2:MountShow) : void
      {
         if(param1.playerVO.mountVO)
         {
            _show.addChild(param2.getShow());
            param2.setMountVO(param1.playerVO.mountVO);
         }
         else
         {
            param2.setMountVO(null);
            if(param2.getShow().parent)
            {
               param2.getShow().parent.removeChild(param2.getShow());
            }
         }
      }
      
      private function updateShow() : void
      {
         if(Boolean(_mountShow1) && _mountShow1.getShow())
         {
            if(Boolean(_automaticPetPanel1) && Boolean(_automaticPetPanel1.getShow()) && _automaticPetPanel1.getShow().parent)
            {
               _mountShow1.getShow().x = _automaticPetPanel1.getShow().x + m_tX_mountShow_automaticPetPanel_1;
            }
            else
            {
               _mountShow1.getShow().x = _automaticPetPanel1.getShow().x;
            }
         }
         if(Boolean(_mountShow2) && _mountShow2.getShow())
         {
            if(Boolean(_automaticPetPanel2) && Boolean(_automaticPetPanel2.getShow()) && _automaticPetPanel2.getShow().parent)
            {
               _mountShow2.getShow().x = _automaticPetPanel2.getShow().x + m_tX_mountShow_automaticPetPanel_2;
            }
            else
            {
               _mountShow2.getShow().x = _automaticPetPanel2.getShow().x;
            }
         }
      }
   }
}

