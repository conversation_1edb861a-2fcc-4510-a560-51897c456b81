package UI.ConsumptionGuide
{
   import UI.EquipEquipmentFuns.EquipEquipmentFunction;
   import UI.EquipEquipmentFuns.IEquipEqListener;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MyFunction2;
   import UI.Players.Player;
   import YJFY.Utils.ClearUtil;
   
   public class GuideUseEquipment implements IEquipEqListener
   {
      
      private var _useEqIds:Vector.<String>;
      
      private var _player:Player;
      
      private var _useAfterFun:Function;
      
      private var _showWarningFun:Function;
      
      private var _WarningTextFontSize:int = 12;
      
      public function GuideUseEquipment()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_useEqIds);
         _useEqIds = null;
         _player = null;
         _useAfterFun = null;
      }
      
      public function setWarningTextFontSizse(param1:int) : void
      {
         _WarningTextFontSize = param1;
      }
      
      public function getWarningTextFontSize() : int
      {
         return _WarningTextFontSize;
      }
      
      public function setPlayer(param1:Player) : void
      {
         _player = param1;
      }
      
      public function setUseAfterFun(param1:Function) : void
      {
         _useAfterFun = param1;
      }
      
      public function setShowWarningFun(param1:Function) : void
      {
         _showWarningFun = param1;
      }
      
      public function addUseEqId(param1:String) : void
      {
         if(_useEqIds == null)
         {
            _useEqIds = new Vector.<String>();
         }
         _useEqIds.push(param1);
      }
      
      public function useEq() : Boolean
      {
         var _loc5_:int = 0;
         var _loc1_:String = null;
         var _loc2_:EquipmentVO = null;
         var _loc4_:EquipEquipmentFunction = null;
         if(_player == null)
         {
            return false;
         }
         var _loc3_:int = _useEqIds ? _useEqIds.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = _useEqIds[_loc5_];
            _loc2_ = MyFunction2.getOneEquipmentVOById(_loc1_,_player.playerVO.packageEquipmentVOs);
            if(_loc2_)
            {
               _loc4_ = new EquipEquipmentFunction();
               _loc4_.EquipEquipmentVOAction(_loc2_,_player,this);
               return true;
            }
            _loc5_++;
         }
         return false;
      }
      
      public function showWarning(param1:String, param2:int) : void
      {
         if(_showWarningFun != null)
         {
            _showWarningFun(param1,param2);
         }
      }
      
      public function actionAfterUse() : void
      {
         if(Boolean(_useAfterFun))
         {
            _useAfterFun();
         }
      }
      
      public function actionAfterUnableUse() : void
      {
      }
   }
}

