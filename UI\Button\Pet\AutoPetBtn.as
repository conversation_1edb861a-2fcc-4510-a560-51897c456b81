package UI.Button.Pet
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class AutoPetBtn extends SwitchBtn
   {
      
      public function AutoPetBtn()
      {
         super();
         setTipString("妖将");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickAutoPetBtn"));
      }
   }
}

