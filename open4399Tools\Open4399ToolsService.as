package open4399Tools
{
   import flash.display.Loader;
   import flash.display.LoaderInfo;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.utils.getTimer;
   import open4399Tools.events.Open4399ToolsEvent;
   
   public class Open4399ToolsService
   {
      
      private static var _open4399Tools:*;
      
      private static var v_url:String = "http://stat.api.4399.com/flash_ctrl_version.xml?ran=";
      
      private static var url:String = "http://cdn.comment.4399pk.com/control/open4399tools_AS3.swf";
      
      public function Open4399ToolsService()
      {
         super();
         var _loc2_:URLRequest = new URLRequest(v_url + getTimer());
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onXmlComplete);
         _loc1_.addEventListener("ioError",onXmlIOError);
         _loc1_.load(_loc2_);
      }
      
      protected function onXmlComplete(param1:Event) : void
      {
         param1.target.removeEventListener("ioError",onXmlIOError);
         param1.target.removeEventListener("complete",onXmlComplete);
         var _loc2_:XML = XML(param1.target.data);
         if(_loc2_ != null)
         {
            if(_loc2_.info.(hasOwnProperty("@resName") && @resName == "tools_as3").length)
            {
               url = _loc2_.info.(hasOwnProperty("@resName") && @resName == "tools_as3");
            }
         }
         onSwfLoad();
      }
      
      protected function onXmlIOError(param1:IOErrorEvent) : void
      {
         param1.target.removeEventListener("ioError",onXmlIOError);
         param1.target.removeEventListener("complete",onXmlComplete);
         onSwfLoad();
      }
      
      protected function onSwfLoad() : void
      {
         var _loc2_:URLRequest = new URLRequest(url);
         var _loc1_:Loader = new Loader();
         _loc1_.contentLoaderInfo.addEventListener("complete",onLoadComplete);
         _loc1_.contentLoaderInfo.addEventListener("ioError",onLoadError);
         _loc1_.contentLoaderInfo.addEventListener("securityError",onLoadError);
         try
         {
            _loc1_.load(_loc2_);
         }
         catch(e:Error)
         {
            trace(e.message);
         }
      }
      
      protected function onLoadError(param1:*) : void
      {
         param1.target.removeEventListener("complete",onLoadComplete);
         param1.target.removeEventListener("ioError",onLoadError);
         param1.target.removeEventListener("securityError",onLoadError);
         Open4399ToolsApi.getInstance().dispatchEvent(new Open4399ToolsEvent("check_bad_words_error"));
      }
      
      protected function onLoadComplete(param1:Event) : void
      {
         param1.target.removeEventListener("complete",onLoadComplete);
         param1.target.removeEventListener("ioError",onLoadError);
         param1.target.removeEventListener("securityError",onLoadError);
         var _loc2_:LoaderInfo = LoaderInfo(param1.target);
         _open4399Tools = _loc2_.content;
         _open4399Tools.eventClass = Open4399ToolsEvent;
         _open4399Tools.addEventListener("check_bad_words",Open4399ToolsApi.getInstance().dispatchEvent);
         _open4399Tools.addEventListener("check_bad_words_error",Open4399ToolsApi.getInstance().dispatchEvent);
         Open4399ToolsApi.getInstance().dispatchEvent(new Open4399ToolsEvent("init_service"));
      }
      
      public function get badWordsService() : *
      {
         if(_open4399Tools)
         {
            return _open4399Tools.badWordsService;
         }
         return null;
      }
   }
}

