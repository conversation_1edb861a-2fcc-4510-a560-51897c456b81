package UI.SocietySystem.SocietyShop
{
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MyFunction2;
   import UI.SocietySystem.MySocietyPanel.MySocietyPanel;
   import UI.SocietySystem.SocietyDataVO;
   import UI.SocietySystem.SocietySystem;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.HaveLockSwitchBtnLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.system.System;
   
   public class SocietyShop
   {
      
      private var m_dragLs:AbleDragSpriteLogicShell;
      
      private var m_levelShopSwitchBtns:Vector.<HaveLockSwitchBtnLogicShell>;
      
      private var m_levelShowSwitchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_shopColumes:Vector.<SocietyShopColume>;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_oneShopDatas:Vector.<OneShopDataInSocietyShop>;
      
      private var m_shopXML:XML;
      
      private var m_columeNumOnePage:int;
      
      private var m_show:MovieClip;
      
      private var m_currentOneShopData:OneShopDataInSocietyShop;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_mySocietyPanel:MySocietyPanel;
      
      private var m_societySystemXML:XML;
      
      private var m_societySystem:SocietySystem;
      
      private var m_societyDataVO:SocietyDataVO;
      
      public function SocietyShop()
      {
         super();
         m_oneShopDatas = new Vector.<OneShopDataInSocietyShop>();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
            m_show.removeEventListener("clickLockBtn",clickLockButton,true);
            ClearUtil.clearDisplayObjectInContainer(m_show);
            if(m_show.parent)
            {
               m_show.parent.removeChild(m_show);
            }
            m_show = null;
         }
         ClearUtil.clearObject(m_dragLs);
         m_dragLs = null;
         ClearUtil.clearObject(m_levelShopSwitchBtns);
         m_levelShopSwitchBtns = null;
         ClearUtil.clearObject(m_levelShowSwitchBtnGroup);
         m_levelShowSwitchBtnGroup = null;
         ClearUtil.clearObject(m_shopColumes);
         m_shopColumes = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_oneShopDatas);
         m_oneShopDatas = null;
         System.disposeXML(m_shopXML);
         m_shopXML = null;
         m_currentOneShopData = null;
         m_myLoader = null;
         m_mySocietyPanel = null;
         m_societySystemXML = null;
         m_societySystem = null;
         m_societyDataVO = null;
      }
      
      public function setMySocietyPanel(param1:MySocietyPanel) : void
      {
         m_mySocietyPanel = param1;
      }
      
      public function setSocietySystemXML(param1:XML) : void
      {
         m_societySystemXML = param1;
      }
      
      public function setSoceitySystem(param1:SocietySystem) : void
      {
         m_societySystem = param1;
      }
      
      public function setSocietyDataVO(param1:SocietyDataVO) : void
      {
         m_societyDataVO = param1;
      }
      
      public function setLoader(param1:YJFYLoader) : void
      {
         m_myLoader = param1;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_show.addEventListener("clickLockBtn",clickLockButton,true,0,true);
         initShow();
      }
      
      private function initShow() : void
      {
         m_myLoader.getXML("UIData/SocietySystem/societyShop.xml",getShopXMLSuccess,getFail);
         m_myLoader.load();
      }
      
      public function refresh() : void
      {
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOnePage);
      }
      
      private function initShow2() : void
      {
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:DisplayObject = null;
         var _loc2_:HaveLockSwitchBtnLogicShell = null;
         var _loc1_:SocietyShopColume = null;
         m_dragLs = new AbleDragSpriteLogicShell();
         m_dragLs.setShow(m_show);
         var _loc4_:int = m_show.numChildren;
         m_columeNumOnePage = 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = m_show.getChildAt(_loc6_);
            if(_loc5_.name.substr(0,"shopBtn".length) == "shopBtn")
            {
               _loc3_++;
            }
            if(_loc5_.name.substr(0,"shopColume".length) == "shopColume")
            {
               m_columeNumOnePage++;
            }
            _loc6_++;
         }
         m_levelShopSwitchBtns = new Vector.<HaveLockSwitchBtnLogicShell>();
         m_levelShowSwitchBtnGroup = new SwitchBtnGroupLogicShell(HaveLockSwitchBtnLogicShell);
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc2_ = new HaveLockSwitchBtnLogicShell();
            _loc2_.setShow(m_show["shopBtn" + (_loc6_ + 1)]);
            _loc2_.setTipString(_loc6_ + 1 + "级商店");
            _loc2_.lock();
            m_levelShopSwitchBtns.push(_loc2_);
            m_levelShowSwitchBtnGroup.addSwitchBtn(_loc2_);
            _loc6_++;
         }
         m_shopColumes = new Vector.<SocietyShopColume>();
         _loc6_ = 0;
         while(_loc6_ < m_columeNumOnePage)
         {
            _loc1_ = new SocietyShopColume();
            _loc1_.setShow(m_show["shopColume" + (_loc6_ + 1)]);
            _loc1_.setTipString("点击购买该物品");
            m_shopColumes.push(_loc1_);
            _loc6_++;
         }
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
         m_quitBtn = new ButtonLogicShell2();
         m_quitBtn.setShow(m_show["quitBtn2"]);
         m_quitBtn.setTipString("点击关闭");
         initShow3();
      }
      
      private function initShow3() : void
      {
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc5_:int = 0;
            var _loc4_:OneShopDataInSocietyShop = null;
            var _loc3_:XMLList = m_shopXML.SocietyShop;
            var _loc2_:int = int(_loc3_ ? _loc3_.length() : 0);
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc4_ = new OneShopDataInSocietyShop();
               _loc4_.initFromXML(_loc3_[_loc5_],param1);
               m_levelShopSwitchBtns[_loc5_].data = _loc4_;
               m_oneShopDatas.push(_loc4_);
               _loc5_++;
            }
            m_societyDataVO.getSocietyShopVO().isReSetAndReSetData(param1,m_shopXML.@resetNeedDay);
            _loc2_ = int(m_levelShopSwitchBtns.length);
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc4_ = m_levelShopSwitchBtns[_loc5_].data as OneShopDataInSocietyShop;
               if(m_societyDataVO.getSocietyLevel() >= _loc4_.getNeedMinSocietyLevel() && m_societyDataVO.getSocietyLevel() <= _loc4_.getNeedMaxSocietyLevel())
               {
                  m_levelShopSwitchBtns[_loc5_].unLock();
               }
               _loc5_++;
            }
            m_levelShowSwitchBtnGroup.addEnd();
            m_mySocietyPanel.addChild(m_show);
         },m_mySocietyPanel.showWarningBox,true);
      }
      
      private function setPageBtn(param1:int, param2:int) : void
      {
         if(param2 == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(param2 % m_columeNumOnePage == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,param2 / m_columeNumOnePage);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(param2 / m_columeNumOnePage) + 1);
         }
      }
      
      private function arrangeColume(param1:int) : void
      {
         var _loc8_:* = 0;
         var _loc5_:int = 0;
         var _loc2_:SocietyShopColume = null;
         var _loc3_:GoodData = null;
         var _loc6_:OneEqSaveDataInSocietyShop = null;
         var _loc7_:int = param1 + m_columeNumOnePage;
         var _loc4_:int = m_currentOneShopData ? m_currentOneShopData.getGoodDataNum() : 0;
         _loc8_ = param1;
         while(_loc8_ < _loc7_ && _loc8_ < _loc4_)
         {
            _loc2_ = m_shopColumes[_loc5_];
            _loc2_.getShow().visible = true;
            _loc3_ = m_currentOneShopData.getGoodDataByIndex(_loc8_);
            _loc6_ = m_societyDataVO.getSocietyShopVO().getOneShopDataByShopLevel(m_currentOneShopData.getShopLevel()).getBuyEqDataByEqId(_loc3_.getEquipmentVO().id.toString());
            _loc2_.setData(_loc3_,_loc6_);
            _loc5_++;
            _loc8_++;
         }
         while(_loc5_ < m_columeNumOnePage)
         {
            m_shopColumes[_loc5_].getShow().visible = false;
            _loc5_++;
         }
      }
      
      private function getShopXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_shopXML = param1.resultXML;
         initShow2();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc5_:int = 0;
         var _loc4_:GoodData = null;
         var _loc2_:OneEqSaveDataInSocietyShop = null;
         var _loc6_:* = param1.button;
         if(m_pageBtnGroup !== _loc6_)
         {
            var _loc3_:int = int(m_shopColumes.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               if(param1.button == m_shopColumes[_loc5_])
               {
                  _loc4_ = m_shopColumes[_loc5_].getGoodData();
                  _loc2_ = m_shopColumes[_loc5_].getOnebuyEqData();
                  if(isCanBuy(_loc4_,_loc2_) && m_currentOneShopData)
                  {
                     m_mySocietyPanel.openSocietyShopBuyPopUpBox(_loc4_,_loc2_,m_currentOneShopData.getShopLevel());
                  }
                  else
                  {
                     m_mySocietyPanel.showWarningBox("剩余数量为0， 您现在不能购买该物品",0);
                  }
                  return;
               }
               _loc5_++;
            }
            _loc3_ = int(m_levelShopSwitchBtns.length);
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               if(param1.button == m_levelShopSwitchBtns[_loc5_])
               {
                  m_currentOneShopData = m_oneShopDatas[_loc5_];
                  setPageBtn(1,m_currentOneShopData ? m_currentOneShopData.getGoodDataNum() : 0);
                  arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOnePage);
                  return;
               }
               _loc5_++;
            }
            return;
         }
         arrangeColume((m_pageBtnGroup.pageNum - 1) * m_columeNumOnePage);
      }
      
      private function clickLockButton(param1:ButtonEvent) : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:OneShopDataInSocietyShop = null;
         var _loc2_:String = null;
         _loc3_ = int(m_levelShopSwitchBtns.length);
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            if(param1.button == m_levelShopSwitchBtns[_loc6_])
            {
               _loc5_ = m_levelShopSwitchBtns[_loc6_].data as OneShopDataInSocietyShop;
               _loc2_ = "";
               _loc4_ = _loc5_.getNeedMinSocietyLevel();
               while(_loc4_ <= _loc5_.getNeedMaxSocietyLevel())
               {
                  _loc2_ += "" + _loc4_;
                  if(_loc4_ != _loc5_.getNeedMaxSocietyLevel())
                  {
                     _loc2_ += "，";
                  }
                  _loc4_++;
               }
               m_mySocietyPanel.showWarningBox("帮会等级" + _loc2_ + "级开启该商店.",0);
               return;
            }
            _loc6_++;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("加载失败");
      }
      
      private function isCanBuy(param1:GoodData, param2:OneEqSaveDataInSocietyShop) : Boolean
      {
         if(param2 == null)
         {
            return true;
         }
         return param1.getMaxNum() - param2.getNum() <= 0 ? false : true;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function getQuitBtn() : ButtonLogicShell2
      {
         return m_quitBtn;
      }
   }
}

