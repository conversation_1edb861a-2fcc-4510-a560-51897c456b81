package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_ApplyJoinReturn extends InformationBodyDetail
   {
      
      public const const_applyJoinSuccess:int = 1;
      
      public const const_fail_lackTicketPoint:int = 2;
      
      public const const_society_not_exist:int = 3;
      
      public const const_societyApplyFull:int = 4;
      
      public const const_other_fail:int = 5;
      
      protected var m_data:int;
      
      protected var m_societyId:int;
      
      public function DOWN_ApplyJoinReturn()
      {
         super();
         m_informationBodyId = 3013;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_data = param1.readInt();
         if(m_data == 1)
         {
            m_societyId = param1.readInt();
         }
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getData() : int
      {
         return m_data;
      }
   }
}

