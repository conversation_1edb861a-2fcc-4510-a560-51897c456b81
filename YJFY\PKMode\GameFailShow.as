package YJFY.PKMode
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFunction2;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GameFailShow
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:AnimationShowPlayLogicShell;
      
      private var m_returnCityBtn:ButtonLogicShell;
      
      private var m_upEquipBtn:ButtonLogicShell2;
      
      private var m_stopListener:StopListener;
      
      public function GameFailShow()
      {
         super();
         m_show = MyFunction2.returnShowByClassName("ChallengeFailUI") as MovieClip;
         m_showMC = new AnimationShowPlayLogicShell();
         m_showMC.setShow(m_show);
         m_returnCityBtn = new ButtonLogicShell();
         m_upEquipBtn = new ButtonLogicShell2();
         m_stopListener = new StopListener();
         m_stopListener.stopFun = stop;
         m_showMC.addNextStopListener(m_stopListener);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
         m_showMC.gotoAndPlay("1");
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_returnCityBtn);
         m_returnCityBtn = null;
         ClearUtil.clearObject(m_upEquipBtn);
         m_upEquipBtn = null;
         ClearUtil.clearObject(m_stopListener);
         m_stopListener = null;
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function stop() : void
      {
         m_returnCityBtn.setShow(m_show["failBackBtn"]);
         m_upEquipBtn.setShow(m_show["upequip"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_returnCityBtn:
               Part1.getInstance().returnCity();
               break;
            case m_upEquipBtn:
               Part1.getInstance().returnCity();
               gotoUpEquip();
         }
      }
      
      private function gotoUpEquip() : void
      {
         var _loc1_:int = 0;
         switch(GamingUI.getInstance().player1.playerVO.name)
         {
            case "SunWuKong":
               _loc1_ = 0;
               break;
            case "BaiLongMa":
               _loc1_ = 1;
               break;
            case "ErLangShen":
               _loc1_ = 2;
               break;
            case "ChangE":
               _loc1_ = 3;
               break;
            case "Fox":
               _loc1_ = 4;
               break;
            case "TieShan":
               _loc1_ = 5;
               break;
            case "Houyi":
               _loc1_ = 6;
               break;
            case "ZiXia":
               _loc1_ = 6;
         }
         GamingUI.getInstance().enterToNewShop(0,_loc1_);
      }
   }
}

