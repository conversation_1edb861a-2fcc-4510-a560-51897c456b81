package UI.XiangMoLevelPanel
{
   import UI.DataManagerParent;
   
   public class XiangMoLevelSaveOneData extends DataManagerParent
   {
      
      private var m_fuBenId:String;
      
      private var m_challengeNum:uint;
      
      public function XiangMoLevelSaveOneData()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_fuBenId = null;
         super.clear();
      }
      
      public function initByData(param1:String, param2:uint) : void
      {
         this.fuBenId = param1;
         this.challengeNum = param2;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         fuBenId = String(param1.@id);
         challengeNum = uint(param1.@n);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <one />;
         _loc1_.@id = fuBenId;
         _loc1_.@n = challengeNum;
         return _loc1_;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.fuBenId = m_fuBenId;
         _antiwear.challengeNum = m_challengeNum;
      }
      
      public function getChallengeNum() : uint
      {
         return challengeNum;
      }
      
      public function getFuBenId() : String
      {
         return fuBenId;
      }
      
      public function addOneChanllengeNum() : void
      {
         ++challengeNum;
      }
      
      public function resetData() : void
      {
         challengeNum = 0;
      }
      
      private function get fuBenId() : String
      {
         return _antiwear.fuBenId;
      }
      
      private function set fuBenId(param1:String) : void
      {
         _antiwear.fuBenId = param1;
      }
      
      private function get challengeNum() : uint
      {
         return _antiwear.challengeNum;
      }
      
      private function set challengeNum(param1:uint) : void
      {
         _antiwear.challengeNum = param1;
      }
   }
}

