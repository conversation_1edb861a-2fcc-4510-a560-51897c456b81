package UI.WarningBox
{
   import UI.Event.UIPassiveEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class ShowWarningBoxLogic
   {
      
      private var m_parent:Sprite;
      
      public function ShowWarningBoxLogic()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_parent)
         {
            m_parent.removeEventListener("warningBox",sureOrCancel,true);
         }
         m_parent = null;
      }
      
      public function init(param1:Sprite) : void
      {
         if(m_parent)
         {
            m_parent.removeEventListener("warningBox",sureOrCancel,true);
         }
         m_parent = param1;
         if(m_parent)
         {
            m_parent.addEventListener("warningBox",sureOrCancel,true,0,true);
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_parent.stage);
         m_parent.addChildAt(WarningBoxSingle.getInstance(),m_parent.numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
   }
}

