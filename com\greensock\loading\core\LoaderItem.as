package com.greensock.loading.core
{
   import com.greensock.events.LoaderEvent;
   import flash.events.Event;
   import flash.events.ProgressEvent;
   import flash.net.URLRequest;
   import flash.net.URLStream;
   import flash.net.URLVariables;
   
   public class LoaderItem extends LoaderCore
   {
      
      protected static var _cacheID:Number = new Date().getTime();
      
      protected var _url:String;
      
      protected var _request:URLRequest;
      
      protected var _scriptAccessDenied:Boolean;
      
      protected var _auditStream:URLStream;
      
      protected var _preferEstimatedBytesInAudit:Boolean;
      
      protected var _httpStatus:int;
      
      protected var _skipAlternateURL:Boolean;
      
      public function LoaderItem(param1:*, param2:Object = null)
      {
         super(param2);
         _request = param1 is URLRequest ? param1 as URLRequest : new URLRequest(param1);
         _url = _request.url;
         _setRequestURL(_request,_url);
      }
      
      protected function _prepRequest() : void
      {
         _scriptAccessDenied = false;
         _httpStatus = 0;
         _closeStream();
         if(this.vars.noCache && (!_isLocal || _url.substr(0,4) == "http"))
         {
            _setRequestURL(_request,_url,"gsCacheBusterID=" + _cacheID++);
         }
      }
      
      protected function _setRequestURL(param1:URLRequest, param2:String, param3:String = "") : void
      {
         var _loc9_:int = 0;
         var _loc7_:URLVariables = null;
         var _loc5_:Array = null;
         var _loc6_:Array = param2.split("?");
         var _loc4_:String = _loc6_[0];
         var _loc8_:String = "";
         _loc9_ = 0;
         while(_loc9_ < _loc4_.length)
         {
            _loc8_ += _loc4_.charAt(_loc9_);
            _loc9_++;
         }
         param1.url = _loc8_;
         if(_loc6_.length == 2)
         {
            param3 += param3 == "" ? _loc6_[1] : "&" + _loc6_[1];
         }
         if(param3 != "")
         {
            _loc7_ = param1.data == null ? new URLVariables() : param1.data as URLVariables;
            _loc6_ = param3.split("&");
            _loc9_ = int(_loc6_.length);
            while(true)
            {
               _loc9_--;
               if(_loc9_ <= -1)
               {
                  break;
               }
               _loc5_ = _loc6_[_loc9_].split("=");
               _loc7_[_loc5_[0]] = _loc5_[1];
            }
            param1.data = _loc7_;
         }
      }
      
      override protected function _dump(param1:int = 0, param2:int = 0, param3:Boolean = false) : void
      {
         _closeStream();
         super._dump(param1,param2,param3);
      }
      
      override public function auditSize() : void
      {
         var _loc1_:URLRequest = null;
         if(_auditStream == null)
         {
            _auditStream = new URLStream();
            _auditStream.addEventListener("progress",_auditStreamHandler,false,0,true);
            _auditStream.addEventListener("complete",_auditStreamHandler,false,0,true);
            _auditStream.addEventListener("ioError",_auditStreamHandler,false,0,true);
            _auditStream.addEventListener("securityError",_auditStreamHandler,false,0,true);
            _loc1_ = new URLRequest();
            _loc1_.data = _request.data;
            _setRequestURL(_loc1_,_url,!_isLocal || _url.substr(0,4) == "http" ? "gsCacheBusterID=" + _cacheID++ + "&purpose=audit" : "");
            _auditStream.load(_loc1_);
         }
      }
      
      protected function _closeStream() : void
      {
         if(_auditStream != null)
         {
            _auditStream.removeEventListener("progress",_auditStreamHandler);
            _auditStream.removeEventListener("complete",_auditStreamHandler);
            _auditStream.removeEventListener("ioError",_auditStreamHandler);
            _auditStream.removeEventListener("securityError",_auditStreamHandler);
            try
            {
               _auditStream.close();
            }
            catch(error:Error)
            {
            }
            _auditStream = null;
         }
      }
      
      protected function _auditStreamHandler(param1:Event) : void
      {
         var _loc2_:URLRequest = null;
         if(param1 is ProgressEvent)
         {
            _cachedBytesTotal = (param1 as ProgressEvent).bytesTotal;
            if(_preferEstimatedBytesInAudit && uint(this.vars.estimatedBytes) > _cachedBytesTotal)
            {
               _cachedBytesTotal = uint(this.vars.estimatedBytes);
            }
         }
         else if(param1.type == "ioError" || param1.type == "securityError")
         {
            if(this.vars.alternateURL != undefined && this.vars.alternateURL != "" && this.vars.alternateURL != _url)
            {
               _url = this.vars.alternateURL;
               _setRequestURL(_request,_url);
               _loc2_ = new URLRequest();
               _loc2_.data = _request.data;
               _setRequestURL(_loc2_,_url,!_isLocal || _url.substr(0,4) == "http" ? "gsCacheBusterID=" + _cacheID++ + "&purpose=audit" : "");
               _auditStream.load(_loc2_);
               _errorHandler(param1);
               return;
            }
            super._failHandler(param1);
         }
         _auditedSize = true;
         _closeStream();
         dispatchEvent(new Event("auditedSize"));
      }
      
      override protected function _failHandler(param1:Event) : void
      {
         if(this.vars.alternateURL != undefined && this.vars.alternateURL != "" && !_skipAlternateURL)
         {
            _skipAlternateURL = true;
            _url = "temp" + Math.random();
            this.url = this.vars.alternateURL;
            _errorHandler(param1);
         }
         else
         {
            super._failHandler(param1);
         }
      }
      
      protected function _httpStatusHandler(param1:Event) : void
      {
         _httpStatus = (param1 as Object).status;
         dispatchEvent(new LoaderEvent("httpStatus",this));
      }
      
      public function get url() : String
      {
         return _url;
      }
      
      public function set url(param1:String) : void
      {
         var _loc2_:* = false;
         if(_url != param1)
         {
            _url = param1;
            _setRequestURL(_request,_url);
            _loc2_ = _status == 1;
            _dump(0,0,true);
            if(_loc2_)
            {
               _load();
            }
         }
      }
      
      public function get request() : URLRequest
      {
         return _request;
      }
      
      public function get httpStatus() : int
      {
         return _httpStatus;
      }
      
      public function get scriptAccessDenied() : Boolean
      {
         return _scriptAccessDenied;
      }
   }
}

