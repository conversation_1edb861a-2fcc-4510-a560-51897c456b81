package UI.DetectionClass
{
   import GM_UI.GMData;
   import UI.CheatData.CheatData;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.DanMedicineEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.FashionEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.PKUI.PlayerDataForPK;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import UI2.APIDataAnalyze.APIDataAnalyze;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.GameData;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class DetectionClass
   {
      
      private static var _instance:DetectionClass = null;
      
      public var accelerateNum:int;
      
      private var _isStartRequestTime:Boolean;
      
      private var _startTime:String;
      
      private var _endTime:String;
      
      private var _requestTimeIntervalTime:int;
      
      private var _interval1:uint;
      
      private var _interval2:uint;
      
      private var _vipPetArr:Array = [10400124,10400224,10400324,10400425];
      
      private var _lastCheckTime:int;
      
      private var _isCheckP1Storage:Boolean;
      
      private var _isCheckP2Storage:Boolean;
      
      private var _isCheckPublicStorage:Boolean;
      
      public function DetectionClass()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗！？");
      }
      
      public static function getInstance() : DetectionClass
      {
         if(!_instance)
         {
            _instance = new DetectionClass();
         }
         return _instance;
      }
      
      public function detectionVersion() : Boolean
      {
         if(Boolean(GameData.getInstance().getLastSaveData()) && GameData.getInstance().getLastSaveData().getVersion() != 9311 && Boolean(GameData.getInstance().getLastSaveData().getVersion()) && GMData.getInstance().isGMApplication == false)
         {
            if(ProgramStartData.getInstance().getVersion() < GameData.getInstance().getLastSaveData().getVersion())
            {
               return true;
            }
         }
         return false;
      }
      
      public function detectionUidAndIdx() : Boolean
      {
         if(Boolean(GameData.getInstance().getLastSaveData()) && (GameData.getInstance().getLastSaveData().getUid() && Boolean(GameData.getInstance().getLastSaveData().getIdx())))
         {
            trace(GameData.getInstance().getLoginReturnData().getUid(),GameData.getInstance().getLastSaveData().getUid(),GameData.getInstance().getSaveFileData().index,GameData.getInstance().getLastSaveData().getIdx());
            if(GameData.getInstance().getLoginReturnData().getUid() != GameData.getInstance().getLastSaveData().getUid() || GameData.getInstance().getSaveFileData().index != GameData.getInstance().getLastSaveData().getIdx())
            {
               return true;
            }
         }
         return false;
      }
      
      public function getXMLByType(param1:String) : XML
      {
         var _loc3_:int = 0;
         var _loc2_:XMLList = XMLSingle.getInstance().dataXML.detection[0].type;
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length())
         {
            if(_loc2_[_loc3_].@value == param1)
            {
               return _loc2_[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function detectionVersionMatchTime() : void
      {
         var _loc1_:SaveFileData = GameData.getInstance().getSaveFileData();
         if(Boolean(GameData.getInstance().getLastSaveData()) && Boolean(GameData.getInstance().getLastSaveData().getVersion()) && GameData.getInstance().getLastSaveData().getVersion() < 1072 && GMData.getInstance().isGMApplication == false)
         {
            if(_loc1_.index == GameData.getInstance().getLastSaveData().getIdx() && new TimeUtil().timeInterval(_loc1_.dateAndTime,"2017-10-30 00:00:00") <= 0)
            {
               Part1.getInstance().openCheatGamePanel();
               return;
            }
         }
      }
      
      public function detectionAllPlayer1(param1:Player, param2:Player, param3:Vector.<EquipmentVO>) : void
      {
         var _loc5_:* = undefined;
         var _loc4_:* = undefined;
         if(!GMData.getInstance().isGMApplication)
         {
            if(!(!_lastCheckTime || GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() - _lastCheckTime > 3600000))
            {
               if(GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() < _lastCheckTime)
               {
                  _lastCheckTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
               }
               if(!_isCheckP1Storage && !param1.playerVO.storageEquipmentXml)
               {
                  detectionEquipmentVos(param1.playerVO.storageEquipmentVOs);
                  _isCheckP1Storage = true;
               }
               if(!_isCheckP2Storage && param2 && !param2.playerVO.storageEquipmentXml)
               {
                  detectionEquipmentVos(param2.playerVO.storageEquipmentVOs);
                  _isCheckP2Storage = true;
               }
               if(!_isCheckPublicStorage && param3)
               {
                  detectionEquipmentVos(param3);
                  _isCheckPublicStorage = true;
               }
               return;
            }
            _lastCheckTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit();
         }
         _loc5_ = param1.playerVO.inforEquipmentVOs.slice();
         _loc4_ = _loc5_.concat(param1.playerVO.packageEquipmentVOs);
         ClearUtil.nullArr(_loc5_,false,false,false);
         _loc5_ = _loc4_;
         if(!param1.playerVO.storageEquipmentXml)
         {
            _loc4_ = _loc5_.concat(param1.playerVO.storageEquipmentVOs);
            ClearUtil.nullArr(_loc5_,false,false,false);
            _loc5_ = _loc4_;
            _isCheckP1Storage = true;
         }
         detectionDan(param1);
         if(param2)
         {
            _loc4_ = _loc5_.concat(param2.playerVO.inforEquipmentVOs);
            ClearUtil.nullArr(_loc5_,false,false,false);
            _loc5_ = _loc4_;
            _loc4_ = _loc5_.concat(param2.playerVO.packageEquipmentVOs);
            ClearUtil.nullArr(_loc5_,false,false,false);
            _loc5_ = _loc4_;
            if(!param2.playerVO.storageEquipmentXml)
            {
               _loc4_ = _loc5_.concat(param2.playerVO.storageEquipmentVOs);
               ClearUtil.nullArr(_loc5_,false,false,false);
               _loc5_ = _loc4_;
               _isCheckP2Storage = true;
            }
            detectionDan(param2);
         }
         if(param3)
         {
            _loc4_ = _loc5_.concat(param3);
            ClearUtil.nullArr(_loc5_,false,false,false);
            _loc5_ = _loc4_;
            _isCheckPublicStorage = true;
         }
         DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(_loc5_);
         detectionAllPlayer2(param1,param3);
         if(param2)
         {
            detectionAllPlayer2(param2,null);
         }
      }
      
      public function detectionDan(param1:Player) : void
      {
         var _loc2_:* = null;
         var _loc3_:int = 0;
         if(param1.playerVO.attackDanMedicineEquipmentVOGrid)
         {
            _loc3_ = 0;
            while(_loc3_ < param1.playerVO.attackDanMedicineEquipmentVOGrid.length)
            {
               for each(_loc2_ in param1.playerVO.attackDanMedicineEquipmentVOGrid[_loc3_])
               {
                  if(!_loc2_)
                  {
                     break;
                  }
                  if(_loc2_.enablePutLine - _loc3_ != 1)
                  {
                     CheatData.getInstance().addCheatDataStr("丹药等级不对应！");
                     break;
                  }
               }
               _loc3_++;
            }
         }
         if(param1.playerVO.defenceDanMedicineEquipmentVOGrid)
         {
            _loc3_ = 0;
            while(_loc3_ < param1.playerVO.defenceDanMedicineEquipmentVOGrid.length)
            {
               for each(_loc2_ in param1.playerVO.defenceDanMedicineEquipmentVOGrid[_loc3_])
               {
                  if(!_loc2_)
                  {
                     break;
                  }
                  if(_loc2_.enablePutLine - _loc3_ != 1)
                  {
                     CheatData.getInstance().addCheatDataStr("丹药等级不对应！");
                     break;
                  }
               }
               _loc3_++;
            }
         }
      }
      
      public function detectionEquipmentVos(param1:Vector.<EquipmentVO>) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1)
         {
            _loc2_ = int(param1.length);
            DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param1);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(param1[_loc3_])
               {
                  detectionEquipmentVO(param1[_loc3_]);
               }
               _loc3_++;
            }
         }
      }
      
      private function detectionTopEquip(param1:EquipmentVO, param2:int) : void
      {
         var _loc3_:Number = NaN;
         if((param1 as AbleEquipmentVO).implictProPlayerId == "ChuanShuo")
         {
            if(!GMData.getInstance().isGMApplication && Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine" && GamingUI.getInstance().player1)
            {
               if(!GamingUI.getInstance().player1.vipVO.vipLevel && !APIDataAnalyze.getInstance().payMoney)
               {
                  _loc3_ = CurrentTicketPointManager.getInstance().getTotalTicketPoint();
                  if(!isNaN(_loc3_))
                  {
                     CheatData.getInstance().addCheatDataStr("装备不应拥有！");
                  }
                  return;
               }
               if(!isNaN(_loc3_) && _loc3_ >= 0 && _loc3_ < param2)
               {
                  CheatData.getInstance().addCheatDataStr("装备不应拥有！");
               }
            }
         }
      }
      
      public function detectionAllPlayer2(param1:Player, param2:Vector.<EquipmentVO>) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Number = NaN;
         detectionPlayerVO(param1.playerVO);
         detectionPKData();
         if(param1.playerVO.pet && param1.playerVO.pet.petEquipmentVO)
         {
            detectionEquipmentVO(param1.playerVO.pet.petEquipmentVO);
         }
         _loc4_ = int(param1.playerVO.inforEquipmentVOs.length);
         DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param1.playerVO.inforEquipmentVOs);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(param1.playerVO.inforEquipmentVOs[_loc5_])
            {
               detectionEquipmentVO(param1.playerVO.inforEquipmentVOs[_loc5_]);
            }
            if(param1.playerVO.inforEquipmentVOs[_loc5_] is AbleEquipmentVO && (param1.playerVO.inforEquipmentVOs[_loc5_] as AbleEquipmentVO).implictProPlayerId == "ChuanShuo")
            {
               if(!GMData.getInstance().isGMApplication && Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "onLine" && GamingUI.getInstance().player1)
               {
                  if(!GamingUI.getInstance().player1.vipVO.vipLevel && !APIDataAnalyze.getInstance().payMoney)
                  {
                     _loc3_ = CurrentTicketPointManager.getInstance().getTotalTicketPoint();
                     if(!isNaN(_loc3_) && _loc3_ == 0)
                     {
                        CheatData.getInstance().addCheatDataStr("装备不应拥有！");
                     }
                  }
               }
            }
            _loc5_++;
         }
         _loc4_ = int(param1.playerVO.packageEquipmentVOs.length);
         DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param1.playerVO.packageEquipmentVOs);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(param1.playerVO.packageEquipmentVOs[_loc5_])
            {
               detectionEquipmentVO(param1.playerVO.packageEquipmentVOs[_loc5_]);
            }
            _loc5_++;
         }
         if(!param1.playerVO.storageEquipmentXml)
         {
            _loc4_ = int(param1.playerVO.storageEquipmentVOs.length);
            DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param1.playerVO.storageEquipmentVOs);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(param1.playerVO.storageEquipmentVOs[_loc5_])
               {
                  detectionEquipmentVO(param1.playerVO.storageEquipmentVOs[_loc5_]);
               }
               _loc5_++;
            }
         }
         if(param2)
         {
            _loc4_ = int(param2.length);
            DetectionClass2.getInstance().detectionEquipmentVOsQuoteIsSame(param2);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(param2[_loc5_])
               {
                  detectionEquipmentVO(param2[_loc5_]);
               }
               _loc5_++;
            }
         }
      }
      
      public function detectionPlayerVO(param1:PlayerVO) : void
      {
         if(param1.money > XMLSingle.getInstance().maxMoney)
         {
            GamingUI.getInstance().closeInternalPanel();
            CheatData.getInstance().addCheatDataStr("游戏金钱出现错误！");
            throw new Error("游戏金钱出现错误！");
         }
      }
      
      public function detectionPKData() : void
      {
         if(PlayerDataForPK.getInstance().pkPoint > XMLSingle.getInstance().maxPKPoint)
         {
            GamingUI.getInstance().closeInternalPanel();
            CheatData.getInstance().addCheatDataStr("pk点出现错误！");
            trace("pk点出现错误！");
            throw new Error("pk点出现错误！");
         }
         if(!MyFunction2.judgmentValueInNormal(PlayerDataForPK.getInstance().failMatch + PlayerDataForPK.getInstance().winMatch,PlayerDataForPK.getInstance().allMatch,5))
         {
            GamingUI.getInstance().closeInternalPanel();
            CheatData.getInstance().addCheatDataStr("场次出现了错误！");
            throw new Error("场次出现了错误！");
         }
         trace(PlayerDataForPK.getInstance().failMatchForTwoPlayer);
         trace(PlayerDataForPK.getInstance().winMatchForTwoPlayer);
         trace(PlayerDataForPK.getInstance().allMatchForTwoPlayer);
         if(!MyFunction2.judgmentValueInNormal(PlayerDataForPK.getInstance().failMatchForTwoPlayer + PlayerDataForPK.getInstance().winMatchForTwoPlayer,PlayerDataForPK.getInstance().allMatchForTwoPlayer,5))
         {
            GamingUI.getInstance().closeInternalPanel();
            CheatData.getInstance().addCheatDataStr("pk2场次出现了错误！");
            throw new Error("pk2场次出现了错误！");
         }
      }
      
      public function addEquipmentVOFix(param1:EquipmentVO) : void
      {
         if(param1 && !param1.prefix && param1.id && param1.id > 100)
         {
            param1.prefix = param1.id.toString().substr(0,2);
            param1.suffix = param1.id.toString().substr(2);
         }
      }
      
      public function detectionEquipmentVO(param1:EquipmentVO) : void
      {
         var _loc11_:int = 0;
         var _loc8_:int = 0;
         var _loc17_:int = 0;
         var _loc20_:Boolean = false;
         var _loc14_:XMLList = null;
         var _loc16_:Array = null;
         var _loc6_:* = null;
         var _loc2_:PreciousEquipmentVO = null;
         var _loc3_:Boolean = false;
         var _loc7_:int = 0;
         var _loc5_:* = null;
         var _loc9_:* = null;
         var _loc12_:ClothesEquipmentVO = null;
         var _loc13_:WeaponEquipmentVO = null;
         var _loc15_:NecklaceEquipmentVO = null;
         var _loc19_:GourdEquipmentVO = null;
         var _loc18_:FashionEquipmentVO = null;
         var _loc10_:ForeverFashionEquipmentVO = null;
         var _loc4_:PetEquipmentVO = null;
         if(param1 is AbleEquipmentVO && (param1 as AbleEquipmentVO).maxHoleNum > 0)
         {
            if((param1 as AbleEquipmentVO).getHoleNum() > (param1 as AbleEquipmentVO).maxHoleNum)
            {
               GamingUI.getInstance().closeInternalPanel();
               CheatData.getInstance().addCheatDataStr("开孔数超出范围！");
               throw new Error("开孔数超出范围！");
            }
         }
         switch(param1.equipmentType)
         {
            case "precious":
               _loc2_ = param1 as PreciousEquipmentVO;
               _loc20_ = false;
               if(_loc2_.addPlayerSaveAttr.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc2_.addPlayerSaveAttr.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].preciousEqAddAttr[0].value;
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc2_.addPlayerSaveAttr[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc2_.addPlayerSaveAttrVals[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("灵符附魔属性值出现错误！");
                              throw new Error("灵符附魔属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
                  _loc3_ = false;
                  _loc7_ = 0;
                  _loc11_ = 0;
                  while(_loc11_ < _loc2_.addPlayerSaveAttr.length - 1)
                  {
                     _loc7_ = _loc11_ + 1;
                     while(_loc7_ < _loc2_.addPlayerSaveAttr.length)
                     {
                        if(_loc2_.addPlayerSaveAttr[_loc11_] == _loc2_.addPlayerSaveAttr[_loc7_])
                        {
                           _loc3_ = true;
                        }
                        _loc7_++;
                     }
                     _loc11_++;
                  }
                  if(_loc3_)
                  {
                     GamingUI.getInstance().closeInternalPanel();
                     CheatData.getInstance().addCheatDataStr("灵符附魔属性出现两个以上相同的属性!");
                     throw new Error("灵符附魔属性出现两个以上相同的属性!");
                  }
               }
               _loc5_ = XMLSingle.getInstance().equipmentXML.item.(@id == param1.id);
               if(_loc5_)
               {
                  _loc9_ = _loc5_.addWeight;
                  if(_loc9_ && _loc9_.length() < _loc2_.basisAttr.length)
                  {
                     CheatData.getInstance().addCheatDataStr("灵符属性个数超限!");
                     Part1.getInstance().isCheat = true;
                  }
                  _loc17_ = int(_loc2_.basisAttr.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc9_ = _loc5_.addAttr.(@addAttName == _loc2_.basisAttr[_loc11_]);
                     if(_loc9_ && _loc9_.length() && _loc2_.basisAttrValue[_loc11_] + _loc2_.basisUpValue[_loc11_] > Number(_loc9_[0].@addAttValue) + Number(_loc9_[0].@maxupgrade) * (_loc2_.level + 12))
                     {
                        trace("属性" + (_loc2_.basisAttrValue[_loc11_] + _loc2_.basisUpValue[_loc11_]));
                        trace("属性" + (Number(_loc9_[0].@addAttValue) + Number(_loc9_[0].@maxupgrade) * (_loc2_.level + 1)));
                        CheatData.getInstance().addCheatDataStr("灵符基础属性" + _loc2_.basisAttr[_loc11_] + "超出范围!");
                        Part1.getInstance().isCheat = true;
                     }
                     _loc11_++;
                  }
                  _loc9_ = _loc5_.saddWeight;
                  if(_loc9_ && _loc9_.length() < (param1 as PreciousEquipmentVO).sAttrName.length)
                  {
                     CheatData.getInstance().addCheatDataStr("灵符特殊个数超限!");
                     Part1.getInstance().isCheat = true;
                  }
                  _loc17_ = int(_loc2_.sAttrName.length);
                  _loc11_ = 0;
                  while(_loc11_ < _loc17_)
                  {
                     _loc9_ = _loc5_.sAddAttr.(@addAttName == _loc2_.sAttrName[_loc11_]);
                     if(_loc2_.sAttrValue[_loc11_] - Number(_loc9_[0].@maxvalue) > Number(_loc9_[0].@maxvalue) || _loc2_.sAvgValue[_loc11_] != Number(_loc9_[0].@avgValue))
                     {
                        CheatData.getInstance().addCheatDataStr("灵符特殊属性" + _loc2_.sAttrName[_loc11_] + "超出范围!");
                        Part1.getInstance().isCheat = true;
                     }
                     _loc11_++;
                  }
               }
               break;
            case "clothes":
               if((param1 as ClothesEquipmentVO).defence > Number(XMLSingle.getInstance().dataXML.detection[0].clothesEquipmentMaxDefence[0]))
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("套装防御力出现错误！");
                  throw new Error("套装防御力出现错误！");
               }
               if((param1 as ClothesEquipmentVO).defence < 250 && (param1 as ClothesEquipmentVO).defence > (param1 as ClothesEquipmentVO).maxDefence + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1))
               {
                  (param1 as ClothesEquipmentVO).defence = (param1 as ClothesEquipmentVO).maxDefence + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1);
               }
               if((param1 as ClothesEquipmentVO).riot > Number(XMLSingle.getInstance().dataXML.detection[0].clothesEquipmentMaxRiot[0]))
               {
                  CheatData.getInstance().addCheatDataStr("套装防暴值出现错误！");
                  trace("套装防暴值出现错误！");
               }
               if((param1 as ClothesEquipmentVO).rengPin > Number(XMLSingle.getInstance().dataXML.detection[0].EquipmentMaxRenpin[0]))
               {
                  CheatData.getInstance().addCheatDataStr("套装人品出现错误！");
               }
               _loc20_ = false;
               _loc16_ = [];
               _loc12_ = param1 as ClothesEquipmentVO;
               if(_loc12_.addPlayerSaveAttr.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc12_.addPlayerSaveAttr.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].clothEqAddAttr[0].value;
                     if(_loc16_.indexOf(_loc12_.addPlayerSaveAttr[_loc11_]) != -1)
                     {
                        CheatData.getInstance().addCheatDataStr("套装附魔属性出现重复！");
                        Part1.getInstance().isCheat = true;
                        _loc20_ = true;
                     }
                     else
                     {
                        _loc16_.push(_loc12_.addPlayerSaveAttr[_loc11_]);
                     }
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc12_.addPlayerSaveAttr[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc12_.addPlayerSaveAttrVals[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("套装附魔属性值出现错误！");
                              throw new Error("套装附魔属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               detectionTopEquip(param1,350);
               break;
            case "weapon":
               if((param1 as WeaponEquipmentVO).attack > Number(XMLSingle.getInstance().dataXML.detection[0].weaponEquipmentMaxAttack[0]))
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("武器攻击力出现错误！");
                  throw new Error("武器攻击力出现错误！");
               }
               if((param1 as WeaponEquipmentVO).attack < 270 && (param1 as WeaponEquipmentVO).attack > (param1 as WeaponEquipmentVO).maxAttack + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1))
               {
                  (param1 as WeaponEquipmentVO).attack = (param1 as WeaponEquipmentVO).maxAttack + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1);
               }
               if((param1 as WeaponEquipmentVO).rengPin > Number(XMLSingle.getInstance().dataXML.detection[0].EquipmentMaxRenpin[0]))
               {
                  CheatData.getInstance().addCheatDataStr("武器人品出现错误！");
               }
               _loc20_ = false;
               _loc13_ = param1 as WeaponEquipmentVO;
               _loc16_ = [];
               if(_loc13_.addPlayerSaveAttr.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc13_.addPlayerSaveAttr.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].weaponEqAddAttr[0].value;
                     if(_loc16_.indexOf(_loc13_.addPlayerSaveAttr[_loc11_]) != -1)
                     {
                        CheatData.getInstance().addCheatDataStr("武器附魔属性出现重复！");
                        Part1.getInstance().isCheat = true;
                        _loc20_ = true;
                     }
                     else
                     {
                        _loc16_.push(_loc13_.addPlayerSaveAttr[_loc11_]);
                     }
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc13_.addPlayerSaveAttr[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc13_.addPlayerSaveAttrVals[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("武器附魔属性值出现错误！");
                              throw new Error("武器附魔属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               detectionTopEquip(param1,350);
               break;
            case "necklace":
               if((param1 as NecklaceEquipmentVO).criticalRate > Number(XMLSingle.getInstance().dataXML.detection[0].necklaceEquipmentCriticalRate[0]))
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("项链暴击率出现错误！");
                  throw new Error("项链暴击率出现错误！");
               }
               if((param1 as NecklaceEquipmentVO).criticalRate < 50 && (param1 as NecklaceEquipmentVO).criticalRate > (param1 as NecklaceEquipmentVO).maxCriticalRate + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1))
               {
                  (param1 as NecklaceEquipmentVO).criticalRate = (param1 as NecklaceEquipmentVO).maxCriticalRate + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1);
               }
               if((param1 as NecklaceEquipmentVO).rengPin > Number(XMLSingle.getInstance().dataXML.detection[0].EquipmentMaxRenpin[0]))
               {
                  CheatData.getInstance().addCheatDataStr("项链人品出现错误！");
               }
               _loc20_ = false;
               _loc15_ = param1 as NecklaceEquipmentVO;
               _loc16_ = [];
               if(_loc15_.addPlayerSaveAttr.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc15_.addPlayerSaveAttr.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].neckLaceEqAddAttr[0].value;
                     if(_loc16_.indexOf(_loc15_.addPlayerSaveAttr[_loc11_]) != -1)
                     {
                        CheatData.getInstance().addCheatDataStr("项链附魔属性出现重复！");
                        Part1.getInstance().isCheat = true;
                        _loc20_ = true;
                     }
                     else
                     {
                        _loc16_.push(_loc15_.addPlayerSaveAttr[_loc11_]);
                     }
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc15_.addPlayerSaveAttr[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc15_.addPlayerSaveAttrVals[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("项链附魔属性值出现错误！");
                              throw new Error("项链附魔属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               detectionTopEquip(param1,50);
               break;
            case "gourd":
               if((param1 as GourdEquipmentVO).maxMagic > Number(XMLSingle.getInstance().dataXML.detection[0].gourdEquipmentMaxMaxMagic[0]))
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("葫芦魔法值出现错误！");
                  throw new Error("葫芦魔法值出现错误！");
               }
               if((param1 as GourdEquipmentVO).maxMagic < 700 && (param1 as GourdEquipmentVO).maxMagic > (param1 as GourdEquipmentVO).maxMaxMagic + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1))
               {
                  (param1 as GourdEquipmentVO).maxMagic = (param1 as GourdEquipmentVO).maxMaxMagic + XMLSingle.getInstance().calculationAllAddValueInUpgradeEquipment(param1);
               }
               if((param1 as GourdEquipmentVO).rengPin > Number(XMLSingle.getInstance().dataXML.detection[0].EquipmentMaxRenpin[0]))
               {
                  CheatData.getInstance().addCheatDataStr("葫芦人品出现错误！");
               }
               _loc20_ = false;
               _loc19_ = param1 as GourdEquipmentVO;
               _loc16_ = [];
               if(_loc19_.addPlayerSaveAttr.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc19_.addPlayerSaveAttr.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].gourdEqAddAttr[0].value;
                     if(_loc16_.indexOf(_loc19_.addPlayerSaveAttr[_loc11_]) != -1)
                     {
                        CheatData.getInstance().addCheatDataStr("葫芦附魔属性出现重复！");
                        Part1.getInstance().isCheat = true;
                        _loc20_ = true;
                     }
                     else
                     {
                        _loc16_.push(_loc19_.addPlayerSaveAttr[_loc11_]);
                     }
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc19_.addPlayerSaveAttr[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc19_.addPlayerSaveAttrVals[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              CheatData.getInstance().addCheatDataStr("葫芦附魔属性值出现错误！");
                              trace("葫芦附魔属性值出现错误！");
                              _loc20_ = true;
                              break;
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               detectionTopEquip(param1,50);
               break;
            case "material":
            case "pocket":
            case "potion":
            case "buffEquipment":
            case "grass":
            case "insetGem":
               if((param1 as StackEquipmentVO).num > (param1 as StackEquipmentVO).maxSuperposition)
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("数量错误！");
                  throw new Error("数量错误！");
               }
               break;
            case "fashion":
               _loc20_ = false;
               _loc18_ = param1 as FashionEquipmentVO;
               if(_loc18_.addPlayerAttributes.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc18_.addPlayerAttributes.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].fashionEqAddAttr[0].value;
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc18_.addPlayerAttributes[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc18_.addPlayerAttributeValues[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("时装附加属性值出现错误！");
                              throw new Error("时装附加属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               break;
            case "forever_fashion":
               _loc20_ = false;
               _loc10_ = param1 as ForeverFashionEquipmentVO;
               if(_loc10_.addPlayerAttributes.length > 0)
               {
                  _loc11_ = 0;
                  while(_loc11_ < _loc10_.addPlayerAttributes.length)
                  {
                     _loc14_ = XMLSingle.getInstance().dataXML.detection[0].fashionEqAddAttr[0].value;
                     _loc17_ = int(_loc14_.length());
                     _loc8_ = 0;
                     while(_loc8_ < _loc17_)
                     {
                        if(_loc10_.addPlayerAttributes[_loc11_] == String(_loc14_[_loc8_].@addPlayerAttribute))
                        {
                           if(_loc10_.addPlayerAttributeValues[_loc11_] > Number(_loc14_[_loc8_].@addPlayerAttributeValue))
                           {
                              GamingUI.getInstance().closeInternalPanel();
                              CheatData.getInstance().addCheatDataStr("永久时装附加属性值出现错误！");
                              throw new Error("永久时装附加属性值出现错误！");
                           }
                        }
                        _loc8_++;
                     }
                     if(_loc20_)
                     {
                        break;
                     }
                     _loc11_++;
                  }
               }
               break;
            case "pet":
               _loc4_ = param1 as PetEquipmentVO;
               detectionPetEquipmentVO(_loc4_);
         }
      }
      
      private function detectionPetEquipmentVO(param1:PetEquipmentVO) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Array = [];
         _loc3_ = int(param1.passiveSkillVOs.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            detectionSkillVO(param1.passiveSkillVOs[_loc4_],param1);
            if(_loc2_.indexOf(param1.passiveSkillVOs[_loc4_].id) != -1)
            {
               GamingUI.getInstance().closeInternalPanel();
               CheatData.getInstance().addCheatDataStr("宠物被动技能重复！");
               throw new Error("宠物被动技能重复！");
            }
            _loc2_.push(param1.passiveSkillVOs[_loc4_].id);
            _loc4_++;
         }
      }
      
      private function detectionSkillVO(param1:SkillVO, param2:PetEquipmentVO) : void
      {
         var _loc4_:PetPassiveSkillVO = null;
         var _loc5_:* = null;
         var _loc3_:Array = null;
         if(param1 is PetPassiveSkillVO)
         {
            _loc4_ = param1 as PetPassiveSkillVO;
            _loc5_ = XMLSingle.getInstance().skillXML.item.(@type == "petPassive" && @level == param2.level && @id == param1.id);
            if(_loc5_ && _loc5_.length() > 0)
            {
               _loc3_ = String(_loc5_[0].@useSeriesType).split(",");
               if(_loc3_.indexOf(param2.petSeries) == -1 && (param1 as PetPassiveSkillVO).promoteValue)
               {
                  CheatData.getInstance().addCheatDataStr("宠物被动技能不适合！");
                  trace("宠物被动技能不适合！");
               }
            }
            switch(_loc4_.className)
            {
               case "PetSkill_GuWu":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillGuWu[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_JiNu":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillJiNu[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_FaShuZengQiang":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillFaShuZengQiang[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_ShouHu":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillShouHu[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_JianZhuang":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillJianZhuang[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_MiShuZhangWo":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillMiShuZhangWo[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_AoYiGuangHuang":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillAoYiGuangHuang[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_JiNengAoYi":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillJiNengAoYi[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_JiNengLingWu":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillJiNengLingWu[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_YiShuJingTong":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillYiShuJingTong[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_YiShuJingTong2":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillYiShuJingTong2[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_GuBen":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillGuBen[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_ShanBi":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillShanBi[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_Mingzhong":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillMingzhong[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_ZhiShang":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillZhiShang[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
                  break;
               case "PetSkill_Xuenu":
                  if(_loc4_.value > Number(XMLSingle.getInstance().dataXML.detection[0].petSkill.(@promoteXMLId == param2.promoteXMLId)[0].petSkillXuenu[0]))
                  {
                     CheatData.getInstance().addCheatDataStr("宠物被动技能数值错误！");
                     trace("宠物被动技能数值错误！");
                  }
            }
         }
      }
      
      public function resetCheckTime() : void
      {
         _lastCheckTime = 0;
      }
   }
}

