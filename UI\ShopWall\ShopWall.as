package UI.ShopWall
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.PageBtn.PageBtnGroup;
   import UI.Button.QuitBtn;
   import UI.Button.SwitchBtn.ShopWallSwitchBtn.ShopWall_DaoJuBtn;
   import UI.Button.SwitchBtn.ShopWallSwitchBtn.ShopWall_FashionBtn;
   import UI.Button.SwitchBtn.ShopWallSwitchBtn.ShopWall_PetBtn;
   import UI.Button.SwitchBtn.ShopWallSwitchBtn.ShopWall_XianShiGoodBtn;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.UnAbleBuyEquipmentData;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ShopWall extends MySprite
   {
      
      public var xianShiGoodBtn:ShopWall_XianShiGoodBtn;
      
      public var daoJuBtn:ShopWall_DaoJuBtn;
      
      public var petBtn:ShopWall_PetBtn;
      
      public var fashionBtn:ShopWall_FashionBtn;
      
      public var currentTicketPointText:TextField;
      
      public var rechargeBtn:RechargeBtn;
      
      public var quitBtn:QuitBtn;
      
      private const SHOP_GOODS_NUMBER_ONE_PAGE:int = 5;
      
      private var _xianShi:Vector.<EquipmentVO>;
      
      private var _daoJu:Vector.<EquipmentVO>;
      
      private var _pets:Vector.<EquipmentVO>;
      
      private var _fashions:Vector.<EquipmentVO>;
      
      private var _sign:int = 0;
      
      private var _popUpBox:ShopWall_BuyPopUpBox;
      
      private var _pageBtn:PageBtnGroup;
      
      private const X:Number = 51;
      
      private const Y:Number = 102.6;
      
      private const BASE_NUM:int = 11;
      
      private const HEIGHT:Number = 69;
      
      private var _player1:Player;
      
      private var _player2:Player;
      
      private var _shopWallXML:XML;
      
      private var _oneBuyEquipmentIds:Vector.<String>;
      
      public function ShopWall(param1:Player, param2:Player)
      {
         super();
         _player1 = param1;
         _player2 = param2;
         initShop();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      public function addGoods() : void
      {
         MyFunction2.loadXMLFunction("ShopWall",function(param1:XML):void
         {
            _shopWallXML = param1;
            _oneBuyEquipmentIds = MyFunction.getInstance().excreteStringToString(param1.oneBuyEquipment[0].@ids);
            var _loc2_:String = GamingUI.getInstance().getNewestTimeStrFromSever();
            var _loc3_:Object = initShopWall(param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,_loc2_);
            if(_loc3_)
            {
               _sign = 0;
               _xianShi = _loc3_.xianShis;
               _daoJu = _loc3_.props;
               _pets = _loc3_.pets;
               _fashions = _loc3_.fashions;
               setShopPage(1);
               arrangeGoods((_pageBtn.pageNum - 1) * 5);
               _loc3_.xianShis = null;
               _loc3_.props = null;
               _loc3_.pets = null;
               _loc3_.fashions = null;
               _loc3_ = null;
            }
         },showWarningBox,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            removeChildAt(0);
         }
         var _loc3_:int = 0;
         if(xianShiGoodBtn)
         {
            xianShiGoodBtn.clear();
         }
         xianShiGoodBtn = null;
         if(daoJuBtn)
         {
            daoJuBtn.clear();
         }
         daoJuBtn = null;
         if(petBtn)
         {
            petBtn.clear();
         }
         petBtn = null;
         if(fashionBtn)
         {
            fashionBtn.clear();
         }
         fashionBtn = null;
         currentTicketPointText = null;
         if(rechargeBtn)
         {
            rechargeBtn.clear();
         }
         rechargeBtn = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_popUpBox)
         {
            _popUpBox.clear();
         }
         _popUpBox = null;
         if(_pageBtn)
         {
            _pageBtn.clear();
         }
         _pageBtn = null;
         if(_xianShi)
         {
            _loc1_ = int(_xianShi.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_xianShi[_loc3_])
               {
                  _xianShi[_loc3_].clear();
               }
               _xianShi[_loc3_] = null;
               _loc3_++;
            }
         }
         _xianShi = null;
         if(_daoJu)
         {
            _loc1_ = int(_daoJu.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_daoJu[_loc3_])
               {
                  _daoJu[_loc3_].clear();
               }
               _daoJu[_loc3_] = null;
               _loc3_++;
            }
         }
         _daoJu = null;
         if(_pets)
         {
            _loc1_ = int(_pets.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_pets[_loc3_])
               {
                  _pets[_loc3_].clear();
               }
               _pets[_loc3_] = null;
               _loc3_++;
            }
         }
         _pets = null;
         if(_fashions)
         {
            _loc1_ = int(_fashions.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_fashions[_loc3_])
               {
                  _fashions[_loc3_].clear();
               }
               _fashions[_loc3_] = null;
               _loc3_++;
            }
         }
         _fashions = null;
         _player1 = null;
         _player2 = null;
         _shopWallXML = null;
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(_popUpBox)
         {
            if(getChildByName(_popUpBox.name))
            {
               removeChild(_popUpBox);
            }
            _popUpBox.clear();
            _popUpBox = null;
         }
      }
      
      public function initShop() : void
      {
         xianShiGoodBtn.init(false);
         daoJuBtn.init(true);
         petBtn.init(true);
         fashionBtn.init(true);
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _pageBtn = new PageBtnGroup();
         _pageBtn.x = 740;
         _pageBtn.y = 470;
         addChild(_pageBtn);
         setShopPage(1);
         currentTicketPointText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,30,16777113);
         currentTicketPointText.embedFonts = true;
      }
      
      private function initShopWall(param1:XML, param2:XML, param3:XML, param4:XML, param5:String) : Object
      {
         var _loc11_:int = 0;
         var _loc9_:XML = null;
         var _loc7_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc13_:XMLList = param1.XianShi[0].children();
         var _loc12_:int = int(_loc13_.length());
         var _loc8_:XML = <XianShi></XianShi>;
         _loc11_ = 0;
         while(_loc11_ < _loc12_)
         {
            _loc9_ = _loc13_[_loc11_];
            _loc7_ = new TimeUtil().timeInterval(_loc9_.@startDate,param5);
            _loc6_ = new TimeUtil().timeInterval(param5,_loc9_.@endDate);
            if(!(_loc7_ < 0 || _loc6_ < 0))
            {
               _loc8_.appendChild(_loc9_);
            }
            _loc11_++;
         }
         var _loc10_:Object = {};
         _loc10_.xianShis = InitUI.getInstance().getEquipmentVOs(_loc8_,param2,param3,param4,param5);
         _loc10_.fashions = InitUI.getInstance().getEquipmentVOs(param1.Fashion[0],param2,param3,param4,param5);
         _loc10_.pets = InitUI.getInstance().getEquipmentVOs(param1.Pet[0],param2,param3,param4,param5);
         _loc10_.props = InitUI.getInstance().getEquipmentVOs(param1.Prop[0],param2,param3,param4,param5);
         return _loc10_;
      }
      
      protected function setShopPage(param1:int) : void
      {
         switch(_sign)
         {
            case 0:
               if(!Boolean(_xianShi) || !_xianShi.length)
               {
                  _pageBtn.initPageNumber(1,1);
                  break;
               }
               if(_xianShi.length % 5 == 0)
               {
                  _pageBtn.initPageNumber(param1,_xianShi.length / 5);
               }
               else
               {
                  _pageBtn.initPageNumber(param1,int(_xianShi.length / 5) + 1);
               }
               break;
            case 1:
               if(!Boolean(_daoJu) || !_daoJu.length)
               {
                  _pageBtn.initPageNumber(1,1);
                  break;
               }
               if(_daoJu.length % 5 == 0)
               {
                  _pageBtn.initPageNumber(param1,_daoJu.length / 5);
               }
               else
               {
                  _pageBtn.initPageNumber(param1,int(_daoJu.length / 5) + 1);
               }
               break;
            case 2:
               if(!Boolean(_pets) || !_pets.length)
               {
                  _pageBtn.initPageNumber(1,1);
                  break;
               }
               if(_pets.length % 5 == 0)
               {
                  _pageBtn.initPageNumber(param1,_pets.length / 5);
               }
               else
               {
                  _pageBtn.initPageNumber(param1,int(_pets.length / 5) + 1);
               }
               break;
            case 3:
               if(!Boolean(_fashions) || !_fashions.length)
               {
                  _pageBtn.initPageNumber(1,1);
                  break;
               }
               if(_fashions.length % 5 == 0)
               {
                  _pageBtn.initPageNumber(param1,_fashions.length / 5);
                  break;
               }
               _pageBtn.initPageNumber(param1,int(_fashions.length / 5) + 1);
               break;
         }
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("pageUp",pageUp,true,0,true);
         addEventListener("pageDown",pageDown,true,0,true);
         addEventListener("showBox",showBox,true,0,true);
         addEventListener("cancelBuy",hideBox,true,0,true);
         addEventListener("clickShopWallSwitchBtn",switchShop,true,0,true);
         addEventListener("buyEquipment",buyEquipment,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("clickRechargeBtn",recharge,true,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("pageUp",pageUp,true);
         removeEventListener("pageDown",pageDown,true);
         removeEventListener("showBox",showBox,true);
         removeEventListener("cancelBuy",hideBox,true);
         removeEventListener("clickShopWallSwitchBtn",switchShop,true);
         removeEventListener("buyEquipment",buyEquipment,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("clickRechargeBtn",recharge,true);
         removeEventListener("clickButton",clickButton,true);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function pageUp(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 5);
      }
      
      protected function pageDown(param1:UIBtnEvent) : void
      {
         arrangeGoods((_pageBtn.pageNum - 1) * 5);
      }
      
      protected function switchShop(param1:UIBtnEvent) : void
      {
         switch(param1.target.name)
         {
            case "xianShiGoodBtn":
               _sign = 0;
               daoJuBtn.gotoTwoFrame();
               petBtn.gotoTwoFrame();
               fashionBtn.gotoTwoFrame();
               setChildIndex(daoJuBtn,1);
               setChildIndex(petBtn,1);
               setChildIndex(fashionBtn,1);
               setShopPage(1);
               arrangeGoods((_pageBtn.pageNum - 1) * 5);
               break;
            case "daoJuBtn":
               _sign = 1;
               xianShiGoodBtn.gotoTwoFrame();
               petBtn.gotoTwoFrame();
               fashionBtn.gotoTwoFrame();
               setChildIndex(xianShiGoodBtn,1);
               setChildIndex(petBtn,1);
               setChildIndex(fashionBtn,1);
               setShopPage(1);
               arrangeGoods((_pageBtn.pageNum - 1) * 5);
               break;
            case "petBtn":
               _sign = 2;
               xianShiGoodBtn.gotoTwoFrame();
               daoJuBtn.gotoTwoFrame();
               fashionBtn.gotoTwoFrame();
               setChildIndex(xianShiGoodBtn,1);
               setChildIndex(daoJuBtn,1);
               setChildIndex(fashionBtn,1);
               setShopPage(1);
               arrangeGoods((_pageBtn.pageNum - 1) * 5);
               break;
            case "fashionBtn":
               _sign = 3;
               xianShiGoodBtn.gotoTwoFrame();
               daoJuBtn.gotoTwoFrame();
               petBtn.gotoTwoFrame();
               setChildIndex(xianShiGoodBtn,1);
               setChildIndex(daoJuBtn,1);
               setChildIndex(petBtn,1);
               setShopPage(1);
               arrangeGoods((_pageBtn.pageNum - 1) * 5);
         }
      }
      
      protected function arrangeGoods(param1:int) : void
      {
         var _loc4_:DisplayObject = null;
         var _loc3_:int = 0;
         var _loc5_:* = 0;
         var _loc2_:ShopWallSlot = null;
         while(numChildren > 11)
         {
            _loc4_ = getChildAt(11);
            removeChild(_loc4_);
            if(_loc4_ is ShopWallSlot)
            {
               (_loc4_ as ShopWallSlot).clear();
            }
         }
         var _loc6_:int = param1 + 5;
         if(_sign == 0)
         {
            _loc3_ = int(_xianShi.length);
            _loc5_ = param1;
            while(_loc5_ < _loc6_ && _loc5_ < _loc3_)
            {
               if(_xianShi[_loc5_] != null)
               {
                  _loc2_ = new ShopWallSlot();
                  _loc2_.initSlot(_xianShi[_loc5_]);
                  if(UnAbleBuyEquipmentData.getInstance().isHaveId(_xianShi[_loc5_].id.toString()))
                  {
                     _loc2_.mouseChildren = false;
                     _loc2_.mouseEnabled = false;
                     MyFunction.getInstance().changeSaturation(_loc2_,-100);
                  }
                  _loc2_.x = 51;
                  _loc2_.y = 69 * (_loc5_ - param1) + 102.6;
                  addChild(_loc2_);
               }
               _loc5_++;
            }
         }
         else if(_sign == 1)
         {
            _loc3_ = int(_daoJu.length);
            _loc5_ = param1;
            while(_loc5_ < _loc6_ && _loc5_ < _loc3_)
            {
               if(_daoJu[_loc5_] != null)
               {
                  _loc2_ = new ShopWallSlot();
                  _loc2_.initSlot(_daoJu[_loc5_]);
                  _loc2_.x = 51;
                  _loc2_.y = 69 * (_loc5_ - param1) + 102.6;
                  addChild(_loc2_);
               }
               _loc5_++;
            }
         }
         else if(_sign == 2)
         {
            _loc3_ = int(_pets.length);
            _loc5_ = param1;
            while(_loc5_ < _loc6_ && _loc5_ < _loc3_)
            {
               if(_pets[_loc5_] != null)
               {
                  _loc2_ = new ShopWallSlot();
                  _loc2_.initSlot(_pets[_loc5_]);
                  _loc2_.x = 51;
                  _loc2_.y = 69 * (_loc5_ - param1) + 102.6;
                  addChild(_loc2_);
               }
               _loc5_++;
            }
         }
         else
         {
            _loc3_ = int(_fashions.length);
            _loc5_ = param1;
            while(_loc5_ < _loc6_ && _loc5_ < _loc3_)
            {
               if(_fashions[_loc5_] != null)
               {
                  _loc2_ = new ShopWallSlot();
                  _loc2_.initSlot(_fashions[_loc5_]);
                  _loc2_.x = 51;
                  _loc2_.y = 69 * (_loc5_ - param1) + 102.6;
                  addChild(_loc2_);
               }
               _loc5_++;
            }
         }
      }
      
      protected function showBox(param1:UIDataEvent) : void
      {
         var _loc5_:int = 0;
         var _loc2_:EquipmentVO = param1.data.equipmentVO;
         var _loc4_:int = 100;
         var _loc3_:int = _oneBuyEquipmentIds ? _oneBuyEquipmentIds.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(_oneBuyEquipmentIds[_loc5_] == _loc2_.id.toString())
            {
               _loc4_ = 1;
            }
            _loc5_++;
         }
         if(getChildByName("popUpBox"))
         {
            _popUpBox.initBox(_loc2_,"shopWallPrice");
            _popUpBox.setNumBtnGroupMaxNum(_loc4_,showWarningBox,["该物品一次购买最大购买数量为" + _loc4_,0]);
         }
         else
         {
            _popUpBox = new ShopWall_BuyPopUpBox(_loc2_,"shopWallPrice");
            _popUpBox.setNumBtnGroupMaxNum(_loc4_,showWarningBox,["该物品一次购买最大购买数量为" + _loc4_,0]);
            _popUpBox.x = 350;
            _popUpBox.y = 170;
            _popUpBox.name = "popUpBox";
            addChild(_popUpBox);
         }
         if(!_player2)
         {
            _popUpBox.state = 1;
         }
         else
         {
            _popUpBox.state = 2;
         }
      }
      
      protected function buyEquipment(param1:UIDataEvent) : void
      {
         var e:UIDataEvent = param1;
         var addUnAbleBuyId:* = function():void
         {
            var _loc2_:int = 0;
            var _loc1_:int = _oneBuyEquipmentIds ? _oneBuyEquipmentIds.length : 0;
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(_oneBuyEquipmentIds[_loc2_] == equipmentVO.id.toString())
               {
                  UnAbleBuyEquipmentData.getInstance().addId(equipmentVO.id.toString());
                  arrangeGoods((_pageBtn.pageNum - 1) * 5);
                  return;
               }
               _loc2_++;
            }
         };
         var equipmentVO:EquipmentVO = e.data.equipmentVO;
         var num:int = int(e.data.num);
         var price:int = int(XMLSingle.getInstance().equipmentXML.item.(@id == equipmentVO.id)[0].@ticketPrice);
         var ticketId:String = String(XMLSingle.getInstance().equipmentXML.item.(@id == equipmentVO.id)[0].@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = num;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         AnalogServiceHoldFunction.getInstance().buyByPayData(dataObj,function(param1:Function):void
         {
            var _loc2_:* = e.data.player;
            if(1 !== _loc2_)
            {
               MyFunction.getInstance().buyEquipmentVO(_player2,equipmentVO,num,showWarningBox,"ticketObj",param1,0,addUnAbleBuyId);
            }
            else
            {
               MyFunction.getInstance().buyEquipmentVO(_player1,equipmentVO,num,showWarningBox,"ticketObj",param1,0,addUnAbleBuyId);
            }
         },stage,showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         hideBox();
      }
      
      protected function recharge(param1:UIBtnEvent) : void
      {
         var _loc2_:* = null;
         AnalogServiceHoldFunction.getInstance().payMoney_As3();
      }
      
      override public function set visible(param1:Boolean) : void
      {
         super.visible = param1;
         hideBox();
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChild(WarningBoxSingle.getInstance());
      }
   }
}

