package UI.AutomaticPetPanel
{
   import GM_UI.AutomaticPetChange;
   import GM_UI.GMData;
   import UI.AnalogServiceHoldFunction;
   import UI.ConsumptionGuide.GuideBuyPopUpBox;
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.ContractEquipmntVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.PageBtnGroupLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.SmallPackage.SmallPackage;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.GameData;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.system.System;
   
   public class AutomaticPetPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_petChooseBtns:Vector.<ChoosePetSwitchBtn>;
      
      private var m_petChooseBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_pageBtnGroup:PageBtnGroupLogicShell;
      
      private var m_inforBtn:MySwitchBtnLogicShell;
      
      private var m_strengthenBtn:MySwitchBtnLogicShell;
      
      private var m_advanceBtn:MySwitchBtnLogicShell;
      
      private var m_mainBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_inforShowPanel:InforShowPanel;
      
      private var m_strengthenPanel:StrengthenPanel;
      
      private var m_advancePanel:AdvancePanel;
      
      private var m_automaticPetDataXML:XML;
      
      private var m_myLoader:YJFYLoader;
      
      private var _popUpBox:GuideBuyPopUpBox;
      
      private var m_versionControl:IVersionControl;
      
      private var m_loadUI:IProgressShow;
      
      private var m_automaticPetsData:AutomaticPetsData;
      
      private var m_gamingUI:GamingUI;
      
      private var m_currentShowAutomaticPetVO:AutomaticPetVO;
      
      private var m_changePanel:AutomaticPetChange;
      
      public function AutomaticPetPanel()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         m_quitBtn = new ButtonLogicShell2();
         m_petChooseBtns = new Vector.<ChoosePetSwitchBtn>();
         m_petChooseBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_pageBtnGroup = new PageBtnGroupLogicShell();
         m_inforBtn = new MySwitchBtnLogicShell();
         m_strengthenBtn = new MySwitchBtnLogicShell();
         m_advanceBtn = new MySwitchBtnLogicShell();
         m_mainBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_mainBtnGroup.addSwitchBtn(m_inforBtn);
         m_mainBtnGroup.addSwitchBtn(m_strengthenBtn);
         m_mainBtnGroup.addSwitchBtn(m_advanceBtn);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("showGuideBox",showBox,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         addEventListener("buyEquipment",BuyEquip,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("showGuideBox",showBox,true);
         removeEventListener("warningBox",sureOrCancel,true);
         removeEventListener("buyEquipment",BuyEquip,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_petChooseBtns);
         m_petChooseBtns = null;
         ClearUtil.clearObject(m_petChooseBtnGroup);
         m_petChooseBtnGroup = null;
         ClearUtil.clearObject(m_pageBtnGroup);
         m_pageBtnGroup = null;
         ClearUtil.clearObject(m_inforBtn);
         m_inforBtn = null;
         ClearUtil.clearObject(m_strengthenBtn);
         m_strengthenBtn = null;
         ClearUtil.clearObject(m_advanceBtn);
         m_advanceBtn = null;
         ClearUtil.clearObject(m_mainBtnGroup);
         m_mainBtnGroup = null;
         ClearUtil.clearObject(m_inforShowPanel);
         m_inforShowPanel = null;
         ClearUtil.clearObject(m_strengthenPanel);
         m_strengthenPanel = null;
         ClearUtil.clearObject(m_advancePanel);
         m_advancePanel = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         System.disposeXML(m_automaticPetDataXML);
         m_automaticPetDataXML = null;
         ClearUtil.clearObject(_popUpBox);
         _popUpBox = null;
         m_versionControl = null;
         m_loadUI = null;
         m_automaticPetsData = null;
         m_gamingUI = null;
         m_currentShowAutomaticPetVO = null;
         super.clear();
      }
      
      protected function BuyEquip(param1:UIDataEvent) : void
      {
         buy2(param1.data.num,param1.data.equipmentVO as ContractEquipmntVO);
      }
      
      private function buy2(param1:int, param2:ContractEquipmntVO) : void
      {
         var num:int = param1;
         var equip:ContractEquipmntVO = param2;
         var item:XML = XMLSingle.getInstance().tehui.xiaoxia[0];
         var price:uint = uint(int(item.@ticketPrice));
         var ticketId:String = String(item.@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = num;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买经验小虾米";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            useXiaoxia(num,equip);
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
         m_strengthenPanel.closeChoiceDevourPanel();
      }
      
      private function useXiaoxia(param1:int, param2:ContractEquipmntVO) : void
      {
         var _loc3_:AutomaticPetVO = null;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < param1)
         {
            _loc3_ = XMLSingle.getAutomaticPetVOByID(param2.targetAutomaticPetId,XMLSingle.getInstance().automaticPetsXML);
            GamingUI.getInstance().getAutomaticPetsData().addAutomaticPetVO(_loc3_);
            _loc4_++;
         }
         refreshShow(m_currentShowAutomaticPetVO);
      }
      
      public function init(param1:AutomaticPetsData, param2:IVersionControl, param3:IProgressShow, param4:GamingUI) : void
      {
         m_automaticPetsData = param1;
         m_versionControl = param2;
         m_loadUI = param3;
         m_gamingUI = param4;
         detect();
         ClearUtil.clearObject(m_myLoader);
         var _loc5_:Class = MyFunction2.returnClassByClassName("AutomaitcPetPanel");
         if(_loc5_ == null)
         {
            m_myLoader = new YJFYLoader();
            m_myLoader.setProgressShow(m_loadUI);
            m_myLoader.setVersionControl(m_versionControl);
            m_myLoader.getClass("UISprite2/AutomaticPetPanel.swf","AutomaitcPetPanel",getShowSuccess,getFail);
            m_myLoader.getXML("UIData/automaticPet/automaticPetData.xml",getXMLSuccess,getFail);
            m_myLoader.load();
         }
         else
         {
            m_show = new _loc5_();
            initShow();
         }
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_inforShowPanel)
         {
            m_inforShowPanel.render(param1);
         }
      }
      
      private function detect() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
      }
      
      private function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc3_:ChoosePetSwitchBtn = null;
         m_showMC.setShow(m_show);
         addChild(m_show);
         m_quitBtn.setShow(m_show["quitBtn"]);
         if(m_automaticPetsData.getAutomaticPetVONum())
         {
            m_showMC.gotoAndStop("infor");
            _loc2_ = m_show.numChildren;
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc1_ = m_show.getChildAt(_loc5_);
               if(_loc1_.name.substr(0,"petBtn_".length) == "petBtn_")
               {
                  _loc4_++;
               }
               _loc5_++;
            }
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc3_ = new ChoosePetSwitchBtn();
               _loc3_.setShow(m_show["petBtn_" + (_loc5_ + 1)]);
               m_petChooseBtns.push(_loc3_);
               m_petChooseBtnGroup.addSwitchBtn(_loc3_);
               _loc5_++;
            }
            m_pageBtnGroup.setShow(m_show["pageBtnGroup"]);
            m_inforBtn.setShow(m_show["inforBtn"]);
            m_strengthenBtn.setShow(m_show["strengthenBtn"]);
            m_advanceBtn.setShow(m_show["advanceBtn"]);
            m_mainBtnGroup.addEnd();
            initPetChooseShow();
         }
         else
         {
            initNoPetFrame();
         }
      }
      
      private function initPetChooseShow() : void
      {
         if(m_automaticPetsData == null || m_show == null)
         {
            return;
         }
         setPageBtn(1);
         arrangePetChooseShow((m_pageBtnGroup.pageNum - 1) * m_petChooseBtns.length);
      }
      
      public function setChangePanel(param1:AutomaticPetChange) : void
      {
         this.m_changePanel = param1;
      }
      
      public function getChangePanel() : AutomaticPetChange
      {
         return this.m_changePanel;
      }
      
      public function getCurrentAutomaticPetVO(param1:Boolean = false) : AutomaticPetVO
      {
         var _loc2_:AutomaticPetVO = null;
         if(param1 && m_currentShowAutomaticPetVO.partnerUid)
         {
            _loc2_ = m_automaticPetsData.getBackPetVoByUid(m_currentShowAutomaticPetVO.partnerUid,m_currentShowAutomaticPetVO.partnerName);
         }
         if(_loc2_)
         {
            return _loc2_;
         }
         return m_currentShowAutomaticPetVO;
      }
      
      public function getAutomaticPetDataXML() : XML
      {
         return m_automaticPetDataXML;
      }
      
      public function refreshShow(param1:AutomaticPetVO) : void
      {
         var _loc3_:* = param1;
         var _loc2_:int = m_automaticPetsData.getIndexOfAutomaticPetVOEnable(_loc3_);
         if(_loc2_ == -1 && !_loc3_.partnerName)
         {
            throw new Error("出错了");
         }
         if(_loc2_ == -1)
         {
            _loc3_ = m_automaticPetsData.getBackPetVoByUid(_loc3_.partnerUid,_loc3_.partnerName);
            if(!_loc3_)
            {
               return;
            }
         }
         setPageBtn(int(_loc2_ / m_petChooseBtns.length) + 1);
         arrangePetChooseShow((m_pageBtnGroup.pageNum - 1) * m_petChooseBtns.length,_loc3_);
      }
      
      private function setPageBtn(param1:int) : void
      {
         if(m_automaticPetsData == null || m_automaticPetsData.getAutomaticPetVONum() == 0)
         {
            m_pageBtnGroup.initPageNumber(1,1);
         }
         else if(m_automaticPetsData.getAutomaticPetVONum() % m_petChooseBtns.length == 0)
         {
            m_pageBtnGroup.initPageNumber(param1,m_automaticPetsData.getAutomaticPetVONum() / m_petChooseBtns.length);
         }
         else
         {
            m_pageBtnGroup.initPageNumber(param1,int(m_automaticPetsData.getAutomaticPetVONum() / m_petChooseBtns.length) + 1);
         }
      }
      
      private function arrangePetChooseShow(param1:int, param2:AutomaticPetVO = null) : void
      {
         var _loc8_:* = 0;
         var _loc3_:AutomaticPetVO = null;
         var _loc4_:int = 0;
         m_automaticPetsData.refreshSort();
         var _loc7_:int = param1 + m_petChooseBtns.length;
         var _loc5_:int = m_automaticPetsData ? m_automaticPetsData.getAutomaticPetVONum() : 0;
         var _loc6_:int = 0;
         _loc8_ = param1;
         while(_loc8_ < _loc7_ && _loc8_ < _loc5_)
         {
            _loc3_ = m_automaticPetsData.getAutomaticPetVOByIndex(_loc8_);
            m_petChooseBtns[_loc6_].getShow().visible = true;
            m_petChooseBtns[_loc6_].setData(_loc3_);
            _loc6_++;
            _loc8_++;
         }
         while(_loc6_ < m_petChooseBtns.length)
         {
            m_petChooseBtns[_loc6_].getShow().visible = false;
            _loc6_++;
         }
         if(param2 == null)
         {
            m_petChooseBtns[0].turnActiveAndDispatchEvent();
         }
         else
         {
            _loc4_ = int(m_petChooseBtns.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               if(m_petChooseBtns[_loc8_].getAutomaticPetVO() == param2)
               {
                  m_petChooseBtns[_loc8_].turnActiveAndDispatchEvent();
                  return;
               }
               _loc8_++;
            }
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         switch(param1.button)
         {
            case m_inforBtn:
               initInforShowPanelFrame();
               break;
            case m_strengthenBtn:
               initStrengthPanelFrame();
               break;
            case m_advanceBtn:
               initAdvancePanelFrame();
               break;
            case m_quitBtn:
               if(m_gamingUI)
               {
                  m_gamingUI.closeAutomaticPetPanel();
               }
               break;
            case m_pageBtnGroup:
               arrangePetChooseShow((m_pageBtnGroup.pageNum - 1) * m_petChooseBtns.length);
         }
         var _loc2_:int = m_petChooseBtns ? m_petChooseBtns.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_petChooseBtns[_loc3_] == param1.button)
            {
               upDateChoosePetShow(m_petChooseBtns[_loc3_].getAutomaticPetVO());
               return;
            }
            _loc3_++;
         }
      }
      
      private function initNoPetFrame() : void
      {
         clearFrameCotent();
         m_showMC.gotoAndStop("notPet");
      }
      
      private function initInforShowPanelFrame() : void
      {
         hideBox();
         clearFrameCotent();
         m_showMC.gotoAndStop("infor");
         m_inforShowPanel = new InforShowPanel();
         m_inforShowPanel.setShow(m_show["inforShow"],m_versionControl,m_loadUI,this);
         if(m_currentShowAutomaticPetVO)
         {
            m_inforShowPanel.setData(m_currentShowAutomaticPetVO);
         }
      }
      
      public function closePanel() : void
      {
         this.m_changePanel.closecall();
         if(parent)
         {
            this.visible = false;
            parent.removeChild(this);
         }
         clear();
      }
      
      private function initStrengthPanelFrame() : void
      {
         hideBox();
         clearFrameCotent();
         m_showMC.gotoAndStop("Strengthen");
         m_strengthenPanel = new StrengthenPanel();
         m_strengthenPanel.setShow(m_show["strengthenShow"],this);
         m_strengthenPanel.setData(m_currentShowAutomaticPetVO,m_automaticPetsData);
      }
      
      private function initAdvancePanelFrame() : void
      {
         hideBox();
         clearFrameCotent();
         m_showMC.gotoAndStop("advance");
         m_advancePanel = new AdvancePanel();
         m_advancePanel.setShow(m_show["advanceShow"],this);
         m_advancePanel.setData(m_currentShowAutomaticPetVO,m_automaticPetsData);
      }
      
      private function clearFrameCotent() : void
      {
         ClearUtil.clearObject(m_inforShowPanel);
         m_inforShowPanel = null;
         ClearUtil.clearObject(m_strengthenPanel);
         m_strengthenPanel = null;
         ClearUtil.clearObject(m_advancePanel);
         m_advancePanel = null;
      }
      
      private function upDateChoosePetShow(param1:AutomaticPetVO) : void
      {
         m_currentShowAutomaticPetVO = param1;
         if(m_inforShowPanel)
         {
            m_inforShowPanel.setData(param1);
         }
         if(m_strengthenPanel)
         {
            m_strengthenPanel.setData(param1,m_automaticPetsData);
         }
         if(m_advancePanel)
         {
            m_advancePanel.setData(param1,m_automaticPetsData);
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         initShow();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_automaticPetDataXML = param1.resultXML;
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeAutomaticPetPanel();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,null);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function hideBox(param1:UIDataEvent = null) : void
      {
         if(_popUpBox)
         {
            if(getChildByName(_popUpBox.name))
            {
               removeChild(_popUpBox);
            }
            ClearUtil.clearObject(_popUpBox);
            _popUpBox = null;
         }
      }
      
      private function showBox(param1:UIDataEvent) : void
      {
         var _loc3_:EquipmentVO = param1.data.equipmentVO;
         var _loc2_:Player = SmallPackage.getInstance().currentSmallPackage == SmallPackage.getInstance().smallPackage_1 ? GamingUI.getInstance().player1 : GamingUI.getInstance().player2;
         if(Boolean(_popUpBox) && getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc3_,"shopWallPrice");
            _popUpBox.player = _loc2_;
            _popUpBox.fun = param1.data.fun;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
         }
         else if(!_popUpBox)
         {
            _popUpBox = new GuideBuyPopUpBox(_loc3_,_loc2_,"shopWallPrice",showWarningBox);
            _popUpBox.fun = param1.data.fun;
            _popUpBox.player = _loc2_;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            _popUpBox.x = (m_show.stage.stageWidth - _popUpBox.width) / 2;
            _popUpBox.y = (m_show.stage.stageHeight - _popUpBox.height) / 2;
            m_show.addChild(_popUpBox);
         }
         else if(Boolean(_popUpBox) && !getChildByName(_popUpBox.name))
         {
            _popUpBox.initBox(_loc3_,"shopWallPrice");
            _popUpBox.player = _loc2_;
            _popUpBox.fun = param1.data.fun;
            _popUpBox.reservePositionNum = param1.data.reservePositionNum;
            _popUpBox.x = (m_show.stage.stageWidth - _popUpBox.width) / 2;
            _popUpBox.y = (m_show.stage.stageHeight - _popUpBox.height) / 2;
            m_show.addChild(_popUpBox);
         }
      }
   }
}

