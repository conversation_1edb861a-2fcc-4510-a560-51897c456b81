package UI.Equipments.EquipmentVO
{
   import UI.UIInterface.ILimitEquipmentVO;
   import YJFY.Utils.ClearUtil;
   
   public class FashionEquipmentVO extends EquipmentVO implements ILimitEquipmentVO
   {
      
      private var _TimeLimit:Number = 0;
      
      private var _initTime:String;
      
      private var _remainingTime:int;
      
      private var _getServerTimeListener:GetServerTimeListenerForLimitEquipmentVO;
      
      public var addPlayerAttributes:Vector.<String> = new Vector.<String>();
      
      public var addPlayerAttributeValues:Vector.<Number> = new Vector.<Number>();
      
      public function FashionEquipmentVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         if(_getServerTimeListener)
         {
            _getServerTimeListener.removeLimitEquipmentVO(this);
         }
         ClearUtil.clearObject(_getServerTimeListener);
         _getServerTimeListener = null;
         var _loc2_:int = 0;
         if(addPlayerAttributes != null)
         {
            _loc1_ = int(addPlayerAttributes.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               addPlayerAttributes[_loc2_] = null;
               _loc2_++;
            }
            addPlayerAttributes = null;
         }
         if(addPlayerAttributeValues != null)
         {
            _loc1_ = int(addPlayerAttributeValues.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               addPlayerAttributeValues[_loc2_] = null;
               _loc2_++;
            }
            addPlayerAttributeValues = null;
         }
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.TimeLimit = _TimeLimit;
         _antiwear.initTime = _initTime;
         _antiwear.remainingTime = _remainingTime;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:FashionEquipmentVO = new FashionEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         super.cloneAttribute(param1);
         (param1 as FashionEquipmentVO).TimeLimit = this.TimeLimit;
         (param1 as FashionEquipmentVO).initTime = this.initTime;
         (param1 as FashionEquipmentVO).remainingTime = this.remainingTime;
         var _loc3_:GetServerTimeListenerForLimitEquipmentVO = this.getGetServerTimeListener();
         if(_loc3_)
         {
            _loc3_.addLimitEquipmentVO(param1 as ILimitEquipmentVO);
         }
         (param1 as FashionEquipmentVO).setGetServerTimeListener(_loc3_);
         _loc2_ = int(addPlayerAttributes.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            (param1 as FashionEquipmentVO).addPlayerAttributes.push(addPlayerAttributes[_loc4_]);
            _loc4_++;
         }
         _loc2_ = int(addPlayerAttributeValues.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            (param1 as FashionEquipmentVO).addPlayerAttributeValues.push(addPlayerAttributeValues[_loc4_]);
            _loc4_++;
         }
      }
      
      public function setGetServerTimeListener(param1:GetServerTimeListenerForLimitEquipmentVO) : void
      {
         _getServerTimeListener = param1;
      }
      
      public function getGetServerTimeListener() : GetServerTimeListenerForLimitEquipmentVO
      {
         return _getServerTimeListener;
      }
      
      public function get TimeLimit() : Number
      {
         return _antiwear.TimeLimit;
      }
      
      public function set TimeLimit(param1:Number) : void
      {
         _antiwear.TimeLimit = param1;
      }
      
      public function get initTime() : String
      {
         return _antiwear.initTime;
      }
      
      public function set initTime(param1:String) : void
      {
         _antiwear.initTime = param1;
      }
      
      public function get remainingTime() : int
      {
         return _antiwear.remainingTime;
      }
      
      public function set remainingTime(param1:int) : void
      {
         _antiwear.remainingTime = param1;
      }
   }
}

