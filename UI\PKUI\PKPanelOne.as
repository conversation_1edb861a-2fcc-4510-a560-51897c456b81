package UI.PKUI
{
   import UI.AutomaticPetPanel.AutomaticPetInforPanel;
   import UI.Button.QuitBtn2;
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   import UI.InformationPanel.InformationPanel;
   import UI.InformationPanel.PlayerInformationPanel;
   import UI.MySprite;
   import UI.PKUI.Button.PlayerOneSwitchBtn_PKPanelOne;
   import UI.PKUI.Button.PlayerTwoSwitchBtn_PKPanelOne;
   import UI.Pets.PetPanel;
   import UI.Players.Player;
   import UI.ShiTu.TuDiPanel;
   import UI2.Mount.MountUI.MountSmallPanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class PKPanelOne extends MySprite
   {
      
      public var quitBtn:QuitBtn2;
      
      public var isTwoMode:Boolean;
      
      private var _mountSmallPanel:MountSmallPanel;
      
      private var _playerInformationPanel:PlayerInformationPanel;
      
      private var _playerInformationPanelBack:InformationPanel;
      
      private var _petPanel:PetPanel;
      
      private var _tuDiPanel:TuDiPanel;
      
      private var _autoPetPanel:AutomaticPetInforPanel;
      
      private var _currentPlayers:Vector.<Player>;
      
      private var _currentPlayer:Player;
      
      private var _currentNicknameData:Object;
      
      private var _playerSwitchBtns:Vector.<SwitchBtn>;
      
      public function PKPanelOne()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:DisplayObject = null;
         stopDrag();
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
            if(_loc2_ is Bitmap)
            {
               if((_loc2_ as Bitmap).bitmapData)
               {
                  (_loc2_ as Bitmap).bitmapData.dispose();
               }
               (_loc2_ as Bitmap).bitmapData = null;
            }
            removeChildAt(0);
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(_playerInformationPanel)
         {
            _playerInformationPanel.clear();
         }
         if(_playerInformationPanelBack)
         {
            _playerInformationPanelBack.clear();
         }
         _playerInformationPanel = null;
         _playerInformationPanelBack = null;
         if(_petPanel)
         {
            _petPanel.clear();
         }
         _petPanel = null;
         if(_tuDiPanel)
         {
            _tuDiPanel.clear();
         }
         _tuDiPanel = null;
         ClearUtil.clearObject(_autoPetPanel);
         _autoPetPanel = null;
         ClearUtil.clearObject(_mountSmallPanel);
         _mountSmallPanel = null;
         _currentPlayers = null;
         _currentPlayer = null;
         _currentNicknameData = null;
         if(_playerSwitchBtns)
         {
            _loc1_ = int(_playerSwitchBtns.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_playerSwitchBtns[_loc3_])
               {
                  _playerSwitchBtns[_loc3_].clear();
               }
               _playerSwitchBtns[_loc3_] = null;
               _loc3_++;
            }
            _playerSwitchBtns = null;
         }
      }
      
      public function initPKPanel(param1:Vector.<Player>, param2:Object) : void
      {
         _currentPlayers = param1;
         _playerInformationPanel.visible = true;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         if(isTwoMode && _currentPlayers[1])
         {
            _playerSwitchBtns = new Vector.<SwitchBtn>(2);
            _playerSwitchBtns[0] = new PlayerOneSwitchBtn_PKPanelOne();
            _playerSwitchBtns[1] = new PlayerTwoSwitchBtn_PKPanelOne();
            _playerSwitchBtns[0].init(false);
            _playerSwitchBtns[1].init(true);
            _playerSwitchBtns[0].x = 85;
            _playerSwitchBtns[0].y = 140;
            _playerSwitchBtns[1].x = 85;
            _playerSwitchBtns[1].y = _playerSwitchBtns[0].y + _playerSwitchBtns[0].height - 7;
            addChild(_playerSwitchBtns[0]);
            addChild(_playerSwitchBtns[1]);
         }
         _currentPlayer = _currentPlayers[0];
         _currentNicknameData = param2;
         initPanelData(param1[0],param2);
      }
      
      private function initPanelData(param1:Player, param2:Object) : void
      {
         _playerInformationPanel.setPlayerPanel(param1);
         _playerInformationPanel.nickname = param2;
         _playerInformationPanel.nameText.text = param1.playerVO.playerUid;
         _petPanel.refresh(param1.playerVO.pet);
         _tuDiPanel.refresh(param1.playerVO.tuDiVO);
         _tuDiPanel.currentPlayer = param1;
         _autoPetPanel.setAutomaticPetVO(param1.playerVO.automaticPetVO);
         _mountSmallPanel.setPlayerVO(param1.playerVO);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickPlayerBtn",switchToPlayer,true,0,true);
         addEventListener("clickPetBtn",switchToPet,true,0,true);
         addEventListener("clickTuDiBtn",switchToTuDi,true,0,true);
         addEventListener("clickAutoPetBtn",switchToAutoPet,true,0,true);
         addEventListener("clickMountBtn",switchToMount,true,0,true);
         _playerInformationPanel.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _playerInformationPanel.addEventListener("mouseUp",mouseUpBox,false,0,true);
         _petPanel.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _petPanel.addEventListener("mouseUp",mouseUpBox,false,0,true);
         _tuDiPanel.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _tuDiPanel.addEventListener("mouseUp",mouseUpBox,false,0,true);
         _autoPetPanel.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _autoPetPanel.addEventListener("mouseUp",mouseUpBox,false,0,true);
         _mountSmallPanel.addEventListener("mouseDown",mouseDownBox,false,0,true);
         _mountSmallPanel.addEventListener("mouseUp",mouseUpBox,false,0,true);
         addEventListener("switchPlayerInPKPanelOne",switchTwoPlayer,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickPlayerBtn",switchToPlayer,true);
         removeEventListener("clickPetBtn",switchToPet,true);
         removeEventListener("clickTuDiBtn",switchToTuDi,true);
         removeEventListener("clickAutoPetBtn",switchToAutoPet,true);
         removeEventListener("clickMountBtn",switchToMount,true);
         _playerInformationPanel.removeEventListener("mouseDown",mouseDownBox,false);
         _playerInformationPanel.removeEventListener("mouseUp",mouseUpBox,false);
         _petPanel.removeEventListener("mouseDown",mouseDownBox,false);
         _petPanel.removeEventListener("mouseUp",mouseUpBox,false);
         _tuDiPanel.removeEventListener("mouseDown",mouseDownBox,false);
         _tuDiPanel.removeEventListener("mouseUp",mouseUpBox,false);
         _autoPetPanel.removeEventListener("mouseDown",mouseDownBox,false);
         _autoPetPanel.removeEventListener("mouseUp",mouseUpBox,false);
         _mountSmallPanel.removeEventListener("mouseDown",mouseDownBox,false);
         _mountSmallPanel.removeEventListener("mouseUp",mouseUpBox,false);
         removeEventListener("switchPlayerInPKPanelOne",switchTwoPlayer,true);
      }
      
      private function switchTwoPlayer(param1:UIBtnEvent) : void
      {
         if(param1.target == _playerSwitchBtns[0])
         {
            _playerSwitchBtns[1].gotoTwoFrame();
            _currentPlayer = _currentPlayers[0];
            initPanelData(_currentPlayers[0],_currentNicknameData);
         }
         else if(param1.target == _playerSwitchBtns[1])
         {
            initPanelData(_currentPlayers[1],_currentNicknameData);
            _currentPlayer = _currentPlayers[1];
            _playerSwitchBtns[0].gotoTwoFrame();
         }
      }
      
      private function mouseDownBox(param1:MouseEvent) : void
      {
         startDrag();
         addEventListener("enterFrame",test,false,0,true);
      }
      
      private function mouseUpBox(param1:MouseEvent) : void
      {
         stopDrag();
         removeEventListener("enterFrame",test,false);
      }
      
      private function test(param1:Event) : void
      {
         if(stage == null)
         {
            return;
         }
         var _loc2_:Point = localToGlobal(new Point(mouseX,mouseY));
         if(_loc2_.x > stage.stageWidth)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            x -= 10;
         }
         if(_loc2_.x < 0)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            x += 10;
         }
         if(_loc2_.y > stage.stageHeight)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            y -= 10;
         }
         if(_loc2_.y < 0)
         {
            dispatchEvent(new MouseEvent("mouseUp"));
            y += 10;
         }
      }
      
      private function init() : void
      {
         var _loc1_:Bitmap = new Bitmap(new PKPanelOneBackgroundBitmapData());
         _loc1_.x = 125;
         _loc1_.y = 105;
         addChild(_loc1_);
         _playerInformationPanelBack = new InformationPanel();
         _playerInformationPanelBack.x = 50;
         _playerInformationPanelBack.y = 100;
         addChild(_playerInformationPanelBack);
         _playerInformationPanel = new PlayerInformationPanel();
         _playerInformationPanel.x = 100;
         _playerInformationPanel.y = 45;
         addChild(_playerInformationPanel);
         _petPanel = new PetPanel();
         _petPanel.x = 110;
         _petPanel.y = 45;
         addChild(_petPanel);
         _petPanel.isShowPet = false;
         _tuDiPanel = new TuDiPanel();
         _tuDiPanel.x = 140;
         _tuDiPanel.y = 120;
         addChild(_tuDiPanel);
         _autoPetPanel = new AutomaticPetInforPanel();
         _autoPetPanel.init();
         _autoPetPanel.x = 120;
         _autoPetPanel.y = 90;
         addChild(_autoPetPanel);
         _mountSmallPanel = new MountSmallPanel();
         _mountSmallPanel.init();
         _mountSmallPanel.x = 120;
         _mountSmallPanel.y = 90;
         addChild(_mountSmallPanel);
         quitBtn = new QuitBtn2();
         quitBtn.x = 462;
         quitBtn.y = 93;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
      }
      
      protected function switchToPlayer(param1:UIBtnEvent = null) : void
      {
         _playerInformationPanel.visible = true;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _playerInformationPanelBack.switchToPlayer(null);
      }
      
      public function switchToPet(param1:UIBtnEvent = null) : void
      {
         _playerInformationPanel.visible = false;
         _petPanel.visible = true;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _playerInformationPanelBack.switchToPet(null);
      }
      
      protected function switchToTuDi(param1:UIBtnEvent) : void
      {
         _playerInformationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = true;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = false;
         _playerInformationPanelBack.switchToTuDi(null);
      }
      
      protected function switchToAutoPet(param1:UIBtnEvent) : void
      {
         _playerInformationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = true;
         _mountSmallPanel.visible = false;
         _playerInformationPanelBack.switchToAutoPet(null);
      }
      
      protected function switchToMount(param1:UIBtnEvent) : void
      {
         _playerInformationPanel.visible = false;
         _petPanel.visible = false;
         _tuDiPanel.visible = false;
         _autoPetPanel.visible = false;
         _mountSmallPanel.visible = true;
         _playerInformationPanelBack.switchToMount(null);
      }
      
      public function get currentPlayer() : Player
      {
         return _currentPlayer;
      }
   }
}

