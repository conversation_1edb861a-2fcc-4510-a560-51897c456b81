package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   public class StopListener implements INextStopListener
   {
      
      public var stopFun:Function;
      
      public var stop2Fun:Function;
      
      public var extra:Object;
      
      public function StopListener()
      {
         super();
      }
      
      public function clear() : void
      {
         stopFun = null;
         stop2Fun = null;
         extra = null;
      }
      
      public function stop() : void
      {
         if(<PERSON><PERSON><PERSON>(stopFun))
         {
            stopFun();
         }
      }
      
      public function stop2(param1:AnimationShowPlayLogicShell) : void
      {
         if(<PERSON><PERSON><PERSON>(stop2Fun))
         {
            stop2Fun(param1);
         }
      }
   }
}

