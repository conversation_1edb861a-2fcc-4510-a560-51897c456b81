package YJFY.PKMode2.PKLogic
{
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIAbleRunInHurt;
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIRun;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.GameEntityListener;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjsListener;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjsListener;
   import YJFY.Logic.SkillContainer_Sort_RunAndAbleRunInHurt;
   import YJFY.Skill.Skill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class PlayerAIEntity implements IHaveActiveSkillForAIAbleRunInHurt, IHaveActiveSkillForAIRun
   {
      
      private var m_skillContainer_Sort_RunAndAbleRunInHurt:SkillContainer_Sort_RunAndAbleRunInHurt;
      
      private var m_playerXydzjsListener:PlayerXydzjsListener;
      
      private var m_playerGameEntityListener:GameEntityListener;
      
      private var m_petXydzjsListener:PetXydzjsListener;
      
      private var m_playerAI:PlayerAI;
      
      private var m_playerXydzjs:PlayerXydzjs;
      
      private var m_petXydzjs:PetXydzjs;
      
      public function PlayerAIEntity()
      {
         super();
         m_skillContainer_Sort_RunAndAbleRunInHurt = new SkillContainer_Sort_RunAndAbleRunInHurt();
         m_playerXydzjsListener = new PlayerXydzjsListener();
         m_playerXydzjsListener.createPetFun = createPet;
         m_playerXydzjsListener.clearPetFun = clearPet;
         m_playerXydzjsListener.postLinkPart1AndPart2SkillDataFun = postLinkPart1AndPart2SkillData_player;
         m_playerGameEntityListener = new GameEntityListener();
         m_playerGameEntityListener.addToWorldFun = playerAddToWorld;
         m_petXydzjsListener = new PetXydzjsListener();
         m_petXydzjsListener.postLinkPart1AndPart2SkillDataFun = postLinkPart1AndPart2SkillData_pet;
         m_playerAI = new PlayerAI();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_skillContainer_Sort_RunAndAbleRunInHurt);
         m_skillContainer_Sort_RunAndAbleRunInHurt = null;
         ClearUtil.clearObject(m_playerXydzjsListener);
         m_playerXydzjsListener = null;
         ClearUtil.clearObject(m_playerGameEntityListener);
         m_playerGameEntityListener = null;
         ClearUtil.clearObject(m_petXydzjsListener);
         m_petXydzjsListener = null;
         ClearUtil.clearObject(m_playerAI);
         m_playerAI = null;
         m_playerXydzjs = null;
         m_petXydzjs = null;
      }
      
      public function setPlayerXydzjs(param1:PlayerXydzjs) : void
      {
         if(m_petXydzjs)
         {
            m_petXydzjs.removePetXydzjsListener(m_petXydzjsListener);
         }
         m_petXydzjs = null;
         if(m_playerXydzjs)
         {
            m_playerXydzjs.removePlayerXydzjsListener(m_playerXydzjsListener);
            m_playerXydzjs.removeGameEntityListener(m_playerGameEntityListener);
         }
         m_playerXydzjs = null;
         m_playerXydzjs = param1;
         if(m_playerXydzjs)
         {
            m_playerXydzjs.addPlayerXydzjsListener(m_playerXydzjsListener);
            m_playerXydzjs.addGameEntityListener(m_playerGameEntityListener);
         }
         if(m_playerXydzjs.getPet())
         {
            m_petXydzjs = m_playerXydzjs.getPet() as PetXydzjs;
            m_petXydzjs.addPetXydzjsListener(m_petXydzjsListener);
         }
         if(m_playerXydzjs)
         {
            postLinkPart1AndPart2SkillData();
         }
      }
      
      public function render() : void
      {
         m_playerAI.render();
      }
      
      private function postLinkPart1AndPart2SkillData() : void
      {
         m_skillContainer_Sort_RunAndAbleRunInHurt.statAndSortSkill(m_playerXydzjs.getAnimalEntity(),m_petXydzjs ? m_petXydzjs.getAnimalEntity() : null);
      }
      
      private function postLinkPart1AndPart2SkillData_player(param1:PlayerXydzjs) : void
      {
         postLinkPart1AndPart2SkillData();
      }
      
      private function postLinkPart1AndPart2SkillData_pet(param1:PetXydzjs) : void
      {
         postLinkPart1AndPart2SkillData();
      }
      
      private function createPet(param1:PlayerXydzjs, param2:PetXydzjs) : void
      {
         if(m_petXydzjs)
         {
            m_petXydzjs.removePetXydzjsListener(m_petXydzjsListener);
         }
         m_petXydzjs = null;
         m_petXydzjs = m_playerXydzjs.getPet() as PetXydzjs;
         if(m_petXydzjs)
         {
            m_petXydzjs.addPetXydzjsListener(m_petXydzjsListener);
         }
      }
      
      private function clearPet(param1:PlayerXydzjs) : void
      {
         createPet(param1,null);
      }
      
      private function playerAddToWorld(param1:GameEntity, param2:World) : void
      {
         m_playerAI.init(m_playerXydzjs.getAnimalEntity(),m_playerXydzjs.getAnimalEntity().getWorld(),this);
      }
      
      public function getPlayerXydzjs() : PlayerXydzjs
      {
         return m_playerXydzjs;
      }
      
      public function getRebuildAllFoeInterval() : uint
      {
         return 2000;
      }
      
      public function getActiveSkillNumOfAbleRunInHurt() : uint
      {
         return m_skillContainer_Sort_RunAndAbleRunInHurt.getActiveSkillNumOfAbleRunInHurt();
      }
      
      public function getActiveSkillOfAbleRunInHurtByIndex(param1:uint) : Skill
      {
         return m_skillContainer_Sort_RunAndAbleRunInHurt.getActiveSkillOfAbleRunInHurtByIndex(param1);
      }
      
      public function getActiveSkillNumOfRun() : uint
      {
         return m_skillContainer_Sort_RunAndAbleRunInHurt.getActiveSkillNumOfRun();
      }
      
      public function getActiveSkillOfRunByIndex(param1:uint) : Skill
      {
         return m_skillContainer_Sort_RunAndAbleRunInHurt.getActiveSkillOfRunByIndex(param1);
      }
      
      public function runSkill(param1:Skill) : void
      {
         m_playerXydzjs.runSkill(param1);
         if(m_petXydzjs)
         {
            m_petXydzjs.runSkill(param1);
         }
      }
      
      public function isAbleRunSkill(param1:Skill) : Boolean
      {
         if(m_playerXydzjs.isAbleRunSkill(param1))
         {
            return true;
         }
         if(Boolean(m_petXydzjs) && m_petXydzjs.isAbleRunSkill(param1))
         {
            return true;
         }
         return false;
      }
      
      public function runSuper() : void
      {
         m_playerXydzjs.pressSuperKey();
      }
   }
}

