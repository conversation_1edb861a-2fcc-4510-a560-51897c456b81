package UI2.broadcast
{
   import UI.GamingUI;
   import YJFY.API_4399.API_4399;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.GameEvent;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   
   public class GetDataFunction
   {
      
      private static var _instance:GetDataFunction;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      public var rankidlist:Vector.<int> = Vector.<int>([1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841]);
      
      private var m_index:int;
      
      public var bIsPK:Boolean = false;
      
      public var bIsRank:Boolean = false;
      
      private var m_4399API:API_4399;
      
      public function GetDataFunction()
      {
         super();
      }
      
      public static function getInstance() : GetDataFunction
      {
         if(_instance == null)
         {
            _instance = new GetDataFunction();
         }
         return _instance;
      }
      
      public function initParams() : void
      {
         delParams();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.rankListErrorFun = rankListError1;
         m_rankListAPIListener.getRankListsDataFun = getPlayerRankInfoSuccess;
         GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
      }
      
      public function delParams() : void
      {
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
      }
      
      public function clear() : void
      {
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(rankidlist);
         rankidlist = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
      }
      
      public function set4399API(param1:API_4399) : void
      {
         m_4399API = param1;
      }
      
      public function getRankListData(param1:int, param2:uint, param3:int, param4:int) : void
      {
         if(bIsPK)
         {
            return;
         }
         if(GamingUI.getInstance().getAPI4399() == null || GamingUI.getInstance().getAPI4399().rankListAPI == null || Part1.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            return;
         }
         m_index = param1;
         GamingUI.getInstance().getAPI4399().rankListAPI.getRankListsData(param2,param4,param3);
      }
      
      private function rankListError1(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
      }
      
      private function getPlayerRankInfoSuccess(param1:Vector.<UserDataInRankList>) : void
      {
         if(bIsRank)
         {
            return;
         }
         BroadDataManager.getInstance().addData(param1);
         if(m_index <= rankidlist.length - 2)
         {
            getRankListData(m_index + 1,rankidlist[m_index + 1],1,30);
         }
         else
         {
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("alltip"));
         }
      }
   }
}

