package YJFY.XydzjsLogic.DropAndPickUp
{
   import UI.DataManagerParent;
   import UI2.ProgramStartData.ProgramStartData;
   
   public class PickUpData extends DataManagerParent
   {
      
      private var m_addMp_moPing:uint;
      
      private var m_addMoney_yuanBao:uint;
      
      private var m_addMoney_yuanBao_10000:uint;
      
      private var m_addMoney_yuanBao_50000:uint;
      
      private var m_addMoney_yuanBao_100000:uint;
      
      private var m_addHp_taoZi:uint;
      
      private var m_addZHH_1:uint;
      
      private var m_addStN_10:uint;
      
      public function PickUpData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function get_addMp_moPing() : uint
      {
         return addMp_moPing;
      }
      
      public function get_addMoney_yuanBao() : uint
      {
         return addMoney_yuanBao;
      }
      
      public function get_addMoney_yuanBao_10000() : uint
      {
         return addMoney_yuanBao_10000;
      }
      
      public function get_addMoney_yuanBao_50000() : uint
      {
         return addMoney_yuanBao_50000;
      }
      
      public function get_addMoney_yuanBao_100000() : uint
      {
         return addMoney_yuanBao_100000;
      }
      
      public function get_addHp_taoZi() : uint
      {
         return addHp_taoZi;
      }
      
      public function get_addZHH_1() : uint
      {
         return addZHH_1;
      }
      
      public function get_addStN_10() : uint
      {
         return addStN_10;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.addMp_moPing = m_addMp_moPing;
         _antiwear.addMoney_yuanBao = m_addMoney_yuanBao;
         _antiwear.addMoney_yuanBao_10000 = m_addMoney_yuanBao_10000;
         _antiwear.addMoney_yuanBao_50000 = m_addMoney_yuanBao_50000;
         _antiwear.addMoney_yuanBao_100000 = m_addMoney_yuanBao_100000;
         _antiwear.addHp_taoZi = m_addHp_taoZi;
         _antiwear.addZHH_1 = m_addZHH_1;
         _antiwear.addStN_10 = m_addStN_10;
         addMp_moPing = ProgramStartData.getInstance().get_ten() * ProgramStartData.getInstance().get_ten();
         addMoney_yuanBao = ProgramStartData.getInstance().get_ten() * ProgramStartData.getInstance().get_ten();
         addMoney_yuanBao_10000 = Math.pow(ProgramStartData.getInstance().get_ten(),ProgramStartData.getInstance().get_four());
         addMoney_yuanBao_50000 = ProgramStartData.getInstance().get_five() * Math.pow(ProgramStartData.getInstance().get_ten(),ProgramStartData.getInstance().get_four());
         addMoney_yuanBao_100000 = Math.pow(ProgramStartData.getInstance().get_ten(),ProgramStartData.getInstance().get_five());
         addHp_taoZi = ProgramStartData.getInstance().get_ten() * ProgramStartData.getInstance().get_ten();
         addZHH_1 = ProgramStartData.getInstance().get_one();
         addStN_10 = ProgramStartData.getInstance().get_ten();
      }
      
      private function get addMp_moPing() : uint
      {
         return _antiwear.addMp_moPing;
      }
      
      private function set addMp_moPing(param1:uint) : void
      {
         _antiwear.addMp_moPing = param1;
      }
      
      private function get addMoney_yuanBao() : uint
      {
         return _antiwear.addMoney_yuanBao;
      }
      
      private function set addMoney_yuanBao(param1:uint) : void
      {
         _antiwear.addMoney_yuanBao = param1;
      }
      
      private function get addMoney_yuanBao_10000() : uint
      {
         return _antiwear.addMoney_yuanBao_10000;
      }
      
      private function set addMoney_yuanBao_10000(param1:uint) : void
      {
         _antiwear.addMoney_yuanBao_10000 = param1;
      }
      
      private function get addMoney_yuanBao_50000() : uint
      {
         return _antiwear.addMoney_yuanBao_50000;
      }
      
      private function set addMoney_yuanBao_50000(param1:uint) : void
      {
         _antiwear.addMoney_yuanBao_50000 = param1;
      }
      
      private function get addMoney_yuanBao_100000() : uint
      {
         return _antiwear.addMoney_yuanBao_100000;
      }
      
      private function set addMoney_yuanBao_100000(param1:uint) : void
      {
         _antiwear.addMoney_yuanBao_100000 = param1;
      }
      
      private function get addHp_taoZi() : uint
      {
         return _antiwear.addHp_taoZi;
      }
      
      private function set addHp_taoZi(param1:uint) : void
      {
         _antiwear.addHp_taoZi = param1;
      }
      
      private function get addZHH_1() : uint
      {
         return _antiwear.addZHH_1;
      }
      
      private function set addZHH_1(param1:uint) : void
      {
         _antiwear.addZHH_1 = param1;
      }
      
      private function get addStN_10() : uint
      {
         return _antiwear.addStN_10;
      }
      
      private function set addStN_10(param1:uint) : void
      {
         _antiwear.addStN_10 = param1;
      }
   }
}

