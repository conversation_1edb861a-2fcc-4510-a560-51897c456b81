package UI.RecaptureGold
{
   import UI.RecaptureGold.Parent.IProp;
   import UI.RecaptureGold.Props.IOneGameLevelProp;
   import UI.RecaptureGold.Props.Prop_Bomb;
   import UI.RecaptureGold.Props.Prop_DiamondToGoldBook;
   import UI.RecaptureGold.Props.Prop_LuckGrass;
   import UI.RecaptureGold.Props.Prop_StrengthPotion;
   
   public class PlayerPropVO
   {
      
      private var _props:Vector.<IProp>;
      
      public function PlayerPropVO()
      {
         super();
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _loc1_ = _props ? _props.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_props[_loc2_])
            {
               _props[_loc2_].clear();
            }
            _props[_loc2_] = null;
            _loc2_++;
         }
         _props = null;
      }
      
      public function removeOneGameLevelProp() : void
      {
         var _loc1_:int = 0;
         if(!_props)
         {
            return;
         }
         _loc1_ = 0;
         while(_loc1_ < _props.length)
         {
            if(_props[_loc1_] is IOneGameLevelProp)
            {
               _props.splice(_loc1_,1);
               _loc1_--;
            }
            _loc1_++;
         }
      }
      
      public function addProp(param1:IProp, param2:XML) : Boolean
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         if(param1 is Prop_Bomb)
         {
            _loc4_ = _props ? _props.length : 0;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(_props[_loc5_] is Prop_Bomb)
               {
                  _loc3_++;
               }
               _loc5_++;
            }
            if(_loc3_ >= int(param2.PlayerData[0].@enbleHaveBombNum))
            {
               return false;
            }
            if(!_props)
            {
               _props = new Vector.<IProp>();
            }
            _props.push(param1);
            return true;
         }
         if(!_props)
         {
            _props = new Vector.<IProp>();
         }
         _props.push(param1);
         return true;
      }
      
      public function removeAllProps() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = _props ? _props.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_props[_loc2_])
            {
               _props[_loc2_] = null;
            }
            _loc2_++;
         }
         _props = null;
      }
      
      public function judeIsHaveBomb() : int
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:int = _props ? _props.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_props[_loc3_] is Prop_Bomb)
            {
               _loc1_++;
            }
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function judeIsHaveLuckGrass() : Prop_LuckGrass
      {
         var _loc2_:int = 0;
         var _loc1_:int = _props ? _props.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_props[_loc2_] is Prop_LuckGrass)
            {
               return _props[_loc2_] as Prop_LuckGrass;
            }
            _loc2_++;
         }
         return null;
      }
      
      public function judeIsHaveDiamondToBook() : Prop_DiamondToGoldBook
      {
         var _loc2_:int = 0;
         var _loc1_:int = _props ? _props.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_props[_loc2_] is Prop_DiamondToGoldBook)
            {
               return _props[_loc2_] as Prop_DiamondToGoldBook;
            }
            _loc2_++;
         }
         return null;
      }
      
      public function judeIsHaveStrengthPotion() : Prop_StrengthPotion
      {
         var _loc2_:int = 0;
         var _loc1_:int = _props ? _props.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_props[_loc2_] is Prop_StrengthPotion)
            {
               return _props[_loc2_] as Prop_StrengthPotion;
            }
            _loc2_++;
         }
         return null;
      }
      
      public function usePropBomb() : Prop_Bomb
      {
         var _loc3_:int = 0;
         var _loc1_:Prop_Bomb = null;
         var _loc2_:int = _props ? _props.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_props[_loc3_] is Prop_Bomb)
            {
               _loc1_ = _props[_loc3_] as Prop_Bomb;
               _props.splice(_loc3_,1);
               break;
            }
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function get props() : Vector.<IProp>
      {
         return _props;
      }
   }
}

