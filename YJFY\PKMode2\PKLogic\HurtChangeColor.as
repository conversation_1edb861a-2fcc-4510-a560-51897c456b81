package YJFY.PKMode2.PKLogic
{
   import UI.EnterFrameTime;
   import flash.display.Sprite;
   import flash.geom.ColorTransform;
   
   public class HurtChangeColor
   {
      
      private var m_recoverElapsedTime:Number;
      
      private var m_show:Sprite;
      
      private var m_oldColorTransform:ColorTransform;
      
      public function HurtChangeColor()
      {
         super();
      }
      
      public function clear() : void
      {
         m_show = null;
         m_oldColorTransform = null;
      }
      
      public function changeColor(param1:Sprite, param2:EnterFrameTime) : void
      {
         m_show = param1;
         m_oldColorTransform = param1.transform.colorTransform;
         var _loc3_:ColorTransform = param1.transform.colorTransform;
         _loc3_.redOffset += 200;
         param1.transform.colorTransform = _loc3_;
         m_recoverElapsedTime = param2.getGameTimeForThisInit() + 200;
      }
      
      public function recover() : void
      {
         m_show.transform.colorTransform = m_oldColorTransform;
         m_show = null;
      }
      
      public function getRecoverElapsedTime() : Number
      {
         return m_recoverElapsedTime;
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
   }
}

