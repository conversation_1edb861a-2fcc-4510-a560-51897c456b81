package UI.WorldBoss.ShakeView
{
   import UI.WorldBoss.View;
   import flash.geom.Point;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class ShakeView1 extends ShakeView
   {
      
      private var _intervalForShake:uint;
      
      private var _times:uint;
      
      private var _offset:uint;
      
      private var _speed:uint;
      
      private var _point:Point;
      
      public function ShakeView1()
      {
         super();
         _times = 2;
         _offset = 1;
         _speed = 32;
      }
      
      override public function clear() : void
      {
         clearInterval(_intervalForShake);
         super.clear();
      }
      
      override public function shakeView(param1:View) : void
      {
         var offsetXYArray:Array;
         var num:int;
         var view:View = param1;
         super.shakeView(view);
         if(_isShake)
         {
            return;
         }
         _isShake = true;
         _isMyShake = true;
         view.setIsShake(true);
         _point = new Point(view.getShow().x,view.getShow().y);
         offsetXYArray = [0,0];
         num = 0;
         _intervalForShake = setInterval(function():void
         {
            offsetXYArray[num % 2] = num++ % 4 < 2 ? 0 : _offset;
            view.getShow().x = offsetXYArray[0] + _point.x;
            view.getShow().y = offsetXYArray[1] + _point.y;
            if(num > _times * 4 + 1)
            {
               clearInterval(_intervalForShake);
               num = 0;
               _isShake = false;
               view.setIsShake(false);
               recover();
            }
         },_speed);
      }
      
      public function setTimes(param1:uint) : void
      {
         _times = param1;
      }
      
      public function setOffset(param1:uint) : void
      {
         _offset = param1;
      }
      
      public function setSpeed(param1:uint) : void
      {
         _speed = param1;
      }
      
      override protected function recover() : void
      {
         if(_isMyShake)
         {
            _view.getShow().x = _point.x;
            _view.getShow().y = _point.y;
         }
         super.recover();
      }
   }
}

