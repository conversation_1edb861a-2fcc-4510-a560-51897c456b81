package UI.Farm.MouseManager
{
   import UI.Farm.Farm;
   import UI.MyFunction2;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   import flash.ui.Mouse;
   
   public class MouseManager extends EventDispatcher
   {
      
      public static const NORMAL:String = "normal";
      
      public static const MOVE:String = "move";
      
      public static const CHAGE_MOUSE_STATE:String = "changeMouseState";
      
      public static var _instance:MouseManager = null;
      
      private var _farm:Farm;
      
      private var _mouseShow:Sprite;
      
      private var _state:String;
      
      public function MouseManager()
      {
         super();
         if(!_instance)
         {
            _state = "normal";
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : MouseManager
      {
         if(!_instance)
         {
            _instance = new MouseManager();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         Mouse.show();
         if(Boolean(_mouseShow) && _farm.getChildByName(_mouseShow.name))
         {
            _farm.removeChild(_mouseShow);
         }
         _mouseShow = null;
         if(_farm)
         {
            _farm.removeEventListener("mouseMove",onMouseMove,false);
         }
         _farm = null;
         _instance = null;
      }
      
      public function set farm(param1:Farm) : void
      {
         _farm = param1;
         _farm.addEventListener("mouseMove",onMouseMove,false,0,true);
      }
      
      public function get state() : String
      {
         return _state;
      }
      
      public function setMouseState(param1:String) : void
      {
         if(_state == param1)
         {
            return;
         }
         _state = param1;
         dispatchEvent(new Event("changeMouseState"));
         if(Boolean(_mouseShow) && _farm.getChildByName(_mouseShow.name))
         {
            _farm.removeChild(_mouseShow);
         }
         switch(param1)
         {
            case "normal":
               Mouse.show();
               _mouseShow = null;
               break;
            case "move":
               Mouse.hide();
               if(!_mouseShow)
               {
                  _mouseShow = MyFunction2.returnShowByClassName("MouseMove") as Sprite;
               }
               _mouseShow.mouseChildren = false;
               _mouseShow.mouseEnabled = false;
               _farm.addChildAt(_mouseShow,_farm.numChildren);
         }
      }
      
      private function onMouseMove(param1:MouseEvent) : void
      {
         if(_mouseShow)
         {
            _mouseShow.x = _farm.mouseX;
            _mouseShow.y = _farm.mouseY;
         }
      }
   }
}

