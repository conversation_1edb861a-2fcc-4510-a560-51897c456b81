package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   public class NextStopListener implements INextStopListener
   {
      
      public var stopFun:Function;
      
      public var stop2Fun:Function;
      
      public var target:AnimationShowPlayLogicShell;
      
      public var extra:Object;
      
      public function NextStopListener()
      {
         super();
      }
      
      public function clear() : void
      {
         stopFun = null;
         stop2Fun = null;
         target = null;
         extra = null;
      }
      
      public function stop() : void
      {
         if(Bo<PERSON>an(stopFun))
         {
            stopFun();
         }
      }
      
      public function stop2(param1:AnimationShowPlayLogicShell) : void
      {
         if(<PERSON><PERSON><PERSON>(stop2Fun))
         {
            stop2Fun(param1);
         }
         target.removeNextStopListener(this);
         clear();
      }
   }
}

