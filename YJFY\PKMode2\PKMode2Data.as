package YJFY.PKMode2
{
   import UI.DataManagerParent;
   import UI.WorldBoss.GetWeekNum;
   import UI.WorldBoss.WeekData;
   
   public class PKMode2Data extends DataManagerParent
   {
      
      private var m_pkNumOneDay:uint;
      
      private var m_refrenceTime:String;
      
      private var m_resetTimeLong:Number;
      
      private var m_currentRankId:int;
      
      private var m_rankId1:int;
      
      private var m_rankId2:int;
      
      public function PKMode2Data()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_refrenceTime = null;
         super.clear();
      }
      
      public function initByXML(param1:XML) : void
      {
         this.pkNumOneDay = uint(param1.@pkNumOneDay);
         this.refrenceTime = String(param1.@refrenceTime);
         this.resetTimeLong = Number(param1.@reSetTimeLong);
         this.rankId1 = int(param1.@rankId1);
         this.rankId2 = int(param1.@rankId2);
      }
      
      public function getPKNumOneDay() : uint
      {
         return pkNumOneDay;
      }
      
      public function getRefrenceTime() : String
      {
         return refrenceTime;
      }
      
      public function getResetTimeLong() : Number
      {
         return resetTimeLong;
      }
      
      public function getCurrentRankId(param1:String, param2:Boolean) : int
      {
         var _loc3_:WeekData = new WeekData();
         new GetWeekNum().getWeekNum(refrenceTime,param1,resetTimeLong,_loc3_);
         if(param2)
         {
            m_currentRankId = int(this["rankId" + (_loc3_.cycleNum % 2 + 1)]);
         }
         else
         {
            m_currentRankId = int(this["rankId" + (_loc3_.cycleNum % 2 == 0 ? 2 : 1)]);
         }
         return m_currentRankId;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.pkNumOneDay = m_pkNumOneDay;
         _antiwear.refrenceTime = m_refrenceTime;
         _antiwear.resetTimeLong = m_resetTimeLong = 0;
         _antiwear.rankId1 = m_rankId1;
         _antiwear.rankId2 = m_rankId2;
         pkNumOneDay = 0;
         refrenceTime = "";
      }
      
      private function get pkNumOneDay() : uint
      {
         return _antiwear.pkNumOneDay;
      }
      
      private function set pkNumOneDay(param1:uint) : void
      {
         _antiwear.pkNumOneDay = param1;
      }
      
      private function get refrenceTime() : String
      {
         return _antiwear.refrenceTime;
      }
      
      private function set refrenceTime(param1:String) : void
      {
         _antiwear.refrenceTime = param1;
      }
      
      private function get resetTimeLong() : Number
      {
         return _antiwear.resetTimeLong;
      }
      
      private function set resetTimeLong(param1:Number) : void
      {
         _antiwear.resetTimeLong = param1;
      }
      
      private function get rankId1() : int
      {
         return _antiwear.rankId1;
      }
      
      private function set rankId1(param1:int) : void
      {
         _antiwear.rankId1 = param1;
      }
      
      private function get rankId2() : int
      {
         return _antiwear.rankId2;
      }
      
      private function set rankId2(param1:int) : void
      {
         _antiwear.rankId2 = param1;
      }
   }
}

