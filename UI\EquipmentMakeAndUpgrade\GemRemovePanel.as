package UI.EquipmentMakeAndUpgrade
{
   import UI.ConsumptionGuide.Button.BuyGoldPocketGuideBtn;
   import UI.ConsumptionGuide.Button.BuyMaterialGuideBtn;
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.InsetGemEquipmentVO;
   import UI.Equipments.StackEquipments.LuckStoneShow;
   import UI.Equipments.StackEquipments.StackEquipment;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.AbleDragSpriteLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.LogicShell.NumBtnGroupLogicShell.NumberBtnGroupLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import com.greensock.TweenLite;
   import com.greensock.easing.Back;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class GemRemovePanel extends MySprite implements IEquipmentProcessPanel
   {
      
      private var _show:MovieClip;
      
      private var _showMC:MovieClipPlayLogicShell;
      
      private var _oldContainer:Sprite;
      
      private var _materialContainer:Sprite;
      
      private var _luckStoneContainer:Sprite;
      
      private var _newContainer:Sprite;
      
      private var _numBtnGroup:NumberBtnGroupLogicShell;
      
      private var _successRateText:TextField;
      
      private var _moneyText:TextField;
      
      private var _haveNumText:TextField;
      
      private var _needNumText:TextField;
      
      private var _startRemoveBtn:ButtonLogicShell2;
      
      private var _successAnimation:MovieClipPlayLogicShell;
      
      private var _failAnimation:MovieClipPlayLogicShell;
      
      private var _wantLoadSources:Array = ["gemRemove"];
      
      private var _gemRemoveXML:XML;
      
      private var _oldEquipment:Equipment;
      
      private var _newEquipment:Equipment;
      
      private var _player:Player;
      
      private var _baseSuccessRate:Number;
      
      private var _successRate:Number;
      
      private var _needMoney:int;
      
      private var _needIndex:int;
      
      private var _holeIndex:int;
      
      private var _needMaterial:Equipment;
      
      private var _needMaterialNum:int;
      
      private var _currentRemoveXML:XML;
      
      private var _luckStoneShow:LuckStoneShow;
      
      private var _selectGemPanelShow:MovieClip;
      
      private var _selectGemPanel:MovieClipPlayLogicShell;
      
      private var _selectGemPanel_AbleDrag:AbleDragSpriteLogicShell;
      
      private var _showGems:Vector.<Equipment>;
      
      private var _gemContainers:SwitchBtnGroupLogicShell;
      
      private var _gemVO:EquipmentVO;
      
      private var _trueBtn:ButtonLogicShell2;
      
      private var _buyMaterialBtn:BuyMaterialGuideBtn;
      
      private var _buyLuckStoneGuideBtn:BuyMaterialGuideBtn;
      
      private var _buyGoldPocketBtn:BuyGoldPocketGuideBtn;
      
      private var _MyControlPanel:MyControlPanel;
      
      public function GemRemovePanel(param1:MyControlPanel = null)
      {
         super();
         _MyControlPanel = param1;
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         GamingUI.getInstance().loadQueue.unLoad(_wantLoadSources);
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         _show = null;
         if(_showMC)
         {
            _showMC.clear();
         }
         _showMC = null;
         ClearUtil.clearDisplayObjectInContainer(_oldContainer);
         _oldContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_materialContainer);
         _materialContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_luckStoneContainer);
         _luckStoneContainer = null;
         ClearUtil.clearDisplayObjectInContainer(_newContainer);
         _newContainer = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         _successRateText = null;
         _moneyText = null;
         _haveNumText = null;
         _needNumText = null;
         if(_startRemoveBtn)
         {
            _startRemoveBtn.clear();
         }
         _startRemoveBtn = null;
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         _successAnimation = null;
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         _failAnimation = null;
         ClearUtil.nullArr(_wantLoadSources);
         _wantLoadSources = null;
         _gemRemoveXML = null;
         if(_oldEquipment)
         {
            _oldEquipment.clear();
         }
         _oldEquipment = null;
         if(_newEquipment)
         {
            _newEquipment.clear();
         }
         _newEquipment = null;
         _player = null;
         if(_needMaterial)
         {
            _needMaterial.clear();
         }
         _needMaterial = null;
         _currentRemoveXML = null;
         _luckStoneShow = null;
         ClearUtil.clearDisplayObjectInContainer(_selectGemPanelShow);
         _selectGemPanelShow = null;
         if(_selectGemPanel)
         {
            _selectGemPanel.clear();
         }
         _selectGemPanel = null;
         if(_selectGemPanel_AbleDrag)
         {
            _selectGemPanel_AbleDrag.clear();
         }
         _selectGemPanel_AbleDrag = null;
         ClearUtil.nullArr(_showGems);
         _showGems = null;
         if(_gemContainers)
         {
            _gemContainers.clear();
         }
         _gemContainers = null;
         if(_trueBtn)
         {
            _trueBtn.clear();
         }
         _trueBtn = null;
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
         _gemVO = null;
      }
      
      public function isCanPutEquipmentVO(param1:EquipmentVO) : IsCanPutInfo
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:InsetGemEquipmentVO = null;
         var _loc2_:IsCanPutInfo = new IsCanPutInfo();
         _loc2_.message = "该装备没有镶嵌宝石，不能放入";
         _loc3_ = param1 as AbleEquipmentVO ? (param1 as AbleEquipmentVO).getHoleNum() : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = (param1 as AbleEquipmentVO).getInsetGem(_loc5_);
            if(_loc4_)
            {
               _loc2_.isCanPut = true;
               _loc2_.message = "";
               break;
            }
            _loc5_++;
         }
         return _loc2_;
      }
      
      private function init() : void
      {
         _luckStoneShow = new LuckStoneShow();
         var loadListener:LoadFinishListener1 = new LoadFinishListener1(function():void
         {
            MyFunction2.loadXMLFunction("gemRemove",function(param1:XML):void
            {
               _gemRemoveXML = param1;
               if(_show == null)
               {
                  _show = MyFunction2.returnShowByClassName("GemRemovePanel") as MovieClip;
               }
               if(_show == null)
               {
                  dispatchEvent(new UIPassiveEvent("showWarningBox",{
                     "text":"出错了",
                     "flag":0
                  }));
                  return;
               }
               addChild(_show);
               _showMC = new MovieClipPlayLogicShell();
               _showMC.setShow(_show);
               initNotPutInFrame();
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"加载失败！",
                  "flag":0
               }));
            });
         },null);
         GamingUI.getInstance().loadQueue.load(_wantLoadSources,loadListener);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("clickButton",clickButton,true);
      }
      
      public function putInEquipmentVO(param1:EquipmentVO, param2:Player) : void
      {
         if(param1 is AbleEquipmentVO)
         {
            _player = param2;
            _currentRemoveXML = null;
            clearOldEquipment();
            _oldEquipment = MyFunction2.sheatheEquipmentShell(param1);
            _oldEquipment.addEventListener("rollOver",onOver,false,0,true);
            _oldEquipment.addEventListener("rollOut",onOut,false,0,true);
            initPutInFrame();
            initShow1();
            return;
         }
      }
      
      public function putOutEquipmentVO(param1:EquipmentVO = null) : Vector.<EquipmentVO>
      {
         var _loc3_:EquipmentVO = null;
         var _loc2_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         initNotPutInFrame();
         _player = null;
         _loc3_ = _oldEquipment ? _oldEquipment.equipmentVO : null;
         clearOldEquipment();
         clearNewEquipment();
         clearMaterial();
         clearBuyBtn();
         if(_loc3_)
         {
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      private function initShowAfterSelectGem(param1:InsetGemEquipmentVO) : void
      {
         _gemVO = param1;
         var _loc3_:XML = getCurrentXML(_oldEquipment.equipmentVO,param1);
         _holeIndex = getHoleIndex(_oldEquipment.equipmentVO as AbleEquipmentVO,param1);
         if(_holeIndex == -1)
         {
            throw new Error();
         }
         _currentRemoveXML = _loc3_["hole" + (_holeIndex + 1)][0];
         if(_currentRemoveXML == null)
         {
            _currentRemoveXML = _loc3_["hole1"][0];
         }
         if(_oldEquipment.equipmentVO is PreciousEquipmentVO)
         {
            _needIndex = 1;
         }
         else
         {
            _needIndex = 0;
         }
         _baseSuccessRate = Number(_currentRemoveXML.@successRate);
         _successRate = _baseSuccessRate;
         _needMoney = int(_currentRemoveXML.@needMoney);
         _needMaterial = MyFunction2.sheatheEquipmentShell(XMLSingle.getEquipmentVOByID(_currentRemoveXML.material[_needIndex].@id,XMLSingle.getInstance().equipmentXML));
         _needMaterial.addEventListener("rollOver",onOver,false,0,true);
         _needMaterial.addEventListener("rollOut",onOut,false,0,true);
         _needMaterialNum = int(_currentRemoveXML.material[_needIndex].@num);
         clearNewEquipment();
         _newEquipment = _oldEquipment.clone();
         _newEquipment.addEventListener("rollOver",onOver,false,0,true);
         _newEquipment.addEventListener("rollOut",onOut,false,0,true);
         (_newEquipment.equipmentVO as AbleEquipmentVO).removeInsetGem((_newEquipment.equipmentVO as AbleEquipmentVO).getInsetGem(_holeIndex));
         _newEquipment.equipmentVO = _newEquipment.equipmentVO;
         (_newEquipment as DisplayObject).scaleX = (_newEquipment as DisplayObject).scaleY = 1.5;
         initGemSelectedFrame();
         initShow2();
         var _loc2_:uint = Math.ceil((1 - _successRate) / 0.1);
         changeSuccessRate2(_loc2_);
      }
      
      private function getCurrentXML(param1:EquipmentVO, param2:EquipmentVO) : XML
      {
         var _loc10_:int = 0;
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:XML = null;
         var _loc3_:* = null;
         var _loc6_:XMLList = _gemRemoveXML.equipment;
         _loc4_ = int(_loc6_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc4_)
         {
            _loc5_ = _loc6_[_loc10_];
            _loc7_ = int(_loc5_.@num);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(String(_loc5_["id" + (_loc8_ + 1)]) == param1.id.toString())
               {
                  _loc3_ = _loc5_;
                  break;
               }
               _loc8_++;
            }
            _loc10_++;
         }
         if(_loc3_ == null)
         {
            _loc3_ = _gemRemoveXML.defau[0];
         }
         _loc6_ = _loc3_.gem;
         var _loc9_:* = _loc3_;
         _loc3_ = null;
         _loc4_ = int(_loc6_.length());
         _loc10_ = 0;
         while(_loc10_ < _loc4_)
         {
            _loc5_ = _loc6_[_loc10_];
            _loc7_ = int(_loc5_.@num);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               if(String(_loc5_["id" + (_loc8_ + 1)]) == param2.id.toString())
               {
                  _loc3_ = _loc5_;
                  break;
               }
               _loc8_++;
            }
            _loc10_++;
         }
         if(_loc3_ == null)
         {
            _loc3_ = _loc9_.defau[0];
         }
         return _loc3_;
      }
      
      private function getHoleIndex(param1:AbleEquipmentVO, param2:InsetGemEquipmentVO) : int
      {
         var _loc3_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = param1.getHoleNum();
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(param1.getInsetGem(_loc5_) == param2)
            {
               return _loc5_;
            }
            _loc5_++;
         }
         return -1;
      }
      
      private function getHaveMaterialNum(param1:*, param2:Player) : int
      {
         var _loc3_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is Equipment)
         {
            _loc3_ = (param1 as Equipment).equipmentVO.id;
         }
         else
         {
            _loc3_ = param1;
         }
         var _loc4_:Vector.<EquipmentVO> = param2.playerVO.packageEquipmentVOs;
         var _loc5_:int = int(_loc4_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            if(_loc4_[_loc7_])
            {
               if(_loc4_[_loc7_].id == _loc3_)
               {
                  if(_loc4_[_loc7_] is StackEquipmentVO)
                  {
                     _loc6_ += (_loc4_[_loc7_] as StackEquipmentVO).num;
                  }
                  else
                  {
                     _loc6_++;
                  }
               }
            }
            _loc7_++;
         }
         return _loc6_;
      }
      
      private function initShow1() : void
      {
         _oldContainer.addChild(_oldEquipment as DisplayObject);
         clearSelectGemPanel();
         clearShowGems();
         createShowGems();
         createSelectGemPanel(_showGems);
      }
      
      private function initShow2() : void
      {
         _materialContainer.addChild(_needMaterial as DisplayObject);
         _luckStoneContainer.addChild(_luckStoneShow);
         _newContainer.addChild(_newEquipment as DisplayObject);
         _successRateText.text = int(_successRate * 100) + "%";
         _needNumText.text = _needMaterialNum.toString();
         clearBuyLuckStoneBtn();
         _buyLuckStoneGuideBtn = new BuyMaterialGuideBtn(10500000,setNumBtnGroupMaxNum,1);
         _buyLuckStoneGuideBtn.x = _numBtnGroup.getShow().x + (_numBtnGroup.getShow().width - _buyLuckStoneGuideBtn.width) / 2;
         _buyLuckStoneGuideBtn.y = _numBtnGroup.getShow().y + _numBtnGroup.getShow().height + 5;
         addChild(_buyLuckStoneGuideBtn);
         refreshMoneyShow();
         refreshMaterialNumShow();
      }
      
      private function refreshMoneyShow() : void
      {
         if(_moneyText == null)
         {
            return;
         }
         clearBuyGoldPocketBtn();
         var _loc1_:TextFormat = _moneyText.defaultTextFormat;
         if(_player.playerVO.money >= _needMoney)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            _buyGoldPocketBtn = new BuyGoldPocketGuideBtn(11100000,refreshMoneyShow,1);
         }
         _moneyText.defaultTextFormat = _loc1_;
         _moneyText.text = _needMoney.toString();
         _moneyText.width = _moneyText.textWidth + 5;
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.x = _moneyText.x + _moneyText.width + 2;
            _buyGoldPocketBtn.y = _moneyText.y;
            addChild(_buyGoldPocketBtn);
         }
      }
      
      private function refreshMaterialNumShow() : void
      {
         if(_needMaterial == null || _haveNumText == null)
         {
            return;
         }
         clearBuyMaterialBtn();
         var _loc2_:int = getHaveMaterialNum(_needMaterial,_player);
         var _loc1_:TextFormat = _haveNumText.defaultTextFormat;
         if(_loc2_ >= _needMaterialNum)
         {
            _loc1_.color = 65331;
         }
         else
         {
            _loc1_.color = 16711680;
            if(_needMaterial.equipmentVO.ticketPrice)
            {
               _buyMaterialBtn = new BuyMaterialGuideBtn(_needMaterial.equipmentVO.id,refreshMaterialNumShow,1);
               _buyMaterialBtn.x = _materialContainer.x - _buyMaterialBtn.width / 2;
               _buyMaterialBtn.y = _materialContainer.y + 25;
               addChild(_buyMaterialBtn);
            }
         }
         _haveNumText.defaultTextFormat = _loc1_;
         _haveNumText.text = _loc2_.toString();
      }
      
      private function setNumBtnGroupMaxNum() : void
      {
         if(_numBtnGroup)
         {
            _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         }
      }
      
      private function changeSuccessRate(param1:Number) : void
      {
         _successRate = Math.min(Math.max(0,param1),1);
         _successRateText.text = int(_successRate * 100) + "%";
      }
      
      private function changeSuccessRate2(param1:uint) : void
      {
         _numBtnGroup.num = param1;
         changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
      }
      
      private function start() : void
      {
         var isHaveSpace:Boolean;
         var gems:Vector.<EquipmentVO>;
         if(_oldEquipment == null)
         {
            return;
         }
         if(_needMaterialNum > getHaveMaterialNum(_needMaterial,_player))
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"材料不足！",
               "flag":0
            }));
            return;
         }
         if(_needMoney > _player.playerVO.money)
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"元宝不足！",
               "flag":0
            }));
            return;
         }
         gems = new Vector.<EquipmentVO>();
         gems.push(_gemVO);
         MyFunction2.falseAddEquipmentVOs(gems,_player,function():void
         {
            dispatchEvent(new UIPassiveEvent("showWarningBox",{
               "text":"背包空间不足！",
               "flag":0
            }));
            ClearUtil.nullArr(gems,false,false,false);
         },function():void
         {
            ClearUtil.nullArr(gems,false,false,false);
            MyFunction2.getUserStateIsRightFunction(function():void
            {
               var saveinfo:SaveTaskInfo;
               var r:Number = Math.random();
               _player.playerVO.money -= _needMoney;
               MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_needMaterialNum,_needMaterial.equipmentVO.id);
               MyFunction.getInstance().minusEquipmentVOs(_player.playerVO.packageEquipmentVOs,_numBtnGroup.num,10500000);
               clearBuyBtn();
               clearBuyLuckStoneBtn();
               clearBuyGoldPocketBtn();
               if(r <= _successRate)
               {
                  parent.parent.mouseChildren = false;
                  parent.parent.mouseEnabled = false;
                  MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_newEquipment.equipmentVO,_oldEquipment.equipmentVO);
                  MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_gemVO.clone());
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399";
                  saveinfo.isHaveData = false;
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame2();
                  initSuccessFrame();
                  equipmentsAnimation(function():void
                  {
                     _successAnimation.gotoAndPlay("start",null,null,function():void
                     {
                        dispatchEvent(new UIPassiveEvent("showWarningBox",{
                           "text":"摘除成功",
                           "flag":0
                        }));
                        TweenLite.to(_newEquipment,2,{
                           "alpha":0.5,
                           "x":600,
                           "y":100,
                           "ease":Back.easeIn,
                           "onComplete":function():void
                           {
                              _player = null;
                              clearNewEquipment();
                              clearOldEquipment();
                              clearMaterial();
                              clearGem();
                              initNotPutInFrame();
                              dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
                              parent.parent.mouseChildren = true;
                              parent.parent.mouseEnabled = true;
                           }
                        });
                     },null);
                  });
               }
               else
               {
                  MyFunction.getInstance().refreshEquipmentVOToEquipmentVOVector(_player.playerVO.packageEquipmentVOs,_oldEquipment.equipmentVO,_oldEquipment.equipmentVO);
                  saveinfo = new SaveTaskInfo();
                  saveinfo.type = "4399";
                  saveinfo.isHaveData = false;
                  SaveTaskList.getInstance().addData(saveinfo);
                  MyFunction2.saveGame2();
                  initFailFrame();
                  equipmentsAnimation(function():void
                  {
                     _failAnimation.gotoAndPlay("start",null,null,function():void
                     {
                        initNotPutInFrame();
                        clearOldEquipment();
                        clearNewEquipment();
                        clearMaterial();
                        clearGem();
                        _player = null;
                        dispatchEvent(new UIPassiveEvent("showWarningBox",{
                           "text":"摘除失败！",
                           "flag":0
                        }));
                        dispatchEvent(new UIPassiveEvent("refreshAtt",2 | 0x20));
                     });
                  });
               }
            },function():void
            {
               dispatchEvent(new UIPassiveEvent("showWarningBox",{
                  "text":"网络连接失败！",
                  "flag":0
               }));
            },true);
         },null,null,1);
      }
      
      private function initNotPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearGemSelectedFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("notPutIn");
      }
      
      private function clearNotPutInFrame() : void
      {
      }
      
      private function initPutInFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearGemSelectedFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("putIn");
         _oldContainer = _show["oldContainer"];
      }
      
      private function clearPutInFrame() : void
      {
         _oldContainer = null;
      }
      
      private function initGemSelectedFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearGemSelectedFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("gemSelected");
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
         _successRateText = _show["successRateText"];
         _moneyText = _show["moneyText"];
         _haveNumText = _show["haveNumText"];
         _needNumText = _show["needNumText"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_successRateText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_moneyText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_haveNumText,true);
         MyFunction2.changeTextFieldFont(_loc1_.fontName,_needNumText,true);
         _numBtnGroup = new NumberBtnGroupLogicShell();
         _numBtnGroup.setShow(_show["numBtnGroup"]);
         _numBtnGroup.maxNum = getHaveMaterialNum(10500000,_player);
         _startRemoveBtn = new ButtonLogicShell2();
         _startRemoveBtn.setShow(_show["startRemoveBtn"]);
         _startRemoveBtn.setTipString("点击开始摘除");
      }
      
      private function clearGemSelectedFrame() : void
      {
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
         _successRateText = null;
         _moneyText = null;
         if(_numBtnGroup)
         {
            _numBtnGroup.clear();
         }
         _numBtnGroup = null;
         if(_startRemoveBtn)
         {
            _startRemoveBtn.clear();
         }
         _startRemoveBtn = null;
      }
      
      private function initSuccessFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearGemSelectedFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("success");
         _successAnimation = new MovieClipPlayLogicShell();
         _successAnimation.setShow(_show["successAnimation"]);
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearSuccessFrame() : void
      {
         if(_successAnimation)
         {
            _successAnimation.clear();
         }
         _successAnimation = null;
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function initFailFrame() : void
      {
         clearNotPutInFrame();
         clearPutInFrame();
         clearGemSelectedFrame();
         clearSuccessFrame();
         clearFailFrame();
         _showMC.gotoAndStop("fail");
         _failAnimation = new MovieClipPlayLogicShell();
         _failAnimation.setShow(_show["failAnimation"]);
         _oldContainer = _show["oldContainer"];
         _materialContainer = _show["materialContainer"];
         _luckStoneContainer = _show["luckStoneContainer"];
         _newContainer = _show["newContainer"];
      }
      
      private function clearFailFrame() : void
      {
         if(_failAnimation)
         {
            _failAnimation.clear();
         }
         _failAnimation = null;
         _oldContainer = null;
         _materialContainer = null;
         _luckStoneContainer = null;
         _newContainer = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _numBtnGroup:
               changeSuccessRate(_baseSuccessRate + _numBtnGroup.num * 0.1);
               break;
            case _startRemoveBtn:
               start();
               break;
            case _trueBtn:
               initShowAfterSelectGem(_gemContainers.currentActivateBtn().data as InsetGemEquipmentVO);
               clearSelectGemPanel();
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":param1.currentTarget}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function equipmentsAnimation(param1:Function) : void
      {
         if(_oldEquipment)
         {
            TweenLite.to(_oldEquipment,2,{
               "alpha":0,
               "x":84,
               "y":58,
               "ease":Back.easeIn,
               "onComplete":param1
            });
         }
         if(_needMaterial)
         {
            TweenLite.to(_needMaterial,2,{
               "alpha":0,
               "x":-84,
               "y":58,
               "ease":Back.easeIn
            });
         }
      }
      
      private function clearOldEquipment() : void
      {
         if(_oldEquipment)
         {
            if((_oldEquipment as DisplayObject).parent)
            {
               (_oldEquipment as DisplayObject).parent.removeChild(_oldEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_oldEquipment);
            _oldEquipment.clear();
            _oldEquipment = null;
         }
      }
      
      private function clearNewEquipment() : void
      {
         if(_newEquipment)
         {
            if((_newEquipment as DisplayObject).parent)
            {
               (_newEquipment as DisplayObject).parent.removeChild(_newEquipment as DisplayObject);
            }
            TweenLite.killTweensOf(_newEquipment);
            _newEquipment.clear();
            _newEquipment = null;
         }
      }
      
      private function clearMaterial() : void
      {
         if(_needMaterial)
         {
            if((_needMaterial as DisplayObject).parent)
            {
               (_needMaterial as DisplayObject).parent.removeChild(_needMaterial as DisplayObject);
            }
            TweenLite.killTweensOf(_needMaterial);
            _needMaterial.clear();
            _needMaterial = null;
         }
      }
      
      private function createSelectGemPanel(param1:Vector.<Equipment>) : void
      {
         var _loc4_:int = 0;
         var _loc2_:* = null;
         if(_selectGemPanel)
         {
            throw new Error();
         }
         _selectGemPanelShow = MyFunction2.returnShowByClassName("ChoiceGemPanel") as MovieClip;
         _selectGemPanel = new MovieClipPlayLogicShell();
         _selectGemPanel.setShow(_selectGemPanelShow);
         _selectGemPanel_AbleDrag = new AbleDragSpriteLogicShell();
         _selectGemPanel_AbleDrag.setShow(_selectGemPanel.getShow());
         _selectGemPanel.getShow().x = 46;
         _selectGemPanel.getShow().y = 76;
         addChild(_selectGemPanel.getShow());
         var _loc3_:int = param1 ? param1.length : 0;
         if(_loc3_ == 0)
         {
            return;
         }
         _selectGemPanel.gotoAndStop("" + _loc3_);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_gemContainers == null)
            {
               _gemContainers = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
            }
            _gemContainers.addSwitchBtnShow(_selectGemPanel.getShow()["gemContainer" + (_loc4_ + 1)],param1[_loc4_].equipmentVO);
            if(param1[_loc4_])
            {
               _selectGemPanel.getShow()["gemContainer" + (_loc4_ + 1)].addChild(param1[_loc4_] as DisplayObject);
            }
            _loc4_++;
         }
         _gemContainers.addEnd();
         _trueBtn = new ButtonLogicShell2();
         _trueBtn.setShow(_selectGemPanel.getShow()["trueBtn"]);
         _trueBtn.setTipString("点击选择宝石");
      }
      
      private function clearSelectGemPanel() : void
      {
         if(_selectGemPanel)
         {
            if(_selectGemPanel.getShow().parent)
            {
               _selectGemPanel.getShow().parent.removeChild(_selectGemPanel.getShow());
            }
            _selectGemPanel.clear();
            _selectGemPanel = null;
            if(_gemContainers)
            {
               _gemContainers.clear();
            }
            _gemContainers = null;
            if(_trueBtn)
            {
               _trueBtn.clear();
            }
            _trueBtn = null;
         }
         if(_selectGemPanel_AbleDrag)
         {
            _selectGemPanel_AbleDrag.clear();
            _selectGemPanel_AbleDrag = null;
         }
      }
      
      private function createShowGems() : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:InsetGemEquipmentVO = null;
         var _loc3_:Equipment = null;
         clearShowGems();
         _loc2_ = _oldEquipment ? (_oldEquipment.equipmentVO as AbleEquipmentVO).getHoleNum() : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ = (_oldEquipment.equipmentVO as AbleEquipmentVO).getInsetGem(_loc4_);
            if(_loc1_)
            {
               _loc3_ = new StackEquipment(_loc1_);
               _loc3_.addEventListener("rollOver",onOver,false,0,true);
               _loc3_.addEventListener("rollOut",onOut,false,0,true);
               if(_showGems == null)
               {
                  _showGems = new Vector.<Equipment>();
               }
               _showGems.push(_loc3_);
            }
            _loc4_++;
         }
      }
      
      private function clearShowGems() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = _showGems ? _showGems.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(_showGems[_loc2_])
            {
               _showGems[_loc2_].equipmentVO = null;
               _showGems[_loc2_].clear();
               _showGems[_loc2_] = null;
            }
            _loc2_++;
         }
         _showGems = null;
      }
      
      private function clearGem() : void
      {
         _gemVO = null;
      }
      
      private function clearBuyMaterialBtn() : void
      {
         if(_buyMaterialBtn)
         {
            if(_buyMaterialBtn.parent)
            {
               _buyMaterialBtn.parent.removeChild(_buyMaterialBtn);
            }
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
      }
      
      private function clearBuyLuckStoneBtn() : void
      {
         if(_buyLuckStoneGuideBtn)
         {
            if(_buyLuckStoneGuideBtn.parent)
            {
               _buyLuckStoneGuideBtn.parent.removeChild(_buyLuckStoneGuideBtn);
            }
            _buyLuckStoneGuideBtn.clear();
            _buyLuckStoneGuideBtn = null;
         }
      }
      
      private function clearBuyBtn() : void
      {
         if(_buyMaterialBtn)
         {
            _buyMaterialBtn.clear();
         }
         _buyMaterialBtn = null;
         if(_buyLuckStoneGuideBtn)
         {
            _buyLuckStoneGuideBtn.clear();
         }
         _buyLuckStoneGuideBtn = null;
      }
      
      private function clearBuyGoldPocketBtn() : void
      {
         if(_buyGoldPocketBtn)
         {
            _buyGoldPocketBtn.clear();
         }
         _buyGoldPocketBtn = null;
      }
   }
}

