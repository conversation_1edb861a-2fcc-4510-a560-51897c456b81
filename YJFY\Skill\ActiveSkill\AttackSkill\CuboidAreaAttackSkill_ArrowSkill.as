package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.BulletEntity.ArrowEntity;
   import YJFY.Entity.BulletEntity.ArrowJuQiEntity;
   import YJFY.Entity.BulletEntity.BoomEntity;
   import YJFY.Entity.BulletEntity.BoomEntityListener;
   import YJFY.Entity.BulletEntity.BulletEntity;
   import YJFY.Entity.BulletEntity.BulletEntityData;
   import YJFY.Entity.BulletEntity.BulletEntityListener;
   import YJFY.Entity.BulletEntity.BulletHideListener;
   import YJFY.Entity.EntityFactory;
   import YJFY.Entity.IEntity;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class CuboidAreaAttackSkill_ArrowSkill extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      private var m_createBulletFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_bulletData:BulletEntityData;
      
      private var m_useBulletEntitys:Vector.<BulletEntity>;
      
      private var m_wasteBulletEntitys:Vector.<BulletEntity>;
      
      private var m_delBulletEntitys:Vector.<BulletEntity>;
      
      private var m_bulletEntityPool:ObjectsPool;
      
      private var m_bulletEntityListener:BulletEntityListener;
      
      private var m_boomEnetityListener:BulletEntityListener;
      
      private var m_bullethideListener:BulletHideListener;
      
      private var m_juqiListentener:BulletHideListener;
      
      private var m_useBoomEntitys:Vector.<BoomEntity>;
      
      private var m_wasteBoomEntitys:Vector.<BoomEntity>;
      
      private var m_delBoomEntitys:Vector.<BoomEntity>;
      
      private var m_delJuQiEntitys:Vector.<ArrowJuQiEntity>;
      
      private var m_bulletBoomPool:ObjectsPool;
      
      private var m_boomentityListener:BoomEntityListener;
      
      private var m_timeId:uint;
      
      private var m_arrowentity:ArrowEntity;
      
      public function CuboidAreaAttackSkill_ArrowSkill()
      {
         super();
         m_useBulletEntitys = new Vector.<BulletEntity>();
         m_wasteBulletEntitys = new Vector.<BulletEntity>();
         m_delBulletEntitys = new Vector.<BulletEntity>();
         m_delBoomEntitys = new Vector.<BoomEntity>();
         m_bulletEntityPool = new ObjectsPool(m_useBulletEntitys,m_wasteBulletEntitys,createBulletEntity2,null);
         m_bulletEntityListener = new BulletEntityListener();
         m_bulletEntityListener.attackSuccessFun = listenerBulletAttackSuccess;
         m_boomEnetityListener = new BulletEntityListener();
         m_boomEnetityListener.attackSuccessFun = listenerBoomAttackSuccess;
         m_bullethideListener = new BulletHideListener();
         m_bullethideListener.RemoveBulletFun = RemoveBullet;
         m_bullethideListener.ShowBoomFun = ShowBoom;
         m_juqiListentener = new BulletHideListener();
         m_juqiListentener.RemoveBulletFun = RemoveJuQi;
         m_delJuQiEntitys = new Vector.<ArrowJuQiEntity>();
         m_useBoomEntitys = new Vector.<BoomEntity>();
         m_wasteBoomEntitys = new Vector.<BoomEntity>();
         m_bulletBoomPool = new ObjectsPool(m_useBoomEntitys,m_wasteBoomEntitys,createBoomEntity2,null);
         m_boomentityListener = new BoomEntityListener();
         m_boomentityListener.RemoveBulletFun = RemoveBoom;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         clearTimeout(m_timeId);
         ClearUtil.clearObject(m_bulletData);
         m_bulletData = null;
         if(m_useBulletEntitys)
         {
            _loc1_ = m_useBulletEntitys ? m_useBulletEntitys.length : 0;
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_useBulletEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_useBulletEntitys[_loc2_]);
               m_useBulletEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_useBulletEntitys.length = 0;
            m_useBulletEntitys = null;
         }
         ClearUtil.clearObject(m_wasteBulletEntitys);
         m_wasteBulletEntitys = null;
         ClearUtil.clearObject(m_bulletEntityPool);
         m_bulletEntityPool = null;
         ClearUtil.clearObject(m_bulletEntityListener);
         m_bulletEntityListener = null;
         ClearUtil.clearObject(m_boomEnetityListener);
         m_boomEnetityListener = null;
         ClearUtil.clearObject(m_bullethideListener);
         m_bullethideListener = null;
         ClearUtil.clearObject(m_useBoomEntitys);
         m_useBoomEntitys = null;
         ClearUtil.clearObject(m_wasteBoomEntitys);
         m_wasteBoomEntitys = null;
         ClearUtil.clearObject(m_bulletBoomPool);
         m_bulletBoomPool = null;
         ClearUtil.clearObject(m_boomentityListener);
         m_boomentityListener = null;
         ClearUtil.clearObject(m_delBulletEntitys);
         m_delBulletEntitys = null;
         ClearUtil.clearObject(m_delBoomEntitys);
         m_delBoomEntitys = null;
         ClearUtil.clearObject(m_juqiListentener);
         m_juqiListentener = null;
         ClearUtil.clearObject(m_delJuQiEntitys);
         m_delJuQiEntitys = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_createBulletFrameLabel = String(param1.@createBulletFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_bulletData = new BulletEntityData(param1.bullet[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      private function createBulletEntity() : void
      {
         m_arrowentity = createBulletEntity2();
         m_arrowentity.setNewPosition(m_owner.getX(),m_owner.getY(),m_owner.getZ());
         m_arrowentity.currentAnimationGotoAndPlay("1");
         m_arrowentity.hideBodyShow();
         m_arrowentity.hideShadow();
         m_world.addEntity(m_arrowentity);
         m_arrowentity.moveBullet(m_owner.getShowDirection(),0);
         if(m_timeId > 0)
         {
            clearTimeout(m_timeId);
         }
         m_timeId = setTimeout(calldelay,100);
      }
      
      private function calldelay() : void
      {
         m_arrowentity.showBodyShow();
         m_arrowentity.showShadow();
      }
      
      private function createBulletEntity2() : ArrowEntity
      {
         var _loc1_:EntityFactory = new EntityFactory();
         var _loc2_:ArrowEntity = _loc1_.createEntity(m_bulletData.getBulletEntityXML().@animalType) as ArrowEntity;
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.addBulletEntityListener(m_bulletEntityListener);
         _loc2_.addBulletHideListener(m_bullethideListener);
         _loc2_.initByXML(m_bulletData.getBulletEntityXML());
         _loc2_.setShowDirection(m_owner.getShowDirection());
         ClearUtil.clearObject(_loc1_);
         return _loc2_;
      }
      
      private function createBoomEntity(param1:Number, param2:Number, param3:Number) : void
      {
         var _loc4_:BoomEntity = createBoomEntity2();
         _loc4_.setNewPosition(param1,param2,param3);
         _loc4_.getCurrentAnimation().gotoAndPlay("start");
         m_world.addEntity(_loc4_);
      }
      
      private function createBoomEntity2() : BoomEntity
      {
         var _loc1_:EntityFactory = new EntityFactory();
         var _loc2_:BoomEntity = _loc1_.createEntity(m_bulletData.getBulletEntityXML().BoomInfo[0].@animalType) as BoomEntity;
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.addBulletEntityListener(m_boomEnetityListener);
         _loc2_.addBulletHideListener(m_boomentityListener);
         _loc2_.initByXML(m_bulletData.getBulletEntityXML());
         ClearUtil.clearObject(_loc1_);
         return _loc2_;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_createBulletFrameLabel:
               createBulletEntity();
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
      
      protected function listenerBulletAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as ArrowEntity).setAttackData(m_attackData);
      }
      
      protected function listenerBoomAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as BoomEntity).setAttackData(m_attackData);
      }
      
      protected function RemoveBullet(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         m_delBulletEntitys.push(param3 as ArrowEntity);
      }
      
      protected function ShowBoom(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         createBoomEntity(param1.getX(),param1.getY(),param1.getZ());
      }
      
      protected function RemoveBoom(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         m_delBoomEntitys.push(param3 as BoomEntity);
      }
      
      protected function RemoveJuQi(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         m_delJuQiEntitys.push(param3 as ArrowJuQiEntity);
      }
   }
}

