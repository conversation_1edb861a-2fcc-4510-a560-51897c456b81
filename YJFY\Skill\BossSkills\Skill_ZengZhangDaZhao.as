package YJFY.Skill.BossSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_EveryTargetAddShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_ZengZhangDa<PERSON>hao extends CuboidAreaAttackSkill_EveryTargetAddShow2
   {
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      public function Skill_ZengZhangDaZhao()
      {
         super();
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function addEffectShowAddToTarget(param1:IEntity) : void
      {
         var _loc3_:AnimationShowPlayLogicShell = null;
         var _loc2_:int = 0;
         if(param1 is IAnimalEntity && !(param1 as AnimalEntity).notShowBeattack && (param1 as IAnimalEntity).isInDie() == false && !(param1 is TangMonkEntity))
         {
            _loc3_ = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc3_.extra = param1;
            (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect.push(param1);
            _loc3_.gotoAndPlay("start");
            _loc2_ = (param1 as AnimalEntity).isFly ? 2 : 1;
            (_loc3_.getShow() as AnimationShow).y = 0 - (m_effectAddtoTargetId != "KunBang" ? param1.getBodyZRange() : 0) * _loc2_;
            param1.addOtherAniamtion(_loc3_);
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         if(param1 is TangMonkEntity)
         {
            return;
         }
         super.attackSuccess(param1);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_bodyAttackReachFrameLabel === _loc2_)
         {
            m_world.shakeView(m_shakeViewDataOfPoints);
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

