package UI.WorldBoss.Boss.CreateOwnImageBoss
{
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.DieAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.PlayEndListener;
   import UI.WorldBoss.Boss.Boss;
   import YJFY.ShowLogicShell.PlayEndListener;
   
   public class ImageBoss extends Boss
   {
      
      private var m_roundNumOfCreate:uint;
      
      private var m_roundNumOfLife:uint;
      
      public function ImageBoss()
      {
         super();
      }
      
      public function setRoundNumOfCreate(param1:uint) : void
      {
         m_roundNumOfCreate = param1;
         trace("设置创建roundNUm:",m_roundNumOfCreate);
      }
      
      public function setRoundNumOfLife(param1:uint) : void
      {
         m_roundNumOfLife = param1;
         trace("设置生命roundNum:",m_roundNumOfLife);
      }
      
      public function cloneRealityAtt(param1:int, param2:int, param3:Number, param4:Number, param5:Number) : void
      {
         m_level = param1;
         m_attack = param2;
         m_criticalRate = param3;
         m_criticalMuti = param4;
         m_hit = param5;
         setLevelStr(m_level);
      }
      
      override protected function runAfterAttack(param1:AnimationData) : void
      {
         super.runAfterAttack(param1);
         if(m_world.getGetNextStepActionEnities().getRoundNum() > m_roundNumOfCreate + m_roundNumOfLife - 1)
         {
            m_world.removeEnemy(this);
            Die();
         }
      }
      
      override public function Die() : void
      {
         var _loc2_:DieAnimationData = new DieAnimationData();
         _loc2_.dieEntitiy = this;
         var _loc1_:UI.WorldBoss.AnimationQueueData.AnimationData.PlayEndListener = new UI.WorldBoss.AnimationQueueData.AnimationData.PlayEndListener();
         _loc1_.animationData = _loc2_;
         _loc1_.playEndFun = clear;
         _loc2_.addPlayEndListener(_loc1_);
         m_animationQueueData.addAnimationData(_loc2_);
      }
      
      public function playStartShowAnimation(param1:Function) : void
      {
         if(m_mc == null)
         {
            return;
         }
         var _loc3_:YJFY.ShowLogicShell.PlayEndListener = new YJFY.ShowLogicShell.PlayEndListener();
         _loc3_.mc = m_mc;
         _loc3_.playEndFun = param1;
         m_mc.addNextStopListener(_loc3_);
         var _loc2_:String = getLevelStr();
         m_mc.gotoAndPlay("showStart");
         setLevelStr(_loc2_);
      }
   }
}

