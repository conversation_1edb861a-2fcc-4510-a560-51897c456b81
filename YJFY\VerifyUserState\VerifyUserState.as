package YJFY.VerifyUserState
{
   import YJFY.API_4399.SaveAPI.SaveAPI;
   import YJFY.API_4399.SaveAPI.SaveAPIListener2;
   import YJFY.UserData.IUserData;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.external.ExternalInterface;
   
   public class VerifyUserState
   {
      
      private var m_verifyUserStateListeners:Vector.<IVerifyUserStateListener>;
      
      private var m_versionControl:IVersionControl;
      
      private var m_userData:IUserData;
      
      private var m_saveAPI:SaveAPI;
      
      private var m_saveAPIListener:SaveAPIListener2;
      
      public function VerifyUserState()
      {
         super();
         m_saveAPIListener = new SaveAPIListener2();
         m_saveAPIListener.multipleErrorFun = multipleError;
         m_saveAPIListener.noInMultipStateFun = noInMultipleState;
         m_saveAPIListener.multipleDataGetErrorFun = multipleDataGetError;
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_verifyUserStateListeners,false,false,false);
         m_verifyUserStateListeners = null;
         m_versionControl = null;
         m_userData = null;
         m_saveAPI = null;
         ClearUtil.clearObject(m_saveAPIListener);
         m_saveAPIListener = null;
      }
      
      public function setSaveAPI(param1:SaveAPI) : void
      {
         m_saveAPI = param1;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setUserData(param1:IUserData) : void
      {
         m_userData = param1;
      }
      
      public function VerifyUserSate() : void
      {
         if(checkUID())
         {
            checkIsLinkAndMultiple();
         }
         else
         {
            dispatchUserLoginOutError();
         }
      }
      
      public function addVerifyUserStateListener(param1:IVerifyUserStateListener) : void
      {
         if(m_verifyUserStateListeners == null)
         {
            m_verifyUserStateListeners = new Vector.<IVerifyUserStateListener>();
         }
         m_verifyUserStateListeners.push(param1);
      }
      
      public function removeVerifyUserStateListener(param1:IVerifyUserStateListener) : void
      {
         if(m_verifyUserStateListeners == null)
         {
            return;
         }
         var _loc2_:int = int(m_verifyUserStateListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_verifyUserStateListeners.splice(_loc2_,1);
            _loc2_ = int(m_verifyUserStateListeners.indexOf(param1));
         }
      }
      
      private function checkUID() : Boolean
      {
         var _loc1_:String = null;
         if(m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            return true;
         }
         _loc1_ = ExternalInterface.call("UniLogin.getUid");
         return _loc1_ == m_userData.getUserID();
      }
      
      private function checkIsLinkAndMultiple() : void
      {
         m_saveAPI.addSaveAPIListener(m_saveAPIListener);
         m_saveAPI.getStoreState();
      }
      
      private function multipleError() : void
      {
         m_saveAPI.removeSaveAPIListener(m_saveAPIListener);
         dispatchMultipleError();
      }
      
      private function noInMultipleState() : void
      {
         m_saveAPI.removeSaveAPIListener(m_saveAPIListener);
         dispatchStateIsOk();
      }
      
      private function multipleDataGetError() : void
      {
         m_saveAPI.removeSaveAPIListener(m_saveAPIListener);
         dispatchIsNotLink();
      }
      
      private function dispatchStateIsOk() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IVerifyUserStateListener> = m_verifyUserStateListeners ? m_verifyUserStateListeners.slice(0) : null;
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].userStateIsOK();
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function dispatchUserLoginOutError() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IVerifyUserStateListener> = m_verifyUserStateListeners ? m_verifyUserStateListeners.slice(0) : null;
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].userLoginOutError();
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function dispatchIsNotLink() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IVerifyUserStateListener> = m_verifyUserStateListeners ? m_verifyUserStateListeners.slice(0) : null;
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].isNotLinkErrror();
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function dispatchMultipleError() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IVerifyUserStateListener> = m_verifyUserStateListeners ? m_verifyUserStateListeners.slice(0) : null;
         var _loc2_:int = _loc1_ ? _loc1_.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].isMultipleError();
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
   }
}

