package YJFY
{
   import GM_UI.GMData;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Players.PlayerVO;
   import UI.VersionControl;
   import UI.XMLSingle;
   import UI2.Mount.MountData.MountVO;
   import YJFY.AutomaticPet.AutomaticPetVO;
   import YJFY.EntityAnimation.AnimationFzShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationShowData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class EntityShowContainer
   {
      
      private const m_const_partMaxNum:uint = 5;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:VersionControl;
      
      private var m_show:MySprite;
      
      private var m_entityShowContainerListeners:Vector.<IEntityShowContainerListener>;
      
      private var m_animationShowDatas:Vector.<AnimationShowData>;
      
      private var m_animationFzDatas:Vector.<AnimationFzShow>;
      
      private var m_isLoading:Boolean;
      
      private var m_wantLoadNum:int;
      
      private var m_currentNum:int;
      
      private var m_partShows:Vector.<DisplayObject>;
      
      private var m_xml:XML;
      
      private var m_state:String;
      
      private var m_bLoadComplete:Boolean = false;
      
      private var m_loadUI:LoadUI2;
      
      private var m_strDefid:int;
      
      private var m_strLabel:String;
      
      public function EntityShowContainer()
      {
         super();
         m_entityShowContainerListeners = new Vector.<IEntityShowContainerListener>();
         m_partShows = new Vector.<DisplayObject>(5);
         m_animationShowDatas = new Vector.<AnimationShowData>();
         m_animationFzDatas = new Vector.<AnimationFzShow>();
      }
      
      public function clear() : void
      {
         m_myLoader = null;
         m_versionControl = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_animationFzDatas);
         m_animationFzDatas = null;
         ClearUtil.nullArr(m_entityShowContainerListeners,false,false,false);
         m_entityShowContainerListeners = null;
         ClearUtil.clearObject(m_partShows);
         m_partShows = null;
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas = null;
         m_loadUI = null;
      }
      
      public function setLoadUI(param1:LoadUI2) : void
      {
         m_loadUI = param1;
      }
      
      public function init() : void
      {
         m_versionControl = Part1.getInstance().getVersionControl();
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         if(m_loadUI == null)
         {
            m_loadUI = Part1.getInstance().getLoadUI();
         }
         if(m_loadUI)
         {
            (m_loadUI as LoadUI2).tranToTransparentcy();
         }
         if(m_loadUI)
         {
            m_myLoader.setProgressShow(m_loadUI);
         }
         m_show = new MySprite();
      }
      
      public function refreshPlayerShow(param1:PlayerVO) : void
      {
         var _loc2_:String = null;
         var _loc3_:String = null;
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         if(param1.inforEquipmentVOs[3] && param1.isShowFashionShow)
         {
            changePlayerShow2(param1.playerType,param1.inforEquipmentVOs[3].className);
         }
         else
         {
            _loc2_ = param1.inforEquipmentVOs[4] ? param1.inforEquipmentVOs[4].className : "defaultWeapon";
            _loc3_ = param1.inforEquipmentVOs[1] ? param1.inforEquipmentVOs[1].className : "defaultClothes";
            changePlayerShow(param1.playerType,_loc2_,_loc3_);
         }
      }
      
      public function refreshPetShow(param1:PetEquipmentVO) : void
      {
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         changePetShow(param1.className);
      }
      
      public function refreshAutomaticPetShow(param1:AutomaticPetVO) : void
      {
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         changeAutomaticPetShow(param1);
      }
      
      public function refreshMountShow(param1:MountVO) : void
      {
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         changeMountShow(param1);
      }
      
      public function addEntityShowContainerListener(param1:IEntityShowContainerListener) : void
      {
         ListenerUtil.addListener(m_entityShowContainerListeners,param1);
      }
      
      public function removeEntityShowContainerListener(param1:IEntityShowContainerListener) : void
      {
         ListenerUtil.removeListener(m_entityShowContainerListeners,param1);
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      private function restartLoad() : void
      {
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         m_currentNum = 0;
         m_wantLoadNum = 0;
         m_isLoading = false;
         m_bLoadComplete = false;
      }
      
      public function changePlayerShow(param1:String, param2:String, param3:String) : void
      {
         var _loc6_:AnimationShowData = null;
         var _loc7_:AnimationShowData = null;
         var _loc5_:XMLList = null;
         var _loc4_:XMLList = null;
         var _loc10_:int = 0;
         var _loc8_:int = 0;
         if(m_isLoading == true)
         {
            return;
         }
         if(Boolean(param2) == false)
         {
            param2 = "defaultWeapon";
         }
         if(Boolean(param3) == false)
         {
            param3 = "defaultClothes";
         }
         restartLoad();
         m_isLoading = true;
         m_wantLoadNum = 2;
         var _loc9_:XML = XMLSingle.getInstance().dataXML.entityShowData[0][param1][0];
         var _loc11_:int = int(_loc9_.@ver);
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_partShows);
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         if(_loc11_ == 0)
         {
            _loc6_ = new AnimationShowData();
            getAnimationShowDataFromXML(_loc9_.show.(@eqClassName == param2)[0],_loc6_);
            _loc7_ = new AnimationShowData();
            getAnimationShowDataFromXML(_loc9_.show.(@eqClassName == param3)[0],_loc7_);
            m_animationShowDatas.push(_loc7_);
            m_animationShowDatas.push(_loc6_);
            loadHeroXml(param3);
            loadHeroHeadXml(param3);
         }
         else
         {
            if(_loc11_ != 1)
            {
               throw new Error("version only can be 0 or 1 now.");
            }
            _loc5_ = _loc9_.show.(@eqClassName == param2)[0].subShow;
            _loc4_ = _loc9_.show.(@eqClassName == param3)[0].subShow;
            m_wantLoadNum = _loc5_.length() + _loc4_.length();
            m_animationShowDatas.length = m_wantLoadNum;
            addAnimationShowDatasByShowXMLList(_loc5_,m_animationShowDatas);
            addAnimationShowDatasByShowXMLList(_loc4_,m_animationShowDatas);
            loadHeroHeadXml(param3);
         }
         _loc8_ = int(m_animationShowDatas.length);
         _loc10_ = 0;
         while(_loc10_ < _loc8_)
         {
            if(m_animationShowDatas[_loc10_])
            {
               if(param3 == "Clothes_ChenGuang_Monkey" || param3 == "Clothes_ChenGuang_Dog" || param3 == "Clothes_ChenGuang_Dragon" || param3 == "Clothes_FoxClothes15" || param3 == "Clothes_ChenGuang_Rabbit" || param3 == "Clothes_ZixiaiClothes_15" || param3 == "Clothes_ShenGuang_Monkey" || param3 == "Clothes_ShenGuang_Dog" || param3 == "Clothes_ShenGuang_Dragon" || param3 == "Clothes_FoxClothes16" || param3 == "Clothes_ShenGuang_Rabbit" || param3 == "Clothes_ZixiaiClothes_16" || param3 == "Clothes_ShiShi_Monkey" || param3 == "Clothes_ShiShi_Dog" || param3 == "Clothes_ShiShi_Dragon" || param3 == "Clothes_FoxClothes17" || param3 == "Clothes_ShiShi_Rabbit" || param3 == "Clothes_ZixiaiClothes_17" || param3 == "Clothes_ChuanShuo_Monkey" || param3 == "Clothes_ChuanShuo_Dog" || param3 == "Clothes_ChuanShuo_Dragon" || param3 == "Clothes_FoxClothes18" || param3 == "Clothes_ChuanShuo_Rabbit" || param3 == "Clothes_ZixiaiClothes_18")
               {
                  m_myLoader.getClass(m_animationShowDatas[_loc10_].getSwfPath(),m_animationShowDatas[_loc10_].getClassName(),getShowSuccess2,getShowFail,null,m_animationShowDatas[_loc10_]);
               }
               else
               {
                  m_myLoader.getClass(m_animationShowDatas[_loc10_].getSwfPath(),m_animationShowDatas[_loc10_].getClassName(),getShowSuccess,getShowFail,null,m_animationShowDatas[_loc10_]);
               }
            }
            _loc10_++;
         }
         m_myLoader.load();
      }
      
      private function loadHeroHeadXml(param1:String) : void
      {
         var _loc2_:String = null;
         if(param1 == "Clothes_ChenGuang_Monkey" || param1 == "Clothes_ShenGuang_Monkey" || param1 == "Clothes_ChuanShuo_Monkey" || param1 == "Clothes_ShiShi_Monkey")
         {
            if(param1 == "Clothes_ChenGuang_Monkey")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_ShenGuang_Monkey")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_ShiShi_Monkey")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_ChuanShuo_Monkey")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/Monkey.xml";
            m_state = "Monkey";
         }
         else if(param1 == "Clothes_ChenGuang_Dog" || param1 == "Clothes_ShenGuang_Dog" || param1 == "Clothes_ShiShi_Dog" || param1 == "Clothes_ChuanShuo_Dog")
         {
            if(param1 == "Clothes_ChenGuang_Dog")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_ShenGuang_Dog")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_ShiShi_Dog")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_ChuanShuo_Dog")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/Dog.xml";
            m_state = "Dog";
         }
         else if(param1 == "Clothes_ChenGuang_Dragon" || param1 == "Clothes_ShenGuang_Dragon" || param1 == "Clothes_ShiShi_Dragon" || param1 == "Clothes_ChuanShuo_Dragon")
         {
            if(param1 == "Clothes_ChenGuang_Dragon")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_ShenGuang_Dragon")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_ShiShi_Dragon")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_ChuanShuo_Dragon")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/Dragon.xml";
            m_state = "Dragon";
         }
         else if(param1 == "Clothes_FoxClothes15" || param1 == "Clothes_FoxClothes16" || param1 == "Clothes_FoxClothes17" || param1 == "Clothes_FoxClothes18")
         {
            if(param1 == "Clothes_FoxClothes15")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_FoxClothes16")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_FoxClothes17")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_FoxClothes18")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/Fox.xml";
            m_state = "Fox";
         }
         else if(param1 == "Clothes_ChenGuang_Rabbit" || param1 == "Clothes_ShenGuang_Rabbit" || param1 == "Clothes_ShiShi_Rabbit" || param1 == "Clothes_ChuanShuo_Rabbit")
         {
            if(param1 == "Clothes_ChenGuang_Rabbit")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_ShenGuang_Rabbit")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_ShiShi_Rabbit")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_ChuanShuo_Rabbit")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/Rabbit.xml";
            m_state = "Rabbit";
         }
         else if(param1 == "Clothes_TieShanClothes14")
         {
            m_state = "TieShan";
         }
         else if(param1 == "Clothes_ZixiaiClothes_15" || param1 == "Clothes_ZixiaiClothes_16" || param1 == "Clothes_ZixiaiClothes_17" || param1 == "Clothes_ZixiaiClothes_18")
         {
            if(param1 == "Clothes_ZixiaiClothes_15")
            {
               m_strLabel = "ChenGuang";
            }
            else if(param1 == "Clothes_ZixiaiClothes_16")
            {
               m_strLabel = "ShenGuang";
            }
            else if(param1 == "Clothes_ZixiaiClothes_17")
            {
               m_strLabel = "ShiShi";
            }
            else if(param1 == "Clothes_ZixiaiClothes_18")
            {
               m_strLabel = "ChuanShuo";
            }
            _loc2_ = "NewGameFolder/Players/ZiXia.xml";
            m_state = "ZiXia";
         }
         if(_loc2_)
         {
            m_myLoader.getXML(_loc2_,getPlayerXMLSuccess2,getFail);
            m_myLoader.load();
         }
      }
      
      private function getSuccessFun(param1:YJFYLoaderData) : void
      {
         var _loc2_:MovieClip = new param1.resultClass();
         _loc2_.x = 0;
         _loc2_.y = 0;
         m_show.addChild(_loc2_);
      }
      
      private function loadHeroXml(param1:String) : void
      {
         var _loc2_:String = null;
         if(param1 == "Clothes_ShenYin_Monkey")
         {
            _loc2_ = "NewGameFolder/Players/Monkey.xml";
            m_state = "Monkey";
         }
         else if(param1 == "Clothes_ShenYin_Dog")
         {
            _loc2_ = "NewGameFolder/Players/Dog.xml";
            m_state = "Dog";
         }
         else if(param1 == "Clothes_ShenYin_Dragon")
         {
            _loc2_ = "NewGameFolder/Players/Dragon.xml";
            m_state = "Dragon";
         }
         else if(param1 == "Clothes_FoxClothes14")
         {
            m_state = "Fox";
         }
         else if(param1 == "Clothes_ShenYin_Rabbit")
         {
            _loc2_ = "NewGameFolder/Players/Rabbit.xml";
            m_state = "Rabbit";
         }
         else if(param1 == "Clothes_TieShanClothes16")
         {
            m_state = "TieShan";
         }
         if(_loc2_)
         {
            m_myLoader.getXML(_loc2_,getPlayerXMLSuccess,getFail);
            m_myLoader.load();
         }
      }
      
      private function getPlayerXMLSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:XMLList = null;
         var _loc4_:int = 0;
         m_xml = param1.resultXML;
         var _loc3_:XML = m_xml.animal[0];
         if(_loc3_ && _loc3_.shows)
         {
            _loc3_ = _loc3_.shows[0];
            if(_loc3_)
            {
               _loc2_ = _loc3_.fzShow;
            }
            if(_loc2_)
            {
               _loc4_ = 0;
               while(_loc4_ < _loc2_.length())
               {
                  if(m_state == "Monkey")
                  {
                     if(String(_loc2_[_loc4_].@defToId) == "monkeyIdle")
                     {
                        m_myLoader.getClass(String(_loc2_[_loc4_].@swfPath),String(_loc2_[_loc4_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Dog")
                  {
                     if(String(_loc2_[_loc4_].@defToId) == "dogIdle")
                     {
                        m_myLoader.getClass(String(_loc2_[_loc4_].@swfPath),String(_loc2_[_loc4_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Dragon")
                  {
                     if(String(_loc2_[_loc4_].@defToId) == "dragonIdle")
                     {
                        m_myLoader.getClass(String(_loc2_[_loc4_].@swfPath),String(_loc2_[_loc4_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state != "Fox")
                  {
                     if(m_state == "Rabbit")
                     {
                        if(String(_loc2_[_loc4_].@defToId) == "rabbitIdle")
                        {
                           m_myLoader.getClass(String(_loc2_[_loc4_].@swfPath),String(_loc2_[_loc4_].@showClass),getSuccess,getShowFail);
                           m_myLoader.load();
                           break;
                        }
                     }
                     else if(m_state == "TieShan")
                     {
                     }
                  }
                  _loc4_++;
               }
            }
         }
      }
      
      private function getPlayerXMLSuccess2(param1:YJFYLoaderData) : void
      {
         var _loc2_:XMLList = null;
         var _loc4_:XMLList = null;
         var _loc5_:int = 0;
         m_xml = param1.resultXML;
         var _loc3_:XML = m_xml.animal[0];
         if(_loc3_ && _loc3_.shows)
         {
            _loc3_ = _loc3_.shows[0];
            if(_loc3_)
            {
               if(m_strLabel == "ChenGuang" || m_strLabel == "ShenGuang")
               {
                  _loc2_ = _loc3_.headShow;
               }
               if(m_strLabel == "ShenGuang")
               {
                  _loc4_ = _loc3_.fzGodShow;
               }
               if(m_strLabel == "ShiShi" || m_strLabel == "ChuanShuo")
               {
                  _loc2_ = _loc3_.headShow2018;
               }
               if(m_strLabel == "ChuanShuo")
               {
                  _loc4_ = _loc3_.fzGodShow2018;
               }
            }
            if(_loc2_)
            {
               _loc5_ = 0;
               while(_loc5_ < _loc2_.length())
               {
                  if(m_state == "Monkey")
                  {
                     if(String(_loc2_[_loc5_].@defToId) == "monkeyShow")
                     {
                        if(_loc4_ && String(_loc4_[_loc5_].@defToId) == "monkeyShow")
                        {
                           m_myLoader.getClass(String(_loc4_[_loc5_].@swfPath),String(_loc4_[_loc5_].@showClass),getSuccess,getShowFail);
                        }
                        m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Dog")
                  {
                     if(String(_loc2_[_loc5_].@defToId) == "dogShow")
                     {
                        if(_loc4_ && String(_loc4_[_loc5_].@defToId) == "dogShow")
                        {
                           m_myLoader.getClass(String(_loc4_[_loc5_].@swfPath),String(_loc4_[_loc5_].@showClass),getSuccess,getShowFail);
                        }
                        m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Dragon")
                  {
                     if(String(_loc2_[_loc5_].@defToId) == "dragonShow")
                     {
                        if(_loc4_ && String(_loc4_[_loc5_].@defToId) == "dragonShow")
                        {
                           m_myLoader.getClass(String(_loc4_[_loc5_].@swfPath),String(_loc4_[_loc5_].@showClass),getSuccess,getShowFail);
                        }
                        m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Fox")
                  {
                     if(String(_loc2_[_loc5_].@defToId) == "foxShow")
                     {
                        m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state == "Rabbit")
                  {
                     if(String(_loc2_[_loc5_].@defToId) == "rabbitShow")
                     {
                        if(_loc4_ && String(_loc4_[_loc5_].@defToId) == "rabbitShow")
                        {
                           m_myLoader.getClass(String(_loc4_[_loc5_].@swfPath),String(_loc4_[_loc5_].@showClass),getSuccess,getShowFail);
                        }
                        m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                        m_myLoader.load();
                        break;
                     }
                  }
                  else if(m_state != "TieShan")
                  {
                     if(m_state == "ZiXia")
                     {
                        if(String(_loc2_[_loc5_].@defToId) == "zixiaShow")
                        {
                           if(_loc4_ && String(_loc4_[_loc5_].@defToId) == "zixiaShow")
                           {
                              m_myLoader.getClass(String(_loc4_[_loc5_].@swfPath),String(_loc4_[_loc5_].@showClass),getSuccess,getShowFail);
                           }
                           m_myLoader.getClass(String(_loc2_[_loc5_].@swfPath),String(_loc2_[_loc5_].@showClass),getSuccess,getShowFail);
                           m_myLoader.load();
                           break;
                        }
                     }
                  }
                  _loc5_++;
               }
            }
         }
      }
      
      private function getSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:MovieClip = new param1.resultClass();
         m_show.addChild(_loc2_);
         _loc2_ = null;
      }
      
      protected function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error(param1.swfPath + param1.xmlPath);
      }
      
      public function changePlayerShow2(param1:String, param2:String) : void
      {
         var _loc5_:AnimationShowData = null;
         var _loc4_:XMLList = null;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         if(m_isLoading == true)
         {
            return;
         }
         if(Boolean(param2) == false)
         {
            throw new Error();
         }
         restartLoad();
         m_isLoading = true;
         m_wantLoadNum = 1;
         var _loc6_:XML = XMLSingle.getInstance().dataXML.entityShowData[0][param1][0];
         var _loc8_:int = int(_loc6_.@ver);
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_partShows);
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         if(_loc8_ == 0)
         {
            _loc5_ = new AnimationShowData();
            getAnimationShowDataFromXML(_loc6_.show.(@eqClassName == param2)[0],_loc5_);
            m_animationShowDatas.push(_loc5_);
         }
         else
         {
            if(_loc8_ != 1)
            {
               throw new Error("version only can be 0 or 1 now.");
            }
            _loc4_ = _loc6_.show.(@eqClassName == param2)[0].subShow;
            m_wantLoadNum = _loc4_.length();
            m_animationShowDatas.length = m_wantLoadNum;
            addAnimationShowDatasByShowXMLList(_loc4_,m_animationShowDatas);
         }
         _loc3_ = int(m_animationShowDatas.length);
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            m_myLoader.getClass(m_animationShowDatas[_loc7_].getSwfPath(),m_animationShowDatas[_loc7_].getClassName(),getShowSuccess,getShowFail,null,m_animationShowDatas[_loc7_]);
            _loc7_++;
         }
         m_myLoader.load();
      }
      
      protected function changePetShow(param1:String) : void
      {
         var _loc2_:AnimationShowData = null;
         if(m_isLoading == true)
         {
            return;
         }
         if(Boolean(param1) == false)
         {
            throw new Error();
         }
         restartLoad();
         m_isLoading = true;
         m_wantLoadNum = 1;
         var _loc3_:XML = XMLSingle.getInstance().dataXML.entityShowData[0]["Pet"][0];
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_partShows);
         _loc2_ = new AnimationShowData();
         getAnimationShowDataFromXML(_loc3_.show.(@eqClassName == param1)[0],_loc2_);
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         m_animationShowDatas.push(_loc2_);
         m_myLoader.getClass(_loc2_.getSwfPath(),_loc2_.getClassName(),getShowSuccess,getShowFail,null,_loc2_);
         m_myLoader.load();
      }
      
      protected function changeAutomaticPetShow(param1:AutomaticPetVO) : void
      {
         var _loc3_:AnimationShowData = null;
         var _loc2_:AnimationShowData = null;
         if(m_isLoading == true)
         {
            return;
         }
         if(Boolean(param1) == false)
         {
            throw new Error();
         }
         restartLoad();
         m_isLoading = true;
         m_wantLoadNum = 1;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_partShows);
         _loc3_ = new AnimationShowData();
         _loc3_.init1(param1.getShowSwfForShowPath(),param1.getShowClassForShowName(),0,0,1,1,null,null);
         _loc2_ = new AnimationShowData();
         _loc2_.init1(param1.getPingJieVO().getShowShadowSwfPath(),param1.getPingJieVO().getShowShadowClassName(),param1.getPingJieVO().getShowShadowXOffset(),param1.getPingJieVO().getShowShadowYOffset(),param1.getPingJieVO().getShowShadowXScale(),param1.getPingJieVO().getShowShadowYScale(),null,null);
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         m_animationShowDatas.push(_loc2_);
         m_animationShowDatas.push(_loc3_);
         m_myLoader.getClass(_loc2_.getSwfPath(),_loc2_.getClassName(),getShowSuccess,getShowFail,null,_loc2_);
         m_myLoader.getClass(_loc3_.getSwfPath(),_loc3_.getClassName(),getShowSuccess,getShowFail,null,_loc3_);
         m_myLoader.load();
      }
      
      private function changeMountShow(param1:MountVO) : void
      {
         var _loc2_:AnimationShowData = null;
         if(m_isLoading == true)
         {
            return;
         }
         if(Boolean(param1) == false)
         {
            throw new Error();
         }
         restartLoad();
         m_isLoading = true;
         m_wantLoadNum = 1;
         ClearUtil.clearDisplayObjectInContainer(m_show);
         ClearUtil.clearObject(m_partShows);
         _loc2_ = new AnimationShowData();
         _loc2_.init1(param1.getShowSwfPath(),param1.getShowClassName(),0,0,1,1,null,null);
         ClearUtil.clearObject(m_animationShowDatas);
         m_animationShowDatas.length = 0;
         m_animationShowDatas.push(_loc2_);
         m_myLoader.getClass(_loc2_.getSwfPath(),_loc2_.getClassName(),getShowSuccess,getShowFail,null,_loc2_);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         var _loc5_:int = 0;
         var _loc4_:int = int(m_animationShowDatas.length);
         var _loc3_:AnimationShowData = param1.extra as AnimationShowData;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(m_animationShowDatas[_loc5_] && m_animationShowDatas[_loc5_].getClassName() == param1.wantClassName)
            {
               _loc2_ = param1.resultClass;
               m_partShows[_loc5_] = new _loc2_();
               m_partShows[_loc5_].x = _loc3_.getXOffset();
               m_partShows[_loc5_].y = _loc3_.getYOffset();
               m_partShows[_loc5_].scaleX = _loc3_.getXScale();
               m_partShows[_loc5_].scaleY = _loc3_.getYScale();
            }
            _loc5_++;
         }
         ++m_currentNum;
         if(m_currentNum >= m_wantLoadNum)
         {
            _loc4_ = int(m_partShows.length);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(m_partShows[_loc5_])
               {
                  m_show.addChild(m_partShows[_loc5_]);
               }
               _loc5_++;
            }
            m_isLoading = false;
            loadComplete();
            m_bLoadComplete = true;
         }
      }
      
      private function getShowSuccess2(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = null;
         var _loc5_:int = 0;
         var _loc4_:int = int(m_animationShowDatas.length);
         var _loc3_:AnimationShowData = param1.extra as AnimationShowData;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(m_animationShowDatas[_loc5_] && m_animationShowDatas[_loc5_].getClassName() == param1.wantClassName)
            {
               _loc2_ = param1.resultClass;
               m_partShows[_loc5_] = new _loc2_();
               m_partShows[_loc5_].x = _loc3_.getXOffset();
               m_partShows[_loc5_].y = _loc3_.getYOffset();
               m_partShows[_loc5_].scaleX = _loc3_.getXScale();
               m_partShows[_loc5_].scaleY = _loc3_.getYScale();
            }
            _loc5_++;
         }
         ++m_currentNum;
         if(m_currentNum >= m_wantLoadNum)
         {
            _loc4_ = int(m_partShows.length);
            if(m_show.numChildren == 1)
            {
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  if(m_partShows[_loc5_])
                  {
                     m_show.addChild(m_partShows[_loc5_]);
                  }
                  _loc5_++;
               }
               m_show.setChildIndex(m_show.getChildAt(0),m_show.numChildren - 1);
            }
            else if(m_show.numChildren == 2)
            {
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  if(m_partShows[_loc5_])
                  {
                     m_show.addChild(m_partShows[_loc5_]);
                  }
                  _loc5_++;
               }
               m_show.setChildIndex(m_show.getChildAt(1),m_show.numChildren - 1);
            }
            m_isLoading = false;
            loadComplete();
            m_bLoadComplete = true;
         }
      }
      
      private function loadComplete() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<IEntityShowContainerListener> = m_entityShowContainerListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_[_loc3_].loadComplete(this);
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function addAnimationShowDatasByShowXMLList(param1:XMLList, param2:Vector.<AnimationShowData>) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = int(param1.length());
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            addAnimationShowDataBySubShowXML(param1[_loc4_],param2);
            _loc4_++;
         }
      }
      
      private function addAnimationShowDataBySubShowXML(param1:XML, param2:Vector.<AnimationShowData>) : void
      {
         var _loc4_:AnimationShowData = new AnimationShowData();
         getAnimationShowDataFromXML(param1,_loc4_);
         var _loc3_:int = int(param1.@i);
         param2[_loc3_] = _loc4_;
      }
      
      private function getAnimationShowDataFromXML(param1:XML, param2:AnimationShowData) : void
      {
         param2.init1(param1.@swfPath,param1.@showClass,param1.@x_offset,param1.@y_offset,param1.@x_scale,param1.@y_scale,param1,param1.@tag);
      }
      
      public function getLoadComplete() : Boolean
      {
         return m_bLoadComplete;
      }
      
      public function getLoading() : Boolean
      {
         return m_isLoading;
      }
   }
}

