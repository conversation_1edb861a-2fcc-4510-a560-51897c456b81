package UI.MessageBox
{
   import UI.MyFunction;
   import UI.MySprite;
   import flash.display.DisplayObject;
   import flash.filters.DropShadowFilter;
   import flash.text.TextField;
   
   public class MessageBox extends MySprite
   {
      
      private const DISTANCE:Number = 5;
      
      private const ANGLE:Number = 45;
      
      private const COLOR:int = 0;
      
      private const ALPHA:Number = 1;
      
      private const BLURX:Number = 5;
      
      private const BLURY:Number = 5;
      
      private const STRENGTH:Number = 1;
      
      private const QUALITY:Number = 1;
      
      protected var _textField:TextField;
      
      protected var _boxWidth:Number;
      
      protected var _boxHeight:Number;
      
      protected var _ellipseWidth:Number;
      
      protected var _ellipseHeight:Number;
      
      protected var _backgroundColor:int;
      
      protected var _backgroundAlpha:Number;
      
      protected var _thickness:Number;
      
      protected var _borderColor:int;
      
      protected var _borderAlpha:Number;
      
      protected var _showObject:DisplayObject;
      
      public function MessageBox()
      {
         super();
      }
      
      public function init(param1:String = "", param2:Number = 0, param3:Number = 0, param4:Number = 0, param5:Number = NaN, param6:uint = 0, param7:uint = 16777215, param8:Number = 1, param9:Number = NaN, param10:int = 16777215, param11:Number = 1) : void
      {
         _textField = new TextField();
         mouseEnabled = false;
         _boxHeight = param3;
         _boxWidth = param2;
         _ellipseWidth = param4;
         _ellipseHeight = param5;
         _backgroundColor = param7;
         _backgroundAlpha = param8;
         _thickness = param9;
         _borderColor = param10;
         _borderAlpha = param11;
         _textField.selectable = false;
         _textField.htmlText = param1;
         _textField.textColor = param6;
         _textField.multiline = true;
         _textField.wordWrap = true;
         if(MyFunction.getInstance().registerFont)
         {
            _textField.embedFonts = true;
         }
         _textField.x = 5;
         _textField.y = 8;
         addChild(_textField);
         filters = [new DropShadowFilter(5,45,0,1,8,8,1)];
      }
      
      public function set htmlText(param1:String) : void
      {
         _textField.htmlText = param1;
         drawBox();
      }
      
      public function clearSprite() : void
      {
         if(_showObject)
         {
            if(getChildByName(_showObject.name))
            {
               removeChild(_showObject);
            }
         }
         _showObject = null;
      }
      
      public function addSprite(param1:DisplayObject) : void
      {
         if(_showObject)
         {
            if(getChildByName(_showObject.name))
            {
               if(_showObject.hasOwnProperty("clear"))
               {
                  _showObject["clear"]();
               }
               removeChild(_showObject);
            }
         }
         _showObject = param1;
         if(_showObject)
         {
            _showObject.x = width - _showObject.width * 4 / 5;
            _showObject.y = Math.min(_showObject.height,height - _showObject.height / 2);
            addChild(_showObject);
         }
      }
      
      public function get boxWidth() : Number
      {
         return _boxWidth;
      }
      
      public function get boxHeight() : Number
      {
         return _boxHeight;
      }
      
      public function set boxWidth(param1:Number) : void
      {
         _boxWidth = param1;
         drawBox();
      }
      
      public function set boxHeight(param1:Number) : void
      {
         _boxHeight = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _textField = null;
         this.graphics.clear();
         _showObject = null;
      }
      
      private function drawBox() : void
      {
         var _loc2_:Number = NaN;
         var _loc1_:Number = NaN;
         _textField.width = 1000;
         if(Boolean(_showObject) && getChildByName(_showObject.name))
         {
            if(_textField.textWidth < _boxWidth - 60)
            {
               _textField.width = _textField.textWidth + 5;
               _loc2_ = _textField.width + 55;
            }
            else
            {
               _textField.width = _boxWidth - 55;
               _loc2_ = _boxWidth;
            }
         }
         else if(_textField.textWidth < _boxWidth - 15)
         {
            _textField.width = _textField.textWidth + 5;
            _loc2_ = _textField.width + 10;
         }
         else
         {
            _textField.width = _boxWidth - 10;
            _loc2_ = _boxWidth;
         }
         if(_boxHeight == 0)
         {
            _textField.height = _textField.textHeight + 4;
            _loc1_ = _textField.textHeight + 25;
         }
         else
         {
            _textField.height = _boxHeight;
            _loc1_ = _boxHeight;
         }
         this.graphics.clear();
         this.graphics.lineStyle(_thickness,_borderColor,_borderAlpha);
         if(_backgroundAlpha > 1)
         {
            _backgroundAlpha = 1;
         }
         else if(_backgroundAlpha < 0)
         {
            _backgroundAlpha = 0;
         }
         this.graphics.beginFill(_backgroundColor,_backgroundAlpha);
         this.graphics.drawRoundRect(0,0,_loc2_,_loc1_,_ellipseWidth,_ellipseHeight);
         this.graphics.endFill();
         if(_showObject)
         {
            _showObject.x = _textField.x + _textField.width + _showObject.width * 1 / 2;
            _showObject.y = Math.min(_showObject.height,height - _showObject.height / 2);
         }
      }
      
      private function setDropShadowFilters() : void
      {
         filters = [new DropShadowFilter(5,45,0,1,5,5,1,1)];
      }
   }
}

