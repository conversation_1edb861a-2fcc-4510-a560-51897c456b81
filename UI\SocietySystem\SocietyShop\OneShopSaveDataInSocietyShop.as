package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   import UI.MyFunction;
   import YJFY.Utils.ClearUtil;
   
   public class OneShopSaveDataInSocietyShop extends DataManagerParent
   {
      
      private var m_buyEqDatas:Vector.<OneEqSaveDataInSocietyShop>;
      
      private var m_shopLevel:int;
      
      public function OneShopSaveDataInSocietyShop()
      {
         super();
         m_buyEqDatas = new Vector.<OneEqSaveDataInSocietyShop>();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_buyEqDatas);
         m_buyEqDatas = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.shopLevel = m_shopLevel;
      }
      
      public function init2(param1:int) : void
      {
         this.shopLevel = param1;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:* = undefined;
         var _loc2_:* = undefined;
         var _loc5_:OneEqSaveDataInSocietyShop = null;
         if(param1 == null)
         {
            return;
         }
         shopLevel = int(param1.@level);
         m_buyEqDatas = new Vector.<OneEqSaveDataInSocietyShop>();
         _loc3_ = MyFunction.getInstance().excreteStringToString(param1.@ids);
         _loc2_ = MyFunction.getInstance().excreteString(param1.@nums);
         _loc4_ = _loc3_ ? _loc3_.length : 0;
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_ = new OneEqSaveDataInSocietyShop(_loc3_[_loc6_],_loc2_[_loc6_]);
            m_buyEqDatas.push(_loc5_);
            _loc6_++;
         }
         ClearUtil.nullArr(_loc3_);
         ClearUtil.nullArr(_loc2_);
         _loc3_ = null;
         _loc2_ = null;
      }
      
      public function exportSaveXML() : XML
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:* = undefined;
         var _loc1_:* = undefined;
         var _loc4_:XML = <shop />;
         _loc4_.@level = shopLevel;
         _loc3_ = int(m_buyEqDatas.length);
         if(_loc3_)
         {
            _loc2_ = new Vector.<String>();
            _loc1_ = new Vector.<int>();
            _loc5_ = 0;
            while(_loc5_ < _loc3_)
            {
               _loc2_.push(m_buyEqDatas[_loc5_].getEqId());
               _loc1_.push(m_buyEqDatas[_loc5_].getNum());
               _loc5_++;
            }
            _loc4_.@ids = MyFunction.getInstance().combineStringsToArr(_loc2_);
            _loc4_.@nums = MyFunction.getInstance().combineIdArray(_loc1_);
         }
         return _loc4_;
      }
      
      public function getShopLevel() : int
      {
         return shopLevel;
      }
      
      public function getBuyEqDataNum() : int
      {
         return m_buyEqDatas.length;
      }
      
      public function getBuyEqDataByIndex(param1:int) : OneEqSaveDataInSocietyShop
      {
         return m_buyEqDatas[param1];
      }
      
      public function getBuyEqDataByEqId(param1:String) : OneEqSaveDataInSocietyShop
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_buyEqDatas.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_buyEqDatas[_loc3_].getEqId() == param1)
            {
               return m_buyEqDatas[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function addBuyEqData(param1:String, param2:int) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = int(m_buyEqDatas.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(m_buyEqDatas[_loc5_].getEqId() == param1)
            {
               m_buyEqDatas[_loc5_].addNum(param2);
               return;
            }
            _loc5_++;
         }
         var _loc4_:OneEqSaveDataInSocietyShop = new OneEqSaveDataInSocietyShop(param1,param2);
         m_buyEqDatas.push(_loc4_);
      }
      
      private function get shopLevel() : int
      {
         return _antiwear.shopLevel;
      }
      
      private function set shopLevel(param1:int) : void
      {
         _antiwear.shopLevel = param1;
      }
   }
}

