package UI.MainLineTask.TaskRewardVO
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class ChoiceEquipmentRewardVO extends TaskRewardVO
   {
      
      private var m_choosableEquipmentVOss:Vector.<Vector.<EquipmentVO>>;
      
      private var m_chooseEquipmentVOs:Vector.<EquipmentVO>;
      
      public function ChoiceEquipmentRewardVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.nullArr(m_choosableEquipmentVOss);
         m_choosableEquipmentVOss = null;
         m_chooseEquipmentVOs = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc3_:* = undefined;
         super.initByXML(param1);
         var _loc4_:XMLList = param1.equipments;
         var _loc2_:int = int(_loc4_.length());
         m_choosableEquipmentVOss = new Vector.<Vector.<EquipmentVO>>();
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc3_ = XMLSingle.getEquipmentVOs(_loc4_[_loc5_],XMLSingle.getInstance().equipmentXML,false);
            m_choosableEquipmentVOss.push(_loc3_);
            _loc5_++;
         }
      }
      
      public function getChoosableEquipmentVOss() : Vector.<Vector.<EquipmentVO>>
      {
         return m_choosableEquipmentVOss;
      }
      
      public function getChooseEquipmentVOs() : Vector.<EquipmentVO>
      {
         return m_chooseEquipmentVOs;
      }
      
      public function setChooseEquipmentVOs(param1:int) : void
      {
         if(param1 < 0 || m_choosableEquipmentVOss == null || param1 > m_choosableEquipmentVOss.length - 1)
         {
            throw new Error();
         }
         m_chooseEquipmentVOs = m_choosableEquipmentVOss[param1];
      }
   }
}

