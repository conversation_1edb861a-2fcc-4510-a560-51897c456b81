package YJFY.optimize
{
   import YJFY.Utils.ClearUtil;
   
   public class SaveNewFileData
   {
      
      private static var _instance:SaveNewFileData;
      
      private var m_funcList:Vector.<SaveFileInfo>;
      
      public var successFunc:Function;
      
      public var failFunc:Function;
      
      public function SaveNewFileData()
      {
         super();
         m_funcList = new Vector.<SaveFileInfo>();
      }
      
      public static function getInstance() : SaveNewFileData
      {
         if(_instance == null)
         {
            _instance = new SaveNewFileData();
         }
         return _instance;
      }
      
      public function getList() : Vector.<SaveFileInfo>
      {
         return m_funcList;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_funcList);
         m_funcList = null;
         successFunc = null;
         failFunc = null;
      }
      
      public function getTotalNum() : int
      {
         return m_funcList.length;
      }
      
      public function doByIndex(param1:int) : void
      {
         if(param1 >= 0 && param1 < m_funcList.length)
         {
            m_funcList[param1].saveFunc();
         }
      }
   }
}

