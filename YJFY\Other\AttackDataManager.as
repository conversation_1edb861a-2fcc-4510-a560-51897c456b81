package YJFY.Other
{
   import YJFY.Entity.AttackData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Utils.ClearUtil;
   
   public class AttackDataManager
   {
      
      private var m_useAttackDatas:Vector.<AttackData>;
      
      private var m_wasteAttackDatas:Vector.<AttackData>;
      
      private var m_attackDatasPool:ObjectsPool;
      
      public function AttackDataManager()
      {
         super();
         m_useAttackDatas = new Vector.<AttackData>();
         m_wasteAttackDatas = new Vector.<AttackData>();
         m_attackDatasPool = new ObjectsPool(m_useAttackDatas,m_wasteAttackDatas,createAttackData,null);
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_useAttackDatas);
         m_useAttackDatas = null;
         ClearUtil.clearObject(m_wasteAttackDatas);
         m_wasteAttackDatas = null;
         ClearUtil.clearObject(m_attackDatasPool);
         m_attackDatasPool = null;
      }
      
      public function getOneAttackData() : AttackData
      {
         return m_attackDatasPool.getOneOrCreateOneObj() as AttackData;
      }
      
      public function wasteOneAttackData(param1:AttackData) : void
      {
         m_attackDatasPool.wasteOneObj(param1);
      }
      
      private function createAttackData() : AttackData
      {
         return new AttackData(false,0,false,false,0);
      }
   }
}

