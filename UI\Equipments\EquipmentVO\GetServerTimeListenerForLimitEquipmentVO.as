package UI.Equipments.EquipmentVO
{
   import UI.UIInterface.ILimitEquipmentVO;
   import UI.Utils.GetServerTime.IGetServerTimeListener;
   import YJFY.Utils.ClearUtil;
   
   public class GetServerTimeListenerForLimitEquipmentVO implements IGetServerTimeListener
   {
      
      private var m_equipmentVOs:Vector.<ILimitEquipmentVO>;
      
      public function GetServerTimeListenerForLimitEquipmentVO()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_equipmentVOs,false,false,false);
      }
      
      public function addLimitEquipmentVO(param1:ILimitEquipmentVO) : void
      {
         if(m_equipmentVOs == null)
         {
            m_equipmentVOs = new Vector.<ILimitEquipmentVO>();
         }
         m_equipmentVOs.push(param1);
      }
      
      public function removeLimitEquipmentVO(param1:ILimitEquipmentVO) : void
      {
         if(m_equipmentVOs == null)
         {
            return;
         }
         var _loc2_:int = int(m_equipmentVOs.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_equipmentVOs.splice(_loc2_,1);
            _loc2_ = int(m_equipmentVOs.indexOf(param1));
         }
      }
      
      public function successGetServerTime(param1:String) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Number = NaN;
         var _loc2_:int = m_equipmentVOs ? m_equipmentVOs.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_equipmentVOs[_loc4_])
            {
               m_equipmentVOs[_loc4_].initTime = param1;
               if(m_equipmentVOs[_loc4_].TimeLimit == -1)
               {
                  _loc3_ = 2147483647;
               }
               else
               {
                  _loc3_ = Number(m_equipmentVOs[_loc4_].TimeLimit);
               }
               if(_loc3_ <= 0)
               {
                  throw new Error();
               }
               m_equipmentVOs[_loc4_].remainingTime = Math.ceil(_loc3_ / 24);
            }
            _loc4_++;
         }
         clear();
      }
      
      public function failGetServerTime() : void
      {
         clear();
      }
   }
}

