package UI.MainLineTask.TaskDescription
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class TaskDescriptionVOFactory
   {
      
      private var _classes:Array;
      
      public function TaskDescriptionVOFactory()
      {
         super();
         _classes = [TaskDescriptionVO_MainLineTask,TaskDescription1,TaskDescription2,TaskDescription3];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_classes);
         _classes = null;
      }
      
      public function createTaskDescriptionByName(param1:String) : TaskDescriptionVO_MainLineTask
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
      
      public function createTaskDescriptionByXML(param1:XML) : TaskDescriptionVO_MainLineTask
      {
         var _loc3_:String = String(param1.@className);
         var _loc2_:TaskDescriptionVO_MainLineTask = createTaskDescriptionByName(_loc3_);
         _loc2_.initByXML(param1);
         return _loc2_;
      }
   }
}

