package YJFY.Utils
{
   import YJFY.Utils.MovieClipToBitmapDatas.DataOfBitmapData;
   import YJFY.Utils.MovieClipToBitmapDatas.DataOfBitmapDatas;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.filters.ColorMatrixFilter;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class ShowFunction
   {
      
      public function ShowFunction()
      {
         super();
      }
      
      public static function judgeParentsIsTheClass(param1:DisplayObject, param2:Class, param3:DisplayObjectContainer) : DisplayObject
      {
         var _loc4_:DisplayObject = null;
         if(param1.parent == param3 || param1.parent == null)
         {
            _loc4_ = null;
         }
         else if(param1.parent is param2)
         {
            _loc4_ = param1.parent;
         }
         else
         {
            _loc4_ = judgeParentsIsTheClass(param1.parent,param2,param3);
         }
         return _loc4_;
      }
      
      public static function dealWithBitmapData(param1:BitmapData, param2:XML) : Vector.<BitmapData>
      {
         var _loc10_:int = 0;
         var _loc5_:XML = null;
         var _loc8_:Rectangle = null;
         var _loc9_:BitmapData = null;
         var _loc7_:XMLList = param2.children();
         var _loc6_:int = int(_loc7_.length());
         var _loc4_:Vector.<BitmapData> = new Vector.<BitmapData>();
         var _loc3_:Point = new Point(0,0);
         _loc10_ = 0;
         while(_loc10_ < _loc6_)
         {
            _loc5_ = _loc7_[_loc10_];
            _loc8_ = new Rectangle(_loc5_.@x,_loc5_.@y,_loc5_.@width,_loc5_.@height);
            _loc9_ = new BitmapData(_loc5_.@width,_loc5_.@height,true,0);
            _loc9_.copyPixels(param1,_loc8_,_loc3_);
            _loc4_.push(_loc9_);
            _loc10_++;
         }
         return _loc4_;
      }
      
      public static function dealWithCutBitmapData(param1:BitmapData, param2:XML, param3:DataOfBitmapDatas) : void
      {
         var _loc7_:int = 0;
         var _loc9_:XML = null;
         var _loc5_:Rectangle = null;
         var _loc16_:BitmapData = null;
         var _loc13_:int = 0;
         var _loc15_:int = 0;
         var _loc6_:String = null;
         var _loc4_:DataOfBitmapData = null;
         param3.clearData();
         var _loc14_:XMLList = param2.children();
         var _loc10_:int = int(_loc14_.length());
         var _loc8_:Point = new Point(0,0);
         var _loc11_:int = int(_loc14_[0].@width);
         var _loc12_:int = int(_loc14_[0].@height);
         _loc7_ = 0;
         while(_loc7_ < _loc10_)
         {
            _loc9_ = _loc14_[_loc7_];
            _loc5_ = new Rectangle(_loc9_.@x,_loc9_.@y,_loc9_.@width,_loc9_.@height);
            if(int(_loc9_.@frameHeight))
            {
               _loc11_ = int(_loc9_.@frameWidth);
               _loc12_ = int(_loc9_.@frameHeight);
               _loc13_ = -int(_loc9_.@frameX);
               _loc15_ = -int(_loc9_.@frameY);
               _loc6_ = String(_loc9_.@frameLabel);
            }
            else
            {
               _loc13_ = 0;
               _loc15_ = 0;
               _loc6_ = null;
            }
            if(_loc5_.width && _loc5_.height)
            {
               _loc16_ = new BitmapData(_loc5_.width,_loc5_.height,true,0);
               _loc16_.copyPixels(param1,_loc5_,_loc8_);
            }
            _loc4_ = new DataOfBitmapData(_loc16_,_loc13_,_loc15_,_loc6_);
            param3.addDataOfBitmapData(_loc4_);
            _loc7_++;
         }
      }
      
      public static function changeSaturation(param1:DisplayObject, param2:Number) : void
      {
         var _loc3_:ColorMatrix = new ColorMatrix();
         _loc3_.adjustSaturation(param2);
         param1.filters = [new ColorMatrixFilter(_loc3_)];
      }
      
      public static function changeSaturation2(param1:DisplayObject, param2:int, param3:int, param4:int) : void
      {
         var _loc5_:ColorMatrix = new ColorMatrix();
         _loc5_.adjustBrightness(param2);
         _loc5_.adjustContrast(param3);
         _loc5_.adjustSaturation(param4);
         param1.filters = [new ColorMatrixFilter(_loc5_)];
      }
      
      public static function getDisplayObjectNumByNameInShow(param1:DisplayObjectContainer, param2:String) : uint
      {
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:DisplayObject = null;
         var _loc5_:int = param1.numChildren;
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            _loc3_ = param1.getChildAt(_loc6_);
            if(_loc3_.name.substr(0,param2.length) == param2)
            {
               _loc4_++;
            }
            _loc6_++;
         }
         return _loc4_;
      }
      
      public function clear() : void
      {
      }
      
      public function playMovieClipAll(param1:MovieClip) : void
      {
         var _loc4_:int = 0;
         var _loc2_:DisplayObject = null;
         var _loc3_:int = param1.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = param1.getChildAt(_loc4_);
            if(_loc2_ is MovieClip)
            {
               playMovieClipAll(_loc2_ as MovieClip);
            }
            _loc4_++;
         }
         param1.gotoAndPlay(param1.currentFrame);
      }
      
      public function nextMovieClipAll(param1:MovieClip) : void
      {
         var _loc2_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         if(param1.currentFrame < param1.totalFrames)
         {
            param1.nextFrame();
         }
         else
         {
            param1.gotoAndStop("1");
         }
         _loc3_ = param1.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = param1.getChildAt(_loc4_);
            if(_loc2_ is MovieClip)
            {
               nextMovieClipAll(_loc2_ as MovieClip);
            }
            _loc4_++;
         }
      }
      
      public function stopMovieClipAll(param1:MovieClip) : void
      {
         var _loc2_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         param1.gotoAndStop(param1.currentFrame);
         _loc3_ = param1.numChildren;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = param1.getChildAt(_loc4_);
            if(_loc2_ is MovieClip)
            {
               stopMovieClipAll(_loc2_ as MovieClip);
            }
            _loc4_++;
         }
      }
   }
}

