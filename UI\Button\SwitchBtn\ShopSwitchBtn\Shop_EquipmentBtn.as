package UI.Button.SwitchBtn.ShopSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Shop_EquipmentBtn extends SwitchBtn
   {
      
      public var btnName:String;
      
      public function Shop_EquipmentBtn()
      {
         super();
         setTipString("装备");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopSwitchBtn"));
      }
   }
}

