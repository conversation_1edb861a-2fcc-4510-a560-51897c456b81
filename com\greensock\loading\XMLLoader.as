package com.greensock.loading
{
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.core.LoaderCore;
   import flash.events.Event;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.system.SecurityDomain;
   
   public class XMLLoader extends DataLoader
   {
      
      public static var RAW_LOAD:String = "rawLoad";
      
      private static var _classActivated:Boolean = _activateClass("XMLLoader",XMLLoader,"xml,php,jsp,asp,cfm,cfml,aspx");
      
      protected static var _varTypes:Object = {
         "skipFailed":true,
         "skipPaused":true,
         "paused":false,
         "load":false,
         "noCache":false,
         "maxConnections":2,
         "autoPlay":false,
         "autoDispose":false,
         "smoothing":false,
         "estimatedBytes":1,
         "x":1,
         "y":1,
         "width":1,
         "height":1,
         "scaleX":1,
         "scaleY":1,
         "rotation":1,
         "alpha":1,
         "visible":true,
         "bgColor":0,
         "bgAlpha":0,
         "deblocking":1,
         "repeat":1,
         "checkPolicyFile":false,
         "centerRegistration":false,
         "bufferTime":5,
         "volume":1,
         "bufferMode":false,
         "estimatedDuration":200,
         "crop":false,
         "autoAdjustBuffer":true,
         "suppressInitReparentEvents":true
      };
      
      protected var _loadingQueue:LoaderMax;
      
      protected var _parsed:LoaderMax;
      
      protected var _initted:Boolean;
      
      public function XMLLoader(param1:*, param2:Object = null)
      {
         super(param1,param2);
         _preferEstimatedBytesInAudit = true;
         _type = "XMLLoader";
         _loader.dataFormat = "text";
      }
      
      protected static function _parseVars(param1:XML) : Object
      {
         var _loc8_:String = null;
         var _loc5_:String = null;
         var _loc4_:* = null;
         var _loc3_:String = null;
         var _loc2_:Object = {};
         var _loc7_:XMLList = param1.attributes();
         for each(var _loc6_ in _loc7_)
         {
            _loc3_ = _loc6_.name();
            _loc5_ = _loc6_.toString();
            if(_loc3_ != "url")
            {
               if(_loc3_ == "context")
               {
                  _loc2_.context = new LoaderContext(true,_loc5_ == "own" ? ApplicationDomain.currentDomain : (_loc5_ == "separate" ? new ApplicationDomain() : new ApplicationDomain(ApplicationDomain.currentDomain)),!_isLocal ? SecurityDomain.currentDomain : null);
               }
               else
               {
                  _loc8_ = typeof _varTypes[_loc3_];
                  if(_loc8_ == "boolean")
                  {
                     _loc2_[_loc3_] = _loc5_ == "true" || _loc5_ == "1";
                  }
                  else if(_loc8_ == "number")
                  {
                     _loc2_[_loc3_] = Number(_loc5_);
                  }
                  else
                  {
                     _loc2_[_loc3_] = _loc5_;
                  }
               }
            }
         }
         return _loc2_;
      }
      
      public static function parseLoaders(param1:XML, param2:LoaderMax, param3:LoaderMax = null) : void
      {
         var _loc4_:LoaderMax = null;
         var _loc6_:String = null;
         var _loc9_:Array = null;
         var _loc8_:Class = null;
         var _loc10_:int = 0;
         var _loc7_:LoaderCore = null;
         for each(var _loc5_ in param1.children())
         {
            _loc6_ = String(_loc5_.name()).toLowerCase();
            if(_loc6_ == "loadermax")
            {
               _loc4_ = param2.append(new LoaderMax(_parseVars(_loc5_))) as LoaderMax;
               if(param3 != null && _loc4_.vars.load)
               {
                  param3.append(_loc4_);
               }
               parseLoaders(_loc5_,_loc4_,param3);
               if("replaceURLText" in _loc4_.vars)
               {
                  _loc9_ = _loc4_.vars.replaceURLText.split(",");
                  _loc10_ = 0;
                  while(_loc10_ < _loc9_.length)
                  {
                     _loc4_.replaceURLText(_loc9_[_loc10_],_loc9_[_loc10_ + 1],false);
                     _loc10_ += 2;
                  }
               }
               if("prependURLs" in _loc4_.vars)
               {
                  _loc4_.prependURLs(_loc4_.vars.prependURLs,false);
               }
            }
            else
            {
               if(_loc6_ in _types)
               {
                  _loc8_ = _types[_loc6_];
                  _loc7_ = param2.append(new _loc8_(_loc5_.@url,_parseVars(_loc5_)));
                  if(param3 != null && _loc7_.vars.load)
                  {
                     param3.append(_loc7_);
                  }
               }
               parseLoaders(_loc5_,param2,param3);
            }
         }
      }
      
      override protected function _load() : void
      {
         if(!_initted)
         {
            _prepRequest();
            _loader.load(_request);
         }
         else if(_loadingQueue != null)
         {
            _changeQueueListeners(true);
            _loadingQueue.load(false);
         }
      }
      
      protected function _changeQueueListeners(param1:Boolean) : void
      {
         var _loc2_:String = null;
         if(_loadingQueue != null)
         {
            if(param1 && this.vars.integrateProgress != false)
            {
               _loadingQueue.addEventListener("complete",_completeHandler,false,0,true);
               _loadingQueue.addEventListener("progress",_progressHandler,false,0,true);
               _loadingQueue.addEventListener("fail",_failHandler,false,0,true);
               for(_loc2_ in _listenerTypes)
               {
                  if(_loc2_ != "onProgress" && _loc2_ != "onInit")
                  {
                     _loadingQueue.addEventListener(_listenerTypes[_loc2_],_passThroughEvent,false,0,true);
                  }
               }
            }
            else
            {
               _loadingQueue.removeEventListener("complete",_completeHandler);
               _loadingQueue.removeEventListener("progress",_progressHandler);
               _loadingQueue.removeEventListener("fail",_failHandler);
               for(_loc2_ in _listenerTypes)
               {
                  if(_loc2_ != "onProgress" && _loc2_ != "onInit")
                  {
                     _loadingQueue.removeEventListener(_listenerTypes[_loc2_],_passThroughEvent);
                  }
               }
            }
         }
      }
      
      override protected function _dump(param1:int = 0, param2:int = 0, param3:Boolean = false) : void
      {
         if(_loadingQueue != null)
         {
            _changeQueueListeners(false);
            if(param1 == 0)
            {
               _loadingQueue.cancel();
            }
            else
            {
               _loadingQueue.dispose(param1 == 3);
               _loadingQueue = null;
            }
         }
         if(param1 >= 1)
         {
            if(_parsed != null)
            {
               _parsed.dispose(param1 == 3);
               _parsed = null;
            }
            _initted = false;
         }
         _cacheIsDirty = true;
         var _loc4_:* = _content;
         super._dump(param1,param2,param3);
         if(param1 == 0)
         {
            _content = _loc4_;
         }
      }
      
      override protected function _calculateProgress() : void
      {
         _cachedBytesLoaded = _loader.bytesLoaded;
         if(_loader.bytesTotal != 0)
         {
            _cachedBytesTotal = _loader.bytesTotal;
         }
         if(_cachedBytesTotal < _cachedBytesLoaded || _initted)
         {
            _cachedBytesTotal = _cachedBytesLoaded;
         }
         var _loc1_:uint = uint(this.vars.estimatedBytes);
         if(this.vars.integrateProgress != false)
         {
            if(_loadingQueue != null && (uint(this.vars.estimatedBytes) < _cachedBytesLoaded || _loadingQueue.auditedSize))
            {
               if(_loadingQueue.status <= 2)
               {
                  _cachedBytesLoaded += _loadingQueue.bytesLoaded;
                  _cachedBytesTotal += _loadingQueue.bytesTotal;
               }
            }
            else if(uint(this.vars.estimatedBytes) > _cachedBytesLoaded && (!_initted || _loadingQueue != null && _loadingQueue.status <= 2 && !_loadingQueue.auditedSize))
            {
               _cachedBytesTotal = uint(this.vars.estimatedBytes);
            }
         }
         if(!_initted && _cachedBytesLoaded == _cachedBytesTotal)
         {
            _cachedBytesLoaded *= 0.99;
         }
         _cacheIsDirty = false;
      }
      
      public function getLoader(param1:String) : *
      {
         return _parsed != null ? _parsed.getLoader(param1) : null;
      }
      
      public function getContent(param1:String) : *
      {
         if(param1 == this.name || param1 == _url)
         {
            return _content;
         }
         var _loc2_:LoaderCore = this.getLoader(param1);
         return _loc2_ != null ? _loc2_.content : null;
      }
      
      public function getChildren(param1:Boolean = false, param2:Boolean = false) : Array
      {
         return _parsed != null ? _parsed.getChildren(param1,param2) : [];
      }
      
      override protected function _progressHandler(param1:Event) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(_dispatchProgress)
         {
            _loc2_ = _cachedBytesLoaded;
            _loc3_ = _cachedBytesTotal;
            _calculateProgress();
            if(_cachedBytesLoaded != _cachedBytesTotal && (_loc2_ != _cachedBytesLoaded || _loc3_ != _cachedBytesTotal))
            {
               dispatchEvent(new LoaderEvent("progress",this));
            }
         }
         else
         {
            _cacheIsDirty = true;
         }
      }
      
      override protected function _passThroughEvent(param1:Event) : void
      {
         if(param1.target != _loadingQueue)
         {
            super._passThroughEvent(param1);
         }
      }
      
      override protected function _receiveDataHandler(param1:Event) : void
      {
         try
         {
            _content = new XML(_loader.data);
         }
         catch(error:Error)
         {
            _content = _loader.data;
            _failHandler(new LoaderEvent("error",this,error.message));
            return;
         }
         dispatchEvent(new LoaderEvent(RAW_LOAD,this,"",_content));
         _initted = true;
         _loadingQueue = new LoaderMax({"name":this.name + "_Queue"});
         _parsed = new LoaderMax({
            "name":this.name + "_ParsedLoaders",
            "paused":true
         });
         parseLoaders(_content as XML,_parsed,_loadingQueue);
         if(_parsed.numChildren == 0)
         {
            _parsed.dispose(false);
            _parsed = null;
         }
         if(_loadingQueue.getChildren(true,true).length == 0)
         {
            _loadingQueue.empty(false);
            _loadingQueue.dispose(false);
            _loadingQueue = null;
         }
         else
         {
            _cacheIsDirty = true;
            _changeQueueListeners(true);
            _loadingQueue.load(false);
         }
         dispatchEvent(new LoaderEvent("init",this,"",_content));
         if(_loadingQueue == null || this.vars.integrateProgress == false)
         {
            _completeHandler(param1);
         }
      }
      
      override protected function _completeHandler(param1:Event = null) : void
      {
         _calculateProgress();
         if(this.progress == 1)
         {
            _changeQueueListeners(false);
            super._completeHandler(param1);
         }
      }
      
      override public function get progress() : Number
      {
         return this.bytesTotal != 0 ? _cachedBytesLoaded / _cachedBytesTotal : (_status == 2 || _initted ? 1 : 0);
      }
   }
}

