package UI
{
   import UI.UIInterface.OldInterface.ICell;
   import flash.events.MouseEvent;
   
   public class BorderEngine
   {
      
      private static var instance:BorderEngine = null;
      
      public function BorderEngine()
      {
         if(instance == null)
         {
            super();
            instance = this;
            return;
         }
         throw new Error("fuck you!没看见实例已经存在么？！");
      }
      
      public static function getInstance() : BorderEngine
      {
         if(instance == null)
         {
            instance = new BorderEngine();
         }
         return instance;
      }
      
      public function clear() : void
      {
         instance = null;
      }
      
      public function addFrameListener(param1:Vector.<ICell>) : void
      {
         for each(var _loc2_ in param1)
         {
            _loc2_.addEventListener("rollOver",rollOverCell,false,0,true);
            _loc2_.addEventListener("rollOut",rollOutCell,false,0,true);
         }
      }
      
      private function rollOverCell(param1:MouseEvent) : void
      {
         param1.target.showBorder();
      }
      
      private function rollOutCell(param1:MouseEvent) : void
      {
         param1.target.hideBorder();
      }
   }
}

