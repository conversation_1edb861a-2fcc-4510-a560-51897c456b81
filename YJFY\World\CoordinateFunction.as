package YJFY.World
{
   import YJFY.geom.Area3D.Cuboid;
   
   public class CoordinateFunction
   {
      
      public function CoordinateFunction()
      {
         super();
      }
      
      public static function cuboidRangeTranToWorld(param1:int, param2:Coordinate, param3:Cuboid, param4:Cuboid) : void
      {
         var _loc5_:Number = param1 == 1 ? param3.getX() + param2.getX() : param3.getMaxX() * param1 + param2.getX();
         param4.setTo(_loc5_,param3.getY() + param2.getY(),param3.getZ() + param2.getZ(),param3.getXRange(),param3.getYRange(),param3.getZRange());
      }
      
      public static function cuboidRangeTranToWorld2(param1:int, param2:Number, param3:Number, param4:Number, param5:Cuboid, param6:Cuboid) : void
      {
         var _loc7_:Number = param1 == 1 ? param5.getX() + param2 : param5.getMaxX() * param1 + param2;
         param6.setTo(_loc7_,param5.getY() + param3,param5.getZ() + param4,param5.getXRange(),param5.getYRange(),param5.getZRange());
      }
   }
}

