package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class CuboidAreaAttackSkill_OneSkillShow extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      protected var m_skillShowDefId:String;
      
      protected var m_skillShowIsFrontOfBody:Boolean;
      
      protected var m_skillShowAnimation:AnimationShowPlayLogicShell;
      
      protected var m_skillShowAnimationListener:AnimationPlayFrameLabelListener;
      
      public function CuboidAreaAttackSkill_OneSkillShow()
      {
         super();
         m_skillShowAnimation = new AnimationShowPlayLogicShell();
         m_skillShowAnimationListener = new AnimationPlayFrameLabelListener();
         m_skillShowAnimationListener.reachFrameLabelFun = reachFrameLabel;
         m_skillShowAnimation.addFrameLabelListener(m_skillShowAnimationListener);
      }
      
      override public function clear() : void
      {
         m_skillShowDefId = null;
         ClearUtil.clearObject(m_skillShowAnimation);
         m_skillShowAnimation = null;
         ClearUtil.clearObject(m_skillShowAnimationListener);
         m_skillShowAnimationListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillShowDefId = String(param1.@skillShowDefId);
         m_skillShowIsFrontOfBody = Boolean(int(param1.@skillShowIsFrontOfBody));
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_skillShowAnimation.setShow(m_owner.getAnimationByDefId(m_skillShowDefId),true);
         m_owner.addOtherAniamtion(m_skillShowAnimation,m_skillShowIsFrontOfBody);
         m_skillShowAnimation.gotoAndPlay("1");
         (m_skillShowAnimation.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
      }
      
      override protected function endSkill2() : void
      {
         m_owner.removeOtherAnimation(m_skillShowAnimation);
         super.endSkill2();
      }
      
      override public function showRender() : void
      {
         super.showRender();
      }
   }
}

