package YJFY.PKMode2
{
   import UI.DataManagerParent;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class PKMode2VO extends DataManagerParent
   {
      
      private const m_const_savePKTargetPlayerDataNum:uint = 6;
      
      private var m_pKResultNumDataTime:String;
      
      private var m_allPKResultNum:uint;
      
      private var m_winPKResultNum:uint;
      
      private var m_failPKResultNum:uint;
      
      private var m_pkNum:uint;
      
      private var m_pkNumDataTime:String;
      
      private var m_pkTargetPlayerDatas:Vector.<PKTargetPlayerData>;
      
      public function PKMode2VO()
      {
         super();
         m_pkTargetPlayerDatas = new Vector.<PKTargetPlayerData>();
      }
      
      override public function clear() : void
      {
         m_pKResultNumDataTime = null;
         m_pkNumDataTime = null;
         ClearUtil.clearObject(m_pkTargetPlayerDatas);
         m_pkTargetPlayerDatas = null;
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.pKResultNumDataTime = m_pKResultNumDataTime;
         _antiwear.allPKResultNum = m_allPKResultNum;
         _antiwear.winPKResultNum = m_winPKResultNum;
         _antiwear.failPKResultNum = m_failPKResultNum;
         _antiwear.pkNum = m_pkNum;
         _antiwear.pkNumDataTime = m_pkNumDataTime;
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:PKTargetPlayerData = null;
         if(param1 == null)
         {
            return;
         }
         this.pKResultNumDataTime = String(param1.@rT);
         this.allPKResultNum = uint(param1.@allRN);
         this.winPKResultNum = uint(param1.@winRN);
         this.failPKResultNum = uint(param1.@failRN);
         this.pkNumDataTime = String(param1.@pkNT);
         this.pkNum = uint(param1.@pkN);
         var _loc4_:XMLList = param1.player;
         _loc3_ = int(_loc4_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc2_ = new PKTargetPlayerData(String(_loc4_[_loc5_].@uid),_loc4_[_loc5_].@sI,_loc4_[_loc5_].@pkI,_loc4_[_loc5_].@pkS,_loc4_[_loc5_].@uN,_loc4_[_loc5_].@p1T,_loc4_[_loc5_].@nN,null);
            m_pkTargetPlayerDatas.push(_loc2_);
            _loc5_++;
         }
      }
      
      public function exportSaveXML(param1:String) : XML
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:XML = new XML("<" + (param1 + " ") + "/>");
         if(this.pKResultNumDataTime)
         {
            _loc3_.@rT = this.pKResultNumDataTime;
         }
         if(this.allPKResultNum)
         {
            _loc3_.@allRN = this.allPKResultNum;
         }
         if(this.winPKResultNum)
         {
            _loc3_.@winRN = this.winPKResultNum;
         }
         if(this.failPKResultNum)
         {
            _loc3_.@failRN = this.failPKResultNum;
         }
         if(this.pkNumDataTime)
         {
            _loc3_.@pkNT = this.pkNumDataTime;
         }
         if(this.pkNum)
         {
            _loc3_.@pkN = this.pkNum;
         }
         _loc2_ = int(m_pkTargetPlayerDatas.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_.appendChild(<player />);
            _loc3_.children()[_loc4_].@pkI = m_pkTargetPlayerDatas[_loc4_].getPKIndex();
            _loc3_.children()[_loc4_].@uid = m_pkTargetPlayerDatas[_loc4_].getUid() ? m_pkTargetPlayerDatas[_loc4_].getUid() : "";
            _loc3_.children()[_loc4_].@sI = m_pkTargetPlayerDatas[_loc4_].getIndexOfSaveXML();
            _loc3_.children()[_loc4_].@pkS = m_pkTargetPlayerDatas[_loc4_].getPKState();
            _loc3_.children()[_loc4_].@p1T = m_pkTargetPlayerDatas[_loc4_].getPlayer1Type() ? m_pkTargetPlayerDatas[_loc4_].getPlayer1Type() : "";
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function resetPKResultDataByTime(param1:String, param2:uint, param3:String) : void
      {
         if(Boolean(pKResultNumDataTime) == false)
         {
            pKResultNumDataTime = param1;
            zeroPKResultData(param1);
            return;
         }
         var _loc4_:int = Math.floor(new TimeUtil().timeInterval(param3,pKResultNumDataTime) / param2);
         var _loc5_:int = Math.floor(new TimeUtil().timeInterval(param3,param1) / param2);
         if(_loc4_ != _loc5_)
         {
            zeroPKResultData(param1);
         }
      }
      
      public function resetPKNum(param1:String) : void
      {
         if(new TimeUtil().newDateIsNewDay(pkNumDataTime,param1))
         {
            pkNumDataTime = param1;
            pkNum = 0;
         }
      }
      
      public function addOneResult_DefaultFail(param1:PKTargetPlayerData) : void
      {
         m_pkTargetPlayerDatas.unshift(param1);
         var _loc2_:Vector.<PKTargetPlayerData> = m_pkTargetPlayerDatas.slice(0,6);
         ClearUtil.nullArr(m_pkTargetPlayerDatas,false,false,false);
         m_pkTargetPlayerDatas = null;
         m_pkTargetPlayerDatas = _loc2_;
         ++this.failPKResultNum;
         ++this.allPKResultNum;
         ++this.pkNum;
      }
      
      public function tranFailToWin(param1:PKTargetPlayerData) : void
      {
         var _loc2_:int = int(m_pkTargetPlayerDatas.indexOf(param1));
         if(_loc2_ == -1)
         {
            throw new Error("There is not the pkTargetPlayerData in pkTargetPlayerDatas");
         }
         --this.failPKResultNum;
         ++this.winPKResultNum;
      }
      
      private function zeroPKResultData(param1:String) : void
      {
         pKResultNumDataTime = param1;
         allPKResultNum = 0;
         winPKResultNum = 0;
         failPKResultNum = 0;
      }
      
      public function getAllPKResultNum() : uint
      {
         return allPKResultNum;
      }
      
      public function getWinPKResultNum() : uint
      {
         return winPKResultNum;
      }
      
      public function getFailPKResultNum() : uint
      {
         return failPKResultNum;
      }
      
      public function getPKNum() : uint
      {
         return pkNum;
      }
      
      public function getPKTargetPlayerDataNum() : uint
      {
         return m_pkTargetPlayerDatas.length;
      }
      
      public function getPKTargetPlayerDataByIndex(param1:int) : PKTargetPlayerData
      {
         return m_pkTargetPlayerDatas[param1];
      }
      
      private function get pKResultNumDataTime() : String
      {
         return _antiwear.pKResultNumDataTime;
      }
      
      private function set pKResultNumDataTime(param1:String) : void
      {
         _antiwear.pKResultNumDataTime = param1;
      }
      
      private function get allPKResultNum() : uint
      {
         return _antiwear.allPKResultNum;
      }
      
      private function set allPKResultNum(param1:uint) : void
      {
         _antiwear.allPKResultNum = param1;
      }
      
      private function get winPKResultNum() : uint
      {
         return _antiwear.winPKResultNum;
      }
      
      private function set winPKResultNum(param1:uint) : void
      {
         _antiwear.winPKResultNum = param1;
      }
      
      private function get failPKResultNum() : uint
      {
         return _antiwear.failPKResultNum;
      }
      
      private function set failPKResultNum(param1:uint) : void
      {
         _antiwear.failPKResultNum = param1;
      }
      
      private function get pkNum() : uint
      {
         return _antiwear.pkNum;
      }
      
      private function set pkNum(param1:uint) : void
      {
         _antiwear.pkNum = param1;
      }
      
      private function get pkNumDataTime() : String
      {
         return _antiwear.pkNumDataTime;
      }
      
      private function set pkNumDataTime(param1:String) : void
      {
         _antiwear.pkNumDataTime = param1;
      }
   }
}

