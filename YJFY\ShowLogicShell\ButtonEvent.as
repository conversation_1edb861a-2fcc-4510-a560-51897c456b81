package YJFY.ShowLogicShell
{
   import flash.events.Event;
   
   public class Button<PERSON><PERSON> extends Event
   {
      
      public static const CLICK_BUTTON:String = "clickButton";
      
      public static const CLICK_LOCK_BUTTON:String = "clickLockBtn";
      
      public static const ON_OVER_BUTTON:String = "onOverButton";
      
      public static const ON_OUT_BUTTON:String = "onOutButton";
      
      private var _button:IButton;
      
      public function ButtonEvent(param1:String, param2:IButton, param3:Boolean = false, param4:<PERSON>olean = false)
      {
         super(param1,param3,param4);
         _button = param2;
      }
      
      public function get button() : IButton
      {
         return _button;
      }
      
      public function set button(param1:IButton) : void
      {
         _button = param1;
      }
   }
}

