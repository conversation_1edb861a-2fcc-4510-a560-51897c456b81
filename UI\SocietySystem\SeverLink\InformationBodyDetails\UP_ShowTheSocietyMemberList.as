package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class UP_ShowTheSocietyMemberList extends InformationBodyDetail
   {
      
      private var m_societyId:int;
      
      private var m_pageIndex:int;
      
      private var m_num:int;
      
      public function UP_ShowTheSocietyMemberList()
      {
         super();
         m_informationBodyId = 3008;
      }
      
      public function initData(param1:int, param2:int, param3:int) : void
      {
         m_societyId = param1;
         m_pageIndex = param2;
         m_num = param3;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_societyId);
         _loc1_.writeInt(m_pageIndex + 1);
         _loc1_.writeInt(m_num);
         return _loc1_;
      }
      
      public function getSocietyId() : int
      {
         return m_societyId;
      }
      
      public function getPageIndex() : int
      {
         return m_pageIndex;
      }
      
      public function getNum() : int
      {
         return m_num;
      }
   }
}

