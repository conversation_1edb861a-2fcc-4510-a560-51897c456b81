package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class CuboidAreaAttackSkill_OneSkillShow2 extends CuboidAreaAttackSkill_OneSkillShow
   {
      
      protected var m_releaseFrameLabel:String;
      
      protected var m_releaseFrameLabel2:String;
      
      protected var m_skillAttackReachFrameLabel:String;
      
      protected var m_skillEndStartFrameLabel:String;
      
      protected var m_skillEndEndFrameLabel:String;
      
      protected var m_bodyEndStartFrameLabel:String;
      
      public function CuboidAreaAttackSkill_OneSkillShow2()
      {
         super();
         m_isAttackReachWhenRelease = false;
      }
      
      override public function clear() : void
      {
         m_releaseFrameLabel = null;
         m_releaseFrameLabel2 = null;
         m_skillAttackReachFrameLabel = null;
         m_skillEndEndFrameLabel = null;
         m_skillEndStartFrameLabel = null;
         m_bodyEndStartFrameLabel = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc2_:XML = param1.frameLabels[0];
         m_releaseFrameLabel = String(_loc2_.@releaseFrameLabel);
         m_releaseFrameLabel2 = String(_loc2_.@releaseFrameLabel2);
         m_skillAttackReachFrameLabel = String(_loc2_.@skillAttackReachFrameLabel);
         m_skillEndStartFrameLabel = String(_loc2_.@skillEndStartFrameLabel);
         m_skillEndEndFrameLabel = String(_loc2_.@skillEndEndFrameLabel);
         m_bodyEndStartFrameLabel = String(_loc2_.@bodyEndStartFrameLabel);
      }
      
      override public function setOwner(param1:AnimalEntity) : void
      {
         super.setOwner(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         m_endTime = param1.getWorldTime() + m_timeOfDuration;
         listenerRealseSkill();
         releaseSkill_in();
         m_owner.hideShadow();
         if(m_isAttackReachWhenRelease)
         {
            attackReach(param1);
         }
      }
      
      protected function playSkillAnimation() : void
      {
         m_endTime = m_world.getWorldTime() + m_timeOfDuration;
         m_skillShowAnimation.setShow(m_owner.getAnimationByDefId(m_skillShowDefId),true);
         m_owner.addOtherAniamtion(m_skillShowAnimation,m_skillShowIsFrontOfBody);
         m_skillShowAnimation.gotoAndPlay("1");
         (m_skillShowAnimation.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
      }
      
      override protected function endSkill1() : void
      {
         m_endTime = NaN;
         m_skillShowAnimation.gotoAndPlay(m_skillEndStartFrameLabel);
         if(m_bodyEndStartFrameLabel)
         {
            m_owner.currentAnimationGotoAndPlay(m_bodyEndStartFrameLabel);
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         if(m_isRun == false)
         {
            return;
         }
         super.reachFrameLabel(param1);
         reachFrameLabel2(param1);
      }
      
      protected function reachFrameLabel2(param1:String) : void
      {
         switch(param1)
         {
            case m_releaseFrameLabel:
               m_owner.currentAnimationStop();
               playSkillAnimation();
               break;
            case m_releaseFrameLabel2:
               m_owner.currentAnimationPlay();
               break;
            case m_skillAttackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_skillEndEndFrameLabel:
               endSkill2();
         }
      }
   }
}

