package UI2.TipDataShow
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class OneTipData
   {
      
      private var m_startTime:String;
      
      private var m_endTime:String;
      
      private var m_interval:uint;
      
      private var m_duration:uint;
      
      private var m_lastShowTime:uint;
      
      private var m_text:String;
      
      private var m_timeUtil:TimeUtil;
      
      public function OneTipData()
      {
         super();
         m_timeUtil = new TimeUtil();
      }
      
      public function clear() : void
      {
         m_startTime = null;
         m_endTime = null;
         m_text = null;
         ClearUtil.clearObject(m_timeUtil);
         m_timeUtil = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         m_startTime = String(param1.@startTime);
         m_endTime = String(param1.@endTime);
         m_interval = uint(param1.@interval);
         m_duration = uint(param1.@duration);
         m_text = String(param1.@txt);
      }
      
      public function showText(param1:uint) : String
      {
         m_lastShowTime = param1;
         return m_text;
      }
      
      public function getIsAbleShowText(param1:uint) : Boolean
      {
         if(m_timeUtil.isInTimeOfOneDay(m_timeUtil.getTimeStr(),m_startTime,m_endTime) == false)
         {
            return false;
         }
         if(m_lastShowTime == 0)
         {
            return true;
         }
         if(param1 - m_lastShowTime > m_interval)
         {
            return true;
         }
         return false;
      }
      
      public function getIsHideTime(param1:uint) : Boolean
      {
         if(param1 - m_lastShowTime > m_duration)
         {
            return true;
         }
         return false;
      }
   }
}

