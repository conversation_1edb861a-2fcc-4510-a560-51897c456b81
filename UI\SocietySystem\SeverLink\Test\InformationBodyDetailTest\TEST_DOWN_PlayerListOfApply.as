package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_PlayerListOfApply;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.PlayerDataOfApply;
   
   public class TEST_DOWN_PlayerListOfApply extends DOWN_PlayerListOfApply
   {
      
      public function TEST_DOWN_PlayerListOfApply()
      {
         super();
         m_informationBodyId = 3020;
      }
      
      public function initData(param1:int, param2:Vector.<PlayerDataOfApply>) : void
      {
         m_playerNumOfApply = param1;
         m_playerDatasOfApply = param2;
      }
   }
}

