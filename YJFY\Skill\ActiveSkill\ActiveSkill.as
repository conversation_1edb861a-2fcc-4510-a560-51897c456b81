package YJFY.Skill.ActiveSkill
{
   import YJFY.Entity.IEntity;
   import YJFY.Skill.ISkillListener;
   import YJFY.Skill.Skill;
   import YJFY.World.World;
   
   public class ActiveSkill extends Skill
   {
      
      protected var m_endTime:Number;
      
      protected var m_isRun:Boolean;
      
      protected var m_timeOfDuration:int;
      
      protected var m_isAbleChangeDirection:Boolean;
      
      private var m_oldDirectionX:Number;
      
      private var m_oldDirectionY:Number;
      
      private var m_oldShowDirection:int;
      
      public function ActiveSkill()
      {
         super();
         m_isAbleChangeDirection = false;
         m_entityListener.setDirectionFun = setDirection_inActiveSkill;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function isAbleRun() : Boolean
      {
         if(m_owner.getWorld() == null)
         {
            return false;
         }
         if(m_owner.getWorld().isStop())
         {
            return false;
         }
         return true;
      }
      
      public function isInRun() : Boolean
      {
         return m_isRun;
      }
      
      public function startSkill(param1:World) : Boolean
      {
         return startSkill2(param1);
      }
      
      public function releaseSkill(param1:World) : void
      {
      }
      
      protected function startSkill2(param1:World) : Boolean
      {
         if(isAbleRun() == false)
         {
            return false;
         }
         if(m_isRun)
         {
            return false;
         }
         m_isRun = true;
         m_endTime = NaN;
         listenerStartSkill();
         return true;
      }
      
      public function releaseskill3(param1:World) : void
      {
         releaseSkill2(param1);
      }
      
      protected function releaseSkill2(param1:World) : void
      {
         if(m_isOutWorldTime)
         {
            m_endTime = param1.getWorldExistTime() + m_timeOfDuration;
         }
         else
         {
            m_endTime = param1.getWorldTime() + m_timeOfDuration;
         }
         listenerRealseSkill();
         releaseSkill_in();
         m_owner.hideShadow();
      }
      
      protected function releaseSkill_in() : void
      {
         m_oldDirectionX = m_owner.getDirectionX();
         m_oldDirectionY = m_owner.getDirectionY();
         m_oldShowDirection = m_owner.getShowDirection();
      }
      
      public function forceEndSkill() : void
      {
         endSkill2();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_timeOfDuration = int(param1.@skillTimeOfDuration);
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun == false)
         {
            return;
         }
         if(m_isOutWorldTime)
         {
            if(!isNaN(m_endTime) && m_isRun && m_timeOfDuration && m_endTime < param1.getWorldExistTime())
            {
               if(m_isRun)
               {
                  endSkill1();
               }
               return;
            }
         }
         else if(!isNaN(m_endTime) && m_isRun && m_timeOfDuration && m_endTime < param1.getWorldTime())
         {
            if(m_isRun)
            {
               endSkill1();
            }
            return;
         }
      }
      
      public function stopSkill() : void
      {
         endSkill1();
      }
      
      public function hideall() : void
      {
         m_owner.hideBodyShow();
         m_owner.hideShadow();
      }
      
      protected function endSkill1() : void
      {
         endSkill2();
      }
      
      protected function endSkill2() : void
      {
         m_isRun = false;
         m_endTime = NaN;
         m_owner.idle();
         m_owner.showShadow();
         listenerEndSkill();
      }
      
      protected function listenerStartSkill() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<ISkillListener> = m_skillListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].startSkill(this);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_ = null;
      }
      
      protected function listenerEndSkill() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<ISkillListener> = m_skillListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].endSkill(this);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_ = null;
      }
      
      protected function listenerRealseSkill() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Vector.<ISkillListener> = m_skillListeners.slice(0);
         var _loc2_:int = int(_loc1_.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc1_[_loc3_])
            {
               _loc1_[_loc3_].releaseSkill(this);
            }
            _loc1_[_loc3_] = null;
            _loc3_++;
         }
         _loc1_ = null;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         if(m_isRun == false)
         {
            return;
         }
         super.reachFrameLabel(param1);
      }
      
      private function setDirection_inActiveSkill(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
         if(m_isAbleChangeDirection == false && m_isRun)
         {
            if(m_owner)
            {
               m_owner.forceSetDirection(m_oldDirectionX,m_oldDirectionY);
               m_owner.setShowDirection(m_oldShowDirection);
            }
         }
      }
      
      public function setIsAbleChangeDirection(param1:Boolean) : void
      {
         m_isAbleChangeDirection = param1;
      }
   }
}

