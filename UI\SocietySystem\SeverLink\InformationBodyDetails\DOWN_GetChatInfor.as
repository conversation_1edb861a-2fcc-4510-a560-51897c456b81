package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class DOWN_GetChatInfor extends InformationBodyDetail
   {
      
      protected var m_uid:Number;
      
      protected var m_idx:int;
      
      protected var m_name:String;
      
      protected var m_nameLength:int;
      
      protected var m_chatStrLength:int;
      
      protected var m_chatStr:String;
      
      protected var m_extraData:int;
      
      public function DOWN_GetChatInfor()
      {
         super();
         m_informationBodyId = 3043;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         var _loc2_:UidAndIdxData = new InformationBodyDetailUtil().readUidAndIdxFromByteArr(param1);
         m_uid = _loc2_.uid;
         m_idx = _loc2_.idx;
         trace("聊天来源uid:",m_uid,"idx:",m_idx);
         m_nameLength = param1.readInt();
         trace("聊天名称长度：",m_nameLength);
         if(m_nameLength)
         {
            m_name = param1.readUTFBytes(m_nameLength);
            trace("聊天人名称：",m_name);
         }
         m_chatStrLength = param1.readInt();
         if(m_chatStrLength)
         {
            m_chatStr = param1.readUTFBytes(m_chatStrLength);
         }
         m_extraData = param1.readInt();
      }
      
      public function getUid() : Number
      {
         return m_uid;
      }
      
      public function getIdx() : int
      {
         return m_idx;
      }
      
      public function getName() : String
      {
         return m_name;
      }
      
      public function getChatStr() : String
      {
         return m_chatStr;
      }
      
      public function getExtraData() : int
      {
         return m_extraData;
      }
   }
}

