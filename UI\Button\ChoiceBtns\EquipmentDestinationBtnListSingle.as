package UI.Button.ChoiceBtns
{
   import UI.TextTrace.traceText;
   
   public class EquipmentDestinationBtnListSingle extends EquipmentDestinationBtnList
   {
      
      private static var instance:EquipmentDestinationBtnListSingle = null;
      
      public function EquipmentDestinationBtnListSingle()
      {
         if(instance == null)
         {
            super();
            instance = this;
         }
         else
         {
            traceText("fuck you!没看见实例已经存在么?!");
         }
      }
      
      public static function getInstance() : EquipmentDestinationBtnListSingle
      {
         if(instance == null)
         {
            instance = new EquipmentDestinationBtnListSingle();
         }
         return instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         instance = null;
      }
   }
}

