package YJFY.Utils
{
   public class ListenerUtil
   {
      
      public function ListenerUtil()
      {
         super();
      }
      
      public static function addListener(param1:*, param2:*) : void
      {
         var _loc3_:int = int(param1.indexOf(param2));
         if(_loc3_ != -1)
         {
            return;
         }
         param1.push(param2);
      }
      
      public static function removeListener(param1:*, param2:*) : void
      {
         if(param1 == null)
         {
            return;
         }
         var _loc3_:int = int(param1.indexOf(param2));
         while(_loc3_ != -1)
         {
            param1.splice(_loc3_,1);
            _loc3_ = int(param1.indexOf(param2));
         }
      }
   }
}

