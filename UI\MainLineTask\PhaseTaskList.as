package UI.MainLineTask
{
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class PhaseTaskList
   {
      
      public var phase:String;
      
      public var needLevel:int;
      
      public var taskVOs:Vector.<MainLineTaskVO>;
      
      public var description_NotReachLevel:String;
      
      public function PhaseTaskList()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(taskVOs);
         taskVOs = null;
      }
      
      public function initByXML(param1:XML, param2:XML, param3:XML) : void
      {
         var _loc11_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:String = null;
         var _loc6_:XML = null;
         var _loc10_:MainLineTaskVO = null;
         var _loc9_:XMLList = null;
         var _loc8_:int = 0;
         phase = String(param1.@phase);
         needLevel = int(param1.@needLevel);
         description_NotReachLevel = String(param1.@description_NotReachLevel);
         var _loc7_:XMLList = param1.task;
         _loc5_ = int(_loc7_ ? _loc7_.length() : 0);
         taskVOs = new Vector.<MainLineTaskVO>();
         _loc11_ = 0;
         while(_loc11_ < _loc5_)
         {
            _loc4_ = String(_loc7_[_loc11_].@id);
            _loc6_ = param2.task.(@id == _loc4_)[0];
            _loc10_ = new MainLineTaskVO();
            _loc10_.initByXML(_loc6_,param3);
            taskVOs.push(_loc10_);
            _loc9_ = XMLSingle.getInstance().mainLineTaskDescription.taskDescription;
            _loc8_ = 0;
            while(_loc8_ < _loc9_.length())
            {
               if(_loc10_.descriptionId == String(_loc9_[_loc8_].@id))
               {
                  _loc10_.txtDescripton = String(_loc9_[_loc8_].completeConditionDescription[0].@value);
                  break;
               }
               _loc8_++;
            }
            _loc11_++;
         }
      }
   }
}

