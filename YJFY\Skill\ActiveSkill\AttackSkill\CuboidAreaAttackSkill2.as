package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   import YJFY.geom.Area3D.Cuboid;
   
   public class CuboidAreaAttackSkill2 extends AreaAttackSkill implements ICuboidAreaAttackSkill
   {
      
      protected var m_cuboidRange:Cuboid;
      
      protected var m_attackPoint:Coordinate;
      
      protected var m_attackPointToScreen:Coordinate;
      
      protected var m_cuboidRangeToWorld:Cuboid;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      public function CuboidAreaAttackSkill2()
      {
         super();
         m_cuboidRange = new Cuboid();
         m_attackPoint = new Coordinate();
         m_attackPointToScreen = new Coordinate();
         m_cuboidRangeToWorld = new Cuboid();
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = attackSuccess;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_cuboidRange);
         m_cuboidRange = null;
         ClearUtil.clearObject(m_attackPoint);
         m_attackPoint = null;
         ClearUtil.clearObject(m_attackPointToScreen);
         m_attackPointToScreen = null;
         ClearUtil.clearObject(m_cuboidRangeToWorld);
         m_cuboidRangeToWorld = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         super.clear();
      }
      
      public function setAttackPoint(param1:Number, param2:Number, param3:Number) : void
      {
         setAttackPoint_in(param1,param2,param3);
      }
      
      override public function setWorld(param1:World) : void
      {
         super.setWorld(param1);
         if(m_world)
         {
            m_world.getTransformCoordinate().worldCoordToScreenCoord2(m_attackPoint,m_attackPointToScreen);
         }
      }
      
      public function getCuboidRange() : Cuboid
      {
         return m_cuboidRange.clone();
      }
      
      public function getCuboidRangeToWorld() : Cuboid
      {
         getCuboidRangeToWorld2();
         return m_cuboidRangeToWorld;
      }
      
      protected function setAttackPoint_in(param1:Number, param2:Number, param3:Number) : void
      {
         m_attackPoint.setX(param1);
         m_attackPoint.setY(param2);
         m_attackPoint.setZ(param3);
         getCuboidRangeToWorld2();
         m_world.getTransformCoordinate().worldCoordToScreenCoord2(m_attackPoint,m_attackPointToScreen);
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_cuboidRange.setTo(param1.@x,param1.@y,param1.@z,param1.@xRange,param1.@yRange,param1.@zRange);
      }
      
      override protected function attackReach(param1:World) : void
      {
         super.attackReach(param1);
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         getCuboidRangeToWorld2();
         if(m_cuboidRangeToWorld.contains(param1,param2,param3))
         {
            return true;
         }
         return false;
      }
      
      protected function getCuboidRangeToWorld2() : void
      {
         m_cuboidRangeToWorld.setTo(m_cuboidRange.getX() + m_attackPoint.getX(),m_cuboidRange.getY() + m_attackPoint.getY(),m_cuboidRange.getZ() + m_attackPoint.getZ(),m_cuboidRange.getXRange(),m_cuboidRange.getYRange(),m_cuboidRange.getZRange());
      }
   }
}

