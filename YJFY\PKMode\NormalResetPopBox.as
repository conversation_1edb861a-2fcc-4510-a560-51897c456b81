package YJFY.PKMode
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class NormalResetPopBox extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_resetNumText:TextField;
      
      private var m_needPointTicketText:TextField;
      
      private var m_totalPointTicketText:TextField;
      
      private var m_rechBtn:ButtonLogicShell;
      
      private var m_sureBtn:ButtonLogicShell;
      
      private var m_cancelBtn:ButtonLogicShell;
      
      private var m_font:FangZhengKaTongJianTi;
      
      public function NormalResetPopBox()
      {
         super();
         m_rechBtn = new ButtonLogicShell();
         m_sureBtn = new ButtonLogicShell();
         m_cancelBtn = new ButtonLogicShell();
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         m_resetNumText = null;
         m_needPointTicketText = null;
         m_totalPointTicketText = null;
         ClearUtil.clearObject(m_rechBtn);
         m_rechBtn = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_font = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("ResetPKTip") as MovieClip;
         addChild(m_show);
         m_resetNumText = m_show["pkNum"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_resetNumText);
         m_needPointTicketText = m_show["pkPrice"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_needPointTicketText);
         m_totalPointTicketText = m_show["value"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_totalPointTicketText);
         m_rechBtn.setShow(m_show["addValue"]);
         m_sureBtn.setShow(m_show["yesBtn"]);
         m_cancelBtn.setShow(m_show["noBtn"]);
      }
      
      public function setData(param1:uint, param2:uint, param3:Number) : void
      {
         m_resetNumText.text = param1.toString();
         m_needPointTicketText.text = param2.toString();
         m_totalPointTicketText.text = param3.toString();
      }
      
      public function getRechBtn() : ButtonLogicShell
      {
         return m_rechBtn;
      }
      
      public function getSureBtn() : ButtonLogicShell
      {
         return m_sureBtn;
      }
      
      public function getCancelBtn() : ButtonLogicShell
      {
         return m_cancelBtn;
      }
   }
}

