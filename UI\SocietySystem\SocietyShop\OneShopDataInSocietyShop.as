package UI.SocietySystem.SocietyShop
{
   import UI.DataManagerParent;
   import UI.MyFunction;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class OneShopDataInSocietyShop extends DataManagerParent
   {
      
      private var m_shopLevel:int;
      
      private var m_needMaxSocietyLevel:int;
      
      private var m_needMinSocietyLevel:int;
      
      private var m_goodDatas:Vector.<GoodData>;
      
      public function OneShopDataInSocietyShop()
      {
         super();
         m_goodDatas = new Vector.<GoodData>();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_goodDatas);
         m_goodDatas = null;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.shopLevel = m_shopLevel;
         _antiwear.needMaxSocietyLevel = m_needMaxSocietyLevel;
         _antiwear.needMinSocietyLevel = m_needMinSocietyLevel;
      }
      
      public function initFromXML(param1:XML, param2:String) : void
      {
         var _loc7_:int = 0;
         var _loc5_:GoodData = null;
         shopLevel = int(param1.@level);
         var _loc3_:Vector.<int> = MyFunction.getInstance().excreteString(param1.@societyLevel);
         needMinSocietyLevel = _loc3_[0];
         needMaxSocietyLevel = _loc3_[1];
         ClearUtil.clearObject(_loc3_);
         _loc3_ = null;
         var _loc6_:XMLList = param1.item;
         var _loc4_:int = int(_loc6_ ? _loc6_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            _loc5_ = new GoodData();
            _loc5_.setEquipmentVO(XMLSingle.getEquipmentVOByID(_loc6_[_loc7_].@id,XMLSingle.getInstance().equipmentXML,param2));
            _loc5_.setConValue(_loc6_[_loc7_].@conValue);
            _loc5_.setMaxNum(_loc6_[_loc7_].@num);
            m_goodDatas.push(_loc5_);
            _loc7_++;
         }
      }
      
      public function getGoodDataNum() : int
      {
         return m_goodDatas.length;
      }
      
      public function getGoodDataByIndex(param1:int) : GoodData
      {
         return m_goodDatas[param1];
      }
      
      public function getShopLevel() : int
      {
         return shopLevel;
      }
      
      public function getNeedMaxSocietyLevel() : int
      {
         return needMaxSocietyLevel;
      }
      
      public function getNeedMinSocietyLevel() : int
      {
         return needMinSocietyLevel;
      }
      
      private function get shopLevel() : int
      {
         return _antiwear.shopLevel;
      }
      
      private function set shopLevel(param1:int) : void
      {
         _antiwear.shopLevel = param1;
      }
      
      private function get needMaxSocietyLevel() : int
      {
         return _antiwear.needMaxSocietyLevel;
      }
      
      private function set needMaxSocietyLevel(param1:int) : void
      {
         _antiwear.needMaxSocietyLevel = param1;
      }
      
      private function get needMinSocietyLevel() : int
      {
         return _antiwear.needMinSocietyLevel;
      }
      
      private function set needMinSocietyLevel(param1:int) : void
      {
         _antiwear.needMinSocietyLevel = param1;
      }
   }
}

