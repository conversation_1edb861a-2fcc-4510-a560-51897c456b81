package YJFY.XydzjsLogic
{
   import UI.MyFunction;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.ISubAttackEntity;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.Part1;
   
   public class AddExpLogic
   {
      
      public function AddExpLogic()
      {
         super();
      }
      
      public function addExp(param1:IEntity, param2:EnemyXydzjs, param3:Player, param4:Player) : void
      {
         if(param3.getAnimalEntity() == param1 || (param3 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && (param3 as PlayerXydzjs).getPet().getAnimalEntity() == param1 || Boolean((param3 as PlayerXydzjs).getUiPlayer().playerVO.automaticPetVO) && (param3 as PlayerXydzjs).getAutoAttackPet().getAnimalEntity() == param1)
         {
            addExp2(param3 as PlayerXydzjs,param4 as PlayerXydzjs,param2);
         }
         else if(Boolean(param4) && (param4.getAnimalEntity() == param1 || (param4 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && (param4 as PlayerXydzjs).getPet().getAnimalEntity() == param1 || Boolean((param4 as PlayerXydzjs).getUiPlayer().playerVO.automaticPetVO) && (param4 as PlayerXydzjs).getAutoAttackPet().getAnimalEntity() == param1))
         {
            addExp2(param4 as PlayerXydzjs,param3 as PlayerXydzjs,param2);
         }
         else if((param3 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && param1.getId() == "pet18" && (param3 as PlayerXydzjs).getPet().getAnimalEntity().getId() == param1.getId())
         {
            addExp2(param3 as PlayerXydzjs,param4 as PlayerXydzjs,param2);
         }
         else if(param1 is ISubAttackEntity)
         {
            this.addExp((param1 as ISubAttackEntity).getOwner(),param2,param3,param4);
         }
         else
         {
            if(!(param1.getExtra() is ISubAttackEntity))
            {
               throw new Error("出错了");
            }
            this.addExp((param1.getExtra() as ISubAttackEntity).getOwner(),param2,param3,param4);
         }
      }
      
      private function addExp2(param1:PlayerXydzjs, param2:PlayerXydzjs, param3:EnemyXydzjs) : void
      {
         if(param2)
         {
            if(Part1.getInstance().getIsFive())
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),0.7 * param3.getExpOfDieThisEnemey() * 5);
            }
            else if((param1 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp > 0)
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),0.7 * param3.getExpOfDieThisEnemey() * (param1 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp);
            }
            else
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),0.7 * param3.getExpOfDieThisEnemey());
            }
            if((param1 as PlayerXydzjs).getUiPlayer().playerVO.pet)
            {
               MyFunction.getInstance().addPetExperience((param1 as PlayerXydzjs).getUiPlayer(),0.7 * param3.getExpOfDieThisEnemey());
            }
            if(Part1.getInstance().getIsFive())
            {
               MyFunction.getInstance().addPlayerExperience((param2 as PlayerXydzjs).getUiPlayer(),0.3 * param3.getExpOfDieThisEnemey() * 5);
            }
            else if((param2 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp > 0)
            {
               MyFunction.getInstance().addPlayerExperience((param2 as PlayerXydzjs).getUiPlayer(),0.3 * param3.getExpOfDieThisEnemey() * (param2 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp);
            }
            else
            {
               MyFunction.getInstance().addPlayerExperience((param2 as PlayerXydzjs).getUiPlayer(),0.3 * param3.getExpOfDieThisEnemey());
            }
            if((param2 as PlayerXydzjs).getUiPlayer().playerVO.pet)
            {
               MyFunction.getInstance().addPetExperience((param2 as PlayerXydzjs).getUiPlayer(),0.3 * param3.getExpOfDieThisEnemey());
            }
         }
         else
         {
            if(Part1.getInstance().getIsFive())
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),param3.getExpOfDieThisEnemey() * 5);
            }
            else if((param1 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp > 0)
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),param3.getExpOfDieThisEnemey() * (param1 as PlayerXydzjs).getUiPlayer().playerVO.addDoubleExp);
            }
            else
            {
               MyFunction.getInstance().addPlayerExperience((param1 as PlayerXydzjs).getUiPlayer(),param3.getExpOfDieThisEnemey());
            }
            if((param1 as PlayerXydzjs).getUiPlayer().playerVO.pet)
            {
               MyFunction.getInstance().addPetExperience((param1 as PlayerXydzjs).getUiPlayer(),param3.getExpOfDieThisEnemey());
            }
         }
      }
   }
}

