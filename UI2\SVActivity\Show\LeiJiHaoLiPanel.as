package UI2.SVActivity.Show
{
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.GiftBagData_ChongZhiFanLi;
   import UI2.SVActivity.Data.JiLeiHaoLiData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SVActivity.Data.TotalRechargeData;
   import YJFY.API_4399.PayAPI.DateInfor;
   import YJFY.API_4399.PayAPI.PayAPI;
   import YJFY.API_4399.PayAPI.PayAPIListener;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LeiJiHaoLiPanel
   {
      
      private var m_giftBagBtns:Vector.<SwitchBtnLogicShell>;
      
      private var m_giftBagBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_totalRechargeText:TextField;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_getRewardBtn:ButtonLogicShell2;
      
      private var m_reChargeBtn:ButtonLogicShell2;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_totalRechargeData:TotalRechargeData;
      
      private var m_payAPIListener:PayAPIListener;
      
      private var m_dateInforOfGetTotalRecharge:DateInfor;
      
      private var m_show:MovieClip;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_jiLeiHaoLiData:JiLeiHaoLiData;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      private var m_payAPI:PayAPI;
      
      private var m_payAPIListenerForReset:PayAPIListener;
      
      private var m_dateInforOfGetTotalRechargeForReset:DateInfor;
      
      private var m_payAPIForReset:PayAPI;
      
      public function LeiJiHaoLiPanel()
      {
         super();
         m_eqCells = new Vector.<Sprite>();
         m_getRewardBtn = new ButtonLogicShell2();
         m_reChargeBtn = new ButtonLogicShell2();
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_totalRechargeData = new TotalRechargeData();
         m_payAPIListener = new PayAPIListener();
         m_payAPIListener.getRechargedMoneyErrorFun = getRechargedMoneyError;
         m_payAPIListener.getRechargedMoneySuccessFun = getRechargedMoneySuccess;
         m_payAPI = GamingUI.getInstance().getAPI4399().payAPI;
         m_payAPIListenerForReset = new PayAPIListener();
         m_payAPIListenerForReset.getRechargedMoneyErrorFun = getRechargedMoneyErrorForReset;
         m_payAPIListenerForReset.getRechargedMoneySuccessFun = getRechargedMoneySuccessForReset;
         m_payAPIForReset = GamingUI.getInstance().getAPI4399().payAPI;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         if(m_payAPIForReset)
         {
            m_payAPIForReset.removePayAPIListener(m_payAPIListenerForReset);
         }
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(m_giftBagBtns);
         m_giftBagBtns = null;
         ClearUtil.clearObject(m_giftBagBtnGroup);
         m_giftBagBtnGroup = null;
         m_totalRechargeText = null;
         ClearUtil.clearObject(m_getRewardBtn);
         m_getRewardBtn = null;
         ClearUtil.clearObject(m_reChargeBtn);
         m_reChargeBtn = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_totalRechargeData);
         m_totalRechargeData = null;
         ClearUtil.clearObject(m_payAPIListener);
         m_payAPIListener = null;
         ClearUtil.clearObject(m_dateInforOfGetTotalRecharge);
         m_dateInforOfGetTotalRecharge = null;
         ClearUtil.clearObject(m_payAPIListenerForReset);
         m_payAPIListenerForReset = null;
         ClearUtil.clearObject(m_dateInforOfGetTotalRechargeForReset);
         m_dateInforOfGetTotalRechargeForReset = null;
         m_show = null;
         m_svActivitySaveData = null;
         m_jiLeiHaoLiData = null;
         m_svActivityPanel = null;
         m_payAPI = null;
         m_payAPIForReset = null;
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:JiLeiHaoLiData, param4:SVActivityPanel) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_svActivitySaveData = param2;
         m_jiLeiHaoLiData = param3;
         m_svActivityPanel = param4;
         initShow();
         getTotalRechargeForSomeTime();
      }
      
      private function getTotalRechargeForSomeTime() : void
      {
         m_payAPI.addPayAPIListener(m_payAPIListener);
         if(m_dateInforOfGetTotalRecharge == null)
         {
            m_dateInforOfGetTotalRecharge = new DateInfor(m_jiLeiHaoLiData.getStartTime(),m_jiLeiHaoLiData.getEndTime());
         }
         m_payAPI.getTotalRechargeFun(m_dateInforOfGetTotalRecharge);
      }
      
      private function getTotalRechargeForSomeTimeForReset() : void
      {
         m_payAPIForReset.addPayAPIListener(m_payAPIListenerForReset);
         if(m_dateInforOfGetTotalRechargeForReset == null)
         {
            m_dateInforOfGetTotalRechargeForReset = new DateInfor("2018-02-01 00:00:00","2018-03-04 00:00:00");
         }
         m_payAPIForReset.getTotalRechargeFun(m_dateInforOfGetTotalRechargeForReset);
      }
      
      private function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc2_:SwitchBtnLogicShell = null;
         m_totalRechargeText = m_show["totalRechargeTxt"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_totalRechargeText);
         m_getRewardBtn.setShow(m_show["getRewardBtn"]);
         m_reChargeBtn.setShow(m_show["reChargeBtn"]);
         unAbleGetReward();
         _loc3_ = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(m_show.getChildAt(_loc5_).name.substr(0,"giftBagBtn_".length) == "giftBagBtn_")
            {
               _loc1_++;
            }
            else if(m_show.getChildAt(_loc5_).name.substr(0,"eqCell_".length) == "eqCell_")
            {
               _loc4_++;
            }
            _loc5_++;
         }
         ClearUtil.clearObject(m_giftBagBtns);
         m_giftBagBtns = new Vector.<SwitchBtnLogicShell>();
         ClearUtil.clearObject(m_giftBagBtnGroup);
         m_giftBagBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc2_ = new MySwitchBtnLogicShell();
            _loc2_.setShow(m_show["giftBagBtn_" + (_loc5_ + 1)]);
            m_giftBagBtns.push(_loc2_);
            m_giftBagBtnGroup.addSwitchBtn(_loc2_);
            _loc5_++;
         }
         m_eqCells.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            m_eqCells.push(m_show["eqCell_" + (_loc5_ + 1)]);
            _loc5_++;
         }
         m_giftBagBtns[0].turnActiveAndDispatchEvent();
         (m_show["czflBtn"] as MovieClip).gotoAndStop("2");
         m_show["czflBtn"].addEventListener("click",clickczflBtn);
      }
      
      private function clickczflBtn(param1:MouseEvent) : void
      {
         (m_show["czflBtn"] as MovieClip).gotoAndStop("1");
         m_show["czflBtn"].removeEventListener("click",clickczflBtn);
         m_svActivityPanel.initMultiShow_chongZhiFanLi();
      }
      
      private function refreshTotalRechargeShow() : void
      {
         unAbleGetReward();
         m_totalRechargeText.text = m_totalRechargeData.getTotalRecharge().toString();
         var _loc1_:int = int(m_giftBagBtns.indexOf(m_giftBagBtnGroup.currentActivateBtn()));
         if(_loc1_ == -1)
         {
            throw new Error("出错了");
         }
         var _loc2_:GiftBagData_ChongZhiFanLi = m_jiLeiHaoLiData.getGiftBagByIndex(_loc1_);
         if(m_totalRechargeData.getTotalRecharge() >= _loc2_.getNeedTotalTicket())
         {
            if(m_svActivitySaveData.isGotThisGiftBagOfJiLeiHaoLi(_loc2_.getId()) == false)
            {
               ableGetReward();
            }
         }
      }
      
      private function initOneGiftBagShow(param1:int) : void
      {
         var _loc6_:int = 0;
         var _loc4_:Equipment = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         var _loc2_:GiftBagData_ChongZhiFanLi = m_jiLeiHaoLiData.getGiftBagByIndex(param1);
         var _loc3_:int = int(_loc2_.getEquipmentVONum());
         var _loc5_:uint = m_eqCells.length;
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc4_ = MyFunction2.sheatheEquipmentShell(_loc2_.getEquipmentVOByIndex(_loc6_));
            _loc4_.addEventListener("rollOver",onOver2,false,0,true);
            _loc4_.addEventListener("rollOut",onOut2,false,0,true);
            if(_loc6_ < _loc5_)
            {
               m_eqCells[_loc6_].addChild(_loc4_);
            }
            m_equipments.push(_loc4_);
            _loc6_++;
         }
         refreshTotalRechargeShow();
      }
      
      private function ableGetReward() : void
      {
         MyFunction.getInstance().changeSaturation(m_getRewardBtn.getShow(),0);
         (m_getRewardBtn.getShow() as MovieClip).mouseChildren = true;
         (m_getRewardBtn.getShow() as MovieClip).mouseEnabled = true;
      }
      
      private function unAbleGetReward() : void
      {
         MyFunction.getInstance().changeSaturation(m_getRewardBtn.getShow(),-100);
         (m_getRewardBtn.getShow() as MovieClip).mouseChildren = false;
         (m_getRewardBtn.getShow() as MovieClip).mouseEnabled = false;
      }
      
      private function getReward() : void
      {
         var _loc1_:int = int(m_giftBagBtns.indexOf(m_giftBagBtnGroup.currentActivateBtn()));
         if(_loc1_ == -1)
         {
            throw new Error("出错了");
         }
         var _loc2_:GiftBagData_ChongZhiFanLi = m_jiLeiHaoLiData.getGiftBagByIndex(_loc1_);
         if(m_getEquipmentVOsLogic.getEquipmentVOs(_loc2_,GamingUI.getInstance().player1))
         {
            m_svActivitySaveData.addGotGiftBagId(_loc2_.getId());
            refreshTotalRechargeShow();
            m_svActivityPanel.showWarningBox("获取礼包成功",0);
         }
         else
         {
            m_svActivityPanel.showWarningBox("背包已满, 不能获取礼包",0);
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc6_:int = 0;
         var _loc5_:String = GamingUI.getInstance().getNewestTimeStrFromSever();
         var _loc4_:String = m_jiLeiHaoLiData.getStartTime();
         var _loc2_:String = m_jiLeiHaoLiData.getEndTime();
         if(!((!Boolean(_loc4_) || new TimeUtil().timeInterval(_loc4_,_loc5_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc5_,_loc2_) > 0)))
         {
            m_svActivityPanel.showWarningBox("不在活动时间内!",0);
            return;
         }
         switch(param1.button)
         {
            case m_reChargeBtn:
               AnalogServiceHoldFunction.getInstance().payMoney_As3();
               break;
            case m_getRewardBtn:
               getReward();
         }
         var _loc3_:int = int(m_giftBagBtns.length);
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            if(param1.button == m_giftBagBtns[_loc6_])
            {
               initOneGiftBagShow(_loc6_);
               break;
            }
            _loc6_++;
         }
      }
      
      private function getRechargedMoneySuccess(param1:int) : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_totalRechargeData.setTotalRecharge(param1);
         refreshTotalRechargeShow();
         if(!m_svActivitySaveData.isResetGiftBagIds)
         {
            getTotalRechargeForSomeTimeForReset();
         }
      }
      
      private function getRechargedMoneyError() : void
      {
         if(m_payAPI)
         {
            m_payAPI.removePayAPIListener(m_payAPIListener);
         }
         m_svActivityPanel.showWarningBox("获取累积充值失败",0);
         m_totalRechargeData.setTotalRecharge(0);
         refreshTotalRechargeShow();
      }
      
      private function getRechargedMoneySuccessForReset(param1:int) : void
      {
         if(m_payAPIForReset)
         {
            m_payAPIForReset.removePayAPIListener(m_payAPIListenerForReset);
         }
         if(m_svActivitySaveData.isResetGiftBagIds)
         {
            return;
         }
         var _loc3_:Vector.<String> = m_svActivitySaveData.getGetGiftBagIds();
         var _loc2_:int = 0;
         if(param1 > 10)
         {
            _loc2_ = int(_loc3_.indexOf("g1"));
            if(_loc2_ != -1)
            {
               _loc3_.splice(_loc2_,1);
            }
         }
         if(param1 >= 200)
         {
            _loc2_ = int(_loc3_.indexOf("g2"));
            if(_loc2_ != -1)
            {
               _loc3_.splice(_loc2_,1);
            }
         }
         if(param1 >= 1000)
         {
            _loc2_ = int(_loc3_.indexOf("g3"));
            if(_loc2_ != -1)
            {
               _loc3_.splice(_loc2_,1);
            }
         }
         if(param1 >= 3000)
         {
            _loc2_ = int(_loc3_.indexOf("g4"));
            if(_loc2_ != -1)
            {
               _loc3_.splice(_loc2_,1);
            }
         }
         if(param1 >= 8000)
         {
            _loc2_ = int(_loc3_.indexOf("g5"));
            if(_loc2_ != -1)
            {
               _loc3_.splice(_loc2_,1);
            }
         }
         m_svActivitySaveData.isResetGiftBagIds = true;
         m_svActivitySaveData.setGetGiftBagIds(_loc3_);
      }
      
      private function getRechargedMoneyErrorForReset() : void
      {
         if(m_payAPIForReset)
         {
            m_payAPI.removePayAPIListener(m_payAPIListenerForReset);
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

