package YJFY.LevelMode5
{
   import UI.Event.NewLevelEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import YJFY.GameEvent;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.MySprite;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class LevelEndDancerFail extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_guanYinProtectBtn:ButtonLogicShell;
      
      private var m_returnCityBtn:ButtonLogicShell;
      
      private var m_restartBtn:ButtonLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_level:LevelEndDancerWorld;
      
      private var m_zhenhun:EndDancerZhenhunView;
      
      public function LevelEndDancerFail(param1:LevelEndDancerWorld)
      {
         super();
         m_level = param1;
         m_guanYinProtectBtn = new ButtonLogicShell();
         m_returnCityBtn = new ButtonLogicShell();
         m_restartBtn = new ButtonLogicShell();
         m_myLoader = new YJFYLoader();
         mouseChildren = true;
         mouseEnabled = true;
         m_myLoader.setVersionControl(GamingUI.getInstance().getVersionControl());
         addEventListener("clickButton",clickButton,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_guanYinProtectBtn);
         m_guanYinProtectBtn = null;
         ClearUtil.clearObject(m_returnCityBtn);
         m_returnCityBtn = null;
         ClearUtil.clearObject(m_restartBtn);
         m_restartBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_zhenhun);
         m_zhenhun = null;
         m_level = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_myLoader.getClass("NewGameFolder/LevelMode3/newfail.swf","newfail",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initshow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      public function initshow() : void
      {
         m_guanYinProtectBtn.setShow(m_show["guanyinbtn"]);
         m_returnCityBtn.setShow(m_show["returncitybtn"]);
         m_restartBtn.setShow(m_show["restartbtn"]);
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_returnCityBtn:
               Part1.getInstance().continueGame();
               Part1.getInstance().returnCity();
               break;
            case m_guanYinProtectBtn:
               m_zhenhun = new EndDancerZhenhunView(this);
               addChild(m_zhenhun);
               break;
            case m_restartBtn:
               Part1.getInstance().continueGame();
               GameEvent.eventDispacher.dispatchEvent(new NewLevelEvent("restart3"));
         }
      }
      
      public function playZhenhun() : void
      {
         m_level.zhenhunProtect();
         closeUI();
      }
      
      public function closeZhenhun() : void
      {
         if(m_zhenhun)
         {
            removeChild(m_zhenhun);
            ClearUtil.clearObject(m_zhenhun);
            m_zhenhun = null;
         }
      }
      
      private function closeUI() : void
      {
         clear();
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

