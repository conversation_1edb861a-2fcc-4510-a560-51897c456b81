package UI.MiragePanel
{
   import UI.Animation.AnimationObject;
   import UI.Animation.M2B;
   import UI.Effects.Effects_Baoji_Mirage;
   import UI.Effects.Effects_Fail_Mirage;
   import UI.Effects.Effects_Mirage;
   import UI.Effects.Effects_Success_Mirage;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.Pets.Pet;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.Skill;
   import UI.Skills.SkillVO;
   import UI.SmallPackage.SmallPackage;
   import UI.TextTrace.traceText;
   import UI.XMLSingle;
   import com.greensock.TweenLite;
   import com.greensock.TweenMax;
   import com.greensock.easing.Back;
   import com.greensock.easing.Circ;
   import com.greensock.easing.Linear;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class MirageFunction
   {
      
      public static var _instance:MirageFunction = null;
      
      private var _dataXML:XML = null;
      
      public function MirageFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了么？！");
      }
      
      public static function getInstance() : MirageFunction
      {
         if(!_instance)
         {
            _instance = new MirageFunction();
         }
         return _instance;
      }
      
      public function getCurrentHaveMirageNum() : int
      {
         return int(XMLSingle.getInstance().dataXML.MirageNum.@num) + GamingUI.getInstance().player1.vipVO.addMirageNum - MirageData.getInstance().currentMirageNum;
      }
      
      public function judgeSkillEnalbeMirage(param1:SkillVO, param2:Pet, param3:XML) : Boolean
      {
         var _loc5_:* = null;
         var _loc4_:PetPassiveSkillVO = null;
         initData(param1,param2,param3);
         if(param1 is PetPassiveSkillVO)
         {
            _loc4_ = param1 as PetPassiveSkillVO;
         }
         return Boolean(_dataXML);
      }
      
      public function initData(param1:Object, param2:Pet, param3:XML) : void
      {
         var _loc4_:SkillVO = null;
         var _loc5_:PetPassiveSkillVO = null;
         _dataXML = null;
         if(param1 is EssenceIcon)
         {
            _dataXML = XMLSingle.getInstance().dataXML.MirageEssence[0];
         }
         else if(param1 is SkillVO || param1 is Skill)
         {
            if(param1 is SkillVO)
            {
               _loc4_ = param1 as SkillVO;
            }
            else
            {
               _loc4_ = (param1 as Skill).skillVO;
            }
            _dataXML = param3.PromotePetSkills.(@promoteXMLId == param2.petEquipmentVO.promoteXMLId)[0].promotePetSkill.(@className == _loc4_.className)[0];
            if(_loc4_ is PetPassiveSkillVO)
            {
               _loc5_ = _loc4_ as PetPassiveSkillVO;
               _dataXML = _dataXML.promote.(@currentMinValue <= _loc5_.value && @currentMaxValue > _loc5_.value)[0];
            }
         }
      }
      
      public function getRate(param1:DisplayObject, param2:int, ... rest) : Number
      {
         return (Number(_dataXML.@baseSuccessRate) - 100) * Math.pow(Number(_dataXML.@baseNum),param2) + 100;
      }
      
      public function getLuckStoneMaxNum(param1:DisplayObject) : int
      {
         var maxNum:int;
         var target:DisplayObject = param1;
         var getMaxNum:* = function():int
         {
            var _loc3_:* = undefined;
            var _loc2_:int = 0;
            switch(SmallPackage.getInstance().currentSmallPackage)
            {
               case SmallPackage.getInstance().smallPackage_1:
                  _loc3_ = GamingUI.getInstance().player1.playerVO.packageEquipmentVOs;
                  break;
               case SmallPackage.getInstance().smallPackage_2:
                  _loc3_ = GamingUI.getInstance().player2.playerVO.packageEquipmentVOs;
                  break;
               default:
                  throw new Error();
            }
            for each(var _loc1_ in _loc3_)
            {
               if(_loc1_)
               {
                  if(_loc1_.className == "LuckStone")
                  {
                     _loc2_ += (_loc1_ as StackEquipmentVO).num;
                  }
               }
            }
            return _loc2_;
         };
         if(target is EssenceIcon)
         {
            maxNum = Math.min(100,getMaxNum());
         }
         else if(target is Skill)
         {
            maxNum = Math.min(100,getMaxNum());
         }
         return maxNum;
      }
      
      public function getPromoteMoney(param1:DisplayObject) : int
      {
         if(param1 is EssenceIcon)
         {
            return int(_dataXML.@money);
         }
         if(param1 is Skill)
         {
            return int(_dataXML.@money);
         }
         throw new Error();
      }
      
      public function getPromoteValueRange(param1:DisplayObject, ... rest) : Array
      {
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         if(param1 is EssenceIcon)
         {
            _loc5_ = (rest[1] as Pet).petEquipmentVO.essentialPercent * (rest[1] as Pet).petEquipmentVO.essentialVolume;
            _loc4_ = _loc5_ * int(_dataXML.@transFormMinRate) / 100;
            _loc6_ = _loc5_ * int(_dataXML.@transFormMaxRate) / 100;
         }
         else if(param1 is Skill)
         {
            _loc4_ = int(_dataXML.@promoteMinValue);
            _loc6_ = int(_dataXML.@promoteMaxValue);
         }
         return [_loc4_,_loc6_];
      }
      
      public function getPromoteValue(param1:DisplayObject, ... rest) : int
      {
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 is EssenceIcon)
         {
            _loc6_ = (rest[1] as Pet).petEquipmentVO.essentialPercent * (rest[1] as Pet).petEquipmentVO.essentialVolume;
            _loc4_ = _loc6_ * int(_dataXML.@transFormMinRate) / 100;
            _loc7_ = _loc6_ * int(_dataXML.@transFormMaxRate) / 100;
         }
         else if(param1 is Skill)
         {
            _loc4_ = int(_dataXML.@promoteMinValue);
            _loc7_ = int(_dataXML.@promoteMaxValue);
         }
         var _loc3_:Number = Math.random();
         return int(_loc4_ + Math.round(_loc3_ * (_loc7_ - _loc4_)));
      }
      
      public function startMirageEffectAnimation(param1:DisplayObjectContainer, param2:DisplayObject, param3:DisplayObject, param4:Boolean, param5:Boolean, param6:Function) : void
      {
         var mirageMC:MovieClip;
         var effectsAnimotion:AnimationObject;
         var miragePanel:DisplayObjectContainer = param1;
         var mainPetShow:DisplayObject = param2;
         var assisantPetShow:DisplayObject = param3;
         var isSuccess:Boolean = param4;
         var isBaoji:Boolean = param5;
         var funAfterEffect:Function = param6;
         var successEffectsFun:* = function(param1:Event):void
         {
            var e:Event = param1;
            var successAnimaitonEnd:* = function(param1:Event):void
            {
               traceText("成功动画播放结束");
            };
            var successMirageEffects:Effects_Success_Mirage = new Effects_Success_Mirage();
            var effectsAnimotion_Success:AnimationObject = new AnimationObject();
            effectsAnimotion_Success.x = 32.35;
            effectsAnimotion_Success.y = 56.35;
            miragePanel.addChildAt(effectsAnimotion_Success,1);
            effectsAnimotion_Success.imgList = M2B.transformM2B(successMirageEffects,false);
            effectsAnimotion_Success.repeatCount = 1;
            effectsAnimotion_Success.delay = 60;
            effectsAnimotion_Success.play();
            effectsAnimotion_Success.addEventListener("play over event",successAnimaitonEnd,false,0,false);
            mainPetShow.filters = [];
            TweenMax.to(mainPetShow,0.5,{
               "glowFilter":{
                  "color":13172735,
                  "alpha":1,
                  "blurX":2,
                  "blurY":2,
                  "strength":100,
                  "quality":3
               },
               "onComplete":removeGlow
            });
            traceText("动画播放结束");
         };
         var failEffectFun:* = function(param1:Event):void
         {
            var e:Event = param1;
            var failFun:* = function(param1:Event):void
            {
               traceText("失败动画播放结束");
               funAfterEffect();
            };
            var failMirageEffects:Effects_Fail_Mirage = new Effects_Fail_Mirage();
            var effectsAnimotion_Fail:AnimationObject = new AnimationObject();
            effectsAnimotion_Fail.x = 38.45;
            effectsAnimotion_Fail.y = 100.55;
            miragePanel.addChild(effectsAnimotion_Fail);
            effectsAnimotion_Fail.imgList = M2B.transformM2B(failMirageEffects,false,50,50);
            effectsAnimotion_Fail.repeatCount = 1;
            effectsAnimotion_Fail.delay = 60;
            effectsAnimotion_Fail.play();
            effectsAnimotion_Fail.addEventListener("play over event",failFun,false,0,true);
            traceText("动画播放结束");
         };
         var assisantPetShowRemove:* = function():void
         {
            assisantPetShow.alpha = 1;
            assisantPetShow.parent.removeChild(assisantPetShow);
         };
         var makeMianPetShowNormal:* = function():void
         {
            mainPetShow.filters = [];
            mainPetShow.alpha = 1;
            TweenMax.from(mainPetShow,1,{
               "glowFilter":{
                  "color":16777215,
                  "alpha":1,
                  "blurX":255,
                  "blurY":255,
                  "strength":255,
                  "quality":3,
                  "inner":true
               },
               "alpha":0,
               "ease":Linear.easeNone,
               "delay":0
            });
         };
         var removeGlow:* = function():void
         {
            mainPetShow.filters = [];
            TweenMax.from(mainPetShow,0.5,{
               "glowFilter":{
                  "color":13172735,
                  "alpha":1,
                  "blurX":2,
                  "blurY":2,
                  "strength":100,
                  "quality":3
               },
               "ease":Back.easeIn,
               "onComplete":successFun
            });
         };
         var successFun:* = function():void
         {
            funAfterEffect();
         };
         if(isSuccess && isBaoji)
         {
            mirageMC = new Effects_Baoji_Mirage();
         }
         else
         {
            mirageMC = new Effects_Mirage();
         }
         effectsAnimotion = new AnimationObject();
         effectsAnimotion.x = 1.6;
         effectsAnimotion.y = 0.8;
         miragePanel.addChild(effectsAnimotion);
         effectsAnimotion.imgList = M2B.transformM2B(mirageMC,false,100,100);
         effectsAnimotion.repeatCount = 1;
         effectsAnimotion.delay = 60;
         effectsAnimotion.play();
         TweenLite.to(assisantPetShow,1,{
            "alpha":0,
            "ease":Back.easeIn,
            "onComplete":assisantPetShowRemove
         });
         TweenMax.to(mainPetShow,0.3,{
            "glowFilter":{
               "color":16777215,
               "alpha":1,
               "blurX":255,
               "blurY":255,
               "strength":200,
               "quality":3,
               "inner":true
            },
            "delay":1.5
         });
         TweenLite.to(mainPetShow,1,{
            "alpha":0,
            "ease":Circ.easeIn,
            "delay":1,
            "onComplete":makeMianPetShowNormal
         });
         if(isSuccess)
         {
            effectsAnimotion.addEventListener("play over event",successEffectsFun,false,0,true);
         }
         else
         {
            effectsAnimotion.addEventListener("play over event",failEffectFun,false,0,true);
         }
      }
   }
}

