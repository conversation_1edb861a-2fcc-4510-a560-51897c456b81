package YJFY.Skill.ActiveSkill
{
   import YJFY.Entity.IEntity;
   
   public class PushSkillListener implements IPushSkillListener
   {
      
      public var pushEntityFun:Function;
      
      public function PushSkillListener()
      {
         super();
      }
      
      public function clear() : void
      {
         pushEntityFun = null;
      }
      
      public function pushEntity(param1:IPushSkill, param2:IEntity) : void
      {
         if(<PERSON><PERSON><PERSON>(pushEntityFun))
         {
            pushEntityFun(param1,param2);
         }
      }
   }
}

