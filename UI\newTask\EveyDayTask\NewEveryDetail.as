package UI.newTask.EveyDayTask
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Task.TaskGoalVO;
   import UI.Task.TaskReward.TaskRewardVO_Equipment;
   import UI.Task.TaskReward.TaskRewardVO_Experience;
   import UI.Task.TaskReward.TaskRewardVO_LSHSHI;
   import UI.Task.TaskReward.TaskRewardVO_Money;
   import UI.Task.TaskReward.TaskRewardVO_ZHHJZH;
   import UI.Task.TaskVO.MTaskVO;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI.newTask.NewTaskPanel;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class NewEveryDetail
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_neweverydaypanel:NewEveryDayPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_txtName:TextField;
      
      private var m_txtDoneNum:TextField;
      
      public var dataLayer:NewDataLayer_ExTaskPanel;
      
      public var dataMask:Sprite;
      
      public var m_slider:Sprite;
      
      public var m_downbtn:Sprite;
      
      public var m_upbtn:Sprite;
      
      public var scroll_bg:Sprite;
      
      private var m_minY:Number = 29;
      
      private var m_maxY:Number = 295;
      
      private var m_yMove:Number = 5;
      
      private var m_bDown:Boolean = false;
      
      private var m_rotio:Number;
      
      private var m_itemHeigh:int = 60;
      
      public function NewEveryDetail()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(dataLayer);
         dataLayer = null;
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewEveryDayPanel) : void
      {
         m_newtaskpanel = param1;
         m_neweverydaypanel = param3;
         m_show = param2;
         m_mc = param2["everydaypanel"];
         initParams();
      }
      
      private function initParams() : void
      {
         dataLayer = new NewDataLayer_ExTaskPanel();
         dataLayer.init(m_newtaskpanel,m_show,m_neweverydaypanel,this);
         m_txtName = m_show["txttaskname"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtName,true);
         m_txtDoneNum = m_show["txttaskdonenum"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtDoneNum,true);
         dataMask = m_mc["dataMask"];
         m_slider = m_mc["slider"];
         m_downbtn = m_mc["downControl"];
         m_upbtn = m_mc["upControl"];
         scroll_bg = m_mc["scroll_bg"];
      }
      
      private function showTaskTitle(param1:MTaskVO) : void
      {
         var _loc5_:int = 0;
         m_txtName.text = param1.name;
         var _loc4_:* = 0;
         _loc5_ = 0;
         while(_loc5_ < param1.currentTaskGoalVO_nums.length)
         {
            _loc4_ += param1.currentTaskGoalVO_nums[_loc5_];
            _loc5_++;
         }
         var _loc3_:int = 0;
         _loc5_ = 0;
         while(_loc5_ < param1.taskGoalVO_nums.length)
         {
            _loc3_ += param1.taskGoalVO_nums[_loc5_];
            _loc5_++;
         }
         if(param1.isGotReward)
         {
            _loc4_ = _loc3_;
         }
         var _loc2_:TextFormat = m_txtDoneNum.defaultTextFormat;
         if(_loc4_ >= _loc3_)
         {
            _loc2_.color = 52224;
         }
         else
         {
            _loc2_.color = 16711680;
         }
         m_txtDoneNum.defaultTextFormat = _loc2_;
         m_txtDoneNum.text = "（" + _loc4_ + "/" + _loc3_ + "）";
         m_txtDoneNum.x = m_txtName.x + m_txtName.textWidth + 5;
      }
      
      public function refreshScript(param1:MTaskVO) : void
      {
         var _loc4_:* = undefined;
         var _loc5_:TextField = null;
         var _loc3_:FangZhengKaTongJianTi = null;
         var _loc12_:* = undefined;
         var _loc9_:int = 0;
         var _loc15_:int = 0;
         var _loc7_:TaskRewardVO_Equipment = null;
         var _loc13_:TaskRewardVO_Experience = null;
         var _loc11_:TaskRewardVO_Money = null;
         var _loc10_:TaskRewardVO_ZHHJZH = null;
         var _loc2_:TaskRewardVO_LSHSHI = null;
         var _loc16_:String = "";
         var _loc17_:String = "";
         var _loc6_:Vector.<TextField> = new Vector.<TextField>();
         var _loc14_:Vector.<MTaskVO> = NewEveryDayData.getInstance().everyDayTaskVOs;
         var _loc8_:* = param1;
         showTaskTitle(_loc8_);
         if(_loc8_)
         {
            _loc3_ = new FangZhengKaTongJianTi();
            _loc17_ = _loc8_.description;
            _loc12_ = XMLSingle.getTaskGoals(_loc8_.taskGoalVO_ids,XMLSingle.getInstance().taskXML);
            _loc9_ = 0;
            _loc15_ = int(_loc12_.length);
            _loc9_ = 0;
            while(_loc9_ < _loc15_)
            {
               _loc16_ += "" + (_loc9_ + 1) + "." + _loc12_[_loc9_].name + "×" + _loc8_.taskGoalVO_nums[_loc9_] + " \n";
               _loc9_++;
            }
            _loc15_ = int(_loc8_.taskRewardVOs.length);
            _loc9_ = 0;
            while(_loc9_ < _loc15_)
            {
               switch(_loc8_.taskRewardVOs[_loc9_].type)
               {
                  case "equipmentReward":
                     _loc7_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Equipment;
                     _loc4_ = XMLSingle.getEquipmentVOsIDs(_loc7_.equipments_IDs,XMLSingle.getInstance().equipmentXML,_loc7_.equipments_Nums,_loc7_.isBinding);
                     break;
                  case "experienceReward":
                  case "moneyReward":
                  case "zhHJZHRward":
                  case "lSHSHRward":
                     _loc5_ = new TextField();
                     _loc5_.selectable = false;
                     _loc5_.defaultTextFormat = new TextFormat(_loc3_.fontName,20,16777215);
                     _loc5_.embedFonts = true;
                     _loc5_.wordWrap = true;
                     _loc5_.width = 475;
                     break;
                  default:
                     throw new Error("类型错误");
               }
               switch(_loc8_.taskRewardVOs[_loc9_].type)
               {
                  case "experienceReward":
                     _loc13_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Experience;
                     _loc5_.text = _loc13_.description + "×" + _loc13_.value;
                     break;
                  case "moneyReward":
                     _loc11_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_Money;
                     _loc5_.text = _loc11_.description + "×" + _loc11_.value;
                     break;
                  case "zhHJZHRward":
                     _loc10_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_ZHHJZH;
                     _loc5_.text = _loc10_.description + "×" + _loc10_.value;
                     break;
                  case "lSHSHRward":
                     _loc2_ = _loc8_.taskRewardVOs[_loc9_] as TaskRewardVO_LSHSHI;
                     _loc5_.text = _loc2_.description + "×" + _loc2_.value;
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               _loc5_.width = Math.min(_loc5_.textWidth + 5,475);
               _loc5_.height = _loc5_.textHeight + 5;
               _loc6_.push(_loc5_);
               _loc9_++;
            }
         }
         dataLayer.refreshDataLayer(_loc17_,_loc16_,_loc4_,_loc6_);
      }
      
      public function show() : void
      {
         if(dataLayer)
         {
            dataLayer.show();
         }
         registerEvent();
         m_mc.visible = true;
         m_mc.x = 336.15;
      }
      
      public function hide() : void
      {
         if(dataLayer)
         {
            dataLayer.hide();
         }
         closeEvent();
         m_mc.visible = false;
         m_mc.x = 10000;
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChild(WarningBoxSingle.getInstance());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         param1.button;
      }
      
      private function registerEvent() : void
      {
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_mc.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_mc.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_mc.addEventListener("enterFrame",render);
         m_slider.addEventListener("mouseDown",calldown);
         m_show.addEventListener("mouseUp",calldown);
         m_upbtn.addEventListener("mouseUp",callup);
         m_downbtn.addEventListener("mouseUp",callnext);
      }
      
      private function callup(param1:MouseEvent) : void
      {
         if(m_slider.y - m_yMove <= m_maxY && m_slider.y - m_yMove >= m_minY)
         {
            m_slider.y -= m_yMove;
         }
         else if(m_slider.y - m_yMove < m_minY)
         {
            m_slider.y = m_minY;
         }
         else if(m_slider.y - m_yMove > m_maxY)
         {
            m_slider.y = m_maxY;
         }
      }
      
      private function callnext(param1:MouseEvent) : void
      {
         if(m_slider.y + m_yMove <= m_maxY && m_slider.y + m_yMove >= m_minY)
         {
            m_slider.y += m_yMove;
         }
         else if(m_slider.y + m_yMove < m_minY)
         {
            m_slider.y = m_minY;
         }
         else if(m_slider.y + m_yMove > m_maxY)
         {
            m_slider.y = m_maxY;
         }
      }
      
      private function calldown(param1:MouseEvent) : void
      {
         if(param1.type == "mouseDown")
         {
            m_bDown = true;
         }
         else
         {
            m_bDown = false;
         }
      }
      
      private function render(param1:Event) : void
      {
         moveMouse();
         moveInfo();
      }
      
      private function moveMouse() : void
      {
         if(m_bDown)
         {
            if(m_mc.mouseY - 16 <= m_maxY && m_mc.mouseY - 16 >= m_minY)
            {
               m_slider.y = m_mc.mouseY - 16;
            }
            else if(m_mc.mouseY - 16 < m_minY)
            {
               m_slider.y = m_minY;
            }
            else if(m_mc.mouseY - 16 > m_maxY)
            {
               m_slider.y = m_maxY;
            }
         }
      }
      
      private function moveInfo() : void
      {
         m_rotio = (m_slider.y - m_minY) / (m_maxY - m_minY);
         if(dataLayer && dataLayer.m_mc)
         {
            dataLayer.m_mc.y = -m_rotio * (dataLayer.m_mc.numChildren * m_itemHeigh);
         }
      }
      
      private function closeEvent() : void
      {
         m_mc.removeEventListener("clickButton",clickButton,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,true);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,true);
         m_mc.removeEventListener("showMessageBox",showMessageBox,false);
         m_mc.removeEventListener("hideMessageBox",hideMessageBox,false);
         m_mc.removeEventListener("enterFrame",render);
         m_slider.removeEventListener("mouseDown",calldown);
         m_show.removeEventListener("mouseUp",calldown);
         m_upbtn.removeEventListener("mouseUp",callup);
         m_downbtn.removeEventListener("mouseUp",callnext);
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChild(MessageBoxEngine.getInstance());
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

