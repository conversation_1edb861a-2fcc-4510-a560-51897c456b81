package YJFY.LevelMode5
{
   import UI.Event.NewLevelEvent;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIAbleRunInHurt;
   import YJFY.EntityAI.AILogic.IHaveActiveSkillForAIRun;
   import YJFY.EntityAI.AILogic.IJugdeIsFoe;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEvent;
   import YJFY.LevelMode2.ILevelMode2Enemy;
   import YJFY.Part1;
   import YJFY.Skill.ISkill;
   import YJFY.Skill.Skill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.AIAttackVO;
   import YJFY.XydzjsLogic.JudgeIsFoe;
   import flash.display.MovieClip;
   
   public class EndDancerBoss extends EnemyXydzjs implements ILevelMode2Enemy, IHaveActiveSkillForAIAbleRunInHurt, IHaveActiveSkillForAIRun, IJugdeIsFoe
   {
      
      public static var m_bIsLight:Boolean = false;
      
      public static var m_bIsCallAnemy:Boolean = false;
      
      private static var _instance:EndDancerBoss;
      
      private var m_lastHp:uint;
      
      private var m_enemyAI:EndDancerBossAI;
      
      public var m_enemyAttackVO:AIAttackVO;
      
      private var m_startX:int;
      
      private var m_startY:int;
      
      public var hpline:int;
      
      public var skill1CD:int;
      
      public var skill2CD:int;
      
      public var skill3CD:int;
      
      private var m_shade:MovieClip;
      
      private var m_sFrameLabel:String;
      
      public function EndDancerBoss()
      {
         super();
         m_enemyAI = new EndDancerBossAI();
         m_enemyAttackVO = new AIAttackVO();
         m_bIsCallAnemy = false;
      }
      
      public static function getInstance() : EndDancerBoss
      {
         if(_instance == null)
         {
            _instance = new EndDancerBoss();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         if(m_enemyAttackVO)
         {
            ClearUtil.clearObject(m_enemyAttackVO);
         }
         m_enemyAttackVO = null;
         super.clear();
      }
      
      override public function render() : void
      {
         super.render();
         m_enemyAI.render();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_enemyAttackVO.initByXML(param1.enemyAttackData[0]);
         if(param1.hasOwnProperty("startX"))
         {
            m_startX = int(param1.startX);
         }
         if(param1.hasOwnProperty("startY"))
         {
            m_startY = int(param1.startY);
         }
         if(param1.hasOwnProperty("hpline"))
         {
            hpline = int(param1.hpline);
         }
         if(param1.hasOwnProperty("skill3CD"))
         {
            skill3CD = int(param1.skill3CD);
         }
         if(param1.hasOwnProperty("skill2CD"))
         {
            skill2CD = int(param1.skill2CD);
         }
         if(param1.hasOwnProperty("skill1CD"))
         {
            skill1CD = int(param1.skill1CD);
         }
      }
      
      override public function isFoe(param1:IEntity) : Boolean
      {
         super.isFoe(param1);
         return m_isFoe || JudgeIsFoe.enemyJudgeEntityIsFoe(param1);
      }
      
      override public function isAbleRunSkill(param1:Skill) : Boolean
      {
         return false;
      }
      
      override public function runSkill(param1:Skill) : void
      {
      }
      
      public function getActiveSkillNumOfRun() : uint
      {
         return 0;
      }
      
      public function getActiveSkillOfRunByIndex(param1:uint) : Skill
      {
         return null;
      }
      
      public function getHpShowFrameLabel() : String
      {
         return null;
      }
      
      public function getActiveSkillOfAbleRunInHurtByIndex(param1:uint) : Skill
      {
         return null;
      }
      
      public function getActiveSkillNumOfAbleRunInHurt() : uint
      {
         return 0;
      }
      
      override protected function addToWorld(param1:IEntity, param2:World) : void
      {
         super.addToWorld(param1,param2);
         m_enemyAI.init(m_animalEntity,param2,this,m_startX,m_startY);
         getChanchu = true;
      }
      
      override protected function beAttack(param1:IEntity, param2:AttackData, param3:ISkill, param4:IEntity) : void
      {
         super.beAttack(param1,param2,param3,param4);
         if(param1.getExtra() is AutoAttackPetXydzjs || param1.getExtra() is PetXydzjs)
         {
            GameEvent.eventDispacher.dispatchEvent(new NewLevelEvent("timedown3"));
         }
         if(m_sFrameLabel == "skill3Start" || m_sFrameLabel == "skill3Reach")
         {
            GameEvent.eventDispacher.dispatchEvent(new NewLevelEvent("timedown3"));
            m_sFrameLabel = "";
            m_enemyAI.setSkill_4(true);
         }
         m_bIsLight = false;
      }
      
      override public function resetPos() : void
      {
         super.resetPos();
         setIsInvincible(false);
         m_enemyAI.resetPos();
      }
      
      override protected function entityAnimationPlayReachFrameLabel(param1:String) : void
      {
         super.entityAnimationPlayReachFrameLabel(param1);
         if(param1 == "disappearEnd^stop^")
         {
            m_enemyAI.setSkill_2_2(true);
         }
         if(param1 == "skill4End^stop^")
         {
            NormalDancer.m_isStop = false;
            setIsInvincible(false);
         }
         if(param1 == "skill3Start" || param1 == "skill3Reach" || param1 == "skill3End^stop^")
         {
            m_sFrameLabel = param1;
            m_bIsLight = true;
         }
         else
         {
            m_bIsLight = false;
         }
         if(param1 == "skill3Start")
         {
            NormalDancer.m_isStop = true;
            m_bIsCallAnemy = true;
            Part1.getInstance().getLevelEndDancerWorld().Summon(m_animalEntity.getX(),m_animalEntity.getY(),m_animalEntity.getZ());
         }
         else if(param1 == "skill3End^stop^")
         {
            m_bIsCallAnemy = false;
            NormalDancer.m_isStop = false;
         }
         if(param1 == "skill1End^stop^")
         {
            setIsInvincible(false);
         }
         if(param1 == "appearEnd^stop^")
         {
            setIsInvincible(false);
         }
         if(param1 == "skill3End^stop^")
         {
            setIsInvincible(false);
         }
      }
      
      override public function isInvincible() : Boolean
      {
         if(super.isInvincible())
         {
            return true;
         }
         return false;
      }
      
      public function getUnableAttackMinInterval() : uint
      {
         return m_enemyAttackVO.getUnableAttackMinInterval();
      }
      
      public function getUnableAttackMaxInterval() : uint
      {
         return m_enemyAttackVO.getUnableAttackMaxInterval();
      }
      
      public function getRebuildAllFoeInterval() : uint
      {
         return m_enemyAttackVO.getRebuildAllFoeInterval();
      }
      
      public function getLastHp() : uint
      {
         return m_lastHp;
      }
      
      public function setLastHp(param1:uint) : void
      {
         m_lastHp = param1;
      }
      
      public function getEndDancerBossEntity() : IAnimalEntity
      {
         return m_animalEntity;
      }
   }
}

