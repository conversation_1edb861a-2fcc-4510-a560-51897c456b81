package UI.HonourHallPanel
{
   import UI.EnterFrameTime;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.EndlessMode.EndlessDoubleHonourPanel;
   import YJFY.EndlessMode.EndlessSingleHonourPanel;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.Sprite;
   import flash.system.System;
   
   public class HonourHallPanel extends MySprite
   {
      
      public static const PK_HONOUR_HALL:String = "pkHonourHall";
      
      public static const WORLD_BOSS_HONOUR_HALL:String = "worldBossHonourHall";
      
      public static const ENDLESSSINGLE_HONOUR_HALL:String = "EndlessSingleHonourHall";
      
      public static const ENDLESSDOUBLE_HONOUR_HALL:String = "EndlessDoubleHonourHall";
      
      private var m_show:Sprite;
      
      private var m_pkBtn:MySwitchBtnLogicShell;
      
      private var m_worldBossBtn:MySwitchBtnLogicShell;
      
      private var m_EndlessSingleBtn:MySwitchBtnLogicShell;
      
      private var m_EndlessDoubleBtn:MySwitchBtnLogicShell;
      
      private var m_switchGroup:SwitchBtnGroupLogicShell;
      
      private var m_pkPanel:PKHonourPanel;
      
      private var m_worldBossPanel:WorldBossHonourPanel;
      
      private var m_endlessSinglePanel:EndlessSingleHonourPanel;
      
      private var m_endlessDoublePanel:EndlessDoubleHonourPanel;
      
      private var m_panelMC:MovieClipPlayLogicShell;
      
      private var m_quiBtn:ButtonLogicShell2;
      
      private var m_honourHanllXML:XML;
      
      private var m_timeStr:String;
      
      private var m_gamingUI:GamingUI;
      
      private var m_wantGotoHallName:String;
      
      public function HonourHallPanel()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      public function setGamingUI(param1:GamingUI) : void
      {
         m_gamingUI = param1;
      }
      
      public function setGotoHallName(param1:String) : void
      {
         m_wantGotoHallName = param1;
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         super.clear();
         m_show = null;
         ClearUtil.clearObject(m_pkBtn);
         m_pkBtn = null;
         ClearUtil.clearObject(m_worldBossBtn);
         m_worldBossBtn = null;
         ClearUtil.clearObject(m_EndlessSingleBtn);
         m_EndlessSingleBtn = null;
         ClearUtil.clearObject(m_EndlessDoubleBtn);
         m_EndlessDoubleBtn = null;
         ClearUtil.clearObject(m_switchGroup);
         m_switchGroup = null;
         ClearUtil.clearObject(m_pkPanel);
         m_pkPanel = null;
         ClearUtil.clearObject(m_worldBossPanel);
         m_worldBossPanel = null;
         ClearUtil.clearObject(m_endlessSinglePanel);
         m_endlessSinglePanel = null;
         ClearUtil.clearObject(m_endlessDoublePanel);
         m_endlessDoublePanel = null;
         ClearUtil.clearObject(m_panelMC);
         m_panelMC = null;
         ClearUtil.clearObject(m_quiBtn);
         m_quiBtn = null;
         System.disposeXML(m_honourHanllXML);
         m_honourHanllXML = null;
         m_gamingUI = null;
      }
      
      public function gotoWorldBossHonourHall() : void
      {
         m_worldBossBtn.turnActiveAndDispatchEvent();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_quiBtn:
               m_gamingUI.closeHonourHallPanel();
               break;
            case m_pkBtn:
               showPKHonourHall();
               break;
            case m_worldBossBtn:
               showWorldBossHonourHall();
               break;
            case m_EndlessSingleBtn:
               showEndlessSingleHonourHall();
               break;
            case m_EndlessDoubleBtn:
               showEndlessDoubleHonourHall();
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function init() : void
      {
         MyFunction2.loadXMLAndGetServerTimeFunction("HonourPanel",function(param1:XML, param2:String):void
         {
            m_honourHanllXML = param1;
            m_timeStr = param2;
            m_show = MyFunction2.returnShowByClassName("HonourHallPanel") as Sprite;
            addChild(m_show);
            m_pkBtn = new MySwitchBtnLogicShell();
            m_pkBtn.setShow(m_show["pkSwitchBtn"]);
            m_pkBtn.setTipString("单人PK荣誉大厅");
            ClearUtil.clearObject(m_switchGroup);
            m_switchGroup = new SwitchBtnGroupLogicShell(MySwitchBtnLogicShell);
            m_switchGroup.addSwitchBtn(m_pkBtn);
            m_worldBossBtn = new MySwitchBtnLogicShell();
            m_worldBossBtn.setShow(m_show["worldBossSwitchBtn"]);
            m_worldBossBtn.setTipString("世界Boss荣誉大厅");
            m_switchGroup.addSwitchBtn(m_worldBossBtn);
            m_EndlessSingleBtn = new MySwitchBtnLogicShell();
            m_EndlessSingleBtn.setShow(m_show["EndlessSwitchBtnSingle"]);
            m_EndlessSingleBtn.setTipString("无尽闯关荣誉大厅(单人)");
            m_switchGroup.addSwitchBtn(m_EndlessSingleBtn);
            m_EndlessDoubleBtn = new MySwitchBtnLogicShell();
            m_EndlessDoubleBtn.setShow(m_show["EndlessSwitchBtnDouble"]);
            m_EndlessDoubleBtn.setTipString("无尽闯关荣誉大厅(双人)");
            m_switchGroup.addSwitchBtn(m_EndlessDoubleBtn);
            m_panelMC = new MovieClipPlayLogicShell();
            m_panelMC.setShow(m_show["panel"]);
            m_quiBtn = new ButtonLogicShell2();
            m_quiBtn.setShow(m_show["quitBtn"]);
            m_quiBtn.setTipString("点击退出");
            switch(m_wantGotoHallName)
            {
               case "pkHonourHall":
                  m_pkBtn.turnActiveAndDispatchEvent();
                  break;
               case "worldBossHonourHall":
                  m_worldBossBtn.turnActiveAndDispatchEvent();
                  break;
               case "EndlessSingleHonourHall":
                  m_EndlessSingleBtn.turnActiveAndDispatchEvent();
                  break;
               case "EndlessDoubleHonourHall":
                  m_EndlessDoubleBtn.turnActiveAndDispatchEvent();
                  break;
               default:
                  m_switchGroup.addEnd();
            }
         },showWarningBox,true);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_worldBossPanel)
         {
            m_worldBossPanel.render(param1);
         }
         if(m_endlessSinglePanel)
         {
            m_endlessSinglePanel.render(param1);
         }
         if(m_endlessDoublePanel)
         {
            m_endlessDoublePanel.render(param1);
         }
      }
      
      private function showPKHonourHall() : void
      {
         if(m_worldBossPanel)
         {
            ClearUtil.clearObject(m_worldBossPanel);
         }
         m_worldBossPanel = null;
         if(m_endlessSinglePanel)
         {
            ClearUtil.clearObject(m_endlessSinglePanel);
         }
         m_endlessSinglePanel = null;
         if(m_endlessDoublePanel)
         {
            ClearUtil.clearObject(m_endlessDoublePanel);
         }
         m_endlessDoublePanel = null;
         m_pkPanel = new PKHonourPanel();
         m_pkPanel.setHonourHallPanel(this);
         m_pkPanel.setHounorHallXML(m_honourHanllXML);
         m_pkPanel.setTimeStr(m_timeStr);
         m_pkPanel.setShow(m_panelMC.getShow());
         var _loc1_:Date = new TimeUtil().stringToDate(m_timeStr);
         if(_loc1_.month == 10 && _loc1_.fullYear == 2015)
         {
            m_pkPanel.initData(139);
         }
         else
         {
            m_pkPanel.initData(GameData.MonthCicly ? 1583 : 1576);
         }
         _loc1_ = null;
      }
      
      private function showWorldBossHonourHall() : void
      {
         if(m_pkPanel)
         {
            ClearUtil.clearObject(m_pkPanel);
         }
         m_pkPanel = null;
         if(m_endlessSinglePanel)
         {
            ClearUtil.clearObject(m_endlessSinglePanel);
         }
         m_endlessSinglePanel = null;
         if(m_endlessDoublePanel)
         {
            ClearUtil.clearObject(m_endlessDoublePanel);
         }
         m_endlessDoublePanel = null;
         m_worldBossPanel = new WorldBossHonourPanel();
         m_worldBossPanel.setHonourHallPanel(this);
         m_worldBossPanel.setHounorHallXML(m_honourHanllXML);
         m_worldBossPanel.setTimeStr(m_timeStr);
         m_worldBossPanel.setShow(m_panelMC.getShow());
         m_worldBossPanel.initData();
      }
      
      private function showEndlessSingleHonourHall() : void
      {
         if(m_pkPanel)
         {
            ClearUtil.clearObject(m_pkPanel);
         }
         m_pkPanel = null;
         if(m_worldBossPanel)
         {
            ClearUtil.clearObject(m_worldBossPanel);
         }
         m_worldBossPanel = null;
         if(m_endlessDoublePanel)
         {
            ClearUtil.clearObject(m_endlessDoublePanel);
         }
         m_endlessDoublePanel = null;
         m_endlessSinglePanel = new EndlessSingleHonourPanel();
         m_endlessSinglePanel.setHonourHallPanel(this);
         m_endlessSinglePanel.setHounorHallXML(m_honourHanllXML);
         m_endlessSinglePanel.setTimeStr(m_timeStr);
         m_endlessSinglePanel.setShow(m_panelMC.getShow());
         m_endlessSinglePanel.initData();
      }
      
      private function showEndlessDoubleHonourHall() : void
      {
         if(m_pkPanel)
         {
            ClearUtil.clearObject(m_pkPanel);
         }
         m_pkPanel = null;
         if(m_worldBossPanel)
         {
            ClearUtil.clearObject(m_worldBossPanel);
         }
         m_worldBossPanel = null;
         if(m_endlessSinglePanel)
         {
            ClearUtil.clearObject(m_endlessSinglePanel);
         }
         m_endlessSinglePanel = null;
         m_endlessDoublePanel = new EndlessDoubleHonourPanel();
         m_endlessDoublePanel.setHonourHallPanel(this);
         m_endlessDoublePanel.setHounorHallXML(m_honourHanllXML);
         m_endlessDoublePanel.setTimeStr(m_timeStr);
         m_endlessDoublePanel.setShow(m_panelMC.getShow());
         m_endlessDoublePanel.initData();
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 300;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

