package UI.Utils.CreateAdvancePet
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.MyFunction;
   import UI.Pets.Pet;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class CreateAdvancePet
   {
      
      public function CreateAdvancePet()
      {
         super();
      }
      
      public function createAdvancePet(param1:int, param2:Pet) : CreateAdvancePetReturn
      {
         var _loc5_:* = null;
         var _loc4_:int = 0;
         var _loc11_:SkillVO = null;
         var _loc7_:Pet = new Pet(null);
         var _loc3_:EquipmentVO = XMLSingle.getEquipmentVOByID(param1,XMLSingle.getInstance().equipmentXML);
         var _loc6_:AdvancePetEquipmentVO = _loc3_ as AdvancePetEquipmentVO;
         var _loc8_:Vector.<SkillVO> = new Vector.<SkillVO>();
         for each(var _loc10_ in param2.petEquipmentVO.passiveSkillVOs)
         {
            if(_loc10_)
            {
               _loc4_ = (_loc10_ as PetPassiveSkillVO).promoteValue;
               _loc11_ = XMLSingle.getSkill(MyFunction.getInstance().calculateCurrentLevelID(int(_loc10_.id),param2.petEquipmentVO.level + 1),XMLSingle.getInstance().skillXML);
               (_loc11_ as PetPassiveSkillVO).promoteValue = _loc4_;
               (_loc11_ as PetPassiveSkillVO).value = (_loc10_ as PetPassiveSkillVO).originalValue + (_loc10_ as PetPassiveSkillVO).promoteValue;
               _loc8_.push(_loc11_);
            }
         }
         ClearUtil.nullArr(_loc6_.passiveSkillVOs);
         _loc6_.passiveSkillVOs = _loc8_;
         var _loc9_:int = _loc6_.essentialPercent * _loc6_.essentialVolume;
         _loc6_.essentialPercent = _loc9_ / _loc6_.essentialVolume;
         _loc6_.experiencePercent = 0;
         _loc6_.talentVO = param2.petEquipmentVO.talentVO.clone();
         MyFunction.getInstance().refreshPet(_loc6_);
         _loc7_.petEquipmentVO = _loc6_;
         return new CreateAdvancePetReturn(_loc3_,_loc7_);
      }
   }
}

