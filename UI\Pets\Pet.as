package UI.Pets
{
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Players.PlayerVO;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   
   public class Pet
   {
      
      private var _playerVO:PlayerVO;
      
      private var _petEquipmentVO:PetEquipmentVO;
      
      public function Pet(param1:PetEquipmentVO)
      {
         super();
         _petEquipmentVO = param1;
      }
      
      public function clear() : void
      {
         if(_petEquipmentVO)
         {
            _petEquipmentVO.clear();
         }
         _petEquipmentVO = null;
      }
      
      public function get petEquipmentVO() : PetEquipmentVO
      {
         return _petEquipmentVO;
      }
      
      public function set petEquipmentVO(param1:PetEquipmentVO) : void
      {
         var _loc2_:AdvancePetEquipmentVO = null;
         if(_petEquipmentVO is AdvancePetEquipmentVO)
         {
            _loc2_ = _petEquipmentVO as AdvancePetEquipmentVO;
            _loc2_.recoverPetAwaProAttri();
         }
         if(_petEquipmentVO)
         {
            _petEquipmentVO.pet = null;
            _petEquipmentVO.playerVO = null;
         }
         _petEquipmentVO = param1;
         if(_petEquipmentVO)
         {
            _petEquipmentVO.pet = this;
            _petEquipmentVO.playerVO = playerVO;
         }
         if(_petEquipmentVO is AdvancePetEquipmentVO)
         {
            _loc2_ = _petEquipmentVO as AdvancePetEquipmentVO;
            _loc2_.setPetAwaProAttri();
         }
         if(Boolean(playerVO) && playerVO.player)
         {
            playerVO.player.changeData();
         }
      }
      
      public function get playerVO() : PlayerVO
      {
         return _playerVO;
      }
      
      public function set playerVO(param1:PlayerVO) : void
      {
         _playerVO = param1;
      }
      
      public function render(param1:int) : void
      {
         if(_petEquipmentVO)
         {
            (_petEquipmentVO.activeSkillVO as PetActiveSkillVO).currentCDTime -= param1;
         }
      }
   }
}

