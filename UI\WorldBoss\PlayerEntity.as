package UI.WorldBoss
{
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.Players.PlayerVO;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AttackAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.CriticalAndDodgeData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.DieAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.PlayEndListener;
   import UI.WorldBoss.Boss.AbleAttackedBoss.AbleAttackedBoss;
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Boss.BossBloodData;
   import UI.WorldBoss.FlashView.FlashViewFactory;
   import UI.WorldBoss.ShakeView.ShakeViewFactory;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   
   public class PlayerEntity extends OwnSideEntity
   {
      
      private var m_playerVO:PlayerVO;
      
      private var m_bloodBar:CMSXChangeBarLogicShell;
      
      private var m_isShowBar:Boolean;
      
      public function PlayerEntity()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_playerVO = null;
         ClearUtil.clearObject(m_bloodBar);
         m_bloodBar = null;
      }
      
      public function setPlayerVO(param1:PlayerVO) : void
      {
         m_playerVO = param1;
         m_isReadyFight = true;
         var _loc4_:XML = m_worldBossXML.playerShow[0].player.(@playerType == m_playerVO.playerType)[0];
         trace("+++++++++++++" + _loc4_);
         m_myLoader.getClass(_loc4_.@swf,_loc4_.@className,getShowSuccess,getShowFail);
         m_myLoader.load();
         _loc4_ = m_worldBossXML.playerViewData[0].shakeView[0];
         var _loc2_:ShakeViewFactory = new ShakeViewFactory();
         m_shakeView_attack = _loc2_.createShakeViewByXML(_loc4_);
         ClearUtil.clearObject(_loc2_);
         _loc4_ = m_worldBossXML.playerViewData[0].flashView[0];
         var _loc3_:FlashViewFactory = new FlashViewFactory();
         m_flashView_attack = _loc3_.createFlashViewByXML(_loc4_);
         ClearUtil.clearObject(_loc3_);
      }
      
      public function setIsShowBar(param1:Boolean) : void
      {
         m_isShowBar = param1;
         if(m_bloodBar)
         {
            m_bloodBar.getShow().visible = m_isShowBar;
         }
      }
      
      override public function attack(param1:Vector.<IEntity>) : void
      {
         var _loc6_:int = 0;
         var _loc11_:int = 0;
         var _loc13_:AbleAttackedBoss = null;
         var _loc8_:int = 0;
         var _loc3_:* = null;
         var _loc4_:BossBloodData = null;
         var _loc12_:CriticalAndDodgeData = null;
         _loc11_ = int(param1.length);
         var _loc9_:Vector.<int> = new Vector.<int>();
         var _loc15_:Vector.<int> = new Vector.<int>();
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc10_:Vector.<Number> = new Vector.<Number>();
         var _loc14_:Vector.<int> = new Vector.<int>();
         var _loc7_:Vector.<CriticalAndDodgeData> = new Vector.<CriticalAndDodgeData>();
         _loc6_ = 0;
         while(_loc6_ < _loc11_)
         {
            _loc13_ = param1[_loc6_] as AbleAttackedBoss;
            _loc12_ = new CriticalAndDodgeData();
            _loc9_.push(_loc13_.getLevel());
            _loc8_ = caculateHurt(_loc13_,_loc12_);
            _loc13_.addLostHurt(_loc8_);
            _loc4_ = new BossBloodData();
            _loc13_.getBloodData(_loc4_);
            _loc2_.push(_loc4_.beAttackedBloodVolume);
            _loc10_.push(_loc4_.bloodPercent);
            _loc14_.push(_loc4_.bloodShowIndex);
            _loc15_.push(_loc8_);
            _loc7_.push(_loc12_);
            _loc6_++;
         }
         var _loc5_:AttackAnimationData = new AttackAnimationData();
         _loc5_.roundNum = m_world.getGetNextStepActionEnities().getRoundNum();
         _loc5_.attackEntity = this;
         _loc5_.attackEntityLevel = m_playerVO.level;
         _loc5_.beAttackedEntities = param1.slice(0);
         _loc5_.beAttackedEntityLevels = _loc9_;
         _loc5_.hurts = _loc15_;
         _loc5_.currentBloods = _loc2_;
         _loc5_.bloodPercents = _loc10_;
         _loc5_.bloodShowIndexs = _loc14_;
         _loc5_.criticalAndDodgeDatas = _loc7_;
         m_animationQueueData.addAnimationData(_loc5_);
      }
      
      override public function isDie() : Boolean
      {
         if(m_playerVO.bloodPercent <= 0)
         {
            m_playerVO.bloodPercent = 1;
            return true;
         }
         return false;
      }
      
      override public function Die() : void
      {
         var _loc2_:DieAnimationData = new DieAnimationData();
         _loc2_.dieEntitiy = this;
         var _loc1_:PlayEndListener = new PlayEndListener();
         _loc1_.animationData = _loc2_;
         _loc1_.playEndFun = clear;
         _loc2_.addPlayEndListener(_loc1_);
         m_animationQueueData.addAnimationData(_loc2_);
      }
      
      private function caculateHurt(param1:Boss, param2:CriticalAndDodgeData) : int
      {
         var _loc5_:int = 0;
         var _loc3_:Number = Math.min(ProgramStartData.getInstance().getOne(),Math.max(ProgramStartData.getInstance().getZero(),m_playerVO.criticalRate / ProgramStartData.getInstance().getPercent_oneHundred() - param1.getRiotValue()));
         var _loc4_:Number = Math.min(ProgramStartData.getInstance().getOne(),Math.max(ProgramStartData.getInstance().getZero(),param1.getDodge() - m_playerVO.hit));
         var _loc6_:Number = Math.random();
         if(_loc6_ < _loc4_)
         {
            param2.isBeDodge = true;
            return ProgramStartData.getInstance().getZero();
         }
         _loc6_ = Math.random();
         if(_loc6_ < _loc3_)
         {
            param2.isCritical = true;
            _loc5_ = Math.round(Math.max(ProgramStartData.getInstance().getOne(),m_playerVO.attack * (ProgramStartData.getInstance().getOne() + m_proAttackPercent) * (ProgramStartData.getInstance().getOne() + ProgramStartData.getInstance().getNormalCriticalMulti()) - param1.getDefence()));
         }
         else
         {
            _loc5_ = Math.max(ProgramStartData.getInstance().getOne(),m_playerVO.attack * (ProgramStartData.getInstance().getOne() + m_proAttackPercent) - param1.getDefence());
         }
         trace("人物提升攻击力百分比为：",m_proAttackPercent);
         return _loc5_;
      }
      
      public function getPlayerVO() : PlayerVO
      {
         return m_playerVO;
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_mc = new AnimationShowPlayLogicShell();
         m_mc.addNextStopListener(this);
         m_mc.setShow(new _loc2_() as DisplayObject);
         addChild(m_mc.getShow() as DisplayObject);
         m_bloodBar = new CMSXChangeBarLogicShell();
         m_bloodBar.setShow(m_mc.getShow()["card"]["bloodBar"]);
         m_bloodBar.getShow().visible = m_isShowBar;
         setPlayerBar(m_playerVO.bloodPercent,Math.round(m_playerVO.bloodPercent * m_playerVO.bloodVolume) + "/" + m_playerVO.bloodVolume);
         setLevelStr(m_playerVO.level);
         m_isReadyPlay = true;
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
      }
      
      public function setPlayerBar(param1:Number, param2:String) : void
      {
         if(m_bloodBar)
         {
            m_bloodBar.change(param1);
            m_bloodBar.setDataShow(param2);
         }
      }
   }
}

