package YJFY.Skill.PetSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_JianSkill;
   import YJFY.World.World;
   
   public class Skill_Pet11Skill extends CuboidAreaAttackSkill_JianSkill
   {
      
      public function Skill_Pet11Skill()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         playDisappear();
         return true;
      }
   }
}

