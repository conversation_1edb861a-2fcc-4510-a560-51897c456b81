package UI.Pets.Talents
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class TalentVO
   {
      
      public var description:String;
      
      private var _name:String;
      
      private var _id:int;
      
      private var _className:String;
      
      private var _level:int;
      
      private var _maxLevel:int;
      
      private var _owner:String;
      
      private var _talentType:String;
      
      private var _promoteValue:int;
      
      private var _upgradePill:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function TalentVO()
      {
         super();
         init();
      }
      
      public function clear() : void
      {
         _binaryEn = null;
         _antiwear = null;
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.level = _level;
         _antiwear.name = _name;
         _antiwear.className = _className;
         _antiwear.maxLevel = _maxLevel;
         _antiwear.owner = _owner;
         _antiwear.talentType = _talentType;
         _antiwear.promoteValue = _promoteValue;
         _antiwear.upgradePill = _upgradePill;
      }
      
      public function clone() : TalentVO
      {
         var _loc1_:TalentVO = new TalentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      protected function cloneAttribute(param1:TalentVO) : void
      {
         param1.name = this.name;
         param1.id = this.id;
         param1.className = this.className;
         param1.level = this.level;
         param1.owner = this.owner;
         param1.talentType = this.talentType;
         param1.promoteValue = this.promoteValue;
         param1.maxLevel = this.maxLevel;
         param1.upgradePill = this.upgradePill;
         param1.description = this.description;
      }
      
      public function get name() : String
      {
         return _antiwear.name;
      }
      
      public function set name(param1:String) : void
      {
         _antiwear.name = param1;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get className() : String
      {
         return _antiwear.className;
      }
      
      public function set className(param1:String) : void
      {
         _antiwear.className = param1;
      }
      
      public function get level() : int
      {
         return _antiwear.level;
      }
      
      public function set level(param1:int) : void
      {
         _antiwear.level = param1;
      }
      
      public function get maxLevel() : int
      {
         return _antiwear.maxLevel;
      }
      
      public function set maxLevel(param1:int) : void
      {
         _antiwear.maxLevel = param1;
      }
      
      public function get owner() : String
      {
         return _antiwear.owner;
      }
      
      public function set owner(param1:String) : void
      {
         _antiwear.owner = param1;
      }
      
      public function get talentType() : String
      {
         return _antiwear.talentType;
      }
      
      public function set talentType(param1:String) : void
      {
         _antiwear.talentType = param1;
      }
      
      public function get promoteValue() : int
      {
         return _antiwear.promoteValue;
      }
      
      public function set promoteValue(param1:int) : void
      {
         _antiwear.promoteValue = param1;
      }
      
      public function get upgradePill() : int
      {
         return _antiwear.upgradePill;
      }
      
      public function set upgradePill(param1:int) : void
      {
         _antiwear.upgradePill = param1;
      }
   }
}

