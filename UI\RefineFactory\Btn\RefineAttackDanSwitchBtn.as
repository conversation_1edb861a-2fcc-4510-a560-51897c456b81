package UI.RefineFactory.Btn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class RefineAttackDanSwitchBtn extends SwitchBtn
   {
      
      public function RefineAttackDanSwitchBtn()
      {
         super();
         setTipString("攻击丹");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToRefineAttackDan"));
      }
   }
}

