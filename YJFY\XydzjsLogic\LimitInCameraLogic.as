package YJFY.XydzjsLogic
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   
   public class LimitInCameraLogic
   {
      
      protected var m_lastRenderCoordinate:Coordinate;
      
      protected var m_p3dVector3D:P3DVector3D;
      
      protected var m_isHaveLastRenderCoordinate:Boolean;
      
      public function LimitInCameraLogic()
      {
         super();
         m_lastRenderCoordinate = new Coordinate();
         m_p3dVector3D = new P3DVector3D();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_lastRenderCoordinate);
         m_lastRenderCoordinate = null;
         ClearUtil.clearObject(m_p3dVector3D);
         m_p3dVector3D = null;
      }
      
      public function render2(param1:IAnimalEntity) : void
      {
         m_lastRenderCoordinate.setTo(param1.getX(),param1.getY(),param1.getZ());
         m_isHaveLastRenderCoordinate = true;
      }
      
      public function render1(param1:IAnimalEntity) : void
      {
         if(m_isHaveLastRenderCoordinate && param1.getWorld())
         {
            if((param1.getWorld().getCamera().getMinX() > param1.getX() || param1.getWorld().getCamera().getMaxX() < param1.getX()) && (param1.getWorld().getCamera().getMinX() < m_lastRenderCoordinate.getX() && param1.getWorld().getCamera().getMaxX() > m_lastRenderCoordinate.getX()))
            {
               param1.setNewPosition(m_lastRenderCoordinate.getX(),m_lastRenderCoordinate.getY(),m_lastRenderCoordinate.getZ());
               m_p3dVector3D.setTo(0,param1.getBody().getVelocityY(),param1.getBody().getVelocityZ());
               param1.getBody().setVelocity(m_p3dVector3D);
            }
         }
      }
   }
}

