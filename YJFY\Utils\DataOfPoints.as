package YJFY.Utils
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.geom.Point;
   
   public class DataOfPoints
   {
      
      private var m_points:Vector.<Point>;
      
      public function DataOfPoints()
      {
         super();
         m_points = new Vector.<Point>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_points);
         m_points = null;
      }
      
      public function copy(param1:DataOfPoints) : void
      {
         var _loc3_:int = 0;
         m_points.length = 0;
         var _loc2_:int = int(param1.m_points.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_points.push(param1.m_points[_loc3_].clone());
            _loc3_++;
         }
      }
      
      public function getPointNum() : int
      {
         return m_points.length;
      }
      
      public function getPointByIndex(param1:int) : Point
      {
         return m_points[param1].clone();
      }
      
      public function initByMovieClip(param1:MovieClip) : void
      {
         var _loc4_:DisplayObject = null;
         var _loc2_:Point = null;
         var _loc5_:int = 0;
         var _loc3_:int = param1.totalFrames;
         m_points.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            param1.gotoAndStop(_loc5_ + 1);
            _loc4_ = param1.getChildAt(0);
            _loc2_ = new Point(_loc4_.x,_loc4_.y);
            m_points.push(_loc2_);
            _loc5_++;
         }
      }
      
      public function getPoint() : Vector.<Point>
      {
         return m_points;
      }
   }
}

