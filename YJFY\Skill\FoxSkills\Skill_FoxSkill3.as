package YJFY.Skill.FoxSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.RealityEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfColorTransforms;
   import YJFY.Utils.DataOfPoints;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   import YJFY.geom.Area3D.Cuboid;
   import flash.geom.Point;
   
   public class Skill_FoxSkill3 extends CuboidAreaAttackSkill_OneSkillShow implements IPushSkill
   {
      
      private const m_const_stage_1:uint = 1;
      
      private const m_const_stage_2:uint = 2;
      
      private const m_const_stage_3:uint = 3;
      
      private const m_const_stage_4:uint = 4;
      
      private const m_const_stage_5:uint = 5;
      
      private const m_const_skillAttackReachFrameLabel1:String = "skillAttackReach1";
      
      private const m_const_skillAttackReachFrameLabel2:String = "skillAttackReach2";
      
      private const m_const_skillAttackReachFrameLabel3:String = "skillAttackReach3";
      
      private const m_const_skillAttackReachFrameLabel4:String = "skillAttackReach4";
      
      private const m_const_skillAttackReachFrameLabel5:String = "skillAttackReach5";
      
      private const m_const_pushStartFrameLabel:String = "pushStart";
      
      private const m_const_pushEndFrameLabel:String = "pushEnd";
      
      private const m_const_skillEndFrameLabel:String = "skillEnd^stop^";
      
      private var m_isPush:Boolean;
      
      private var m_isPushEntity:Boolean;
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_skillDirectionX:int;
      
      private var m_moveDataOfPoints:DataOfPoints;
      
      private var m_originalCoordOfOwner:Coordinate;
      
      private var m_pushForce:P3DVector3D;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_currentSkillStage:uint;
      
      private var m_attackRanges:Vector.<Cuboid>;
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      private var m_flashViewDataOfColorTransforms:DataOfColorTransforms;
      
      private var m_pushForce2:P3DVector3D;
      
      public function Skill_FoxSkill3()
      {
         super();
         m_moveDataOfPoints = new DataOfPoints();
         m_originalCoordOfOwner = new Coordinate();
         m_pushForce = new P3DVector3D();
         m_pushForce2 = new P3DVector3D();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
         m_pushForce.setTo(5000,0,0);
         m_attackRanges = new Vector.<Cuboid>();
         m_isAttackReachWhenRelease = false;
         m_shakeViewDataOfPoints = new DataOfPoints();
         m_flashViewDataOfColorTransforms = new DataOfColorTransforms();
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = pushEntity;
      }
      
      override public function clear() : void
      {
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         ClearUtil.clearObject(m_moveDataOfPoints);
         m_moveDataOfPoints = null;
         ClearUtil.clearObject(m_originalCoordOfOwner);
         m_originalCoordOfOwner = null;
         ClearUtil.clearObject(m_pushForce);
         m_pushForce = null;
         ClearUtil.clearObject(m_attackRanges);
         m_attackRanges = null;
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         ClearUtil.clearObject(m_flashViewDataOfColorTransforms);
         m_flashViewDataOfColorTransforms = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         ClearUtil.clearObject(m_pushForce2);
         m_pushForce2 = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         var _loc11_:int = 0;
         var _loc4_:Cuboid = null;
         super.initByXML(param1);
         var _loc10_:XMLList = param1.attackRanges[0].attackRange;
         var _loc7_:int = int(_loc10_.length());
         ClearUtil.clearObject(m_attackRanges);
         m_attackRanges.length = 0;
         _loc11_ = 0;
         while(_loc11_ < _loc7_)
         {
            _loc4_ = new Cuboid(_loc10_[_loc11_].@x,_loc10_[_loc11_].@y,_loc10_[_loc11_].@z,_loc10_[_loc11_].@xRange,_loc10_[_loc11_].@yRange,_loc10_[_loc11_].@zRange);
            m_attackRanges.push(_loc4_);
            _loc11_++;
         }
         var _loc9_:String = String(param1.shakeView[0].@swfPath);
         var _loc6_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc9_,_loc6_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         var _loc2_:String = String(param1.flashView[0].@swfPath);
         var _loc8_:String = String(param1.flashView[0].@className);
         m_myLoader.getClass(_loc2_,_loc8_,getFlashViewMovieClipSuccess,getFlashViewMovieClipFailFun);
         var _loc3_:String = String(param1.moveData[0].@swfPath);
         var _loc5_:String = String(param1.moveData[0].@className);
         m_myLoader.getClass(_loc3_,_loc5_,getMoveDataOfPointsShowSuccess,getMoveDataOfPointsShowFail);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      public function setPushData(param1:P3DVector3D) : void
      {
         if(param1)
         {
            m_pushForce.copy(param1);
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_originalCoordOfOwner.setTo(m_owner.getX(),m_owner.getY(),m_owner.getZ());
         m_skillDirectionX = m_owner.getShowDirection();
         m_owner.forceSetDirection(m_owner.getShowDirection(),0);
         m_currentSkillStage = 0;
      }
      
      override public function render(param1:World) : void
      {
         var _loc2_:Point = null;
         super.render(param1);
         if(m_isRun && m_isPush)
         {
            m_owner.setMoveSpeed(0);
            if(m_owner.getAnimationShow(m_bodyDefId).currentFrame < m_moveDataOfPoints.getPointNum())
            {
               _loc2_ = m_moveDataOfPoints.getPointByIndex(m_owner.getAnimationShow(m_bodyDefId).currentFrame);
               m_owner.setNewPosition(m_originalCoordOfOwner.getX() + m_owner.getShowDirection() * _loc2_.x,m_originalCoordOfOwner.getY() + _loc2_.y,m_originalCoordOfOwner.getZ());
            }
            renderPushEntity();
         }
      }
      
      private function getMoveDataOfPointsShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_moveDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getMoveDataOfPointsShowFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      private function getFlashViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_flashViewDataOfColorTransforms.initByMovieClip(new _loc2_());
      }
      
      private function getFlashViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      override protected function ownerSetDirection(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
         if(m_isRun)
         {
            if(m_owner)
            {
               m_owner.forceSetDirection(m_skillDirectionX,0);
            }
         }
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      private function renderPushEntity() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      private function pushEntity(param1:IEntity) : void
      {
         pushEntity2(param1);
         if(m_isPushEntity)
         {
            m_pushForce2.multi2(m_owner.getShowDirection(),m_pushForce);
            m_pushForce2.multi2(param1.getBody().getMass(),m_pushForce2);
            param1.applyForce(m_pushForce2);
         }
      }
      
      private function pushEntity2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "pushStart":
               m_isPush = true;
               break;
            case "pushEnd":
               m_isPush = false;
               break;
            case "skillAttackReach1":
            case "skillAttackReach2":
            case "skillAttackReach3":
            case "skillAttackReach4":
            case "skillAttackReach5":
               addSkillStageAndChangeAttackRange();
               attackReach(m_world);
               switch(param1)
               {
                  case "skillAttackReach3":
                  case "skillAttackReach4":
                  case "skillAttackReach5":
                     m_world.shakeView(m_shakeViewDataOfPoints);
                     m_world.flashView(m_flashViewDataOfColorTransforms);
               }
               break;
            case "skillEnd^stop^":
               endSkill2();
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         attackAfter(param1);
      }
      
      protected function attackAfter(param1:IEntity) : void
      {
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            switch(int(m_currentSkillStage) - 2)
            {
               case 0:
                  if(!(param1 is TangMonkEntity) && (!(param1 is RealityEntity) || (param1 as RealityEntity).canNotBePushed))
                  {
                     param1.applyImpulse(new P3DVector3D(m_owner.getShowDirection() * 500000,0,500000));
                  }
                  break;
               case 1:
               case 2:
               case 3:
                  if(!(param1 is TangMonkEntity) && (!(param1 is RealityEntity) || (param1 as RealityEntity).canNotBePushed))
                  {
                     param1.applyImpulse(new P3DVector3D(m_owner.getShowDirection() * 80,0,300).multi(param1.getBody().getMass()));
                  }
            }
         }
      }
      
      private function addSkillStageAndChangeAttackRange() : void
      {
         ++m_currentSkillStage;
         m_cuboidRange.copy(m_attackRanges[m_currentSkillStage - 1]);
      }
   }
}

