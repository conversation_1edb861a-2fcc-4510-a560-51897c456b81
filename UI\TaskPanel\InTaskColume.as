package UI.TaskPanel
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.Task.TaskVO.MTaskVO;
   import flash.display.Bitmap;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class InTaskColume extends SwitchBtn
   {
      
      private var _taskNameText:TextField;
      
      private var _isNewBitmap:Bitmap;
      
      private var _isCompleteBitmap:Bitmap;
      
      private var _taskVO:MTaskVO;
      
      public function InTaskColume()
      {
         super();
         initTaskColume();
      }
      
      public function set taskVO(param1:MTaskVO) : void
      {
         _taskVO = param1;
         _taskNameText.text = _taskVO.name;
         _taskNameText.height = _taskNameText.textHeight + 5;
         _taskNameText.y = (height - _taskNameText.height) / 2;
         if(param1.isNew)
         {
            if(!_isNewBitmap)
            {
               _isNewBitmap = new Bitmap(new NewTaskTipBitmapData());
            }
            if(!getChildByName(_isNewBitmap.name))
            {
               _isNewBitmap.x = 261.4;
               _isNewBitmap.y = 1.3;
               addChild(_isNewBitmap);
            }
         }
         else
         {
            if(Boolean(_isNewBitmap) && getChildByName(_isNewBitmap.name))
            {
               removeChild(_isNewBitmap);
            }
            if(_isNewBitmap)
            {
               if(_isNewBitmap.bitmapData)
               {
                  _isNewBitmap.bitmapData.dispose();
               }
               _isNewBitmap.bitmapData = null;
               _isNewBitmap = null;
            }
         }
         if(param1.state == 3)
         {
            if(!_isCompleteBitmap)
            {
               _isCompleteBitmap = new Bitmap(new CompletedTaskBitmapData());
            }
            if(!getChildByName(_isCompleteBitmap.name))
            {
               _isCompleteBitmap.x = 200;
               _isCompleteBitmap.y = 15;
               addChild(_isCompleteBitmap);
            }
         }
         else
         {
            if(Boolean(_isCompleteBitmap) && getChildByName(_isCompleteBitmap.name))
            {
               removeChild(_isCompleteBitmap);
            }
            if(_isCompleteBitmap)
            {
               if(_isCompleteBitmap.bitmapData)
               {
                  _isCompleteBitmap.bitmapData.dispose();
               }
               _isCompleteBitmap.bitmapData = null;
               _isCompleteBitmap = null;
            }
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         _taskNameText = null;
         _taskVO = null;
         if(_isNewBitmap)
         {
            if(_isNewBitmap.bitmapData)
            {
               _isNewBitmap.bitmapData.dispose();
            }
            _isNewBitmap.bitmapData = null;
         }
         _isNewBitmap = null;
         if(_isCompleteBitmap)
         {
            if(_isCompleteBitmap.bitmapData)
            {
               _isCompleteBitmap.bitmapData.dispose();
            }
            _isCompleteBitmap.bitmapData = null;
         }
         _isCompleteBitmap = null;
      }
      
      private function initTaskColume() : void
      {
         _taskNameText = new TextField();
         _taskNameText.x = 10;
         _taskNameText.y = 0;
         _taskNameText.width = width - 10;
         _taskNameText.height = height;
         addChild(_taskNameText);
         _taskNameText.autoSize = "center";
         _taskNameText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,20,16762997);
         _taskNameText.filters = [new GlowFilter(0,1,2,2,10,3)];
         _taskNameText.embedFonts = true;
         _taskNameText.selectable = false;
         _taskNameText.wordWrap = true;
         _taskNameText.mouseEnabled = false;
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickTaskColume",_taskVO));
      }
   }
}

