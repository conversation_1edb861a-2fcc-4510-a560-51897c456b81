package UI.newGuide.GuideMainLine
{
   import UI.newGuide.NewGuidePanel;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GuideMainPanel
   {
      
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_mainlist:GuideMainList;
      
      private var m_mainbtnshell:GuideMainBtnShell;
      
      public function GuideMainPanel()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_mainlist);
         m_mainlist = null;
         ClearUtil.clearObject(m_mainbtnshell);
         m_mainbtnshell = null;
      }
      
      public function show() : void
      {
         m_mainlist.show();
         m_mainbtnshell.show();
      }
      
      public function refreshlist() : void
      {
         m_mainlist.refreshlist();
      }
      
      public function hide() : void
      {
         m_mainlist.hide();
         m_mainbtnshell.hide();
      }
      
      public function init(param1:NewGuidePanel, param2:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_show = param2;
         initParams();
      }
      
      private function initParams() : void
      {
         m_mainlist = new GuideMainList();
         m_mainlist.init(m_newguidepanel,this,m_show);
         m_mainbtnshell = new GuideMainBtnShell();
         m_mainbtnshell.init(m_newguidepanel,this,m_show);
      }
      
      public function refreshScript(param1:GuideMainItem) : void
      {
         m_mainbtnshell.refreshScript(param1.getData());
      }
   }
}

