package YJFY.Lottery
{
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.XMLSingle;
   import YJFY.EndlessMode.EndlessLotteryUI;
   import YJFY.LevelMode3.LotteryNew;
   import YJFY.Loader.YJFYLoader;
   import YJFY.MySprite;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.Sprite;
   
   public class Lottery
   {
      
      private var m_show:MySprite;
      
      private var m_lotteryData:LotteryData;
      
      private var m_lotteryPanel:LotteryPanel;
      
      private var m_newlottery:LotteryNew;
      
      private var m_endlessLotteryPanel:EndlessLotteryUI;
      
      private var m_lotteryDataOne:LotteryDataOne;
      
      private var m_isReadyToLottery:Boolean;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      public function Lottery()
      {
         super();
         m_show = new MySprite();
         m_show.mouseChildren = true;
         m_show.mouseEnabled = true;
         m_lotteryData = new LotteryData();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_lotteryData);
         m_lotteryData = null;
         ClearUtil.clearObject(m_newlottery);
         m_newlottery = null;
         if(m_lotteryPanel)
         {
            ClearUtil.clearObject(m_lotteryPanel);
         }
         m_lotteryPanel = null;
         if(m_endlessLotteryPanel)
         {
            ClearUtil.clearObject(m_endlessLotteryPanel);
         }
         m_endlessLotteryPanel = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_lotteryDataOne = null;
         m_lotteryDataOne = null;
         m_myLoader = null;
         m_enterFrameTime = null;
      }
      
      public function init(param1:XML, param2:YJFYLoader, param3:EnterFrameTime) : void
      {
         m_myLoader = param2;
         m_enterFrameTime = param3;
         m_lotteryData.initByXML(param1);
      }
      
      public function createLotteryPanel() : void
      {
         createLotteryPanel_in();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_isReadyToLottery && m_lotteryPanel != null && m_lotteryPanel.getIsReady())
         {
            m_lotteryPanel.startLottery(m_lotteryDataOne);
            m_lotteryDataOne = null;
            m_isReadyToLottery = false;
         }
         if(m_lotteryPanel)
         {
            m_lotteryPanel.render(param1);
         }
         if(m_isReadyToLottery && m_endlessLotteryPanel != null && m_endlessLotteryPanel.getIsReady())
         {
            m_endlessLotteryPanel.startLottery(m_lotteryDataOne);
            m_lotteryDataOne = null;
            m_isReadyToLottery = false;
         }
         if(m_endlessLotteryPanel)
         {
            m_endlessLotteryPanel.render(param1);
         }
         if(m_isReadyToLottery && m_newlottery && m_newlottery.getIsReady())
         {
            m_newlottery.startLottery(m_lotteryDataOne);
            m_lotteryDataOne = null;
            m_isReadyToLottery = false;
         }
         if(m_newlottery)
         {
            m_newlottery.render(param1);
         }
      }
      
      private function createLotteryPanel_in() : void
      {
         if(m_lotteryPanel == null)
         {
            m_lotteryPanel = new LotteryPanel();
            m_lotteryPanel.init(m_myLoader,m_lotteryData,this,m_enterFrameTime);
         }
         m_show.addChild(m_lotteryPanel);
      }
      
      public function createNewLotteryPanel() : void
      {
         if(m_newlottery == null)
         {
            m_newlottery = new LotteryNew();
            m_newlottery.init(m_myLoader,m_lotteryData,this,m_enterFrameTime);
         }
         m_show.addChild(m_newlottery);
      }
      
      public function createEndlessLotteryPanel() : void
      {
         if(m_endlessLotteryPanel == null)
         {
            m_endlessLotteryPanel = new EndlessLotteryUI();
            m_endlessLotteryPanel.init(m_myLoader,m_lotteryData,this,m_enterFrameTime);
         }
         m_show.addChild(m_endlessLotteryPanel);
      }
      
      public function closeLotteryPanel() : void
      {
         if(m_lotteryPanel)
         {
            ClearUtil.clearObject(m_lotteryPanel);
         }
         m_lotteryPanel = null;
         if(m_endlessLotteryPanel)
         {
            ClearUtil.clearObject(m_endlessLotteryPanel);
         }
         m_endlessLotteryPanel = null;
      }
      
      public function lottery() : Boolean
      {
         if(m_lotteryDataOne == null)
         {
            m_lotteryDataOne = m_lotteryData.randomOneLotteryDataOne();
         }
         if(isCanPutPackage(m_lotteryDataOne) == false)
         {
            GamingUI.getInstance().showMessageTip("背包空间不足");
            trace("背包空间不足");
            return false;
         }
         trace("抽奖成功");
         m_isReadyToLottery = true;
         putPackage(m_lotteryDataOne);
         var _loc1_:SaveTaskInfo = new SaveTaskInfo();
         _loc1_.type = "4399";
         _loc1_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc1_);
         MyFunction2.saveGame();
         return true;
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      private function isCanPutPackage(param1:LotteryDataOne) : Boolean
      {
         var isCanPutPackage:Boolean;
         var lotteryDataOne:LotteryDataOne = param1;
         var length:int = int(lotteryDataOne.getEquipmentNum());
         var equipmentVO:EquipmentVO = XMLSingle.getEquipmentVOByID(int(lotteryDataOne.getEquipmentId()),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
         var equipmentVOs:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var i:int = 0;
         while(i < length)
         {
            equipmentVOs.push(equipmentVO.clone());
            ++i;
         }
         MyFunction2.falseAddEquipmentVOs(equipmentVOs,GamingUI.getInstance().player1,function():void
         {
            isCanPutPackage = false;
         },function():void
         {
            isCanPutPackage = true;
         },null,null);
         ClearUtil.clearObject(equipmentVO);
         equipmentVO = null;
         ClearUtil.clearObject(equipmentVOs);
         equipmentVOs = null;
         return isCanPutPackage;
      }
      
      private function putPackage(param1:LotteryDataOne) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = int(param1.getEquipmentNum());
         var _loc2_:EquipmentVO = XMLSingle.getEquipmentVOByID(int(param1.getEquipmentId()),XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
         var _loc4_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_.push(_loc2_.clone());
            _loc5_++;
         }
         MyFunction2.trueAddEquipmentVOs(_loc4_,GamingUI.getInstance().player1,GamingUI.getInstance().showMessageTip,["兑换成功"]);
         ClearUtil.clearObject(_loc2_);
         _loc2_ = null;
         ClearUtil.clearObject(_loc4_);
         _loc4_ = null;
      }
   }
}

