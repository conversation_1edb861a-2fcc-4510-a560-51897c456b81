package UI.RecaptureGold.RewardPanel
{
   import UI.Button.QuitBtn;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKFunction;
   import UI.TextTrace.traceText;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class RewardPanel extends MySprite
   {
      
      private var _columes:Vector.<RewardColume>;
      
      public var quitBtn:QuitBtn;
      
      public function RewardPanel(param1:int)
      {
         super();
         init(param1);
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(_columes)
         {
            _loc1_ = int(_columes.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               if(_columes[_loc3_])
               {
                  _columes[_loc3_].clear();
               }
               _columes[_loc3_] = null;
               _loc3_++;
            }
         }
         _columes = null;
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 300;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
      
      private function init(param1:int) : void
      {
         var rankId:int = param1;
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 5;
         addChild(quitBtn);
         quitBtn.name = "quitBtn";
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         GamingUI.getInstance().manBan.text.text = "处理中....";
         MyFunction2.loadXMLAndGetServerTimeFunction("RecaptureGoldReward",function(param1:XML, param2:String):void
         {
            var i:int;
            var length:int;
            var myRank:int;
            var xml:XML = param1;
            var timeStr:String = param2;
            var pkHonourPanelXML:XML = xml;
            var timeString:String = timeStr;
            PKFunction.getInstance().getOneRankListDataByUserName(rankId,GameData.getInstance().getLoginReturnData().getName(),null,function(param1:Array):void
            {
               var _loc2_:Boolean = false;
               if(!param1 || param1.length == 0)
               {
                  traceText("暂无排名");
                  myRank = -1;
               }
               else
               {
                  length = param1.length;
                  _loc2_ = false;
                  i = 0;
                  while(i < length)
                  {
                     if(param1[i].index == GameData.getInstance().getSaveFileData().index)
                     {
                        myRank = param1[i].rank;
                        _loc2_ = true;
                        break;
                     }
                     i++;
                  }
                  if(!_loc2_)
                  {
                     myRank = -1;
                  }
               }
               initColume(pkHonourPanelXML,myRank,timeString);
               i = 0;
               while(i < length)
               {
                  param1[i] = null;
                  i++;
               }
               param1 = null;
            });
         },showWarningBox,true);
      }
      
      private function initColume(param1:XML, param2:int, param3:String) : void
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:Object = null;
         var _loc5_:RewardColume = null;
         var _loc4_:* = undefined;
         var _loc9_:XMLList = param1.reward;
         var _loc6_:XML = XMLSingle.getInstance().equipmentXML;
         _loc7_ = int(_loc9_.length());
         _columes = new Vector.<RewardColume>();
         _loc10_ = 0;
         while(_loc10_ < _loc7_)
         {
            _loc8_ = {};
            _loc8_.name = String(_loc9_[_loc10_].@name);
            _loc8_.description = String(_loc9_[_loc10_].@description);
            _loc8_["medalID"] = String(_loc9_[_loc10_].@medal);
            _loc5_ = new RewardColume(_loc8_,param3,_loc6_);
            _loc4_ = MyFunction.getInstance().excreteString(String(_loc9_[_loc10_].@rank));
            if(param2 >= _loc4_[0] && param2 <= _loc4_[1])
            {
               _loc5_.setActivate(true);
            }
            else
            {
               _loc5_.setActivate(false);
            }
            _loc5_.x = int(_loc10_ / 4) * 420 + 70;
            _loc5_.y = _loc10_ % 4 * 100 + 120;
            addChild(_loc5_);
            _columes.push(_loc5_);
            _loc10_++;
         }
      }
   }
}

