package UI.SocietySystem.SeverLink
{
   import UI.Utils.UidAndIdxData;
   import flash.utils.ByteArray;
   
   public class InformationBodyDetailUtil
   {
      
      public function InformationBodyDetailUtil()
      {
         super();
      }
      
      public function writeUidAndIdxToByteArr(param1:ByteArray, param2:Number, param3:int) : void
      {
         param2 = param2 * 10 + param3;
         param1.writeDouble(param2);
      }
      
      public function readUidAndIdxFromByteArr(param1:ByteArray) : UidAndIdxData
      {
         var _loc3_:UidAndIdxData = new UidAndIdxData();
         var _loc2_:Number = param1.readDouble();
         _loc3_.idx = _loc2_ % 10;
         _loc3_.uid = Math.floor(_loc2_ / 10);
         return _loc3_;
      }
      
      public function getStringLength(param1:String) : int
      {
         if(Boolean(param1) == false)
         {
            return 0;
         }
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.endian = "littleEndian";
         _loc2_.writeUTFBytes(param1);
         var _loc3_:int = int(_loc2_.length);
         _loc2_.clear();
         return _loc3_;
      }
      
      public function encrytInormationBodyByteArray(param1:ByteArray, param2:ByteArray) : void
      {
         var _loc5_:* = 0;
         var _loc3_:int = 8;
         param1.position = 0;
         param2.position = 0;
         var _loc4_:int = int(param1.bytesAvailable);
         _loc5_ = _loc3_;
         while(_loc5_ < _loc4_)
         {
            param1[_loc5_] ^= param2[0];
            _loc5_++;
         }
      }
      
      public function encrytByteArray(param1:ByteArray, param2:ByteArray) : void
      {
         var _loc4_:int = 0;
         param1.position = 0;
         param2.position = 0;
         var _loc3_:int = int(param1.bytesAvailable);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            param1[_loc4_] ^= param2[0];
            _loc4_++;
         }
      }
      
      public function decryptByteArray(param1:ByteArray, param2:ByteArray) : void
      {
         var _loc6_:int = 0;
         param1.position = 0;
         param2.position = 0;
         var _loc4_:int = int(param1.bytesAvailable);
         var _loc3_:String = "";
         var _loc5_:String = "";
         _loc6_ = 0;
         while(_loc6_ < _loc4_)
         {
            _loc3_ += "" + param1[_loc6_] + "|";
            param1[_loc6_] ^= param2[0];
            _loc5_ += "" + param1[_loc6_] + "|";
            _loc6_++;
         }
         trace("strBeforeDecrypet:",_loc3_);
         trace("strAfterDecrypet:",_loc5_);
      }
   }
}

