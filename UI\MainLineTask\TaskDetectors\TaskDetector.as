package UI.MainLineTask.TaskDetectors
{
   public class TaskDetector
   {
      
      protected var m_eventStr:String;
      
      public function TaskDetector()
      {
         super();
      }
      
      public function initByXML(param1:XML) : void
      {
         m_eventStr = String(param1.@eventStr);
      }
      
      public function clear() : void
      {
      }
      
      public function detect() : void
      {
      }
   }
}

