package YJFY.PKMode.PKLogic
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.Players.Player;
   import UI2.NewRank.RankPK;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAI.AutomationAI.RunOrWalkToOnePositionAI;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.GameEntityListener;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dog;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dragon;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Fox;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Houyi;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Monkey;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Rabbit;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.GameEntity.XydzjsPlayerAndPet.ZiXia;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.MapBase;
   import YJFY.PKMode.PK;
   import YJFY.PKMode.PKingInforPanel;
   import YJFY.Part1;
   import YJFY.Skill.ActiveSkill.ActiveSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.AreaAttackSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.ICuboidAreaAttackSkill;
   import YJFY.Skill.ISkill;
   import YJFY.Skill.Skill;
   import YJFY.Skill.SkillListener;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.IGetNextStepActionEntities;
   import YJFY.StepAttackGame.IJudgeStepAttackGameIsEnd;
   import YJFY.StepAttackGame.NextEntities;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.XydzjsData.IAttackDataCalculateVOXydzjs;
   import YJFY.XydzjsLogic.GetOwnerOfSubEntity;
   
   public class PKWorld extends MapBase implements IGetNextStepActionEntities, IJudgeStepAttackGameIsEnd
   {
      
      private var m_myPlayer1InitPosition_onlyOne:Coordinate;
      
      private var m_foePlayer1InitPosition_onlyOne:Coordinate;
      
      private var m_myPlayer1:PKPlayer;
      
      private var m_myPlayer2:PKPlayer;
      
      private var m_myPlayer1InitPosition:Coordinate;
      
      private var m_myPlayer2InitPosition:Coordinate;
      
      private var m_foePlayer1:PKPlayer;
      
      private var m_foePlayer2:PKPlayer;
      
      private var m_foePlayer1InitPosition:Coordinate;
      
      private var m_foePlayer2InitPosition:Coordinate;
      
      private var m_pkStepWorld:StepAttackGameWorld;
      
      private var m_pkIsStart:Boolean;
      
      private var m_pkIsEnd:Boolean;
      
      private var m_isStartPlayPkProgress:Boolean;
      
      private var m_isStartPlayPKProgress2:Boolean;
      
      private var m_isEndPlayPkProgress:Boolean;
      
      private var m_stepDataManager:StepDataManager;
      
      private var m_isTwoMode:Boolean;
      
      private var m_runToOnePositionAI:RunOrWalkToOnePositionAI;
      
      private var m_runReturnPositionAI:RunOrWalkToOnePositionAI;
      
      private var m_runToPosition:Coordinate;
      
      private var m_skillListener:SkillListener;
      
      private var m_pkValueAnimationsManager:PkValueAnimationsManager;
      
      private var m_pkingInforPanel:PKingInforPanel;
      
      private var m_getOwnerOfSubsidinaryEntity:GetOwnerOfSubEntity;
      
      private var m_isFristGet:Boolean = true;
      
      private var m_isMyFirst:Boolean;
      
      private var m_isMyWin:Boolean;
      
      private var m_gameEntityListener:GameEntityListener;
      
      private var m_myUiPlayer1:UI.Players.Player;
      
      private var m_myUiPlayer2:UI.Players.Player;
      
      private var m_foeUiPlayer1:UI.Players.Player;
      
      private var m_foeUiPlayer2:UI.Players.Player;
      
      private var m_currentStepData:OneStepData;
      
      private var m_pk:PK;
      
      private var m_thisload:YJFYLoader;
      
      private var m_petWaitRunSkill:ActiveSkill;
      
      private var m_rankpk:RankPK;
      
      public function PKWorld()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         m_thisload = new YJFYLoader();
         m_thisload.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myPlayer1InitPosition_onlyOne = new Coordinate(200,250,0);
         m_foePlayer1InitPosition_onlyOne = new Coordinate(760,250,0);
         m_myPlayer1InitPosition = new Coordinate(200,100,0);
         m_myPlayer2InitPosition = new Coordinate(200,400,0);
         m_foePlayer1InitPosition = new Coordinate(760,100,0);
         m_foePlayer2InitPosition = new Coordinate(760,400,0);
         m_stepDataManager = new StepDataManager();
         m_skillListener = new SkillListener();
         m_skillListener.endSkillFun = petSkillEnd;
         m_pkValueAnimationsManager = new PkValueAnimationsManager();
         m_runToPosition = new Coordinate();
         m_getOwnerOfSubsidinaryEntity = new GetOwnerOfSubEntity();
         m_gameEntityListener = new GameEntityListener();
         m_gameEntityListener.attackSuccessFun = attackSuccess2;
         m_gameEntityListener.beAttackFun = beAttack2;
         m_gameEntityListener.attackEndFun = attackEnd2;
         m_gameEntityListener.preJudgeIsFoeFun = gameEntityPreJudgeIsFoe;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_myPlayer1);
         m_myPlayer1 = null;
         ClearUtil.clearObject(m_myPlayer2);
         m_myPlayer2 = null;
         ClearUtil.clearObject(m_myPlayer1InitPosition);
         m_myPlayer1InitPosition = null;
         ClearUtil.clearObject(m_myPlayer2InitPosition);
         m_myPlayer2InitPosition = null;
         ClearUtil.clearObject(m_foePlayer1);
         m_foePlayer1 = null;
         ClearUtil.clearObject(m_foePlayer2);
         m_foePlayer2 = null;
         ClearUtil.clearObject(m_foePlayer1InitPosition);
         m_foePlayer1InitPosition = null;
         ClearUtil.clearObject(m_foePlayer2InitPosition);
         m_foePlayer2InitPosition = null;
         ClearUtil.clearObject(m_myPlayer1InitPosition_onlyOne);
         m_myPlayer1InitPosition_onlyOne = null;
         ClearUtil.clearObject(m_foePlayer1InitPosition_onlyOne);
         m_foePlayer1InitPosition_onlyOne = null;
         ClearUtil.clearObject(m_pkStepWorld);
         m_pkStepWorld = null;
         ClearUtil.clearObject(m_stepDataManager);
         m_stepDataManager = null;
         ClearUtil.clearObject(m_runToOnePositionAI);
         m_runToOnePositionAI = null;
         ClearUtil.clearObject(m_runReturnPositionAI);
         m_runReturnPositionAI = null;
         ClearUtil.clearObject(m_runToPosition);
         m_runToPosition = null;
         ClearUtil.clearObject(m_skillListener);
         m_skillListener = null;
         ClearUtil.clearObject(m_pkValueAnimationsManager);
         m_pkValueAnimationsManager = null;
         ClearUtil.clearObject(m_pkingInforPanel);
         m_pkingInforPanel = null;
         ClearUtil.clearObject(m_getOwnerOfSubsidinaryEntity);
         m_getOwnerOfSubsidinaryEntity = null;
         ClearUtil.clearObject(m_thisload);
         m_thisload = null;
         ClearUtil.clearObject(m_gameEntityListener);
         m_gameEntityListener = null;
         m_currentStepData = null;
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         m_foeUiPlayer1 = null;
         m_foeUiPlayer2 = null;
         m_pk = null;
         m_rankpk = null;
         m_petWaitRunSkill = null;
         super.clear();
      }
      
      public function setPk(param1:PK) : void
      {
         m_pk = param1;
      }
      
      public function setRankWorld(param1:RankPK) : void
      {
         m_rankpk = param1;
      }
      
      override public function init() : void
      {
         super.init();
         m_runToOnePositionAI = new RunOrWalkToOnePositionAI();
         m_runToOnePositionAI.init(null,m_world);
         m_runReturnPositionAI = new RunOrWalkToOnePositionAI();
         m_runReturnPositionAI.init(null,m_world);
         Part1.getInstance().showGameWaitShow();
      }
      
      public function setIsTwoMode(param1:Boolean) : void
      {
         m_isTwoMode = param1;
      }
      
      public function initUiPlayerData(param1:UI.Players.Player, param2:UI.Players.Player, param3:UI.Players.Player, param4:UI.Players.Player) : void
      {
         if(m_myUiPlayer1)
         {
            throw new Error("不能重复初始化");
         }
         m_myUiPlayer1 = param1;
         m_myUiPlayer2 = param2;
         m_foeUiPlayer1 = param3;
         m_foeUiPlayer2 = param4;
         m_myPlayer1 = new PKPlayer();
         m_myPlayer1.setStepDataManager(m_stepDataManager);
         if(Boolean(m_myUiPlayer2) && m_isTwoMode)
         {
            m_myPlayer2 = new PKPlayer();
            m_myPlayer2.setStepDataManager(m_stepDataManager);
         }
         else
         {
            m_myPlayer1InitPosition.copy(m_myPlayer1InitPosition_onlyOne);
         }
         m_foePlayer1 = new PKPlayer();
         m_foePlayer1.setStepDataManager(m_stepDataManager);
         if(Boolean(m_foeUiPlayer2) && m_isTwoMode)
         {
            m_foePlayer2 = new PKPlayer();
            m_foePlayer2.setStepDataManager(m_stepDataManager);
         }
         else
         {
            m_foePlayer1InitPosition.copy(m_foePlayer1InitPosition_onlyOne);
         }
      }
      
      override public function render(param1:EnterFrameTime) : void
      {
         super.render(param1);
         if(m_myPlayer1)
         {
            m_myPlayer1.render();
         }
         if(m_myPlayer2)
         {
            m_myPlayer2.render();
         }
         if(m_foePlayer1)
         {
            m_foePlayer1.render();
         }
         if(m_foePlayer2)
         {
            m_foePlayer2.render();
         }
         startStepGame();
         playPkProgress();
         m_pkValueAnimationsManager.render();
         m_runToOnePositionAI.render();
         if(m_runToOnePositionAI.getIsReachTargetPosition())
         {
            attackEntityAttack();
            m_runToOnePositionAI.setOwner(null);
         }
         m_runReturnPositionAI.render();
         if(m_runReturnPositionAI.getIsReachTargetPosition())
         {
            m_currentStepData = null;
            m_runReturnPositionAI.setOwner(null);
         }
         if(m_petWaitRunSkill)
         {
            runPetWaitRunSkill();
         }
      }
      
      private function runPetWaitRunSkill() : void
      {
         if(m_petWaitRunSkill)
         {
            m_petWaitRunSkill.startSkill(m_world);
            m_petWaitRunSkill = null;
         }
      }
      
      public function getMyPlayer1() : PKPlayer
      {
         return m_myPlayer1;
      }
      
      public function getMyPlayer2() : PKPlayer
      {
         return m_myPlayer2;
      }
      
      public function getFoePlayer1() : PKPlayer
      {
         return m_foePlayer1;
      }
      
      public function getFoePlayer2() : PKPlayer
      {
         return m_foePlayer2;
      }
      
      private function startStepGame() : void
      {
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Number = NaN;
         if(isReadyStartStepGame())
         {
            trace("开始pk，进行pk计算");
            m_myPlayer1.readyStartFight();
            if(m_myPlayer2)
            {
               m_myPlayer2.readyStartFight();
            }
            m_foePlayer1.readyStartFight();
            if(m_foePlayer2)
            {
               m_foePlayer2.readyStartFight();
            }
            ClearUtil.clearObject(m_pkStepWorld);
            if(m_pkingInforPanel == null)
            {
               m_pkingInforPanel = new PKingInforPanel();
               if(m_pk == null)
               {
                  m_pkingInforPanel.setPK(null);
                  m_pkingInforPanel.setRankWorld(m_rankpk);
               }
               else
               {
                  m_pkingInforPanel.setRankWorld(null);
                  m_pkingInforPanel.setPK(m_pk);
               }
               m_pkingInforPanel.setPKWorld(this);
               m_pkingInforPanel.setMyLoader(m_thisload);
               m_pkingInforPanel.init();
            }
            m_pkingInforPanel.setPKPlayers(m_myPlayer1,m_myPlayer2,m_foePlayer1,m_foePlayer2);
            addChild(m_pkingInforPanel);
            m_pkStepWorld = new StepAttackGameWorld();
            m_pkStepWorld.setGetNextStepActionEntities(this);
            m_pkStepWorld.setJudgeStepAttackGameIsEnd(this);
            m_pkStepWorld.addFriend(m_myPlayer1);
            if(m_myPlayer2)
            {
               m_pkStepWorld.addFriend(m_myPlayer2);
            }
            m_pkStepWorld.addEnemy(m_foePlayer1);
            if(m_myPlayer2)
            {
               m_pkStepWorld.addEnemy(m_foePlayer2);
            }
            m_pkIsEnd = false;
            m_pkIsStart = true;
            _loc1_ = m_myPlayer1.getOffensiveValue();
            if(m_myPlayer2)
            {
               _loc1_ += m_myPlayer2.getOffensiveValue();
            }
            _loc3_ = m_foePlayer1.getOffensiveValue();
            if(m_foePlayer2)
            {
               _loc3_ += m_foePlayer2.getOffensiveValue();
            }
            _loc2_ = Math.random() + 0.5 + (_loc1_ - _loc3_) / 1000;
            if(_loc2_ > 0)
            {
               m_isMyFirst = Boolean(int(_loc2_));
            }
            else
            {
               m_isMyFirst = false;
            }
            m_pkStepWorld.startAttack();
         }
      }
      
      private function isReadyStartStepGame() : Boolean
      {
         if(m_pkIsStart)
         {
            return false;
         }
         if(m_myPlayer1 == null)
         {
            return false;
         }
         if(m_myPlayer1.getPlayer() == null)
         {
            return false;
         }
         if(m_myPlayer1.getPlayer().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_myPlayer1.getPlayer().getPet()) && m_myPlayer1.getPlayer().getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(m_foePlayer1 == null)
         {
            return false;
         }
         if(m_foePlayer1.getPlayer() == null)
         {
            return false;
         }
         if(m_foePlayer1.getPlayer().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_foePlayer1.getPlayer().getPet()) && m_foePlayer1.getPlayer().getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(m_isTwoMode)
         {
            if(m_myUiPlayer2)
            {
               if(m_myPlayer2 == null)
               {
                  return false;
               }
               if(m_myPlayer2.getPlayer() == null)
               {
                  return false;
               }
               if(m_myPlayer2.getPlayer().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_myPlayer2.getPlayer().getPet()) && m_myPlayer2.getPlayer().getPet().getAnimalEntity() == null)
               {
                  return false;
               }
            }
            if(m_foeUiPlayer2)
            {
               if(m_foePlayer2 == null)
               {
                  return false;
               }
               if(m_foePlayer2.getPlayer() == null)
               {
                  return false;
               }
               if(m_foePlayer2.getPlayer().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_foePlayer2.getPlayer().getPet()) && m_foePlayer2.getPlayer().getPet().getAnimalEntity() == null)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      protected function playPkProgress() : void
      {
         if(m_isStartPlayPkProgress && m_isStartPlayPKProgress2 && m_isEndPlayPkProgress == false)
         {
            if(m_currentStepData == null)
            {
               if(m_stepDataManager.getIsEndAnimation() == false)
               {
                  m_currentStepData = m_stepDataManager.getOneStepDataByOrder();
                  recoverPkPlayerPostion();
                  walkToAppropriatePosition();
               }
               else
               {
                  stepGameEnd();
               }
            }
            return;
         }
         if(m_isStartPlayPKProgress2 == false && isReadyPlayPKProgress())
         {
            trace("开始pk动画过程");
            m_isStartPlayPkProgress = true;
            ClearUtil.clearObject(m_pkStepWorld);
            m_pkStepWorld = null;
            Part1.getInstance().hideGameWaitShow();
            m_pkingInforPanel.playRandomSideAnimation(m_isMyFirst,startPlayAnimation);
         }
      }
      
      private function startPlayAnimation() : void
      {
         m_isStartPlayPKProgress2 = true;
         m_stepDataManager.startPKAnimation();
         addGameEntityListener();
      }
      
      private function addGameEntityListener() : void
      {
         m_myPlayer1.getPlayer().removeGameEntityListener(m_gameEntityListener);
         if(m_myPlayer1.getPet())
         {
            m_myPlayer1.getPet().removeGameEntityListener(m_gameEntityListener);
         }
         m_foePlayer1.getPlayer().removeGameEntityListener(m_gameEntityListener);
         if(m_foePlayer1.getPet())
         {
            m_foePlayer1.getPet().removeGameEntityListener(m_gameEntityListener);
         }
         if(m_isTwoMode)
         {
            if(m_myPlayer2)
            {
               m_myPlayer2.getPlayer().removeGameEntityListener(m_gameEntityListener);
               if(m_myPlayer2.getPet())
               {
                  m_myPlayer2.getPet().removeGameEntityListener(m_gameEntityListener);
               }
            }
            if(m_foePlayer2)
            {
               m_foePlayer2.getPlayer().removeGameEntityListener(m_gameEntityListener);
               if(m_foePlayer2.getPet())
               {
                  m_foePlayer2.getPet().removeGameEntityListener(m_gameEntityListener);
               }
            }
         }
         m_myPlayer1.getPlayer().addGameEntityListener(m_gameEntityListener);
         if(m_myPlayer1.getPet())
         {
            m_myPlayer1.getPet().addGameEntityListener(m_gameEntityListener);
         }
         m_foePlayer1.getPlayer().addGameEntityListener(m_gameEntityListener);
         if(m_foePlayer1.getPet())
         {
            m_foePlayer1.getPet().addGameEntityListener(m_gameEntityListener);
         }
         if(m_isTwoMode)
         {
            if(m_myPlayer2)
            {
               m_myPlayer2.getPlayer().addGameEntityListener(m_gameEntityListener);
               if(m_myPlayer2.getPet())
               {
                  m_myPlayer2.getPet().addGameEntityListener(m_gameEntityListener);
               }
            }
            if(m_foePlayer2)
            {
               m_foePlayer2.getPlayer().addGameEntityListener(m_gameEntityListener);
               if(m_foePlayer2.getPet())
               {
                  m_foePlayer2.getPet().addGameEntityListener(m_gameEntityListener);
               }
            }
         }
      }
      
      public function nowOverGame() : void
      {
         stepGameEnd();
         m_world.stopWorldTime();
      }
      
      private function stepGameEnd() : void
      {
         m_isEndPlayPkProgress = true;
         m_pkingInforPanel.playGameEndAnimation(m_isMyWin,null);
         trace("pk动画过程结束");
      }
      
      protected function isReadyPlayPKProgress() : Boolean
      {
         if(m_pkStepWorld == null)
         {
            return false;
         }
         if(m_pkIsEnd == false)
         {
            return false;
         }
         if(m_myPlayer1.getPlayer().getAnimalEntity() == null)
         {
            return false;
         }
         if(m_myPlayer1.getPlayer().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(Boolean(m_myPlayer1.getPlayer().getPet()) && m_myPlayer1.getPlayer().getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_myPlayer1.getPlayer().getPet()) && m_myPlayer1.getPlayer().getPet().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(m_foePlayer1.getPlayer().getAnimalEntity() == null)
         {
            return false;
         }
         if(m_foePlayer1.getPlayer().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(Boolean(m_foePlayer1.getPlayer().getPet()) && m_foePlayer1.getPlayer().getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_foePlayer1.getPlayer().getPet()) && m_foePlayer1.getPlayer().getPet().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(m_isTwoMode)
         {
            if(m_myUiPlayer2)
            {
               if(m_myPlayer2.getPlayer().getAnimalEntity() == null)
               {
                  return false;
               }
               if(m_myPlayer2.getPlayer().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
               if(Boolean(m_myPlayer2.getPlayer().getPet()) && m_myPlayer2.getPlayer().getPet().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_myPlayer2.getPlayer().getPet()) && m_myPlayer2.getPlayer().getPet().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
            }
            if(m_foeUiPlayer2)
            {
               if(m_foePlayer2.getPlayer().getAnimalEntity() == null)
               {
                  return false;
               }
               if(m_foePlayer2.getPlayer().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
               if(Boolean(m_foePlayer2.getPlayer().getPet()) && m_foePlayer2.getPlayer().getPet().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_foePlayer2.getPlayer().getPet()) && m_foePlayer2.getPlayer().getPet().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      private function recoverPkPlayerPostion() : void
      {
         m_myPlayer1.getPlayer().getAnimalEntity_control().setNewPosition(m_myPlayer1InitPosition.getX(),m_myPlayer1InitPosition.getY(),m_myPlayer1InitPosition.getZ());
         m_myPlayer1.getPlayer().getAnimalEntity_control().setDirection(1,0);
         if(m_myPlayer2)
         {
            m_myPlayer2.getPlayer().getAnimalEntity_control().setNewPosition(m_myPlayer2InitPosition.getX(),m_myPlayer2InitPosition.getY(),m_myPlayer2InitPosition.getZ());
            m_myPlayer2.getPlayer().getAnimalEntity_control().setDirection(1,0);
         }
         m_foePlayer1.getPlayer().getAnimalEntity_control().setNewPosition(m_foePlayer1InitPosition.getX(),m_foePlayer1InitPosition.getY(),m_foePlayer1InitPosition.getZ());
         m_foePlayer1.getPlayer().getAnimalEntity_control().setDirection(-1,0);
         if(m_foePlayer2)
         {
            m_foePlayer2.getPlayer().getAnimalEntity_control().setNewPosition(m_foePlayer2InitPosition.getX(),m_foePlayer2InitPosition.getY(),m_foePlayer2InitPosition.getZ());
            m_foePlayer2.getPlayer().getAnimalEntity_control().setDirection(-1,0);
         }
      }
      
      private function walkToAppropriatePosition() : void
      {
         var _loc5_:IAnimalEntity = null;
         var _loc1_:ICuboidAreaAttackSkill = null;
         var _loc4_:IAnimalEntity = null;
         var _loc3_:IAnimalEntity = null;
         var _loc6_:Boolean = false;
         var _loc2_:IAnimalEntity = null;
         if(m_currentStepData.getAttackEntity() is PkPet)
         {
            _loc5_ = (m_currentStepData.getAttackEntity() as PkPet).getPet().getAnimalEntity_control();
            if(_loc5_.getSkillByIndex(0) is ICuboidAreaAttackSkill == false)
            {
               throw new Error();
            }
            _loc1_ = _loc5_.getSkillByIndex(0) as ICuboidAreaAttackSkill;
            _loc4_ = (m_currentStepData.getBeAttackDataByIndex(0).getBeAttackEntity() as PKPlayer).getPlayer().getAnimalEntity();
            if(m_currentStepData.getBeAttackDataNum() > 1)
            {
               _loc3_ = (m_currentStepData.getBeAttackDataByIndex(1).getBeAttackEntity() as PKPlayer).getPlayer().getAnimalEntity();
            }
            if(_loc3_)
            {
               _loc6_ = _loc1_.isAbleAttackOnePosition(_loc4_.getX(),_loc4_.getY(),_loc4_.getZ()) && _loc1_.isAbleAttackOnePosition(_loc3_.getX(),_loc3_.getY(),_loc3_.getZ());
            }
            else
            {
               _loc6_ = _loc1_.isAbleAttackOnePosition(_loc4_.getX(),_loc4_.getY(),_loc4_.getZ());
            }
            if(_loc6_ == false)
            {
               getPetWalkTargetPosition(_loc5_,_loc4_,_loc3_,_loc1_,m_runToPosition);
            }
            else
            {
               m_runToPosition.setTo(_loc5_.getX(),_loc5_.getY(),_loc5_.getZ());
            }
            (m_currentStepData.getAttackEntity() as PkPet).getPet().closeAI();
            m_runToOnePositionAI.setOwner(_loc5_);
            m_runToOnePositionAI.setToWalk();
            m_runToOnePositionAI.moveToOnePosition(m_runToPosition);
         }
         else
         {
            _loc5_ = (m_currentStepData.getAttackEntity() as PKPlayer).getPlayer().getAnimalEntity_control();
            _loc2_ = (m_currentStepData.getBeAttackDataByIndex(0).getBeAttackEntity() as PKPlayer).getPlayer().getAnimalEntity();
            getPlayerWalkTargetPosition(_loc5_,_loc2_,m_runToPosition);
            m_runToOnePositionAI.setOwner(_loc5_);
            m_runToOnePositionAI.setToRun();
            m_runToOnePositionAI.moveToOnePosition(m_runToPosition);
         }
      }
      
      private function attackEntityAttack() : void
      {
         var _loc3_:IAnimalEntity = null;
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:IAnimalEntity = (m_currentStepData.getBeAttackDataByIndex(0).getBeAttackEntity() as PKPlayer).getPlayer().getAnimalEntity();
         (m_currentStepData.getAttackEntity() as IPKEntity).getAnimalEntity_control().setDirection(_loc1_.getX() - (m_currentStepData.getAttackEntity() as IPKEntity).getAnimalEntity().getX(),0);
         if(m_currentStepData.getAttackEntity() is PkPet)
         {
            _loc3_ = (m_currentStepData.getAttackEntity() as PkPet).getPet().getAnimalEntity_control();
            _loc2_ = _loc3_.getSkillNum();
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc3_.getSkillByIndex(_loc4_).addSkillListener(m_skillListener);
               _loc4_++;
            }
            (_loc3_.getSkillByIndex(0) as ActiveSkill).startSkill(m_world);
         }
         else
         {
            (m_currentStepData.getAttackEntity() as PKPlayer).getPlayer().getAnimalEntity_control().setAttackShowIndex(1);
            (m_currentStepData.getAttackEntity() as PKPlayer).getPlayer().getAnimalEntity_control().attack();
         }
      }
      
      private function attackEntityAttackEnd() : void
      {
         if(m_currentStepData.getAttackEntity() is PKPlayer)
         {
            runReturn((m_currentStepData.getAttackEntity() as PKPlayer).getAnimalEntity_control());
         }
         else
         {
            (m_currentStepData.getAttackEntity() as PkPet).getPet().openAI();
            m_currentStepData = null;
         }
      }
      
      private function runReturn(param1:IAnimalEntity) : void
      {
         var _loc2_:Coordinate = null;
         var _loc3_:* = param1;
         if(m_myPlayer1.getPlayer().getAnimalEntity_control() !== _loc3_)
         {
            switch(_loc3_)
            {
               case m_myPlayer2 ? m_myPlayer2.getAnimalEntity_control() : null:
                  _loc2_ = m_myPlayer2InitPosition;
                  break;
               case m_foePlayer1.getPlayer().getAnimalEntity_control():
                  _loc2_ = m_foePlayer1InitPosition;
                  break;
               default:
                  if((m_foePlayer2 ? m_foePlayer2.getAnimalEntity_control() : null) !== _loc3_)
                  {
                     throw new Error("出错了");
                  }
                  _loc2_ = m_foePlayer2InitPosition;
                  break;
            }
         }
         else
         {
            _loc2_ = m_myPlayer1InitPosition;
         }
         m_runReturnPositionAI.setOwner(param1);
         m_runReturnPositionAI.moveToOnePosition(_loc2_);
      }
      
      private function getPetWalkTargetPosition(param1:IAnimalEntity, param2:IAnimalEntity, param3:IAnimalEntity, param4:ICuboidAreaAttackSkill, param5:Coordinate) : void
      {
         var _loc11_:Number = param2.getX() - param1.getX();
         var _loc9_:Number = param3 ? param3.getX() - param1.getX() : 0;
         if(_loc11_ * _loc9_ < 0 || _loc11_ == 0)
         {
            throw new Error();
         }
         var _loc10_:Number = _loc11_ > 0 ? Math.max(_loc11_,_loc9_) : Math.min(_loc11_,_loc9_);
         var _loc6_:Number = Math.random() * param4.getCuboidRange().getXRange();
         var _loc8_:Number = _loc11_ > 0 ? param1.getX() + _loc10_ - _loc6_ : param1.getX() + _loc10_ + _loc6_;
         _loc8_ = Math.max(m_world.getWorldArea().getMinX(),Math.min(m_world.getWorldArea().getMaxX(),_loc8_));
         var _loc7_:Number = Number(param3 ? (param2.getY() + param3.getY()) / 2 : param2.getY());
         param5.setTo(_loc8_,_loc7_,0);
      }
      
      private function getPlayerWalkTargetPosition(param1:IAnimalEntity, param2:IAnimalEntity, param3:Coordinate) : void
      {
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc8_:Number = NaN;
         if(param1.getIsLongDistanceAttack() == false)
         {
            _loc4_ = param2.getX() - param1.getX();
            _loc5_ = Math.random() * param1.getAttackRange().getXRange();
            if(_loc4_ == 0)
            {
               throw new Error();
            }
            _loc7_ = _loc4_ > 0 ? param2.getX() - _loc5_ : param2.getX() + _loc5_;
            _loc6_ = -param1.getAttackRange().getYRange() / 2 + Math.random() * param1.getAttackRange().getYRange();
            _loc8_ = param2.getY() + _loc6_;
            param3.setTo(_loc7_,param2.getY(),0);
         }
         else
         {
            param3.setTo(param1.getX(),param2.getY(),0);
         }
      }
      
      private function entityIsPKEntity(param1:YJFY.Entity.IEntity) : Boolean
      {
         var _loc2_:Boolean = false;
         var _loc3_:* = param1;
         if(m_myPlayer1.getPlayer().getAnimalEntity() !== _loc3_)
         {
            if((m_myPlayer1.getPet() ? m_myPlayer1.getPet().getAnimalEntity() : null) !== _loc3_)
            {
               if((m_myPlayer2 ? m_myPlayer2.getAnimalEntity() : null) !== _loc3_)
               {
                  switch(_loc3_)
                  {
                     case Boolean(m_myPlayer2) && Boolean(m_myPlayer2.getPet()) ? m_myPlayer2.getPet().getAnimalEntity() : null:
                     case m_foePlayer1.getPlayer().getAnimalEntity():
                        addr14:
                        _loc2_ = true;
                        break;
                        addr12:
                        addr13:
                        addr11:
                     default:
                        if((m_foePlayer1.getPet() ? m_foePlayer1.getPet().getAnimalEntity() : null) !== _loc3_)
                        {
                           if((m_foePlayer2 ? m_foePlayer2.getAnimalEntity() : null) !== _loc3_)
                           {
                              if((Boolean(m_foePlayer2) && Boolean(m_foePlayer2.getPet()) ? m_foePlayer2.getPet().getAnimalEntity() : null) !== _loc3_)
                              {
                                 _loc2_ = false;
                                 break;
                              }
                           }
                           §§goto(addr14);
                        }
                        else
                        {
                           §§goto(addr12);
                        }
                  }
                  return _loc2_;
               }
               addr10:
               §§goto(addr11);
            }
            addr9:
            §§goto(addr10);
         }
         §§goto(addr9);
      }
      
      private function isFoeForAttackTarget(param1:YJFY.Entity.IEntity, param2:YJFY.Entity.IEntity) : Boolean
      {
         var _loc3_:Boolean = false;
         var _loc4_:Boolean = false;
         if(param2 == null)
         {
            throw new Error("出错了");
         }
         if(param1 == null)
         {
            throw new Error("出错了");
         }
         if(entityIsPKEntity(param1) == false)
         {
            return false;
         }
         if(entityIsPKEntity(param2) == false)
         {
            return false;
         }
         if(Boolean(m_myPlayer1.getPet()) && param1 == m_myPlayer1.getPet().getAnimalEntity())
         {
            return false;
         }
         if(Boolean(m_myPlayer2) && Boolean(m_myPlayer2.getPet()) && param1 == m_myPlayer2.getPet().getAnimalEntity())
         {
            return false;
         }
         if(Boolean(m_foePlayer1.getPet()) && param1 == m_foePlayer1.getPet().getAnimalEntity())
         {
            return false;
         }
         if(Boolean(m_foePlayer2) && Boolean(m_foePlayer2.getPet()) && param1 == m_foePlayer2.getPet().getAnimalEntity())
         {
            return false;
         }
         if(param2 == m_myPlayer1.getAnimalEntity())
         {
            _loc3_ = true;
         }
         if(Boolean(m_myPlayer1.getPet()) && param2 == m_myPlayer1.getPet().getAnimalEntity())
         {
            _loc3_ = true;
         }
         if(Boolean(m_myPlayer2) && param2 == m_myPlayer2.getAnimalEntity())
         {
            _loc3_ = true;
         }
         if(Boolean(m_myPlayer2) && Boolean(m_myPlayer2.getPet()) && param2 == m_myPlayer2.getPet().getAnimalEntity())
         {
            _loc3_ = true;
         }
         if(param1 == m_myPlayer1.getAnimalEntity())
         {
            _loc4_ = true;
         }
         if(Boolean(m_myPlayer1.getPet()) && param1 == m_myPlayer1.getPet().getAnimalEntity())
         {
            _loc4_ = true;
         }
         if(Boolean(m_myPlayer2) && param1 == m_myPlayer2.getAnimalEntity())
         {
            _loc4_ = true;
         }
         if(Boolean(m_myPlayer2) && Boolean(m_myPlayer2.getPet()) && param1 == m_myPlayer2.getPet().getAnimalEntity())
         {
            _loc4_ = true;
         }
         return _loc3_ != _loc4_;
      }
      
      private function attackSuccess2(param1:GameEntity, param2:YJFY.Entity.IEntity, param3:ISkill, param4:YJFY.Entity.IEntity) : void
      {
         var _loc10_:int = 0;
         var _loc7_:int = 0;
         var _loc5_:BeAttackDataOfOneStepData = null;
         var _loc6_:Boolean = false;
         var _loc9_:* = m_getOwnerOfSubsidinaryEntity.getOwnerOfSubAttackEntity(param4);
         if(_loc9_ == null)
         {
            _loc9_ = param4;
         }
         if(_loc9_ != (m_currentStepData.getAttackEntity() as IPKEntity).getAnimalEntity())
         {
            throw new Error("出错了");
         }
         var _loc8_:IAttackDataCalculateVOXydzjs = param4.getExtra() is IAttackDataCalculateVOXydzjs ? param4.getExtra() as IAttackDataCalculateVOXydzjs : _loc9_.getExtra() as IAttackDataCalculateVOXydzjs;
         if(isFoeForAttackTarget(param2,_loc9_))
         {
            _loc7_ = m_currentStepData.getBeAttackDataNum();
            _loc10_ = 0;
            while(_loc10_ < _loc7_)
            {
               _loc5_ = m_currentStepData.getBeAttackDataByIndex(_loc10_);
               if((_loc5_.getBeAttackEntity() as IPKEntity).getAnimalEntity() == param2)
               {
                  AttackData.hurtNum2++;
                  (_loc8_ as GameEntity).getAnimalEntity_control().setAttackData(_loc5_.getAttackData());
                  m_pkValueAnimationsManager.addValueAnimationToTarget(param2,_loc5_.getAttackData());
                  (_loc5_.getBeAttackEntity() as PKPlayer).decPkBlood2(_loc5_.getAttackData().getHurt());
                  if((_loc5_.getBeAttackEntity() as PKPlayer).getPkBlood2() <= 0)
                  {
                     (_loc5_.getBeAttackEntity() as PKPlayer).getAnimalEntity().die();
                  }
                  _loc6_ = true;
                  break;
               }
               _loc10_++;
            }
            if(_loc6_ == false)
            {
               throw new Error("出错了");
            }
            if((m_currentStepData.getAttackEntity() as IPKEntity).getAnimalEntity_control().getIsLongDistanceAttack())
            {
               attackEntityAttackEnd();
            }
         }
         else
         {
            (_loc8_ as GameEntity).getAnimalEntity_control().setAttackData(null);
         }
         if(m_pkingInforPanel)
         {
            m_pkingInforPanel.refreshShow();
         }
      }
      
      private function beAttack2(param1:GameEntity, param2:YJFY.Entity.IEntity, param3:AttackData, param4:ISkill, param5:YJFY.Entity.IEntity) : void
      {
      }
      
      private function gameEntityPreJudgeIsFoe(param1:GameEntity, param2:YJFY.Entity.IEntity) : void
      {
         param1.setIsFoe(isFoeForAttackTarget(param2,param1.getAnimalEntity()));
      }
      
      private function attackEnd2(param1:GameEntity, param2:IAnimalEntity) : void
      {
         if(param2 != (m_currentStepData.getAttackEntity() as IPKEntity).getAnimalEntity_control())
         {
            throw new Error();
         }
         if(param2.getIsLongDistanceAttack() == false)
         {
            attackEntityAttackEnd();
         }
      }
      
      private function petSkillEnd(param1:Skill) : void
      {
         var _loc2_:int = 0;
         param1.removeSkillListener(m_skillListener);
         if(m_currentStepData.getAttackEntity() is PkPet && (m_currentStepData.getAttackEntity() as PkPet).getAnimalEntity_control().getSkillNum() > 1)
         {
            _loc2_ = (m_currentStepData.getAttackEntity() as PkPet).getAnimalEntity_control().getIndexBySkill(param1);
            if(_loc2_ == -1)
            {
               throw new Error("出现了意外情况");
            }
            if(_loc2_ < (m_currentStepData.getAttackEntity() as PkPet).getAnimalEntity_control().getSkillNum() - 1)
            {
               m_petWaitRunSkill = (m_currentStepData.getAttackEntity() as PkPet).getAnimalEntity_control().getSkillByIndex(_loc2_ + 1) as ActiveSkill;
               return;
            }
         }
         attackEntityAttackEnd();
      }
      
      override protected function mapInitSuccess() : void
      {
         super.mapInitSuccess();
         initPlayerOne(m_myUiPlayer1);
         if(m_myUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_myUiPlayer2);
         }
         initPlayerOne(m_foeUiPlayer1);
         if(m_foeUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_foeUiPlayer2);
         }
      }
      
      private function initPlayerOne(param1:UI.Players.Player) : void
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               m_thisload.getXML("NewGameFolder/Players/Monkey.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "BaiLongMa":
               m_thisload.getXML("NewGameFolder/Players/Dragon.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ErLangShen":
               m_thisload.getXML("NewGameFolder/Players/Dog.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ChangE":
               m_thisload.getXML("NewGameFolder/Players/Rabbit.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Fox":
               m_thisload.getXML("NewGameFolder/Players/Fox.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "TieShan":
               m_thisload.getXML("NewGameFolder/Players/TieShan.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Houyi":
               m_thisload.getXML("NewGameFolder/Players/Houyi.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ZiXia":
               m_thisload.getXML("NewGameFolder/Players/ZiXia.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            default:
               throw new Error();
         }
         m_thisload.load();
      }
      
      private function getPlayerXMLSuccess(param1:YJFYLoaderData, param2:UI.Players.Player) : void
      {
         var _loc3_:YJFY.GameEntity.PlayerAndPet.Player = null;
         var _loc4_:PKPlayer = null;
         if(param1.xmlPath == "NewGameFolder/Players/Monkey.xml")
         {
            _loc3_ = new Monkey();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dragon.xml")
         {
            _loc3_ = new Dragon();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dog.xml")
         {
            _loc3_ = new Dog();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Rabbit.xml")
         {
            _loc3_ = new Rabbit();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Fox.xml")
         {
            _loc3_ = new Fox();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/TieShan.xml")
         {
            _loc3_ = new TieShan();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Houyi.xml")
         {
            _loc3_ = new Houyi();
            (_loc3_ as Houyi).bPk = true;
         }
         else
         {
            if(param1.xmlPath != "NewGameFolder/Players/ZiXia.xml")
            {
               throw new Error();
            }
            _loc3_ = new ZiXia();
         }
         (_loc3_ as PlayerXydzjs).setPkWorld(this);
         _loc3_.setMyLoader(m_thisload);
         _loc3_.setEnterFrameTime(m_enterFrameTime);
         _loc3_.setAnimationDefinitionManager(m_animationDefinitionManager);
         _loc3_.setSoundMananger(m_soundManager);
         _loc3_.initByXML(param1.resultXML);
         switch(param2)
         {
            case m_myUiPlayer1:
               _loc4_ = m_myPlayer1;
               m_myPlayer1.setEntity(_loc3_ as PlayerXydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_myPlayer1InitPosition.getX(),m_myPlayer1InitPosition.getY(),m_myPlayer1InitPosition.getZ());
               initCompletePlayer(m_myPlayer1.getPlayer());
               break;
            case m_myUiPlayer2:
               _loc4_ = m_myPlayer2;
               m_myPlayer2.setEntity(_loc3_ as PlayerXydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_myPlayer2InitPosition.getX(),m_myPlayer2InitPosition.getY(),m_myPlayer2InitPosition.getZ());
               initCompletePlayer(m_myPlayer2.getPlayer());
               break;
            case m_foeUiPlayer1:
               _loc4_ = m_foePlayer1;
               m_foePlayer1.setEntity(_loc3_ as PlayerXydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_foePlayer1InitPosition.getX(),m_foePlayer1InitPosition.getY(),m_foePlayer1InitPosition.getZ());
               initCompletePlayer(m_foePlayer1.getPlayer());
               break;
            case m_foeUiPlayer2:
               _loc4_ = m_foePlayer2;
               m_foePlayer2.setEntity(_loc3_ as PlayerXydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_foePlayer2InitPosition.getX(),m_foePlayer2InitPosition.getY(),m_foePlayer2InitPosition.getZ());
               initCompletePlayer(m_foePlayer2.getPlayer());
               break;
            default:
               throw new Error();
         }
         (_loc3_ as PlayerXydzjs).setIsCreateAutoAttackPet(false);
         (_loc3_ as PlayerXydzjs).setUiPlayer(param2);
         _loc4_.setPet((_loc3_ as PlayerXydzjs).getPet() as PetXydzjs);
         m_world.addEntity(_loc3_.getAnimalEntity());
      }
      
      public function getNextAttackAndBeAttackedEntities() : NextEntities
      {
         var _loc2_:YJFY.StepAttackGame.IEntity = null;
         var _loc4_:NextEntities = new NextEntities();
         var _loc3_:YJFY.StepAttackGame.IEntity = m_pkStepWorld.getCurrentAttackEntity();
         var _loc1_:Vector.<YJFY.StepAttackGame.IEntity> = new Vector.<YJFY.StepAttackGame.IEntity>();
         if(_loc3_ == null)
         {
            if(m_isFristGet == false)
            {
               throw new Error("出错了， 不是第一次获取居然为null");
            }
            if(m_isMyFirst)
            {
               _loc2_ = m_myPlayer1;
               _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
            }
            else
            {
               _loc2_ = m_foePlayer1;
               _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
            }
            m_isFristGet = false;
         }
         else
         {
            var _loc5_:* = _loc3_;
            loop0:
            switch(_loc5_)
            {
               case m_myPlayer1:
                  if(m_myPlayer1.getPet())
                  {
                     _loc2_ = m_myPlayer1.getPkPet();
                     getPetAttackTargets(m_myPlayer1.getPkPet(),m_foePlayer1,m_foePlayer2,_loc1_);
                  }
                  else if(m_myPlayer2 && m_myPlayer2.isDie() == false)
                  {
                     _loc2_ = m_myPlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                  }
                  else if(m_foePlayer1.isDie() == false)
                  {
                     _loc2_ = m_foePlayer1;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                  }
                  else
                  {
                     if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                     {
                        throw new Error("出错了");
                     }
                     _loc2_ = m_foePlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                  }
                  break;
               case m_myPlayer1.getPkPet():
                  if(m_myPlayer2 && m_myPlayer2.isDie() == false)
                  {
                     _loc2_ = m_myPlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                  }
                  else if(m_foePlayer1.isDie() == false)
                  {
                     _loc2_ = m_foePlayer1;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                  }
                  else
                  {
                     if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                     {
                        throw new Error("出错了");
                     }
                     _loc2_ = m_foePlayer2;
                     _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                  }
                  break;
               default:
                  if((m_myPlayer2 ? m_myPlayer2 : null) === _loc5_)
                  {
                     if(m_myPlayer2.getPet())
                     {
                        _loc2_ = m_myPlayer2.getPkPet();
                        getPetAttackTargets(m_myPlayer2.getPkPet(),m_foePlayer1,m_foePlayer2,_loc1_);
                     }
                     else if(m_foePlayer1.isDie() == false)
                     {
                        _loc2_ = m_foePlayer1;
                        _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                     }
                     else
                     {
                        if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                        {
                           throw new Error("出错了");
                        }
                        _loc2_ = m_foePlayer2;
                        _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                     }
                     break;
                  }
                  switch(_loc5_)
                  {
                     case m_myPlayer2 ? m_myPlayer2.getPkPet() : null:
                        if(m_foePlayer1.isDie() == false)
                        {
                           _loc2_ = m_foePlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer1,m_myPlayer1,m_myPlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        break loop0;
                     case m_foePlayer1:
                        if(m_foePlayer1.getPet())
                        {
                           _loc2_ = m_foePlayer1.getPkPet();
                           getPetAttackTargets(m_foePlayer1.getPkPet(),m_myPlayer1,m_myPlayer2,_loc1_);
                        }
                        else if(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false)
                        {
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                     case m_foePlayer1.getPkPet():
                        if(Boolean(m_foePlayer2) && m_foePlayer2.isDie() == false)
                        {
                           _loc2_ = m_foePlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_foePlayer2,m_myPlayer1,m_myPlayer2));
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                     default:
                        if((m_foePlayer2 ? m_foePlayer2 : null) !== _loc5_)
                        {
                           if((m_foePlayer2 ? m_foePlayer2.getPkPet() : null) !== _loc5_)
                           {
                              throw new Error();
                           }
                           if(m_myPlayer1.isDie() == false)
                           {
                              _loc2_ = m_myPlayer1;
                              _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                           }
                           else
                           {
                              if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                              {
                                 throw new Error("出错了");
                              }
                              _loc2_ = m_myPlayer2;
                              _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                           }
                           break loop0;
                        }
                        if(m_foePlayer2.getPet())
                        {
                           _loc2_ = m_foePlayer2.getPkPet();
                           getPetAttackTargets(m_foePlayer2.getPkPet(),m_myPlayer1,m_myPlayer2,_loc1_);
                        }
                        else if(m_myPlayer1.isDie() == false)
                        {
                           _loc2_ = m_myPlayer1;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer1,m_foePlayer1,m_foePlayer2));
                        }
                        else
                        {
                           if(!(Boolean(m_myPlayer2) && m_myPlayer2.isDie() == false))
                           {
                              throw new Error("出错了");
                           }
                           _loc2_ = m_myPlayer2;
                           _loc1_.push(getPlayerAttackTarget(m_myPlayer2,m_foePlayer1,m_foePlayer2));
                        }
                        break loop0;
                  }
            }
         }
         _loc4_.nextAttackEntity = _loc2_;
         _loc4_.nextBeAttackedEntities = _loc1_;
         if(_loc1_.length > 2 || _loc1_.length <= 0)
         {
            throw new Error("nextBeAttackEntities.length出错:" + _loc1_.length);
         }
         return _loc4_;
      }
      
      public function getRoundNum() : int
      {
         return 0;
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
      }
      
      private function getPetAttackTargets(param1:PkPet, param2:PKPlayer, param3:PKPlayer, param4:Vector.<YJFY.StepAttackGame.IEntity>) : void
      {
         switch(int(getPetAttackTargetNum(param1,param2,param3)) - 1)
         {
            case 0:
               param4.push(getPetAttackTarget(param1,param2,param3));
               break;
            case 1:
               param4.push(param2);
               param4.push(param3);
               break;
            default:
               throw new Error();
         }
      }
      
      private function getPlayerAttackTarget(param1:PKPlayer, param2:PKPlayer, param3:PKPlayer) : PKPlayer
      {
         if(param2.isDie() == false)
         {
            return param2;
         }
         if(param3.isDie() == false)
         {
            return param3;
         }
         throw new Error("出错了");
      }
      
      private function getPetAttackTarget(param1:PkPet, param2:PKPlayer, param3:PKPlayer) : PKPlayer
      {
         if(param2.isDie() == false)
         {
            return param2;
         }
         if(param3.isDie() == false)
         {
            return param3;
         }
         throw new Error("出错了");
      }
      
      private function getPetAttackTargetNum(param1:PkPet, param2:PKPlayer, param3:PKPlayer) : uint
      {
         var _loc5_:Skill = param1.getPet().getAnimalEntity().getSkillByIndex(0);
         var _loc4_:uint = 2;
         if(param2.isDie())
         {
            _loc4_--;
         }
         if(param3 == null || param3.isDie())
         {
            _loc4_--;
         }
         if(param3 == null)
         {
            return Math.min(_loc4_,1);
         }
         if(_loc5_ is AttackSkill)
         {
            if(_loc5_ is AreaAttackSkill)
            {
               if(_loc5_ is ICuboidAreaAttackSkill)
               {
                  if(Math.abs(param2.getPlayer().getAnimalEntity().getY() - param3.getPlayer().getAnimalEntity().getY()) < (_loc5_ as ICuboidAreaAttackSkill).getCuboidRange().getYRange())
                  {
                     return Math.min(_loc4_,2);
                  }
                  return Math.min(_loc4_,1);
               }
               throw new Error("还没为其他种类的区域攻击技能做好准备");
            }
            throw new Error("还没为该类型的攻击技能做好装备");
         }
         throw new Error("宠物技能为非攻击技能，代码Pk功能还未非攻击技能做好准备");
      }
      
      public function judeIsEnd() : Boolean
      {
         if(m_myPlayer1.getPkBlood1() <= 0 && (Boolean(m_myPlayer2) == false || m_myPlayer2.getPkBlood1() <= 0))
         {
            m_pkIsEnd = true;
            m_isMyWin = false;
            end();
            return true;
         }
         if(m_foePlayer1.getPkBlood1() <= 0 && (Boolean(m_foePlayer2) == false || m_foePlayer2.getPkBlood1() <= 0))
         {
            m_pkIsEnd = true;
            m_isMyWin = true;
            end();
            return true;
         }
         m_pkIsEnd = false;
         return false;
      }
      
      private function end() : void
      {
         if(m_pk == null)
         {
            m_rankpk.addPKDataAndSubmitToRankList(m_isMyWin);
         }
         else
         {
            m_pk.addPKDataAndSubmitToRankList(m_isMyWin);
         }
      }
      
      protected function initCompletePlayer(param1:YJFY.GameEntity.PlayerAndPet.Player) : void
      {
         (param1 as PlayerXydzjs).setMedalIsDynamic(false);
         if(Boolean(m_myPlayer1.getPlayer()) && (m_myPlayer2 == null || Boolean(m_myPlayer2.getPlayer())) && Boolean(m_foePlayer1.getPlayer()) && (m_foePlayer2 == null || Boolean(m_foePlayer2.getPlayer())))
         {
            recoverPkPlayerPostion();
         }
      }
   }
}

