package UI.EquipEquipmentFuns
{
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.BuffDrive;
   import UI.Buff.Buff.BuffFunction;
   import UI.Buff.Buff.BuffVO;
   import UI.Buff.Buff.OnlyOnLineBuff.OnlyOnLineBuffVO;
   import UI.Buff.BuffData;
   import UI.Equipments.AbleEquipments.ClothesEquipmentVO;
   import UI.Equipments.AbleEquipments.ForeverFashionEquipmentVO;
   import UI.Equipments.AbleEquipments.GourdEquipmentVO;
   import UI.Equipments.AbleEquipments.NecklaceEquipmentVO;
   import UI.Equipments.AbleEquipments.PreciousEquipmentVO;
   import UI.Equipments.AbleEquipments.WeaponEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.PetSkillBookEquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.PocketEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MessageBox.MessageBoxFunction;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PetSkills.PetPassiveSkillVO;
   import UI.Skills.SkillVO;
   import UI.UIConstant.UIConstantData;
   import UI.UIInterface.IBuffEquipmentVO;
   import UI.UIInterface.IOneValueEquipmentVO;
   import UI.XMLSingle;
   import UI2.broadcast.SubmitFunction;
   import YJFY.AutomaticPet.AutomaticPetsData;
   import YJFY.EndlessMode.EndlessManage;
   import YJFY.GameEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class EquipEquipmentFunction
   {
      
      private static var _intervalID:uint;
      
      private var _equipPetSkillBook:EquipPetSkillBook;
      
      private var _equipPocket:EquipPocket;
      
      public function EquipEquipmentFunction()
      {
         super();
      }
      
      public function clear() : void
      {
         clearTimeout(_intervalID);
         if(_equipPetSkillBook)
         {
            _equipPetSkillBook.clear();
         }
         _equipPetSkillBook = null;
         if(_equipPocket)
         {
            _equipPocket.clear();
         }
         _equipPocket = null;
      }
      
      public function EquipEquipmentVOAction(param1:EquipmentVO, param2:Player, param3:IEquipEqListener, param4:String = null) : void
      {
         var oneValueEquipmentVO:EquipmentVO = param1;
         var player:Player = param2;
         var equipEqListener:IEquipEqListener = param3;
         var time:String = param4;
         if(time)
         {
            EquipEquipmentVOAction2(oneValueEquipmentVO,player,equipEqListener);
         }
         else
         {
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               EquipEquipmentVOAction2(oneValueEquipmentVO,player,equipEqListener);
            },equipEqListener.showWarning,true);
         }
      }
      
      private function EquipEquipmentVOAction2(param1:EquipmentVO, param2:Player, param3:IEquipEqListener) : void
      {
         var xml:XML;
         var length1:int;
         var n:int;
         var k:int;
         var length2:int;
         var n2:int;
         var k1:int;
         var length3:int;
         var n3:int;
         var k2:int;
         var str:String;
         var autoPetsData:AutomaticPetsData;
         var hasDuowen:Boolean;
         var i:int;
         var NezhaautoPetsData:AutomaticPetsData;
         var hasNezha:Boolean;
         var j:int;
         var BianFuAutoPetsData:AutomaticPetsData;
         var hasFengBo:Boolean;
         var x:int;
         var ShenQiAutoPetsData:AutomaticPetsData;
         var hasYuShi:Boolean;
         var y:int;
         var value:String;
         var className:String;
         var index:int;
         var valueArr:Vector.<int>;
         var length:int;
         var skillVO:SkillVO;
         var resultSkillVO:SkillVO;
         var saveinfo:SaveTaskInfo;
         var petSkillNum:int;
         var saveinfo1:SaveTaskInfo;
         var skillVo:SkillVO;
         var saveinfo2:SaveTaskInfo;
         var valueArr1:Vector.<int>;
         var saveinfo3:SaveTaskInfo;
         var saveinfo4:SaveTaskInfo;
         var saveinfo7:SaveTaskInfo;
         var num:int;
         var remainNum:int;
         var index1:int;
         var preciousEquipmentVO:PreciousEquipmentVO;
         var foreverFashionEquipmentVO:ForeverFashionEquipmentVO;
         var clothesEquipmentVO:ClothesEquipmentVO;
         var weaponEquipmentVO:WeaponEquipmentVO;
         var necklaceEquipmentVO:NecklaceEquipmentVO;
         var gourdEquipmentVO:GourdEquipmentVO;
         var ADD_EXTATTR:String;
         var oneValueEquipmentVO:EquipmentVO = param1;
         var player:Player = param2;
         var equipEqListener:IEquipEqListener = param3;
         if(oneValueEquipmentVO._antiwear == null)
         {
            return;
         }
         if(oneValueEquipmentVO == null)
         {
            return;
         }
         if(oneValueEquipmentVO is IBuffEquipmentVO)
         {
            EquipBuffEquipmentVOAction(oneValueEquipmentVO,player,equipEqListener);
            return;
         }
         if(oneValueEquipmentVO is PetSkillBookEquipmentVO)
         {
            if(_equipPetSkillBook == null)
            {
               _equipPetSkillBook = new EquipPetSkillBook();
            }
            _equipPetSkillBook.equip(oneValueEquipmentVO,player,equipEqListener);
            return;
         }
         if(oneValueEquipmentVO is PocketEquipmentVO && int((oneValueEquipmentVO as IOneValueEquipmentVO).value) == 0)
         {
            if(_equipPocket == null)
            {
               _equipPocket = new EquipPocket();
            }
            _equipPocket.EquipPotionEquipmentVOAction(oneValueEquipmentVO as PocketEquipmentVO,player,equipEqListener);
            return;
         }
         switch(oneValueEquipmentVO.className)
         {
            case "Potion_MiHunTang":
               return;
            case "Pocket_OpenPackageCellKey":
               if(!Boolean(player) || player.playerVO.packageEquipmentVOs.length >= XMLSingle.getInstance().getPackageCellNum())
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("背包格数已达最大值！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Pocket_OpenStorageCellKey":
               if(!Boolean(player) || player.playerVO.storageEquipmentVOs.length >= XMLSingle.getInstance().getStorageCellNum())
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("仓库格数已达最大值！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Pocket_OpenPageStorageCellKey":
               if(!Boolean(player) || player.playerVO.storageEquipmentVOs.length >= XMLSingle.getInstance().getStorageCellNum())
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("仓库格数已达最大值！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_PetEssentialPotion":
            case "Potion_PetTrainPotion":
            case "Potion_zhulongkaiwuyaoji":
            case "Potion_zhulongbeidongchongzhi":
            case "Potion_qiongqikaiwuyaoji":
            case "Potion_qiongqibeidongchongzhi":
            case "Potion_SmallExperiencePill_Pet":
            case "Potion_BigExperiencePill_Pet":
            case "Potion_XianPingExperiencePill_Pet":
            case "Potion_XiSuiPill":
            case "Potion_ChaoJiXiSuiPill":
            case "Potion_TalentStrengthenPill":
            case "Potion_TalentStrengthenPill_XianPing":
            case "Potion_ResetPetPassiveSkillPotion":
               if(!Boolean(player.playerVO.pet) || !Boolean(player.playerVO.pet.petEquipmentVO))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("没有装备的宠物！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               switch(oneValueEquipmentVO.className)
               {
                  case "Potion_PetTrainPotion":
                  case "Potion_ResetPetPassiveSkillPotion":
                  case "Potion_XiSuiPill":
                  case "Potion_TalentStrengthenPill":
                  case "Potion_TalentStrengthenPill_XianPing":
                     if(player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物为超进化宠物，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_ChaoJiXiSuiPill":
                  case "Potion_ChaoJiTalentStrengthenPill":
                  case "Potion_ChaoJiPetTrainPotion":
                     if(player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO == false)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物不是超进化宠物，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
               }
               xml = XMLSingle.getInstance().getPetPotionUseRestrictionXML();
               xml = xml.item.(@className == oneValueEquipmentVO.className)[0];
               if(xml && UIConstantData.ZHULONG_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) != -1 && player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO)
               {
                  if(oneValueEquipmentVO.className == "Potion_zhulongkaiwuyaoji" || oneValueEquipmentVO.className == "Potion_zhulongbeidongchongzhi")
                  {
                     xml = null;
                  }
               }
               if(xml && UIConstantData.QIONGQI_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) != -1 && player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO)
               {
                  if(oneValueEquipmentVO.className == "Potion_qiongqikaiwuyaoji" || oneValueEquipmentVO.className == "Potion_qiongqibeidongchongzhi")
                  {
                     xml = null;
                  }
               }
               if(xml)
               {
                  if(player.playerVO.pet.petEquipmentVO.level < int(xml.@level) || player.playerVO.pet.petEquipmentVO.petLevel < int(xml.@petLevel))
                  {
                     if(equipEqListener)
                     {
                        equipEqListener.showWarning("宠物阶段或等级没达到要求，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                        equipEqListener.actionAfterUnableUse();
                     }
                     return;
                  }
               }
               switch(oneValueEquipmentVO.className)
               {
                  case "Potion_PetEssentialPotion":
                     if(player.playerVO.pet.petEquipmentVO.essentialPercent == 1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物精气已满，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_PetTrainPotion":
                     length1 = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
                     n = 0;
                     k = 0;
                     while(k < length1)
                     {
                        if(player.playerVO.pet.petEquipmentVO.passiveSkillVOs[k])
                        {
                           n++;
                        }
                        k++;
                     }
                     if(n == 5)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能已满5个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_zhulongkaiwuyaoji":
                     n2 = 0;
                     if(UIConstantData.ZHULONG_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) == -1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物不是烛龙，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     length2 = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
                     k1 = 0;
                     while(k1 < length2)
                     {
                        if(player.playerVO.pet.petEquipmentVO.passiveSkillVOs[k1])
                        {
                           n2++;
                        }
                        k1++;
                     }
                     if(n2 < 5)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能不满5个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     if(n2 == 6)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能已满6个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_zhulongbeidongchongzhi":
                     n3 = 0;
                     if(UIConstantData.ZHULONG_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) == -1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物不是烛龙，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     length3 = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
                     k2 = 0;
                     while(k2 < length3)
                     {
                        if(player.playerVO.pet.petEquipmentVO.passiveSkillVOs[k2])
                        {
                           n3++;
                        }
                        k2++;
                     }
                     if(n3 < 6)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能不满6个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_qiongqikaiwuyaoji":
                     n2 = 0;
                     if(UIConstantData.QIONGQI_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) == -1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物不是穷奇，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     length2 = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
                     k1 = 0;
                     while(k1 < length2)
                     {
                        if(player.playerVO.pet.petEquipmentVO.passiveSkillVOs[k1])
                        {
                           n2++;
                        }
                        k1++;
                     }
                     if(n2 < 5)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能不满5个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     if(n2 == 6)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能已满6个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_qiongqibeidongchongzhi":
                     n3 = 0;
                     if(UIConstantData.QIONGQI_ID_ARRAY.indexOf(player.playerVO.pet.petEquipmentVO.id) == -1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物不是穷奇，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     length3 = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
                     k2 = 0;
                     while(k2 < length3)
                     {
                        if(player.playerVO.pet.petEquipmentVO.passiveSkillVOs[k2])
                        {
                           n3++;
                        }
                        k2++;
                     }
                     if(n3 < 6)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物被动技能不满6个，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_SmallExperiencePill_Pet":
                  case "Potion_BigExperiencePill_Pet":
                  case "Potion_XianPingExperiencePill_Pet":
                     if(player.playerVO.pet.petEquipmentVO.level == player.playerVO.pet.petEquipmentVO.maxLevel && player.playerVO.pet.petEquipmentVO.petLevel == player.playerVO.pet.petEquipmentVO.upgradeLevelNum && player.playerVO.pet.petEquipmentVO.experiencePercent == 1)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物已处于满级状态，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
                  case "Potion_TalentStrengthenPill":
                  case "Potion_TalentStrengthenPill_XianPing":
                     if(player.playerVO.pet.petEquipmentVO.talentVO.level == player.playerVO.pet.petEquipmentVO.talentVO.maxLevel)
                     {
                        if(equipEqListener)
                        {
                           equipEqListener.showWarning("宠物天赋等级已是最高，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        return;
                     }
                     break;
               }
               break;
            case "Potion_Shenglingwan":
               if(player.playerVO.level < XMLSingle.getInstance().getPlayerTuPoMaxLevel(player))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("等级不足，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.experiencePercent < 1)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("经验不足，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.level >= XMLSingle.getInstance().getPlayerMaxShengling(player))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("已经达到升灵的最大等级，无法再突破了！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_JingJieTuPoPill":
               if(player.playerVO.level < XMLSingle.getInstance().getPlayerMaxLevel(player))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("等级不足，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.experiencePercent < 1)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("经验不足，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.level >= XMLSingle.getInstance().getPlayerTuPoMaxLevel(player))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("已经达到突破的最大等级，无法再突破了！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_SmallExperiencePill_Player":
            case "Potion_BigExperiencePill_Player":
            case "Potion_XianPingExperiencePill_Player":
               if(player.playerVO.level >= XMLSingle.getInstance().getPlayerMaxLevel(player) && player.playerVO.experiencePercent == 1)
               {
                  str = "经验已满，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！";
                  if(player.playerVO.level < XMLSingle.getInstance().getPlayerTuPoMaxLevel(player))
                  {
                     str = "经验已满！请使用突破丹！";
                  }
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning(str,0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_LanPotion":
            case "Potion_DaMoFaYaoShui":
               if(EndlessManage.getInstance().IsEndlessMode)
               {
                  equipEqListener.showWarning("无尽闯关不能使用",0);
                  equipEqListener.actionAfterUnableUse();
                  return;
               }
               if(player.playerVO.magicPercent == 1)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("魔法值已满，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.bloodPercent == 0)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("人物已阵亡！<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>暂不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_XuePotion":
            case "Potion_DaZhiLiaoYaoShui":
               if(EndlessManage.getInstance().IsEndlessMode)
               {
                  equipEqListener.showWarning("无尽闯关不能使用",0);
                  equipEqListener.actionAfterUnableUse();
                  return;
               }
               if(player.playerVO.bloodPercent == 1)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("生命值已满，<font size=\'20\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.bloodPercent == 0)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("人物已阵亡！<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>暂不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_WuDiYaoShui":
               if(EndlessManage.getInstance().IsEndlessMode)
               {
                  equipEqListener.showWarning("无尽闯关不能使用",0);
                  equipEqListener.actionAfterUnableUse();
                  return;
               }
               break;
            case "Pocket_GoldPacket":
               if(player.playerVO.money >= XMLSingle.getInstance().maxMoney)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("金钱已经达到最大值，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + oneValueEquipmentVO.name + "</font>不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Kaiyunlingshi":
               if(!(player.playerVO.inforEquipmentVOs[3] && player.playerVO.inforEquipmentVOs[3].equipmentType == "forever_fashion"))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("身上没穿永久时装啊！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               if(player.playerVO.inforEquipmentVOs[3].level != 9)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("请先强化到九级再来使用吧！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Chongzhu1":
               if(!(player.playerVO.inforEquipmentVOs[1] && player.playerVO.inforEquipmentVOs[1].equipmentType == "clothes"))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("身上没穿衣服啊！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Chongzhu2":
               if(!(player.playerVO.inforEquipmentVOs[4] && player.playerVO.inforEquipmentVOs[4].equipmentType == "weapon"))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("身上没装备武器啊！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Chongzhu3":
               if(!(player.playerVO.inforEquipmentVOs[5] && player.playerVO.inforEquipmentVOs[5].equipmentType == "necklace"))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("身上没装备项链啊！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Chongzhu4":
               if(!(player.playerVO.inforEquipmentVOs[2] && player.playerVO.inforEquipmentVOs[2].equipmentType == "gourd"))
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("身上没装备葫芦啊！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_dabaibianshenka":
               autoPetsData = GamingUI.getInstance().getAutomaticPetsData();
               hasDuowen = false;
               i = 0;
               while(i < autoPetsData.getAutomaticPetVONum())
               {
                  if(autoPetsData.getAutomaticPetVOByIndex(i).getRealID() == "Duowen3")
                  {
                     hasDuowen = true;
                     break;
                  }
                  i++;
               }
               if(!hasDuowen)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("你还没有多闻天王妖将！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_meiduibianshenka":
               NezhaautoPetsData = GamingUI.getInstance().getAutomaticPetsData();
               hasNezha = false;
               j = 0;
               while(j < NezhaautoPetsData.getAutomaticPetVONum())
               {
                  if(NezhaautoPetsData.getAutomaticPetVOByIndex(j).getRealID() == "Nezha3")
                  {
                     hasNezha = true;
                     break;
                  }
                  j++;
               }
               if(!hasNezha)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("你还没有哪吒妖将！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Bianfuxia":
               BianFuAutoPetsData = GamingUI.getInstance().getAutomaticPetsData();
               hasFengBo = false;
               x = 0;
               while(x < BianFuAutoPetsData.getAllAutomaticPetVONum())
               {
                  if(BianFuAutoPetsData.getAllAutomaticPetVOByIndex(x).getRealID() == "fengbo")
                  {
                     hasFengBo = true;
                     break;
                  }
                  x++;
               }
               if(!hasFengBo)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("你还没有风伯妖将！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Shenqinvxia":
               ShenQiAutoPetsData = GamingUI.getInstance().getAutomaticPetsData();
               hasYuShi = false;
               y = 0;
               while(y < ShenQiAutoPetsData.getAllAutomaticPetVONum())
               {
                  if(ShenQiAutoPetsData.getAllAutomaticPetVOByIndex(y).getRealID() == "yushi")
                  {
                     hasYuShi = true;
                     break;
                  }
                  y++;
               }
               if(!hasYuShi)
               {
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("你还没有雨师妖将！不能使用！",0);
                     equipEqListener.actionAfterUnableUse();
                  }
                  return;
               }
               break;
            case "Potion_Feishengdan_Player":
               if(player.playerVO.level >= 60)
               {
                  equipEqListener.showWarning("只限60以下等级使用",0);
                  equipEqListener.actionAfterUnableUse();
                  return;
               }
               break;
            case "Potion_Feishengdan1_Player":
               if(player.playerVO.level >= 70)
               {
                  equipEqListener.showWarning("只限70以下等级使用",0);
                  equipEqListener.actionAfterUnableUse();
                  return;
               }
               break;
         }
         value = (oneValueEquipmentVO as IOneValueEquipmentVO).value;
         className = oneValueEquipmentVO.className;
         index = int(player.playerVO.packageEquipmentVOs.indexOf(oneValueEquipmentVO));
         if(index == -1)
         {
            if(equipEqListener)
            {
               equipEqListener.showWarning("物品已不存在， 使用失败！",0);
               equipEqListener.actionAfterUnableUse();
            }
            return;
         }
         if(oneValueEquipmentVO is StackEquipmentVO)
         {
            (oneValueEquipmentVO as StackEquipmentVO).num -= 1;
            if((oneValueEquipmentVO as StackEquipmentVO).num == 0)
            {
               player.playerVO.packageEquipmentVOs[index] = null;
               oneValueEquipmentVO.clear();
               oneValueEquipmentVO = null;
            }
         }
         else
         {
            player.playerVO.packageEquipmentVOs[index] = null;
            oneValueEquipmentVO.clear();
            oneValueEquipmentVO = null;
         }
         GamingUI.getInstance().refresh(2);
         switch(className)
         {
            case "Potion_WuDiYaoShui":
               player.playerVO.isInvincible = true;
               player.playerVO.endInvincibleTime = GamingUI.getInstance().getEnterFrameTime().getGameTimeForThisInit() + uint(value);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("您已处于无敌状态",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_SmallExperiencePill_Player":
            case "Potion_BigExperiencePill_Player":
            case "Potion_XianPingExperiencePill_Player":
               GamingUI.getInstance().mouseChildren = false;
               GamingUI.getInstance().mouseEnabled = false;
               GamingUI.getInstance().manBan.text.text = "请耐心等待。经验正在吸收中...";
               GamingUI.getInstance().manBan.wheel.visible = false;
               _intervalID = setTimeout(function():void
               {
                  clearTimeout(_intervalID);
                  MyFunction.getInstance().addPlayerExperience(player,int(value));
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
                  GamingUI.getInstance().manBan.wheel.visible = true;
                  GamingUI.getInstance().refresh(1);
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("您已获得了" + value + "点的经验值！",0);
                     equipEqListener.actionAfterUse();
                  }
               },100);
               break;
            case "Potion_SmallExperiencePill_Pet":
            case "Potion_BigExperiencePill_Pet":
            case "Potion_XianPingExperiencePill_Pet":
               GamingUI.getInstance().mouseChildren = false;
               GamingUI.getInstance().mouseEnabled = false;
               GamingUI.getInstance().manBan.text.text = "请耐心等待。经验正在吸收中...";
               GamingUI.getInstance().manBan.wheel.visible = false;
               _intervalID = setTimeout(function():void
               {
                  clearTimeout(_intervalID);
                  MyFunction.getInstance().addPetExperience(player,int(value));
                  GamingUI.getInstance().mouseChildren = true;
                  GamingUI.getInstance().mouseEnabled = true;
                  GamingUI.getInstance().manBan.wheel.visible = true;
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("您的已装备宠物已获得了" + value + "点的经验值！",0);
                     equipEqListener.actionAfterUse();
                  }
               },100);
               break;
            case "Potion_LanPotion":
            case "Potion_DaMoFaYaoShui":
               player.playerVO.magicPercent += int(value) / player.playerVO.maxMagic;
               GamingUI.getInstance().refresh(0x80 | 0x40 | 1);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("您已获得了" + value + "点的魔法值！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_XuePotion":
            case "Potion_DaZhiLiaoYaoShui":
               player.playerVO.bloodPercent += int(value) / player.playerVO.bloodVolume;
               GamingUI.getInstance().refresh(0x80 | 1);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("您已获得" + value + "点的血量！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_PetEssentialPotion":
               player.playerVO.pet.petEquipmentVO.essentialPercent += int(value) / player.playerVO.pet.petEquipmentVO.essentialVolume;
               MyFunction.getInstance().refreshPet(player.playerVO.pet.petEquipmentVO);
               GamingUI.getInstance().refresh(0x10 | 0x0200 | 0x40 | 1 | 8 | 0x80);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("宠物获得了" + value + "点的精气！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_PetTrainPotion":
            case "Potion_ChaoJiPetTrainPotion":
            case "Potion_zhulongkaiwuyaoji":
            case "Potion_qiongqikaiwuyaoji":
               valueArr = MyFunction.getInstance().excreteString(value);
               length = 0;
               for each(skillVO in player.playerVO.pet.petEquipmentVO.passiveSkillVOs)
               {
                  if(skillVO)
                  {
                     length++;
                  }
               }
               resultSkillVO = XMLSingle.getInstance().randomGetOnePassiveSkillVO(player.playerVO.pet.petEquipmentVO,2,valueArr[length] / 100);
               if(resultSkillVO)
               {
                  if(className == "Potion_ChaoJiPetTrainPotion")
                  {
                     changePetToPetLevelOne(player.playerVO.pet);
                  }
                  else if(className == "Potion_PetTrainPotion")
                  {
                     changePetToInitalState(player.playerVO.pet);
                  }
                  else if(player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO)
                  {
                     changePetToPetLevelOne(player.playerVO.pet);
                  }
                  else
                  {
                     changePetToInitalState(player.playerVO.pet);
                  }
                  GamingUI.getInstance().refresh(0x10 | 0x0200 | 1 | 0x80 | 8 | 0x40);
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("宠物获得了" + resultSkillVO.name + "技能！",0);
                     equipEqListener.actionAfterUse();
                  }
               }
               else if(equipEqListener)
               {
                  equipEqListener.showWarning("训练失败",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo = new SaveTaskInfo();
               saveinfo.type = "4399";
               saveinfo.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo);
               MyFunction2.saveGame2();
               break;
            case "Potion_ResetPetPassiveSkillPotion":
               petSkillNum = int(player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
               if(petSkillNum >= 5)
               {
                  petSkillNum = 5;
               }
               player.playerVO.pet.petEquipmentVO.passiveSkillVOs = XMLSingle.getInstance().randomFixedNumPassiveSkillVO(player.playerVO.pet.petEquipmentVO,player.playerVO.pet.petEquipmentVO.passiveSkillVOs.length);
               changePetToInitalState(player.playerVO.pet);
               GamingUI.getInstance().refresh(0x10 | 0x0200 | 1 | 0x80 | 8 | 0x40);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("宠物重置被动技能成功！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo1 = new SaveTaskInfo();
               saveinfo1.type = "4399";
               saveinfo1.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo1);
               MyFunction2.saveGame2();
               break;
            case "Potion_zhulongbeidongchongzhi":
            case "Potion_qiongqibeidongchongzhi":
               skillVo = XMLSingle.getInstance().randomResetOnePassiveSkillVO(player.playerVO.pet.petEquipmentVO,5);
               if(SkillVO)
               {
                  if(player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO)
                  {
                     changePetToPetLevelOne(player.playerVO.pet);
                  }
                  else
                  {
                     changePetToInitalState(player.playerVO.pet);
                  }
               }
               GamingUI.getInstance().refresh(0x10 | 0x0200 | 1 | 0x80 | 8 | 0x40);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("宠物重置被动技能成功！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo1 = new SaveTaskInfo();
               saveinfo1.type = "4399";
               saveinfo1.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo1);
               MyFunction2.saveGame2();
               break;
            case "Potion_XiSuiPill":
            case "Potion_ChaoJiXiSuiPill":
               player.playerVO.pet.petEquipmentVO.talentVO = XMLSingle.getInstance().randomGetTalentVO(3,player.playerVO.pet.petEquipmentVO.talentVO.level);
               if(className == "Potion_ChaoJiXiSuiPill")
               {
                  changePetToPetLevelOne(player.playerVO.pet);
               }
               else
               {
                  changePetToInitalState(player.playerVO.pet);
               }
               GamingUI.getInstance().refresh(0x10 | 0x0200 | 1 | 0x80 | 8 | 0x40);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("宠物脱胎换骨！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo2 = new SaveTaskInfo();
               saveinfo2.type = "4399";
               saveinfo2.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo2);
               MyFunction2.saveGame2();
               break;
            case "Potion_TalentStrengthenPill":
            case "Potion_TalentStrengthenPill_XianPing":
            case "Potion_ChaoJiTalentStrengthenPill":
               valueArr1 = MyFunction.getInstance().excreteString(value);
               if(player.playerVO.pet.petEquipmentVO && XMLSingle.getInstance().randomUpgradeTalent(player.playerVO.pet.petEquipmentVO,2,valueArr1[player.playerVO.pet.petEquipmentVO.talentVO.level - 1] / 100))
               {
                  if(className == "Potion_ChaoJiTalentStrengthenPill")
                  {
                     changePetToPetLevelOne(player.playerVO.pet);
                  }
                  else
                  {
                     changePetToInitalState(player.playerVO.pet);
                  }
                  GamingUI.getInstance().refresh(0x10 | 0x0200 | 1 | 0x80 | 8 | 0x40);
                  if(equipEqListener)
                  {
                     equipEqListener.showWarning("强化成功！",0);
                     equipEqListener.actionAfterUse();
                  }
               }
               else if(equipEqListener)
               {
                  equipEqListener.showWarning("强化失败！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo3 = new SaveTaskInfo();
               saveinfo3.type = "4399";
               saveinfo3.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo3);
               MyFunction2.saveGame2();
               break;
            case "Pocket_GoldPacket":
               player.playerVO.money += int(value);
               GamingUI.getInstance().refresh(32);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("您已获得了" + value + "点金钱！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_Shenglingwan":
               player.playerVO.level += 1;
               XMLSingle.setPlayerVO(player.playerVO,1);
               player.playerVO.experiencePercent = 0;
               MyFunction.getInstance().refreshPlayer(player,2);
               player.playerVO.bloodPercent = 1;
               player.playerVO.magicPercent = 1;
               GamingUI.getInstance().refresh(1 | 0x80 | 8 | 0x40);
               if(equipEqListener)
               {
                  SubmitFunction.getInstance().setData5(3,player.playerVO.level,getRoleName(player.playerVO.playerType));
                  equipEqListener.showWarning("突破成功！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo4 = new SaveTaskInfo();
               saveinfo4.type = "4399";
               saveinfo4.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo4);
               MyFunction2.saveGame2();
               break;
            case "Potion_JingJieTuPoPill":
               player.playerVO.level += 1;
               XMLSingle.setPlayerVO(player.playerVO,1);
               player.playerVO.experiencePercent = 0;
               MyFunction.getInstance().refreshPlayer(player,2);
               player.playerVO.bloodPercent = 1;
               player.playerVO.magicPercent = 1;
               GamingUI.getInstance().refresh(1 | 0x80 | 8 | 0x40);
               if(equipEqListener)
               {
                  SubmitFunction.getInstance().setData5(3,player.playerVO.level,getRoleName(player.playerVO.playerType));
                  equipEqListener.showWarning("突破成功！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo7 = new SaveTaskInfo();
               saveinfo7.type = "4399";
               saveinfo7.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo7);
               MyFunction2.saveGame2();
               break;
            case "Pocket_OpenPackageCellKey":
               player.playerVO.packageEquipmentVOs.push(null);
               GamingUI.getInstance().refresh(2);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("成功开启一个背包格！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Pocket_OpenAllCellKey":
               num = XMLSingle.getInstance().getPackageCellNum() - player.playerVO.packageEquipmentVOs.length;
               i = 0;
               while(i < num)
               {
                  player.playerVO.packageEquipmentVOs.push(null);
                  i++;
               }
               GamingUI.getInstance().refresh(2);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("成功开启所有背包格！",0);
                  equipEqListener.actionAfterUse();
               }
               saveinfo1 = new SaveTaskInfo();
               saveinfo1.type = "4399";
               saveinfo1.isHaveData = false;
               SaveTaskList.getInstance().addData(saveinfo1);
               MyFunction2.saveGame();
               break;
            case "Pocket_OpenStorageCellKey":
               player.playerVO.storageEquipmentVOs.push(null);
               GamingUI.getInstance().refresh(4);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("成功开启一个仓库格！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Pocket_OpenPageStorageCellKey":
               remainNum = XMLSingle.getInstance().getStorageCellNum() - player.playerVO.storageEquipmentVOs.length;
               index1 = 0;
               if(remainNum >= 36)
               {
                  remainNum = 36;
               }
               index1 = 0;
               while(index1 < remainNum)
               {
                  player.playerVO.storageEquipmentVOs.push(null);
                  index1++;
               }
               GamingUI.getInstance().refresh(4);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("成功开启" + String(remainNum) + "个仓库格！",0);
                  equipEqListener.actionAfterUse();
               }
               break;
            case "Potion_lingfuxilian":
               if(player.playerVO.inforEquipmentVOs[0])
               {
                  preciousEquipmentVO = player.playerVO.inforEquipmentVOs[0] as PreciousEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     var _loc18_:XMLList = null;
                     var _loc12_:int = 0;
                     var _loc15_:int = 0;
                     var _loc16_:int = 0;
                     var _loc5_:int = 0;
                     var _loc6_:int = 0;
                     var _loc21_:XMLList = null;
                     var _loc17_:int = 0;
                     var _loc20_:* = 0;
                     var _loc14_:* = undefined;
                     var _loc1_:int = 0;
                     var _loc10_:int = 0;
                     var _loc3_:Boolean = false;
                     var _loc9_:Number = NaN;
                     var _loc19_:Number = NaN;
                     var _loc4_:Number = NaN;
                     var _loc7_:int = 0;
                     var _loc2_:SaveTaskInfo = null;
                     var _loc8_:String = null;
                     var _loc11_:String = null;
                     preciousEquipmentVO.sAttrName.length = 0;
                     preciousEquipmentVO.sAttrValue.length = 0;
                     preciousEquipmentVO.sAvgValue.length = 0;
                     preciousEquipmentVO.sMaxValue.length = 0;
                     preciousEquipmentVO.sMinValue.length = 0;
                     preciousEquipmentVO.sTotalWeight.length = 0;
                     preciousEquipmentVO.sWeight.length = 0;
                     var _loc13_:XML = XMLSingle.getInstance().equipmentXML.item.(@id == preciousEquipmentVO.id)[0];
                     if(_loc13_ == null)
                     {
                        trace("没有id为",preciousEquipmentVO.id,"的配置数据");
                     }
                     else
                     {
                        _loc6_ = 0;
                        _loc6_ = 0;
                        if(_loc13_.hasOwnProperty("saddWeight"))
                        {
                           _loc18_ = _loc13_.saddWeight;
                           _loc12_ = int(_loc18_[0].@totalweight);
                           _loc5_ = 1 + Math.random() * (_loc12_ - 1 + 1);
                           i = 0;
                           while(i < _loc18_.length())
                           {
                              if(_loc5_ >= int(_loc18_[i].@weightmin) && _loc5_ <= int(_loc18_[i].@weightmax))
                              {
                                 _loc6_ = int(_loc18_[i].@value);
                                 break;
                              }
                              i++;
                           }
                        }
                        if(_loc13_.hasOwnProperty("sAddAttr"))
                        {
                           _loc21_ = _loc13_.sAddAttr;
                           _loc17_ = int(_loc21_.length());
                           _loc20_ = _loc6_;
                           _loc14_ = new Vector.<int>();
                           while(_loc14_.length < _loc20_)
                           {
                              if(_loc1_ >= _loc17_)
                              {
                                 _loc1_ = 0;
                              }
                              _loc10_ = 1 + Math.random() * (Number(_loc21_[_loc1_].@totalweight) - 1 + 1);
                              if(_loc10_ <= Number(_loc21_[_loc1_].@weight))
                              {
                                 _loc3_ = false;
                                 _loc7_ = 0;
                                 while(_loc7_ < _loc14_.length)
                                 {
                                    if(_loc14_[_loc7_] == _loc1_)
                                    {
                                       _loc3_ = true;
                                    }
                                    _loc7_++;
                                 }
                                 if(_loc3_ == false)
                                 {
                                    _loc14_.push(_loc1_);
                                 }
                              }
                              _loc1_++;
                           }
                           i = 0;
                           while(i < _loc20_)
                           {
                              preciousEquipmentVO.sAttrName.push(String(_loc21_[_loc14_[i]].@addAttName));
                              preciousEquipmentVO.sAvgValue.push(int(_loc21_[_loc14_[i]].@avgValue));
                              preciousEquipmentVO.sMaxValue.push(Number(_loc21_[_loc14_[i]].@maxvalue));
                              preciousEquipmentVO.sMinValue.push(Number(_loc21_[_loc14_[i]].@minvalue));
                              preciousEquipmentVO.sWeight.push(Number(_loc21_[_loc14_[i]].@weight));
                              preciousEquipmentVO.sTotalWeight.push(Number(_loc21_[_loc14_[i]].@totalweight));
                              _loc19_ = Number(_loc21_[_loc14_[i]].@minvalue) * 100;
                              _loc9_ = Number(_loc21_[_loc14_[i]].@maxvalue) * 100;
                              _loc4_ = _loc19_ + Math.random() * (_loc9_ - _loc19_ + 1);
                              preciousEquipmentVO.sAttrValue.push(_loc4_ / 100);
                              i++;
                           }
                           ClearUtil.clearObject(_loc14_);
                           _loc14_ = null;
                           GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                           _loc2_ = new SaveTaskInfo();
                           _loc2_.type = "4399";
                           _loc2_.isHaveData = false;
                           SaveTaskList.getInstance().addData(_loc2_);
                           MyFunction2.saveGame2();
                           _loc8_ = MessageBoxFunction.getInstance().toHTMLText("恭喜获得了额外属性",16);
                           i = 0;
                           while(i < preciousEquipmentVO.sAttrName.length)
                           {
                              if(preciousEquipmentVO.sAttrName[i] == "doubleexpgold")
                              {
                                 _loc11_ = "<br>获得的经验值和金钱可翻" + String(int(preciousEquipmentVO.sAttrValue[i])) + "倍" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "increaseAttack")
                              {
                                 _loc11_ = "<br>每" + String(int(preciousEquipmentVO.sAvgValue[i])) + "个魔法点增加" + String(preciousEquipmentVO.sAttrValue[i].toFixed(2)) + "个攻击点" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "increaseShanbi")
                              {
                                 _loc11_ = "<br>每" + String(int(preciousEquipmentVO.sAvgValue[i])) + "个防御点增加" + String(preciousEquipmentVO.sAttrValue[i].toFixed(2)) + "个闪避点" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "increaseShanbi")
                              {
                                 _loc11_ = "<br>每" + String(int(preciousEquipmentVO.sAvgValue[i])) + "个防御点增加" + String(preciousEquipmentVO.sAttrValue[i].toFixed(2)) + "个闪避点" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "dincreasehp")
                              {
                                 _loc11_ = "<br>每发动普攻或者技能时降低百分之" + String(int(preciousEquipmentVO.sAttrValue[i])) + "的血量" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "dincreaseDef")
                              {
                                 _loc11_ = "<br>防御降低" + String(int(preciousEquipmentVO.sAttrValue[i])) + "%<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "increaseBaoji")
                              {
                                 _loc11_ = "<br>每" + String(int(preciousEquipmentVO.sAvgValue[i])) + "个攻击点增加" + String(preciousEquipmentVO.sAttrValue[i].toFixed(2)) + "个暴击点" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "increaseMingzhong")
                              {
                                 _loc11_ = "<br>血量每下降" + String(int(preciousEquipmentVO.sAvgValue[i])) + "%增加" + String(preciousEquipmentVO.sAttrValue[i].toFixed(4)) + "个命中点" + "<br>";
                              }
                              else if(preciousEquipmentVO.sAttrName[i] == "changerenpin")
                              {
                                 _loc11_ = "<br>将人品值全部转换为防御力<br>";
                              }
                              _loc8_ += MessageBoxFunction.getInstance().toHTMLText(_loc11_,16);
                              i++;
                           }
                           equipEqListener.showWarning(_loc8_,0);
                           equipEqListener.actionAfterUnableUse();
                        }
                        else
                        {
                           trace("没有sAddAttr的配置");
                        }
                     }
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Kaiyunlingshi":
               if(player.playerVO.inforEquipmentVOs[3])
               {
                  foreverFashionEquipmentVO = player.playerVO.inforEquipmentVOs[3] as ForeverFashionEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     var _loc4_:* = undefined;
                     var _loc14_:* = undefined;
                     var _loc8_:int = 0;
                     var _loc1_:* = undefined;
                     var _loc7_:* = undefined;
                     var _loc10_:int = 0;
                     var _loc13_:* = undefined;
                     var _loc17_:int = 0;
                     var _loc11_:Array = null;
                     _loc4_ = new Vector.<String>();
                     _loc14_ = new Vector.<Number>();
                     var _loc5_:int = int(foreverFashionEquipmentVO.addPlayerAttributes.length);
                     var _loc2_:Boolean = false;
                     _loc8_ = 0;
                     for(; _loc8_ < _loc5_; _loc8_++)
                     {
                        if(_loc2_ == false)
                        {
                           if(foreverFashionEquipmentVO.addPlayerAttributes[_loc8_] == foreverFashionEquipmentVO.addExtraAttrEx && foreverFashionEquipmentVO.addPlayerAttributeValues[_loc8_] == foreverFashionEquipmentVO.addExtraAttrValue)
                           {
                              _loc2_ = true;
                              continue;
                           }
                        }
                        _loc4_.push(foreverFashionEquipmentVO.addPlayerAttributes[_loc8_]);
                        _loc14_.push(foreverFashionEquipmentVO.addPlayerAttributeValues[_loc8_]);
                     }
                     if(foreverFashionEquipmentVO.id == 11500915)
                     {
                        if(_loc4_.length >= 3 && _loc4_.length >= _loc5_)
                        {
                           _loc4_.pop();
                           _loc14_.pop();
                        }
                     }
                     else if(_loc4_.length >= 2 && _loc4_.length >= _loc5_)
                     {
                        _loc4_.pop();
                        _loc14_.pop();
                     }
                     var _loc12_:XMLList = XMLSingle.getInstance().dataXML.ForeverFashionAddAttr;
                     var _loc16_:XMLList = _loc12_.value;
                     var _loc15_:int = int(_loc16_.length());
                     _loc1_ = new Vector.<String>();
                     _loc7_ = new Vector.<Number>();
                     _loc10_ = 0;
                     while(_loc10_ < _loc15_)
                     {
                        _loc1_.push(String(_loc16_[_loc10_].@addPlayerAttribute));
                        _loc13_ = MyFunction.getInstance().excreteStringToNumber(String(_loc16_[_loc10_].@addPlayerAttributeValue));
                        _loc17_ = Math.floor(Math.random() * _loc13_.length);
                        _loc7_.push(_loc13_[_loc17_]);
                        _loc10_++;
                     }
                     var _loc6_:int = Math.floor(Math.random() * _loc15_);
                     foreverFashionEquipmentVO.addExtraAttrEx = _loc1_[_loc6_];
                     foreverFashionEquipmentVO.addExtraAttrValue = _loc7_[_loc6_];
                     _loc4_.push(foreverFashionEquipmentVO.addExtraAttrEx);
                     _loc14_.push(foreverFashionEquipmentVO.addExtraAttrValue);
                     foreverFashionEquipmentVO.addPlayerAttributes = _loc4_;
                     foreverFashionEquipmentVO.addPlayerAttributeValues = _loc14_;
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     MyFunction.getInstance().refreshPlayer(player,1);
                     var _loc3_:SaveTaskInfo = new SaveTaskInfo();
                     _loc3_.type = "4399";
                     _loc3_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc3_);
                     MyFunction2.saveGame2();
                     _loc11_ = MessageBoxEngine.getInstance().returnAttributeNameByAttribute(foreverFashionEquipmentVO.addExtraAttrEx,foreverFashionEquipmentVO.addExtraAttrValue);
                     var _loc9_:String = "恭喜获得了额外属性<br>" + MessageBoxFunction.getInstance().toHTMLText(_loc11_[0] + "：",16) + MessageBoxFunction.getInstance().toHTMLText(_loc11_[1],16);
                     equipEqListener.showWarning(_loc9_,0);
                     equipEqListener.actionAfterUnableUse();
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Chongzhu1":
               if(player.playerVO.inforEquipmentVOs[1])
               {
                  clothesEquipmentVO = player.playerVO.inforEquipmentVOs[1] as ClothesEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     if(clothesEquipmentVO.level == 0)
                     {
                        clothesEquipmentVO.defence = clothesEquipmentVO.minDefence + Math.random() * (clothesEquipmentVO.maxDefence - clothesEquipmentVO.minDefence + 1);
                        clothesEquipmentVO.riot = clothesEquipmentVO.maxRiot ? clothesEquipmentVO.minRiot + Math.round(Math.random() * (clothesEquipmentVO.maxRiot - clothesEquipmentVO.minRiot) * 100) / 100 : 0;
                     }
                     clothesEquipmentVO.rengPin = clothesEquipmentVO.minRenPin + Math.random() * (clothesEquipmentVO.maxRenPin - clothesEquipmentVO.minRenPin + 1);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     MyFunction.getInstance().refreshPlayer(player,1);
                     var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                     _loc1_.type = "4399";
                     _loc1_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc1_);
                     MyFunction2.saveGame2();
                     var _loc2_:String = "恭喜重铸成功";
                     equipEqListener.showWarning(_loc2_,0);
                     equipEqListener.actionAfterUnableUse();
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Chongzhu2":
               if(player.playerVO.inforEquipmentVOs[4])
               {
                  weaponEquipmentVO = player.playerVO.inforEquipmentVOs[4] as WeaponEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     if(weaponEquipmentVO.level == 0)
                     {
                        weaponEquipmentVO.attack = weaponEquipmentVO.minAttack + Math.random() * (weaponEquipmentVO.maxAttack - weaponEquipmentVO.minAttack + 1);
                     }
                     weaponEquipmentVO.rengPin = weaponEquipmentVO.minRenPin + Math.random() * (weaponEquipmentVO.maxRenPin - weaponEquipmentVO.minRenPin + 1);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     MyFunction.getInstance().refreshPlayer(player,1);
                     var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                     _loc1_.type = "4399";
                     _loc1_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc1_);
                     MyFunction2.saveGame2();
                     var _loc2_:String = "恭喜重铸成功";
                     equipEqListener.showWarning(_loc2_,0);
                     equipEqListener.actionAfterUnableUse();
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Chongzhu3":
               if(player.playerVO.inforEquipmentVOs[5])
               {
                  necklaceEquipmentVO = player.playerVO.inforEquipmentVOs[5] as NecklaceEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     if(necklaceEquipmentVO.level == 0)
                     {
                        necklaceEquipmentVO.criticalRate = necklaceEquipmentVO.minCriticalRate + Math.random() * (necklaceEquipmentVO.maxCriticalRate - necklaceEquipmentVO.minCriticalRate + 1);
                     }
                     necklaceEquipmentVO.rengPin = necklaceEquipmentVO.minRenPin + Math.random() * (necklaceEquipmentVO.maxRenPin - necklaceEquipmentVO.minRenPin + 1);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     MyFunction.getInstance().refreshPlayer(player,1);
                     var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                     _loc1_.type = "4399";
                     _loc1_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc1_);
                     MyFunction2.saveGame2();
                     var _loc2_:String = "恭喜重铸成功";
                     equipEqListener.showWarning(_loc2_,0);
                     equipEqListener.actionAfterUnableUse();
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Chongzhu4":
               if(player.playerVO.inforEquipmentVOs[2])
               {
                  gourdEquipmentVO = player.playerVO.inforEquipmentVOs[2] as GourdEquipmentVO;
                  MyFunction2.getUserStateIsRightFunction(function():void
                  {
                     if(gourdEquipmentVO.level == 0)
                     {
                        gourdEquipmentVO.maxMagic = gourdEquipmentVO.minMaxMagic + Math.random() * (gourdEquipmentVO.maxMaxMagic - gourdEquipmentVO.minMaxMagic + 1);
                     }
                     gourdEquipmentVO.rengPin = gourdEquipmentVO.minRenPin + Math.random() * (gourdEquipmentVO.maxRenPin - gourdEquipmentVO.minRenPin + 1);
                     GamingUI.getInstance().refresh(2 | 1 | 0x0200);
                     MyFunction.getInstance().refreshPlayer(player,1);
                     var _loc1_:SaveTaskInfo = new SaveTaskInfo();
                     _loc1_.type = "4399";
                     _loc1_.isHaveData = false;
                     SaveTaskList.getInstance().addData(_loc1_);
                     MyFunction2.saveGame2();
                     var _loc2_:String = "恭喜重铸成功";
                     equipEqListener.showWarning(_loc2_,0);
                     equipEqListener.actionAfterUnableUse();
                  },function(param1:String, param2:int):void
                  {
                     equipEqListener.showWarning("登陆异常，请重新登陆！！！",0);
                     equipEqListener.actionAfterUnableUse();
                  },true);
               }
               break;
            case "Potion_Bianfuxia":
            case "Potion_Shenqinvxia":
            case "Potion_dabaibianshenka":
               GamingUI.getInstance().getAutomaticPetsData().activitySkin(value);
               ADD_EXTATTR = "恭喜使用成功";
               equipEqListener.showWarning(ADD_EXTATTR,0);
               equipEqListener.actionAfterUnableUse();
               break;
            case "Potion_meiduibianshenka":
               GamingUI.getInstance().getAutomaticPetsData().activitySkin(value);
               equipEqListener.showWarning("恭喜使用成功",0);
               equipEqListener.actionAfterUnableUse();
               break;
            case "Potion_Feishengdan_Player":
               MyFunction.getInstance().addPlayerToLevel(player,int(value));
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
               GamingUI.getInstance().manBan.wheel.visible = true;
               GamingUI.getInstance().refresh(1);
               if(equipEqListener)
               {
                  equipEqListener.showWarning("使用成功！",0);
                  equipEqListener.actionAfterUse();
               }
               GameEvent.eventDispacher.dispatchEvent(new GameEvent("rundetector"));
               break;
            case "Potion_Feishengdan1_Player":
               MyFunction.getInstance().addPlayerToLevel(player,int(value));
               GamingUI.getInstance().mouseChildren = true;
               GamingUI.getInstance().mouseEnabled = true;
               GamingUI.getInstance().manBan.wheel.visible = true;
               GamingUI.getInstance().refresh(1);
               if(equipEqListener)
               {
                  SubmitFunction.getInstance().setData5(3,player.playerVO.level,getRoleName(player.playerVO.playerType));
                  equipEqListener.showWarning("使用成功！",0);
                  equipEqListener.actionAfterUse();
               }
               GameEvent.eventDispacher.dispatchEvent(new GameEvent("rundetector"));
               break;
            default:
               throw new Error("不存在的药物品!!");
         }
      }
      
      private function getRoleName(param1:String) : String
      {
         if(param1 == "SunWuKong")
         {
            return "孙悟空";
         }
         if(param1 == "BaiLongMa")
         {
            return "白龙马";
         }
         if(param1 == "ErLangShen")
         {
            return "二郎神";
         }
         if(param1 == "ChangE")
         {
            return "嫦娥";
         }
         if(param1 == "Fox")
         {
            return "灵狐";
         }
         if(param1 == "TieShan")
         {
            return "铁扇公主";
         }
         if(param1 == "ZiXia")
         {
            return "紫霞仙子";
         }
         return "";
      }
      
      public function EquipBuffEquipmentVOAction(param1:EquipmentVO, param2:Player, param3:IEquipEqListener) : void
      {
         var equipmentVO:EquipmentVO;
         var index:int;
         var buffEquipmentVO:EquipmentVO = param1;
         var player:Player = param2;
         var equipEqListener:IEquipEqListener = param3;
         if(buffEquipmentVO.id == 11400000 && BuffData.getInstance().isHaveTheFieldBuff(XMLSingle.getInstance().buffXML.item.(@id == "10004")[0].@field,"buffDrives"))
         {
            equipEqListener.showWarning("五倍经验正在执行中，不能使用",0);
            equipEqListener.actionAfterUnableUse();
            return;
         }
         index = int(player.playerVO.packageEquipmentVOs.indexOf(buffEquipmentVO));
         if(index == -1)
         {
            if(equipEqListener)
            {
               equipEqListener.showWarning("物品已不存在， 使用失败！",0);
               equipEqListener.actionAfterUnableUse();
            }
            return;
         }
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc6_:BuffVO = null;
            var _loc2_:int = 0;
            var _loc3_:String = null;
            var _loc9_:XML = XMLSingle.getInstance().buffXML;
            var _loc5_:String = (buffEquipmentVO as IBuffEquipmentVO).value;
            (buffEquipmentVO as StackEquipmentVO).num -= 1;
            if((buffEquipmentVO as StackEquipmentVO).num == 0)
            {
               player.playerVO.packageEquipmentVOs[index] = null;
               buffEquipmentVO.clear();
               buffEquipmentVO = null;
            }
            GamingUI.getInstance().refresh(2);
            var _loc8_:Vector.<int> = MyFunction.getInstance().excreteString(_loc5_);
            var _loc7_:int = 0;
            var _loc4_:int = int(_loc8_.length);
            _loc7_ = 0;
            while(_loc7_ < _loc4_)
            {
               _loc6_ = XMLSingle.getBuff(_loc8_[_loc7_],_loc9_);
               _loc2_ = int(_loc9_.item.(@id == _loc8_[_loc7_])[0].@time);
               switch(_loc6_.type)
               {
                  case "allTimeBuff":
                     (_loc6_ as AllTimeBuffVO).startDate = param1;
                     (_loc6_ as AllTimeBuffVO).totalTime = _loc2_;
                     _loc6_.remainTime = (_loc6_ as AllTimeBuffVO).totalTime * 3600;
                     break;
                  case "onlyOnLineBuff":
                     (_loc6_ as OnlyOnLineBuffVO).saveRemainTime = _loc2_ * 3600;
                     (_loc6_ as OnlyOnLineBuffVO).remainTime = (_loc6_ as OnlyOnLineBuffVO).saveRemainTime;
                     break;
                  case "noTimeBuff":
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               _loc3_ = "buffDrives";
               switch(String(_loc6_.xml.@target))
               {
                  case "onePlayer":
                     _loc6_.player = player;
                     _loc3_ += "_" + player.playerVO.playerID;
                  case "twoPlayer":
                     break;
                  default:
                     throw new Error("buff配置错误！");
               }
               BuffFunction.addBuffVOToBuffs(new BuffDrive(_loc6_),BuffData.getInstance()[_loc3_]);
               if(equipEqListener)
               {
                  equipEqListener.actionAfterUse();
               }
               _loc7_++;
            }
         },equipEqListener.showWarning,false);
      }
      
      private function changePetToPetLevelOne(param1:Pet) : void
      {
         var _loc2_:int = param1.petEquipmentVO.essentialPercent * param1.petEquipmentVO.essentialVolume;
         param1.petEquipmentVO.petLevel = 1;
         XMLSingle.getInstance().setPetData(param1.petEquipmentVO.id,param1.petEquipmentVO.petLevel,param1.petEquipmentVO);
         var _loc3_:PetActiveSkillVO = param1.petEquipmentVO.activeSkillVO as PetActiveSkillVO;
         _loc3_.originalHurt = param1.petEquipmentVO.petLevel * _loc3_.hurCoefficient + _loc3_.additionHurt;
         _loc3_.originalPkHurt = param1.petEquipmentVO.petLevel * _loc3_.pkHurtCoefficient + _loc3_.additionPkHurt;
         param1.petEquipmentVO.essentialPercent = _loc2_ / param1.petEquipmentVO.essentialVolume;
         MyFunction.getInstance().refreshPet(param1.petEquipmentVO);
      }
      
      public function changePetToInitalState(param1:Pet) : void
      {
         var _loc3_:int = 0;
         var _loc6_:PetEquipmentVO = param1.petEquipmentVO;
         var _loc2_:PetEquipmentVO = XMLSingle.getEquipment(MyFunction.getInstance().calculateInitLevelID(_loc6_.id),XMLSingle.getInstance().equipmentXML) as PetEquipmentVO;
         _loc2_.petLevel = 1;
         XMLSingle.getInstance().setPetData(_loc2_.id,_loc2_.petLevel,_loc2_);
         var _loc8_:PetActiveSkillVO = _loc2_.activeSkillVO as PetActiveSkillVO;
         _loc8_.originalHurt = _loc2_.petLevel * _loc8_.hurCoefficient + _loc8_.additionHurt;
         _loc8_.originalPkHurt = _loc2_.petLevel * _loc8_.pkHurtCoefficient + _loc8_.additionPkHurt;
         var _loc7_:Vector.<SkillVO> = new Vector.<SkillVO>();
         for each(var _loc5_ in _loc6_.passiveSkillVOs)
         {
            if(_loc5_)
            {
               _loc3_ = (_loc5_ as PetPassiveSkillVO).promoteValue;
               _loc5_ = XMLSingle.getSkill(MyFunction.getInstance().calculateInitLevelID(int(_loc5_.id)),XMLSingle.getInstance().skillXML);
               (_loc5_ as PetPassiveSkillVO).promoteValue = _loc3_;
               (_loc5_ as PetPassiveSkillVO).value = (_loc5_ as PetPassiveSkillVO).originalValue + (_loc5_ as PetPassiveSkillVO).promoteValue;
               _loc7_.push(_loc5_);
            }
         }
         _loc2_.passiveSkillVOs = _loc7_;
         var _loc4_:int = _loc6_.essentialPercent * _loc6_.essentialVolume;
         _loc2_.essentialPercent = _loc4_ / _loc2_.essentialVolume;
         _loc2_.experiencePercent = 0;
         _loc2_.talentVO = _loc6_.talentVO;
         _loc6_.talentVO = null;
         MyFunction.getInstance().refreshPet(_loc2_);
         _loc6_.clear();
         param1.petEquipmentVO = _loc2_;
      }
   }
}

