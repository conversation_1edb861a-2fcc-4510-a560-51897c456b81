package YJFY.Skill.PetSkills
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_Pet21Skill extends Skill_Pet20Skill
   {
      
      private const m_const_startEffectFrameLabel:String = "start";
      
      private var m_playEffectShowObj:AnimationShowPlayLogicShell;
      
      private var m_playEffectShowObj_FrameLabelListener:AnimationPlayFrameLabelListener;
      
      public function Skill_Pet21Skill()
      {
         super();
         m_playEffectShowObj = new AnimationShowPlayLogicShell();
         m_playEffectShowObj_FrameLabelListener = new AnimationPlayFrameLabelListener();
         m_playEffectShowObj_FrameLabelListener.reachFrameLabelFun = effectReachFrameLabel;
         m_playEffectShowObj.addFrameLabelListener(m_playEffectShowObj_FrameLabelListener);
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_playEffectShowObj);
         m_playEffectShowObj = null;
         ClearUtil.clearObject(m_playEffectShowObj_FrameLabelListener);
         m_playEffectShowObj_FrameLabelListener = null;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      override public function setPostion() : void
      {
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_bodyAttackReachFrameLabel === _loc2_)
         {
            playEffect();
         }
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      private function playEffect() : void
      {
         m_playEffectShowObj.setShow(m_owner.getAnimationByDefId("petSkillShow"));
         m_playEffectShowObj.getDisplayShow().x = m_world.getCamera().getScreenX();
         m_playEffectShowObj.getDisplayShow().y = m_world.getCamera().getScreenY();
         m_playEffectShowObj.gotoAndPlay("start");
         m_world.addAnimationInFront(m_playEffectShowObj);
      }
      
      private function effectReachFrameLabel(param1:String) : void
      {
         if(param1 == "end^stop^")
         {
            if(m_playEffectShowObj)
            {
               m_owner.getWorld().removeAnimationInFront(m_playEffectShowObj);
            }
         }
      }
   }
}

