package UI.Buff.Buff.OnlyOnLineBuff
{
   import UI.Buff.Buff.BuffVO;
   
   public class OnlyOnLineBuffVO extends BuffVO
   {
      
      private var _saveRemainTime:uint;
      
      public var initTime:String;
      
      public function OnlyOnLineBuffVO()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.saveRemainTime = _saveRemainTime;
      }
      
      public function get saveRemainTime() : uint
      {
         return _antiwear.saveRemainTime;
      }
      
      public function set saveRemainTime(param1:uint) : void
      {
         _antiwear.saveRemainTime = param1;
      }
   }
}

