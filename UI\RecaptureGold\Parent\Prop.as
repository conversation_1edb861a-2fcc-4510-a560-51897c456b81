package UI.RecaptureGold.Parent
{
   import UI.MyMovieClip;
   
   public class Prop extends MyMovieClip implements IProp
   {
      
      public function Prop()
      {
         super();
      }
      
      public function clone() : IProp
      {
         return new Prop();
      }
      
      override public function clear() : void
      {
         stop();
         super.clear();
      }
   }
}

