package UI.RefineFactory.Btn
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class StartRefineBtn extends Btn
   {
      
      public function StartRefineBtn()
      {
         super();
         setTipString("点击开始炼制");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickStartRefineBtn"));
      }
   }
}

