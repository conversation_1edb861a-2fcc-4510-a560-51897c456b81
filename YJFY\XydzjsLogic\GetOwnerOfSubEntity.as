package YJFY.XydzjsLogic
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.ISubAttackEntity;
   
   public class GetOwnerOfSubEntity
   {
      
      public function GetOwnerOfSubEntity()
      {
         super();
      }
      
      public function getOwnerOfSubAttackEntity(param1:IEntity) : IEntity
      {
         if(param1 is ISubAttackEntity)
         {
            return (param1 as ISubAttackEntity).getOwner();
         }
         if(param1.getExtra() is ISubAttackEntity)
         {
            return (param1.getExtra() as ISubAttackEntity).getOwner();
         }
         return null;
      }
   }
}

