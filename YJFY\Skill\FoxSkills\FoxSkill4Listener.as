package YJFY.Skill.FoxSkills
{
   public class Fox<PERSON>kill4Listener implements IFoxSkill4Listener
   {
      
      public var createImageFun:Function;
      
      public function FoxSkill4Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         createImageFun = null;
      }
      
      public function createImage(param1:Skill_FoxSkill4) : void
      {
         if(<PERSON><PERSON><PERSON>(createImageFun))
         {
            createImageFun(param1);
         }
      }
   }
}

