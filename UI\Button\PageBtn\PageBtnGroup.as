package UI.Button.PageBtn
{
   import UI.Event.UIBtnEvent;
   import flash.events.Event;
   
   public class PageBtnGroup extends PageBtnGroup_Parent
   {
      
      public function PageBtnGroup()
      {
         super();
      }
      
      override public function initPageNumber(param1:int = 1, param2:int = 4) : void
      {
         super.initPageNumber(param1,param2);
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override protected function clickPageBtn(param1:UIBtnEvent) : void
      {
         super.clickPageBtn(param1);
      }
      
      override protected function init() : void
      {
         _showSprite = new PageBtnGroup_H_ShowPart();
         super.init();
      }
   }
}

