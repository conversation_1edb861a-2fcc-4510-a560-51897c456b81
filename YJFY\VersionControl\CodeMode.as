package YJFY.VersionControl
{
   public class CodeMode
   {
      
      public static const ON_CODE:String = "true";
      
      public static const OFF_CODE:String = "false";
      
      private var m_codemode:String = "false";
      
      public function CodeMode()
      {
         super();
      }
      
      public function getCodeMode() : String
      {
         return m_codemode;
      }
      
      public function setCodeMode(param1:String) : void
      {
         if(param1 != "true" && param1 != "false")
         {
            throw new Error("codeMode CodeMode.ON_CODE 或 CodeMode.OFF_CODE");
         }
         m_codemode = param1;
      }
   }
}

