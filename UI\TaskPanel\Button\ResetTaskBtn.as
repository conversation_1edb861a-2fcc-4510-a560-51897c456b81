package UI.TaskPanel.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import flash.events.MouseEvent;
   
   public class ResetTaskBtn extends Btn
   {
      
      public function ResetTaskBtn()
      {
         super();
         setTipString("点击重置任务");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickResetTaskBtn"));
      }
   }
}

