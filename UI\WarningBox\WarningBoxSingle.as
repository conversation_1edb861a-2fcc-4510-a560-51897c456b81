package UI.WarningBox
{
   public class WarningBoxSingle extends WarningBox
   {
      
      private static var _instance:WarningBoxSingle = null;
      
      public function WarningBoxSingle()
      {
         if(_instance == null)
         {
            super();
            _instance = this;
            return;
         }
         throw new Error("fuck you ! 没看见实例已经存在了吗?!");
      }
      
      public static function getInstance() : WarningBoxSingle
      {
         if(_instance == null)
         {
            _instance = new WarningBoxSingle();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         _instance = null;
      }
   }
}

