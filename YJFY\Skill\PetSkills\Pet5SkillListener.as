package YJFY.Skill.PetSkills
{
   import YJFY.Entity.IEntity;
   
   public class Pet5<PERSON>killListener implements IPet5SkillListener
   {
      
      public var addFronzeShowFun:Function;
      
      public function Pet5SkillListener()
      {
         super();
      }
      
      public function clear() : void
      {
         addFronzeShowFun = null;
      }
      
      public function addFronzeShow(param1:Skill_Pet5Skill, param2:IEntity) : void
      {
         if(<PERSON><PERSON>an(addFronzeShowFun))
         {
            addFronzeShowFun(param1,param2);
         }
      }
   }
}

