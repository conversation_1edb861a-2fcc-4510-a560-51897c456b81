package UI.WorldBoss.Boss.AbleAttackedBoss
{
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.Boss.BossBloodData;
   import UI.WorldBoss.Boss.BossData;
   import UI.WorldBoss.MultiplyBar;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   
   public class AbleAttackedBoss extends Boss
   {
      
      private var m_beAttackedBloodVolume:int = 0;
      
      private var m_bossBar:MultiplyBar;
      
      private var m_bossData:BossData;
      
      public function AbleAttackedBoss()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_bossBar);
         m_bossBar = null;
         m_bossData = null;
      }
      
      override protected function getShowSuccess(param1:YJFYLoaderData) : void
      {
         ClearUtil.clearObject(m_bossBar);
         m_bossBar = null;
         super.getShowSuccess(param1);
         m_bossBar = new MultiplyBar();
         m_bossBar.setShow(m_mc.getShow()["card"]["bossBar"]);
         m_bossBar.change(0);
         m_bossBar.setDataShow("");
         m_bossBar.setDataShow2("");
         setBossBar(0,"0",1);
      }
      
      public function getBeAttackedBloodVolume() : int
      {
         return m_beAttackedBloodVolume;
      }
      
      public function addLostHurt(param1:int) : void
      {
         m_beAttackedBloodVolume += param1;
         m_bossData.totalBloodOfBossLost += param1;
         m_bossData.totalHurtNum++;
      }
      
      public function setBossData(param1:BossData) : void
      {
         m_bossData = param1;
      }
      
      public function getBloodData(param1:BossBloodData) : void
      {
         param1.beAttackedBloodVolume = m_beAttackedBloodVolume;
         var _loc2_:int = m_beAttackedBloodVolume / m_hurtVolume;
         param1.bloodShowIndex = _loc2_ + 1;
         param1.bloodPercent = (m_beAttackedBloodVolume - _loc2_ * m_hurtVolume) / m_hurtVolume;
      }
      
      public function setBossBar(param1:Number, param2:String, param3:int) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         if(m_bossBar)
         {
            m_bossBar.change(param1);
            m_bossBar.setDataShow(param2);
            m_bossBar.setDataShow2(param3.toString());
            _loc5_ = int(m_xml.@changeColorNum);
            _loc4_ = Math.min(2,Math.max(0,int(param3 / _loc5_)));
            m_bossBar.setColorIndex(_loc4_);
         }
      }
   }
}

