package YJFY.Skill.ActiveSkill
{
   import YJFY.World.World;
   
   public interface IChoiceLocationSkill
   {
      
      function releaseSkill(param1:World) : void;
      
      function setMoveSpeed(param1:Number) : void;
      
      function setDirection(param1:Number, param2:Number) : void;
      
      function startMove() : void;
      
      function stopMove() : void;
      
      function setLocation(param1:Number, param2:Number) : void;
   }
}

