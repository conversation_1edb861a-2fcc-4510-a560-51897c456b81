package YJFY.ShowLogicShell
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class PageBtnGroupLogicShell implements IButton
   {
      
      private var m_show:Sprite;
      
      private var _pageBtnUp:IButton;
      
      private var _pageBtnDown:IButton;
      
      private var _pageNumText:TextField;
      
      private var _pageCountText:TextField;
      
      private var _pageNum:int;
      
      private var _pageCount:int;
      
      public function PageBtnGroupLogicShell()
      {
         super();
      }
      
      public function setShow(param1:Sprite, param2:int = 1, param3:int = 1) : void
      {
         this.m_show = param1;
         if(param1["pageBtnUp"] is MovieClip)
         {
            _pageBtnUp = new ButtonLogicShell2();
         }
         else
         {
            _pageBtnUp = new ButtonLogicShell();
         }
         _pageBtnUp["setShow"](param1["pageBtnUp"]);
         if(param1["pageBtnDown"] is MovieClip)
         {
            _pageBtnDown = new ButtonLogicShell2();
         }
         else
         {
            _pageBtnDown = new ButtonLogicShell();
         }
         _pageBtnDown["setShow"](param1["pageBtnDown"]);
         if(param1.hasOwnProperty("pageNumText"))
         {
            _pageNumText = param1["pageNumText"];
         }
         if(param1.hasOwnProperty("pageCountText"))
         {
            _pageCountText = param1["pageCountText"];
         }
         _pageNum = param2;
         _pageCount = param3;
         var _loc4_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         if(_pageNumText)
         {
            _pageNumText.text = _pageNum.toString();
            MyFunction2.changeTextFieldFont(_loc4_.fontName,_pageNumText,true);
         }
         if(_pageCountText)
         {
            _pageCountText.text = _pageCount.toString();
            MyFunction2.changeTextFieldFont(_loc4_.fontName,_pageCountText,true);
         }
         param1.addEventListener("clickButton",onClick,true,0,true);
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function clear() : void
      {
         m_show = null;
         ClearUtil.clearObject(_pageBtnUp);
         _pageBtnUp = null;
         ClearUtil.clearObject(_pageBtnDown);
         _pageBtnDown = null;
         _pageNumText = null;
         _pageCountText = null;
      }
      
      public function initPageNumber(param1:int, param2:int) : void
      {
         _pageNum = param1;
         _pageCount = param2;
         if(_pageNumText)
         {
            _pageNumText.text = _pageNum.toString();
         }
         if(_pageCountText)
         {
            _pageCountText.text = _pageCount.toString();
         }
      }
      
      public function get pageNum() : int
      {
         return _pageNum;
      }
      
      public function get pageCount() : int
      {
         return _pageCount;
      }
      
      protected function onClick(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case _pageBtnUp:
               if(pageNum > 1)
               {
                  _pageNum--;
                  if(_pageNumText)
                  {
                     _pageNumText.text = _pageNum.toString();
                  }
                  m_show.dispatchEvent(new ButtonEvent("clickButton",this));
               }
               break;
            case _pageBtnDown:
               if(_pageNum < _pageCount)
               {
                  _pageNum++;
                  if(_pageNumText)
                  {
                     _pageNumText.text = _pageNum.toString();
                  }
                  m_show.dispatchEvent(new ButtonEvent("clickButton",this));
               }
         }
      }
   }
}

