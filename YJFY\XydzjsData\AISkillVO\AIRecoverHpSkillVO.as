package YJFY.XydzjsData.AISkillVO
{
   public class AIRecoverHpSkillVO extends AIActiveSkillVO
   {
      
      private var m_recoverHpPerS:uint;
      
      private var m_recoverHpPerMilliS:Number;
      
      public function AIRecoverHpSkillVO()
      {
         super();
      }
      
      override public function initFromXML(param1:XML) : void
      {
         super.initFromXML(param1);
         m_recoverHpPerS = uint(param1.@recoverHpPerS);
         m_recoverHpPerMilliS = m_recoverHpPerS / 1000;
      }
      
      public function getRecoverHpPerS() : uint
      {
         return m_recoverHpPerS;
      }
      
      public function getRecoverHpPerMilliS() : Number
      {
         return m_recoverHpPerMilliS;
      }
   }
}

