package UI.newGuide.GuideEveryDay
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Task.TaskVO.MTaskVO;
   import UI.newGuide.NewGuidePanel;
   import YJFY.LevelMode1.LevelModeSaveData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   
   public class GuideEveryItem
   {
      
      private var m_newguidepanel:NewGuidePanel;
      
      private var m_guideeverypanel:GuideEveryPanel;
      
      private var m_guideeverylist:GuideEveryList;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_id:String;
      
      private var m_item:Sprite;
      
      private var m_data:MTaskVO;
      
      private var m_xz:ButtonLogicShell2;
      
      private var m_wzx:ButtonLogicShell2;
      
      private var m_btnmain:ButtonLogicShell2;
      
      private var m_txtname1:TextField;
      
      private var m_txtname2:TextField;
      
      private var m_txtname3:TextField;
      
      private var m_txtnum1:TextField;
      
      private var m_txtnum2:TextField;
      
      private var m_txtnum3:TextField;
      
      private var m_txtdec1:TextField;
      
      private var m_txtdec2:TextField;
      
      private var m_txtdec3:TextField;
      
      private var m_lingqutexiao:MovieClip;
      
      private var m_lingqutexiaobig:MovieClip;
      
      private var m_doubleClick:MovieClip;
      
      private var m_clicktime:Number;
      
      public function GuideEveryItem()
      {
         super();
      }
      
      public function init(param1:NewGuidePanel, param2:GuideEveryPanel, param3:GuideEveryList, param4:MovieClip, param5:MTaskVO, param6:Sprite, param7:String) : void
      {
         m_newguidepanel = param1;
         m_guideeverypanel = param2;
         m_guideeverylist = param3;
         m_show = param4;
         m_id = param7;
         m_item = param6;
         m_data = param5;
         initParams();
         refreshInfo();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_btnmain);
         m_btnmain = null;
         ClearUtil.clearObject(m_xz);
         m_xz = null;
         ClearUtil.clearObject(m_wzx);
         m_wzx = null;
      }
      
      public function initParams() : void
      {
         var _loc1_:Array = null;
         m_doubleClick = m_item["doubleclickmc"];
         m_lingqutexiao = m_item["lingqutexiao"];
         m_lingqutexiaobig = m_item["lingqutexiaobig"];
         m_xz = new ButtonLogicShell2();
         m_xz.setShow(m_item["guidexz"]);
         m_wzx = new ButtonLogicShell2();
         m_wzx.setShow(m_item["guidewxz"]);
         m_btnmain = new ButtonLogicShell2();
         m_btnmain.setShow(m_item["guidexzmain"]);
         m_txtname1 = m_xz.getShow()["txtname"];
         m_txtnum1 = m_xz.getShow()["txtnum"];
         m_txtdec1 = m_xz.getShow()["txtdescription"];
         m_txtname2 = m_wzx.getShow()["txtname"];
         m_txtnum2 = m_wzx.getShow()["txtnum"];
         m_txtdec2 = m_wzx.getShow()["txtdescription"];
         m_txtname3 = m_btnmain.getShow()["txtname"];
         m_txtnum3 = m_btnmain.getShow()["txtnum"];
         m_txtdec3 = m_btnmain.getShow()["txtdescription"];
         m_txtname1.mouseEnabled = false;
         m_txtnum1.mouseEnabled = false;
         m_txtdec1.mouseEnabled = false;
         m_txtname2.mouseEnabled = false;
         m_txtnum2.mouseEnabled = false;
         m_txtdec2.mouseEnabled = false;
         m_txtname3.mouseEnabled = false;
         m_txtnum3.mouseEnabled = false;
         m_txtdec3.mouseEnabled = false;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtname1,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtnum1,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtdec1,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtname2,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtnum2,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtdec2,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtname3,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtnum3,true);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_txtdec3,true);
         _loc1_ = [new GlowFilter(0,1,2,2,10,1,false,false)];
         m_txtname1.filters = _loc1_;
         m_txtnum1.filters = _loc1_;
         m_txtdec1.filters = _loc1_;
         m_txtname2.filters = _loc1_;
         m_txtnum2.filters = _loc1_;
         m_txtdec2.filters = _loc1_;
         m_txtname3.filters = _loc1_;
         m_txtnum3.filters = _loc1_;
         m_txtdec3.filters = _loc1_;
      }
      
      public function refreshInfo() : void
      {
         m_txtname1.text = m_data.name;
         m_txtnum1.text = "";
         m_txtname2.text = m_data.name;
         m_txtnum2.text = "";
         m_txtdec1.text = m_data.description;
         m_txtdec2.text = m_data.description;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < m_data.currentTaskGoalVO_nums.length)
         {
            _loc2_ += m_data.currentTaskGoalVO_nums[_loc3_];
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < m_data.taskGoalVO_nums.length)
         {
            _loc1_ += m_data.taskGoalVO_nums[_loc3_];
            _loc3_++;
         }
         m_txtnum1.text = String(_loc2_) + "/" + String(_loc1_);
         m_txtnum2.text = String(_loc2_) + "/" + String(_loc1_);
         m_lingqutexiaobig.visible = false;
         if(_loc2_ >= _loc1_ && _loc1_ != 0)
         {
            m_lingqutexiao.visible = true;
         }
         else
         {
            m_lingqutexiao.visible = false;
         }
      }
      
      public function checkCurrent(param1:String) : void
      {
         if(param1 == m_id)
         {
            m_xz.getShow().visible = true;
            m_xz.unLock();
            m_wzx.getShow().visible = false;
            m_wzx.lock();
         }
         else
         {
            m_xz.getShow().visible = false;
            m_xz.lock();
            m_wzx.getShow().visible = true;
            m_wzx.unLock();
         }
         m_btnmain.getShow().visible = false;
         m_btnmain.lock();
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_xz:
            case m_wzx:
               checkdouble();
               m_guideeverypanel.refreshScript(this);
               m_guideeverylist.refreshById(m_id);
         }
      }
      
      private function checkdouble() : void
      {
         if(new Date().time - m_clicktime < 500)
         {
            calldoubleclick(null);
         }
         m_clicktime = new Date().time;
      }
      
      private function calldoubleclick(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < m_data.currentTaskGoalVO_nums.length)
         {
            _loc3_ += m_data.currentTaskGoalVO_nums[_loc4_];
            _loc4_++;
         }
         _loc4_ = 0;
         while(_loc4_ < m_data.taskGoalVO_nums.length)
         {
            _loc2_ += m_data.taskGoalVO_nums[_loc4_];
            _loc4_++;
         }
         if(_loc3_ >= _loc2_)
         {
            m_newguidepanel.refreshState();
            GamingUI.getInstance().openNewTask(m_newguidepanel.currentType,String(m_data.id));
         }
         else if(m_data.gotoInfo)
         {
            if(m_data.gotoInfo.type == "2" || m_data.gotoInfo.type == "3" || m_data.gotoInfo.type == "4" || m_data.gotoInfo.type == "5" || m_data.gotoInfo.type == "6" || m_data.gotoInfo.type == "7" || m_data.gotoInfo.type == "8" || m_data.gotoInfo.type == "9" || m_data.gotoInfo.type == "10")
            {
               if("NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" == m_data.gotoInfo.xmlpath)
               {
                  if(LevelModeSaveData.getInstance().getMapLevel() >= 16)
                  {
                     Part1.getInstance().closeCityMap();
                     Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("关卡未开启");
                  }
               }
               else if("NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" == m_data.gotoInfo.xmlpath)
               {
                  if(GamingUI.getInstance().player1.playerVO.level >= 30)
                  {
                     Part1.getInstance().closeCityMap();
                     Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                  }
               }
               else if("NewGameFolder/GuardingTangSengLevelMode/routeMap4.xml" == m_data.gotoInfo.xmlpath)
               {
                  if(GamingUI.getInstance().player1.playerVO.level >= 60)
                  {
                     if(LevelModeSaveData.getInstance().getGodLevel() >= int(m_data.gotoInfo.gotobtnname.substr("levelMaptBtn".length,m_data.gotoInfo.gotobtnname.length)))
                     {
                        Part1.getInstance().closeCityMap();
                        Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                     }
                     else
                     {
                        GamingUI.getInstance().showMessageTip("关卡未开启");
                     }
                  }
                  else
                  {
                     GamingUI.getInstance().showMessageTip("60级之后才能挑战");
                  }
               }
               else if("NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" == m_data.gotoInfo.xmlpath)
               {
                  Part1.getInstance().closeCityMap();
                  Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
               }
            }
            else if(m_data.gotoInfo.type == "1")
            {
               if(GamingUI.getInstance().player1.playerVO.level >= 30)
               {
                  GamingUI.getInstance().openPreciousView();
               }
               else
               {
                  GamingUI.getInstance().showMessageTip("30级之后才能挑战");
               }
            }
            m_newguidepanel.refreshState();
         }
      }
      
      public function refreshScript() : void
      {
         m_guideeverypanel.refreshScript(this);
      }
      
      public function show() : void
      {
         m_item.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function hide() : void
      {
         m_item.removeEventListener("clickButton",clickButton,true);
      }
      
      public function getShow() : Sprite
      {
         return m_item;
      }
      
      public function getData() : MTaskVO
      {
         return m_data;
      }
      
      public function getID() : String
      {
         return m_id;
      }
   }
}

