package YJFY.ToolTip
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class MessageTip
   {
      
      private var m_showMC:AnimationShowPlayLogicShell;
      
      private var m_endPlayListener:PlayEndListener;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_show:MovieClip;
      
      private var m_textField:TextField;
      
      private var m_endFun:Function;
      
      private var m_parent:DisplayObjectContainer;
      
      public function MessageTip()
      {
         super();
         m_font = new FangZhengKaTongJianTi();
      }
      
      public function clear() : void
      {
         if(Boolean(m_parent) && Boolean(m_textField) && m_parent.contains(m_textField))
         {
            m_parent.removeChild(m_textField);
         }
         m_parent = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_endPlayListener);
         m_endPlayListener = null;
         m_font = null;
         m_show = null;
         m_textField = null;
         m_endFun = null;
      }
      
      public function setShow(param1:MovieClip, param2:DisplayObjectContainer, param3:TextField, param4:Function) : void
      {
         m_show = param1;
         m_endFun = param4;
         var _loc5_:TextFormat = param3.defaultTextFormat;
         var _loc6_:* = param3.parent;
         var _loc7_:Array = param3.filters ? param3.filters.slice(0) : null;
         if(Boolean(_loc6_) && _loc6_.contains(param3))
         {
            _loc6_.removeChild(param3);
         }
         if(_loc6_ == null)
         {
            _loc6_ = param2;
         }
         m_textField = new TextField();
         m_textField.defaultTextFormat = _loc5_;
         if(_loc7_)
         {
            m_textField.filters = _loc7_;
         }
         _loc6_.addChild(m_textField);
         m_parent = _loc6_;
         initShow();
      }
      
      public function setText(param1:String) : void
      {
         if(m_textField)
         {
            m_textField.htmlText = param1;
            m_textField.width = m_textField.textWidth + 10;
            m_textField.height = m_textField.textHeight + 10;
         }
      }
      
      public function play() : void
      {
         if(m_showMC)
         {
            m_showMC.gotoAndPlay("start");
         }
      }
      
      private function initShow() : void
      {
         m_showMC = new AnimationShowPlayLogicShell();
         m_showMC.setShow(m_show);
         m_endPlayListener = new PlayEndListener();
         m_endPlayListener.playEndFun = playEndFun;
         m_showMC.addNextStopListener(m_endPlayListener);
         MyFunction2.changeTextFieldFont(m_font.fontName,m_textField);
         m_textField.selectable = false;
         m_textField.wordWrap = false;
      }
      
      private function playEndFun() : void
      {
         if(Boolean(m_endFun))
         {
            m_endFun();
         }
      }
   }
}

