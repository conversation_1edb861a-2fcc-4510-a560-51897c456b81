package YJFY.PKMode2.PKLogic
{
   import UI.EnterFrameTime;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class HurtChangeColorManager
   {
      
      private var m_useHurtChangeColors:Vector.<HurtChangeColor>;
      
      private var m_wasteHurtChangeColors:Vector.<HurtChangeColor>;
      
      private var m_hurtChangeColorPool:ObjectsPool;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      public function HurtChangeColorManager()
      {
         super();
         m_useHurtChangeColors = new Vector.<HurtChangeColor>();
         m_wasteHurtChangeColors = new Vector.<HurtChangeColor>();
         m_hurtChangeColorPool = new ObjectsPool(m_useHurtChangeColors,m_wasteHurtChangeColors,createNewHurtChangeColor,null);
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_useHurtChangeColors,false,false,false);
         m_useHurtChangeColors = null;
         ClearUtil.nullArr(m_wasteHurtChangeColors,false,false,false);
         ClearUtil.clearObject(m_hurtChangeColorPool);
         m_hurtChangeColorPool = null;
         m_enterFrameTime = null;
      }
      
      public function init(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_useHurtChangeColors.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_useHurtChangeColors[_loc3_].getRecoverElapsedTime() < param1.getGameTimeForThisInit())
            {
               m_useHurtChangeColors[_loc3_].recover();
               m_hurtChangeColorPool.wasteOneObj(m_useHurtChangeColors[_loc3_]);
               _loc3_--;
               _loc2_--;
            }
            _loc3_++;
         }
      }
      
      public function changeColor(param1:Sprite) : void
      {
         var _loc5_:int = 0;
         var _loc2_:Boolean = false;
         var _loc4_:HurtChangeColor = null;
         var _loc3_:int = int(m_useHurtChangeColors.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            if(m_useHurtChangeColors[_loc5_].getShow() == param1)
            {
               _loc2_ = true;
            }
            _loc5_++;
         }
         if(_loc2_ == false)
         {
            _loc4_ = m_hurtChangeColorPool.getOneOrCreateOneObj() as HurtChangeColor;
            _loc4_.changeColor(param1,m_enterFrameTime);
         }
      }
      
      private function createNewHurtChangeColor() : HurtChangeColor
      {
         return new HurtChangeColor();
      }
   }
}

