package UI.RecaptureGold
{
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.RecaptureGold.Props.Prop_StrengthPotion;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.getQualifiedClassName;
   
   public class RGPlayer extends MySprite
   {
      
      public static const FLEW_INTO:String = "flewInto";
      
      public static const NORMAL:String = "normal";
      
      public static const THROW_ROPE:String = "throwRope";
      
      public static const DRAG_ROPE_1:String = "dragRope1";
      
      public static const DRAG_ROPE_2:String = "dragRope2";
      
      public static const USE_BOMB:String = "useBomb";
      
      private var _rgPlayerVO:RGPlayerVO;
      
      private var _playerPropVO:PlayerPropVO;
      
      private var _playerType:String;
      
      private var _animationLayer:Sprite;
      
      private var _recaptureGoldXML:XML;
      
      public function RGPlayer(param1:RGPlayerVO, param2:XML)
      {
         super();
         _rgPlayerVO = param1;
         _animationLayer = new Sprite();
         addChild(_animationLayer);
         _recaptureGoldXML = param2;
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_animationLayer)
         {
            while(_animationLayer.numChildren > 0)
            {
               _loc1_ = _animationLayer.getChildAt(0);
               _animationLayer.removeChildAt(0);
               if(_loc1_.hasOwnProperty("clear"))
               {
                  _loc1_["clear"]();
               }
            }
         }
         _animationLayer = null;
         if(_rgPlayerVO)
         {
            _rgPlayerVO.clear();
         }
         _rgPlayerVO = null;
         if(_playerPropVO)
         {
            _playerPropVO.clear();
         }
         _playerPropVO = null;
         _animationLayer = null;
         _recaptureGoldXML = null;
      }
      
      public function get rgPlayerVO() : RGPlayerVO
      {
         return _rgPlayerVO;
      }
      
      public function get playerPropVO() : PlayerPropVO
      {
         return _playerPropVO;
      }
      
      public function set playerPropVO(param1:PlayerPropVO) : void
      {
         _playerPropVO = param1;
      }
      
      public function set playerType(param1:String) : void
      {
         _playerType = param1;
      }
      
      public function getSpeed(param1:XML) : Number
      {
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc2_:Prop_StrengthPotion = _playerPropVO.judeIsHaveStrengthPotion();
         if(_loc2_)
         {
            _loc4_ = getQualifiedClassName(_loc2_);
            _loc3_ = int(_recaptureGoldXML.PropData[0].item.(@classNameForShow == _loc4_)[0].@promoteValue);
         }
         return (_rgPlayerVO.physicalStrength + _loc3_) / Number(param1.OtherData[0].PowerToSpeed[0].@value);
      }
      
      public function playAnimation(param1:XML, param2:String = "normal", param3:Array = null) : void
      {
         var animation:MovieClip;
         var i:int;
         var length:int;
         var eventFun:Function;
         var recaptureGoldXML:XML = param1;
         var animationStr:String = param2;
         var funObjects:Array = param3;
         while(_animationLayer.numChildren > 0)
         {
            _animationLayer.removeChildAt(0);
         }
         animation = MyFunction2.returnShowByClassName(recaptureGoldXML.PlayerData[0].Animation.(@playerType == _playerType)[0][animationStr]) as MovieClip;
         _animationLayer.addChild(animation);
         length = funObjects ? funObjects.length : 0;
         i = 0;
         while(i < length)
         {
            eventFun = function(param1:Event):void
            {
               var _loc2_:int = 0;
               param1.target.removeEventListener(param1.type,eventFun,false);
               _loc2_ = 0;
               while(_loc2_ < length)
               {
                  if(funObjects[_loc2_].eventType == param1.type)
                  {
                     if(funObjects[_loc2_].fun)
                     {
                        funObjects[_loc2_].fun.apply(null,funObjects[_loc2_].funParams);
                     }
                  }
                  _loc2_++;
               }
            };
            animation.addEventListener(funObjects[i].eventType,eventFun,false);
            i++;
         }
      }
   }
}

