package YJFY.Skill.PetSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_JianSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_Pet7Skill extends CuboidAreaAttackSkill_JianSkill
   {
      
      private var m_pushImpluse:P3DVector3D;
      
      private var m_pushImpluse2:P3DVector3D;
      
      public function Skill_Pet7Skill()
      {
         super();
         m_pushImpluse = new P3DVector3D(1500000,0,800000);
         m_pushImpluse2 = new P3DVector3D();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pushImpluse);
         m_pushImpluse = null;
         ClearUtil.clearObject(m_pushImpluse2);
         m_pushImpluse2 = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         playDisappear();
         return true;
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            m_pushImpluse2.multi2(m_owner.getShowDirection(),m_pushImpluse);
            param1.getBody().applyImpulse(m_pushImpluse2);
         }
      }
   }
}

