package YJFY.ShowLogicShell.ChangeBarLogicShell
{
   import YJFY.Utils.ClearUtil;
   import YJFY.data.linkList.LinkList;
   import YJFY.data.linkList.Node;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.Font;
   
   public class DoubleShowChangeBarLogicShell2
   {
      
      private var m_upBar:CMSXChangeBarLogicShell;
      
      private var m_down_upShow:Sprite;
      
      private var m_down_mask:Sprite;
      
      private var m_changeDelay:uint;
      
      private var m_percentNodes:LinkList;
      
      private var m_timeNodes:LinkList;
      
      private var m_currentPercent:Number;
      
      private var m_targetPercent:Number;
      
      private var m_maxMinEnd:Number;
      
      private var m_show:MovieClip;
      
      private var m_elapsedTimeData:IElapsedTimeData;
      
      private var m_upLength:Number;
      
      private var m_index:int;
      
      public function DoubleShowChangeBarLogicShell2()
      {
         super();
         m_upBar = new CMSXChangeBarLogicShell();
         m_percentNodes = new LinkList();
         m_timeNodes = new LinkList();
         m_currentPercent = 0;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_upBar);
         m_upBar = null;
         ClearUtil.clearObject(m_percentNodes);
         m_percentNodes = null;
         ClearUtil.clearObject(m_timeNodes);
         m_timeNodes = null;
         m_show = null;
         m_elapsedTimeData = null;
      }
      
      public function init(param1:MovieClip, param2:IElapsedTimeData, param3:uint) : void
      {
         m_show = param1;
         m_elapsedTimeData = param2;
         m_changeDelay = param3;
         initShow();
         change1(1);
      }
      
      public function render() : void
      {
         m_index++;
         if(m_index == 3)
         {
            updateShow();
            m_index = 0;
         }
      }
      
      public function change1(param1:Number) : void
      {
         param1 = Math.max(0,Math.min(1,param1));
         ClearUtil.clearObject(m_percentNodes);
         ClearUtil.clearObject(m_timeNodes);
         m_targetPercent = param1;
         m_currentPercent = param1;
         m_upBar.change(param1);
         drawDownMask(param1,param1);
         m_maxMinEnd = 1;
      }
      
      public function change2(param1:Number) : void
      {
         param1 = Math.max(0,Math.min(1,param1));
         m_targetPercent = param1;
         var _loc4_:Node = m_timeNodes.getEndNode();
         var _loc3_:TimeNode = new TimeNode();
         if(_loc4_ == null)
         {
            _loc3_.start = m_elapsedTimeData.getElapsedTime();
         }
         else
         {
            _loc3_.start = (_loc4_.getItem() as TimeNode).end;
         }
         _loc3_.interval = m_changeDelay;
         _loc3_.end = _loc3_.start + _loc3_.interval;
         _loc4_ = m_percentNodes.getEndNode();
         var _loc2_:PercentNode = new PercentNode();
         if(_loc4_ == null)
         {
            _loc2_.start = m_currentPercent;
         }
         else
         {
            _loc2_.start = (_loc4_.getItem() as PercentNode).end;
         }
         _loc2_.end = param1;
         _loc2_.interval = _loc2_.start - _loc2_.end;
         m_percentNodes.push(_loc2_);
         m_timeNodes.push(_loc3_);
         m_maxMinEnd = Math.min(param1,m_maxMinEnd);
         updateShow();
      }
      
      public function setDataShow(param1:String) : void
      {
         m_upBar.setDataShow(param1);
      }
      
      public function setFont(param1:Font) : void
      {
         m_upBar.setFont(param1);
      }
      
      private function updateShow() : void
      {
         var _loc3_:PercentNode = null;
         var _loc1_:* = undefined;
         var _loc2_:TimeNode = null;
         if(m_percentNodes.getEndNode())
         {
            _loc3_ = m_percentNodes.getFirstNode() ? m_percentNodes.getFirstNode().getItem() as PercentNode : null;
            while(Boolean(_loc3_) && _loc3_.start < _loc3_.end)
            {
               _loc1_ = m_percentNodes.shift();
               if(_loc3_.end == m_maxMinEnd)
               {
                  updateMaxMin();
               }
               else if(_loc3_.end < m_maxMinEnd)
               {
                  throw new Error();
               }
               ClearUtil.clearObject(_loc1_);
               _loc1_ = m_timeNodes.shift();
               ClearUtil.clearObject(_loc1_);
               _loc3_ = m_percentNodes.getFirstNode() ? m_percentNodes.getFirstNode().getItem() as PercentNode : null;
            }
            if(_loc3_ == null)
            {
               return;
            }
            _loc2_ = m_timeNodes.getFirstNode() ? m_timeNodes.getFirstNode().getItem() as TimeNode : null;
            while(Boolean(_loc2_) && _loc2_.end < m_elapsedTimeData.getElapsedTime())
            {
               _loc1_ = m_percentNodes.shift();
               if(_loc3_.end == m_maxMinEnd)
               {
                  updateMaxMin();
               }
               else if(_loc3_.end < m_maxMinEnd)
               {
                  throw new Error();
               }
               ClearUtil.clearObject(_loc1_);
               _loc1_ = m_timeNodes.shift();
               ClearUtil.clearObject(_loc1_);
               _loc2_ = m_timeNodes.getFirstNode() ? m_timeNodes.getFirstNode().getItem() as TimeNode : null;
               _loc3_ = m_timeNodes.getFirstNode() ? m_percentNodes.getFirstNode().getItem() as PercentNode : null;
               if(Boolean(_loc2_) && _loc2_.start > m_elapsedTimeData.getElapsedTime())
               {
                  throw new Error("出错了");
               }
            }
            if(_loc2_ == null)
            {
               return;
            }
            if(_loc2_.start > m_elapsedTimeData.getElapsedTime())
            {
               throw new Error("出错了");
            }
            m_currentPercent = _loc3_.end + (_loc2_.end - m_elapsedTimeData.getElapsedTime()) / _loc2_.interval * _loc3_.interval;
            if(m_targetPercent <= m_maxMinEnd)
            {
               m_upBar.change(m_targetPercent);
               drawDownMask(m_targetPercent,m_currentPercent);
            }
            else
            {
               m_upBar.change(0);
               drawDownMask(0,m_currentPercent);
            }
         }
         else if(m_currentPercent != m_targetPercent)
         {
            m_currentPercent = m_targetPercent;
            m_upBar.change(m_targetPercent);
            drawDownMask(m_targetPercent,m_currentPercent);
         }
      }
      
      private function updateMaxMin() : void
      {
         var _loc1_:Node = null;
         _loc1_ = m_percentNodes.getFirstNode();
         while(_loc1_ != null)
         {
            m_maxMinEnd = Math.min(1,(_loc1_.getItem() as PercentNode).end);
            _loc1_ = _loc1_.getNextNode();
         }
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      private function initShow() : void
      {
         m_upBar.setShow(m_show["upBar"]);
         m_down_upShow = m_show["downShow"];
         if(m_down_mask == null)
         {
            m_down_mask = new Sprite();
         }
         m_show.addChild(m_down_mask);
         m_down_upShow.mask = m_down_mask;
         m_upLength = m_upBar.getShow().width;
         drawDownMask(0,1);
      }
      
      private function drawDownMask(param1:Number, param2:Number) : void
      {
         m_down_mask.graphics.clear();
         m_down_mask.graphics.beginFill(0,1);
         m_down_mask.graphics.drawRect(0,0,(param2 - param1) * m_upLength,m_down_upShow.height);
         m_down_mask.graphics.endFill();
         m_down_mask.x = m_upBar.getShow().x + param1 * m_upLength;
         m_down_mask.y = m_down_upShow.y;
         m_down_upShow.mask = m_down_mask;
      }
   }
}

class PercentNode
{
   
   public var start:Number;
   
   public var end:Number;
   
   public var interval:Number;
   
   public function PercentNode()
   {
      super();
   }
   
   public function clear() : void
   {
   }
}

class TimeNode
{
   
   public var start:uint;
   
   public var end:uint;
   
   public var interval:uint;
   
   public function TimeNode()
   {
      super();
   }
   
   public function clear() : void
   {
   }
}
