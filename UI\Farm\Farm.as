package UI.Farm
{
   import UI.AnalogServiceHoldFunction;
   import UI.Button.QuitBtn;
   import UI.Equipments.Equipment;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.Farm.Bar.FarmLevelProgressBar;
   import UI.Farm.FarmBlock.FarmBlockShow;
   import UI.Farm.Land.Land;
   import UI.Farm.Land.LandVO;
   import UI.Farm.MouseManager.MouseManager;
   import UI.Farm.PlantBox.PlantBox;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class Farm extends MySprite
   {
      
      public var quitBtn:QuitBtn;
      
      public var dataLayer:Sprite;
      
      public var sceneLayer:FarmSceneLayer;
      
      private var _levelProgressBar:FarmLevelProgressBar;
      
      private var _farmLevel:int;
      
      private var _buyPlantBox:PlantBox;
      
      private var _plantLand:Land;
      
      private var _mouseX:Number;
      
      private var _mouseY:Number;
      
      public function Farm()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         if(hasEventListener("enterFrame"))
         {
            removeEventListener("enterFrame",detection,false);
         }
         while(numChildren > 0)
         {
            _loc2_ = getChildAt(0);
            removeChildAt(0);
            if(_loc2_.hasOwnProperty("clear"))
            {
               _loc2_["clear"]();
            }
         }
         if(dataLayer)
         {
            while(dataLayer.numChildren > 0)
            {
               _loc2_ = dataLayer.getChildAt(0);
               dataLayer.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
            dataLayer = null;
         }
         if(quitBtn)
         {
            quitBtn.clear();
         }
         quitBtn = null;
         if(sceneLayer)
         {
            sceneLayer.clear();
         }
         sceneLayer = null;
         if(_levelProgressBar)
         {
            _levelProgressBar.clear();
         }
         _levelProgressBar = null;
         if(_buyPlantBox)
         {
            _buyPlantBox.clear();
         }
         _buyPlantBox = null;
         _plantLand = null;
         var _loc1_:int = 0;
         if(_levelProgressBar)
         {
            _levelProgressBar.clear();
         }
         _levelProgressBar = null;
         if(_buyPlantBox)
         {
            _buyPlantBox.clear();
         }
         _buyPlantBox = null;
         _plantLand = null;
         MouseManager.getInstance().clear();
         InformationBoard.getInstance().clear();
      }
      
      public function showBuyPlantBox(param1:Land) : void
      {
         if(!_buyPlantBox)
         {
            _buyPlantBox = new PlantBox();
         }
         else
         {
            _buyPlantBox.refreshBox();
         }
         if(!getChildByName(_buyPlantBox.name))
         {
            addChild(_buyPlantBox);
         }
         _plantLand = param1;
      }
      
      private function init() : void
      {
         var _loc2_:XML = XMLSingle.getInstance().farmXML;
         quitBtn = new QuitBtn();
         quitBtn.x = 910;
         quitBtn.y = 10;
         dataLayer.addChild(quitBtn);
         quitBtn.name = "quitBtn";
         _levelProgressBar = new FarmLevelProgressBar();
         _levelProgressBar.x = 100;
         _levelProgressBar.y = 50;
         dataLayer.addChild(_levelProgressBar);
         var _loc1_:MouseStatePanel = new MouseStatePanel();
         _loc1_.x = 800;
         _loc1_.y = 520;
         dataLayer.addChild(_loc1_);
         initLevel(_loc2_);
         MouseManager.getInstance().farm = this;
      }
      
      public function initLevel(param1:XML) : void
      {
         var _loc7_:int = 0;
         var _loc2_:Number = NaN;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:XMLList = param1.farmLevel[0].farmLevel;
         var _loc8_:int = 0;
         var _loc5_:int = int(_loc6_.length());
         _loc8_ = 0;
         while(_loc8_ < _loc5_)
         {
            if(int(_loc6_[_loc8_].@sunValue) > FarmData.getInstance().currentSunValue)
            {
               break;
            }
            _loc8_++;
         }
         if(_loc8_ == _loc5_)
         {
            _loc2_ = 1;
            _loc7_ = int(_loc6_[_loc8_ - 1].@sunValue) - int(_loc6_[_loc8_ - 2].@sunValue);
            _farmLevel = _loc8_;
         }
         else
         {
            _loc4_ = int(_loc6_[_loc8_].@sunValue);
            _loc3_ = int(_loc6_[_loc8_ - 1].@sunValue);
            _loc7_ = _loc4_ - _loc3_;
            _loc2_ = (FarmData.getInstance().currentSunValue - _loc3_) / _loc7_;
            _farmLevel = _loc8_;
         }
         _levelProgressBar.changeProgress(_loc2_,_loc7_,_farmLevel);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("showLandProgressBar",showOrHideBar,true,0,true);
         addEventListener("hideLandProgressBar",showOrHideBar,true,0,true);
         addEventListener("clickOpenAllBtn",openAll,true,0,true);
         addEventListener("clickQuitBtn",quit,true,0,true);
         addEventListener("buyEquipment",buyPlant,true,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         sceneLayer.addEventListener("mouseDown",dragSceneLayer,false,0,true);
         sceneLayer.addEventListener("mouseUp",dragSceneLayer,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("showLandProgressBar",showOrHideBar,true);
         removeEventListener("hideLandProgressBar",showOrHideBar,true);
         removeEventListener("clickOpenAllBtn",openAll,true);
         removeEventListener("clickQuitBtn",quit,true);
         removeEventListener("buyEquipment",buyPlant,true);
         removeEventListener("warningBox",sureOrCancel,true);
         sceneLayer.removeEventListener("mouseDown",dragSceneLayer,false);
         sceneLayer.removeEventListener("mouseUp",dragSceneLayer,false);
      }
      
      private function openAll(param1:UIBtnEvent) : void
      {
         showWarningBox("是否花费" + int(XMLSingle.getInstance().farmXML.openallinfo[0].@ticketPrice) + "点券开启所有土地",1 | 2,{"type":"buyAllLand"});
      }
      
      private function buy2() : void
      {
         var price:uint = uint(int(XMLSingle.getInstance().farmXML.openallinfo[0].@ticketPrice));
         var ticketId:String = String(XMLSingle.getInstance().farmXML.openallinfo[0].@ticketId);
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买开启所有土地";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            sceneLayer.openAll();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
         },null,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function dragSceneLayer(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "mouseDown":
               _mouseX = param1.stageX;
               _mouseY = param1.stageY;
               addEventListener("enterFrame",detection,false,0,true);
               break;
            case "mouseUp":
               removeEventListener("enterFrame",detection,false);
         }
      }
      
      private function detection(param1:Event) : void
      {
         var _loc4_:Point = new Point(mouseX,mouseY);
         _loc4_ = localToGlobal(_loc4_);
         var _loc3_:Number = _loc4_.x - _mouseX;
         var _loc2_:Number = _loc4_.y - _mouseY;
         if(_loc3_ > 0)
         {
            sceneLayer.x += Math.min(_loc3_,-sceneLayer.x + sceneLayer.environment.width / 2);
         }
         else if(_loc3_ < 0)
         {
            sceneLayer.x += Math.max(_loc3_,stage.stageWidth - sceneLayer.environment.width / 2 - sceneLayer.x);
         }
         if(_loc2_ > 0)
         {
            sceneLayer.y += Math.min(_loc2_,-sceneLayer.y + sceneLayer.environment.height / 2);
         }
         else if(_loc2_ < 0)
         {
            sceneLayer.y += Math.max(_loc2_,stage.stageHeight - sceneLayer.environment.height / 2 - sceneLayer.y);
         }
         _mouseX = _loc4_.x;
         _mouseY = _loc4_.y;
         if(mouseX > stage.stageWidth || mouseX < 0 || mouseY > stage.stageHeight || mouseY < 0)
         {
            sceneLayer.dispatchEvent(new MouseEvent("mouseUp"));
         }
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var buyDataObj:Object;
         var land:Land;
         var farmBlock:FarmBlockShow;
         var recoverLand:Land;
         var e:UIPassiveEvent = param1;
         if(e.data.detail == 1 && e.data.task.type == "buyLand")
         {
            buyDataObj = e.data.task.buyDataObj;
            land = e.data.task.land;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
            {
               if(param1["propId"] != buyDataObj["propId"])
               {
                  showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               sceneLayer.buyNewLand(land);
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
            },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         if(e.data.detail == 1 && e.data.task.type == "buyAllLand")
         {
            buy2();
         }
         if(e.data.detail == 1 && e.data.task.type == "buyFarmBlock")
         {
            buyDataObj = e.data.task.buyDataObj;
            farmBlock = e.data.task.farmBlock;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
            {
               if(param1["propId"] != buyDataObj["propId"])
               {
                  showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               FarmData.getInstance().farmBlockIDs.push(farmBlock.farmBlockVO.id);
               farmBlock.farmBlockVO.isDeveloped = true;
               farmBlock.refreshDraw();
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         if(e.data.detail == 1 && e.data.task.type == "recoverLand")
         {
            buyDataObj = e.data.task.buyDataObj;
            recoverLand = e.data.task.land;
            AnalogServiceHoldFunction.getInstance().buyByPayDataFun(buyDataObj,function(param1:Object):void
            {
               if(param1["propId"] != buyDataObj["propId"])
               {
                  showWarningBox("购买物品id前后端不相同！",0);
                  throw new Error("购买物品id前后端不相同！");
               }
               recoverLand.landDrive.landVO.date = "";
               recoverLand.landDrive.changeLandVOState = 0;
               FarmData.getInstance().recoverLandNum++;
               var _loc2_:SaveTaskInfo = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame2();
            },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
      }
      
      private function buyPlant(param1:UIDataEvent) : void
      {
         var equipment:Equipment;
         var plantLandVO:LandVO;
         var plantLand:Land;
         var e:UIDataEvent = param1;
         if(getChildByName(_buyPlantBox.name))
         {
            removeChild(_buyPlantBox);
         }
         equipment = e.data.equipment;
         if(GamingUI.getInstance().player1.playerVO.money < equipment.equipmentVO.price)
         {
            showWarningBox("元宝不足！",0);
            return;
         }
         GamingUI.getInstance().player1.playerVO.money = GamingUI.getInstance().player1.playerVO.money - equipment.equipmentVO.price;
         _plantLand.landDrive.landVO.equipmentIDInLand = equipment.equipmentVO.id;
         plantLandVO = _plantLand.landDrive.landVO;
         plantLand = _plantLand;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            plantLandVO.date = param1;
            plantLandVO.state = 1;
            var _loc2_:Object = FarmFunction.getInstance().countLandStateRemainTime(plantLandVO,param1,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().farmXML);
            if(Boolean(plantLand) && Boolean(plantLand.landDrive))
            {
               plantLand.landDrive.landData.plantState = "seedState";
               plantLand.landDrive.changeLandVOState = 1;
               plantLand.landDrive.landData.remainTime = _loc2_.remainTime;
               plantLand.landDrive.landData.plantState = _loc2_.plantState;
            }
            GamingUI.getInstance().addMainLineTaskGoalGameEventStr("plantOneLand");
         },showWarningBox,false);
      }
      
      private function quit(param1:Event) : void
      {
         if(param1.target.parent == _buyPlantBox)
         {
            if(getChildByName(_buyPlantBox.name))
            {
               removeChild(_buyPlantBox);
            }
            _plantLand = null;
         }
      }
      
      private function showOrHideBar(param1:UIPassiveEvent) : void
      {
         switch(param1.type)
         {
            case "showLandProgressBar":
               InformationBoard.getInstance().showInformation(param1.data);
               InformationBoard.getInstance().x = param1.data.x;
               InformationBoard.getInstance().y = param1.data.y - param1.data.height;
               sceneLayer.addChildAt(InformationBoard.getInstance(),sceneLayer.numChildren);
               break;
            case "hideLandProgressBar":
               InformationBoard.getInstance().hideInformation();
               if(sceneLayer.getChildByName(InformationBoard.getInstance().name))
               {
                  sceneLayer.removeChild(InformationBoard.getInstance());
               }
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().x = (stage.stageWidth - WarningBoxSingle.getInstance().width) / 2;
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
      }
   }
}

