package UI.Button
{
   import UI.Event.UIBtnEvent;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   
   public class QuitBtn extends Btn
   {
      
      public var quitBtn:SimpleButton;
      
      public function QuitBtn()
      {
         super();
         setTipString("关闭");
      }
      
      override public function clear() : void
      {
         super.clear();
         quitBtn = null;
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickQuitBtn"));
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("rollOver",onOver,false,0,true);
         addEventListener("rollOut",onOut,false,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("rollOver",onOver,false);
         removeEventListener("rollOut",onOut,false);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         this.filters = [new GlowFilter(16735067,1,5,5,1,1)];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         this.filters = [];
      }
   }
}

