package UI.Equipments.StackEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class MaterialEquipmentVO extends StackEquipmentVO
   {
      
      public function MaterialEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:MaterialEquipmentVO = new MaterialEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
      }
   }
}

