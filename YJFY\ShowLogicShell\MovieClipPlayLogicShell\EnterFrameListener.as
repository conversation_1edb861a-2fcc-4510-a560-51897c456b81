package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   public class EnterFrameListener implements IEnterFrameListener
   {
      
      public var enterFrameFun:Function;
      
      public function EnterFrameListener()
      {
         super();
      }
      
      public function clear() : void
      {
         enterFrameFun = null;
      }
      
      public function enterFrame(param1:AnimationShowPlayLogicShell, param2:uint) : void
      {
         if(<PERSON><PERSON>an(enterFrameFun))
         {
            enterFrameFun(param1,param2);
         }
      }
   }
}

