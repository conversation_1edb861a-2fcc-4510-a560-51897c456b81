package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class StartGameBtn extends Btn
   {
      
      public function StartGameBtn()
      {
         super();
         setTipString("开始游戏");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("startRecaptureGoldGame"));
      }
   }
}

