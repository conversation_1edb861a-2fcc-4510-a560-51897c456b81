package YJFY.PKMode.PKData
{
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   
   public class PKTargetPlayerData
   {
      
      public static const const_pkState_noPK:int = 0;
      
      public static const const_PkState_win:int = 1;
      
      public static const const_PkState_fail:int = 2;
      
      private var m_pkIndex:uint;
      
      private var m_pkState:int;
      
      private var m_player1Type:String;
      
      private var m_uName:String;
      
      private var m_nickName:String;
      
      private var m_uid:String;
      
      private var m_index_saveXML:uint;
      
      private var m_useDataInRank:UserDataInRankList;
      
      public function PKTargetPlayerData(param1:String, param2:uint, param3:uint, param4:int, param5:String, param6:String, param7:String, param8:UserDataInRankList)
      {
         super();
         m_pkIndex = param3;
         m_pkState = param4;
         m_player1Type = param6;
         m_uName = param5;
         m_nickName = param7;
         m_uid = param1;
         m_index_saveXML = param2;
         m_useDataInRank = param8;
      }
      
      public function clear() : void
      {
         m_player1Type = null;
         m_uName = null;
         m_nickName = null;
         m_uid = null;
         m_useDataInRank = null;
      }
      
      public function updateData(param1:String, param2:String) : void
      {
         m_nickName = param2;
         m_player1Type = param1;
      }
      
      public function setPKStateToWin() : void
      {
         m_pkState = 1;
      }
      
      public function setPKStateToFail() : void
      {
         m_pkState = 2;
      }
      
      public function getPKIndex() : int
      {
         return m_pkIndex;
      }
      
      public function getPKState() : int
      {
         return m_pkState;
      }
      
      public function getPlayer1Type() : String
      {
         return m_player1Type;
      }
      
      public function getUid() : String
      {
         return m_uid;
      }
      
      public function getIndexOfSaveXML() : uint
      {
         return m_index_saveXML;
      }
      
      public function getUserDataInRank() : UserDataInRankList
      {
         return m_useDataInRank;
      }
   }
}

