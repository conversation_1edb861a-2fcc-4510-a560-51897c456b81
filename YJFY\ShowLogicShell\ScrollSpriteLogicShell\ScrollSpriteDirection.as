package YJFY.ShowLogicShell.ScrollSpriteLogicShell
{
   public class ScrollSpriteDirection
   {
      
      public static const HORIZONTAL:String = "H";
      
      public static const VERTICAL:String = "L";
      
      private var _direction:String = "L";
      
      public function ScrollSpriteDirection()
      {
         super();
      }
      
      public function getDirection() : String
      {
         return _direction;
      }
      
      public function setDirection(param1:String) : void
      {
         if(param1 != "H" && param1 != "L")
         {
            throw new Error("value 必须为 静态常量 ScrollSpriteDirection.HORIZONTAL 或 ScrollSpriteDirection.VERTICAL");
         }
         _direction = param1;
      }
   }
}

