package YJFY.Skill.HouyiSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_ArrowSkill;
   import YJFY.World.World;
   
   public class Skill_HouyiSkill1 extends CuboidAreaAttackSkill_ArrowSkill
   {
      
      public function Skill_HouyiSkill1()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

