package Json
{
   public class JSONTokenizer
   {
      
      private var strict:Boolean;
      
      private var obj:Object;
      
      private var jsonString:String;
      
      private var loc:int;
      
      private var ch:String;
      
      private var controlCharsRegExp:RegExp = /[\x00-\x1F]/;
      
      public function JSONTokenizer(param1:String, param2:Boolean)
      {
         super();
         jsonString = param1;
         this.strict = param2;
         loc = 0;
         nextChar();
      }
      
      public function getNextToken() : JSONToken
      {
         var _loc5_:String = null;
         var _loc3_:String = null;
         var _loc1_:String = null;
         var _loc4_:String = null;
         var _loc2_:JSONToken = new JSONToken();
         skipIgnored();
         switch(ch)
         {
            case "{":
               _loc2_.type = 1;
               _loc2_.value = "{";
               nextChar();
               break;
            case "}":
               _loc2_.type = 2;
               _loc2_.value = "}";
               nextChar();
               break;
            case "[":
               _loc2_.type = 3;
               _loc2_.value = "[";
               nextChar();
               break;
            case "]":
               _loc2_.type = 4;
               _loc2_.value = "]";
               nextChar();
               break;
            case ",":
               _loc2_.type = 0;
               _loc2_.value = ",";
               nextChar();
               break;
            case ":":
               _loc2_.type = 6;
               _loc2_.value = ":";
               nextChar();
               break;
            case "t":
               _loc5_ = "t" + nextChar() + nextChar() + nextChar();
               if(_loc5_ == "true")
               {
                  _loc2_.type = 7;
                  _loc2_.value = true;
                  nextChar();
               }
               else
               {
                  parseError("Expecting \'true\' but found " + _loc5_);
               }
               break;
            case "f":
               _loc3_ = "f" + nextChar() + nextChar() + nextChar() + nextChar();
               if(_loc3_ == "false")
               {
                  _loc2_.type = 8;
                  _loc2_.value = false;
                  nextChar();
               }
               else
               {
                  parseError("Expecting \'false\' but found " + _loc3_);
               }
               break;
            case "n":
               _loc1_ = "n" + nextChar() + nextChar() + nextChar();
               if(_loc1_ == "null")
               {
                  _loc2_.type = 9;
                  _loc2_.value = null;
                  nextChar();
               }
               else
               {
                  parseError("Expecting \'null\' but found " + _loc1_);
               }
               break;
            case "N":
               _loc4_ = "N" + nextChar() + nextChar();
               if(_loc4_ == "NaN")
               {
                  _loc2_.type = 12;
                  _loc2_.value = NaN;
                  nextChar();
               }
               else
               {
                  parseError("Expecting \'NaN\' but found " + _loc4_);
               }
               break;
            case "\"":
               _loc2_ = readString();
               break;
            default:
               if(isDigit(ch) || ch == "-")
               {
                  _loc2_ = readNumber();
               }
               else
               {
                  if(ch == "")
                  {
                     return null;
                  }
                  parseError("Unexpected " + ch + " encountered");
               }
         }
         return _loc2_;
      }
      
      private function readString() : JSONToken
      {
         var _loc1_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = loc;
         while(true)
         {
            _loc3_ = int(jsonString.indexOf("\"",_loc3_));
            if(_loc3_ >= 0)
            {
               _loc1_ = 0;
               _loc4_ = _loc3_ - 1;
               while(jsonString.charAt(_loc4_) == "\\")
               {
                  _loc1_++;
                  _loc4_--;
               }
               if(_loc1_ % 2 == 0)
               {
                  break;
               }
               _loc3_++;
            }
            else
            {
               parseError("Unterminated string literal");
            }
         }
         var _loc2_:JSONToken = new JSONToken();
         _loc2_.type = 10;
         _loc2_.value = unescapeString(jsonString.substr(loc,_loc3_ - loc));
         loc = _loc3_ + 1;
         nextChar();
         return _loc2_;
      }
      
      public function unescapeString(param1:String) : String
      {
         var _loc6_:int = 0;
         var _loc9_:String = null;
         var _loc4_:String = null;
         var _loc10_:* = 0;
         var _loc3_:String = null;
         if(strict && controlCharsRegExp.test(param1))
         {
            parseError("String contains unescaped control character (0x00-0x1F)");
         }
         var _loc2_:String = "";
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = param1.length;
         do
         {
            _loc5_ = int(param1.indexOf("\\",_loc7_));
            if(_loc5_ < 0)
            {
               _loc2_ += param1.substr(_loc7_);
               break;
            }
            _loc2_ += param1.substr(_loc7_,_loc5_ - _loc7_);
            _loc7_ = _loc5_ + 2;
            _loc6_ = _loc5_ + 1;
            switch(_loc9_ = param1.charAt(_loc6_))
            {
               case "\"":
                  _loc2_ += "\"";
                  break;
               case "\\":
                  _loc2_ += "\\";
                  break;
               case "n":
                  _loc2_ += "\n";
                  break;
               case "r":
                  _loc2_ += "\r";
                  break;
               case "t":
                  _loc2_ += "\t";
                  break;
               case "u":
                  _loc4_ = "";
                  if(_loc7_ + 4 > _loc8_)
                  {
                     parseError("Unexpected end of input.  Expecting 4 hex digits after \\u.");
                  }
                  _loc10_ = _loc7_;
                  while(_loc10_ < _loc7_ + 4)
                  {
                     _loc3_ = param1.charAt(_loc10_);
                     if(!isHexDigit(_loc3_))
                     {
                        parseError("Excepted a hex digit, but found: " + _loc3_);
                     }
                     _loc4_ += _loc3_;
                     _loc10_++;
                  }
                  _loc2_ += String.fromCharCode(parseInt(_loc4_,16));
                  _loc7_ += 4;
                  break;
               case "f":
                  _loc2_ += "\f";
                  break;
               case "/":
                  _loc2_ += "/";
                  break;
               case "b":
                  _loc2_ += "\b";
                  break;
               default:
                  _loc2_ += "\\" + _loc9_;
            }
         }
         while(_loc7_ < _loc8_);
         
         return _loc2_;
      }
      
      private function readNumber() : JSONToken
      {
         var _loc3_:JSONToken = null;
         var _loc2_:String = "";
         if(ch == "-")
         {
            _loc2_ += "-";
            nextChar();
         }
         if(!isDigit(ch))
         {
            parseError("Expecting a digit");
         }
         if(ch == "0")
         {
            _loc2_ += ch;
            nextChar();
            if(isDigit(ch))
            {
               parseError("A digit cannot immediately follow 0");
            }
            else if(!strict && ch == "x")
            {
               _loc2_ += ch;
               nextChar();
               if(isHexDigit(ch))
               {
                  _loc2_ += ch;
                  nextChar();
               }
               else
               {
                  parseError("Number in hex format require at least one hex digit after \"0x\"");
               }
               while(isHexDigit(ch))
               {
                  _loc2_ += ch;
                  nextChar();
               }
            }
         }
         else
         {
            while(isDigit(ch))
            {
               _loc2_ += ch;
               nextChar();
            }
         }
         if(ch == ".")
         {
            _loc2_ += ".";
            nextChar();
            if(!isDigit(ch))
            {
               parseError("Expecting a digit");
            }
            while(isDigit(ch))
            {
               _loc2_ += ch;
               nextChar();
            }
         }
         if(ch == "e" || ch == "E")
         {
            _loc2_ += "e";
            nextChar();
            if(ch == "+" || ch == "-")
            {
               _loc2_ += ch;
               nextChar();
            }
            if(!isDigit(ch))
            {
               parseError("Scientific notation number needs exponent value");
            }
            while(isDigit(ch))
            {
               _loc2_ += ch;
               nextChar();
            }
         }
         var _loc1_:Number = Number(_loc2_);
         if(isFinite(_loc1_) && !isNaN(_loc1_))
         {
            _loc3_ = new JSONToken();
            _loc3_.type = 11;
            _loc3_.value = _loc1_;
            return _loc3_;
         }
         parseError("Number " + _loc1_ + " is not valid!");
         return null;
      }
      
      private function nextChar() : String
      {
         return ch = jsonString.charAt(loc++);
      }
      
      private function skipIgnored() : void
      {
         var _loc1_:int = 0;
         do
         {
            _loc1_ = loc;
            skipWhite();
            skipComments();
         }
         while(_loc1_ != loc);
         
      }
      
      private function skipComments() : void
      {
         if(ch == "/")
         {
            nextChar();
            switch(ch)
            {
               case "/":
                  do
                  {
                     nextChar();
                  }
                  while(ch != "\n" && ch != "");
                  
                  nextChar();
                  break;
               case "*":
                  nextChar();
                  while(true)
                  {
                     if(ch == "*")
                     {
                        nextChar();
                        if(ch == "/")
                        {
                           break;
                        }
                     }
                     else
                     {
                        nextChar();
                     }
                     if(ch == "")
                     {
                        parseError("Multi-line comment not closed");
                     }
                  }
                  nextChar();
                  break;
               default:
                  parseError("Unexpected " + ch + " encountered (expecting \'/\' or \'*\' )");
            }
         }
      }
      
      private function skipWhite() : void
      {
         while(isWhiteSpace(ch))
         {
            nextChar();
         }
      }
      
      private function isWhiteSpace(param1:String) : Boolean
      {
         if(param1 == " " || param1 == "\t" || param1 == "\n" || param1 == "\r")
         {
            return true;
         }
         if(!strict && param1.charCodeAt(0) == 160)
         {
            return true;
         }
         return false;
      }
      
      private function isDigit(param1:String) : Boolean
      {
         return param1 >= "0" && param1 <= "9";
      }
      
      private function isHexDigit(param1:String) : Boolean
      {
         return isDigit(param1) || param1 >= "A" && param1 <= "F" || param1 >= "a" && param1 <= "f";
      }
      
      public function parseError(param1:String) : void
      {
         throw new JSONParseError(param1,loc,jsonString);
      }
   }
}

