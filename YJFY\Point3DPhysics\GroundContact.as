package YJFY.Point3DPhysics
{
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.ClearUtil;
   
   public class GroundContact
   {
      
      private var m_point:P3DVector3D;
      
      private var m_normal:P3DVector3D;
      
      private var m_impulse:Number;
      
      private var m_body:P3DBody;
      
      public function GroundContact(param1:P3DBody, param2:P3DVector3D, param3:P3DVector3D, param4:Number)
      {
         super();
         m_body = param1;
         m_point = new P3DVector3D();
         m_point.copy(param2);
         m_normal = new P3DVector3D();
         m_normal.copy(param3);
         m_impulse = param4;
      }
      
      public function clear() : void
      {
         m_body = null;
         ClearUtil.clearObject(m_point);
         m_point = null;
         ClearUtil.clearObject(m_normal);
         m_normal = null;
      }
      
      public function getBody() : P3DBody
      {
         return m_body;
      }
      
      public function getPoint() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_point);
         return _loc1_;
      }
      
      public function getNormal() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_normal);
         return _loc1_;
      }
      
      public function getImpulse() : Number
      {
         return m_impulse;
      }
   }
}

