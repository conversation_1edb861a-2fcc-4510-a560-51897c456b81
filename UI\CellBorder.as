package UI
{
   import UI.UIInterface.OldInterface.ICellBorder;
   
   public class CellBorder extends MySprite implements ICellBorder
   {
      
      private var _id:int;
      
      private var _color:uint;
      
      public function CellBorder()
      {
         super();
      }
      
      override public function clear() : void
      {
      }
      
      public function get color() : uint
      {
         return _color;
      }
      
      public function set color(param1:uint) : void
      {
         _color = param1;
      }
      
      public function get id() : int
      {
         return _id;
      }
      
      public function set id(param1:int) : void
      {
         _id = param1;
      }
   }
}

