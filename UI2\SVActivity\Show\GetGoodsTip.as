package UI2.SVActivity.Show
{
   import Json.MyJSON;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.API_4399.GetTimeAPI.GetTimeAPI;
   import YJFY.API_4399.GetTimeAPI.GetTimeListener;
   import YJFY.GameData;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import com.utils.MD5;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   
   public class GetGoodsTip extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var _okFun:Function;
      
      private var _Equipments:Vector.<EquipmentVO>;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_versionControl:IVersionControl;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      private var m_sureBtn:ButtonLogicShell2;
      
      private var m_charBtn:ButtonLogicShell2;
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_getTimeApi:GetTimeAPI;
      
      private var _getTimeListener:GetTimeListener;
      
      private var _time:int;
      
      private var _sendBool:Boolean = false;
      
      private var _type:int;
      
      public function GetGoodsTip()
      {
         super();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_sureBtn = new ButtonLogicShell2();
         m_charBtn = new ButtonLogicShell2();
         m_eqCells = new Vector.<Sprite>();
         _getTimeListener = new GetTimeListener();
         _getTimeListener.getTimeFun = getServerTimeSuccess;
         m_getTimeApi = GamingUI.getInstance().getAPI4399().getTimeAPI;
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_versionControl = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_charBtn);
         m_charBtn = null;
         ClearUtil.nullArr(_Equipments,false,false,false);
         m_eqCells = null;
         ClearUtil.nullArr(m_equipments);
         m_equipments = null;
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         if(m_getTimeApi)
         {
            m_getTimeApi.removeGetTimeListener(_getTimeListener);
         }
         ClearUtil.clearObject(_getTimeListener);
         _getTimeListener = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_sureBtn:
               m_svActivityPanel.hidGoodsTip();
               break;
            case m_charBtn:
               starGetServerTime();
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      public function init(param1:Vector.<EquipmentVO>, param2:Function, param3:SVActivityPanel, param4:IVersionControl, param5:int = 0) : void
      {
         _type = param5;
         _okFun = param2;
         _Equipments = param1;
         if(m_myLoader)
         {
            throw new Error("出错了");
         }
         m_versionControl = param4;
         m_svActivityPanel = param3;
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(m_versionControl);
         if(_Equipments.length > 1)
         {
            m_myLoader.getClass("UISprite2/NewYearActivityPanel.swf","getSomeGiftTip",getShowSuccess,getFail);
         }
         else
         {
            m_myLoader.getClass("UISprite2/NewYearActivityPanel.swf","getOneGiftTip",getShowSuccess,getFail);
         }
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc3_:Equipment = null;
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         m_sureBtn.setShow(m_show["show"]["sureBtn"]);
         m_sureBtn.setTipString("点击关闭");
         m_charBtn.setShow(m_show["show"]["sharBtn"]);
         m_charBtn.setTipString("点击分享");
         var _loc4_:int = 0;
         _loc4_ = 0;
         while(_loc4_ < _Equipments.length)
         {
            m_eqCells.push(m_show["show"]["equa_" + _loc4_]);
            _loc4_++;
         }
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         _loc4_ = 0;
         while(_loc4_ < _Equipments.length)
         {
            _loc3_ = MyFunction2.sheatheEquipmentShell(_Equipments[_loc4_]);
            _loc3_.addEventListener("rollOver",onOver2,false,0,true);
            _loc3_.addEventListener("rollOut",onOut2,false,0,true);
            m_eqCells[_loc4_].addChild(_loc3_);
            m_equipments.push(_loc3_);
            _loc4_++;
         }
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_svActivityPanel.hidGoodsTip();
      }
      
      private function starGetServerTime() : void
      {
         if(_sendBool)
         {
            m_svActivityPanel.showWarningBox("分享过于频繁，请勿灌水！",0);
            return;
         }
         _sendBool = true;
         _getTimeListener.getTimeAPI = m_getTimeApi;
         m_getTimeApi.addGetTimeListener(_getTimeListener);
         m_getTimeApi.getTime();
      }
      
      private function getServerTimeSuccess(param1:String) : void
      {
         m_getTimeApi.removeGetTimeListener(_getTimeListener);
         var _loc2_:Date = TimeUtil.getTimeUtil().stringToDate(param1);
         _time = _loc2_.time / 1000;
         publishedBBS();
      }
      
      private function publishedBBS() : void
      {
         var _loc9_:String = null;
         var _loc6_:URLVariables = null;
         var _loc10_:int = 0;
         _loc9_ = "http://my.4399.com/fapi/post-thread";
         var _loc14_:URLRequest = new URLRequest(_loc9_);
         _loc14_.method = "POST";
         var _loc7_:String = GameData.getInstance().getLoginReturnData().getUid();
         var _loc16_:int = 46;
         var _loc8_:int = 725;
         var _loc1_:int = 81260;
         var _loc3_:int = 1;
         var _loc2_:String = "/fapi/post-thread";
         var _loc5_:String = "我的暑期活动";
         if(_type == 1)
         {
            _loc5_ = "我在玩欢乐大转盘看看我抽到了什么宝贝吧！";
         }
         else
         {
            _loc5_ = "我在玩暑期送宝看看我开到了什么好东西！";
         }
         var _loc11_:String = "我在暑期活动中获得了：";
         if(_type == 1)
         {
            _loc11_ = "我在欢乐大转盘中抽到了:";
         }
         else
         {
            _loc11_ = "我在暑期送宝中开出了:";
         }
         _loc10_ = 0;
         while(_loc10_ < _Equipments.length)
         {
            _loc11_ = _loc11_ + _Equipments[_loc10_].name + "*1";
            _loc10_++;
         }
         if(_type == 1)
         {
            _loc11_ += "你们觉得怎么样？";
         }
         else
         {
            _loc11_ += "是不是很幸运啊？";
         }
         var _loc12_:int = _time;
         var _loc17_:String = "d74f84920cff44becb075005db0eef26";
         _loc6_ = new URLVariables();
         _loc6_.uid = _loc7_;
         _loc6_.subject = _loc5_;
         _loc6_.message = _loc11_;
         _loc6_.tagid = _loc1_;
         _loc6_.kind_id = _loc8_;
         _loc6_.time = _loc12_;
         _loc6_.app_id = _loc16_;
         _loc6_.uri = _loc2_;
         var _loc15_:String = _loc16_ + "||" + _loc8_ + "||" + _loc11_ + "||" + _loc5_ + "||" + _loc1_ + "||" + _loc12_ + "||" + _loc7_ + "||" + _loc2_ + "||" + _loc17_;
         var _loc13_:String = MD5.hash(_loc15_);
         _loc6_.token = _loc13_;
         _loc14_.data = _loc6_;
         var _loc4_:URLLoader = new URLLoader();
         _loc4_.addEventListener("complete",DoUIEvent);
         _loc4_.addEventListener("ioError",DoUIEventError);
         _loc4_.addEventListener("securityError",DoUIEvent);
         _loc4_.load(_loc14_);
      }
      
      private function DoUIEvent(param1:Event) : void
      {
         var _loc2_:String = param1.target.data;
         trace(_loc2_);
         var _loc3_:int = int(MyJSON.decode(param1.currentTarget.data).code);
         if(_loc3_ == 100)
         {
            m_svActivityPanel.showWarningBox("分享成功",0);
         }
         else if(_loc3_ == 185)
         {
            m_svActivityPanel.showWarningBox("分享过多，请勿灌水",0);
         }
         else if(_loc3_ == 153)
         {
            m_svActivityPanel.showWarningBox("发帖太多，请稍后再发",0);
         }
         else
         {
            m_svActivityPanel.showWarningBox("分享失败，清先登入论坛",0);
         }
      }
      
      private function DoUIEventError(param1:IOErrorEvent) : void
      {
         var _loc2_:String = param1.target.data;
         trace(_loc2_);
         m_svActivityPanel.showWarningBox("分享失败，清先登入论坛",0);
      }
   }
}

