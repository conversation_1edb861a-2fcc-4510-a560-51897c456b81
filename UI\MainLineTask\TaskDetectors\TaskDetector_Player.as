package UI.MainLineTask.TaskDetectors
{
   import UI.GamingUI;
   
   public class TaskDetector_Player extends TaskDetector
   {
      
      private var m_xml:XML;
      
      public function TaskDetector_Player()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_xml = param1;
      }
      
      override public function detect() : void
      {
         var _loc9_:* = false;
         var _loc8_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:IPlayerVOForTaskDetector_Player = null;
         var _loc2_:String = null;
         var _loc1_:String = null;
         var _loc3_:String = null;
         var _loc7_:Boolean = true;
         var _loc6_:XMLList = m_xml.player;
         _loc5_ = int(_loc6_ ? _loc6_.length() : 0);
         _loc8_ = 0;
         while(_loc8_ < _loc5_)
         {
            _loc2_ = String(_loc6_[_loc8_].@playerID);
            _loc1_ = String(_loc6_[_loc8_].@playerLevel);
            _loc3_ = String(_loc6_[_loc8_].@playerAttack);
            if(_loc2_ == "playerOne")
            {
               _loc4_ = GamingUI.getInstance().player1.playerVO;
            }
            else if(_loc2_ == "tuDiOne")
            {
               _loc4_ = GamingUI.getInstance().player1.playerVO.tuDiVO;
            }
            if(_loc4_ == null)
            {
               _loc7_ = false;
               break;
            }
            if(_loc1_)
            {
               if(_loc4_.level < int(_loc1_))
               {
                  _loc7_ = false;
                  break;
               }
            }
            if(_loc3_)
            {
               if(_loc4_.attack < int(_loc3_))
               {
                  _loc7_ = false;
                  break;
               }
            }
            _loc8_++;
         }
         _loc9_ = _loc7_;
         if(_loc9_)
         {
            GamingUI.getInstance().taskEvent(m_eventStr);
         }
      }
   }
}

