package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_DragonSkill2 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private var m_pushImpluse:P3DVector3D;
      
      private var m_pushImpluse2:P3DVector3D;
      
      public function Skill_DragonSkill2()
      {
         super();
         m_pushImpluse = new P3DVector3D(500000,0,0);
         m_pushImpluse2 = new P3DVector3D();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pushImpluse);
         m_pushImpluse = null;
         ClearUtil.clearObject(m_pushImpluse2);
         m_pushImpluse2 = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(param1 is TangMonkEntity)
         {
            return;
         }
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            m_pushImpluse2.multi2(m_owner.getShowDirection(),m_pushImpluse);
            param1.getBody().applyImpulse(m_pushImpluse2);
         }
      }
   }
}

