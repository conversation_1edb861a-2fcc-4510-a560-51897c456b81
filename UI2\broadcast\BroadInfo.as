package UI2.broadcast
{
   import YJFY.Utils.ClearUtil;
   
   public class BroadInfo
   {
      
      public var type:int;
      
      public var value:String;
      
      public var txtInfo:String;
      
      public var doId:int;
      
      public var name:String;
      
      public var isShowReward:int;
      
      public var rankid:int;
      
      public var hp:uint;
      
      public var score:uint;
      
      public var uid:uint;
      
      public var time:String;
      
      public var equipOriginal:Vector.<RewardInfo>;
      
      public var equipResult:Vector.<RewardInfo>;
      
      public var ids:Vector.<int>;
      
      public var idsstr:Vector.<String>;
      
      public var levels:Vector.<int>;
      
      public function BroadInfo()
      {
         super();
         equipOriginal = new Vector.<RewardInfo>();
         equipResult = new Vector.<RewardInfo>();
         ids = new Vector.<int>();
         levels = new Vector.<int>();
         idsstr = new Vector.<String>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(equipOriginal);
         equipOriginal = null;
         ClearUtil.clearObject(equipResult);
         equipResult = null;
         ClearUtil.clearObject(ids);
         ids = null;
         ClearUtil.clearObject(levels);
         levels = null;
         ClearUtil.clearObject(idsstr);
         idsstr = null;
      }
      
      public function initXML(param1:XML) : void
      {
      }
   }
}

