package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Utils.ClearUtil;
   
   public class CuboidAreaAttackSkill_EveryTargetAddShow2 extends CuboidAreaAttackSkill_EveryTargetAddShow
   {
      
      protected var m_effectAddtoTargetId:String;
      
      protected var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      protected var m_effectShowsAddtoTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_wasteEffectShowsAddToTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_effectShowsAddtoTargetPool:ObjectsPool;
      
      protected var m_effectShowReachFrameLabelListener:AnimationPlayFrameLabelListener;
      
      protected var m_animalEntitys_addEffect:Vector.<AnimalEntity>;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      public function CuboidAreaAttackSkill_EveryTargetAddShow2()
      {
         super();
         m_effectShowsAddtoTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteEffectShowsAddToTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_animalEntitys_addEffect = new Vector.<AnimalEntity>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.changeStateFun = entityChangeState;
         m_effectShowsAddtoTargetPool = new ObjectsPool(m_effectShowsAddtoTarget,m_wasteEffectShowsAddToTarget,createEffectShowAddToTarget,null);
         m_effectShowReachFrameLabelListener = new AnimationPlayFrameLabelListener();
         m_effectShowReachFrameLabelListener.reachFrameLabelFun2 = effectShowReachFrameLabel;
      }
      
      override public function clear() : void
      {
         clear2();
         m_effectAddtoTargetId = null;
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowsAddtoTarget);
         m_effectShowsAddtoTarget = null;
         ClearUtil.clearObject(m_wasteEffectShowsAddToTarget);
         m_wasteEffectShowsAddToTarget = null;
         ClearUtil.clearObject(m_effectShowsAddtoTargetPool);
         m_effectShowsAddtoTargetPool = null;
         ClearUtil.clearObject(m_animalEntitys_addEffect);
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         ClearUtil.clearObject(m_effectShowReachFrameLabelListener);
         m_effectShowReachFrameLabelListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_effectAddtoTargetId = String(param1.@effectAddtoTargetId);
         if(m_effectAddtoTargetId)
         {
            m_effectAddtoTargetDefinitionData = new AnimationDefinitionData();
            m_effectAddtoTargetDefinitionData.initByXML(param1.animationDefinition.(@id == m_effectAddtoTargetId)[0]);
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && m_attackData.getHurtDuration())
         {
            addEffectShowAddToTarget(param1);
         }
      }
      
      protected function addEffectShowAddToTarget(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if(m_effectAddtoTargetDefinitionData && param1 is IAnimalEntity)
         {
            _loc2_ = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            effectShowOfPreAddToTarget(_loc2_);
            _loc2_.extra = param1;
            _loc2_.addFrameLabelListener(m_effectShowReachFrameLabelListener);
            (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect.push(param1);
            _loc2_.gotoAndPlay("start");
            param1.addOtherAniamtion(_loc2_);
         }
      }
      
      protected function effectShowOfPreAddToTarget(param1:AnimationShowPlayLogicShell) : void
      {
      }
      
      protected function createEffectShowAddToTarget() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_effectAddtoTargetDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      protected function effectShowReachFrameLabel(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
      
      protected function entityChangeState(param1:AnimalEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1.isInHurt() == false)
         {
            _loc3_ = int(m_effectShowsAddtoTarget.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_effectShowsAddtoTarget[_loc4_].extra == param1)
               {
                  param1.removeOtherAnimation(m_effectShowsAddtoTarget[_loc4_]);
                  m_effectShowsAddtoTarget[_loc4_].removeFrameLabelListener(m_effectShowReachFrameLabelListener);
                  m_effectShowsAddtoTargetPool.wasteOneObj(m_effectShowsAddtoTarget[_loc4_]);
                  _loc4_--;
                  _loc3_--;
               }
               _loc4_++;
            }
            _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1));
            m_animalEntitys_addEffect.splice(_loc2_,1);
            _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1));
            if(_loc2_ == -1)
            {
               param1.removeAnimalEntityListener(m_animalEntityListener);
            }
         }
      }
      
      protected function clear2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_effectShowsAddtoTarget[_loc2_].extra)
            {
               (m_effectShowsAddtoTarget[_loc2_].extra as IEntity).removeOtherAnimation(m_effectShowsAddtoTarget[_loc2_]);
            }
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_].getShow());
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_]);
            m_effectShowsAddtoTarget[_loc2_] = null;
            _loc2_++;
         }
         m_effectShowsAddtoTarget.length = 0;
         _loc1_ = m_animalEntitys_addEffect ? m_animalEntitys_addEffect.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_animalEntitys_addEffect[_loc2_].removeAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect[_loc2_] = null;
            _loc2_++;
         }
         m_animalEntitys_addEffect.length = 0;
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
      }
   }
}

