package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   import flash.display.MovieClip;
   
   public class MovieClipPlayLogicShell2 extends MovieClipPlayLogicShell
   {
      
      public function MovieClipPlayLogicShell2()
      {
         super();
      }
      
      override public function setShow(param1:MovieClip) : void
      {
         super.setShow(param1);
         this.m_show.gotoAndStop(this.m_show.totalFrames);
      }
   }
}

