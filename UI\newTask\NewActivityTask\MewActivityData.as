package UI.newTask.NewActivityTask
{
   import UI.DataManagerParent;
   import UI.MyFunction;
   import UI.Task.TaskVO.AccumulatedTaskVO;
   import UI.Task.TaskVO.LimitingTimeTaskVO;
   import UI.Task.TaskVO.LimtingTimeAccumulatedTaskVO;
   import UI.Task.TaskVO.MTaskVO;
   import UI.XMLSingle;
   import UI.newTask.EveyDayTask.NewEveryDataItem;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class MewActivityData extends DataManagerParent
   {
      
      public static var _instance:MewActivityData = null;
      
      public var everyDayTaskVOs:Vector.<MTaskVO> = new Vector.<MTaskVO>();
      
      public var everyAddNums:Vector.<NewEveryDataItem> = new Vector.<NewEveryDataItem>();
      
      private var saveXML:XML;
      
      public function MewActivityData()
      {
         super();
      }
      
      public static function getInstance() : MewActivityData
      {
         if(!_instance)
         {
            _instance = new MewActivityData();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(everyDayTaskVOs)
         {
            _loc1_ = int(everyDayTaskVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               everyDayTaskVOs[_loc2_].clear();
               everyDayTaskVOs[_loc2_] = null;
               _loc2_++;
            }
         }
         everyDayTaskVOs = null;
         ClearUtil.clearObject(everyAddNums);
         everyAddNums = null;
         super.clear();
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         saveXML = param1;
         if(everyDayTaskVOs)
         {
            ClearUtil.clearObject(everyDayTaskVOs);
            everyDayTaskVOs = null;
            everyDayTaskVOs = new Vector.<MTaskVO>();
         }
         if(everyAddNums)
         {
            ClearUtil.clearObject(everyAddNums);
            everyAddNums = null;
            everyAddNums = new Vector.<NewEveryDataItem>();
         }
      }
      
      public function exportToSaveXML() : XML
      {
         var _loc8_:int = 0;
         var _loc3_:XML = null;
         var _loc6_:int = 0;
         var _loc5_:XML = null;
         var _loc1_:XML = null;
         var _loc7_:int = 0;
         var _loc2_:XML = null;
         var _loc4_:XML = <NewActivityTask></NewActivityTask>;
         _loc8_ = 0;
         while(_loc8_ < everyDayTaskVOs.length)
         {
            _loc3_ = <task></task>;
            _loc3_.@id = everyDayTaskVOs[_loc8_].id;
            _loc3_.@isGotReward = everyDayTaskVOs[_loc8_].isGotReward;
            _loc6_ = 0;
            while(_loc6_ < everyDayTaskVOs[_loc8_].taskGoalVO_ids.length)
            {
               _loc5_ = <goaltask></goaltask>;
               _loc5_.@id = everyDayTaskVOs[_loc8_].taskGoalVO_ids[_loc6_];
               _loc5_.@num = everyDayTaskVOs[_loc8_].currentTaskGoalVO_nums[_loc6_];
               _loc3_.appendChild(_loc5_);
               _loc6_++;
            }
            _loc4_.appendChild(_loc3_);
            _loc8_++;
         }
         if(everyAddNums.length > 0)
         {
            _loc1_ = <addevery></addevery>;
            _loc4_.appendChild(_loc1_);
            _loc7_ = 0;
            while(_loc7_ < everyAddNums.length)
            {
               _loc2_ = <additem></additem>;
               _loc2_.@id = everyAddNums[_loc7_].id;
               _loc2_.@num = everyAddNums[_loc7_].num;
               _loc1_.appendChild(_loc2_);
               _loc7_++;
            }
         }
         return _loc4_;
      }
      
      public function refreshData() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(everyDayTaskVOs && everyDayTaskVOs.length > 0)
         {
            _loc3_ = 0;
            while(_loc3_ < everyDayTaskVOs.length)
            {
               _loc2_ = 0;
               while(_loc2_ < everyDayTaskVOs[_loc3_].taskGoalVO_ids.length)
               {
                  _loc1_ = 0;
                  while(_loc1_ < everyAddNums.length)
                  {
                     if(everyAddNums[_loc1_].id == everyDayTaskVOs[_loc3_].taskGoalVO_ids[_loc2_])
                     {
                        var _loc4_:* = _loc2_;
                        var _loc5_:* = everyDayTaskVOs[_loc3_].currentTaskGoalVO_nums[_loc4_] + everyAddNums[_loc1_].num;
                        everyDayTaskVOs[_loc3_].currentTaskGoalVO_nums[_loc4_] = _loc5_;
                     }
                     _loc1_++;
                  }
                  _loc2_++;
               }
               _loc3_++;
            }
            everyAddNums.length = 0;
         }
      }
      
      public function initXML(param1:XML, param2:String) : void
      {
         var _loc10_:MTaskVO = null;
         var _loc11_:int = 0;
         var _loc15_:XML = null;
         var _loc3_:XMLList = null;
         var _loc9_:XMLList = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc12_:int = 0;
         var _loc14_:int = 0;
         var _loc13_:XML = null;
         var _loc6_:XMLList = null;
         var _loc17_:int = 0;
         var _loc5_:NewEveryDataItem = null;
         if(everyDayTaskVOs.length > 0)
         {
            refreshData();
            return;
         }
         everyDayTaskVOs.length = 0;
         var _loc4_:XML = XMLSingle.getInstance().taskXML;
         var _loc16_:XMLList = param1.item;
         _loc11_ = 0;
         while(_loc11_ < _loc16_.length())
         {
            if(MyFunction.getInstance().getTaskTypeFromID(_loc16_[_loc11_].@id) == 1 || MyFunction.getInstance().getTaskTypeFromID(_loc16_[_loc11_].@id) == 2 || MyFunction.getInstance().getTaskTypeFromID(_loc16_[_loc11_].@id) == 3)
            {
               _loc10_ = XMLSingle.getTask(int(_loc16_[_loc11_].@id),_loc4_);
               if(_loc10_ is LimitingTimeTaskVO)
               {
                  if(TimeUtil.getTimeUtil().getInTime((_loc10_ as LimitingTimeTaskVO).formerData,(_loc10_ as LimitingTimeTaskVO).latterData,param2))
                  {
                     everyDayTaskVOs.push(_loc10_);
                  }
               }
               else if(_loc10_ is AccumulatedTaskVO)
               {
                  if(TimeUtil.getTimeUtil().getInTime((_loc10_ as LimtingTimeAccumulatedTaskVO).formerData,(_loc10_ as LimtingTimeAccumulatedTaskVO).latterData,param2))
                  {
                     everyDayTaskVOs.push(_loc10_);
                  }
               }
               else if(_loc10_ is LimtingTimeAccumulatedTaskVO)
               {
                  if(TimeUtil.getTimeUtil().getInTime((_loc10_ as LimtingTimeAccumulatedTaskVO).formerData,(_loc10_ as LimtingTimeAccumulatedTaskVO).latterData,param2))
                  {
                     everyDayTaskVOs.push(_loc10_);
                  }
               }
            }
            _loc11_++;
         }
         if(saveXML.hasOwnProperty("NewActivityTask"))
         {
            _loc9_ = saveXML.NewActivityTask[0].task;
            _loc7_ = 0;
            while(_loc7_ < _loc9_.length())
            {
               _loc15_ = _loc9_[_loc7_];
               _loc8_ = 0;
               while(_loc8_ < everyDayTaskVOs.length)
               {
                  if(everyDayTaskVOs[_loc8_].id == int(_loc15_.@id))
                  {
                     everyDayTaskVOs[_loc8_].isGotReward = int(_loc15_.@isGotReward);
                     _loc3_ = _loc15_.goaltask;
                     _loc12_ = 0;
                     while(_loc12_ < _loc3_.length())
                     {
                        _loc14_ = 0;
                        while(_loc14_ < everyDayTaskVOs[_loc8_].taskGoalVO_ids.length)
                        {
                           if(int(_loc3_[_loc12_].@id) == everyDayTaskVOs[_loc8_].taskGoalVO_ids[_loc14_])
                           {
                              everyDayTaskVOs[_loc8_].currentTaskGoalVO_nums[_loc14_] = int(_loc3_[_loc12_].@num);
                           }
                           _loc14_++;
                        }
                        _loc12_++;
                     }
                  }
                  _loc8_++;
               }
               _loc7_++;
            }
            _loc13_ = saveXML.NewEveryTask[0];
            if(_loc13_.hasOwnProperty("addevery"))
            {
               _loc6_ = _loc13_.addevery[0].additem;
               _loc17_ = 0;
               while(_loc17_ < _loc6_.length())
               {
                  _loc5_ = new NewEveryDataItem();
                  _loc5_.id = int(_loc6_[_loc17_].@id);
                  _loc5_.num = int(_loc6_[_loc17_].@num);
                  everyAddNums.push(_loc5_);
                  _loc17_++;
               }
            }
         }
         refreshData();
      }
   }
}

