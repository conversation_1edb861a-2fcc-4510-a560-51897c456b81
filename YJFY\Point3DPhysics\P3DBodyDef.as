package YJFY.Point3DPhysics
{
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Utils.ClearUtil;
   
   public class P3DBodyDef
   {
      
      private var m_type:String;
      
      private var m_mass:Number;
      
      private var m_friction:Number;
      
      private var m_restitution:Number;
      
      private var m_velocity:P3DVector3D;
      
      private var m_location:P3DVector3D;
      
      public function P3DBodyDef()
      {
         super();
         m_type = "d";
         m_mass = 1;
         m_velocity = new P3DVector3D();
         m_location = new P3DVector3D();
      }
      
      public function clear() : void
      {
         m_type = null;
         ClearUtil.clearObject(m_velocity);
         m_velocity = null;
         ClearUtil.clearObject(m_location);
         m_location = null;
      }
      
      public function setType(param1:String) : void
      {
         if(param1 != "d" && param1 != "s")
         {
            throw new Error("设置的P3DBodyDef类型不符合要求");
         }
         m_type = param1;
      }
      
      public function setMass(param1:Number) : void
      {
         if(isNaN(param1) || param1 <= 0)
         {
            throw new Error("设置的新的BodyDef质量为NaN 或 小于等于0");
         }
         m_mass = param1;
         if(isNaN(m_mass))
         {
            throw new Error("质量出错");
         }
      }
      
      public function setFriction(param1:Number) : void
      {
         if(param1 < 0 || param1 > 1)
         {
            throw new Error("摩擦率的值必须在0-1之间");
         }
         m_friction = param1;
      }
      
      public function setRestitution(param1:Number) : void
      {
         if(isNaN(param1))
         {
            throw new Error();
         }
         if(param1 < 0 || param1 > 1)
         {
            throw new Error("反弹率的值必须在0-1之间");
         }
         m_restitution = param1;
      }
      
      public function getFriction() : Number
      {
         return m_friction;
      }
      
      public function getRestitution() : Number
      {
         return m_restitution;
      }
      
      public function setPosition(param1:Number, param2:Number, param3:Number) : void
      {
         if(isNaN(param1) || isNaN(param2) || isNaN(param3))
         {
            throw new Error("设置的P3DBodyDef新的坐标不能为NaN");
         }
         m_location.setX(param1);
         m_location.setY(param2);
         m_location.setZ(param3);
      }
      
      public function setVelocity(param1:Number, param2:Number, param3:Number) : void
      {
         m_velocity.setX(param1);
         m_velocity.setY(param2);
         m_velocity.setZ(param3);
      }
      
      public function getType() : String
      {
         return m_type;
      }
      
      public function getMass() : Number
      {
         return m_mass;
      }
      
      public function getVelocity() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_velocity);
         return _loc1_;
      }
      
      public function getPosition() : P3DVector3D
      {
         var _loc1_:P3DVector3D = new P3DVector3D();
         _loc1_.copy(m_location);
         return _loc1_;
      }
   }
}

