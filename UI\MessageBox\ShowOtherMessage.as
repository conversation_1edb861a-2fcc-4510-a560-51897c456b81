package UI.MessageBox
{
   import UI.MyFunction2;
   import UI.Players.Player;
   
   public class ShowOtherMessage
   {
      
      public function ShowOtherMessage()
      {
         super();
      }
      
      public function showMessage(param1:*, param2:MessageBox, param3:Player) : void
      {
         if(!Object(param1).hasOwnProperty("target"))
         {
            return;
         }
         if(param1.target == "experienceBar")
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText("经验：",20) + "<br>" + MessageBoxFunction.getInstance().toHTMLText(MyFunction2.transformMoneyConcise2(param1.currentVolume),20) + MessageBoxFunction.getInstance().toHTMLText("/",20) + MessageBoxFunction.getInstance().toHTMLText(MyFunction2.transformMoneyConcise2(param1.totalVolume),20) + "<br>" + MessageBoxFunction.getInstance().toHTMLText("（",20) + MessageBoxFunction.getInstance().toHTMLText((param1.currentVolume / param1.totalVolume * 100).toFixed(2),20) + MessageBoxFunction.getInstance().toHTMLText("%）",20);
         }
         else if(param1.target == "moneyText")
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText(MyFunction2.transformMoneyConcise2(param3.playerVO.money) + "元宝",20);
         }
         else if(param1.target == "charactText")
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText("BOSS掉宝率增加" + param3.playerVO.renPin / 10 + "%",20);
         }
         else if(param1.target == "showAttributeExplain" || param1.target == "text")
         {
            param2.htmlText = MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText(param1.text,15),"#ffff00");
         }
      }
   }
}

