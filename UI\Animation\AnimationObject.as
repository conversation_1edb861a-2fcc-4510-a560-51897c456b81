package UI.Animation
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   public class AnimationObject extends Bitmap
   {
      
      public static const PLAY_OVER_EVENT:String = "play over event";
      
      public var imgList:Array = [];
      
      private var timer:Timer = new Timer(0,0);
      
      private var currentFrame:int = 0;
      
      private var _stopIndex:int = 0;
      
      public function AnimationObject(param1:BitmapData = null, param2:String = "auto", param3:Boolean = false)
      {
         super(param1,param2,param3);
         timer.addEventListener("timer",onTimer,false,0,true);
         timer.addEventListener("timerComplete",clearAnimationObject,false,0,true);
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         timer.removeEventListener("timer",onTimer,false);
         timer.removeEventListener("timerComplete",clearAnimationObject,false);
         if(imgList)
         {
            _loc2_ = 0;
            _loc1_ = int(imgList.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               imgList[_loc2_] = null;
               _loc2_++;
            }
            imgList = null;
         }
         if(timer)
         {
            timer.stop();
         }
         timer = null;
      }
      
      public function endAnimationFun() : void
      {
         if(timer.running)
         {
            dispatchEvent(new Event("play over event"));
         }
      }
      
      public function play() : void
      {
         timer.reset();
         timer.start();
         onTimer(null);
      }
      
      public function stop() : void
      {
         timer.stop();
      }
      
      public function set frameRate(param1:int) : void
      {
         timer.delay = param1;
      }
      
      public function set repeatCount(param1:int) : void
      {
         timer.repeatCount = param1 * (imgList.length - 1);
      }
      
      public function set delay(param1:Number) : void
      {
         timer.delay = param1;
      }
      
      public function set stopIndex(param1:int) : void
      {
         _stopIndex = param1;
         currentFrame = _stopIndex;
         this.bitmapData = imgList[_stopIndex];
      }
      
      private function onTimer(param1:TimerEvent) : void
      {
         currentFrame++;
         if(currentFrame > imgList.length - 1)
         {
            currentFrame = _stopIndex;
            this.bitmapData = imgList[_stopIndex];
         }
         else
         {
            this.bitmapData = imgList[currentFrame];
         }
      }
      
      private function clearAnimationObject(param1:TimerEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         timer.removeEventListener("timer",onTimer,false);
         timer.removeEventListener("timerComplete",clearAnimationObject,false);
         if(imgList)
         {
            _loc2_ = int(imgList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               imgList[_loc3_] = null;
               _loc3_++;
            }
            imgList = null;
         }
         dispatchEvent(new Event("play over event"));
         if(this.parent)
         {
            parent.removeChild(this);
         }
      }
   }
}

