package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_MonkeySkill5 extends AttackSkill
   {
      
      private const m_const_produceImageFrameLabel:String = "produceImage";
      
      private const m_const_skillEndFrameLabel:String = "blow^stop^";
      
      private var m_bodyDefId:String;
      
      protected var m_imageXML:XML;
      
      private var m_createdImageEntitys:Vector.<TimeLimitEntity>;
      
      private var m_isAbleCreateImage:Boolean;
      
      private var m_monkeySkill5Listeners:Vector.<IMonkeySkill5Listener>;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      protected var m_imageDurationTime:int;
      
      public function Skill_MonkeySkill5()
      {
         super();
         m_createdImageEntitys = new Vector.<TimeLimitEntity>();
         m_isAbleCreateImage = true;
         m_monkeySkill5Listeners = new Vector.<IMonkeySkill5Listener>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.attackSuccessFun = listeneredImageAttackSuccess;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         m_bodyDefId = null;
         m_imageXML = null;
         if(m_createdImageEntitys)
         {
            _loc1_ = int(m_createdImageEntitys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_createdImageEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_createdImageEntitys[_loc2_]);
               m_createdImageEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_createdImageEntitys.length = 0;
            m_createdImageEntitys = null;
         }
         ClearUtil.nullArr(m_monkeySkill5Listeners,false,false,false);
         m_monkeySkill5Listeners = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_imageXML = param1.image[0];
         m_imageDurationTime = int(param1.@imageDurationTime);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      public function setIsAbleCreateImage(param1:Boolean) : void
      {
         m_isAbleCreateImage = param1;
      }
      
      public function addMonkeySkill5Listener(param1:IMonkeySkill5Listener) : void
      {
         m_monkeySkill5Listeners.push(param1);
      }
      
      public function removeMonkeySkill5Listener(param1:IMonkeySkill5Listener) : void
      {
         var _loc2_:int = int(m_monkeySkill5Listeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_monkeySkill5Listeners.splice(_loc2_,1);
            _loc2_ = int(m_monkeySkill5Listeners.indexOf(param1));
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      protected function createImages() : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:TimeLimitEntity_MonkeySkill5 = null;
         var _loc1_:int = 8;
         var _loc2_:int = m_world.getWorldArea().getYRange() / 100;
         _loc5_ = 0;
         while(_loc5_ < _loc1_)
         {
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               _loc4_ = new TimeLimitEntity_MonkeySkill5();
               _loc4_.setOwner(m_owner);
               _loc4_.setMyLoader(m_myLoader);
               _loc4_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
               _loc4_.setSoundManager(m_world.getSoundManager());
               _loc4_.setDisappearTime(m_imageDurationTime,"^stop^");
               _loc4_.initByXML(m_imageXML);
               _loc4_.setNewPosition(m_world.getCamera().getMinX() + 60 + _loc5_ * 120,m_world.getWorldArea().getMinY() + 50 + _loc3_ * 100,0);
               _loc4_.attack();
               m_world.addEntity(_loc4_);
               m_createdImageEntitys.push(_loc4_);
               _loc4_.addAnimalEntityListener(m_animalEntityListener);
               _loc3_++;
            }
            _loc5_++;
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case "produceImage":
               if(m_isRun)
               {
                  createImages();
               }
               return;
            case "blow^stop^":
               endSkill2();
         }
      }
      
      protected function createImages2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<IMonkeySkill5Listener> = m_monkeySkill5Listeners.slice(0);
         var _loc2_:int = int(_loc3_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_[_loc4_].createImage(this,param1);
            _loc3_[_loc4_] = null;
            _loc4_++;
         }
         _loc3_.length = 0;
         _loc3_ = null;
      }
      
      protected function listeneredImageAttackSuccess(param1:IEntity, param2:AttackSkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as TimeLimitEntity).setAttackData(m_attackData);
      }
   }
}

