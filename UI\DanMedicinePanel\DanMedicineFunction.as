package UI.DanMedicinePanel
{
   import UI.Equipments.EquipmentVO.DanMedicineEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Players.Player;
   
   public class DanMedicineFunction
   {
      
      private static var _instance:DanMedicineFunction;
      
      public function DanMedicineFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : DanMedicineFunction
      {
         if(!_instance)
         {
            _instance = new DanMedicineFunction();
         }
         return _instance;
      }
      
      public function addEquipmentVOToDanMedicineEquipmentGrid(param1:EquipmentVO, param2:Player) : Array
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:* = undefined;
         if(param1.equipmentType != "danMedicine")
         {
            return [false,"该装备不是丹药"];
         }
         if((param1 as DanMedicineEquipmentVO).danMedicineType == "attack")
         {
            _loc6_ = param2.playerVO.attackDanMedicineEquipmentVOGrid;
         }
         else
         {
            _loc6_ = param2.playerVO.defenceDanMedicineEquipmentVOGrid;
         }
         _loc3_ = 7;
         _loc7_ = 0;
         while(_loc7_ < _loc3_)
         {
            _loc4_ = 2 + _loc7_ * 1;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(!_loc6_[_loc7_][_loc5_])
               {
                  if((param1 as DanMedicineEquipmentVO).enablePutLine != _loc7_ + 1)
                  {
                     if((param1 as DanMedicineEquipmentVO).enablePutLine < _loc7_ + 1)
                     {
                        return [false,"该品级丹药已无作用！请服用其他丹药！"];
                     }
                     return [false,"丹药必须按顺序服用！现在还不能服用该丹药！"];
                  }
                  _loc6_[_loc7_][_loc5_] = param1;
                  return [true,"服用成功，身体骨骼发生变化!"];
               }
               _loc5_++;
            }
            _loc7_++;
         }
         return [false," 该品级丹药已无作用！"];
      }
      
      public function calculationTotalValue(param1:Vector.<Vector.<EquipmentVO>>) : int
      {
         var _loc8_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = int(param1.length);
         _loc8_ = 0;
         while(_loc8_ < _loc2_)
         {
            _loc4_ = int(param1[_loc8_].length);
            if(param1[_loc8_][0])
            {
               _loc3_ = (param1[_loc8_][0] as DanMedicineEquipmentVO).maxValue;
               _loc6_ = (param1[_loc8_][0] as DanMedicineEquipmentVO).reduceValue;
               _loc5_ = 0;
               while(_loc5_ < _loc4_)
               {
                  if(param1[_loc8_][_loc5_])
                  {
                     _loc7_ += _loc3_ - _loc5_ * _loc6_;
                  }
                  _loc5_++;
               }
            }
            _loc8_++;
         }
         return _loc7_;
      }
   }
}

