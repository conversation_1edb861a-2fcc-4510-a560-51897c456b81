package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.GamingUI;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyMemberList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.MemberDataInMemberList;
   
   public class TEST_DOWN_TheSocietyMemberList extends DOWN_TheSocietyMemberList
   {
      
      public function TEST_DOWN_TheSocietyMemberList()
      {
         super();
         m_informationBodyId = 3009;
      }
      
      public function initData(param1:int, param2:Vector.<MemberDataInMemberList>) : void
      {
         m_memberDatas = param2;
         m_memberTotalNum = param1;
         m_dataDeadTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + 60000;
      }
   }
}

