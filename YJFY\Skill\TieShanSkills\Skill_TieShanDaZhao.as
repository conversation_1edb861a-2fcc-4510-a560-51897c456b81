package YJFY.Skill.TieShanSkills
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_DogBigSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   
   public class Skill_TieShanDaZhao extends CuboidAreaAttackSkill2_DogBigSkill
   {
      
      private var m_const_shakeViewFrameLabel:String = "shakeView";
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      private var isCanDown:Boolean = false;
      
      public function Skill_TieShanDaZhao()
      {
         super();
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         (m_owner.getExtra() as TieShan).removeSkillBuff();
         releaseSkill2(param1);
         isCanDown = false;
         return true;
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && param1 is IAnimalEntity)
         {
            _loc2_ = 0;
            _loc4_ = 0;
            _loc3_ = 0;
            if(m_cuboidRangeToWorld.getX() + m_cuboidRangeToWorld.getXRange() / 2 < param1.getX())
            {
               _loc2_ = -50;
            }
            else
            {
               _loc2_ = 50;
            }
            if(m_cuboidRangeToWorld.getY() + m_cuboidRangeToWorld.getYRange() / 2 < param1.getY())
            {
               _loc4_ = -50;
            }
            else
            {
               _loc4_ = 50;
            }
            if(Math.abs(m_cuboidRangeToWorld.getY() + m_cuboidRangeToWorld.getYRange() / 2 - param1.getY()) < 50 && Math.abs(m_cuboidRangeToWorld.getX() + m_cuboidRangeToWorld.getXRange() / 2 - param1.getX()) < 50 && param1.getZ() < 300)
            {
               _loc3_ = 50;
            }
            param1.setNewPosition(param1.getX() + _loc2_,param1.getY() + _loc4_,isCanDown ? 0 : param1.getZ() + _loc3_);
         }
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_const_shakeViewFrameLabel:
               m_world.shakeViewOutOfTime(m_shakeViewDataOfPoints);
               break;
            case "downEnemys":
               isCanDown = true;
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

