package YJFY.Point3DPhysics.P3DMath
{
   public class P3DVector3D
   {
      
      private var m_x:Number;
      
      private var m_y:Number;
      
      private var m_z:Number;
      
      public function P3DVector3D(param1:Number = 0, param2:Number = 0, param3:Number = 0)
      {
         super();
         m_x = param1;
         m_y = param2;
         m_z = param3;
      }
      
      public function clear() : void
      {
      }
      
      public function clone() : P3DVector3D
      {
         return new P3DVector3D(m_x,m_y,m_z);
      }
      
      public function copy(param1:P3DVector3D) : void
      {
         m_x = param1.m_x;
         m_y = param1.m_y;
         m_z = param1.m_z;
      }
      
      public function setTo(param1:Number, param2:Number, param3:Number) : void
      {
         m_x = param1;
         m_y = param2;
         m_z = param3;
      }
      
      public function multi(param1:Number) : P3DVector3D
      {
         return new P3DVector3D(m_x * param1,m_y * param1,m_z * param1);
      }
      
      public function multi2(param1:Number, param2:P3DVector3D) : void
      {
         this.setTo(param2.getX() * param1,param2.getY() * param1,param2.getZ() * param1);
      }
      
      public function normalize() : Number
      {
         var _loc1_:int = Math.sqrt(Math.pow(m_x,2) + Math.pow(m_y,2) + Math.pow(m_z,2));
         if(_loc1_)
         {
            m_x /= _loc1_;
            m_y /= _loc1_;
            m_z /= _loc1_;
         }
         return _loc1_;
      }
      
      public function getLength() : Number
      {
         return Math.sqrt(Math.pow(m_x,2) + Math.pow(m_y,2) + Math.pow(m_z,2));
      }
      
      public function negate() : void
      {
         m_x = -m_x;
         m_y = -m_y;
         m_z = -m_z;
      }
      
      public function add(param1:P3DVector3D) : void
      {
         m_x += param1.m_x;
         m_y += param1.m_y;
         m_z += param1.m_z;
      }
      
      public function setX(param1:Number) : void
      {
         m_x = param1;
      }
      
      public function setY(param1:Number) : void
      {
         m_y = param1;
      }
      
      public function setZ(param1:Number) : void
      {
         m_z = param1;
      }
      
      public function getX() : Number
      {
         return m_x;
      }
      
      public function getY() : Number
      {
         return m_y;
      }
      
      public function getZ() : Number
      {
         return m_z;
      }
   }
}

