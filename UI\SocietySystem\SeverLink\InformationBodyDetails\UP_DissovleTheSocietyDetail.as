package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_DissovleTheSocietyDetail extends InformationBodyDetail
   {
      
      private var m_uid_leader:Number;
      
      private var m_idx_leader:int;
      
      public function UP_DissovleTheSocietyDetail()
      {
         super();
         m_informationBodyId = 3024;
      }
      
      public function initData(param1:Number, param2:int) : void
      {
         m_uid_leader = param1;
         m_idx_leader = param2;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid_leader,m_idx_leader);
         return _loc1_;
      }
   }
}

