package UI.Players.ImplicitProPlayer
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MessageBox.MessageBoxFunction;
   import UI.Players.PlayerVO;
   import YJFY.Utils.AttributeNameAndValue;
   import YJFY.Utils.ClearUtil;
   
   public class SuitOfAbleEquipmentNum extends ImplicitProPlayer
   {
      
      private var m_promotedAttributeNameAndValues:Vector.<AttributeNameAndValue>;
      
      public function SuitOfAbleEquipmentNum()
      {
         super();
         m_promotedAttributeNameAndValues = new Vector.<AttributeNameAndValue>();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_promotedAttributeNameAndValues);
         m_promotedAttributeNameAndValues = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      override public function recoverPlayer(param1:PlayerVO) : void
      {
         var _loc3_:int = 0;
         super.recoverPlayer(param1);
         var _loc2_:int = int(m_promotedAttributeNameAndValues.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            param1.set(m_promotedAttributeNameAndValues[_loc3_].getAttributeName(),String(Number(param1.get(m_promotedAttributeNameAndValues[_loc3_].getAttributeName())) - Number(m_promotedAttributeNameAndValues[_loc3_].getAttributeValue())));
            _loc3_++;
         }
         ClearUtil.clearObject(m_promotedAttributeNameAndValues);
         m_promotedAttributeNameAndValues.length = 0;
      }
      
      override public function judgeAndProPlayer(param1:PlayerVO) : void
      {
         var _loc2_:int = 0;
         var _loc9_:int = 0;
         var _loc4_:String = null;
         super.judgeAndProPlayer(param1);
         var _loc7_:int = int(m_xml.@maxSuitNum);
         var _loc3_:String = String(m_xml.@suitName);
         for each(var _loc8_ in param1.inforEquipmentVOs)
         {
            if(_loc8_ && _loc8_ is AbleEquipmentVO && (_loc8_ as AbleEquipmentVO).implictProPlayerId == m_id)
            {
               _loc2_++;
            }
         }
         var _loc6_:XMLList = m_xml.promote;
         var _loc5_:int = int(_loc6_.length());
         m_proDescription = MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText(_loc3_ + "（" + _loc2_ + "/" + _loc7_ + "）",15));
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            if(int(_loc6_[_loc9_].@suitNum) > _loc2_)
            {
               _loc4_ = "#999999";
            }
            else
            {
               _loc4_ = "00ff00";
               param1.set(String(_loc6_[_loc9_].@attribute),String(Number(param1.get(_loc6_[_loc9_].@attribute)) + Number(_loc6_[_loc9_].@value)));
               m_promotedAttributeNameAndValues.push(new AttributeNameAndValue(String(_loc6_[_loc9_].@attribute),_loc6_[_loc9_].@value));
               m_isProPlayer = true;
            }
            m_proDescription += MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().addHTMLTextColor(MessageBoxFunction.getInstance().toHTMLText(_loc6_[_loc9_].@description,14),_loc4_);
            _loc9_++;
         }
         m_proDescription += MessageBoxFunction.getInstance().toHTMLText("<br>") + MessageBoxFunction.getInstance().toHTMLText("<br>");
      }
   }
}

