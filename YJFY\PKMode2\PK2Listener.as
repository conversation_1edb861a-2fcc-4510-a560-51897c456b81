package YJFY.PKMode2
{
   public class PK2Listener implements IPK2Listener
   {
      
      public var matchCompleteFun:Function;
      
      public function PK2Listener()
      {
         super();
      }
      
      public function clear() : void
      {
         matchCompleteFun = null;
      }
      
      public function matchComplete(param1:PK2) : void
      {
         if(<PERSON><PERSON>an(matchCompleteFun))
         {
            matchCompleteFun(param1);
         }
      }
   }
}

