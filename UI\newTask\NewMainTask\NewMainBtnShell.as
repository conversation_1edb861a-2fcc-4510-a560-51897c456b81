package UI.newTask.NewMainTask
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MainLineTask.ChoiceGetRewardPlayerPanel;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI.newTask.NewTaskPanel;
   import YJFY.GameEvent;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class NewMainBtnShell
   {
      
      private var m_newtaskpanel:NewTaskPanel;
      
      private var m_newmaintaskpanel:NewMainTaskPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_DoneBtn:ButtonLogicShell2;
      
      private var m_GetBtn:ButtonLogicShell2;
      
      private var m_GoBtn:ButtonLogicShell2;
      
      private var m_data:MainLineTaskVO;
      
      private var m_choiceGetRewardPlayerPanel:ChoiceGetRewardPlayerPanel;
      
      public function NewMainBtnShell()
      {
         super();
      }
      
      public function clear() : void
      {
         hide();
         if(m_DoneBtn)
         {
            ClearUtil.clearObject(m_DoneBtn);
         }
         m_DoneBtn = null;
         if(m_GetBtn)
         {
            ClearUtil.clearObject(m_GetBtn);
         }
         m_GetBtn = null;
         if(m_GoBtn)
         {
            ClearUtil.clearObject(m_GoBtn);
         }
         m_GoBtn = null;
         ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
         m_choiceGetRewardPlayerPanel = null;
         m_mc.removeEventListener("clickButton",clickButton,true);
         if(m_choiceGetRewardPlayerPanel)
         {
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
      }
      
      public function init(param1:NewTaskPanel, param2:MovieClip, param3:NewMainTaskPanel) : void
      {
         m_newtaskpanel = param1;
         m_newmaintaskpanel = param3;
         m_show = param2;
         m_mc = m_show["btnshell_1"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_DoneBtn = new ButtonLogicShell2();
         m_GetBtn = new ButtonLogicShell2();
         m_GoBtn = new ButtonLogicShell2();
         m_DoneBtn.setShow(m_mc["donebtn"]);
         m_GetBtn.setShow(m_mc["getbtn"]);
         m_GoBtn.setShow(m_mc["gobtn"]);
         m_DoneBtn.getShow().visible = false;
         m_GetBtn.getShow().visible = false;
         m_GoBtn.getShow().visible = false;
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function show() : void
      {
      }
      
      public function hide() : void
      {
      }
      
      private function getTaskReward(param1:Player) : void
      {
         var player:Player = param1;
         var getTaskRewardListener:GetRewardListener = new GetRewardListener();
         getTaskRewardListener.notChoiceEquipmentVOsFun = function():void
         {
            showWarningBox("请选择要获得的奖励！",0);
         };
         getTaskRewardListener.packageNotHaveSpaceFun = function():void
         {
            showWarningBox("背包空间不足！",0);
         };
         getTaskRewardListener.rewardHaveBeGotFun = function():void
         {
            showWarningBox("奖励已被领取！",0);
         };
         getTaskRewardListener.successGetRewardFun = function():void
         {
            m_data.isGotReward = true;
            showWarningBox("奖励领取成功！",0);
            runTaskDetectorsInTaskList();
            if(NewMainTaskData.getInstance().gotoNextTask())
            {
               m_newmaintaskpanel.getMainList().setFirst(false);
               m_newmaintaskpanel.getMainList().arrangeList();
            }
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("hidetip"));
            GameEvent.eventDispacher.dispatchEvent(new GameEvent("refreshtip"));
         };
         m_data.getTaskReward(player,getTaskRewardListener);
      }
      
      private function runTaskDetectorsInTaskList() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         if(Boolean(m_data) && Boolean(m_data.taskDetectors) && m_data.taskDetectors.length)
         {
            _loc1_ = int(m_data.taskDetectors.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               m_data.taskDetectors[_loc2_].detect();
               _loc2_++;
            }
         }
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChild(WarningBoxSingle.getInstance());
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_GetBtn:
               if(GamingUI.getInstance().player2)
               {
                  if(m_choiceGetRewardPlayerPanel == null)
                  {
                     m_choiceGetRewardPlayerPanel = new ChoiceGetRewardPlayerPanel();
                  }
                  m_choiceGetRewardPlayerPanel.x = -200;
                  m_choiceGetRewardPlayerPanel.y = -300;
                  m_mc.addChild(m_choiceGetRewardPlayerPanel);
               }
               else
               {
                  getTaskReward(GamingUI.getInstance().player1);
               }
               break;
            case m_GoBtn:
               if(m_data.gotoInfo)
               {
                  if(m_data.gotoInfo.type == "1" || m_data.gotoInfo.type == "2")
                  {
                     m_newtaskpanel.CloseUI(null);
                     Part1.getInstance().closeCityMap();
                     Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                  }
                  else if(m_data.gotoInfo.type == "3" || m_data.gotoInfo.type == "4")
                  {
                     m_newtaskpanel.CloseUI(null);
                     GamingUI.getInstance().openWeaponMake();
                  }
                  else if(m_data.gotoInfo.type == "5")
                  {
                     m_newtaskpanel.CloseUI(null);
                     GamingUI.getInstance().openHatchPanel();
                  }
                  else if(m_data.gotoInfo.type == "6")
                  {
                     m_newtaskpanel.CloseUI(null);
                     GamingUI.getInstance().enterToNewSign();
                  }
                  else if(m_data.gotoInfo.type == "7")
                  {
                     m_newtaskpanel.CloseUI(null);
                     GamingUI.getInstance().openMiragePanel();
                  }
                  else if(m_data.gotoInfo.type == "8")
                  {
                     m_newtaskpanel.CloseUI(null);
                     GamingUI.getInstance().openAdvancePetPanel();
                  }
                  else if(m_data.gotoInfo.type == "9")
                  {
                     if(GamingUI.getInstance().player1.playerVO.level >= 30)
                     {
                        m_newtaskpanel.CloseUI(null);
                        Part1.getInstance().openPK("onePK");
                     }
                     else
                     {
                        GamingUI.getInstance().showMessageTip("30级之后才能挑战");
                     }
                  }
               }
               break;
            case m_DoneBtn:
         }
         if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn1)
         {
            getTaskReward(GamingUI.getInstance().player1);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
         else if(m_choiceGetRewardPlayerPanel != null && param1.button == m_choiceGetRewardPlayerPanel.btn2)
         {
            getTaskReward(GamingUI.getInstance().player2);
            ClearUtil.clearObject(m_choiceGetRewardPlayerPanel);
            m_choiceGetRewardPlayerPanel = null;
         }
      }
      
      public function refreshScript(param1:MainLineTaskVO) : void
      {
         m_data = param1;
         allhide();
         if(m_data)
         {
            if(m_data.isGotReward)
            {
               showDone();
            }
            else if(m_data.isWorking)
            {
               if(m_data.isFinish)
               {
                  showGet();
               }
               else
               {
                  showGO();
               }
            }
         }
      }
      
      private function allhide() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showDone() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showGet() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = true;
            m_GetBtn.unLock();
         }
      }
      
      private function showGO() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = true;
            m_GoBtn.unLock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      public function getGoBtn() : ButtonLogicShell2
      {
         return m_GoBtn;
      }
      
      public function getGetBtn() : ButtonLogicShell2
      {
         return m_GetBtn;
      }
      
      public function getDoneBtn() : ButtonLogicShell2
      {
         return m_DoneBtn;
      }
   }
}

import UI.MainLineTask.IGetTaskRewardListener;

class GetRewardListener implements IGetTaskRewardListener
{
   
   public var successGetRewardFun:Function;
   
   public var taskNotFinishFun:Function;
   
   public var rewardHaveBeGotFun:Function;
   
   public var packageNotHaveSpaceFun:Function;
   
   public var notChoiceEquipmentVOsFun:Function;
   
   public function GetRewardListener()
   {
      super();
   }
   
   public function clear() : void
   {
      successGetRewardFun = null;
      taskNotFinishFun = null;
      rewardHaveBeGotFun = null;
      packageNotHaveSpaceFun = null;
      notChoiceEquipmentVOsFun = null;
   }
   
   public function successGetReward() : void
   {
      if(successGetRewardFun)
      {
         successGetRewardFun();
      }
      clear();
   }
   
   public function taskNotFinish() : void
   {
      if(taskNotFinishFun)
      {
         taskNotFinishFun();
      }
      clear();
   }
   
   public function rewardHaveBeGot() : void
   {
      if(rewardHaveBeGotFun)
      {
         rewardHaveBeGotFun();
      }
      clear();
   }
   
   public function packageNotHaveSpace() : void
   {
      if(packageNotHaveSpaceFun)
      {
         packageNotHaveSpaceFun();
      }
      clear();
   }
   
   public function notChoiceEquipmentVOs() : void
   {
      if(notChoiceEquipmentVOsFun)
      {
         notChoiceEquipmentVOsFun();
      }
      clear();
   }
}
