package UI2.tehui
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class TeHuiData
   {
      
      private static var _instance:TeHuiData;
      
      public var thList:Vector.<THData>;
      
      private var dataTime:String;
      
      public function TeHuiData()
      {
         super();
         thList = new Vector.<THData>();
      }
      
      public static function getInstance() : TeHuiData
      {
         if(_instance == null)
         {
            _instance = new TeHuiData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(thList);
         thList = null;
      }
      
      public function addData(param1:int) : void
      {
         var _loc4_:int = 0;
         var _loc3_:THData = null;
         var _loc2_:Boolean = false;
         _loc4_ = 0;
         while(_loc4_ < thList.length)
         {
            if(thList[_loc4_].goodsId == param1)
            {
               _loc2_ = true;
               thList[_loc4_].buyNum++;
            }
            _loc4_++;
         }
         if(_loc2_ == false)
         {
            _loc3_ = new THData();
            _loc3_.buyNum = 1;
            _loc3_.goodsId = param1;
            thList.push(_loc3_);
         }
      }
      
      public function getBuyNum(param1:int) : int
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < thList.length)
         {
            if(thList[_loc2_].goodsId == param1)
            {
               return thList[_loc2_].buyNum;
            }
            _loc2_++;
         }
         return 0;
      }
      
      public function initSaveData(param1:XML) : void
      {
         var _loc3_:XMLList = null;
         var _loc4_:int = 0;
         var _loc2_:THData = null;
         thList.length = 0;
         if(param1.hasOwnProperty("TeHui"))
         {
            dataTime = String(param1.TeHui[0].@time);
            _loc3_ = param1.TeHui[0].dataone;
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length())
            {
               _loc2_ = new THData();
               _loc2_.goodsId = int(_loc3_[_loc4_].@goodsid);
               _loc2_.buyNum = int(_loc3_[_loc4_].@buynum);
               thList.push(_loc2_);
               _loc4_++;
            }
         }
         else
         {
            dataTime = TimeUtil.getTimeUtil().getTimeStr();
         }
      }
      
      public function exploreData() : XML
      {
         var _loc3_:int = 0;
         var _loc1_:XML = null;
         var _loc2_:XML = <TeHui />;
         if(dataTime)
         {
            _loc2_.@time = dataTime;
         }
         _loc3_ = 0;
         while(_loc3_ < thList.length)
         {
            _loc1_ = <dataone />;
            _loc1_.@goodsid = String(thList[_loc3_].goodsId);
            _loc1_.@buynum = String(thList[_loc3_].buyNum);
            _loc2_.appendChild(_loc1_);
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function checkNewWeek(param1:String) : void
      {
         if(TimeUtil.getTimeUtil().newTimeIsNewWeek(dataTime,param1))
         {
            thList.length = 0;
         }
         dataTime = param1;
      }
   }
}

