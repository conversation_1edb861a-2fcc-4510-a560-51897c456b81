package YJFY
{
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   
   public class LoadPartOne
   {
      
      private var m_myLoader:YJFYLoader;
      
      private var m_wantLoadSwfPaths:Vector.<String>;
      
      private var m_loadedNum:int;
      
      private var m_completeFuns:Vector.<Function>;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      public function LoadPartOne()
      {
         super();
         m_wantLoadSwfPaths = new Vector.<String>();
         m_completeFuns = new Vector.<Function>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_wantLoadSwfPaths);
         m_wantLoadSwfPaths = null;
         ClearUtil.clearObject(m_completeFuns);
         m_completeFuns = null;
         m_loadUI = null;
         m_versionControl = null;
      }
      
      public function init(param1:IProgressShow, param2:IVersionControl) : void
      {
         m_loadUI = param1;
         m_versionControl = param2;
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
      }
      
      public function addLoadSwfPath(param1:String) : void
      {
         m_wantLoadSwfPaths.push(param1);
      }
      
      public function addCompleteFun(param1:Function) : void
      {
         m_completeFuns.push(param1);
      }
      
      public function load() : void
      {
         var _loc2_:int = 0;
         m_loadedNum = 0;
         var _loc1_:int = int(m_wantLoadSwfPaths.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_myLoader.getClass(m_wantLoadSwfPaths[_loc2_],null,getSuccess,getFail,null,null,null,null,false);
            _loc2_++;
         }
         if(_loc1_)
         {
            m_myLoader.load();
         }
      }
      
      private function getSuccess(param1:YJFYLoaderData) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         ++m_loadedNum;
         if(m_loadedNum == m_wantLoadSwfPaths.length)
         {
            _loc2_ = int(m_completeFuns.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(Boolean(m_completeFuns[_loc3_]))
               {
                  m_completeFuns[_loc3_]();
               }
               m_completeFuns[_loc3_] = null;
               _loc3_++;
            }
            m_completeFuns.length = 0;
         }
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         throw new Error("加载失败, path:",param1.swfPath);
      }
   }
}

