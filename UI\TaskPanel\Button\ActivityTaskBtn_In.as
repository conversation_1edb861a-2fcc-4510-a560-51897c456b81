package UI.TaskPanel.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class ActivityTaskBtn_In extends SwitchBtn
   {
      
      public function ActivityTaskBtn_In()
      {
         super();
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToActivityTask"));
      }
   }
}

