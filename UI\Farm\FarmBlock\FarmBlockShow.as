package UI.Farm.FarmBlock
{
   import UI.Farm.CoordGrid.CoordGrid;
   import UI.Farm.CoordGrid.SceneCoordGrids;
   import UI.Farm.Farm;
   import UI.Farm.MouseManager.MouseManager;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import YJFY.GameData;
   import flash.display.Shape;
   import flash.events.MouseEvent;
   import flash.utils.getTimer;
   
   public class FarmBlockShow extends MySprite
   {
      
      private const _TIME:int = 200;
      
      private var _intervalTime:int;
      
      public var farmBlockVO:FarmBlockVO;
      
      private var _listenerList:Array = [];
      
      public function FarmBlockShow(param1:FarmBlockVO)
      {
         super();
         this.farmBlockVO = param1;
         drawBlock(param1);
         addEventListener("rollOver",onRollOver,false,0,true);
         addEventListener("rollOut",onRollOut,false,0,true);
         addEventListener("mouseDown",onMouseDown,false,0,true);
         addEventListener("mouseUp",onMouseUp,false,0,true);
         this.alpha = 0.1;
      }
      
      override public function clear() : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         super.clear();
         destory();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         farmBlockVO = null;
         if(_listenerList)
         {
            _loc2_ = int(_listenerList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_listenerList[_loc3_])
               {
                  for(var _loc1_ in _listenerList[_loc3_])
                  {
                     _listenerList[_loc3_][_loc1_] = null;
                  }
               }
               _listenerList[_loc3_] = null;
               _loc3_++;
            }
         }
         _listenerList = null;
      }
      
      public function refreshDraw() : void
      {
         graphics.clear();
         drawBlock(farmBlockVO);
      }
      
      private function onMouseDown(param1:MouseEvent) : void
      {
         _intervalTime = getTimer();
      }
      
      private function onMouseUp(param1:MouseEvent) : void
      {
         var _loc2_:Farm = null;
         var _loc5_:Number = NaN;
         var _loc4_:String = null;
         var _loc3_:Object = null;
         _intervalTime = getTimer() - _intervalTime;
         if(MouseManager.getInstance().state != "normal")
         {
            return;
         }
         if(_intervalTime < 200 && farmBlockVO.isDeveloped == false)
         {
            _loc2_ = MyFunction2.judgeParentsIsTheClass(this,Farm,GamingUI.getInstance()) as Farm;
            if(_loc2_)
            {
               _loc5_ = Number(XMLSingle.getInstance().farmXML.farmBlock.(@id == farmBlockVO.id)[0].@ticket);
               _loc4_ = String(XMLSingle.getInstance().farmXML.farmBlock.(@id == farmBlockVO.id)[0].@ticketId);
               _loc3_ = {};
               _loc3_["propId"] = _loc4_;
               _loc3_["count"] = 1;
               _loc3_["price"] = _loc5_;
               _loc3_["idx"] = GameData.getInstance().getSaveFileData().index;
               _loc3_["tag"] = "购买土地摆放区块";
               _loc2_.showWarningBox("是否花费 <font size=\'" + (WarningBoxSingle.getInstance().getTextFontSize() + 9) + "\' color=\'#ff0000\'>" + _loc5_ + "</font>点券开启该土地！",1 | 2,{
                  "type":"buyFarmBlock",
                  "buyDataObj":_loc3_,
                  "farmBlock":this
               });
            }
         }
      }
      
      private function onRollOver(param1:MouseEvent) : void
      {
         var _loc6_:Shape = null;
         var _loc2_:CoordGrid = null;
         var _loc3_:CoordGrid = null;
         var _loc4_:CoordGrid = null;
         var _loc5_:CoordGrid = null;
         if(!farmBlockVO.isDeveloped)
         {
            _loc6_ = new Shape();
            _loc6_.graphics.lineStyle(2,16711680,1);
            _loc6_.graphics.beginFill(16711680,1);
            _loc2_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.rightMaxCo,farmBlockVO.upMinCo);
            _loc3_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.rightMaxCo,farmBlockVO.downMaxCo);
            _loc4_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.leftMinCo,farmBlockVO.downMaxCo);
            _loc5_ = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.leftMinCo,farmBlockVO.upMinCo);
            _loc6_.graphics.moveTo(_loc2_.x,_loc2_.y - 30 / 2);
            _loc6_.graphics.lineTo(_loc3_.x + 60 / 2,_loc3_.y);
            _loc6_.graphics.lineTo(_loc4_.x,_loc4_.y + 30 / 2);
            _loc6_.graphics.lineTo(_loc5_.x - 60 / 2,_loc5_.y);
            _loc6_.graphics.endFill();
            addChild(_loc6_);
         }
      }
      
      private function onRollOut(param1:MouseEvent) : void
      {
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
      }
      
      private function drawBlock(param1:FarmBlockVO) : void
      {
         graphics.lineStyle(2,farmBlockVO.isDeveloped ? 16776960 : 16711680,farmBlockVO.isDeveloped ? 0 : 0.8);
         graphics.beginFill(16777215,0);
         var _loc2_:CoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.rightMaxCo,farmBlockVO.upMinCo);
         var _loc3_:CoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.rightMaxCo,farmBlockVO.downMaxCo);
         var _loc4_:CoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.leftMinCo,farmBlockVO.downMaxCo);
         var _loc5_:CoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(farmBlockVO.leftMinCo,farmBlockVO.upMinCo);
         graphics.moveTo(_loc2_.x,_loc2_.y - 30 / 2);
         graphics.lineTo(_loc3_.x + 60 / 2,_loc3_.y);
         graphics.lineTo(_loc4_.x,_loc4_.y + 30 / 2);
         graphics.lineTo(_loc5_.x - 60 / 2,_loc5_.y);
         graphics.endFill();
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(_listenerList.length);
         _loc5_ = 0;
         while(_loc5_ < _listenerList.length)
         {
            if(_listenerList[_loc5_].type == param1 && _listenerList[_loc5_].listener == param2)
            {
               _listenerList.splice(_loc5_,1);
               super.removeEventListener(param1,param2,param3);
               _loc5_--;
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
   }
}

