package UI.LogicShell
{
   import GM_UI.GMData;
   import UI.MyFont.FangZhengKaTongJianTi;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.ButtonLogicShell2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.SoundData;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class ButtonLogicShell2 extends YJFY.ShowLogicShell.ButtonLogicShell2
   {
      
      protected var m_buttonSoundData:SoundData;
      
      public function ButtonLogicShell2()
      {
         super();
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         m_buttonSoundData = new SoundData("buttonSound","buttonSound","NewGameFolder/FirstEnterSource.swf","ButtonSound");
         Part1.getInstance().getSoundManager().addSound2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_buttonSoundData);
         m_buttonSoundData = null;
         super.clear();
      }
      
      override protected function onUp(param1:MouseEvent) : void
      {
         if(_isLock)
         {
            m_show.dispatchEvent(new ButtonEvent("clickLockBtn",this));
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop("over");
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(2);
         }
         if(GMData.getInstance().isGMApplication == false)
         {
            Part1.getInstance().getSoundManager().play2(m_buttonSoundData.getName(),m_buttonSoundData.getSwfPath(),m_buttonSoundData.getClassName());
         }
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
      
      override protected function createSmallToolTip() : void
      {
         super.createSmallToolTip();
         m_smallToolTip.setFont(new FangZhengKaTongJianTi());
      }
   }
}

