package YJFY.Skill.ActiveSkill
{
   import YJFY.World.World;
   
   public class ActiveSkill_OnlyPlayBody extends ActiveSkill
   {
      
      protected var m_bodyDefId:String;
      
      public function ActiveSkill_OnlyPlayBody()
      {
         super();
      }
      
      override public function clear() : void
      {
         m_bodyDefId = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         super.releaseSkill2(param1);
      }
   }
}

