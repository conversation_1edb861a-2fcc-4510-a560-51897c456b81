package YJFY.VerifyUserState
{
   public class VerifyUserStateListener implements IVerifyUserStateListener
   {
      
      public var verifyUserState:VerifyUserState;
      
      public var userStateIsOkFun:Function;
      
      public var userLoginOutErrorFun:Function;
      
      public var IsNotLinkErrorFun:Function;
      
      public var isMultipleErrorFun:Function;
      
      public function VerifyUserStateListener()
      {
         super();
      }
      
      public function clear() : void
      {
         verifyUserState = null;
         userStateIsOkFun = null;
         userLoginOutErrorFun = null;
         IsNotLinkErrorFun = null;
         isMultipleErrorFun = null;
      }
      
      public function userStateIsOK() : void
      {
         if(<PERSON>olean(userStateIsOkFun))
         {
            userStateIsOkFun();
         }
         verifyUserState.removeVerifyUserStateListener(this);
         clear();
      }
      
      public function userLoginOutError() : void
      {
         if(<PERSON>olean(userLoginOutErrorFun))
         {
            userLoginOutErrorFun();
         }
         verifyUserState.removeVerifyUserStateListener(this);
         clear();
      }
      
      public function isNotLinkErrror() : void
      {
         if(Boolean(IsNotLinkErrorFun))
         {
            IsNotLinkErrorFun();
         }
         verifyUserState.removeVerifyUserStateListener(this);
         clear();
      }
      
      public function isMultipleError() : void
      {
         if(Boolean(isMultipleErrorFun))
         {
            isMultipleErrorFun();
         }
         verifyUserState.removeVerifyUserStateListener(this);
         clear();
      }
   }
}

