package YJFY.Loader
{
   import UI.GamingUI;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.LoaderMax;
   import com.greensock.loading.SWFLoader;
   import com.greensock.loading.XMLLoader;
   import deng.fzip.FZipLoading;
   
   public class YJFYLoader
   {
      
      public static var XML_LOAD_TYPE:String = "xmlLoadType";
      
      public static var SWF_LOAD_TYPE:String = "swfLoadType";
      
      private var _loaderMax:YJFYLoaderMax;
      
      private var _loaderDatas:Vector.<YJFYLoaderData>;
      
      private var _progressShow:IProgressShow;
      
      private var _versionControl:IVersionControl;
      
      public function YJFYLoader()
      {
         super();
      }
      
      public function clear() : void
      {
         if(_loaderMax)
         {
            _loaderMax.clear(true,true);
         }
         _loaderMax = null;
         ClearUtil.nullArr(_loaderDatas);
         _loaderDatas = null;
         _progressShow = null;
         _versionControl = null;
      }
      
      public function getClass(param1:String, param2:String, param3:Function, param4:Function, param5:IProgressShow = null, param6:Object = null, param7:Array = null, param8:Array = null, param9:Boolean = false) : void
      {
         var _loc10_:YJFYLoaderData = new YJFYLoaderData();
         _loc10_.loadType = SWF_LOAD_TYPE;
         _loc10_.swfPath = param1;
         _loc10_.wantClassName = param2;
         _loc10_.getSuccessFun = param3;
         _loc10_.getSuccessFunParams = param7;
         _loc10_.getFailFun = param4;
         _loc10_.getFailFunParams = param8;
         _loc10_.progressShow = param5;
         _loc10_.extra = param6;
         _loc10_.isAutoDispose = false;
         if(_loaderDatas == null)
         {
            _loaderDatas = new Vector.<YJFYLoaderData>();
         }
         _loaderDatas.push(_loc10_);
      }
      
      public function getXML(param1:String, param2:Function, param3:Function, param4:IProgressShow = null, param5:Object = null, param6:Array = null, param7:Array = null, param8:Boolean = false) : void
      {
         var _loc9_:YJFYLoaderData = new YJFYLoaderData();
         _loc9_.loadType = XML_LOAD_TYPE;
         _loc9_.xmlPath = param1;
         _loc9_.getSuccessFun = param2;
         _loc9_.getSuccessFunParams = param6;
         _loc9_.getFailFun = param3;
         _loc9_.getFailFunParams = param7;
         _loc9_.progressShow = param4;
         _loc9_.extra = param5;
         _loc9_.isAutoDispose = param8;
         if(_loaderDatas == null)
         {
            _loaderDatas = new Vector.<YJFYLoaderData>();
         }
         _loaderDatas.push(_loc9_);
      }
      
      public function load(param1:IProgressShow = null) : void
      {
         var _loc7_:Array = null;
         var _loc9_:int = 0;
         var _loc5_:YJFYLoaderData = null;
         var _loc4_:Class = null;
         var _loc2_:* = undefined;
         var _loc6_:Boolean = false;
         var _loc8_:String = null;
         var _loc3_:String = null;
         if(_loaderMax == null)
         {
            _loaderMax = new YJFYLoaderMax(null,param1);
         }
         _loaderMax.maxConnections = 1;
         if(param1)
         {
            _loaderMax.progressShow = param1;
         }
         _progressShow = param1;
         _loc9_ = 0;
         while(_loc9_ < _loaderDatas.length)
         {
            switch((_loc5_ = _loaderDatas[_loc9_]).loadType)
            {
               case SWF_LOAD_TYPE:
                  _loc5_.loaderCore = LoaderMax.getLoader(_loc5_.swfPath);
                  if(_loc5_.loaderCore == null)
                  {
                     _loc5_.loaderCore = new YJFYSWFLoader(_loc5_.swfPath,{
                        "name":_loc5_.swfPath,
                        "onComplete":complete,
                        "onError":error
                     },_loc5_.progressShow,_versionControl);
                     _loc5_.loaderCore.autoDispose = _loc5_.isAutoDispose;
                     if(_versionControl.getLineMode().getLineMode() == "offLine")
                     {
                     }
                     _loaderMax.append(_loc5_.loaderCore);
                  }
                  else
                  {
                     _loc5_.loaderCore.autoDispose = _loc5_.isAutoDispose;
                     if(_loc5_.wantClassName)
                     {
                        _loc4_ = SWFLoader(_loc5_.loaderCore).getClass(_loc5_.wantClassName);
                        if(_loc4_ == null)
                        {
                           if(_loc5_.loaderCore.status == 0 || _loc5_.loaderCore.status == 1)
                           {
                              _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                              _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                           }
                           else if(_loc5_.loaderCore.status == 3)
                           {
                              _loc5_.loaderCore.resume();
                              _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                              _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                           }
                           else
                           {
                              if(_loc5_.loaderCore.status == 2)
                              {
                                 throw new Error(_loc5_.swfPath + "不存在" + _loc5_.wantClassName + "!");
                              }
                              if(_loc5_.loaderCore.status == 5)
                              {
                                 throw new Error("LoaderMax中不应该存在该LoaderCore！");
                              }
                           }
                        }
                        else
                        {
                           spliceYJFYLoaderDataForLoaderDatas(_loc5_);
                           _loc5_.resultClass = _loc4_;
                           _loc7_ = _loc5_.getSuccessFunParams ? _loc5_.getSuccessFunParams.slice(0) : [];
                           _loc7_.unshift(_loc5_);
                           _loc5_.getSuccessFun.apply(null,_loc7_);
                           ClearUtil.nullArr(_loc7_,false,false,false);
                           _loc5_.clear();
                           _loc9_--;
                        }
                     }
                     else if(_loc5_.loaderCore.status == 0 || _loc5_.loaderCore.status == 1)
                     {
                        _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                        _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                     }
                     else if(_loc5_.loaderCore.status == 3)
                     {
                        _loc5_.loaderCore.resume();
                        _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                        _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                     }
                     else if(_loc5_.loaderCore.status == 2)
                     {
                        loadOneComplete(_loc5_.swfPath);
                        _loc9_--;
                     }
                     else if(_loc5_.loaderCore.status == 5)
                     {
                        throw new Error("LoaderMax中不应该存在该LoaderCore！");
                     }
                  }
                  break;
               case XML_LOAD_TYPE:
                  _loc5_.loaderCore = LoaderMax.getLoader(_loc5_.xmlPath);
                  _loc6_ = false;
                  _loc8_ = _loc5_.xmlPath;
                  if(FZipLoading.dic.hasOwnProperty(_loc8_) || GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
                  {
                     _loc6_ = true;
                  }
                  if(_loc5_.loaderCore == null && !_loc6_)
                  {
                     _loc5_.loaderCore = new YJFYXMLLoader(_loc5_.xmlPath,{
                        "name":_loc5_.xmlPath,
                        "onComplete":complete,
                        "onError":error
                     },param1,_versionControl);
                     _loaderMax.append(_loc5_.loaderCore);
                  }
                  else
                  {
                     if(_loc6_)
                     {
                        trace("zip加载XML：" + _loc8_);
                        _loc2_ = FZipLoading.dic[_loc8_];
                     }
                     else
                     {
                        _loc2_ = XMLLoader(_loc5_.loaderCore).getContent(_loc5_.xmlPath);
                     }
                     if(_loc2_ == null)
                     {
                        if(_loc5_.loaderCore.status == 0 || _loc5_.loaderCore.status == 1)
                        {
                           _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                           _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                        }
                        else if(_loc5_.loaderCore.status == 3)
                        {
                           _loc5_.loaderCore.resume();
                           _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                           _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                        }
                        else
                        {
                           if(_loc5_.loaderCore.status == 2)
                           {
                              throw new Error(_loc5_.xmlPath + "不存在" + "!");
                           }
                           if(_loc5_.loaderCore.status == 5)
                           {
                              _loc5_.loaderCore.load();
                              _loc5_.loaderCore.addEventListener("complete",complete,false,0,true);
                              _loc5_.loaderCore.addEventListener("error",error,false,0,true);
                           }
                        }
                     }
                     else
                     {
                        spliceYJFYLoaderDataForLoaderDatas(_loc5_);
                        if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
                        {
                           try
                           {
                              _loc3_ = MyBase64.getReallyDate(_loc2_);
                           }
                           catch(e:Error)
                           {
                              throw new Error("配置表解密错误," + _loc5_.xmlPath,43999);
                           }
                        }
                        else
                        {
                           _loc3_ = String(_loc2_);
                        }
                        _loc5_.resultXML = new XML(_loc3_);
                        _loc7_ = _loc5_.getSuccessFunParams ? _loc5_.getSuccessFunParams.slice(0) : [];
                        _loc7_.unshift(_loc5_);
                        _loc5_.getSuccessFun.apply(null,_loc7_);
                        ClearUtil.nullArr(_loc7_,false,false,false);
                        _loc5_.clear();
                        _loc9_--;
                     }
                  }
                  break;
            }
            _loc9_++;
         }
         _loaderMax.load();
      }
      
      public function setProgressShow(param1:IProgressShow = null) : void
      {
         if(_loaderMax == null)
         {
            _loaderMax = new YJFYLoaderMax(null,param1);
         }
         if(param1)
         {
            _loaderMax.progressShow = param1;
         }
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         _versionControl = param1;
      }
      
      private function spliceYJFYLoaderDataForLoaderDatas(param1:YJFYLoaderData) : void
      {
         var _loc2_:int = int(_loaderDatas.indexOf(param1));
         if(_loc2_ != -1)
         {
            _loaderDatas.splice(_loc2_,1);
         }
      }
      
      private function complete(param1:LoaderEvent) : void
      {
         loadOneComplete(param1.target.name);
      }
      
      private function loadOneComplete(param1:String) : void
      {
         var _loc5_:Array = null;
         var _loc7_:int = 0;
         var _loc6_:YJFYLoaderData = null;
         var _loc4_:Class = null;
         var _loc2_:* = undefined;
         var _loc3_:String = null;
         if(_loaderDatas == null)
         {
            return;
         }
         _loc7_ = 0;
         while(_loc7_ < _loaderDatas.length)
         {
            switch((_loc6_ = _loaderDatas[_loc7_]).loadType)
            {
               case SWF_LOAD_TYPE:
                  if(param1 == _loc6_.swfPath)
                  {
                     spliceYJFYLoaderDataForLoaderDatas(_loc6_);
                     if(_loc6_.swfPath)
                     {
                        _loc4_ = SWFLoader(_loc6_.loaderCore).getClass(_loc6_.wantClassName);
                        _loc6_.resultClass = _loc4_;
                     }
                     _loc5_ = _loc6_.getSuccessFunParams ? _loc6_.getSuccessFunParams.slice(0) : [];
                     _loc5_.unshift(_loc6_);
                     if(Boolean(_loc6_.getSuccessFun))
                     {
                        _loc6_.getSuccessFun.apply(null,_loc5_);
                     }
                     ClearUtil.nullArr(_loc5_,false,false,false);
                     _loc6_.loaderCore.removeEventListener("complete",complete,false);
                     _loc6_.loaderCore.removeEventListener("error",error,false);
                     _loc6_.clear();
                     _loc7_--;
                  }
                  break;
               case XML_LOAD_TYPE:
                  if(param1 == _loc6_.xmlPath)
                  {
                     spliceYJFYLoaderDataForLoaderDatas(_loc6_);
                     if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
                     {
                        _loc2_ = FZipLoading.dic[_loc6_.xmlPath];
                     }
                     else
                     {
                        _loc2_ = XMLLoader(_loc6_.loaderCore).getContent(_loc6_.xmlPath);
                     }
                     if(GamingUI.getInstance().getVersionControl().getCodeMode().getCodeMode() == "true")
                     {
                        try
                        {
                           _loc3_ = MyBase64.getReallyDate(_loc2_);
                        }
                        catch(e:Error)
                        {
                           throw new Error("配置表解密错误," + _loc6_.xmlPath,43999);
                        }
                        _loc6_.resultXML = new XML(_loc3_);
                     }
                     else
                     {
                        _loc6_.resultXML = new XML(_loc2_);
                     }
                     _loc5_ = _loc6_.getSuccessFunParams ? _loc6_.getSuccessFunParams.slice(0) : [];
                     _loc5_.unshift(_loc6_);
                     _loc6_.getSuccessFun.apply(null,_loc5_);
                     ClearUtil.nullArr(_loc5_,false,false,false);
                     _loc6_.loaderCore.removeEventListener("complete",complete,false);
                     _loc6_.loaderCore.removeEventListener("error",error,false);
                     _loc6_.clear();
                     _loc7_--;
                  }
            }
            _loc7_++;
         }
      }
      
      private function error(param1:LoaderEvent) : void
      {
         var _loc5_:Array = null;
         var _loc7_:int = 0;
         var _loc6_:YJFYLoaderData = null;
         var _loc3_:* = null;
         var _loc2_:* = undefined;
         var _loc4_:int = int(_loaderDatas.length);
         _loc7_ = 0;
         while(_loc7_ < _loc4_)
         {
            switch((_loc6_ = _loaderDatas[_loc7_]).loadType)
            {
               case SWF_LOAD_TYPE:
                  if(param1.target.name == _loc6_.swfPath)
                  {
                     spliceYJFYLoaderDataForLoaderDatas(_loc6_);
                     _loc5_ = _loc6_.getFailFunParams ? _loc6_.getFailFunParams.slice(0) : [];
                     _loc5_.unshift(_loc6_);
                     _loc6_.getFailFun.apply(null,_loc5_);
                     ClearUtil.nullArr(_loc5_,false,false,false);
                     _loc6_.loaderCore.removeEventListener("complete",complete,false);
                     _loc6_.loaderCore.removeEventListener("error",error,false);
                     _loc6_.clear();
                     _loc4_--;
                     _loc7_--;
                  }
                  break;
               case XML_LOAD_TYPE:
                  if(param1.target.name == _loc6_.xmlPath)
                  {
                     spliceYJFYLoaderDataForLoaderDatas(_loc6_);
                     _loc5_ = _loc6_.getFailFunParams ? _loc6_.getFailFunParams.slice(0) : [];
                     _loc5_.unshift(_loc6_);
                     _loc6_.getFailFun.apply(null,_loc5_);
                     ClearUtil.nullArr(_loc5_,false,false,false);
                     _loc6_.loaderCore.removeEventListener("complete",complete,false);
                     _loc6_.loaderCore.removeEventListener("error",error,false);
                     _loc6_.clear();
                     _loc4_--;
                     _loc7_--;
                  }
            }
            _loc7_++;
         }
      }
      
      public function getProgressShow() : IProgressShow
      {
         return _progressShow;
      }
   }
}

