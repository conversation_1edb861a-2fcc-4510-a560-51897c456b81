package YJFY.Skill.TieShanSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TangMonkEntity.TangMonkEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneBullet;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_TieShanSkillFeng extends CuboidAreaAttackSkill_OneBullet
   {
      
      protected var m_effectAddtoTargetId:String;
      
      protected var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      protected var m_effectShowsAddtoTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_wasteEffectShowsAddToTarget:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_effectShowsAddtoTargetPool:ObjectsPool;
      
      protected var m_animalEntitys_addEffect:Vector.<AnimalEntity>;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      private var m_blowing:int = 0;
      
      protected var m_releaseFrameLabel:String;
      
      protected var m_releaseFrameLabel2:String;
      
      protected var m_skillEndEndFrameLabel:String;
      
      protected var m_skillShowDefId:String;
      
      protected var m_skillShowIsFrontOfBody:Boolean;
      
      protected var m_skillShowAnimation:AnimationShowPlayLogicShell;
      
      protected var m_skillShowAnimationListener:AnimationPlayFrameLabelListener;
      
      public function Skill_TieShanSkillFeng()
      {
         super();
         m_effectShowsAddtoTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_wasteEffectShowsAddToTarget = new Vector.<AnimationShowPlayLogicShell>();
         m_animalEntitys_addEffect = new Vector.<AnimalEntity>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.changeStateFun = entityChangeState;
         m_effectShowsAddtoTargetPool = new ObjectsPool(m_effectShowsAddtoTarget,m_wasteEffectShowsAddToTarget,createEffectShowAddToTarget,null);
         m_skillShowAnimation = new AnimationShowPlayLogicShell();
         m_skillShowAnimationListener = new AnimationPlayFrameLabelListener();
         m_skillShowAnimationListener.reachFrameLabelFun = reachFrameLabel;
         m_skillShowAnimation.addFrameLabelListener(m_skillShowAnimationListener);
      }
      
      override public function clear() : void
      {
         clear2();
         m_effectAddtoTargetId = null;
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowsAddtoTarget);
         m_effectShowsAddtoTarget = null;
         ClearUtil.clearObject(m_wasteEffectShowsAddToTarget);
         m_wasteEffectShowsAddToTarget = null;
         ClearUtil.clearObject(m_effectShowsAddtoTargetPool);
         m_effectShowsAddtoTargetPool = null;
         ClearUtil.clearObject(m_animalEntitys_addEffect);
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         m_skillShowDefId = null;
         ClearUtil.clearObject(m_skillShowAnimation);
         m_skillShowAnimation = null;
         ClearUtil.clearObject(m_skillShowAnimationListener);
         m_skillShowAnimationListener = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_effectAddtoTargetId = String(param1.@effectAddtoTargetId);
         if(m_effectAddtoTargetId)
         {
            m_effectAddtoTargetDefinitionData = new AnimationDefinitionData();
            m_effectAddtoTargetDefinitionData.initByXML(param1.animationDefinition.(@id == m_effectAddtoTargetId)[0]);
         }
         m_blowing = int(param1.@blowing);
         m_skillShowDefId = String(param1.@skillShowDefId);
         m_skillShowIsFrontOfBody = Boolean(int(param1.@skillShowIsFrontOfBody));
         var _loc2_:XML = param1.frameLabels[0];
         m_releaseFrameLabel = String(_loc2_.@releaseFrameLabel);
         m_releaseFrameLabel2 = String(_loc2_.@releaseFrameLabel2);
         m_skillEndEndFrameLabel = String(_loc2_.@skillEndEndFrameLabel);
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_skillShowAnimation.setShow(m_owner.getAnimationByDefId(m_skillShowDefId),true);
         m_owner.addOtherAniamtion(m_skillShowAnimation,m_skillShowIsFrontOfBody);
         m_skillShowAnimation.gotoAndPlay("1");
         (m_skillShowAnimation.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
      }
      
      override protected function endSkill2() : void
      {
         m_owner.removeOtherAnimation(m_skillShowAnimation);
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         if(m_isRun == false)
         {
            return;
         }
         super.reachFrameLabel(param1);
         switch(param1)
         {
            case m_releaseFrameLabel:
               m_owner.currentAnimationStop();
               playSkillAnimation();
               break;
            case m_releaseFrameLabel2:
               m_owner.currentAnimationPlay();
               break;
            case m_skillEndEndFrameLabel:
               endSkill2();
         }
      }
      
      protected function playSkillAnimation() : void
      {
         m_endTime = m_world.getWorldTime() + m_timeOfDuration;
         m_skillShowAnimation.setShow(m_owner.getAnimationByDefId(m_skillShowDefId),true);
         m_owner.addOtherAniamtion(m_skillShowAnimation,m_skillShowIsFrontOfBody);
         m_skillShowAnimation.gotoAndPlay("1");
         (m_skillShowAnimation.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
      }
      
      protected function createEffectShowAddToTarget() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_effectAddtoTargetDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      override protected function listenerBulletAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         super.listenerBulletAttackSuccess(param1,param2,param3);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && param1 is IAnimalEntity)
         {
            addEffectShowAddToTarget(param1);
            param1.applyImpulse(new P3DVector3D(0,0,m_blowing));
         }
      }
      
      protected function addEffectShowAddToTarget(param1:IEntity) : void
      {
         var _loc3_:AnimationShowPlayLogicShell = null;
         var _loc2_:int = 0;
         if(m_effectAddtoTargetDefinitionData && !(param1 as AnimalEntity).notShowBeattack && (param1 as IAnimalEntity).isInDie() == false && !(param1 is TangMonkEntity))
         {
            _loc3_ = m_effectShowsAddtoTargetPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc3_.extra = param1;
            (param1 as IAnimalEntity).addAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect.push(param1);
            _loc3_.gotoAndPlay("start");
            _loc2_ = (param1 as AnimalEntity).isFly ? 2 : 1;
            (_loc3_.getShow() as AnimationShow).y = 0 - (m_effectAddtoTargetId != "KunBang" ? param1.getBodyZRange() : 0) * _loc2_;
            param1.addOtherAniamtion(_loc3_);
         }
      }
      
      protected function entityChangeState(param1:AnimalEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1.isInHurt() == false)
         {
            _loc3_ = int(m_effectShowsAddtoTarget.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(m_effectShowsAddtoTarget[_loc4_].extra == param1)
               {
                  param1.removeOtherAnimation(m_effectShowsAddtoTarget[_loc4_]);
                  m_effectShowsAddtoTargetPool.wasteOneObj(m_effectShowsAddtoTarget[_loc4_]);
                  _loc4_--;
                  _loc3_--;
               }
               _loc4_++;
            }
            _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1));
            m_animalEntitys_addEffect.splice(_loc2_,1);
            _loc2_ = int(m_animalEntitys_addEffect.indexOf(param1));
            if(_loc2_ == -1)
            {
               param1.removeAnimalEntityListener(m_animalEntityListener);
            }
         }
      }
      
      protected function clear2() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_effectShowsAddtoTarget ? m_effectShowsAddtoTarget.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_effectShowsAddtoTarget[_loc2_].extra)
            {
               (m_effectShowsAddtoTarget[_loc2_].extra as IEntity).removeOtherAnimation(m_effectShowsAddtoTarget[_loc2_]);
            }
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_].getShow());
            ClearUtil.clearObject(m_effectShowsAddtoTarget[_loc2_]);
            m_effectShowsAddtoTarget[_loc2_] = null;
            _loc2_++;
         }
         m_effectShowsAddtoTarget.length = 0;
         _loc1_ = m_animalEntitys_addEffect ? m_animalEntitys_addEffect.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_animalEntitys_addEffect[_loc2_].removeAnimalEntityListener(m_animalEntityListener);
            m_animalEntitys_addEffect[_loc2_] = null;
            _loc2_++;
         }
         m_animalEntitys_addEffect.length = 0;
         m_animalEntitys_addEffect = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
      }
   }
}

