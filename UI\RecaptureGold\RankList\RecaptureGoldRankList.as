package UI.RecaptureGold.RankList
{
   import UI.MyFunction2;
   import UI.PKUI.RankListPanel;
   import YJFY.GameData;
   import flash.display.MovieClip;
   
   public class RecaptureGoldRankList extends RankListPanel
   {
      
      public function RecaptureGoldRankList()
      {
         super();
         _isTwoMode = true;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("RecaptureGoldRankList") as MovieClip;
         addChild(m_show);
         super.init(param1,param2);
      }
      
      override protected function initRankListData(param1:Number = 0) : void
      {
         var score:Number = param1;
         var PkRankListPanel:RankListPanel = this;
         getMyDataByUserName(null,function(param1:Array):void
         {
            var i:int;
            var length:int;
            var isHave:Boolean;
            var arr:Array = param1;
            if(!arr || arr.length == 0)
            {
               m_myRankText.text = "暂无排名";
               m_myScoreText.text = "暂无分数";
            }
            else
            {
               length = int(arr.length);
               isHave = false;
               i = 0;
               while(i < length)
               {
                  if(arr[i].index == GameData.getInstance().getSaveFileData().index)
                  {
                     m_myRankText.text = arr[i].rank.toString();
                     m_myScoreText.text = arr[i].score.toString();
                     isHave = true;
                     break;
                  }
                  i++;
               }
               if(!isHave)
               {
                  m_myRankText.text = "暂无排名";
                  m_myScoreText.text = "暂无分数";
               }
               i = 0;
               while(i < length)
               {
                  arr[i] = null;
                  i++;
               }
               arr = null;
            }
            getRankListData(null,function(param1:Array):void
            {
               arrangeRankList(param1);
               length = param1 ? param1.length : 0;
               i = 0;
               while(i < length)
               {
                  param1[i] = null;
                  i++;
               }
               param1 = null;
            });
         });
      }
   }
}

