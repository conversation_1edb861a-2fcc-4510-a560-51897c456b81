package YJFY.ShowLogicShell
{
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import flash.display.InteractiveObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ButtonLogicShell implements IButton
   {
      
      protected var m_show:InteractiveObject;
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      public function ButtonLogicShell()
      {
         super();
      }
      
      public function setShow(param1:InteractiveObject) : void
      {
         m_show = param1;
         m_show.addEventListener("click",onClick,false,0,true);
         m_show.addEventListener("rollOver",onOver,false,0,true);
         m_show.addEventListener("rollOut",onOut,false,0,true);
         if(param1 is Sprite)
         {
            (m_show as Sprite).buttonMode = true;
         }
      }
      
      public function getShow() : InteractiveObject
      {
         return m_show;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("click",onClick,false);
            m_show.removeEventListener("rollOut",onOut,false);
            m_show.removeEventListener("rollOver",onOver,false);
            m_show = null;
         }
         ClearUtil.clearObject(m_smallToolTip);
         m_smallToolTip = null;
      }
      
      public function setTipString(param1:String) : void
      {
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               createSmallToolTip();
            }
            else
            {
               m_smallToolTip.setTipStr(m_tipString);
            }
         }
      }
      
      protected function createSmallToolTip() : void
      {
         m_smallToolTip = new SmallToolTip();
         m_smallToolTip.setTipStr(m_tipString);
         m_smallToolTip.init();
      }
      
      protected function onOut(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         m_show.dispatchEvent(new ButtonEvent("onOutButton",this));
      }
      
      protected function onOver(param1:MouseEvent) : void
      {
         if(m_smallToolTip)
         {
            if(m_show.stage)
            {
               m_show.stage.addChild(m_smallToolTip);
               if(m_show.stage.mouseX + 10 + m_smallToolTip.width > m_show.stage.stageWidth)
               {
                  m_smallToolTip.x = m_show.stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = m_show.stage.mouseX + 10;
               }
               if(m_show.stage.mouseY + 10 + m_smallToolTip.height > m_show.stage.stageHeight)
               {
                  m_smallToolTip.y = m_show.stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = m_show.stage.mouseY + 10;
               }
            }
         }
         m_show.dispatchEvent(new ButtonEvent("onOverButton",this));
      }
      
      protected function onClick(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
   }
}

