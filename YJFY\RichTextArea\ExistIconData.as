package YJFY.RichTextArea
{
   import YJFY.RichTextArea.IconData.IconData;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.text.TextFormat;
   
   public class ExistIconData
   {
      
      private var m_iconData:IconData;
      
      private var m_index:int;
      
      private var m_iconContainer:Sprite;
      
      private var m_textFormat:TextFormat;
      
      public function ExistIconData(param1:IconData, param2:int, param3:Sprite, param4:TextFormat)
      {
         super();
         m_iconData = param1;
         m_index = param2;
         m_iconContainer = param3;
         m_textFormat = param4;
      }
      
      public function clear() : void
      {
         m_iconData = null;
         ClearUtil.clearObject(m_iconContainer);
         m_iconContainer = null;
         m_textFormat = null;
      }
      
      public function getIconData() : IconData
      {
         return m_iconData;
      }
      
      public function getIconContainer() : Sprite
      {
         return m_iconContainer;
      }
      
      public function getTextFormat() : TextFormat
      {
         return m_textFormat;
      }
      
      public function getIndex() : int
      {
         return m_index;
      }
      
      public function setIndex(param1:int) : void
      {
         m_index = param1;
      }
      
      public function setIconContainer(param1:Sprite) : void
      {
         m_iconContainer = param1;
      }
      
      public function setTextFormat(param1:TextFormat) : void
      {
         m_textFormat = param1;
      }
   }
}

