package UI.TextTrace
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class DataLayer extends MySprite
   {
      
      public static const TEXT_MIN_HEIGHT:Number = 256;
      
      public var traceText:TextField;
      
      public function DataLayer()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         traceText = null;
      }
      
      private function init() : void
      {
         traceText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,15,10092288);
         traceText.defaultTextFormat = new TextFormat(null,15,10092288);
         traceText.width = 372;
         traceText.height = 256;
         traceText.embedFonts = true;
         traceText.multiline = true;
         traceText.wordWrap = true;
      }
   }
}

