package UI.WorldBoss
{
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class Position
   {
      
      public var container:Sprite;
      
      public var positionX:int;
      
      public var positionY:int;
      
      public function Position()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearDisplayObjectInContainer(container,false,false);
         container = null;
      }
   }
}

