package YJFY.ShowLogicShell
{
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import flash.display.InteractiveObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ButtonLogicShell2 implements IButton
   {
      
      protected var m_show:Sprite;
      
      protected var _isLock:Boolean;
      
      public var extraData:Object;
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      public function ButtonLogicShell2()
      {
         super();
      }
      
      public function clear() : void
      {
         if(m_tipString == "点击开始无尽闯关！")
         {
            trace("Error");
         }
         if(m_show)
         {
            m_show.removeEventListener("rollOut",onOut,false);
            m_show.removeEventListener("rollOver",onOver,false);
            m_show.removeEventListener("mouseDown",onDown,false);
            m_show.removeEventListener("mouseUp",onUp,false);
            m_show = null;
         }
         ClearUtil.clearObject(m_smallToolTip);
         m_smallToolTip = null;
         extraData = null;
      }
      
      public function lock() : void
      {
         _isLock = true;
         try
         {
            MovieClip(m_show).gotoAndStop("lock");
         }
         catch(error:ArgumentError)
         {
            MovieClip(m_show).gotoAndStop(4);
         }
      }
      
      public function unLock() : void
      {
         _isLock = false;
         try
         {
            MovieClip(m_show).gotoAndStop("out");
         }
         catch(error:ArgumentError)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
      }
      
      public function setTipString(param1:String) : void
      {
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               createSmallToolTip();
            }
            else
            {
               m_smallToolTip.setTipStr(m_tipString);
            }
         }
      }
      
      protected function createSmallToolTip() : void
      {
         m_smallToolTip = new SmallToolTip();
         m_smallToolTip.setTipStr(m_tipString);
         m_smallToolTip.init();
      }
      
      protected function onDown(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(_isLock)
         {
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop("down");
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(3);
         }
      }
      
      protected function onUp(param1:MouseEvent) : void
      {
         if(_isLock)
         {
            m_show.dispatchEvent(new ButtonEvent("clickLockBtn",this));
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop("over");
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(2);
         }
         m_show.dispatchEvent(new ButtonEvent("clickButton",this));
      }
      
      protected function onOut(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(_isLock)
         {
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop("out");
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(1);
         }
         m_show.dispatchEvent(new ButtonEvent("onOutButton",this));
      }
      
      protected function onOver(param1:MouseEvent) : void
      {
         if(m_smallToolTip)
         {
            if(m_show.stage)
            {
               m_show.stage.addChild(m_smallToolTip);
               if(m_show.stage.mouseX + 10 + m_smallToolTip.width > m_show.stage.stageWidth)
               {
                  m_smallToolTip.x = m_show.stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = m_show.stage.mouseX + 10;
               }
               if(m_show.stage.mouseY + 10 + m_smallToolTip.height > m_show.stage.stageHeight)
               {
                  m_smallToolTip.y = m_show.stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = m_show.stage.mouseY + 10;
               }
            }
         }
         if(_isLock)
         {
            return;
         }
         try
         {
            MovieClip(m_show).gotoAndStop("over");
         }
         catch(error:Error)
         {
            MovieClip(m_show).gotoAndStop(2);
         }
         m_show.dispatchEvent(new ButtonEvent("onOverButton",this));
      }
      
      public function setShow(param1:Sprite) : void
      {
         MovieClip(param1).gotoAndStop(1);
         m_show = param1;
         (m_show as MovieClip).buttonMode = true;
         m_show.addEventListener("rollOver",onOver,false,0,true);
         m_show.addEventListener("rollOut",onOut,false,0,true);
         m_show.addEventListener("mouseDown",onDown,false,0,true);
         m_show.addEventListener("mouseUp",onUp,false,0,true);
      }
      
      public function getShow() : InteractiveObject
      {
         return m_show;
      }
      
      public function get isLock() : Boolean
      {
         return _isLock;
      }
      
      public function set isLock(param1:Boolean) : void
      {
         _isLock = param1;
      }
   }
}

