package UI.RecaptureGold
{
   import flash.events.Event;
   
   public class RecaptureGoldEvent extends Event
   {
      
      public static const RECAPTURE_GOLD_GAME_END:String = "recaptureGoldGameEnd";
      
      public static const PLAYER_OVER:String = "playerOver";
      
      public static const START_GAME:String = "startRecaptureGoldGame";
      
      public static const GOTO_RG_RANKLIST:String = "gotoRGRankList";
      
      public static const GOTO_MONEY_SHOP_REWARD:String = "gotoMoneyShopReward";
      
      public static const NEXT_LEVEL:String = "nextLevel";
      
      public static const RETURN_MAIN_PANEL:String = "returnMainPanel";
      
      public static const QUIT_GAME:String = "quitGame";
      
      public static const REFRESH_UI:String = "refreshUI";
      
      public static const COMPLETE_PRESS_BTN:String = "completePressBtn";
      
      public static const BUY_RG_NUM:String = "buyRGNum";
      
      public static const SHOW_INSTRUCTION:String = "showInstruction";
      
      public static const CLICK_GET_REWARD_BTN:String = "clickGetRewardBtn";
      
      public static const CLICK_QUIT_GAME_BTN:String = "clickQuitGameBtn";
      
      public static const EXPLODE_SHAKE:String = "explodeShake";
      
      private var _data:*;
      
      public function RecaptureGoldEvent(param1:String, param2:* = null, param3:Boolean = false, param4:Boolean = false)
      {
         _data = param2;
         super(param1,param3,param4);
      }
      
      override public function clone() : Event
      {
         return new RecaptureGoldEvent(type,data,bubbles,cancelable);
      }
      
      public function get data() : *
      {
         return _data;
      }
   }
}

