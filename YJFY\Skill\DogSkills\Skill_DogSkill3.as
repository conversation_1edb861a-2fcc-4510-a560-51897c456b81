package YJFY.Skill.DogSkills
{
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_DogSkill3 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private var m_groundEffectDefId:String;
      
      private var m_groundEffectEndFrameLabel:String;
      
      private var m_groundEffectShowPlay:AnimationShowPlayLogicShell;
      
      private var m_shakeViewDataOfPoints:DataOfPoints;
      
      public function Skill_DogSkill3()
      {
         super();
         m_groundEffectShowPlay = new AnimationShowPlayLogicShell();
         m_shakeViewDataOfPoints = new DataOfPoints();
      }
      
      override public function clear() : void
      {
         m_groundEffectDefId = null;
         m_groundEffectEndFrameLabel = null;
         ClearUtil.clearObject(m_groundEffectShowPlay);
         m_groundEffectShowPlay = null;
         ClearUtil.clearObject(m_shakeViewDataOfPoints);
         m_shakeViewDataOfPoints = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_groundEffectDefId = String(param1.@groundEffectDefId);
         m_groundEffectEndFrameLabel = String(param1.@groundEffectEndFrameLabel);
         var _loc3_:String = String(param1.shakeView[0].@swfPath);
         var _loc2_:String = String(param1.shakeView[0].@className);
         m_myLoader.getClass(_loc3_,_loc2_,getShakeViewMovieClipSuccess,getShakeViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      override protected function playSkillAnimation() : void
      {
         super.playSkillAnimation();
         if(m_groundEffectShowPlay.getShow() == null)
         {
            m_groundEffectShowPlay.setShow(m_owner.getAnimationByDefId(m_groundEffectDefId),true);
         }
         (m_groundEffectShowPlay.getShow() as DisplayObject).scaleX = m_owner.getShowDirection();
         (m_groundEffectShowPlay.getShow() as DisplayObject).x = m_owner.getScreenX();
         (m_groundEffectShowPlay.getShow() as DisplayObject).y = m_owner.getScreenY();
         m_world.addAnimationInGround(m_groundEffectShowPlay);
         m_groundEffectShowPlay.gotoAndPlay("1");
      }
      
      override protected function endSkill1() : void
      {
         super.endSkill1();
         m_skillShowAnimation.gotoAndPlay(m_groundEffectEndFrameLabel);
      }
      
      override protected function endSkill2() : void
      {
         m_world.removeAnimationInGround(m_groundEffectShowPlay);
         super.endSkill2();
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if(m_skillAttackReachFrameLabel === _loc2_)
         {
            m_world.shakeView(m_shakeViewDataOfPoints);
         }
      }
      
      private function getShakeViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_shakeViewDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getShakeViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

