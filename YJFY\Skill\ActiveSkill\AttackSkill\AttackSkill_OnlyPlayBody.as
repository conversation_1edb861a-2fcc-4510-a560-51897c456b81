package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class AttackSkill_OnlyPlayBody extends AttackSkill
   {
      
      protected var m_bodyDefId:String;
      
      protected var m_skillAttackEffectId:String;
      
      protected var m_skillAttackEffectDefinitionData:AnimationDefinitionData;
      
      protected var m_effectAnimationShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_effectAnimationStopListener:StopListener;
      
      protected var m_wasteEffecAnimationShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_effectAnimationShowPlaysPool:ObjectsPool;
      
      protected var m_isAttackReachWhenRelease:Boolean;
      
      public function AttackSkill_OnlyPlayBody()
      {
         super();
         m_effectAnimationShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_effectAnimationStopListener = new StopListener();
         m_effectAnimationStopListener.stop2Fun = effectAnimationStop;
         m_wasteEffecAnimationShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_isAttackReachWhenRelease = true;
         m_effectAnimationShowPlaysPool = new ObjectsPool(m_effectAnimationShowPlays,m_wasteEffecAnimationShowPlays,createEffectAniamtionShowPlay,null);
      }
      
      override public function clear() : void
      {
         clearEffectAnimationPlays();
         clearWasteEffectAnimationPlays();
         m_bodyDefId = null;
         m_skillAttackEffectId = null;
         ClearUtil.clearObject(m_skillAttackEffectDefinitionData);
         m_skillAttackEffectDefinitionData = null;
         ClearUtil.clearObject(m_effectAnimationShowPlays);
         m_effectAnimationShowPlays = null;
         ClearUtil.clearObject(m_effectAnimationStopListener);
         m_effectAnimationStopListener = null;
         ClearUtil.clearObject(m_wasteEffecAnimationShowPlays);
         m_wasteEffecAnimationShowPlays = null;
         ClearUtil.clearObject(m_effectAnimationShowPlaysPool);
         m_effectAnimationShowPlaysPool = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_skillAttackEffectId = String(param1.@skillAttackEffectDefId);
         if(m_skillAttackEffectId)
         {
            m_skillAttackEffectDefinitionData = new AnimationDefinitionData();
            m_skillAttackEffectDefinitionData.initByXML(param1.animationDefinition.(@id == m_skillAttackEffectId)[0]);
         }
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override public function showRender() : void
      {
         clearWasteEffectAnimationPlays();
         super.showRender();
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         super.releaseSkill2(param1);
         if(m_isAttackReachWhenRelease)
         {
            attackReach(param1);
         }
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            addAttackEffect(param1);
         }
      }
      
      protected function addAttackEffect(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if(m_skillAttackEffectDefinitionData)
         {
            _loc2_ = m_effectAnimationShowPlaysPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc2_.gotoAndPlay("start");
            param1.addOtherAniamtion(_loc2_);
            _loc2_.extra = param1;
         }
      }
      
      private function createEffectAniamtionShowPlay() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_skillAttackEffectDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.addNextStopListener(m_effectAnimationStopListener);
         _loc1_.setShow(_loc2_,true);
         return _loc1_;
      }
      
      protected function effectAnimationStop(param1:AnimationShowPlayLogicShell) : void
      {
         (param1.extra as IEntity).removeOtherAnimation(param1);
         m_effectAnimationShowPlaysPool.wasteOneObj(param1);
      }
      
      protected function clearWasteEffectAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_wasteEffecAnimationShowPlays ? m_wasteEffecAnimationShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_wasteEffecAnimationShowPlays[0];
            m_wasteEffecAnimationShowPlays.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_.getShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
      }
      
      protected function clearEffectAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_effectAnimationShowPlays ? m_effectAnimationShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_effectAnimationShowPlays[0];
            m_effectAnimationShowPlays.splice(0,1);
            _loc1_--;
            if(_loc2_.extra)
            {
               (_loc2_.extra as IEntity).removeOtherAnimation(_loc2_);
            }
            ClearUtil.clearObject(_loc2_.getShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
      }
   }
}

