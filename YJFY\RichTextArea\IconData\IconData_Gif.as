package YJFY.RichTextArea.IconData
{
   public class IconData_Gif extends IconData
   {
      
      private var m_url:String;
      
      public function IconData_Gif(param1:String, param2:String, param3:String)
      {
         super(param1,param2);
         m_iconType = "gif";
         m_url = param3;
      }
      
      public function getUrl() : String
      {
         return m_url;
      }
   }
}

