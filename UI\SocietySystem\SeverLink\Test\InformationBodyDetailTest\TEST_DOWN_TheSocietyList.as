package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.GamingUI;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyList;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.SocietyDataInList;
   
   public class TEST_DOWN_TheSocietyList extends DOWN_TheSocietyList
   {
      
      public function TEST_DOWN_TheSocietyList()
      {
         super();
         m_informationBodyId = 3005;
      }
      
      public function initData(param1:int, param2:Vector.<SocietyDataInList>) : void
      {
         m_societyTotalNum = param1;
         m_societyList = param2;
         m_dataDeadTime = GamingUI.getInstance().getEnterFrameTime().getOnLineTimeForThisInit() + 60000;
      }
   }
}

