package UI.Protect
{
   import UI.MySprite;
   
   public class BuyProtectSlotBorder extends MySprite
   {
      
      private static var _instance:BuyProtectSlotBorder = null;
      
      public function BuyProtectSlotBorder()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在!");
      }
      
      public static function getInstance() : BuyProtectSlotBorder
      {
         if(!_instance)
         {
            _instance = new BuyProtectSlotBorder();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(parent)
         {
            parent.removeChild(this);
         }
         _instance = null;
      }
   }
}

