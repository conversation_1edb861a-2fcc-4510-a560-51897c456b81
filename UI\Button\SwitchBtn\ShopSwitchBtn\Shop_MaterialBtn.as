package UI.Button.SwitchBtn.ShopSwitchBtn
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class Shop_MaterialBtn extends SwitchBtn
   {
      
      public function Shop_MaterialBtn()
      {
         super();
         setTipString("材料");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("clickShopSwitchBtn"));
      }
   }
}

