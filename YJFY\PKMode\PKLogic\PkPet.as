package YJFY.PKMode.PKLogic
{
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import YJFY.Entity.AttackData;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import YJFY.XydzjsData.AttackDataCalculate;
   import YJFY.XydzjsData.IAttackDataCalculateVOXydzjs;
   import YJFY.XydzjsData.IAttackSkillVOForAttackDataCalculate;
   
   public class PkPet extends PKEntity implements IEntity, IAttackDataCalculateVOXydzjs
   {
      
      private var m_attackDataCalculate:AttackDataCalculate;
      
      private var m_pet:PetXydzjs;
      
      private var m_pkStepWorld:StepAttackGameWorld;
      
      private var m_stepDataManager:StepDataManager;
      
      public function PkPet()
      {
         super();
         m_attackDataCalculate = new AttackDataCalculate();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_attackDataCalculate);
         m_attackDataCalculate = null;
         ClearUtil.clearObject(m_pet);
         m_pet = null;
         m_pkStepWorld = null;
         m_stepDataManager = null;
         super.clear();
      }
      
      override public function setEntity(param1:GameEntity) : void
      {
         super.setEntity(param1);
         m_pet = param1 as PetXydzjs;
         if(m_pet)
         {
            m_pet.setIsOpenNearOwnerWhenRunSkill(false);
         }
      }
      
      public function getPet() : PetXydzjs
      {
         return m_pet;
      }
      
      public function setStepDataManager(param1:StepDataManager) : void
      {
         m_stepDataManager = param1;
      }
      
      public function attack(param1:Vector.<IEntity>) : void
      {
         var _loc8_:int = 0;
         var _loc2_:BeAttackDataOfOneStepData = null;
         var _loc4_:AttackData = null;
         var _loc5_:int = 0;
         var _loc3_:OneStepData = new OneStepData(this);
         var _loc6_:int = int(param1.length);
         if(_loc6_ == 0)
         {
            throw new Error();
         }
         var _loc7_:int = m_pet.getAnimalEntity().getSkillNum();
         _loc5_ = 0;
         while(_loc5_ < _loc7_)
         {
            _loc8_ = 0;
            while(_loc8_ < _loc6_)
            {
               _loc4_ = new AttackData(true,0,false,false,m_pet.getAttackHurtDuration());
               m_attackDataCalculate.caculateHurt(this,(param1[_loc8_] as PKPlayer).getPlayer(),_loc4_);
               (param1[_loc8_] as PKPlayer).decPkBlood1(_loc4_.getHurt());
               _loc2_ = new BeAttackDataOfOneStepData(param1[_loc8_],_loc4_);
               AttackData.hurtNum1++;
               _loc2_.testIndex = AttackData.hurtNum1;
               _loc3_.addBeAttackData(_loc2_);
               _loc8_++;
            }
            _loc5_++;
         }
         m_stepDataManager.addOneStepData(_loc3_);
      }
      
      public function isDie() : Boolean
      {
         return false;
      }
      
      public function Die() : void
      {
      }
      
      public function setWorld(param1:StepAttackGameWorld) : void
      {
         m_pkStepWorld = param1;
      }
      
      public function getTotalHp() : uint
      {
         return 0;
      }
      
      public function getCurrentHp() : uint
      {
         return 0;
      }
      
      public function getPetPassiveSkillDecHurt() : Number
      {
         return 0;
      }
      
      public function decCurrentHp(param1:uint) : void
      {
      }
      
      public function getAttack() : Number
      {
         return (m_pet.getPetEquipmentVO().activeSkillVO as PetActiveSkillVO).pkHurt;
      }
      
      public function getAttackHurtDuration() : uint
      {
         return m_pet.getAttackHurtDuration();
      }
      
      public function setAttackSkill(param1:IAttackSkillVOForAttackDataCalculate) : void
      {
      }
      
      public function getDefence() : Number
      {
         return 0;
      }
      
      public function getDogdeRate() : Number
      {
         return 0;
      }
      
      public function getCriticalRate() : Number
      {
         return 0;
      }
      
      public function getCriticalMulti() : Number
      {
         return 0;
      }
      
      public function getDeCriticalRate() : Number
      {
         return 0;
      }
      
      public function getHitRate() : Number
      {
         return 1.7976931348623157e+308;
      }
   }
}

