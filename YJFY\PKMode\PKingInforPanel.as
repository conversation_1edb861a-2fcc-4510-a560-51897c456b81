package YJFY.PKMode
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PlayerDataForPK;
   import UI2.NewRank.RankDataInfo;
   import UI2.NewRank.RankPK;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.PKLogic.PKPlayer;
   import YJFY.PKMode.PKLogic.PKWorld;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class PKingInforPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showMC:MovieClipPlayLogicShell;
      
      private var m_myPlayer1HeadShow:MovieClipPlayLogicShell;
      
      private var m_myPlayer1HeadBtn:ButtonLogicShell2;
      
      private var m_myPlayer2HeadShow:MovieClipPlayLogicShell;
      
      private var m_myPlayer2HeadBtn:ButtonLogicShell2;
      
      private var m_foePlayer1HeadShow:MovieClipPlayLogicShell;
      
      private var m_foePlayer1HeadBtn:ButtonLogicShell2;
      
      private var m_foePlayer2HeadShow:MovieClipPlayLogicShell;
      
      private var m_foePlayer2HeadBtn:ButtonLogicShell2;
      
      private var m_xzClose:ButtonLogicShell2;
      
      private var m_xzpanel:MovieClip;
      
      private var m_xzMc:MovieClip;
      
      private var m_myUidText:TextField;
      
      private var m_foeUidText:TextField;
      
      private var m_rank:TextField;
      
      private var m_myPlayer1HpBar:CMSXChangeBarLogicShell;
      
      private var m_myPlayer2HpBar:CMSXChangeBarLogicShell;
      
      private var m_foePlayer1HpBar:CMSXChangeBarLogicShell;
      
      private var m_foePlayer2HpBar:CMSXChangeBarLogicShell;
      
      private var m_stopListener:StopListener;
      
      private var m_randomToLeftShow:AnimationShowPlayLogicShell;
      
      private var m_randomToRightShow:AnimationShowPlayLogicShell;
      
      private var m_winAnimationShow:AnimationShowPlayLogicShell;
      
      private var m_failAnimationShow:AnimationShowPlayLogicShell;
      
      private var m_backBtn:ButtonLogicShell;
      
      private var m_nowOverBtn:ButtonLogicShell2;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_myPkPlayer1:PKPlayer;
      
      private var m_myPkPlayer2:PKPlayer;
      
      private var m_foePkPlayer1:PKPlayer;
      
      private var m_foePkPlayer2:PKPlayer;
      
      private var m_stopFun:Function;
      
      private var m_pkWorld:PKWorld;
      
      private var m_pk:PK;
      
      private var m_rankpk:RankPK;
      
      public function PKingInforPanel()
      {
         super();
         m_showMC = new MovieClipPlayLogicShell();
         m_myPlayer1HeadShow = new MovieClipPlayLogicShell();
         m_myPlayer1HeadBtn = new ButtonLogicShell2();
         m_myPlayer2HeadShow = new MovieClipPlayLogicShell();
         m_myPlayer2HeadBtn = new ButtonLogicShell2();
         m_foePlayer1HeadShow = new MovieClipPlayLogicShell();
         m_foePlayer1HeadBtn = new ButtonLogicShell2();
         m_foePlayer2HeadShow = new MovieClipPlayLogicShell();
         m_foePlayer2HeadBtn = new ButtonLogicShell2();
         m_myPlayer1HpBar = new CMSXChangeBarLogicShell();
         m_myPlayer2HpBar = new CMSXChangeBarLogicShell();
         m_foePlayer1HpBar = new CMSXChangeBarLogicShell();
         m_foePlayer2HpBar = new CMSXChangeBarLogicShell();
         m_nowOverBtn = new ButtonLogicShell2();
         m_xzClose = new ButtonLogicShell2();
         m_stopListener = new StopListener();
         m_stopListener.stop2Fun = showStop;
         addEventListener("clickButton",clickButton,true,0,true);
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(GamingUI.getInstance().getVersionControl());
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_showMC);
         m_showMC = null;
         ClearUtil.clearObject(m_myPlayer1HeadShow);
         m_myPlayer1HeadShow = null;
         ClearUtil.clearObject(m_myPlayer1HeadBtn);
         m_myPlayer1HeadBtn = null;
         ClearUtil.clearObject(m_myPlayer2HeadShow);
         m_myPlayer2HeadShow = null;
         ClearUtil.clearObject(m_myPlayer2HeadBtn);
         m_myPlayer2HeadBtn = null;
         ClearUtil.clearObject(m_foePlayer1HeadShow);
         m_foePlayer1HeadShow = null;
         ClearUtil.clearObject(m_foePlayer1HeadBtn);
         m_foePlayer1HeadBtn = null;
         ClearUtil.clearObject(m_foePlayer2HeadShow);
         m_foePlayer2HeadShow = null;
         ClearUtil.clearObject(m_foePlayer2HeadBtn);
         m_foePlayer2HeadBtn = null;
         m_myUidText = null;
         m_foeUidText = null;
         m_rank = null;
         ClearUtil.clearObject(m_myPlayer1HpBar);
         m_myPlayer1HpBar = null;
         ClearUtil.clearObject(m_myPlayer2HpBar);
         m_myPlayer2HpBar = null;
         ClearUtil.clearObject(m_foePlayer1HpBar);
         m_foePlayer1HpBar = null;
         ClearUtil.clearObject(m_foePlayer2HpBar);
         m_foePlayer2HpBar = null;
         ClearUtil.clearObject(m_stopListener);
         m_stopListener = null;
         ClearUtil.clearObject(m_xzClose);
         m_xzClose = null;
         ClearUtil.clearObject(m_randomToLeftShow);
         m_randomToLeftShow = null;
         ClearUtil.clearObject(m_randomToRightShow);
         m_randomToRightShow = null;
         ClearUtil.clearObject(m_winAnimationShow);
         m_winAnimationShow = null;
         ClearUtil.clearObject(m_failAnimationShow);
         m_failAnimationShow = null;
         ClearUtil.clearObject(m_backBtn);
         m_backBtn = null;
         ClearUtil.clearObject(m_nowOverBtn);
         m_nowOverBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         m_myPkPlayer1 = null;
         m_myPkPlayer2 = null;
         m_foePkPlayer1 = null;
         m_foePkPlayer2 = null;
         m_stopFun = null;
         m_pkWorld = null;
         m_pk = null;
         super.clear();
      }
      
      public function setPK(param1:PK) : void
      {
         m_pk = param1;
      }
      
      public function setRankWorld(param1:RankPK) : void
      {
         m_rankpk = param1;
      }
      
      public function setPKWorld(param1:PKWorld) : void
      {
         m_pkWorld = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
      }
      
      public function init() : void
      {
         m_myLoader.getClass("NewGameFolder/PKMode/PkSource.swf","PKHPUI",getShowSuccess,null);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      public function setPKPlayers(param1:PKPlayer, param2:PKPlayer, param3:PKPlayer, param4:PKPlayer) : void
      {
         m_myPkPlayer1 = param1;
         m_myPkPlayer2 = param2;
         m_foePkPlayer1 = param3;
         m_foePkPlayer2 = param4;
         initShow2();
      }
      
      public function refreshShow() : void
      {
         if(m_myPkPlayer1)
         {
            changeHpBarShow(m_myPlayer1HpBar,m_myPkPlayer1);
         }
         if(m_myPkPlayer2)
         {
            changeHpBarShow(m_myPlayer2HpBar,m_myPkPlayer2);
         }
         if(m_foePkPlayer1)
         {
            changeHpBarShow(m_foePlayer1HpBar,m_foePkPlayer1);
         }
         if(m_foePkPlayer2)
         {
            changeHpBarShow(m_foePlayer2HpBar,m_foePkPlayer2);
         }
      }
      
      public function playRandomSideAnimation(param1:Boolean, param2:Function) : void
      {
         m_stopFun = param2;
         initGameStartFrame();
         if(param1)
         {
            m_randomToLeftShow.gotoAndPlay("1");
            (m_randomToLeftShow.getShow() as DisplayObject).visible = true;
            (m_randomToRightShow.getShow() as DisplayObject).visible = false;
         }
         else
         {
            m_randomToRightShow.gotoAndPlay("1");
            (m_randomToRightShow.getShow() as DisplayObject).visible = true;
            (m_randomToLeftShow.getShow() as DisplayObject).visible = false;
         }
      }
      
      public function playGameEndAnimation(param1:Boolean, param2:Function) : void
      {
         m_stopFun = param2;
         initGameEndFrame();
         if(param1)
         {
            m_winAnimationShow.gotoAndPlay("1");
            (m_winAnimationShow.getShow() as DisplayObject).visible = true;
            (m_failAnimationShow.getShow() as DisplayObject).visible = false;
            if(RankDataInfo.getInstance().getFightType() != 1)
            {
               (m_show["winAnimationShowrank"] as MovieClip).visible = false;
               (m_show["failAnimationShowrank"] as MovieClip).visible = false;
            }
         }
         else
         {
            m_failAnimationShow.gotoAndPlay("1");
            (m_failAnimationShow.getShow() as DisplayObject).visible = true;
            (m_winAnimationShow.getShow() as DisplayObject).visible = false;
            if(RankDataInfo.getInstance().getFightType() != 1)
            {
               (m_show["winAnimationShowrank"] as MovieClip).visible = false;
               (m_show["failAnimationShowrank"] as MovieClip).visible = false;
            }
         }
      }
      
      private function clearFrame() : void
      {
         ClearUtil.clearObject(m_randomToLeftShow);
         m_randomToLeftShow = null;
         ClearUtil.clearObject(m_randomToRightShow);
         m_randomToRightShow = null;
         ClearUtil.clearObject(m_winAnimationShow);
         m_winAnimationShow = null;
         ClearUtil.clearObject(m_failAnimationShow);
         m_failAnimationShow = null;
         ClearUtil.clearObject(m_nowOverBtn);
         m_nowOverBtn = null;
         ClearUtil.clearObject(m_backBtn);
         m_backBtn = null;
      }
      
      private function initGameStartFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("gameStart");
         m_randomToLeftShow = new AnimationShowPlayLogicShell();
         m_randomToRightShow = new AnimationShowPlayLogicShell();
         m_randomToLeftShow.addNextStopListener(m_stopListener);
         m_randomToRightShow.addNextStopListener(m_stopListener);
         m_randomToLeftShow.setShow(m_show["randomToLeftShow"]);
         m_randomToRightShow.setShow(m_show["randomToRightShow"]);
      }
      
      private function initGameEndFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("gameEnd");
         m_winAnimationShow = new AnimationShowPlayLogicShell();
         m_failAnimationShow = new AnimationShowPlayLogicShell();
         m_winAnimationShow.addNextStopListener(m_stopListener);
         m_failAnimationShow.addNextStopListener(m_stopListener);
         if(RankDataInfo.getInstance().getFightType() == 1)
         {
            m_winAnimationShow.setShow(m_show["winAnimationShowrank"]);
            m_failAnimationShow.setShow(m_show["failAnimationShowrank"]);
            (m_show["winAnimationShowrank"] as MovieClip).visible = false;
            (m_show["failAnimationShowrank"] as MovieClip).visible = false;
            m_show["winAnimationShow"].visible = false;
            m_show["failAnimationShow"].visible = false;
            m_show["failAnimationShow2"].visible = false;
            m_show["winAnimationShow2"].visible = false;
            m_xzpanel = m_show["xzpanel"] as MovieClip;
            m_xzpanel.visible = false;
            m_xzClose.setShow(m_xzpanel["btnclosexz"]);
            m_xzMc = m_xzpanel["rankgrademc"] as MovieClip;
            if(RankDataInfo.getInstance().getFightWin())
            {
               m_rank = m_show["endrankwintext"] as TextField;
               (m_show["endrankwintext"] as TextField).visible = true;
               (m_show["endrankfailtext"] as TextField).visible = false;
               m_rank.text = "您的排名:" + String(RankDataInfo.getInstance().getPreRank()) + "-" + String(RankDataInfo.getInstance().getCurFightRank());
               (m_show["winAnimationShowrank"] as MovieClip).visible = true;
               (m_show["failAnimationShowrank"] as MovieClip).visible = false;
            }
            else
            {
               m_rank = m_show["endrankfailtext"] as TextField;
               (m_show["endrankwintext"] as TextField).visible = false;
               (m_show["endrankfailtext"] as TextField).visible = true;
               m_rank.text = "您的排名:" + String(RankDataInfo.getInstance().getPreRank()) + "-" + String(RankDataInfo.getInstance().getPreRank());
               (m_show["winAnimationShowrank"] as MovieClip).visible = false;
               (m_show["failAnimationShowrank"] as MovieClip).visible = true;
            }
         }
         else
         {
            (m_show["winAnimationShowrank"] as MovieClip).visible = false;
            (m_show["failAnimationShowrank"] as MovieClip).visible = false;
            (m_show["xzpanel"] as MovieClip).visible = false;
            (m_show["endrankwintext"] as TextField).visible = false;
            (m_show["endrankfailtext"] as TextField).visible = false;
            if(PlayerDataForPK.getInstance().extraAddPkPointRate > 0)
            {
               m_winAnimationShow.setShow(m_show["winAnimationShow2"]);
               m_failAnimationShow.setShow(m_show["failAnimationShow2"]);
               m_show["winAnimationShow"].visible = false;
               m_show["failAnimationShow"].visible = false;
            }
            else
            {
               m_winAnimationShow.setShow(m_show["winAnimationShow"]);
               m_failAnimationShow.setShow(m_show["failAnimationShow"]);
               m_show["failAnimationShow2"].visible = false;
               m_show["winAnimationShow2"].visible = false;
            }
         }
      }
      
      private function initNormalFrame() : void
      {
         clearFrame();
         m_showMC.gotoAndStop("normal");
         m_nowOverBtn = new ButtonLogicShell2();
         m_nowOverBtn.setShow(m_show["nowOverBtn"]);
      }
      
      private function initShow() : void
      {
         m_showMC.setShow(m_show);
         m_myPlayer1HeadShow.setShow(m_show["myPlayer1HeadShow"]);
         m_myPlayer2HeadShow.setShow(m_show["myPlayer2HeadShow"]);
         m_foePlayer1HeadShow.setShow(m_show["foePlayer1HeadShow"]);
         m_foePlayer2HeadShow.setShow(m_show["foePlayer2HeadShow"]);
         m_myPlayer1HeadBtn.setShow(m_myPlayer1HeadShow.getShow()["headBtn"]);
         m_myPlayer2HeadBtn.setShow(m_myPlayer2HeadShow.getShow()["headBtn"]);
         m_foePlayer1HeadBtn.setShow(m_foePlayer1HeadShow.getShow()["headBtn"]);
         m_foePlayer2HeadBtn.setShow(m_foePlayer2HeadShow.getShow()["headBtn"]);
         m_myPlayer1HpBar.setShow(m_show["myPlayer1HpBar"]);
         m_myPlayer2HpBar.setShow(m_show["myPlayer2HpBar"]);
         m_foePlayer1HpBar.setShow(m_show["foePlayer1HpBar"]);
         m_foePlayer2HpBar.setShow(m_show["foePlayer2HpBar"]);
         (m_show["randomToLeftShow"] as MovieClip).visible = false;
         (m_show["randomToRightShow"] as MovieClip).visible = false;
         m_myUidText = m_show["myUidText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_myUidText);
         m_foeUidText = m_show["foeUidText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_foeUidText);
      }
      
      private function initShow2() : void
      {
         if(m_show == null)
         {
            return;
         }
         if(m_myPkPlayer1 == null || m_foePkPlayer1 == null)
         {
            return;
         }
         if(m_myPkPlayer2 == null)
         {
            m_myPlayer2HeadShow.getShow().visible = false;
            m_myPlayer2HpBar.getShow().visible = false;
         }
         if(m_foePkPlayer2 == null)
         {
            m_foePlayer2HeadShow.getShow().visible = false;
            m_foePlayer2HpBar.getShow().visible = false;
         }
         changeHeadBtnShow(m_myPlayer1HeadShow,m_myPlayer1HeadBtn,m_myPkPlayer1);
         if(m_myPkPlayer2)
         {
            changeHeadBtnShow(m_myPlayer2HeadShow,m_myPlayer2HeadBtn,m_myPkPlayer2);
         }
         changeHeadBtnShow(m_foePlayer1HeadShow,m_foePlayer1HeadBtn,m_foePkPlayer1);
         if(m_foePkPlayer2)
         {
            changeHeadBtnShow(m_foePlayer2HeadShow,m_foePlayer2HeadBtn,m_foePkPlayer2);
         }
         m_myUidText.text = m_myPkPlayer1.getPlayer().getUiPlayer().playerVO.playerUid;
         m_foeUidText.text = m_foePkPlayer1.getPlayer().getUiPlayer().playerVO.playerUid;
         changeHpBarShow(m_myPlayer1HpBar,m_myPkPlayer1);
         if(m_myPkPlayer2)
         {
            changeHpBarShow(m_myPlayer2HpBar,m_myPkPlayer2);
         }
         changeHpBarShow(m_foePlayer1HpBar,m_foePkPlayer1);
         if(m_foePkPlayer2)
         {
            changeHpBarShow(m_foePlayer2HpBar,m_foePkPlayer2);
         }
      }
      
      private function changeHeadBtnShow(param1:MovieClipPlayLogicShell, param2:ButtonLogicShell2, param3:PKPlayer) : void
      {
         param1.gotoAndStop(param3.getPlayer().getUiPlayer().playerVO.playerType);
         param2.setShow(param1.getShow()["headBtn"]);
      }
      
      private function changeHpBarShow(param1:CMSXChangeBarLogicShell, param2:PKPlayer) : void
      {
         param1.change(param2.getPkBlood2() / param2.getTotalPkBlood());
         param1.setDataShow(param2.getPkBlood2() + "/" + param2.getTotalPkBlood());
      }
      
      private function showStop(param1:AnimationShowPlayLogicShell) : void
      {
         switch(param1)
         {
            case m_randomToLeftShow:
            case m_randomToRightShow:
               if(Boolean(m_stopFun))
               {
                  m_stopFun();
               }
               initNormalFrame();
               break;
            case m_winAnimationShow:
            case m_failAnimationShow:
               if(Boolean(m_stopFun))
               {
                  m_stopFun();
               }
               m_backBtn = new ButtonLogicShell();
               m_backBtn.setShow(param1.getShow()["yesBtn"]);
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_backBtn:
               if(m_pk != null)
               {
                  m_pk.closePKWorld();
               }
               else if(RankDataInfo.getInstance().getGradeIndex() > -1)
               {
                  m_xzpanel.visible = true;
                  m_xzMc.gotoAndStop(RankDataInfo.getInstance().getGradeIndex());
                  (m_winAnimationShow.getShow() as MovieClip).visible = false;
                  (m_failAnimationShow.getShow() as MovieClip).visible = false;
                  m_rank.visible = false;
               }
               else
               {
                  m_rankpk.closePKWorld();
               }
               break;
            case m_xzClose:
               m_rankpk.closePKWorld();
               break;
            case m_nowOverBtn:
               m_pkWorld.nowOverGame();
               break;
            case m_myPlayer1HeadBtn:
            case m_myPlayer2HeadBtn:
               if(m_pk != null)
               {
                  m_pk.lookUpMyPlayerInfor();
               }
               else
               {
                  m_rankpk.lookUpMyPlayerInfor();
               }
               break;
            case m_foePlayer1HeadBtn:
            case m_foePlayer2HeadBtn:
               if(m_pk != null)
               {
                  m_pk.lookUpFoePlayerInfor();
               }
               else
               {
                  m_rankpk.lookUpFoePlayerInfor();
               }
         }
      }
   }
}

