package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class CuboidAreaAttackSkill_EveryTargetAddShow extends CuboidAreaAttackSkill
   {
      
      protected var m_bodyDefId:String;
      
      protected var m_bodyAttackReachFrameLabel:String;
      
      protected var m_bodySkillEndFrameLabel:String;
      
      protected var m_everyEntityAddShowDefId:String;
      
      protected var m_everyEntityAddShowDefinitionData:AnimationDefinitionData;
      
      protected var m_everyEntityAddShowPlays:Vector.<AnimationEntityLogicShell>;
      
      protected var m_everyEntityAddShowPlayEndListener:StopListener;
      
      protected var m_everyEntityAddShowPlayReachFrameListener:AnimationPlayFrameLabelListener;
      
      protected var m_wasteEveryEntityAddShowPlays:Vector.<AnimationEntityLogicShell>;
      
      protected var m_everyEntityAddShowPlaysPool:ObjectsPool;
      
      public function CuboidAreaAttackSkill_EveryTargetAddShow()
      {
         super();
         m_everyEntityAddShowPlays = new Vector.<AnimationEntityLogicShell>();
         m_everyEntityAddShowPlayEndListener = new StopListener();
         m_everyEntityAddShowPlayEndListener.stop2Fun = AnimationStop;
         m_everyEntityAddShowPlayReachFrameListener = new AnimationPlayFrameLabelListener();
         m_everyEntityAddShowPlayReachFrameListener.reachFrameLabelFun2 = everyEntityAddShowPlayReachFrame;
         m_wasteEveryEntityAddShowPlays = new Vector.<AnimationEntityLogicShell>();
         m_everyEntityAddShowPlaysPool = new ObjectsPool(m_everyEntityAddShowPlays,m_wasteEveryEntityAddShowPlays,createEntityShow,null);
      }
      
      override public function clear() : void
      {
         clearWasteEveryEntityAddShowAnimationPlays();
         clearEveryEntityAddShowAnimationPlays();
         m_bodyDefId = null;
         m_bodyAttackReachFrameLabel = null;
         m_bodySkillEndFrameLabel = null;
         m_everyEntityAddShowDefId = null;
         ClearUtil.clearObject(m_everyEntityAddShowDefinitionData);
         m_everyEntityAddShowDefinitionData = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlays);
         m_everyEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayEndListener);
         m_everyEntityAddShowPlayEndListener = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayReachFrameListener);
         m_everyEntityAddShowPlayReachFrameListener = null;
         ClearUtil.clearObject(m_wasteEveryEntityAddShowPlays);
         m_wasteEveryEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlaysPool);
         m_everyEntityAddShowPlaysPool = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_bodyAttackReachFrameLabel = String(param1.@bodyAttackReachFrameLabel);
         m_bodySkillEndFrameLabel = String(param1.@bodySkillEndFrameLabel);
         m_everyEntityAddShowDefId = String(param1.@everyEntityAddShowDefId);
         if(m_everyEntityAddShowDefId)
         {
            m_everyEntityAddShowDefinitionData = new AnimationDefinitionData();
            m_everyEntityAddShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_everyEntityAddShowDefId)[0]);
         }
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            addEntityShow(param1);
         }
      }
      
      protected function addEntityShow(param1:IEntity) : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         if(m_everyEntityAddShowDefinitionData)
         {
            _loc2_ = m_everyEntityAddShowPlaysPool.getOneOrCreateOneObj() as AnimationEntityLogicShell;
            _loc2_.init(param1.getX(),param1.getY(),0);
            m_world.addEntity(_loc2_);
            _loc2_.getAniamtionShowPlay().extra = param1;
            _loc2_.getAniamtionShowPlay().gotoAndPlay("start");
         }
      }
      
      protected function createEntityShow() : AnimationEntityLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_everyEntityAddShowDefinitionData));
         var _loc1_:AnimationEntityLogicShell = new AnimationEntityLogicShell();
         _loc1_.setShow(_loc2_);
         _loc1_.getAniamtionShowPlay().addNextStopListener(m_everyEntityAddShowPlayEndListener);
         _loc1_.getAniamtionShowPlay().addFrameLabelListener(m_everyEntityAddShowPlayReachFrameListener);
         return _loc1_;
      }
      
      protected function AnimationStop(param1:AnimationShowPlayLogicShell) : void
      {
         var _loc4_:int = 0;
         var _loc3_:AnimationEntityLogicShell = null;
         var _loc2_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(m_everyEntityAddShowPlays[_loc4_].getAniamtionShowPlay() == param1)
            {
               _loc3_ = m_everyEntityAddShowPlays[_loc4_];
               _loc3_.getAniamtionShowPlay().extra;
               m_world.removeEntity(_loc3_);
               m_everyEntityAddShowPlaysPool.wasteOneObj(_loc3_);
               _loc4_--;
               _loc2_--;
            }
            _loc4_++;
         }
      }
      
      protected function everyEntityAddShowPlayReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
      
      protected function clearWasteEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         var _loc1_:int = m_wasteEveryEntityAddShowPlays ? m_wasteEveryEntityAddShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_wasteEveryEntityAddShowPlays[0];
            m_wasteEveryEntityAddShowPlays.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_.getAnimationShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_wasteEveryEntityAddShowPlays = null;
      }
      
      protected function clearEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:AnimationEntityLogicShell = null;
         var _loc1_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_everyEntityAddShowPlays[0];
            m_everyEntityAddShowPlays.splice(0,1);
            _loc1_--;
            _loc2_.getAniamtionShowPlay().extra = null;
            if(m_world)
            {
               m_world.removeEntity(_loc2_);
            }
            ClearUtil.clearObject(_loc2_.getAnimationShow());
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_everyEntityAddShowPlays = null;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_bodySkillEndFrameLabel:
               endSkill1();
               break;
            case m_bodyAttackReachFrameLabel:
               attackReach(m_world);
         }
      }
   }
}

