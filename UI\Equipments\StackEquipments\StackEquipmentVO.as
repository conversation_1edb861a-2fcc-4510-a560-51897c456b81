package UI.Equipments.StackEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public class StackEquipmentVO extends EquipmentVO
   {
      
      private var _num:int = 1;
      
      private var _maxSuperposition:int = 100;
      
      public function StackEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.num = _num;
         _antiwear.maxSuperposition = _maxSuperposition;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:StackEquipmentVO = new StackEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as StackEquipmentVO).num = this.num;
         (param1 as StackEquipmentVO).maxSuperposition = this.maxSuperposition;
      }
      
      public function get num() : int
      {
         return _antiwear.num;
      }
      
      public function set num(param1:int) : void
      {
         _antiwear.num = param1;
      }
      
      public function get maxSuperposition() : int
      {
         return _antiwear.maxSuperposition;
      }
      
      public function set maxSuperposition(param1:int) : void
      {
         _antiwear.maxSuperposition = param1;
      }
   }
}

