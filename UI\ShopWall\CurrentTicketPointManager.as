package UI.ShopWall
{
   import UI.DataManagerParent;
   
   public class CurrentTicketPointManager extends DataManagerParent
   {
      
      public static var _instance:CurrentTicketPointManager = null;
      
      private var m_currentTicketPoint:Number;
      
      private var m_totalTicketPoint:Number;
      
      public function CurrentTicketPointManager()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗？！");
      }
      
      public static function getInstance() : CurrentTicketPointManager
      {
         if(!_instance)
         {
            _instance = new CurrentTicketPointManager();
         }
         return _instance;
      }
      
      override public function clear() : void
      {
         super.clear();
         _instance = null;
      }
      
      public function getCurrentTicketPoint() : Number
      {
         return currentTicketPoint;
      }
      
      public function setCurrentTicketPoint(param1:Number) : void
      {
         currentTicketPoint = param1;
      }
      
      public function getTotalTicketPoint() : Number
      {
         return totalTicketPoint;
      }
      
      public function setTotalTicketPoint(param1:Number) : void
      {
         totalTicketPoint = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currentTicketPoint = m_currentTicketPoint;
         _antiwear.totalTicketPoint = m_totalTicketPoint;
      }
      
      private function get currentTicketPoint() : Number
      {
         return _antiwear.currentTicketPoint;
      }
      
      private function set currentTicketPoint(param1:Number) : void
      {
         _antiwear.currentTicketPoint = param1;
      }
      
      private function get totalTicketPoint() : Number
      {
         return _antiwear.totalTicketPoint;
      }
      
      private function set totalTicketPoint(param1:Number) : void
      {
         _antiwear.totalTicketPoint = param1;
      }
   }
}

