package YJFY.Lottery
{
   import YJFY.Utils.ClearUtil;
   
   public class LotteryData
   {
      
      private var m_lotteryDataOnes:Vector.<LotteryDataOne>;
      
      public function LotteryData()
      {
         super();
         m_lotteryDataOnes = new Vector.<LotteryDataOne>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_lotteryDataOnes);
         m_lotteryDataOnes = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc4_:LotteryDataOne = null;
         var _loc3_:XMLList = param1.item;
         var _loc2_:int = int(_loc3_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = new LotteryDataOne(_loc3_[_loc5_].@id,_loc3_[_loc5_].@num,_loc3_[_loc5_].@proWeight);
            m_lotteryDataOnes.push(_loc4_);
            _loc5_++;
         }
      }
      
      public function randomOneLotteryDataOne() : LotteryDataOne
      {
         var _loc1_:* = 0;
         var _loc5_:int = 0;
         var _loc2_:Number = Math.random();
         var _loc3_:int = int(m_lotteryDataOnes.length);
         var _loc4_:Number = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ += m_lotteryDataOnes[_loc5_].getProWeight();
            _loc5_++;
         }
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ += m_lotteryDataOnes[_loc5_].getProWeight() / _loc1_;
            if(_loc4_ > _loc2_)
            {
               return m_lotteryDataOnes[_loc5_];
            }
            _loc5_++;
         }
         return m_lotteryDataOnes[_loc3_ - 1];
      }
      
      public function randomOneLotteryDataOne2(param1:IFilter) : LotteryDataOne
      {
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:* = 0;
         var _loc5_:Vector.<LotteryDataOne> = new Vector.<LotteryDataOne>(m_lotteryDataOnes.length);
         _loc4_ = int(m_lotteryDataOnes.length);
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            if(param1.runFilter(m_lotteryDataOnes[_loc8_]))
            {
               _loc5_[_loc6_] = m_lotteryDataOnes[_loc8_];
               _loc6_++;
            }
            _loc8_++;
         }
         var _loc3_:Number = Math.random();
         _loc4_ = int(_loc5_.length);
         var _loc7_:Number = 0;
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            if(_loc5_[_loc8_])
            {
               _loc2_ += _loc5_[_loc8_].getProWeight();
            }
            _loc8_++;
         }
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            if(_loc5_[_loc8_])
            {
               _loc7_ += _loc5_[_loc8_].getProWeight() / _loc2_;
               if(_loc7_ > _loc3_)
               {
                  return _loc5_[_loc8_];
               }
            }
            _loc8_++;
         }
         ClearUtil.nullArr(_loc5_,false,false,false);
         _loc5_ = null;
         return null;
      }
      
      public function randomMultiLotteryDataOnes(param1:uint) : Vector.<LotteryDataOne>
      {
         var _loc2_:* = 0;
         var _loc8_:int = 0;
         var _loc3_:Number = NaN;
         if(param1 == 0)
         {
            throw new Error("num must big than 0");
         }
         var _loc4_:int = int(m_lotteryDataOnes.length);
         _loc8_ = 0;
         while(_loc8_ < _loc4_)
         {
            _loc2_ += m_lotteryDataOnes[_loc8_].getProWeight();
            _loc8_++;
         }
         var _loc7_:Number = 0;
         var _loc5_:Vector.<LotteryDataOne> = m_lotteryDataOnes.slice(0);
         var _loc6_:Vector.<LotteryDataOne> = new Vector.<LotteryDataOne>();
         while(param1)
         {
            _loc3_ = Math.random();
            _loc4_ = int(_loc5_.length);
            _loc8_ = 0;
            while(_loc8_ < _loc4_)
            {
               _loc7_ += _loc5_[_loc8_].getProWeight() / _loc2_;
               if(_loc7_ > _loc3_)
               {
                  _loc2_ -= _loc5_[_loc8_].getProWeight();
                  _loc6_.push(_loc5_[_loc8_].clone());
                  _loc5_.splice(_loc8_,1);
                  param1--;
                  break;
               }
               _loc8_++;
            }
         }
         ClearUtil.nullArr(_loc5_,false,false,false);
         _loc5_ = null;
         return _loc6_;
      }
      
      public function getLotteryDataOneNum() : int
      {
         return m_lotteryDataOnes.length;
      }
      
      public function getLotteryDataOneByIndex(param1:int) : LotteryDataOne
      {
         return m_lotteryDataOnes[param1];
      }
      
      public function clone() : LotteryData
      {
         var _loc1_:LotteryData = new LotteryData();
         copy(_loc1_);
         return _loc1_;
      }
      
      public function copy(param1:LotteryData) : void
      {
         var _loc3_:int = 0;
         ClearUtil.clearObject(param1.m_lotteryDataOnes);
         param1.m_lotteryDataOnes.length = 0;
         var _loc2_:int = int(m_lotteryDataOnes.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            param1.m_lotteryDataOnes.push(m_lotteryDataOnes[_loc3_].clone());
            _loc3_++;
         }
      }
   }
}

