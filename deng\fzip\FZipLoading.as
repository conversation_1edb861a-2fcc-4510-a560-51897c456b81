package deng.fzip
{
   import YJFY.LoadUI2;
   import YJFY.VersionControl.IVersionControl;
   import com.utils.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import flash.utils.setTimeout;
   
   public class FZipLoading
   {
      
      public static var dic:Dictionary = new Dictionary();
      
      private var _fZip:FZip;
      
      private var _errorIndex:int = 0;
      
      private var _loadUIProgress:LoadUI2;
      
      private var _successFunc:Function;
      
      private var _versionControl:IVersionControl;
      
      protected var _urlLoader:URLLoader;
      
      public function FZipLoading()
      {
         super();
      }
      
      protected function onLoadError(param1:IOErrorEvent) : void
      {
         if(this._errorIndex < 2)
         {
            this._errorIndex++;
            setTimeout(load,1000);
         }
         else
         {
            if(this._errorIndex >= 4)
            {
               throw new Error("找不到数据包，可能网络阻塞引起的。" + param1.text);
            }
            this._errorIndex++;
            setTimeout(load,1000);
         }
      }
      
      public function initLoaders(param1:LoadUI2, param2:Function = null, param3:IVersionControl = null) : void
      {
         this._fZip = new FZip();
         this._fZip.addEventListener("ioError",this.onLoadError);
         this._fZip.addEventListener("complete",this.onItemComplete);
         this._fZip.addEventListener("progress",onLoadProgress);
         this._urlLoader = new URLLoader();
         this._urlLoader.dataFormat = "binary";
         this._urlLoader.addEventListener("complete",this.onBytesLoadComplete);
         this._urlLoader.addEventListener("ioError",this.onLoadError);
         this._urlLoader.addEventListener("progress",this.onLoadProgress);
         _loadUIProgress = param1;
         _successFunc = param2;
         _versionControl = param3;
      }
      
      protected function onLoadProgress(param1:ProgressEvent) : void
      {
         var _loc2_:Number = param1.bytesLoaded / param1.bytesTotal;
         if(_loadUIProgress)
         {
            _loadUIProgress.setProgress(_loc2_,param1.bytesLoaded,param1.bytesTotal,0,0,0);
         }
      }
      
      protected function onItemComplete(param1:Event) : void
      {
         var _loc4_:* = undefined;
         var _loc5_:int = 0;
         var _loc3_:FZipFile = null;
         var _loc2_:FZip = param1.currentTarget as FZip;
         var _loc6_:int = int(_loc2_.getFileCount());
         _loc5_ = 0;
         while(_loc5_ < _loc6_)
         {
            _loc3_ = _loc2_.getFileAt(_loc5_);
            _loc4_ = _loc3_.content.readUTFBytes(_loc3_.content.bytesAvailable);
            if(_loc4_)
            {
               dic[_loc3_.filename] = _loc4_;
            }
            _loc5_++;
         }
         _loc2_.close();
         if(Boolean(_successFunc))
         {
            _successFunc();
         }
      }
      
      protected function onBytesLoadComplete(param1:Event) : void
      {
         var _loc5_:ByteArray = null;
         var _loc4_:int = 0;
         var _loc6_:String = null;
         var _loc2_:String = null;
         var _loc3_:ByteArray = new ByteArray();
         try
         {
            _loc5_ = _urlLoader.data as ByteArray;
            _loc5_.position = 0;
            _loc4_ = _loc5_.readInt();
            _loc5_.readBytes(_loc3_,0);
            _loc3_.uncompress();
         }
         catch(error:*)
         {
            _loc6_ = "data" + _versionControl.getXMLVersionStr() + ".swf" + "?" + MD5.hash(_versionControl.getXMLVersionStr() + Math.random() * 1000);
            _urlLoader.load(new URLRequest(_loc6_));
         }
         if(_loc3_.length > 0)
         {
            this._fZip.loadBytes(_loc3_);
         }
         else
         {
            _loc2_ = "data" + _versionControl.getXMLVersionStr() + ".swf" + "?" + MD5.hash(_versionControl.getXMLVersionStr() + Math.random() * 1000);
            _urlLoader.load(new URLRequest(_loc2_));
         }
      }
      
      public function load() : void
      {
         if(_versionControl)
         {
            this._urlLoader.load(new URLRequest("data" + _versionControl.getXMLVersionStr() + ".swf" + "?" + MD5.hash(_versionControl.getXMLVersionStr() + Math.random() * 1000)));
         }
      }
      
      public function get fZip() : FZip
      {
         return this._fZip;
      }
   }
}

