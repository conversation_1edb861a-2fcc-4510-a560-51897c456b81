package YJFY.Skill.PetSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   
   public class Skill_Pet17Skill extends CuboidAreaAttackSkill implements IPushSkill
   {
      
      private const m_const_attackReachFrameLabel:String = "reach";
      
      private const m_const_skillEndFrameLabel:String = "end^stop^";
      
      private const m_const_bodyDefId:String = "petSkillBodyShow";
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_isPushEntity:Boolean;
      
      private var m_pushForce:P3DVector3D;
      
      private var m_pushForce2:P3DVector3D;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private var m_isStartPush:Boolean;
      
      public function Skill_Pet17Skill()
      {
         super();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
         m_pushForce = new P3DVector3D();
         m_pushForce2 = new P3DVector3D();
         m_pushForce = new P3DVector3D(5000,0,0);
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = pushEntity;
      }
      
      override public function clear() : void
      {
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         ClearUtil.clearObject(m_pushForce);
         m_pushForce = null;
         ClearUtil.clearObject(m_pushForce2);
         m_pushForce2 = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun && m_isStartPush)
         {
            renderPushEntity();
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow("petSkillBodyShow");
         m_owner.currentAnimationGotoAndPlay("1");
         m_owner.setShowDirection((m_owner.getExtra() as PetXydzjs).getOwner().getAnimalEntity().getShowDirection());
         super.releaseSkill2(param1);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "reach":
               attackReach(m_world);
               m_isStartPush = true;
               break;
            case "end^stop^":
               endSkill1();
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         m_isPushEntity = false;
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      private function renderPushEntity() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      private function pushEntity(param1:IEntity) : void
      {
         dispatchPushEntity(param1);
         if(m_isPushEntity)
         {
            m_pushForce2.multi2(m_owner.getShowDirection(),m_pushForce);
            m_pushForce2.multi2(param1.getBody().getMass(),m_pushForce2);
            param1.applyForce(m_pushForce2);
         }
      }
      
      private function dispatchPushEntity(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
   }
}

