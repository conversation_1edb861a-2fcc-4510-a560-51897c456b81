package UI.InitPlayerData
{
   public class GetPlayerDataListener implements IGetPlayerDataListener
   {
      
      public var getPlayerDataFun:Function;
      
      public var getPlayerDataFun2:Function;
      
      public function GetPlayerDataListener()
      {
         super();
      }
      
      public function clear() : void
      {
         getPlayerDataFun = null;
         getPlayerDataFun2 = null;
      }
      
      public function getPlayerData(param1:InitPlayersData) : void
      {
         if(Boolean(getPlayerDataFun))
         {
            getPlayerDataFun(param1);
         }
      }
      
      public function getPlayerData2(param1:InitPlayersData, param2:PlayerDatas) : void
      {
         if(<PERSON><PERSON><PERSON>(getPlayerDataFun2))
         {
            getPlayerDataFun2(param1,param2);
         }
      }
   }
}

