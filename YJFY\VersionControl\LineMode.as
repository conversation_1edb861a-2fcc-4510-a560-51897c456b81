package YJFY.VersionControl
{
   public class LineMode
   {
      
      public static const ON_LINE:String = "onLine";
      
      public static const OFF_LINE:String = "offLine";
      
      private var m_lineMode:String = "offLine";
      
      public function LineMode()
      {
         super();
      }
      
      public function getLineMode() : String
      {
         return m_lineMode;
      }
      
      public function setLineMode(param1:String) : void
      {
         if(param1 != "onLine" && param1 != "offLine")
         {
            throw new Error("lineMode 必须是LineMode.ON_LINE 或 LineMode.OFF_LINE");
         }
         m_lineMode = param1;
      }
   }
}

