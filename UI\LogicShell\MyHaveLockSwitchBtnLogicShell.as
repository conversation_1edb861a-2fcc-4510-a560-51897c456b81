package UI.LogicShell
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.SwitchBtn.HaveLockSwitchBtnLogicShell;
   
   public class MyHaveLockSwitchBtnLogicShell extends HaveLockSwitchBtnLogicShell
   {
      
      public function MyHaveLockSwitchBtnLogicShell()
      {
         super();
      }
      
      public static function setDefaultActivateBtnFromBtns(param1:Array) : MyHaveLockSwitchBtnLogicShell
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 == null)
         {
            throw new Error();
         }
         _loc2_ = int(param1.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(!MyHaveLockSwitchBtnLogicShell(param1[_loc3_]).isLock())
            {
               MyHaveLockSwitchBtnLogicShell(param1[_loc3_]).turnActivate();
               MyHaveLockSwitchBtnLogicShell(param1[_loc3_]).getShow().dispatchEvent(new ButtonEvent("clickButton",param1[0]));
               return param1[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      override protected function createSmallToolTip() : void
      {
         super.createSmallToolTip();
         m_smallToolTip.setFont(new FangZhengKaTongJianTi());
      }
   }
}

