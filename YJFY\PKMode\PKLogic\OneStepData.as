package YJFY.PKMode.PKLogic
{
   import YJFY.StepAttackGame.IEntity;
   import YJFY.Utils.ClearUtil;
   
   public class OneStepData
   {
      
      private var m_attackEntity:IEntity;
      
      private var m_beAttackDatas:Vector.<BeAttackDataOfOneStepData>;
      
      public function OneStepData(param1:IEntity)
      {
         super();
         m_attackEntity = param1;
         m_beAttackDatas = new Vector.<BeAttackDataOfOneStepData>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_attackEntity);
         m_attackEntity = null;
         ClearUtil.clearObject(m_beAttackDatas);
         m_beAttackDatas = null;
      }
      
      public function addBeAttackData(param1:BeAttackDataOfOneStepData) : void
      {
         m_beAttackDatas.push(param1);
      }
      
      public function getAttackEntity() : IEntity
      {
         return m_attackEntity;
      }
      
      public function getBeAttackDataNum() : int
      {
         return m_beAttackDatas.length;
      }
      
      public function getBeAttackDataByIndex(param1:int) : BeAttackDataOfOneStepData
      {
         return m_beAttackDatas[param1];
      }
   }
}

