package UI.VIPPanel
{
   import UI.ChangeBar;
   import UI.UIInterface.ISegmentedBar;
   import flash.geom.Rectangle;
   
   public class VIPProgressBar extends ChangeBar implements ISegmentedBar
   {
      
      public var yellowBar:VIPYellowBar;
      
      public var vipProgressBarMask:VipProgressBarMask;
      
      private const SCALE_X:Number = 10;
      
      private const SCALE_Y:Number = 10;
      
      public function VIPProgressBar()
      {
         super();
         _width = yellowBar.width;
         _heidth = yellowBar.height;
      }
      
      override public function clear() : void
      {
         if(yellowBar)
         {
            yellowBar.clear();
         }
         if(vipProgressBarMask)
         {
            vipProgressBarMask.clear();
         }
         yellowBar = null;
         vipProgressBarMask = null;
      }
      
      public function change(param1:Number, param2:Boolean = false) : void
      {
         changebar(yellowBar,vipProgressBarMask,param1,0,param2);
      }
      
      public function t() : void
      {
         scale9Grid = new Rectangle(10,10,width - 2 * 10,height - 2 * 10);
      }
   }
}

