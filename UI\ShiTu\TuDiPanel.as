package UI.ShiTu
{
   import GM_UI.GMData;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.MyControlPanel;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Players.Player;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.OnePlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.text.TextField;
   
   public class TuDiPanel extends MySprite
   {
      
      private static const htmlText:String = "<b><font size=\'30\' color=\'#ffffff\'>没有出战宠物</font><br><font size=\'20\' color=\'#ffffff\'><li>点击背包中的宠物选择出战</li><li>宠物会在战斗中随机出现</li><li>也可以通过宠物蛋孵化获得</li></font></b>";
      
      private static const TEXT_FIELD_X:Number = 55;
      
      private static const TEXT_FIELD_Y:Number = 200;
      
      private var _show:MovieClip;
      
      private var _tuDiShowPanel:Sprite;
      
      private var _bloodBar:CMSXChangeBarLogicShell;
      
      private var _energyBar:CMSXChangeBarLogicShell;
      
      private var _xiuLianValueBar:CMSXChangeBarLogicShell;
      
      private var _levelShowNum:OnePlaceNumLogicShell;
      
      private var _attackText:TextField;
      
      private var _defenceText:TextField;
      
      private var _criticalRateText:TextField;
      
      private var _xiuLianBtn:ButtonLogicShell2;
      
      private var _tuDiNameShow:MovieClipPlayLogicShell;
      
      private var _isShowBtn:Boolean;
      
      private var _xiuLianPanel:XiuLianPanel;
      
      private var _currentPlayer:Player;
      
      private var _loadSwfName:String;
      
      public function TuDiPanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         ClearUtil.clearDisplayObjectInContainer(this);
         ClearUtil.clearDisplayObjectInContainer(_show);
         ClearUtil.clearDisplayObjectInContainer(_tuDiShowPanel);
         _tuDiShowPanel = null;
         if(_xiuLianPanel)
         {
            _xiuLianPanel.clear();
            _xiuLianPanel.removeEventListener("clickQuitBtn",quit,true);
         }
         _currentPlayer = null;
         _xiuLianPanel = null;
         _show = null;
         if(_bloodBar)
         {
            _bloodBar.clear();
         }
         _bloodBar = null;
         if(_energyBar)
         {
            _energyBar.clear();
         }
         _energyBar = null;
         if(_xiuLianValueBar)
         {
            _xiuLianValueBar.clear();
         }
         _xiuLianValueBar = null;
         if(_levelShowNum)
         {
            _levelShowNum.clear();
         }
         _levelShowNum = null;
         _attackText = null;
         _defenceText = null;
         _criticalRateText = null;
         if(_xiuLianBtn)
         {
            _xiuLianBtn.clear();
         }
         _xiuLianBtn = null;
         if(_tuDiNameShow)
         {
            _tuDiNameShow.clear();
         }
         _tuDiNameShow = null;
      }
      
      public function get show() : MovieClip
      {
         return _show;
      }
      
      private function init() : void
      {
         _show = MyFunction2.returnShowByClassName("TuDiPanel") as MovieClip;
         addChild(_show);
      }
      
      private function initOneFrame() : void
      {
         var _loc1_:FangZhengKaTongJianTi = null;
         if(_show.currentFrame != 1 || _levelShowNum == null)
         {
            _loc1_ = new FangZhengKaTongJianTi();
            _show.gotoAndStop(1);
            _tuDiShowPanel = _show["tuDiShowPanel"];
            _levelShowNum = new OnePlaceNumLogicShell();
            _levelShowNum.setShow(_show["levelShow"]["one"]);
            _attackText = _show["attackText"];
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_attackText,true);
            _defenceText = _show["defenceText"];
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_defenceText,true);
            _criticalRateText = _show["criticalRateText"];
            MyFunction2.changeTextFieldFont(_loc1_.fontName,_criticalRateText,true);
            _bloodBar = new CMSXChangeBarLogicShell();
            _bloodBar.setShow(_show["bloodBar"]);
            _energyBar = new CMSXChangeBarLogicShell();
            _energyBar.setShow(_show["energyBar"]);
            _xiuLianValueBar = new CMSXChangeBarLogicShell();
            _xiuLianValueBar.setShow(_show["xiuLianValueBar"]);
            _xiuLianBtn = new ButtonLogicShell2();
            _xiuLianBtn.setShow(_show["xiuLianBtn"]);
            _xiuLianBtn.setTipString("点击查看修炼详细信息");
            _tuDiNameShow = new MovieClipPlayLogicShell();
            _tuDiNameShow.setShow(_show["tuDiNameShow"]);
         }
      }
      
      private function clearOneFrameRefrence() : void
      {
         if(_levelShowNum)
         {
            _levelShowNum.clear();
         }
         _levelShowNum = null;
         _attackText = null;
         _defenceText = null;
         _criticalRateText = null;
         if(_bloodBar)
         {
            _bloodBar.clear();
         }
         _bloodBar = null;
         if(_energyBar)
         {
            _energyBar.clear();
         }
         _energyBar = null;
         if(_xiuLianValueBar)
         {
            _xiuLianValueBar.clear();
         }
         _xiuLianValueBar = null;
         if(_xiuLianBtn)
         {
            _xiuLianBtn.clear();
         }
         _xiuLianBtn = null;
         if(_tuDiNameShow)
         {
            _tuDiNameShow.clear();
         }
         _tuDiNameShow = null;
      }
      
      public function set currentPlayer(param1:Player) : void
      {
         _currentPlayer = param1;
      }
      
      public function refresh(param1:TuDiVO) : void
      {
         var newLoadSwfName:String;
         var tuDiPanel:TuDiPanel;
         var loadSwfName:String;
         var loadFinishLitener:LoadFinishListener1;
         var show:Sprite;
         var tuDiVO:TuDiVO = param1;
         if(tuDiVO == null)
         {
            _show.gotoAndStop(2);
            _loadSwfName = "";
            clearOneFrameRefrence();
            return;
         }
         initOneFrame();
         newLoadSwfName = tuDiVO.type + "Show";
         if(_tuDiNameShow)
         {
            _tuDiNameShow.gotoAndStop(tuDiVO.type);
         }
         if(!Boolean(_loadSwfName) || newLoadSwfName != _loadSwfName)
         {
            tuDiPanel = this;
            _loadSwfName = newLoadSwfName;
            loadSwfName = _loadSwfName;
            if(GMData.getInstance().isGMApplication == false)
            {
               loadFinishLitener = new LoadFinishListener1(function():void
               {
                  var _loc1_:Sprite = MyFunction2.returnShowByClassName(tuDiVO.type + "_stand") as Sprite;
                  _loc1_.scaleX = _loc1_.scaleY = 1.2;
                  tuDiPanel.addChildToShowPanel(_loc1_);
                  tuDiPanel = null;
                  _loc1_ = null;
               },null);
               GamingUI.getInstance().loadQueue.load([_loadSwfName],loadFinishLitener);
            }
            else
            {
               show = MyFunction2.returnShowByClassName(tuDiVO.type + "_stand") as Sprite;
               show.scaleX = show.scaleY = 1.2;
               tuDiPanel.addChildToShowPanel(show);
            }
         }
         _bloodBar.change(tuDiVO.bloodPercent);
         _bloodBar.setDataShow("" + Math.round(tuDiVO.blood * tuDiVO.bloodPercent) + "/" + tuDiVO.blood);
         _energyBar.change(tuDiVO.energyPercent);
         _energyBar.setDataShow("" + Math.round(tuDiVO.energy * tuDiVO.energyPercent) + "/" + tuDiVO.energy);
         _xiuLianValueBar.change(tuDiVO.currentXiuLianValue / tuDiVO.xiuLianValue);
         _xiuLianValueBar.setDataShow("" + tuDiVO.currentXiuLianValue + "/" + tuDiVO.xiuLianValue);
         _attackText.text = tuDiVO.attack.toString();
         _defenceText.text = tuDiVO.defence.toString();
         _criticalRateText.text = tuDiVO.criticalRate + "%";
         _levelShowNum.showNum(tuDiVO.level);
      }
      
      public function addChildToShowPanel(param1:DisplayObject) : void
      {
         while(_tuDiShowPanel.numChildren > 1)
         {
            _tuDiShowPanel.removeChildAt(1);
         }
         if(param1)
         {
            _tuDiShowPanel.addChild(param1);
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
      }
      
      private function quit(param1:UIBtnEvent) : void
      {
         var e:UIBtnEvent = param1;
         var _loc2_:* = e.currentTarget;
         if(_xiuLianPanel === _loc2_)
         {
            if(_xiuLianPanel)
            {
               _xiuLianPanel.playRemovedAnimation(function():void
               {
                  dispatchEvent(new UIPassiveEvent("hideMessageBox"));
                  if(parent is MyControlPanel)
                  {
                     (parent as MyControlPanel).myPackage.removeDragTarget(_xiuLianPanel);
                  }
                  if(Boolean(_xiuLianPanel) && _xiuLianPanel.parent)
                  {
                     _xiuLianPanel.parent.removeChild(_xiuLianPanel);
                  }
                  if(_xiuLianPanel)
                  {
                     _xiuLianPanel.removeEventListener("clickQuitBtn",quit,true);
                  }
                  if(_xiuLianPanel)
                  {
                     _xiuLianPanel.clear();
                  }
                  _xiuLianPanel = null;
               },[]);
            }
         }
      }
      
      public function get xiuLianPanel() : XiuLianPanel
      {
         return _xiuLianPanel;
      }
   }
}

