package YJFY.Lottery
{
   import UI.DataManagerParent;
   
   public class LotteryDataOne extends DataManagerParent
   {
      
      private var m_equipmentId:String;
      
      private var m_equipmentNum:uint;
      
      private var m_proWeight:uint;
      
      public function LotteryDataOne(param1:String, param2:uint, param3:uint)
      {
         super();
         this.equipmentId = param1;
         this.equipmentNum = param2;
         this.proWeight = param3;
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function getEquipmentId() : String
      {
         return equipmentId;
      }
      
      public function getEquipmentNum() : uint
      {
         return equipmentNum;
      }
      
      public function getProWeight() : uint
      {
         return proWeight;
      }
      
      public function clone() : LotteryDataOne
      {
         var _loc1_:LotteryDataOne = new LotteryDataOne("",0,0);
         copy(_loc1_);
         return _loc1_;
      }
      
      public function copy(param1:LotteryDataOne) : void
      {
         param1.equipmentId = this.equipmentId;
         param1.equipmentNum = this.equipmentNum;
         param1.proWeight = this.proWeight;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.equipmentId = m_equipmentId;
         _antiwear.proWeight = m_proWeight;
         _antiwear.equipmentNum = m_equipmentNum;
      }
      
      private function set equipmentId(param1:String) : void
      {
         _antiwear.equipmentId = param1;
      }
      
      private function get equipmentId() : String
      {
         return _antiwear.equipmentId;
      }
      
      private function set equipmentNum(param1:uint) : void
      {
         _antiwear.equipmentNum = param1;
      }
      
      private function get equipmentNum() : uint
      {
         return _antiwear.equipmentNum;
      }
      
      private function set proWeight(param1:uint) : void
      {
         _antiwear.proWeight = param1;
      }
      
      private function get proWeight() : uint
      {
         return _antiwear.proWeight;
      }
   }
}

