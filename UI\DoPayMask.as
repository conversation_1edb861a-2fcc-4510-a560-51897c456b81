package UI
{
   import UI.MyFont.FangZhengKaTongJianTi;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class DoPayMask extends MySprite
   {
      
      public var text:TextField;
      
      public var wheel:MovieClip;
      
      public function DoPayMask()
      {
         super();
         text.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,40,16777215);
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         text = null;
         wheel = null;
      }
   }
}

