package UI.PKUI.PKRankListPanel
{
   import UI.Event.UIBtnEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.PKUI.PlayerDataForPK;
   import UI.PKUI.RankListPanel;
   import YJFY.GameData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class OnePlayerAllPKRankListPanel extends RankListPanel
   {
      
      private var m_lookPkHonourPanelBtn:ButtonLogicShell;
      
      private var m_btnTotal:ButtonLogicShell2 = new ButtonLogicShell2();
      
      private var m_btnMonth:ButtonLogicShell2 = new ButtonLogicShell2();
      
      private var m_remBtn:ButtonLogicShell2;
      
      public function OnePlayerAllPKRankListPanel()
      {
         super();
         _isTwoMode = false;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("OnePKRankList") as MovieClip;
         addChild(m_show);
         m_btnTotal.setShow(m_show["totalRankBtn"]);
         m_btnMonth.setShow(m_show["monthRankBtn"]);
         setBtnLock(m_btnMonth);
         super.init(param1,param2);
         m_lookPkHonourPanelBtn = new ButtonLogicShell();
         m_lookPkHonourPanelBtn.setShow(m_show["lookPkHonourPanelBtn"]);
         m_lookPkHonourPanelBtn.setTipString("点击打开荣誉大厅");
      }
      
      private function setBtnLock(param1:ButtonLogicShell2) : void
      {
         param1.lock();
         if(m_remBtn)
         {
            m_remBtn.unLock();
         }
         m_remBtn = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_lookPkHonourPanelBtn);
         m_lookPkHonourPanelBtn = null;
         ClearUtil.clearObject(m_btnTotal);
         m_btnTotal = null;
         ClearUtil.clearObject(m_btnMonth);
         m_btnMonth = null;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
      }
      
      override protected function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:GamingUI = null;
         super.clickButton(param1);
         switch(param1.button)
         {
            case m_lookPkHonourPanelBtn:
               _loc2_ = _gamingUI;
               _loc2_.closeRankList();
               _loc2_.openHonourPanel("pkHonourHall");
               break;
            case m_btnMonth:
               setBtnLock(m_btnMonth);
               quit();
               PlayerDataForPK.getInstance().clearRankListTsData();
               m_pageBtnGroup.initPageNumber(1,8);
               _rankId = GameData.MonthCicly ? 1576 : 1583;
               initRankListData(PlayerDataForPK.getInstance().winMonthMatch);
               m_myRankText.text = String(m_curRank);
               m_myScoreText.text = String(PlayerDataForPK.getInstance().winMonthMatch);
               break;
            case m_btnTotal:
               setBtnLock(m_btnTotal);
               quit();
               PlayerDataForPK.getInstance().clearRankListTsData();
               m_pageBtnGroup.initPageNumber(1,8);
               _rankId = 139;
               initRankListData(PlayerDataForPK.getInstance().winMatch);
               m_myRankText.text = String(m_curRank);
               m_myScoreText.text = String(PlayerDataForPK.getInstance().winMatch);
         }
      }
      
      private function lookPkHonourPanel(param1:UIBtnEvent) : void
      {
      }
   }
}

