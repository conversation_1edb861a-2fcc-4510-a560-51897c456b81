package YJFY.Skill.PetSkills
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2_PiKaQiuSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfColorTransforms;
   import YJFY.World.World;
   
   public class Skill_Pet1Skill extends CuboidAreaAttackSkill2_PiKaQiuSkill
   {
      
      private var m_flashViewDataOfColorTransforms:DataOfColorTransforms;
      
      public function Skill_Pet1Skill()
      {
         super();
         m_flashViewDataOfColorTransforms = new DataOfColorTransforms();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_flashViewDataOfColorTransforms);
         m_flashViewDataOfColorTransforms = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc2_:String = String(param1.flashView[0].@swfPath);
         var _loc3_:String = String(param1.flashView[0].@className);
         m_myLoader.getClass(_loc2_,_loc3_,getFlashViewMovieClipSuccess,getFlashViewMovieClipFailFun);
         m_myLoader.load();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function entityChangeState(param1:AnimalEntity) : void
      {
         if(param1.isInHurt() == false)
         {
            param1.showBodyShow();
         }
         super.entityChangeState(param1);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && m_attackData.getHurtDuration())
         {
            if(param1 is AnimalEntity)
            {
               (param1 as AnimalEntity).hideBodyShow();
            }
         }
      }
      
      override protected function attackReach(param1:World) : void
      {
         super.attackReach(param1);
         m_world.flashView(m_flashViewDataOfColorTransforms);
      }
      
      private function getFlashViewMovieClipSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_flashViewDataOfColorTransforms.initByMovieClip(new _loc2_());
      }
      
      private function getFlashViewMovieClipFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

