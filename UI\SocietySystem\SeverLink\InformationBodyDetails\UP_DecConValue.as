package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import flash.utils.ByteArray;
   
   public class UP_DecConValue extends InformationBodyDetail
   {
      
      private var m_uid:Number;
      
      private var m_idx:int;
      
      private var m_decConValue:int;
      
      public function UP_DecConValue()
      {
         super();
         m_informationBodyId = 3029;
      }
      
      public function initData(param1:Number, param2:int, param3:int) : void
      {
         if(param3 <= 0)
         {
            throw new Error("减少贡献值不能小于等于0");
         }
         m_uid = param1;
         m_idx = param2;
         m_decConValue = param3;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         new InformationBodyDetailUtil().writeUidAndIdxToByteArr(_loc1_,m_uid,m_idx);
         _loc1_.writeInt(m_decConValue);
         return _loc1_;
      }
      
      public function getDecConValue() : int
      {
         return m_decConValue;
      }
   }
}

