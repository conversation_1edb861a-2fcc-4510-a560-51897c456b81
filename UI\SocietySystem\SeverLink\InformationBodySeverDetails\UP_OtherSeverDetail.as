package UI.SocietySystem.SeverLink.InformationBodySeverDetails
{
   import flash.utils.ByteArray;
   
   public class UP_OtherSeverDetail extends UP_SeverDetail
   {
      
      private var m_session:int;
      
      public function UP_OtherSeverDetail()
      {
         super();
      }
      
      public function setSession(param1:int) : void
      {
         m_session = param1;
      }
      
      override public function getThisByteArray() : ByteArray
      {
         var _loc1_:ByteArray = new ByteArray();
         _loc1_.endian = "littleEndian";
         _loc1_.writeInt(m_checkValue);
         _loc1_.writeInt(m_session);
         _loc1_.position = 0;
         return _loc1_;
      }
   }
}

