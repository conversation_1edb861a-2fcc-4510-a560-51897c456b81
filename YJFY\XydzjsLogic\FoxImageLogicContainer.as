package YJFY.XydzjsLogic
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.GameEntityListener;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Fox;
   import YJFY.GameEntity.XydzjsPlayerAndPet.IPlayerImageOwner;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerImage;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ISkill;
   import YJFY.Skill.Skill;
   import YJFY.Skill.SkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.World.Coordinate;
   import flash.geom.Point;
   
   public class FoxImageLogicContainer
   {
      
      private const m_const_stopMoveFrameLabel:String = "stopMove";
      
      private const m_const_imageEffectShowEndFrameLabel:String = "end^stop^";
      
      private const m_const_imageMoveAnimationDefId:String = "foxSkill3MoveBodyOfImage";
      
      private const m_const_imageEffectShowDefId:String = "foxSkill3Effect2";
      
      private const m_const_endChangeFrameLabel:String = "endChange";
      
      private const m_const_existInterval:uint = 5000;
      
      private const m_const_disappearInteval:uint = 200;
      
      private var m_foxImageEntity:PlayerImage;
      
      private var m_foxImageGameEntityListener:GameEntityListener;
      
      private var m_foxImageSkillListener:SkillListener;
      
      private var m_foxImageAnimationPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_ownerAnimalEntityListener:AnimalEntityListener;
      
      private var m_imageDirectionX:int;
      
      private var m_originalCoordOfOwner:Coordinate;
      
      private var m_isMoveImage:Boolean;
      
      private var m_moveDataOfPointsOfImage:DataOfPoints;
      
      private var m_imageEffectShowAnimationPlay:AnimationShowPlayLogicShell;
      
      private var m_imageEffectShowAnimationPlayListener:AnimationPlayFrameLabelListener;
      
      private var m_isAbleRunSkill:Boolean;
      
      private var m_isInRunSkill:Boolean;
      
      private var m_endExistTime:uint;
      
      private var m_isDisappear:Boolean;
      
      private var m_disappearTime:uint;
      
      private var m_foxImageOwner:IPlayerImageOwner;
      
      private var m_fox:Fox;
      
      public function FoxImageLogicContainer()
      {
         super();
         m_ownerAnimalEntityListener = new AnimalEntityListener();
         m_ownerAnimalEntityListener.changeStateFun = ownerChangeState;
         m_foxImageGameEntityListener = new GameEntityListener();
         m_foxImageGameEntityListener.attackSuccessFun = imageAttackSuccess;
         m_foxImageGameEntityListener.preJudgeIsFoeFun = imagePreJudgeIsFoe;
         m_foxImageAnimationPlayListener = new AnimationPlayFrameLabelListener();
         m_foxImageAnimationPlayListener.reachFrameLabelFun2 = reachFrameLabel2;
         m_foxImageSkillListener = new SkillListener();
         m_foxImageSkillListener.endSkillFun = foxImageEndSkill;
         m_moveDataOfPointsOfImage = new DataOfPoints();
         m_originalCoordOfOwner = new Coordinate();
         m_imageEffectShowAnimationPlay = new AnimationShowPlayLogicShell();
         m_imageEffectShowAnimationPlayListener = new AnimationPlayFrameLabelListener();
         m_imageEffectShowAnimationPlayListener.reachFrameLabelFun2 = reachFrameLabel2;
         m_imageEffectShowAnimationPlay.addFrameLabelListener(m_imageEffectShowAnimationPlayListener);
      }
      
      public function clear() : void
      {
         if(m_foxImageOwner)
         {
            m_foxImageOwner.getAnimalEntity_owner().removeAnimalEntityListener(m_ownerAnimalEntityListener);
         }
         ClearUtil.clearObject(m_foxImageEntity);
         m_foxImageEntity = null;
         ClearUtil.clearObject(m_foxImageGameEntityListener);
         m_foxImageGameEntityListener = null;
         ClearUtil.clearObject(m_foxImageSkillListener);
         m_foxImageSkillListener = null;
         ClearUtil.clearObject(m_foxImageAnimationPlayListener);
         m_foxImageAnimationPlayListener = null;
         ClearUtil.clearObject(m_ownerAnimalEntityListener);
         m_ownerAnimalEntityListener = null;
         ClearUtil.clearObject(m_originalCoordOfOwner);
         m_originalCoordOfOwner = null;
         ClearUtil.clearObject(m_moveDataOfPointsOfImage);
         m_moveDataOfPointsOfImage = null;
         ClearUtil.clearObject(m_imageEffectShowAnimationPlay);
         m_imageEffectShowAnimationPlay = null;
         ClearUtil.clearObject(m_imageEffectShowAnimationPlayListener);
         m_imageEffectShowAnimationPlayListener = null;
         m_foxImageOwner = null;
         m_fox = null;
      }
      
      public function init(param1:IPlayerImageOwner) : void
      {
         if(m_foxImageOwner)
         {
            m_foxImageOwner.getAnimalEntity_owner().removeAnimalEntityListener(m_ownerAnimalEntityListener);
         }
         m_foxImageOwner = param1;
         if(m_foxImageOwner)
         {
            m_foxImageOwner.getAnimalEntity_owner().addAnimalEntityListener(m_ownerAnimalEntityListener);
         }
         m_fox = param1 as Fox;
         m_foxImageEntity = new PlayerImage();
         m_foxImageEntity.setIsInvincible(true);
         m_foxImageEntity.setMyLoader(m_foxImageOwner.getMyLoader_owner());
         m_foxImageEntity.setEnterFrameTime(m_foxImageOwner.getEnterFrameTime_owner());
         m_foxImageEntity.setAnimationDefinitionManager(m_foxImageOwner.getAnimationDefinitionManager_owner());
         m_foxImageEntity.setSoundMananger(m_foxImageOwner.getSoundManager_owner());
         m_foxImageEntity.initByXML(m_foxImageOwner.getXML_owner());
         m_foxImageEntity.addGameEntityListener(m_foxImageGameEntityListener);
         (m_foxImageEntity.getAnimalEntity() as AnimalEntity).addAnimationFrameLabelListener(m_foxImageAnimationPlayListener);
         m_foxImageEntity.setUiPlayer(m_foxImageOwner.getUIPlayer_owner());
         m_foxImageEntity.setOwner(m_foxImageOwner.getAnimalEntity_owner());
         m_foxImageEntity.setOwnerPlayer(m_foxImageOwner.getPlayer_owner());
         var _loc4_:XML = m_foxImageOwner.getXML_owner();
         var _loc2_:String = String(_loc4_.imageMoveData[0].@swfPath);
         var _loc3_:String = String(_loc4_.imageMoveData[0].@className);
         m_foxImageOwner.getMyLoader_owner().getClass(_loc2_,_loc3_,getMoveDataOfPointsShowSuccess,getMoveDataOfPointsShowFail);
         m_foxImageOwner.getMyLoader_owner().load();
      }
      
      public function render() : void
      {
         m_foxImageEntity.render();
         renderMoveImage();
         if(m_isDisappear)
         {
            if(m_foxImageEntity.getAnimalEntity().getWorld().getWorldTime() > m_disappearTime)
            {
               m_fox.clearImage(this);
            }
            else
            {
               m_foxImageEntity.getAnimalEntity().getShow().alpha = (m_disappearTime - m_foxImageEntity.getAnimalEntity().getWorld().getWorldTime()) / 200;
            }
         }
         else if(m_isInRunSkill == false)
         {
            if(isNaN(m_endExistTime) == false && m_foxImageEntity.getAnimalEntity().getWorld())
            {
               if(m_endExistTime < m_foxImageEntity.getAnimalEntity().getWorld().getWorldTime())
               {
                  disappearImage();
               }
            }
         }
      }
      
      public function reInit() : void
      {
         var _loc1_:AnimalEntity = m_foxImageEntity.getAnimalEntity() as AnimalEntity;
         var _loc2_:AnimalEntity = m_foxImageOwner.getAnimalEntity_owner() as AnimalEntity;
         m_isMoveImage = true;
         m_isInRunSkill = false;
         cancelDisappear();
         resetEndExistTime();
         m_originalCoordOfOwner.setTo(_loc2_.getX(),_loc2_.getY(),_loc2_.getZ());
         m_imageDirectionX = _loc2_.getShowDirection();
         m_foxImageEntity.setUiPlayer(m_foxImageOwner.getUIPlayer_owner());
      }
      
      public function moveImage() : void
      {
         var _loc1_:AnimalEntity = m_foxImageEntity.getAnimalEntity() as AnimalEntity;
         var _loc2_:AnimalEntity = m_foxImageOwner.getAnimalEntity_owner() as AnimalEntity;
         _loc1_.setDirection(_loc2_.getShowDirection(),0);
         _loc1_.changeAnimationShow("foxSkill3MoveBodyOfImage");
         _loc1_.currentAnimationGotoAndPlay("1");
         m_imageEffectShowAnimationPlay.setShow(_loc2_.getAnimationByDefId("foxSkill3Effect2"),true);
         _loc1_.addOtherAniamtion(m_imageEffectShowAnimationPlay,true);
         m_imageEffectShowAnimationPlay.gotoAndPlay("1");
         m_imageEffectShowAnimationPlay.getDisplayShow().scaleX = _loc1_.getShowDirection();
      }
      
      private function imageAttackSuccess(param1:GameEntity, param2:IEntity, param3:ISkill, param4:IEntity) : void
      {
         m_foxImageOwner.attackSuccess_owner(param2,param3,param4);
      }
      
      private function imagePreJudgeIsFoe(param1:GameEntity, param2:IEntity) : void
      {
         m_foxImageEntity.setIsFoe(m_foxImageOwner.isFoe(param2));
      }
      
      private function ownerChangeState(param1:IAnimalEntity) : void
      {
         var _loc2_:Skill = null;
         if(param1 == m_foxImageOwner.getAnimalEntity_owner() && param1.isInSkill() && m_isAbleRunSkill && Boolean(m_foxImageEntity.getAnimalEntity().getWorld()) && param1.getSkillInRunByIndex(0).getId() != m_fox.getFoxSkill4Id() && param1.getSkillInRunByIndex(0).getId() != m_fox.getFoxSkill5Id())
         {
            _loc2_ = m_foxImageEntity.getAnimalEntity().getSkillById(param1.getSkillInRunByIndex(0).getId());
            _loc2_.addSkillListener(m_foxImageSkillListener);
            m_foxImageEntity.runSkill(_loc2_);
            m_isAbleRunSkill = false;
            m_isInRunSkill = true;
         }
      }
      
      private function foxImageEndSkill(param1:Skill) : void
      {
         param1.removeSkillListener(m_foxImageSkillListener);
         m_isInRunSkill = false;
         disappearImage();
      }
      
      private function disappearImage() : void
      {
         m_isDisappear = true;
         m_isAbleRunSkill = false;
         m_disappearTime = m_foxImageEntity.getAnimalEntity().getWorld().getWorldTime() + 200;
      }
      
      private function reachFrameLabel2(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
         switch(param2)
         {
            case "stopMove":
               m_isMoveImage = false;
               m_isAbleRunSkill = true;
               m_foxImageEntity.getAnimalEntity().setDirection(0,0);
               m_foxImageEntity.getAnimalEntity().removeOtherAnimation(m_imageEffectShowAnimationPlay);
               break;
            case "end^stop^":
               m_foxImageEntity.getAnimalEntity().removeOtherAnimation(m_imageEffectShowAnimationPlay);
               break;
            case "endChange":
               m_foxImageEntity.getAnimalEntity().walk();
               m_foxImageEntity.getAnimalEntity().idle();
         }
      }
      
      private function renderMoveImage() : void
      {
         var _loc2_:AnimalEntity = null;
         var _loc1_:Point = null;
         if(m_isMoveImage)
         {
            _loc2_ = m_foxImageEntity.getAnimalEntity() as AnimalEntity;
            _loc2_.setMoveSpeed(0);
            if(_loc2_.getAnimationShow("foxSkill3MoveBodyOfImage").currentFrame < m_moveDataOfPointsOfImage.getPointNum())
            {
               _loc1_ = m_moveDataOfPointsOfImage.getPointByIndex(_loc2_.getAnimationShow("foxSkill3MoveBodyOfImage").currentFrame);
               _loc2_.setNewPosition(m_originalCoordOfOwner.getX() + _loc2_.getShowDirection() * _loc1_.x,m_originalCoordOfOwner.getY() + _loc1_.y,m_originalCoordOfOwner.getZ());
            }
         }
      }
      
      public function getFoxImage() : PlayerImage
      {
         return m_foxImageEntity;
      }
      
      public function getAnimalEntityOfFoxImage() : IAnimalEntity
      {
         return m_foxImageEntity.getAnimalEntity();
      }
      
      private function resetEndExistTime() : void
      {
         m_endExistTime = m_foxImageOwner.getAnimalEntity_owner().getWorld().getWorldTime() + 5000;
      }
      
      private function cancelDisappear() : void
      {
         m_isDisappear = false;
         m_foxImageEntity.getAnimalEntity().getShow().alpha = 1;
      }
      
      private function getMoveDataOfPointsShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_moveDataOfPointsOfImage.initByMovieClip(new _loc2_());
      }
      
      private function getMoveDataOfPointsShowFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
   }
}

