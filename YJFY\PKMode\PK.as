package YJFY.PKMode
{
   import UI.EnterFrameTime;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.PKUI.PKPanelOne;
   import UI.PKUI.PlayerDataForPK;
   import UI.Players.Player;
   import UI.WarningBox.WarningBoxSingle;
   import UI.XMLSingle;
   import UI2.NewRank.RankDataInfo;
   import UI2.ProgramStartData.ProgramStartData;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.SubmitData;
   import YJFY.API_4399.RankListAPI.SubmitReturnData;
   import YJFY.API_4399.RankListAPI.SubmitSuccessReturnData;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.LoadUI2;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.PKMode.PKData.MyPKSaveData;
   import YJFY.PKMode.PKData.PKSaveDataOne;
   import YJFY.PKMode.PKData.PKTargetPlayerData;
   import YJFY.PKMode.PKLogic.PKWorld;
   import YJFY.PKMode.PKLogic.PKWorldForOneKey;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.VersionControl.IVersionControl;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class PK implements IPK_test
   {
      
      public static const s_const_OnePK:String = "onePK";
      
      public static const s_const_TwoPK:String = "twoPK";
      
      private const m_const_onePKRankListId:uint = 139;
      
      private const m_const_twoPKRankListId:uint = 1580;
      
      private const m_const_MaxDataNumInRankList:uint = 10000;
      
      private const m_const_getRankInforNum:uint = 100;
      
      private const m_const_pkPlayerNum:uint = 8;
      
      private var m_show:Sprite;
      
      private var m_pkPanel:PKPanel;
      
      private var m_myPlayerDatas:PlayerDatas;
      
      private var m_foePalyerDatas:PlayerDatas;
      
      private var m_otherPlayerDatas:PlayerDatas;
      
      private var m_otherPlayerData:InitPlayersData;
      
      private var m_myUiPlayerData:InitPlayersData;
      
      private var m_myUiPlayer1:Player;
      
      private var m_myUiPlayer2:Player;
      
      private var m_foeUiPlayerData:InitPlayersData;
      
      private var m_foeUiPlayer1:Player;
      
      private var m_foeUiPlayer2:Player;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_localRankListAPI:LocalPKRankListAPI;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_loadIsEx:Boolean;
      
      private var m_myDataInRankList:UserDataInRankList;
      
      private var m_myMonthDataInRankList:UserDataInRankList;
      
      private var m_rankListId:uint;
      
      private var m_panelShowClassName:String;
      
      private var m_pkType:String;
      
      private var m_pkIndex:uint;
      
      private var m_bClear:Boolean = false;
      
      private var m_pkMap:PKWorld;
      
      private var m_pkOneKeyMap:PKWorldForOneKey;
      
      private var m_otherPlayerShowPanel:PKPanelOne;
      
      private var m_players:Vector.<Player>;
      
      private var m_playerNickNameObj:Object;
      
      private var m_isSigned:Boolean;
      
      private var m_getNeedRank:uint;
      
      private var m_versionControl:IVersionControl;
      
      private var m_enterFrameTime:EnterFrameTime;
      
      private var m_loadUI:IProgressShow;
      
      private var m_pkSaveDataOne:PKSaveDataOne;
      
      private var m_pkTargetPlayerData:PKTargetPlayerData;
      
      private var m_userDatas:Vector.<UserDataInRankList>;
      
      public var m_OneKeyIndex:Array = [];
      
      public var m_OneKeyPK:Boolean;
      
      public function PK()
      {
         super();
         GameEvent.eventDispacher.addEventListener("fail",getDataFail);
         m_show = new MySprite();
         m_show.addEventListener("warningBox",sureOrCancel,true,0,true);
         m_show.addEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,true,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         m_show.addEventListener("showMessageBox",showMessageBox,false,0,true);
         m_show.addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         m_myPlayerDatas = new PlayerDatas();
         m_foePalyerDatas = new PlayerDatas();
         m_getPlayerDataListener = new GetPlayerDataListener();
         m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData2;
         m_myPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_foePalyerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_myPlayerDatas.init();
         m_foePalyerDatas.init();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.rankListErrorFun = rankListError1;
         m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerRankInfoSuccess;
         m_localRankListAPI = new LocalPKRankListAPI();
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(m_localRankListAPI);
         m_myLoader = new YJFYLoader();
         m_myLoader.setVersionControl(GamingUI.getInstance().getVersionControl());
      }
      
      public function clear() : void
      {
         m_bClear = true;
         if(m_show)
         {
            m_show.removeEventListener("warningBox",sureOrCancel,true);
            m_show.removeEventListener("clickQuitBtn",closeOtherPlayerShowPanel,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,true);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,true);
            m_show.removeEventListener("showMessageBox",showMessageBox,false);
            m_show.removeEventListener("hideMessageBox",hideMessageBox,false);
         }
         GameEvent.eventDispacher.removeEventListener("fail",getDataFail);
         Part1.getInstance().getApi4399().rankListAPI.setLocalRankListAPI(null);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_pkPanel);
         m_pkPanel = null;
         ClearUtil.clearObject(m_myPlayerDatas);
         m_myPlayerDatas = null;
         ClearUtil.clearObject(m_foePalyerDatas);
         m_foePalyerDatas = null;
         ClearUtil.clearObject(m_otherPlayerDatas);
         m_otherPlayerDatas = null;
         ClearUtil.clearObject(m_otherPlayerData);
         m_otherPlayerData = null;
         ClearUtil.clearObject(m_myUiPlayerData);
         m_myUiPlayerData = null;
         ClearUtil.clearObject(m_myUiPlayer1);
         m_myUiPlayer1 = null;
         ClearUtil.clearObject(m_myUiPlayer2);
         m_myUiPlayer2 = null;
         ClearUtil.clearObject(m_foeUiPlayerData);
         m_foeUiPlayerData = null;
         ClearUtil.clearObject(m_foeUiPlayer1);
         m_foeUiPlayer1 = null;
         ClearUtil.clearObject(m_foeUiPlayer2);
         m_foeUiPlayer2 = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         ClearUtil.clearObject(m_localRankListAPI);
         m_localRankListAPI = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_myDataInRankList);
         m_myDataInRankList = null;
         if(m_myMonthDataInRankList)
         {
            ClearUtil.clearObject(m_myMonthDataInRankList);
         }
         m_myMonthDataInRankList = null;
         m_panelShowClassName = null;
         m_pkType = null;
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
         ClearUtil.clearObject(m_players);
         m_players = null;
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = null;
         m_versionControl = null;
         m_enterFrameTime = null;
         m_loadUI = null;
         m_pkSaveDataOne = null;
         m_pkTargetPlayerData = null;
      }
      
      public function setVersionControl(param1:IVersionControl) : void
      {
         m_versionControl = param1;
      }
      
      public function setEnterFrameTime(param1:EnterFrameTime) : void
      {
         m_enterFrameTime = param1;
      }
      
      public function setLoadUI(param1:IProgressShow) : void
      {
         m_loadUI = param1;
      }
      
      public function setMyLoader(param1:YJFYLoader) : void
      {
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function startPK(param1:int) : void
      {
         m_pkIndex = param1;
         getOnePKTargetPlayerRankData(0);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_pkMap)
         {
            m_pkMap.render(m_enterFrameTime);
            m_pkMap.showRender();
         }
         if(m_pkOneKeyMap)
         {
            m_pkOneKeyMap.render();
         }
      }
      
      public function openPKWorld() : void
      {
         if(m_pkMap)
         {
            return;
         }
         Part1.getInstance().continueGame();
         RankDataInfo.getInstance().setFightType(2);
         m_pkMap = new PKWorld();
         m_pkMap.setPk(this);
         m_pkMap.setIsTwoMode(true);
         if(m_show.contains(m_pkPanel))
         {
            m_show.removeChild(m_pkPanel);
         }
         m_show.addChild(m_pkMap);
         m_pkMap.setVersionControl(m_versionControl);
         m_pkMap.setLoadUI(m_loadUI as LoadUI2);
         m_pkMap.setMyLoader(m_myLoader);
         (m_loadUI as LoadUI2).tranToOpacity();
         m_pkMap.setEnterFrameTime(m_enterFrameTime);
         m_pkMap.init();
         if(m_pkType == "onePK")
         {
            m_pkMap.initUiPlayerData(m_myUiPlayer1,null,m_foeUiPlayer1,null);
         }
         else
         {
            m_pkMap.initUiPlayerData(m_myUiPlayer1,m_myUiPlayer2,m_foeUiPlayer1,m_foeUiPlayer2);
         }
         m_pkMap.initByXMLPath("NewGameFolder/PKMap.xml");
         m_show.stage.focus = Part1.getInstance();
         Part1.getInstance().setTipXY(250,150);
         GamingUI.getInstance().addMainLineTaskGoalGameEventStr("pk");
      }
      
      public function openOneKeyPKWorld() : void
      {
         m_pkOneKeyMap = new PKWorldForOneKey();
         m_pkOneKeyMap.setPk(this);
         m_pkOneKeyMap.setIsTwoMode(true);
         m_pkOneKeyMap.setLoad(m_myLoader);
         Part1.getInstance().continueGame();
         (m_loadUI as LoadUI2).tranToTransparentcy();
         if(m_pkType == "onePK")
         {
            m_pkOneKeyMap.initUiPlayerData(m_myUiPlayer1,null,m_foeUiPlayer1,null);
         }
         else
         {
            m_pkOneKeyMap.initUiPlayerData(m_myUiPlayer1,m_myUiPlayer2,m_foeUiPlayer1,m_foeUiPlayer2);
         }
         GamingUI.getInstance().addMainLineTaskGoalGameEventStr("pk");
      }
      
      public function reFreshPanel() : void
      {
         clearPKDataForNextPK();
         m_pkOneKeyMap.clear();
         m_pkOneKeyMap = null;
         m_pkPanel.refreshShow();
         m_OneKeyIndex.shift();
         if(m_OneKeyIndex.length > 0)
         {
            GamingUI.getInstance().mouseChildren = false;
            startPK(m_OneKeyIndex[0]);
         }
         else
         {
            showOneKeyWait(false);
         }
      }
      
      public function closePKWorld() : void
      {
         clearPKDataForNextPK();
         ClearUtil.clearObject(m_pkMap);
         m_pkMap = null;
         m_pkPanel.refreshShow();
         m_show.addChild(m_pkPanel);
         Part1.getInstance().setTipXY(250,10);
      }
      
      public function addPKDataAndSubmitToRankList(param1:Boolean) : void
      {
         var _loc3_:* = null;
         var _loc2_:SaveTaskInfo = null;
         _loc2_ = null;
         if(param1)
         {
            m_pkTargetPlayerData.setPKStateToWin();
         }
         else
         {
            m_pkTargetPlayerData.setPKStateToFail();
         }
         if(param1)
         {
            PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint + ProgramStartData.getInstance().getWinAddPKPoint();
            if(PlayerDataForPK.getInstance().pkPoint >= XMLSingle.getInstance().maxPKPoint)
            {
               showWarningBox("您的PK点已达最大上限",0);
            }
            if(m_pkType == "onePK")
            {
               if(m_pkSaveDataOne.getresetType() != 3)
               {
                  PlayerDataForPK.getInstance().allMatch++;
                  PlayerDataForPK.getInstance().winMatch++;
                  PlayerDataForPK.getInstance().winMonthMatch++;
                  GameEvent.eventDispacher.dispatchEvent(new GameEvent("rundetector"));
               }
            }
            else
            {
               if(m_pkType != "twoPK")
               {
                  throw new Error("出错了111");
               }
               if(m_pkSaveDataOne.getresetType() != 3)
               {
                  PlayerDataForPK.getInstance().allMatchForTwoPlayer++;
                  PlayerDataForPK.getInstance().winMatchForTwoPlayer++;
               }
            }
         }
         else
         {
            PlayerDataForPK.getInstance().pkPoint = PlayerDataForPK.getInstance().pkPoint + ProgramStartData.getInstance().getFailAddPKPoint();
            if(PlayerDataForPK.getInstance().pkPoint >= XMLSingle.getInstance().maxPKPoint)
            {
               showWarningBox("您的PK点已达最大上限",0);
            }
            if(m_pkType == "onePK")
            {
               PlayerDataForPK.getInstance().allMatch++;
               PlayerDataForPK.getInstance().failMatch++;
               GameEvent.eventDispacher.dispatchEvent(new GameEvent("rundetector"));
            }
            else
            {
               if(m_pkType != "twoPK")
               {
                  throw new Error("出错了222");
               }
               PlayerDataForPK.getInstance().allMatchForTwoPlayer++;
               PlayerDataForPK.getInstance().failMatchForTwoPlayer++;
            }
         }
         if(m_OneKeyPK)
         {
            if(m_OneKeyIndex.length == 1)
            {
               subMitScore(_loc3_,param1);
               _loc2_ = new SaveTaskInfo();
               _loc2_.type = "4399";
               _loc2_.isHaveData = false;
               SaveTaskList.getInstance().addData(_loc2_);
               MyFunction2.saveGame();
            }
         }
         else
         {
            subMitScore(_loc3_,param1);
            _loc2_ = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame();
         }
         if(m_pkOneKeyMap)
         {
            reFreshPanel();
         }
      }
      
      private function subMitScore(param1:SubmitData, param2:Boolean) : void
      {
         var submitData:SubmitData = param1;
         var isMyWin:Boolean = param2;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc2_:SubmitData = null;
            var _loc4_:int = 0;
            var _loc3_:* = undefined;
            if(m_bClear)
            {
               return;
            }
            Part1.getInstance().hideGameWaitShow();
            var _loc5_:Date = new TimeUtil().stringToDate(param1);
            if(m_pkType == "onePK")
            {
               submitData = new SubmitData(m_rankListId,PlayerDataForPK.getInstance().winMatch);
               if(isMyWin)
               {
                  _loc4_ = (31 - _loc5_.getDate()) * 24 * 60 + (24 - _loc5_.getHours()) * 60 + (60 - _loc5_.getMinutes()) + PlayerDataForPK.getInstance().winMonthMatch * 100000;
                  _loc2_ = new SubmitData(GameData.MonthCicly ? 1576 : 1583,_loc4_);
               }
            }
            else
            {
               if(m_pkType != "twoPK")
               {
                  throw new Error("出错了333");
               }
               _loc4_ = (31 - _loc5_.getDate()) * 24 * 60 + (24 - _loc5_.getHours()) * 60 + (60 - _loc5_.getMinutes()) + PlayerDataForPK.getInstance().winMatchForTwoPlayer * 100000;
               submitData = new SubmitData(1580,_loc4_);
               if(isMyWin)
               {
                  if(PlayerDataForPK.getInstance().winMatchForTwoPlayer > PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer)
                  {
                     PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer = PlayerDataForPK.getInstance().winMatchForTwoPlayer;
                     _loc4_ = (31 - _loc5_.getDate()) * 24 * 60 + (24 - _loc5_.getHours()) * 60 + (60 - _loc5_.getMinutes()) + PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer * 100000;
                     _loc2_ = new SubmitData(1575,_loc4_);
                  }
               }
            }
            if(submitData)
            {
               m_rankListAPIListener.submitScoreInfoReturnFun = submitScoreInfoReturnFun;
               m_rankListAPIListener.submitScoreInfoErrorFun = submitScoreInfoErrorFun;
               GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
               _loc3_ = new Vector.<SubmitData>();
               _loc3_.push(submitData);
               if(_loc2_)
               {
                  _loc3_.push(_loc2_);
               }
               GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(GameData.getInstance().getSaveFileData().index,_loc3_);
            }
         },function():void
         {
            GamingUI.getInstance().showMessageTip("提交排行榜失败");
            Part1.getInstance().hideGameWaitShow();
         },true);
      }
      
      private function getOnePKTargetPlayerRankData(param1:uint) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(!m_OneKeyPK)
         {
            Part1.getInstance().showGameWaitShow();
         }
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getRankListsDataFun = getRankInfos;
         if(!param1)
         {
            _loc2_ = m_myDataInRankList ? m_myDataInRankList.getRank() : 10000;
            _loc3_ = Math.max(1,_loc2_ - 100);
            param1 = _loc3_ + Math.random() * 100;
         }
         Part1.getInstance().getApi4399().rankListAPI.getRankListsData(m_rankListId,1,param1);
         m_getNeedRank = param1;
      }
      
      public function showOneKeyWait(param1:Boolean) : void
      {
         m_OneKeyPK = param1;
         GamingUI.getInstance().mouseChildren = !param1;
         GamingUI.getInstance().mouseEnabled = !param1;
         GamingUI.getInstance().manBan.text.text = "PK中，请稍等...";
         GamingUI.getInstance().manBan.wheel.visible = param1;
      }
      
      public function init(param1:String) : void
      {
         m_pkType = param1;
         switch(param1)
         {
            case "onePK":
               m_pkSaveDataOne = MyPKSaveData.getInstance().getOnePKSaveData();
               m_rankListId = 139;
               m_panelShowClassName = "PkChooseUI";
               break;
            case "twoPK":
               m_pkSaveDataOne = MyPKSaveData.getInstance().getTwoPKSaveData();
               m_rankListId = 1580;
               m_panelShowClassName = "FreePkChooseUI";
               break;
            default:
               throw new Error();
         }
         getMyInfoInRankList();
      }
      
      private function getMyInfoInRankList() : void
      {
         Part1.getInstance().showGameWaitShow();
         m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerRankInfoSuccess;
         Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         Part1.getInstance().getApi4399().rankListAPI.getOneRankInfo(m_rankListId,GameData.getInstance().getLoginReturnData().getName());
      }
      
      private function init2() : void
      {
         Part1.getInstance().showGameWaitShow();
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            if(isNewDay(param1))
            {
               newDayResetPkSaveData(param1);
            }
            Part1.getInstance().hideGameWaitShow();
            init3();
         },function():void
         {
            GamingUI.getInstance().showMessageTip("加载失败");
            Part1.getInstance().hideGameWaitShow();
            Part1.getInstance().closePK();
         },true);
      }
      
      private function init3() : void
      {
         m_rankListAPIListener.rankListErrorFun = rankListError2;
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.getClass("NewGameFolder/PKMode/PkSource.swf",m_panelShowClassName,getPanelShowSuccess,getShowFail);
         m_myLoader.load();
      }
      
      private function getPanelShowSuccess(param1:YJFYLoaderData) : void
      {
         ClearUtil.clearObject(m_pkPanel);
         var _loc2_:Class = param1.resultClass;
         m_pkPanel = new PKPanel();
         m_pkPanel.init(new _loc2_(),m_pkType,this,m_myDataInRankList ? m_myDataInRankList.getRank() : 0,m_pkSaveDataOne,m_myMonthDataInRankList);
         m_show.addChild(m_pkPanel);
      }
      
      private function getShowFail(param1:YJFYLoaderData) : void
      {
         GamingUI.getInstance().showMessageTip("加载失败");
         Part1.getInstance().closePK();
      }
      
      private function newDayResetPkSaveData(param1:String) : void
      {
         m_pkSaveDataOne.newDataResetData(param1);
      }
      
      public function lookUpMyPlayerInfor() : void
      {
         openPlayerShowPanel(m_myUiPlayer1,m_myUiPlayer2,m_myUiPlayerData.nickNameData);
      }
      
      public function lookUpFoePlayerInfor() : void
      {
         openPlayerShowPanel(m_foeUiPlayer1,m_foeUiPlayer2,m_foeUiPlayerData.nickNameData);
      }
      
      public function lookUpPlayerInfor(param1:String, param2:uint) : void
      {
         if(m_otherPlayerDatas == null)
         {
            m_otherPlayerDatas = new PlayerDatas();
            if(m_getPlayerDataListener == null)
            {
               m_getPlayerDataListener = new GetPlayerDataListener();
            }
            m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData2;
            m_otherPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
            m_otherPlayerDatas.init();
         }
         m_otherPlayerDatas.getPlayerData(param1,param2);
      }
      
      private function openPlayerShowPanel(param1:Player, param2:Player, param3:Object) : void
      {
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         ClearUtil.nullArr(m_players,false,false,false);
         ClearUtil.nullObject(m_playerNickNameObj);
         m_playerNickNameObj = param3;
         m_players = new Vector.<Player>();
         m_otherPlayerShowPanel = new PKPanelOne();
         m_otherPlayerShowPanel.isTwoMode = true;
         m_players.push(param1);
         m_players.push(param2);
         m_otherPlayerShowPanel.initPKPanel(m_players,m_playerNickNameObj);
         m_show.addChild(m_otherPlayerShowPanel);
      }
      
      private function closeOtherPlayerShowPanel(param1:UIBtnEvent) : void
      {
         if(Boolean(param1) && param1.target.parent != m_otherPlayerShowPanel)
         {
            return;
         }
         ClearUtil.clearObject(m_otherPlayerShowPanel);
         m_otherPlayerShowPanel = null;
      }
      
      private function isNewDay(param1:String) : Boolean
      {
         return new TimeUtil().newDateIsNewDay(m_pkSaveDataOne.getPkDate(),param1);
      }
      
      private function getPlayerData2(param1:InitPlayersData, param2:PlayerDatas) : void
      {
         if(param2 == m_otherPlayerDatas)
         {
            m_otherPlayerData = param1;
            m_otherPlayerDatas = param2;
            openPlayerShowPanel(param1.player1,param1.player2,param1.nickNameData);
            return;
         }
         if(param2 == m_myPlayerDatas)
         {
            m_myUiPlayerData = param1;
            m_myUiPlayer1 = param1.player1;
            m_myUiPlayer2 = param1.player2;
            if(start() == false)
            {
               getFoeSaveData();
            }
         }
         else if(param2 == m_foePalyerDatas)
         {
            m_foeUiPlayerData = param1;
            m_foeUiPlayer1 = param1.player1;
            m_foeUiPlayer2 = param1.player2;
            m_pkTargetPlayerData.updateData(param1.player1.playerVO.playerType,param1.nickNameData && param1.nickNameData.extra ? param1.nickNameData.extra : "");
            start();
         }
      }
      
      private function start() : Boolean
      {
         if(Boolean(m_myUiPlayer1) && Boolean(m_foeUiPlayer1))
         {
            if(!m_OneKeyPK)
            {
               Part1.getInstance().hideGameWaitShow();
            }
            if(!m_OneKeyPK)
            {
               openPKWorld();
            }
            else
            {
               openOneKeyPKWorld();
            }
            return true;
         }
         return false;
      }
      
      private function getPlayerMonthRankInfoSuccess(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc3_:int = 0;
         Part1.getInstance().hideGameWaitShow();
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getOneRankInfoSuccessFun = null;
         var _loc2_:int = param1 ? param1.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1[_loc3_].getUid() == GameData.getInstance().getLoginReturnData().getUid() && param1[_loc3_].getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               m_myMonthDataInRankList = param1[_loc3_];
               break;
            }
            _loc3_++;
         }
         initAll(m_userDatas);
      }
      
      private function getPlayerRankInfoSuccess(param1:Vector.<UserDataInRankList>) : void
      {
         m_rankListAPIListener.getOneRankInfoSuccessFun = null;
         if(m_pkType == "twoPK")
         {
            Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
            Part1.getInstance().hideGameWaitShow();
            initAll(param1);
         }
         else
         {
            m_rankListAPIListener.getOneRankInfoSuccessFun = getPlayerMonthRankInfoSuccess;
            Part1.getInstance().getApi4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
            Part1.getInstance().getApi4399().rankListAPI.getOneRankInfo(GameData.MonthCicly ? 1576 : 1583,GameData.getInstance().getLoginReturnData().getName());
         }
         m_userDatas = param1;
      }
      
      private function initAll(param1:Vector.<UserDataInRankList>) : void
      {
         var isHave:Boolean;
         var winMatch:int;
         var isSignUp:Boolean;
         var userDatas:Vector.<UserDataInRankList> = param1;
         var length:int = userDatas ? userDatas.length : 0;
         var i:int = 0;
         while(i < length)
         {
            if(userDatas[i].getUid() == GameData.getInstance().getLoginReturnData().getUid() && userDatas[i].getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               m_myDataInRankList = userDatas[i];
               isHave = true;
               break;
            }
            ++i;
         }
         isSignUp = false;
         if(m_pkType == "onePK")
         {
            winMatch = PlayerDataForPK.getInstance().winMatch;
            isSignUp = PlayerDataForPK.getInstance().isSignUpForSingle;
         }
         else if(m_pkType == "twoPK")
         {
            winMatch = PlayerDataForPK.getInstance().winMatchForTwoPlayer;
            isSignUp = PlayerDataForPK.getInstance().isSignUpForDouble;
         }
         if(isSignUp || m_isSigned == true || isHave || m_versionControl.getLineMode().getLineMode() == "offLine")
         {
            init2();
         }
         else
         {
            Part1.getInstance().showGameWaitShow();
            MyFunction2.getServerTimeFunction(function(param1:String):void
            {
               var _loc2_:Date = new TimeUtil().stringToDate(param1);
               Part1.getInstance().hideGameWaitShow();
               submitMyDataWhenNoHave(_loc2_);
            },function():void
            {
               GamingUI.getInstance().showMessageTip("获取服务器时间失败");
               Part1.getInstance().hideGameWaitShow();
               Part1.getInstance().closePK();
            },true);
         }
      }
      
      private function submitMyDataWhenNoHave(param1:Date) : void
      {
         var _loc3_:SubmitData = null;
         var _loc2_:SubmitData = null;
         var _loc5_:int = 0;
         if(m_pkType == "onePK")
         {
            _loc3_ = new SubmitData(m_rankListId,PlayerDataForPK.getInstance().winMatch);
            _loc5_ = (31 - param1.getDate()) * 24 * 60 + (24 - param1.getHours()) * 60 + (60 - param1.getMinutes()) + PlayerDataForPK.getInstance().winMonthMatch * 100000;
            _loc2_ = new SubmitData(GameData.MonthCicly ? 1576 : 1583,_loc5_);
         }
         else
         {
            if(m_pkType != "twoPK")
            {
               throw new Error("出错了444");
            }
            _loc5_ = (31 - param1.getDate()) * 24 * 60 + (24 - param1.getHours()) * 60 + (60 - param1.getMinutes()) + PlayerDataForPK.getInstance().winMatchForTwoPlayer * 100000;
            _loc3_ = new SubmitData(1580,_loc5_);
            if(PlayerDataForPK.getInstance().winMatchForTwoPlayer > PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer)
            {
               PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer = PlayerDataForPK.getInstance().winMatchForTwoPlayer;
               _loc5_ = (31 - param1.getDate()) * 24 * 60 + (24 - param1.getHours()) * 60 + (60 - param1.getMinutes()) + PlayerDataForPK.getInstance().mingrenMatchForTwoPlayer * 100000;
               _loc2_ = new SubmitData(1575,_loc5_);
            }
         }
         Part1.getInstance().showGameWaitShow();
         m_rankListAPIListener.submitScoreInfoReturnFun = submitScoreInfoReturnFunWhenNotHave;
         m_rankListAPIListener.submitScoreInfoErrorFun = submitScoreInfoErrorFunWhenNotHave;
         GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         var _loc4_:Vector.<SubmitData> = new Vector.<SubmitData>();
         _loc4_.push(_loc3_);
         if(_loc2_)
         {
            _loc4_.push(_loc2_);
         }
         GamingUI.getInstance().getAPI4399().rankListAPI.submitScoreToRankLists(GameData.getInstance().getSaveFileData().index,_loc4_);
         trace("提交数据到排行榜:","存档索引:",GameData.getInstance().getSaveFileData().index,"submitData:","rankListId:",_loc3_.getRId(),"winMath:",_loc3_.getScore());
      }
      
      private function getRankInfos(param1:Vector.<UserDataInRankList>) : void
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:UserDataInRankList = null;
         var _loc3_:PKTargetPlayerData = null;
         Part1.getInstance().getApi4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         m_rankListAPIListener.getRankListsDataFun = null;
         if(param1.length == 1)
         {
            _loc4_ = m_pkSaveDataOne.getPKTargetPlayerNum();
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(m_pkSaveDataOne.getPKTargetPlayerDataByIndex(_loc5_).getPKIndex() == m_pkIndex)
               {
                  throw new Error("出错了555");
               }
               _loc5_++;
            }
            _loc2_ = param1[0];
            if(_loc2_.getUid() == GameData.getInstance().getLoginReturnData().getUid() && _loc2_.getIndex() == GameData.getInstance().getSaveFileData().index)
            {
               getOnePKTargetPlayerRankData(m_getNeedRank - 1);
               return;
            }
            _loc3_ = new PKTargetPlayerData(_loc2_.getUid(),_loc2_.getIndex(),m_pkIndex,0,_loc2_.getUserName(),"","",_loc2_);
            m_pkTargetPlayerData = _loc3_;
            m_pkSaveDataOne.addPKTargetPlayerData(m_pkTargetPlayerData);
            m_getNeedRank = 0;
            getMySaveData();
         }
         else
         {
            if(param1.length > 1)
            {
               throw new Error("出错了666");
            }
            getOnePKTargetPlayerRankData(m_getNeedRank = m_getNeedRank - 1);
         }
      }
      
      private function getMySaveData() : void
      {
         m_myPlayerDatas.getPlayerData(GameData.getInstance().getLoginReturnData().getUid(),GameData.getInstance().getSaveFileData().index);
      }
      
      private function getFoeSaveData() : void
      {
         m_foePalyerDatas.getPlayerData(m_pkTargetPlayerData.getUid(),m_pkTargetPlayerData.getIndexOfSaveXML());
      }
      
      private function getDataFail(param1:Event) : void
      {
         getFoeSaveData();
      }
      
      private function submitScoreInfoReturnFunWhenNotHave(param1:Vector.<SubmitSuccessReturnData>) : void
      {
         Part1.getInstance().hideGameWaitShow();
         ClearUtil.clearObject(param1);
         param1 = null;
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         trace("提交数据到排行榜成功");
         m_isSigned = true;
         if(m_pkType == "onePK")
         {
            PlayerDataForPK.getInstance().isSignUpForSingle = true;
         }
         else if(m_pkType == "twoPK")
         {
            PlayerDataForPK.getInstance().isSignUpForDouble = true;
         }
         init(m_pkType);
      }
      
      private function submitScoreInfoErrorFunWhenNotHave(param1:SubmitReturnData, param2:String) : void
      {
         Part1.getInstance().hideGameWaitShow();
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(param1);
         param1 = null;
         Part1.getInstance().closePK();
         Part1.getInstance().returnCity();
         GamingUI.getInstance().showMessageTip("数据提交失败");
         trace("提交数据到排行榜失败, errorMessage:",param2);
      }
      
      private function submitScoreInfoReturnFun(param1:Vector.<SubmitSuccessReturnData>) : void
      {
         ClearUtil.clearObject(param1);
         param1 = null;
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         trace("提交数据到排行榜成功");
      }
      
      private function submitScoreInfoErrorFun(param1:SubmitReturnData, param2:String) : void
      {
         m_rankListAPIListener.submitScoreInfoReturnFun = null;
         m_rankListAPIListener.submitScoreInfoErrorFun = null;
         GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         ClearUtil.clearObject(param1);
         param1 = null;
         trace("提交数据到排行榜失败");
      }
      
      private function rankListError1(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
         Part1.getInstance().closePK();
         Part1.getInstance().returnCity();
      }
      
      private function rankListError2(param1:String) : void
      {
         GamingUI.getInstance().showMessageTip("加载数据出错");
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         m_show.addChildAt(MessageBoxEngine.getInstance(),m_show.numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1,m_otherPlayerShowPanel ? m_otherPlayerShowPanel.currentPlayer : null);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      private function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(m_show.stage);
         m_show.addChildAt(WarningBoxSingle.getInstance(),m_show.numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function clearPKDataForNextPK() : void
      {
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         ClearUtil.clearObject(m_foeUiPlayer1);
         m_foeUiPlayer1 = null;
         ClearUtil.clearObject(m_foeUiPlayer2);
         m_foeUiPlayer2 = null;
         if(m_foeUiPlayerData)
         {
            m_foePalyerDatas.clearOnePlayerData(m_foeUiPlayerData);
         }
         m_foeUiPlayerData = null;
      }
   }
}

