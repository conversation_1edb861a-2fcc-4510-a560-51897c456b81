package UI.NicknameSystem
{
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.RankListFunction;
   import YJFY.API_4399.LogAPI.LoginReturnData;
   import YJFY.API_4399.SaveAPI.SaveFileData;
   import YJFY.GameData;
   import YJFY.Part1;
   import flash.utils.setTimeout;
   import unit4399.events.RankListEvent;
   
   public class NicknameRankListFunction
   {
      
      private static var _instance:NicknameRankListFunction = null;
      
      public function NicknameRankListFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已存在！");
      }
      
      public static function getInstance() : NicknameRankListFunction
      {
         if(!_instance)
         {
            _instance = new NicknameRankListFunction();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
         {
            Part1.getInstance().stage.removeEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
         }
         if(!Part1.getInstance().stage.hasEventListener("rankListError"))
         {
            Part1.getInstance().stage.removeEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
         }
         _instance = null;
      }
      
      public function getOneRankListDataByUserName(param1:String, param2:uint, param3:String, param4:uint, param5:Function, param6:Function, param7:Array, param8:Array, param9:Function, param10:Array) : void
      {
         var loginReturnData:LoginReturnData;
         var saveFileData:SaveFileData;
         var nickNameType:String = param1;
         var rankListId:uint = param2;
         var userName:String = param3;
         var idx:uint = param4;
         var getOneDataBeforeFun:Function = param5;
         var getOneDataAfterFun:Function = param6;
         var getOneDataBeforeFunParams:Array = param7;
         var getOneDataAfterFunParams:Array = param8;
         var getOneDataFailFun:Function = param9;
         var getOneDataFailFunParams:Array = param10;
         var decodeRankListInfo:* = function(param1:Array):void
         {
            var _loc4_:int = 0;
            if(param1 == null || param1.length == 0)
            {
               trace("getOneRankListDataByUserName 获取到的昵称数据为null");
               getOneDataAfterFunParams.push(null);
               getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
               return;
            }
            var _loc3_:int = int(param1.length);
            var _loc2_:Boolean = false;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(param1[_loc4_])
               {
                  if(param1[_loc4_].index == idx)
                  {
                     param1[_loc4_].rId = rankListId;
                     param1[_loc4_].nicknameType = nickNameType;
                     trace("getOneRankListDataByUserName， 获取到符合人物存档索引的昵称数据：",param1[_loc4_]);
                     getOneDataAfterFunParams.push(param1[_loc4_]);
                     getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
                     _loc2_ = true;
                     break;
                  }
               }
               _loc4_++;
            }
            if(!_loc2_)
            {
               trace("getOneRankListDataByUserName. 获取的昵称数据为null");
               getOneDataAfterFunParams.push(null);
               getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
            }
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               param1[_loc4_] = null;
               _loc4_++;
            }
            param1 = null;
         };
         if(Boolean(getOneDataBeforeFun))
         {
            getOneDataBeforeFun.apply(null,getOneDataBeforeFunParams);
         }
         if(!getOneDataAfterFunParams)
         {
            getOneDataAfterFunParams = [];
         }
         RankListFunction.getInstance().getOneRankListDataByUserName(rankListId,userName,null,decodeRankListInfo,null,null,getOneDataFailFun,getOneDataFailFunParams);
         if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
            {
               Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
            }
            if(!Part1.getInstance().stage.hasEventListener("rankListError"))
            {
               Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
            }
            loginReturnData = GameData.getInstance().getLoginReturnData();
            saveFileData = GameData.getInstance().getSaveFileData();
            setTimeout(function():void
            {
               Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                  "data":[{
                     "userName":loginReturnData.getName(),
                     "score":1,
                     "rank":1,
                     "index":GameData.getInstance().getSaveFileData().index,
                     "extra":"ABC"
                  }],
                  "apiName":"1"
               }));
            },500);
         }
      }
      
      public function getOneRankListDataByUserNameFromAllRankLists(param1:String, param2:Function, param3:Function, param4:Array, param5:Array, param6:Function, param7:Array) : void
      {
         var userName:String = param1;
         var getOneDataBeforeFun:Function = param2;
         var getOneDataAfterFun:Function = param3;
         var getOneDataBeforeFunParams:Array = param4;
         var getOneDataAfterFunParams:Array = param5;
         var getOneDataFailFun:Function = param6;
         var getOneDataFailFunParams:Array = param7;
         if(Boolean(getOneDataBeforeFun))
         {
            getOneDataBeforeFun.apply(null,getOneDataBeforeFunParams);
         }
         if(!getOneDataAfterFunParams)
         {
            getOneDataAfterFunParams = [];
         }
         MyFunction2.loadXMLFunction("nickname",function(param1:XML):void
         {
            var i:int;
            var xml:XML = param1;
            var decodeRankListInfo:* = function(param1:Array):void
            {
               var _loc4_:int = 0;
               if(param1 == null || param1.length == 0)
               {
                  i++;
                  if(i < length)
                  {
                     trace("获取昵称从排行榜from getOneRankListDataByUserNameFromAllRankLists","排行榜id：",rankListIds[i],"userName：",userName);
                     RankListFunction.getInstance().getOneRankListDataByUserName(rankListIds[i],userName,null,decodeRankListInfo,null,null,getOneDataFailFun,getOneDataFailFunParams);
                  }
                  else
                  {
                     trace("getOneRankListDataByUserNameFromAllRankLists  获取到的数据为null");
                     getOneDataAfterFunParams.push(null);
                     getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
                  }
                  return;
               }
               var _loc3_:int = int(param1.length);
               var _loc2_:Boolean = false;
               _loc4_ = 0;
               while(_loc4_ < _loc3_)
               {
                  if(param1[_loc4_])
                  {
                     if(param1[_loc4_].index == GameData.getInstance().getSaveFileData().index)
                     {
                        param1[_loc4_].rId = rankListIds[i];
                        if(i < payRankListIds.length)
                        {
                           param1[_loc4_].nicknameType = "redNickname";
                        }
                        else
                        {
                           param1[_loc4_].nicknameType = "whiteNickname";
                        }
                        trace("getOneRankListDataByUserNameFromAllRankLists  获取到符合存档索引的昵称数据:" + param1[_loc4_]);
                        getOneDataAfterFunParams.push(param1[_loc4_]);
                        getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
                        _loc2_ = true;
                        break;
                     }
                  }
                  _loc4_++;
               }
               if(!_loc2_)
               {
                  i++;
                  if(i < length)
                  {
                     RankListFunction.getInstance().getOneRankListDataByUserName(rankListIds[i],userName,null,decodeRankListInfo,null,null,getOneDataFailFun,getOneDataFailFunParams);
                  }
                  else
                  {
                     trace("getOneRankListDataByUserNameFromAllRankLists 获取到的昵称数据为null");
                     getOneDataAfterFunParams.push(null);
                     getOneDataAfterFun.apply(null,getOneDataAfterFunParams);
                  }
               }
               _loc4_ = 0;
               while(_loc4_ < _loc3_)
               {
                  param1[_loc4_] = null;
                  _loc4_++;
               }
               param1 = null;
            };
            var nicknameXML:XML = xml;
            var payRankListIds:Vector.<int> = MyFunction.getInstance().excreteString(xml.RankListID[0].@payRankListID);
            var freeRankListIds:Vector.<int> = MyFunction.getInstance().excreteString(xml.RankListID[0].@freeRankListID);
            var rankListIds:Vector.<int> = payRankListIds.concat(freeRankListIds);
            var length:int = int(rankListIds.length);
            if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               length = 1;
            }
            trace("获取昵称从排行榜from getOneRankListDataByUserNameFromAllRankLists","排行榜id：",rankListIds[i],"userName：",userName);
            RankListFunction.getInstance().getOneRankListDataByUserName(rankListIds[i],userName,null,decodeRankListInfo,null,null,getOneDataFailFun,getOneDataFailFunParams);
            if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
               {
                  Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
               }
               if(!Part1.getInstance().stage.hasEventListener("rankListError"))
               {
                  Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
               }
               setTimeout(function():void
               {
                  Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                     "data":[{
                        "userName":GameData.getInstance().getLoginReturnData().getName(),
                        "score":1,
                        "rank":1,
                        "index":GameData.getInstance().getSaveFileData().index,
                        "extra":"ABC"
                     }],
                     "apiName":"1"
                  }));
               },100);
            }
         },function():void
         {
            showWarningBox("网络连接失败！",0);
            getOneDataFailFun.apply(null,getOneDataFailFunParams);
         });
      }
      
      public function makeSureRankListIsFull(param1:int, param2:Function, param3:Function, param4:Array, param5:Array) : void
      {
         var freeOrPay:int = param1;
         var makeSureRankListIsFullBeforeFun:Function = param2;
         var makeSureRankListIsFullAfterFun:Function = param3;
         var makeSureRankListIsFullBeforeFunParams:Array = param4;
         var makeSureRankListFullAfterParams:Array = param5;
         if(Boolean(makeSureRankListIsFullBeforeFun))
         {
            makeSureRankListIsFullBeforeFun.apply(null,makeSureRankListIsFullBeforeFunParams);
         }
         if(!freeOrPay)
         {
            NicknameData.getInstance().freeRankListId = 0;
         }
         else
         {
            NicknameData.getInstance().payRankListId = 0;
         }
         MyFunction2.loadXMLFunction("nickname",function(param1:XML):void
         {
            var rankListIds:Vector.<int>;
            var length:int;
            var i:int;
            var xml:XML = param1;
            var decodeRankListInfo:* = function(param1:Array):void
            {
               var _loc3_:int = 0;
               if(param1 == null || param1.length == 0)
               {
                  trace("makeSureRankListIsFull data == null || data.length == 0");
                  if(!freeOrPay)
                  {
                     NicknameData.getInstance().freeRankListId = rankListIds[i];
                     trace("设置免费排行榜id：",rankListIds[i]);
                  }
                  else
                  {
                     NicknameData.getInstance().payRankListId = rankListIds[i];
                     trace("设置付费排行榜id：",rankListIds[i]);
                  }
                  makeSureRankListIsFullAfterFun.apply(null,makeSureRankListFullAfterParams);
                  return;
               }
               var _loc2_:int = int(param1.length);
               i++;
               if(i < length)
               {
                  RankListFunction.getInstance().getRankListData(rankListIds[i],9000,1,null,decodeRankListInfo,null,null,showWarningBox,["获取数据出错了！",0]);
               }
               else
               {
                  trace("所有排行榜（付费或免费）都满了");
                  makeSureRankListIsFullAfterFun.apply(null,makeSureRankListFullAfterParams);
               }
               _loc3_ = 0;
               while(_loc3_ < _loc2_)
               {
                  param1[_loc3_] = null;
                  _loc3_++;
               }
               param1 = null;
            };
            var nicknameXML:XML = xml;
            if(!freeOrPay)
            {
               rankListIds = MyFunction.getInstance().excreteString(xml.RankListID[0].@freeRankListIDForMakeSureFull);
            }
            else
            {
               rankListIds = MyFunction.getInstance().excreteString(xml.RankListID[0].@payRankListIDForMakeSureFull);
            }
            length = int(rankListIds.length);
            RankListFunction.getInstance().getRankListData(rankListIds[i],9000,1,null,decodeRankListInfo,null,null,showWarningBox,["获取数据出错了！",0]);
            if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
            {
               if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
               {
                  Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
               }
               if(!Part1.getInstance().stage.hasEventListener("rankListError"))
               {
                  Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
               }
               setTimeout(function():void
               {
                  Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
                     "data":[],
                     "apiName":"4"
                  }));
               },100);
            }
         },function():void
         {
            showWarningBox("网络连接失败!",0);
         });
      }
      
      public function submitMyDataToRankLists(param1:uint, param2:int, param3:int, param4:String, param5:Function, param6:Function, param7:Function, param8:Array, param9:Array, param10:Array) : void
      {
         if(Boolean(param5))
         {
            param5.apply(null,param8);
         }
         var _loc11_:Array = [{
            "rId":param2,
            "score":param3,
            "extra":param4
         }];
         RankListFunction.getInstance().submitScoreToRankLists(param1,_loc11_,null,param6,null,param9,param7,param10);
         if(GamingUI.getInstance().getVersionControl().getLineMode().getLineMode() == "offLine")
         {
            if(!Part1.getInstance().stage.hasEventListener("rankListSuccess"))
            {
               Part1.getInstance().stage.addEventListener("rankListSuccess",RankListFunction.getInstance().onRankListSuccessHander);
            }
            if(!Part1.getInstance().stage.hasEventListener("rankListError"))
            {
               Part1.getInstance().stage.addEventListener("rankListError",RankListFunction.getInstance().onRankListErrorHandler);
            }
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "data":[{
                  "code":"10000",
                  "curRank":1
               }],
               "apiName":"3"
            }));
         }
      }
      
      public function getNicknameTypeByRankListId(param1:uint, param2:XML) : String
      {
         var _loc5_:String = null;
         var _loc7_:int = 0;
         var _loc4_:Vector.<int> = MyFunction.getInstance().excreteString(param2.RankListID[0].@payRankListID);
         var _loc3_:Vector.<int> = MyFunction.getInstance().excreteString(param2.RankListID[0].@freeRankListID);
         var _loc6_:int = int(_loc4_.length);
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            if(_loc4_[_loc7_] == param1)
            {
               trace("getNicknameTypeByRankListId 确定昵称类型为：redNickname");
               _loc5_ = "redNickname";
               break;
            }
            _loc7_++;
         }
         if(_loc7_ == _loc6_)
         {
            trace("getNicknameTypeByRankListId 确定昵称类型为：whiteNickname");
            _loc5_ = "whiteNickname";
         }
         return _loc5_;
      }
      
      public function showWarningBox(param1:String, param2:int) : void
      {
         if(GamingUI.getInstance().internalPanel)
         {
            GamingUI.getInstance().internalPanel.showWarningBox(param1,param2,null);
         }
      }
   }
}

