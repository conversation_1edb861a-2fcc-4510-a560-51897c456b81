package UI.MessageTextShow
{
   import flash.filters.GlowFilter;
   
   public class MessageTextFilters
   {
      
      public function MessageTextFilters()
      {
         super();
      }
      
      public static function get FILTERS_1() : Array
      {
         return [new GlowFilter(0,1,2,2,10,2)];
      }
      
      public static function get FILTERS_2() : Array
      {
         return [new GlowFilter(16777215,1,2,2,10,3)];
      }
   }
}

