package UI.EquipEquipmentFuns
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.EquipmentVO.PetSkillBookEquipmentVO;
   import UI.Equipments.PetEquipments.AdvancePetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.GamingUI;
   import UI.MyControlPanel;
   import UI.PetAdvancePanel.UsePetSkillBookPanel;
   import UI.Players.Player;
   import UI.Skills.Skill;
   import UI.XMLSingle;
   
   public class EquipPetSkillBook
   {
      
      private var _oldSkill:Skill;
      
      private var _newSkill:Skill;
      
      private var _usePetSkillBookPanel:UsePetSkillBookPanel;
      
      public function EquipPetSkillBook()
      {
         super();
      }
      
      public function clear() : void
      {
         if(_oldSkill)
         {
            _oldSkill.clear();
         }
         _oldSkill = null;
         if(_newSkill)
         {
            _newSkill.clear();
         }
         _newSkill = null;
         if(_usePetSkillBookPanel)
         {
            _usePetSkillBookPanel.clear();
         }
         _usePetSkillBookPanel = null;
      }
      
      public function equip(param1:EquipmentVO, param2:Player, param3:IEquipEqListener) : void
      {
         var myControlPanel:MyControlPanel;
         var petEqupmentVO:AdvancePetEquipmentVO;
         var petSkillBook:EquipmentVO = param1;
         var player:Player = param2;
         var equipEqListener:IEquipEqListener = param3;
         if(player == null || player.playerVO == null || player.playerVO.pet == null || !(player.playerVO.pet.petEquipmentVO is AdvancePetEquipmentVO))
         {
            if(equipEqListener)
            {
               equipEqListener.showWarning("没有进阶宠物，<font size=\'" + (equipEqListener.getWarningTextFontSize() + 4) + "\' color=\'#ff0000\'>" + petSkillBook.name + "</font>不能使用！",0);
               equipEqListener.actionAfterUnableUse();
            }
            return;
         }
         myControlPanel = GamingUI.getInstance().internalPanel;
         if(myControlPanel == null)
         {
            return;
         }
         if(_usePetSkillBookPanel)
         {
            if(_usePetSkillBookPanel.parent)
            {
               _usePetSkillBookPanel.parent.removeChild(_usePetSkillBookPanel);
            }
            _usePetSkillBookPanel.clear();
         }
         _usePetSkillBookPanel = null;
         _usePetSkillBookPanel = new UsePetSkillBookPanel();
         myControlPanel.addChildAt(_usePetSkillBookPanel,myControlPanel.getChildIndex(myControlPanel.topLayer));
         if(_oldSkill)
         {
            _oldSkill.clear();
         }
         _oldSkill = null;
         if(_newSkill)
         {
            _newSkill.clear();
         }
         _newSkill = null;
         _oldSkill = new Skill((player.playerVO.pet.petEquipmentVO as AdvancePetEquipmentVO).petAwakePassiveSkillVOs[0].clone());
         _newSkill = new Skill(XMLSingle.getSkill((petSkillBook as PetSkillBookEquipmentVO).value,XMLSingle.getInstance().skillXML));
         petEqupmentVO = player.playerVO.pet.petEquipmentVO as AdvancePetEquipmentVO;
         _usePetSkillBookPanel.init2(_oldSkill.clone(),_newSkill.clone(),function():void
         {
            var _loc1_:* = null;
            var _loc2_:int = int(player.playerVO.packageEquipmentVOs.indexOf(petSkillBook));
            if(_loc2_ == -1)
            {
               if(equipEqListener)
               {
                  equipEqListener.showWarning("物品已不存在, 使用失败！",0);
                  equipEqListener.actionAfterUnableUse();
               }
               return;
            }
            if(petSkillBook is StackEquipmentVO)
            {
               (petSkillBook as StackEquipmentVO).num -= 1;
               if((petSkillBook as StackEquipmentVO).num == 0)
               {
                  player.playerVO.packageEquipmentVOs[_loc2_] = null;
                  petSkillBook.clear();
                  petSkillBook = null;
               }
            }
            else
            {
               player.playerVO.packageEquipmentVOs[_loc2_] = null;
               petSkillBook.clear();
               petSkillBook = null;
            }
            GamingUI.getInstance().refresh(2);
            petEqupmentVO.recoverAwakeSkill(_newSkill.skillVO,0);
            _newSkill = null;
            if(_oldSkill)
            {
               _oldSkill.clear();
            }
            _oldSkill = null;
            _usePetSkillBookPanel.clear();
            _usePetSkillBookPanel = null;
            if(equipEqListener)
            {
               equipEqListener.actionAfterUse();
            }
         },null,function():void
         {
            if(_newSkill)
            {
               _newSkill.clear();
            }
            _newSkill = null;
            if(_oldSkill)
            {
               _oldSkill.clear();
            }
            _oldSkill = null;
            if(_usePetSkillBookPanel)
            {
               _usePetSkillBookPanel.clear();
            }
            _usePetSkillBookPanel = null;
         },null);
      }
   }
}

