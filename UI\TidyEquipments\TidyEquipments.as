package UI.TidyEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MyFunction;
   import UI.MyFunction2;
   import YJFY.Utils.ClearUtil;
   
   public class TidyEquipments
   {
      
      private const _EUIPMEN_TYPE:String = "equipmentType";
      
      private const _IS_BINDING:String = "isBinding";
      
      private const _ID:String = "id";
      
      private var _tiDyDataXML:XML;
      
      public function TidyEquipments()
      {
         super();
      }
      
      public function loadXML(param1:Function, param2:Function) : void
      {
         var completeFun:Function = param1;
         var showWarningBox:Function = param2;
         if(_tiDyDataXML == null)
         {
            MyFunction2.loadXMLFunction("tiDyEquipments",function(param1:XML):void
            {
               _tiDyDataXML = param1;
               if(Boolean(completeFun))
               {
                  completeFun();
               }
            },showWarningBox,true);
         }
         else if(Boolean(completeFun))
         {
            completeFun();
         }
      }
      
      public function clear() : void
      {
         _tiDyDataXML = null;
      }
      
      public function tiDyEquipments(param1:Vector.<EquipmentVO>) : Vector.<EquipmentVO>
      {
         var _loc13_:int = 0;
         var _loc9_:int = 0;
         var _loc17_:int = 0;
         var _loc10_:int = 0;
         var _loc14_:int = 0;
         var _loc11_:int = 0;
         var _loc2_:EquipmentVO = null;
         var _loc4_:* = undefined;
         var _loc19_:Number = NaN;
         var _loc8_:XML = null;
         var _loc12_:SortObj = null;
         var _loc5_:Boolean = false;
         var _loc6_:String = null;
         var _loc16_:* = undefined;
         var _loc18_:* = undefined;
         var _loc7_:XMLList = _tiDyDataXML.priority;
         var _loc3_:Array = [];
         _loc17_ = int(param1.length);
         _loc13_ = 0;
         while(_loc13_ < _loc17_)
         {
            _loc2_ = param1[_loc13_];
            if(_loc2_ != null)
            {
               _loc4_ = _loc3_;
               _loc10_ = int(_loc7_.length());
               _loc9_ = 0;
               while(_loc9_ < _loc10_)
               {
                  _loc8_ = _loc7_[_loc9_];
                  _loc19_ = int(_loc8_.@defaultPriority);
                  _loc6_ = String(_loc8_.@by);
                  if(_loc2_.hasOwnProperty(_loc6_))
                  {
                     if(int(_loc8_.@isUseAttValue))
                     {
                        _loc19_ = Number(_loc2_[_loc6_]);
                        _loc8_ = null;
                     }
                     else
                     {
                        _loc8_ = _loc8_.item.(@value == _loc2_[_loc6_])[0];
                     }
                  }
                  else
                  {
                     _loc8_ = null;
                  }
                  if(_loc8_)
                  {
                     _loc19_ = int(_loc8_.@priority);
                  }
                  if(_loc9_ < _loc10_ - 1)
                  {
                     _loc11_ = int(_loc4_.length);
                     _loc5_ = false;
                     _loc14_ = 0;
                     while(_loc14_ < _loc11_)
                     {
                        if((_loc4_[_loc14_] as SortObj).prioriry > _loc19_)
                        {
                           _loc12_ = new SortObj();
                           _loc12_.prioriry = _loc19_;
                           _loc12_.arr = [];
                           (_loc4_ as Array).splice(_loc14_,0,_loc12_);
                           _loc4_ = _loc12_.arr;
                           _loc5_ = true;
                           break;
                        }
                        if((_loc4_[_loc14_] as SortObj).prioriry == _loc19_)
                        {
                           _loc4_ = (_loc4_[_loc14_] as SortObj).arr;
                           _loc5_ = true;
                           break;
                        }
                        _loc14_++;
                     }
                     if(!_loc5_)
                     {
                        _loc12_ = new SortObj();
                        _loc12_.prioriry = _loc19_;
                        _loc12_.arr = [];
                        (_loc4_ as Array).push(_loc12_);
                        _loc4_ = _loc12_.arr;
                     }
                  }
                  else
                  {
                     _loc11_ = int(_loc4_.length);
                     _loc5_ = false;
                     _loc14_ = 0;
                     while(_loc14_ < _loc11_)
                     {
                        if((_loc4_[_loc14_] as SortObj).prioriry > _loc19_)
                        {
                           _loc12_ = new SortObj();
                           _loc12_.prioriry = _loc19_;
                           _loc12_.arr = new Vector.<EquipmentVO>();
                           (_loc4_ as Array).splice(_loc14_,0,_loc12_);
                           _loc4_ = _loc12_.arr;
                           _loc5_ = true;
                           break;
                        }
                        if((_loc4_[_loc14_] as SortObj).prioriry == _loc19_)
                        {
                           _loc4_ = (_loc4_[_loc14_] as SortObj).arr;
                           _loc5_ = true;
                           break;
                        }
                        _loc14_++;
                     }
                     if(!_loc5_)
                     {
                        _loc12_ = new SortObj();
                        _loc12_.prioriry = _loc19_;
                        _loc12_.arr = new Vector.<EquipmentVO>();
                        (_loc4_ as Array).push(_loc12_);
                        _loc4_ = _loc12_.arr;
                     }
                     if(!MyFunction.getInstance().combineTheEquipmentVO(_loc2_,_loc4_ as Vector.<EquipmentVO>))
                     {
                        _loc11_ = int(_loc4_.length);
                        _loc5_ = false;
                        _loc14_ = 0;
                        while(_loc14_ < _loc11_)
                        {
                           if(_loc4_[_loc14_].id == _loc2_.id)
                           {
                              _loc4_.splice(_loc14_,0,_loc2_);
                              _loc5_ = true;
                              break;
                           }
                           _loc14_++;
                        }
                        if(!_loc5_)
                        {
                           _loc4_.push(_loc2_);
                        }
                     }
                  }
                  _loc9_++;
               }
            }
            _loc13_++;
         }
         var _loc15_:* = returnEquipmentsFromArr(_loc3_);
         ClearUtil.nullArr(_loc3_);
         if(_loc15_.length < param1.length)
         {
            _loc18_ = new Vector.<EquipmentVO>(param1.length - _loc15_.length);
            _loc16_ = _loc15_.concat(_loc18_);
            ClearUtil.nullArr(_loc18_,false,false,false);
            ClearUtil.nullArr(_loc15_,false,false,false);
            _loc15_ = _loc16_;
         }
         return _loc15_;
      }
      
      private function returnEquipmentsFromArr(param1:Array) : Vector.<EquipmentVO>
      {
         var _loc5_:* = undefined;
         var _loc4_:* = undefined;
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:SortObj = null;
         var _loc2_:* = new Vector.<EquipmentVO>();
         _loc3_ = param1 ? param1.length : 0;
         _loc7_ = _loc3_ - 1;
         while(_loc7_ >= 0)
         {
            _loc6_ = SortObj(param1[_loc7_]);
            if(_loc6_ != null)
            {
               if(_loc6_.arr is Vector.<EquipmentVO>)
               {
                  _loc5_ = _loc2_.concat(_loc6_.arr);
                  ClearUtil.nullArr(_loc2_,false,false,false);
                  ClearUtil.nullArr(_loc6_.arr,false,false,false);
                  _loc2_ = _loc5_;
               }
               else
               {
                  _loc5_ = returnEquipmentsFromArr(_loc6_.arr);
                  _loc4_ = _loc2_.concat(_loc5_);
                  ClearUtil.nullArr(_loc5_,false,false,false);
                  ClearUtil.nullArr(_loc2_,false,false,false);
                  _loc2_ = _loc4_;
               }
            }
            _loc7_--;
         }
         return _loc2_;
      }
   }
}

import YJFY.Utils.ClearUtil;

class SortObj
{
   
   public var prioriry:Number;
   
   public var arr:*;
   
   public function SortObj()
   {
      super();
   }
   
   public function clear() : void
   {
      ClearUtil.nullArr(arr);
   }
}
