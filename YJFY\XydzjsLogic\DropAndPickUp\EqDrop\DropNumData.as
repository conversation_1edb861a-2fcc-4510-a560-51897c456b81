package YJFY.XydzjsLogic.DropAndPickUp.EqDrop
{
   import YJFY.Utils.ClearUtil;
   
   public class DropNumData
   {
      
      private var m_dropNumDataOnes:Vector.<DropNumDataOne>;
      
      public function DropNumData()
      {
         super();
         m_dropNumDataOnes = new Vector.<DropNumDataOne>();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_dropNumDataOnes);
         m_dropNumDataOnes = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         var _loc9_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:DropNumDataOne = null;
         var _loc6_:Number = NaN;
         var _loc7_:XMLList = param1.children();
         _loc5_ = int(_loc7_.length());
         var _loc2_:Number = 0;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc2_ += Number(_loc7_[_loc9_].@proWeight);
            _loc9_++;
         }
         var _loc4_:Number = 0;
         var _loc8_:* = 0;
         _loc9_ = 0;
         while(_loc9_ < _loc5_)
         {
            _loc6_ = Number(_loc7_[_loc9_].@proWeight);
            _loc4_ = _loc8_ + _loc6_ / _loc2_;
            _loc3_ = new DropNumDataOne(_loc6_,_loc4_,_loc8_);
            _loc3_.initNumDatas(_loc7_[_loc9_]);
            m_dropNumDataOnes.push(_loc3_);
            _loc8_ = _loc4_;
            _loc9_++;
         }
      }
      
      public function getOneRandomDropNum(param1:RandomNumReturn, param2:Number) : void
      {
         var _loc6_:int = 0;
         var _loc4_:Boolean = false;
         var _loc3_:Number = Math.random();
         _loc3_ = Math.min(1,_loc3_ + param2);
         var _loc5_:int = int(m_dropNumDataOnes.length);
         _loc6_ = 0;
         while(_loc6_ < _loc5_)
         {
            if(_loc3_ <= m_dropNumDataOnes[_loc6_].getUpPro())
            {
               if(_loc6_)
               {
                  _loc4_ = true;
               }
               param1.num = m_dropNumDataOnes[_loc6_].getOneRandomNumData().getNum();
               param1.isRenPingBaoFa = _loc4_;
               return;
            }
            _loc6_++;
         }
         throw new Error("概率计算出错了");
      }
   }
}

