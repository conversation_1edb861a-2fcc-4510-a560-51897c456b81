package YJFY.Skill.TieShanSkills
{
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Skill.SkillLogic.EffectShowAddToTargetInHurt;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_TieShanSkillHuo1 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private var m_effectAddtoTargetId:String;
      
      private var m_effectAddtoTargetDefinitionData:AnimationDefinitionData;
      
      private var m_effectShowAddToTargetInHurt:EffectShowAddToTargetInHurt;
      
      public function Skill_TieShanSkillHuo1()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_effectAddtoTargetId = null;
         ClearUtil.clearObject(m_effectAddtoTargetDefinitionData);
         m_effectAddtoTargetDefinitionData = null;
         ClearUtil.clearObject(m_effectShowAddToTargetInHurt);
         m_effectShowAddToTargetInHurt = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_effectAddtoTargetId = String(param1.@addBuffId);
         if(m_effectAddtoTargetId)
         {
            m_effectAddtoTargetDefinitionData = new AnimationDefinitionData();
            m_effectAddtoTargetDefinitionData.initByXML(param1.animationDefinition.(@id == m_effectAddtoTargetId)[0]);
            m_effectShowAddToTargetInHurt = new EffectShowAddToTargetInHurt();
            m_effectShowAddToTargetInHurt.setIsHideTargetBodyShow(true);
            m_effectShowAddToTargetInHurt.setEffectAddToTargetDefinitionData(m_effectAddtoTargetDefinitionData);
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         if(m_effectShowAddToTargetInHurt)
         {
            m_effectShowAddToTargetInHurt.setWorld(param1);
         }
         super.releaseSkill2(param1);
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && param1 is IAnimalEntity)
         {
            if(m_effectShowAddToTargetInHurt)
            {
               m_effectShowAddToTargetInHurt.addEffectShowAddToTarget(param1);
            }
         }
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

