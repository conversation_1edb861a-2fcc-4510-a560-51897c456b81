package YJFY.Utils
{
   public class TransformFunction
   {
      
      public function TransformFunction()
      {
         super();
      }
      
      public static function excreteString(param1:String) : Vector.<int>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1) || param1 == "")
         {
            return new Vector.<int>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc2_:Vector.<int> = new Vector.<int>();
         var _loc3_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_] = int(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc2_;
      }
      
      public static function excreteStringToNumber(param1:String) : Vector.<Number>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1) || param1 == "")
         {
            return new Vector.<Number>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc2_:Vector.<Number> = new Vector.<Number>();
         var _loc3_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_] = Number(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc2_;
      }
      
      public static function excreteStringToString(param1:String) : Vector.<String>
      {
         var _loc4_:int = 0;
         if(!Boolean(param1))
         {
            return new Vector.<String>();
         }
         var _loc5_:Array = param1.split("_");
         var _loc3_:Vector.<String> = new Vector.<String>();
         var _loc2_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc3_.push(_loc5_[_loc4_]);
            _loc4_++;
         }
         return _loc3_;
      }
      
      public static function excreteStringToStringArr(param1:String) : Array
      {
         if(!Boolean(param1))
         {
            return [];
         }
         return param1.split("_");
      }
      
      public static function excreteSpaceStrToStringArr(param1:String) : Array
      {
         if(!Boolean(param1))
         {
            return [];
         }
         return param1.split(" ");
      }
      
      public static function changeNum(param1:int) : String
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:String = null;
         if(param1 < 0)
         {
            throw new Error("提供的数字小于零，不符合要求");
         }
         if(param1 < 10)
         {
            return changeSingleNum(param1);
         }
         if(param1 < 100)
         {
            _loc2_ = param1 / 10;
            _loc3_ = param1 % 10;
            if(_loc2_ == 1)
            {
               _loc4_ = "";
            }
            else
            {
               _loc4_ = changeSingleNum(_loc2_);
            }
            if(_loc3_ == 0)
            {
               _loc5_ = "";
            }
            else
            {
               _loc5_ = changeSingleNum(_loc3_);
            }
            return "" + _loc4_ + "十" + _loc5_;
         }
         throw new Error("提供的数字超过了99");
      }
      
      private static function changeSingleNum(param1:int) : String
      {
         if(param1 > 9 || param1 < 0)
         {
            throw new Error("你妹，数字大于9了或小于0了");
         }
         switch(param1)
         {
            case 0:
               return "零";
            case 1:
               return "一";
            case 2:
               return "二";
            case 3:
               return "三";
            case 4:
               return "四";
            case 5:
               return "五";
            case 6:
               return "六";
            case 7:
               return "七";
            case 8:
               return "八";
            case 9:
               return "九";
            default:
               throw new Error("没有符合的");
         }
      }
   }
}

