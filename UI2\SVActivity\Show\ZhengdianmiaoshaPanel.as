package UI2.SVActivity.Show
{
   import Json.MyJSON;
   import UI.AnalogServiceHoldFunction;
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.ShopWall.CurrentTicketPointManager;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.GetEquipmentVOsLogic.GetEquipmentVOsLogic;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import UI2.SVActivity.Data.ZhengdianmiaoshaData;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.text.TextField;
   
   public class ZhengdianmiaoshaPanel
   {
      
      private var m_eqCells:Vector.<Sprite>;
      
      private var m_equipments:Vector.<Equipment>;
      
      private var m_btnTimeList:Vector.<MovieClip>;
      
      private var m_lebNameList:Vector.<TextField>;
      
      private var m_lebOPriceList:Vector.<TextField>;
      
      private var m_lebNPriceList:Vector.<TextField>;
      
      private var m_lebGetNumList:Vector.<TextField>;
      
      private var m_lebRemainNumList:Vector.<TextField>;
      
      private var m_btnGetList:Vector.<MovieClip>;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_zhengdianData:ZhengdianmiaoshaData;
      
      private var m_svActivityPanel:SVActivityPanel;
      
      private var m_nTotalTime:uint;
      
      private var m_strTime:String;
      
      private var m_tDate:Date;
      
      private var m_nSecIndex:int;
      
      private var m_nQueIndex:int;
      
      private var m_nTimeArray:Array;
      
      private var m_nShowIndex:int;
      
      private var m_nCurrTime:int;
      
      private var m_nMoney:int;
      
      private var m_strOpenUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/onSale/query";
      
      private var m_strGetUrl:String = "http://sgxz.aiwan4399.com:8600/monkeyYear/onSale/alter";
      
      private var m_nNumReamin:Array;
      
      private var m_nIdList:Array;
      
      private var m_nClick:int;
      
      private var m_getEquipmentVOsLogic:GetEquipmentVOsLogic;
      
      private var m_nFlag:int;
      
      private var m_show:MovieClip;
      
      private var m_isBuying:Boolean = false;
      
      public function ZhengdianmiaoshaPanel()
      {
         super();
         m_eqCells = new Vector.<Sprite>();
         m_btnTimeList = new Vector.<MovieClip>();
         m_btnGetList = new Vector.<MovieClip>();
         m_lebNameList = new Vector.<TextField>();
         m_lebOPriceList = new Vector.<TextField>();
         m_lebNPriceList = new Vector.<TextField>();
         m_lebGetNumList = new Vector.<TextField>();
         m_lebRemainNumList = new Vector.<TextField>();
         m_nTimeArray = new Array(10,11,13,15,17,19,20);
         m_nNumReamin = new Array(10,10,10);
         m_nIdList = new Array(11000013,11000004,11600016);
         m_getEquipmentVOsLogic = new GetEquipmentVOsLogic();
         m_nSecIndex = 0;
         m_nFlag = 0;
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_eqCells,false,false,false);
         m_eqCells = null;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         ClearUtil.clearObject(m_getEquipmentVOsLogic);
         m_getEquipmentVOsLogic = null;
         ClearUtil.clearObject(m_btnTimeList);
         m_btnTimeList.length = 0;
         m_btnTimeList = null;
         ClearUtil.clearObject(m_btnGetList);
         m_btnGetList.length = 0;
         m_btnGetList = null;
         ClearUtil.clearObject(m_lebNameList);
         m_lebNameList.length = 0;
         m_lebNameList = null;
         ClearUtil.clearObject(m_lebOPriceList);
         m_lebOPriceList.length = 0;
         m_lebOPriceList = null;
         ClearUtil.clearObject(m_lebNPriceList);
         m_lebNPriceList.length = 0;
         m_lebNPriceList = null;
         ClearUtil.clearObject(m_lebGetNumList);
         m_lebGetNumList.length = 0;
         m_lebGetNumList = null;
         ClearUtil.clearObject(m_lebRemainNumList);
         m_lebRemainNumList.length = 0;
         m_lebRemainNumList = null;
         ClearUtil.clearObject(m_nTimeArray);
         m_nTimeArray.length = 0;
         m_nTimeArray = null;
         ClearUtil.clearObject(m_nNumReamin);
         m_nNumReamin.length = 0;
         m_nNumReamin = null;
         ClearUtil.clearObject(m_nIdList);
         m_nIdList.length = 0;
         m_nIdList = null;
         this.m_show.removeEventListener("enterFrame",updateShow);
         GameEvent.eventDispacher.removeEventListener("ticketChange",onResetTicketTextHandler);
         m_show = null;
      }
      
      public function init(param1:MovieClip, param2:SVActivitySaveData, param3:ZhengdianmiaoshaData, param4:SVActivityPanel) : void
      {
         if(Boolean(GamingUI.getInstance()) && Boolean(GamingUI.getInstance().manBan))
         {
            GamingUI.getInstance().manBan.text.text = "处理中...";
         }
         m_show = param1;
         m_svActivitySaveData = param2;
         m_zhengdianData = param3;
         m_svActivityPanel = param4;
         loadUI();
         this.sendInfo();
      }
      
      private function sendInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandler);
         _loc1_.addEventListener("ioError",onErrorHandler);
         _loc1_.addEventListener("securityError",onErrorHandler);
         var _loc2_:URLRequest = new URLRequest(this.m_strOpenUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderCompleteHandler(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         var _loc2_:Object = MyJSON.decode(param1.currentTarget.data);
         this.m_tDate = new Date();
         this.m_tDate.setTime(int(_loc2_.Time) * 1000);
         var _loc3_:int = 0;
         if(_loc2_.Items == null)
         {
            _loc3_ = 0;
            while(_loc3_ < 3)
            {
               this.m_nNumReamin[_loc3_] = this.m_zhengdianData.getMaxNumByIndex(_loc3_);
               this.m_nIdList[_loc3_] = this.m_zhengdianData.getIdByIndex(_loc3_);
               _loc3_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < 3)
            {
               if(int(_loc2_.Items[_loc3_].Num) <= 0)
               {
                  this.m_nNumReamin[_loc3_] = 0;
               }
               else
               {
                  this.m_nNumReamin[_loc3_] = int(_loc2_.Items[_loc3_].Num);
               }
               this.m_nIdList[_loc3_] = int(_loc2_.Items[_loc3_].Id);
               _loc3_++;
            }
         }
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         if(m_nFlag == 0)
         {
            this.initShow();
         }
      }
      
      protected function onErrorHandler(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandler);
         param1.currentTarget.removeEventListener("ioError",onErrorHandler);
         param1.currentTarget.removeEventListener("securityError",onErrorHandler);
         trace("错误:",param1.toString());
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         m_svActivityPanel.showWarningBox("接收数据错误!",0);
      }
      
      private function loadUI() : void
      {
         var _loc1_:int = 0;
         m_btnTimeList.length = 0;
         _loc1_ = 0;
         while(_loc1_ < 7)
         {
            this.m_btnTimeList.push(m_show["btnTime_" + (_loc1_ + 1)]);
            this.m_btnTimeList[_loc1_].gotoAndStop(0);
            _loc1_++;
         }
         m_btnGetList.length = 0;
         m_lebNameList.length = 0;
         m_lebOPriceList.length = 0;
         m_lebNPriceList.length = 0;
         m_lebGetNumList.length = 0;
         m_lebRemainNumList.length = 0;
         m_eqCells.length = 0;
         _loc1_ = 0;
         while(_loc1_ < 3)
         {
            this.m_btnGetList.push(m_show["btnGet_" + (_loc1_ + 1)]);
            this.m_btnGetList[_loc1_].gotoAndStop(0);
            this.m_eqCells.push(m_show["eqCell_" + (_loc1_ + 1)]);
            this.m_lebNameList.push(this.m_show["lebName_" + (_loc1_ + 1)]);
            this.m_lebOPriceList.push(this.m_show["lebOPrice_" + (_loc1_ + 1)]);
            this.m_lebNPriceList.push(this.m_show["NowPrice_" + (_loc1_ + 1)]);
            this.m_lebGetNumList.push(this.m_show["lebMaxNum_" + (_loc1_ + 1)]);
            this.m_lebRemainNumList.push(this.m_show["lebRemain_" + (_loc1_ + 1)]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,this.m_lebNameList[_loc1_]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,this.m_lebOPriceList[_loc1_]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,this.m_lebNPriceList[_loc1_]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,this.m_lebGetNumList[_loc1_]);
            MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,this.m_lebRemainNumList[_loc1_]);
            _loc1_++;
         }
      }
      
      private function registerBtn() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _loc1_ = int(m_btnTimeList.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            this.m_btnTimeList[_loc2_].addEventListener("mouseDown",addBtnTouch);
            _loc2_++;
         }
         _loc1_ = int(m_btnGetList.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            this.m_btnGetList[_loc2_].addEventListener("mouseDown",addGetBtnTouch);
            _loc2_++;
         }
      }
      
      private function initShow() : void
      {
         this.m_nQueIndex = 0;
         this.m_nSecIndex = 0;
         this.m_show.addEventListener("enterFrame",updateShow);
         this.refreshInfo();
         this.m_nShowIndex = 0;
         this.refreshShowIndex();
         this.refreshReward();
         this.m_nMoney = CurrentTicketPointManager.getInstance().getCurrentTicketPoint();
         GameEvent.eventDispacher.addEventListener("ticketChange",onResetTicketTextHandler);
         this.registerBtn();
      }
      
      private function updateShow(param1:Event) : void
      {
         this.m_nQueIndex++;
         this.m_nSecIndex++;
         if(this.m_nQueIndex >= 900)
         {
            this.m_nFlag = 1;
            this.sendInfo();
            this.m_nQueIndex = 0;
         }
         if(this.m_nSecIndex >= 30)
         {
            this.refreshInfo();
            this.m_tDate.setTime(this.m_tDate.time + 1000);
            this.m_nSecIndex = 0;
         }
      }
      
      private function refreshInfo() : void
      {
         var _loc3_:int = 0;
         this.refreshCurrTime();
         var _loc2_:int = this.m_tDate.hours;
         var _loc1_:int = this.m_tDate.minutes;
         _loc3_ = 0;
         while(_loc3_ < 7)
         {
            if(this.m_nCurrTime == _loc3_)
            {
               this.m_btnTimeList[_loc3_].gotoAndStop(1);
            }
            else if(this.m_nTimeArray[_loc3_] - 1 == _loc2_)
            {
               if(_loc1_ >= 50)
               {
                  this.m_btnTimeList[_loc3_].gotoAndStop(2);
               }
               else
               {
                  this.m_btnTimeList[_loc3_].gotoAndStop(3);
               }
            }
            else
            {
               this.m_btnTimeList[_loc3_].gotoAndStop(3);
            }
            _loc3_++;
         }
      }
      
      private function addBtnTouch(param1:MouseEvent) : void
      {
         var _loc4_:String = GamingUI.getInstance().getNewestTimeStrFromSever();
         var _loc3_:String = m_zhengdianData.getStartTime();
         var _loc2_:String = m_zhengdianData.getEndTime();
         if(!((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_loc4_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc4_,_loc2_) > 0)))
         {
            m_svActivityPanel.showWarningBox("不在活动时间内!",0);
            return;
         }
         if(m_isBuying)
         {
            return;
         }
         this.m_nShowIndex = int(String(param1.currentTarget.name).substr(8)) - 1;
         this.refreshReward();
      }
      
      private function addGetBtnTouch(param1:MouseEvent) : void
      {
         var _loc4_:String = GamingUI.getInstance().getNewestTimeStrFromSever();
         var _loc3_:String = m_zhengdianData.getStartTime();
         var _loc2_:String = m_zhengdianData.getEndTime();
         if(!((!Boolean(_loc3_) || new TimeUtil().timeInterval(_loc3_,_loc4_) > 0) && (!Boolean(_loc2_) || new TimeUtil().timeInterval(_loc4_,_loc2_) > 0)))
         {
            m_svActivityPanel.showWarningBox("不在活动时间内!",0);
            return;
         }
         if(MyFunction2.getPlayerBgEmptNum(GamingUI.getInstance().player1) < 1)
         {
            m_svActivityPanel.showWarningBox("背包空间不足,请先清理背包",0);
            return;
         }
         if(m_isBuying)
         {
            return;
         }
         this.m_nClick = int(String(param1.currentTarget.name).substr(7)) - 1;
         if(CurrentTicketPointManager.getInstance().getCurrentTicketPoint() < this.m_zhengdianData.getNPriceIntByIndex(this.m_nShowIndex * 3 + this.m_nClick))
         {
            m_svActivityPanel.showWarningBox("点券不足，请充值",0);
            return;
         }
         m_isBuying = true;
         m_svActivityPanel.showWarningBox("是否花费" + this.m_zhengdianData.getNPriceByIndex(this.m_nShowIndex * 3 + this.m_nClick) + "点券进行抢购计划",1 | 2,{
            "type":"buyZhengdianmiaosha",
            "okFunction":sendGetInfo,
            "cancelFunction":cancelFunction
         });
      }
      
      private function cancelFunction() : void
      {
         m_isBuying = false;
      }
      
      private function sendGetInfo() : void
      {
         GamingUI.getInstance().mouseChildren = false;
         GamingUI.getInstance().mouseEnabled = false;
         m_svActivityPanel.setButtonEnable(false);
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener("complete",onLoaderCompleteHandlerGet);
         _loc1_.addEventListener("ioError",onErrorHandleGet);
         _loc1_.addEventListener("securityError",onErrorHandleGet);
         var _loc2_:URLRequest = new URLRequest(this.m_strGetUrl);
         _loc2_.method = "POST";
         _loc2_.contentType = "application/json";
         var _loc3_:Object = {};
         _loc3_.MD5 = "2B01530154A2C991";
         _loc3_.Itemid = this.m_nIdList[this.m_nClick];
         _loc2_.data = MyJSON.encode(_loc3_);
         _loc1_.load(_loc2_);
      }
      
      protected function onLoaderCompleteHandlerGet(param1:Event) : void
      {
         var result:int;
         var price:uint;
         var ticketId:String;
         var dataObj:Object;
         var data:Object;
         var i:int;
         var e:Event = param1;
         e.currentTarget.removeEventListener("complete",onLoaderCompleteHandlerGet);
         e.currentTarget.removeEventListener("ioError",onErrorHandleGet);
         e.currentTarget.removeEventListener("securityError",onErrorHandleGet);
         try
         {
            result = int(MyJSON.decode(e.currentTarget.data).Result);
         }
         catch(error:Error)
         {
            m_svActivityPanel.showWarningBox(String(e.currentTarget.data),0);
            return;
         }
         switch(result - 1)
         {
            case 0:
               price = uint(this.m_zhengdianData.getPriceByIndex(this.m_nShowIndex * 3 + this.m_nClick));
               ticketId = this.m_zhengdianData.getPriceIdByIndex(this.m_nShowIndex * 3 + this.m_nClick).toString();
               dataObj = {};
               dataObj["propId"] = ticketId;
               dataObj["count"] = 1;
               dataObj["price"] = price;
               dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
               dataObj["tag"] = "整点抢购";
               AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
               {
                  if(param1["propId"] != ticketId)
                  {
                     m_svActivityPanel.showWarningBox("购买物品id前后端不相同！",0);
                     throw new Error("购买物品id前后端不相同！");
                  }
                  m_zhengdianData.setCurrIndex(m_nShowIndex * 3 + m_nClick);
                  m_getEquipmentVOsLogic.getEquipmentVOs(m_zhengdianData,GamingUI.getInstance().player1);
                  var _loc2_:SaveTaskInfo = new SaveTaskInfo();
                  _loc2_.type = "4399";
                  _loc2_.isHaveData = false;
                  SaveTaskList.getInstance().addData(_loc2_);
                  MyFunction2.saveGame2();
                  m_isBuying = false;
               },m_svActivityPanel.showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
         }
         data = MyJSON.decode(e.currentTarget.data);
         i = 0;
         while(i < 3)
         {
            this.m_nNumReamin[i] = int(data.Items[i].Num);
            this.m_nIdList[i] = int(data.Items[i].Id);
            i++;
         }
         this.m_zhengdianData.refreshID(this.m_nIdList);
         this.m_tDate.setTime(int(data.Time) * 1000);
         this.refreshReward();
         this.refreshInfo();
         m_isBuying = false;
         m_svActivityPanel.setButtonEnable(true);
      }
      
      protected function onErrorHandleGet(param1:IOErrorEvent) : void
      {
         param1.currentTarget.removeEventListener("complete",onLoaderCompleteHandlerGet);
         param1.currentTarget.removeEventListener("ioError",onErrorHandleGet);
         param1.currentTarget.removeEventListener("securityError",onErrorHandleGet);
         GamingUI.getInstance().mouseChildren = true;
         GamingUI.getInstance().mouseEnabled = true;
         m_svActivityPanel.setButtonEnable(true);
         m_isBuying = false;
      }
      
      private function refreshShowIndex() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = this.m_tDate.hours;
         _loc2_ = 0;
         while(_loc2_ < 6)
         {
            if(this.m_nTimeArray[_loc2_] <= _loc1_ && this.m_nTimeArray[_loc2_ + 1] > _loc1_)
            {
               this.m_nShowIndex = _loc2_;
               break;
            }
            _loc2_++;
         }
         if(_loc1_ >= this.m_nTimeArray[6])
         {
            m_nShowIndex = 6;
         }
      }
      
      private function refreshCurrTime() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = this.m_tDate.hours;
         _loc1_ = 0;
         while(_loc1_ < 6)
         {
            if(this.m_nTimeArray[_loc1_] <= _loc2_ && this.m_nTimeArray[_loc1_ + 1] > _loc2_)
            {
               this.m_nCurrTime = _loc1_;
               return;
            }
            _loc1_++;
         }
         if(_loc2_ >= this.m_nTimeArray[6])
         {
            m_nCurrTime = 6;
         }
         else
         {
            m_nCurrTime = -1;
         }
      }
      
      private function refreshReward() : void
      {
         var _loc1_:Equipment = null;
         var _loc3_:int = 0;
         ClearUtil.clearObject(m_equipments);
         m_equipments = null;
         m_equipments = new Vector.<Equipment>();
         var _loc2_:int = 0;
         _loc3_ = this.m_nShowIndex * 3;
         while(_loc3_ < this.m_nShowIndex * 3 + 3)
         {
            _loc1_ = MyFunction2.sheatheEquipmentShell(m_zhengdianData.getInfoByIndex(_loc3_));
            _loc1_.addEventListener("rollOver",onOver2,false,0,true);
            _loc1_.addEventListener("rollOut",onOut2,false,0,true);
            m_eqCells[_loc2_].addChild(_loc1_);
            trace(m_zhengdianData.getNameByIndex(_loc3_).toString());
            this.m_lebNameList[_loc2_].text = m_zhengdianData.getNameByIndex(_loc3_).toString();
            this.m_lebOPriceList[_loc2_].text = this.m_zhengdianData.getOPriceByIndex(_loc3_).toString();
            this.m_lebNPriceList[_loc2_].text = this.m_zhengdianData.getNPriceByIndex(_loc3_).toString();
            this.m_lebGetNumList[_loc2_].text = this.m_zhengdianData.getNumByIndex(_loc3_).toString();
            this.m_lebRemainNumList[_loc2_].text = this.getRemainById(this.m_zhengdianData.getIdByIndex(_loc3_)).toString();
            m_equipments.push(_loc1_);
            _loc2_++;
            _loc3_++;
         }
         this.refreshGetBtn();
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      private function getRemainById(param1:int) : int
      {
         var _loc2_:int = 0;
         if(this.m_nCurrTime == this.m_nShowIndex)
         {
            _loc2_ = 0;
            while(_loc2_ < 3)
            {
               if(param1 == this.m_nIdList[_loc2_])
               {
                  return this.m_nNumReamin[_loc2_];
               }
               _loc2_++;
            }
            return 0;
         }
         return 0;
      }
      
      private function refreshGetBtn() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = this.m_tDate.hours;
         var _loc2_:int = 1;
         if(this.m_nShowIndex == 6 && _loc1_ >= this.m_nTimeArray[this.m_nShowIndex])
         {
            _loc2_ = 1;
         }
         else if(_loc1_ >= this.m_nTimeArray[this.m_nShowIndex] && _loc1_ < this.m_nTimeArray[this.m_nShowIndex + 1])
         {
            _loc2_ = 1;
         }
         else
         {
            _loc2_ = 0;
         }
         if(_loc2_ == 1)
         {
            _loc4_ = 0;
            while(_loc4_ < 3)
            {
               if(this.m_nNumReamin[_loc4_] > 0)
               {
                  this.m_btnGetList[_loc4_].gotoAndStop(1);
                  this.m_btnGetList[_loc4_].mouseChildren = true;
                  this.m_btnGetList[_loc4_].mouseEnabled = true;
               }
               else
               {
                  this.m_btnGetList[_loc4_].gotoAndStop(3);
                  this.m_btnGetList[_loc4_].mouseChildren = false;
                  this.m_btnGetList[_loc4_].mouseEnabled = false;
               }
               _loc4_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < 3)
            {
               this.m_btnGetList[_loc3_].gotoAndStop(2);
               this.m_btnGetList[_loc3_].mouseChildren = false;
               this.m_btnGetList[_loc3_].mouseEnabled = false;
               _loc3_++;
            }
         }
      }
      
      private function onResetTicketTextHandler(param1:GameEvent) : void
      {
         this.m_nMoney = int(param1.data);
      }
   }
}

