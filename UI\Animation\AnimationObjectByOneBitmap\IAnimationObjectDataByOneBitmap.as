package UI.Animation.AnimationObjectByOneBitmap
{
   import flash.display.BitmapData;
   
   public interface IAnimationObjectDataByOneBitmap
   {
      
      function get sourceBitmapData() : BitmapData;
      
      function set sourceBitmapData(param1:BitmapData) : void;
      
      function get delay() : Number;
      
      function set delay(param1:Number) : void;
      
      function get moveDistance() : Number;
      
      function set moveDistance(param1:Number) : void;
      
      function get direction() : int;
      
      function set direction(param1:int) : void;
      
      function get width() : Number;
      
      function set width(param1:Number) : void;
      
      function get height() : Number;
      
      function set height(param1:Number) : void;
   }
}

