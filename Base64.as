package
{
   import flash.utils.ByteArray;
   
   public class Base64
   {
      
      private static const base64chars:String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      
      public function Base64()
      {
         super();
      }
      
      public static function encode(param1:String) : String
      {
         var _loc11_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc3_:* = 0;
         var _loc2_:* = 0;
         var _loc4_:* = 0;
         var _loc5_:* = 0;
         if(!param1 || param1 == "")
         {
            return "";
         }
         var _loc10_:int = param1.length;
         var _loc7_:String = "";
         while(_loc11_ < _loc10_)
         {
            _loc6_ = int(param1.charCodeAt(_loc11_++));
            _loc8_ = int(param1.charCodeAt(_loc11_++));
            _loc9_ = int(param1.charCodeAt(_loc11_++));
            _loc5_ = _loc6_ >> 2;
            _loc3_ = (_loc6_ & 3) << 4 | _loc8_ >> 4;
            _loc2_ = (_loc8_ & 0x0F) << 2 | _loc9_ >> 6;
            _loc4_ = _loc9_ & 0x3F;
            if(isNaN(_loc8_))
            {
               _loc2_ = _loc4_ = 64;
            }
            else if(isNaN(_loc9_))
            {
               _loc4_ = 64;
            }
            _loc7_ += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc5_) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc3_);
            _loc7_ = _loc7_ + ("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc2_) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc4_));
         }
         return _loc7_;
      }
      
      public static function encodeByteArray(param1:ByteArray) : String
      {
         var _loc7_:* = 0;
         var _loc3_:int = 0;
         var _loc2_:Array = null;
         if(!param1)
         {
            return "";
         }
         var _loc6_:uint = param1.position;
         var _loc4_:String = "";
         var _loc5_:Array = new Array(4);
         param1.position = 0;
         while(param1.bytesAvailable > 0)
         {
            _loc2_ = [];
            _loc7_ = 0;
            while(_loc7_ < 3 && param1.bytesAvailable > 0)
            {
               _loc2_[_loc7_] = param1.readUnsignedByte();
               _loc7_++;
            }
            _loc5_[0] = (_loc2_[0] & 0xFC) >> 2;
            _loc5_[1] = (_loc2_[0] & 3) << 4 | _loc2_[1] >> 4;
            _loc5_[2] = (_loc2_[1] & 0x0F) << 2 | _loc2_[2] >> 6;
            _loc5_[3] = _loc2_[2] & 0x3F;
            _loc3_ = int(_loc2_.length);
            _loc7_ = _loc3_;
            while(_loc7_ < 3)
            {
               _loc5_[_loc7_ + 1] = 64;
               _loc7_++;
            }
            _loc3_ = int(_loc5_.length);
            _loc7_ = 0;
            while(_loc7_ < _loc3_)
            {
               _loc4_ += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(_loc5_[_loc7_]);
               _loc7_++;
            }
         }
         param1.position = _loc6_;
         return _loc4_;
      }
      
      public static function decode(param1:String) : String
      {
         var _loc8_:* = 0;
         var _loc9_:* = 0;
         var _loc7_:* = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         if(!param1 || param1 == "")
         {
            return "";
         }
         var _loc10_:int = param1.length;
         var _loc6_:String = "";
         while(_loc11_ < _loc10_)
         {
            _loc5_ = int("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc11_++)));
            _loc3_ = int("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc11_++)));
            _loc2_ = int("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc11_++)));
            _loc4_ = int("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc11_++)));
            _loc7_ = _loc5_ << 2 | _loc3_ >> 4;
            _loc8_ = (_loc3_ & 0x0F) << 4 | _loc2_ >> 2;
            _loc9_ = (_loc2_ & 3) << 6 | _loc4_;
            _loc6_ += String.fromCharCode(_loc7_);
            if(_loc2_ != 64)
            {
               _loc6_ += String.fromCharCode(_loc8_);
            }
            if(_loc4_ != 64)
            {
               _loc6_ += String.fromCharCode(_loc9_);
            }
         }
         return _loc6_;
      }
      
      public static function decodeToByteArray(param1:String) : ByteArray
      {
         var _loc8_:* = 0;
         var _loc4_:* = 0;
         var _loc6_:* = 0;
         var _loc5_:ByteArray = new ByteArray();
         var _loc2_:Array = new Array(4);
         var _loc7_:Array = new Array(3);
         var _loc3_:uint = uint(param1.length);
         _loc8_;
         while(_loc8_ < _loc3_)
         {
            _loc4_ = 0;
            while(_loc4_ < 4 && _loc8_ + _loc4_ < _loc3_)
            {
               _loc2_[_loc4_] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(param1.charAt(_loc8_ + _loc4_));
               _loc4_++;
            }
            _loc7_[0] = (_loc2_[0] << 2) + ((_loc2_[1] & 0x30) >> 4);
            _loc7_[1] = ((_loc2_[1] & 0x0F) << 4) + ((_loc2_[2] & 0x3C) >> 2);
            _loc7_[2] = ((_loc2_[2] & 3) << 6) + _loc2_[3];
            _loc6_ = 0;
            while(_loc6_ < 3)
            {
               if(_loc2_[_loc6_ + 1] == 64)
               {
                  break;
               }
               _loc5_.writeByte(_loc7_[_loc6_]);
               _loc6_++;
            }
            _loc8_ += 4;
         }
         _loc5_.position = 0;
         return _loc5_;
      }
   }
}

