package UI.Pets.Bar
{
   import UI.ChangeBar;
   
   public class PetEssenceBar extends ChangeBar
   {
      
      public var redBar:PetRedBar;
      
      public var bloodBarMask:PetEssenceBarMask;
      
      public function PetEssenceBar()
      {
         super();
         _width = redBar.width;
         _heidth = redBar.height;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(redBar)
         {
            redBar.clear();
         }
         redBar = null;
         if(bloodBarMask)
         {
            bloodBarMask.clear();
         }
         bloodBarMask = null;
      }
      
      public function changeLifeBar(param1:Number, param2:int = 0) : void
      {
         changebar(redBar,bloodBarMask,param1,param2);
      }
   }
}

