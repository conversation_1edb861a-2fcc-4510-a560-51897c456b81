package YJFY.Skill.PetSkills
{
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_Pet16Skill extends CuboidAreaAttackSkill2
   {
      
      public const m_const_startEffectFrameLabel:String = "start";
      
      public const m_const_endEffectFrameLabel:String = "end^stop^";
      
      public const m_const_startPlayEffectFrameLabel:String = "playEffect";
      
      public const m_const_attackReachFrameLabel:String = "down";
      
      public const m_const_skillEndFrameLabel:String = "end^stop^";
      
      public const m_const_bodyDefId:String = "petSkillBodyShow";
      
      public const m_const_effectShowDefId:String = "petSkillEffect";
      
      public var m_playEffectShowObj:AnimationShowPlayLogicShell;
      
      public var m_playEffectShowObj_FrameLabelListener:AnimationPlayFrameLabelListener;
      
      public function Skill_Pet16Skill()
      {
         super();
         m_playEffectShowObj = new AnimationShowPlayLogicShell();
         m_playEffectShowObj_FrameLabelListener = new AnimationPlayFrameLabelListener();
         m_playEffectShowObj_FrameLabelListener.reachFrameLabelFun = effectReachFrameLabel;
         m_playEffectShowObj.addFrameLabelListener(m_playEffectShowObj_FrameLabelListener);
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(m_playEffectShowObj);
         m_playEffectShowObj = null;
         ClearUtil.clearObject(m_playEffectShowObj_FrameLabelListener);
         m_playEffectShowObj_FrameLabelListener = null;
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         relearseFun(param1);
         super.releaseSkill2(param1);
      }
      
      public function relearseFun(param1:World) : void
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow("petSkillBodyShow");
         m_owner.currentAnimationGotoAndPlay("1");
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         reachFun(param1);
      }
      
      public function reachFun(param1:String) : void
      {
         switch(param1)
         {
            case "down":
               attackReach(m_world);
               break;
            case "end^stop^":
               endSkill1();
               break;
            case "playEffect":
               playEffect();
         }
      }
      
      private function playEffect() : void
      {
         m_playEffectShowObj.setShow(m_owner.getAnimationByDefId("petSkillEffect"));
         m_playEffectShowObj.getDisplayShow().x = m_world.getCamera().getScreenX();
         m_playEffectShowObj.getDisplayShow().y = m_world.getCamera().getScreenY();
         m_playEffectShowObj.gotoAndPlay("start");
         m_world.addAnimationInFront(m_playEffectShowObj);
      }
      
      public function effectReachFrameLabel(param1:String) : void
      {
         if(param1 == "end^stop^")
         {
            m_world.removeAnimationInFront(m_playEffectShowObj);
         }
      }
   }
}

