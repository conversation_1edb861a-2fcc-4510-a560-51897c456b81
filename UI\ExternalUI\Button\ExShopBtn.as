package UI.ExternalUI.Button
{
   import UI.Button.Btn;
   import UI.Event.UIBtnEvent;
   import UI.ExternalUI.Button.Other.BurstBox;
   import UI.ExternalUI.Button.Other.NormalBox;
   import UI.ExternalUI.Button.Other.WriggleBox;
   import UI.MyFunction;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class ExShopBtn extends Btn
   {
      
      private var _currentState:int = 0;
      
      private var _burstBox:BurstBox;
      
      private var _wriggleBox:WriggleBox;
      
      private var _normalBox:NormalBox;
      
      private var _isLock:Boolean;
      
      public function ExShopBtn()
      {
         super();
         setTipString("商城");
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_burstBox)
         {
            _burstBox.clear();
         }
         if(_wriggleBox)
         {
            _wriggleBox.clear();
         }
         if(_normalBox)
         {
            _normalBox.clear();
         }
         _burstBox = null;
         _wriggleBox = null;
         _normalBox = null;
      }
      
      public function set isLock(param1:Boolean) : void
      {
         if(_isLock != param1)
         {
            if(param1)
            {
               if(getChildByName(_wriggleBox.name))
               {
                  removeChild(_wriggleBox);
               }
               if(!_normalBox)
               {
                  _normalBox = new NormalBox();
               }
               addChildAt(_normalBox,numChildren);
               mouseChildren = false;
               mouseEnabled = false;
               MyFunction.getInstance().changeSaturation(this,-100);
            }
            else
            {
               if(getChildByName(_normalBox.name))
               {
                  removeChild(_normalBox);
               }
               if(!_wriggleBox)
               {
                  _wriggleBox = new WriggleBox();
               }
               addChildAt(_wriggleBox,numChildren);
               mouseChildren = true;
               mouseEnabled = true;
               MyFunction.getInstance().changeSaturation(this,0);
            }
         }
         _isLock = param1;
      }
      
      public function get isLock() : Boolean
      {
         return _isLock;
      }
      
      private function init() : void
      {
         _isLock = false;
         _wriggleBox = new WriggleBox();
         addChild(_wriggleBox);
         buttonMode = false;
      }
      
      override protected function addToStage(param1:Event) : void
      {
         addEventListener("click",clickBtn2,false,0,true);
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("click",clickBtn,false,0,true);
         addEventListener("rollOver",onOver,false,0,true);
         addEventListener("rollOut",onOut,false,0,true);
         addEventListener("mouseDown",onClick,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("click",clickBtn2,false);
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("click",clickBtn,false);
         removeEventListener("rollOver",onOver,false);
         addEventListener("rollOut",onOut,false);
         addEventListener("mouseDown",onClick,true);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         if(!_currentState)
         {
            if(getChildByName(_wriggleBox.name))
            {
               removeChild(_wriggleBox);
            }
            if(!_burstBox)
            {
               _burstBox = new BurstBox();
            }
            if(!getChildByName(_burstBox.name))
            {
               _burstBox.gotoAndPlay(1);
               addChildAt(_burstBox,numChildren);
            }
         }
         if(m_smallToolTip)
         {
            if(stage)
            {
               stage.addChild(m_smallToolTip);
               if(stage.mouseX + 10 + m_smallToolTip.width > stage.stageWidth)
               {
                  m_smallToolTip.x = stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = stage.mouseX + 10;
               }
               if(stage.mouseY + 10 + m_smallToolTip.height > stage.stageHeight)
               {
                  m_smallToolTip.y = stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = stage.mouseY + 10;
               }
            }
         }
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         _currentState = 1;
         if(Boolean(_wriggleBox) && getChildByName(_wriggleBox.name))
         {
            removeChild(_wriggleBox);
         }
         if(Boolean(_burstBox) && getChildByName(_burstBox.name))
         {
            removeChild(_burstBox);
         }
         if(!_normalBox)
         {
            _normalBox = new NormalBox();
         }
         if(!getChildByName(_normalBox.name))
         {
            addChildAt(_normalBox,numChildren);
         }
         dispatchEvent(new MouseEvent("click"));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(!_currentState)
         {
            if(getChildByName(_burstBox.name))
            {
               removeChild(_burstBox);
            }
            if(!getChildByName(_wriggleBox.name))
            {
               _wriggleBox.gotoAndPlay(1);
               addChildAt(_wriggleBox,numChildren);
            }
         }
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("enterIntoShop"));
      }
   }
}

