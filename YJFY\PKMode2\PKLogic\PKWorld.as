package YJFY.PKMode2.PKLogic
{
   import UI.EnterFrameTime;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Pets.Pet;
   import UI.Players.Player;
   import UI.Players.PlayerVO;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.PlayerSkills.PlayerActiveSkillVO;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimalEntity_AbleToSuper;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.GameEntity.GameEntity;
   import YJFY.GameEntity.GameEntityListener;
   import YJFY.GameEntity.IAbleInvincibleGameEntity;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dog;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Dragon;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Fox;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Monkey;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.Rabbit;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.GameWorldLogic.GameKeyControlLogic_Xydzjs;
   import YJFY.GameWorldLogic.IHavePlayersWorld;
   import YJFY.GameWorldLogic.WorldCameraPosition;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.MapBase;
   import YJFY.Other.AttackDataManager;
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PKUI.PKingInforPanel;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ChangeBarLogicShell.DoubleShowChangeBarLogicShell;
   import YJFY.Skill.ISkill;
   import YJFY.Skill.IStunSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import YJFY.XydzjsData.IAttackDataCalculateVOXydzjs;
   import YJFY.XydzjsLogic.GetOwnerOfSubEntity;
   import YJFY.XydzjsLogic.ValueAnimationsManagerXydzjs;
   import YJFY.XydzjsLogic.XydzjsAttackDataCaculate;
   import flash.display.MovieClip;
   import flash.geom.Point;
   
   public class PKWorld extends MapBase implements IHavePlayersWorld
   {
      
      private var m_myPlayer1InitPosition_onlyOne:Coordinate;
      
      private var m_foePlayer1InitPosition_onlyOne:Coordinate;
      
      private var m_myPlayer1Xydzjs:PlayerXydzjs;
      
      private var m_myPlayer2Xydzjs:PlayerXydzjs;
      
      private var m_myPlayer1InitPosition:Coordinate;
      
      private var m_myPlayer2InitPosition:Coordinate;
      
      private var m_myPlayer1HpBarShow:MovieClip;
      
      private var m_myPlayer2HpBarShow:MovieClip;
      
      private var m_myPlayer1HpBarLG:DoubleShowChangeBarLogicShell;
      
      private var m_myPlayer2HpBarLG:DoubleShowChangeBarLogicShell;
      
      private var m_foePlayer1Xydzjs:PlayerXydzjs;
      
      private var m_foePlayer2Xydzjs:PlayerXydzjs;
      
      private var m_foePlayerAIEntity1:PlayerAIEntity;
      
      private var m_foePlayerAIEntity2:PlayerAIEntity;
      
      private var m_foePlayer1InitPosition:Coordinate;
      
      private var m_foePlayer2InitPosition:Coordinate;
      
      private var m_foePlayer1HpBarShow:MovieClip;
      
      private var m_foePlayer2HpBarShow:MovieClip;
      
      private var m_foePlayer1HpBarLG:DoubleShowChangeBarLogicShell;
      
      private var m_foePlayer2HpBarLG:DoubleShowChangeBarLogicShell;
      
      private var m_isTwoMode:Boolean;
      
      private var m_worldCameraPositionTransfer:WorldCameraPosition;
      
      private var m_gameKeyControlLogic:GameKeyControlLogic_Xydzjs;
      
      private var m_gameEntityListener:GameEntityListener;
      
      private var m_getOwnerOfSubsidinaryEntity:GetOwnerOfSubEntity;
      
      private var m_xydzjsAttackDataCalculate:XydzjsAttackDataCaculate;
      
      private var m_attackDataManager:AttackDataManager;
      
      private var m_valueAnimationsManager:ValueAnimationsManagerXydzjs;
      
      private var m_hurtChangeColorManager:HurtChangeColorManager;
      
      private var m_pkingInforPanel:PKingInforPanel;
      
      private var m_isReady:Boolean;
      
      private var m_isPKing:Boolean;
      
      private var m_isMyWin:Boolean;
      
      private var m_uiLayer2:MySprite;
      
      private var m_myUiPlayer1:UI.Players.Player;
      
      private var m_myUiPlayer2:UI.Players.Player;
      
      private var m_foeUiPlayer1:UI.Players.Player;
      
      private var m_foeUiPlayer2:UI.Players.Player;
      
      private var m_pk:PK2;
      
      private var m_thisload:YJFYLoader;
      
      public function PKWorld()
      {
         super();
         mouseChildren = true;
         mouseEnabled = true;
         m_thisload = new YJFYLoader();
         m_thisload.setVersionControl(GamingUI.getInstance().getVersionControl());
         m_myPlayer1InitPosition_onlyOne = new Coordinate(200,250,0);
         m_foePlayer1InitPosition_onlyOne = new Coordinate(760,250,0);
         m_myPlayer1InitPosition = new Coordinate(200,100,0);
         m_myPlayer2InitPosition = new Coordinate(200,400,0);
         m_foePlayer1InitPosition = new Coordinate(760,100,0);
         m_foePlayer2InitPosition = new Coordinate(760,400,0);
         m_worldCameraPositionTransfer = new WorldCameraPosition();
         m_gameKeyControlLogic = new GameKeyControlLogic_Xydzjs();
         m_gameKeyControlLogic.lockKeyAction();
         m_gameKeyControlLogic.setIsAbleUsePotion(false);
         m_gameEntityListener = new GameEntityListener();
         m_gameEntityListener.attackSuccessFun = gameEntityAttackSuccess;
         m_gameEntityListener.beAttackFun = gameEntityBeAttack;
         m_gameEntityListener.preJudgeIsFoeFun = gameEntityPreJudgeIsFoe;
         m_getOwnerOfSubsidinaryEntity = new GetOwnerOfSubEntity();
         m_xydzjsAttackDataCalculate = new XydzjsAttackDataCaculate();
         m_attackDataManager = new AttackDataManager();
         m_valueAnimationsManager = new ValueAnimationsManagerXydzjs();
         m_hurtChangeColorManager = new HurtChangeColorManager();
      }
      
      override public function clear() : void
      {
         recoverPlayersBlood();
         ClearUtil.clearObject(m_myPlayer1InitPosition_onlyOne);
         m_myPlayer1InitPosition_onlyOne = null;
         ClearUtil.clearObject(m_foePlayer1InitPosition_onlyOne);
         m_foePlayer1InitPosition_onlyOne = null;
         ClearUtil.clearObject(m_myPlayer1Xydzjs);
         m_myPlayer1Xydzjs = null;
         ClearUtil.clearObject(m_myPlayer2Xydzjs);
         m_myPlayer2Xydzjs = null;
         ClearUtil.clearObject(m_myPlayer1InitPosition);
         m_myPlayer1InitPosition = null;
         ClearUtil.clearObject(m_myPlayer2InitPosition);
         m_myPlayer2InitPosition = null;
         ClearUtil.clearObject(m_myPlayer1HpBarShow);
         m_myPlayer1HpBarShow = null;
         ClearUtil.clearObject(m_myPlayer2HpBarShow);
         m_myPlayer2HpBarShow = null;
         ClearUtil.clearObject(m_myPlayer1HpBarLG);
         m_myPlayer1HpBarLG = null;
         ClearUtil.clearObject(m_myPlayer2HpBarLG);
         m_myPlayer2HpBarLG = null;
         ClearUtil.clearObject(m_foePlayer1Xydzjs);
         m_foePlayer1Xydzjs = null;
         ClearUtil.clearObject(m_foePlayer2Xydzjs);
         m_foePlayer2Xydzjs = null;
         ClearUtil.clearObject(m_foePlayerAIEntity1);
         m_foePlayerAIEntity1 = null;
         ClearUtil.clearObject(m_foePlayerAIEntity2);
         m_foePlayerAIEntity2 = null;
         ClearUtil.clearObject(m_foePlayer1InitPosition);
         m_foePlayer1InitPosition = null;
         ClearUtil.clearObject(m_foePlayer2InitPosition);
         m_foePlayer2InitPosition = null;
         ClearUtil.clearObject(m_foePlayer1HpBarShow);
         m_foePlayer1HpBarShow = null;
         ClearUtil.clearObject(m_foePlayer2HpBarShow);
         m_foePlayer2HpBarShow = null;
         ClearUtil.clearObject(m_foePlayer1HpBarLG);
         m_foePlayer1HpBarLG = null;
         ClearUtil.clearObject(m_foePlayer2HpBarLG);
         m_foePlayer2HpBarLG = null;
         ClearUtil.clearObject(m_worldCameraPositionTransfer);
         m_worldCameraPositionTransfer = null;
         ClearUtil.clearObject(m_gameKeyControlLogic);
         m_gameKeyControlLogic = null;
         ClearUtil.clearObject(m_thisload);
         m_thisload = null;
         ClearUtil.clearObject(m_gameEntityListener);
         m_gameEntityListener = null;
         ClearUtil.clearObject(m_getOwnerOfSubsidinaryEntity);
         m_getOwnerOfSubsidinaryEntity = null;
         ClearUtil.clearObject(m_xydzjsAttackDataCalculate);
         m_xydzjsAttackDataCalculate = null;
         ClearUtil.clearObject(m_attackDataManager);
         m_attackDataManager = null;
         ClearUtil.clearObject(m_valueAnimationsManager);
         m_valueAnimationsManager = null;
         ClearUtil.clearObject(m_hurtChangeColorManager);
         m_hurtChangeColorManager = null;
         ClearUtil.clearObject(m_pkingInforPanel);
         m_pkingInforPanel = null;
         ClearUtil.clearObject(m_uiLayer2);
         m_uiLayer2 = null;
         m_myUiPlayer1 = null;
         m_myUiPlayer2 = null;
         m_foeUiPlayer1 = null;
         m_foeUiPlayer2 = null;
         m_pk = null;
         super.clear();
      }
      
      public function setPK(param1:PK2) : void
      {
         m_pk = param1;
      }
      
      override public function init() : void
      {
         super.init();
         m_worldCameraPositionTransfer.init(this);
         m_gameKeyControlLogic.init(m_keyManager,this);
         Part1.getInstance().showGameWaitShow();
         m_world.stopWorldTime();
         m_hurtChangeColorManager.init(m_enterFrameTime);
         m_uiLayer2 = new MySprite();
         addChildAt(m_uiLayer2,getChildIndex(m_uiLayer));
      }
      
      public function setIsTwoMode(param1:Boolean) : void
      {
         m_isTwoMode = param1;
      }
      
      public function initUiPlayerData(param1:UI.Players.Player, param2:UI.Players.Player, param3:UI.Players.Player, param4:UI.Players.Player) : void
      {
         if(m_myUiPlayer1)
         {
            throw new Error("不能重复初始化");
         }
         m_myUiPlayer1 = param1;
         m_myUiPlayer2 = param2;
         m_foeUiPlayer1 = param3;
         m_foeUiPlayer2 = param4;
         upPlayersBlood();
         fullPlayersMagic();
         fullPetEssension();
         zeroPlayersAndPetsCDs();
         if(!(Boolean(m_myUiPlayer2) && m_isTwoMode))
         {
            m_myPlayer1InitPosition.copy(m_myPlayer1InitPosition_onlyOne);
         }
         m_foePlayerAIEntity1 = new PlayerAIEntity();
         if(Boolean(m_foeUiPlayer2) && m_isTwoMode)
         {
            m_foePlayerAIEntity2 = new PlayerAIEntity();
         }
         else
         {
            m_foePlayer1InitPosition.copy(m_foePlayer1InitPosition_onlyOne);
         }
      }
      
      private function fullPlayersMagic() : void
      {
         if(m_myUiPlayer1)
         {
            m_myUiPlayer1.playerVO.magicPercent = 1;
         }
         if(m_isTwoMode && m_myUiPlayer2)
         {
            m_myUiPlayer2.playerVO.magicPercent = 1;
         }
         if(m_foeUiPlayer1)
         {
            m_foeUiPlayer1.playerVO.magicPercent = 1;
         }
         if(m_isTwoMode && m_foeUiPlayer2)
         {
            m_foeUiPlayer2.playerVO.magicPercent = 1;
         }
      }
      
      private function fullPetEssension() : void
      {
         if(Boolean(m_myUiPlayer1) && Boolean(m_myUiPlayer1.playerVO.pet) && m_myUiPlayer1.playerVO.pet.petEquipmentVO)
         {
            m_myUiPlayer1.playerVO.pet.petEquipmentVO.essentialPercent = 1;
         }
         if(m_isTwoMode && Boolean(m_myUiPlayer2) && Boolean(m_myUiPlayer2.playerVO.pet) && m_myUiPlayer2.playerVO.pet.petEquipmentVO)
         {
            m_myUiPlayer2.playerVO.pet.petEquipmentVO.essentialPercent = 1;
         }
         if(Boolean(m_foeUiPlayer1) && Boolean(m_foeUiPlayer1.playerVO.pet) && m_foeUiPlayer1.playerVO.pet.petEquipmentVO)
         {
            m_foeUiPlayer1.playerVO.pet.petEquipmentVO.essentialPercent = 1;
         }
         if(m_isTwoMode && Boolean(m_foeUiPlayer2) && Boolean(m_foeUiPlayer2.playerVO.pet) && m_foeUiPlayer2.playerVO.pet.petEquipmentVO)
         {
            m_foeUiPlayer2.playerVO.pet.petEquipmentVO.essentialPercent = 1;
         }
      }
      
      private function zeroPlayersAndPetsCDs() : void
      {
         if(m_myUiPlayer1)
         {
            zeroPlayerCD(m_myUiPlayer1.playerVO);
            zeroPetCD(m_myUiPlayer1.playerVO.pet);
         }
         if(m_myUiPlayer2)
         {
            zeroPlayerCD(m_myUiPlayer2.playerVO);
            zeroPetCD(m_myUiPlayer2.playerVO.pet);
         }
         if(m_foeUiPlayer1)
         {
            zeroPlayerCD(m_foeUiPlayer1.playerVO);
            zeroPetCD(m_foeUiPlayer1.playerVO.pet);
         }
         if(m_foeUiPlayer2)
         {
            zeroPlayerCD(m_foeUiPlayer2.playerVO);
            zeroPetCD(m_foeUiPlayer2.playerVO.pet);
         }
      }
      
      private function zeroPlayerCD(param1:PlayerVO) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = int(param1.skillVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.skillVOs[_loc3_] is PlayerActiveSkillVO)
            {
               (param1.skillVOs[_loc3_] as PlayerActiveSkillVO).currentCDTime = 0;
            }
            _loc3_++;
         }
      }
      
      private function zeroPetCD(param1:Pet) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(param1.petEquipmentVO == null)
         {
            return;
         }
         if(param1.petEquipmentVO.activeSkillVO is PetActiveSkillVO)
         {
            (param1.petEquipmentVO.activeSkillVO as PetActiveSkillVO).currentCDTime = 0;
         }
      }
      
      private function upPlayersBlood() : void
      {
         if(m_myUiPlayer1)
         {
            m_myUiPlayer1.playerVO.set("blood_PKUp",String(m_myUiPlayer1.playerVO.bloodVolume * 9));
            m_myUiPlayer1.playerVO.bloodPercent = 1;
            if(m_myUiPlayer1.playerVO.automaticPetVO)
            {
               m_myUiPlayer1.playerVO.automaticPetVO.set2("blood_PKUp",m_myUiPlayer1.playerVO.automaticPetVO.getTotalHp() * 9);
               m_myUiPlayer1.playerVO.automaticPetVO.addHp(m_myUiPlayer1.playerVO.automaticPetVO.getTotalHp());
            }
         }
         if(m_myUiPlayer2)
         {
            m_myUiPlayer2.playerVO.set("blood_PKUp",String(m_myUiPlayer2.playerVO.bloodVolume * 9));
            m_myUiPlayer2.playerVO.bloodPercent = 1;
            if(m_myUiPlayer2.playerVO.automaticPetVO)
            {
               m_myUiPlayer2.playerVO.automaticPetVO.set2("blood_PKUp",m_myUiPlayer2.playerVO.automaticPetVO.getTotalHp() * 9);
               m_myUiPlayer2.playerVO.automaticPetVO.addHp(m_myUiPlayer2.playerVO.automaticPetVO.getTotalHp());
            }
         }
         if(m_foeUiPlayer1)
         {
            m_foeUiPlayer1.playerVO.set("blood_PKUp",String(m_foeUiPlayer1.playerVO.bloodVolume * 9));
            m_foeUiPlayer1.playerVO.bloodPercent = 1;
            if(m_foeUiPlayer1.playerVO.automaticPetVO)
            {
               m_foeUiPlayer1.playerVO.automaticPetVO.set2("blood_PKUp",m_foeUiPlayer1.playerVO.automaticPetVO.getTotalHp() * 9);
               m_foeUiPlayer1.playerVO.automaticPetVO.addHp(m_foeUiPlayer1.playerVO.automaticPetVO.getTotalHp());
            }
         }
         if(m_foeUiPlayer2)
         {
            m_foeUiPlayer2.playerVO.set("blood_PKUp",String(m_foeUiPlayer2.playerVO.bloodVolume * 9));
            m_foeUiPlayer2.playerVO.bloodPercent = 1;
            if(m_foeUiPlayer2.playerVO.automaticPetVO)
            {
               m_foeUiPlayer2.playerVO.automaticPetVO.set2("blood_PKUp",m_foeUiPlayer2.playerVO.automaticPetVO.getTotalHp() * 9);
               m_foeUiPlayer2.playerVO.automaticPetVO.addHp(m_foeUiPlayer2.playerVO.automaticPetVO.getTotalHp());
            }
         }
      }
      
      private function recoverPlayersBlood() : void
      {
         if(m_myUiPlayer1)
         {
            m_myUiPlayer1.playerVO.set("blood_PKUp","0");
            if(m_myUiPlayer1.playerVO.automaticPetVO)
            {
               m_myUiPlayer1.playerVO.automaticPetVO.set2("blood_PKUp",0);
            }
         }
         if(m_myUiPlayer2)
         {
            m_myUiPlayer2.playerVO.set("blood_PKUp","0");
            if(m_myUiPlayer2.playerVO.automaticPetVO)
            {
               m_myUiPlayer2.playerVO.automaticPetVO.set2("blood_PKUp",0);
            }
         }
         if(m_foeUiPlayer1)
         {
            m_foeUiPlayer1.playerVO.set("blood_PKUp","0");
            if(m_foeUiPlayer1.playerVO.automaticPetVO)
            {
               m_foeUiPlayer1.playerVO.automaticPetVO.set2("blood_PKUp",0);
            }
         }
         if(m_foeUiPlayer2)
         {
            m_foeUiPlayer2.playerVO.set("blood_PKUp","0");
            if(m_foeUiPlayer2.playerVO.automaticPetVO)
            {
               m_foeUiPlayer2.playerVO.automaticPetVO.set2("blood_PKUp",0);
            }
         }
      }
      
      override public function render(param1:EnterFrameTime) : void
      {
         super.render(param1);
         if(m_isPKing)
         {
            if(m_myPlayer1Xydzjs)
            {
               m_myPlayer1Xydzjs.render();
            }
            if(m_myPlayer2Xydzjs)
            {
               m_myPlayer2Xydzjs.render();
            }
            if(m_foePlayer1Xydzjs)
            {
               m_foePlayer1Xydzjs.render();
            }
            if(m_foePlayer2Xydzjs)
            {
               m_foePlayer2Xydzjs.render();
            }
            if(m_foePlayerAIEntity1)
            {
               m_foePlayerAIEntity1.render();
            }
            if(m_foePlayerAIEntity2)
            {
               m_foePlayerAIEntity2.render();
            }
         }
         m_worldCameraPositionTransfer.render(param1);
         m_valueAnimationsManager.render();
         if(m_isReady == false)
         {
            m_isReady = isAllReady();
            if(m_isReady)
            {
               startPK();
            }
         }
         if(m_myUiPlayer1)
         {
            m_myUiPlayer1.render(param1);
         }
         if(m_myUiPlayer2)
         {
            m_myUiPlayer2.render(param1);
         }
         if(m_foeUiPlayer1)
         {
            m_foeUiPlayer1.render(param1);
         }
         if(m_foeUiPlayer2)
         {
            m_foeUiPlayer2.render(param1);
         }
         m_hurtChangeColorManager.render(param1);
         if(m_pkingInforPanel)
         {
            m_pkingInforPanel.render(param1);
         }
         renderHpBar();
         judgeIsEnd();
      }
      
      private function startPK() : void
      {
         Part1.getInstance().hideGameWaitShow();
         m_world.continueWorldTime();
         if(m_pkingInforPanel == null)
         {
            m_pkingInforPanel = new PKingInforPanel();
            m_pkingInforPanel.setIsShowAutomaticPetPanel(false);
            m_pkingInforPanel.init(m_thisload,m_pk,this);
         }
         m_pkingInforPanel.setPlayers(m_myUiPlayer1,m_myUiPlayer2);
         m_uiLayer.addChild(m_pkingInforPanel);
         m_pkingInforPanel.playStartAnimation(startPK2);
      }
      
      private function startPK2() : void
      {
         m_gameKeyControlLogic.unLockKeyAction();
         m_isPKing = true;
      }
      
      private function isAllReady() : Boolean
      {
         if(m_myPlayer1Xydzjs == null)
         {
            return false;
         }
         if(m_myPlayer1Xydzjs.getAnimalEntity() == null)
         {
            return false;
         }
         if(m_myPlayer1Xydzjs.getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(Boolean(m_myPlayer1Xydzjs.getPet()) && m_myPlayer1Xydzjs.getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_myPlayer1Xydzjs.getPet()) && m_myPlayer1Xydzjs.getPet().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(m_isTwoMode)
         {
            if(m_myUiPlayer2)
            {
               if(m_myPlayer2Xydzjs == null)
               {
                  return false;
               }
               if(m_myPlayer2Xydzjs.getAnimalEntity() == null)
               {
                  return false;
               }
               if(m_myPlayer2Xydzjs.getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
               if(Boolean(m_myPlayer2Xydzjs.getPet()) && m_myPlayer2Xydzjs.getPet().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_myPlayer2Xydzjs.getPet()) && m_myPlayer2Xydzjs.getPet().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
            }
         }
         if(m_foePlayer1Xydzjs == null)
         {
            return false;
         }
         if(m_foePlayer1Xydzjs.getAnimalEntity() == null)
         {
            return false;
         }
         if(m_foePlayer1Xydzjs.getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(Boolean(m_foePlayer1Xydzjs.getPet()) && m_foePlayer1Xydzjs.getPet().getAnimalEntity() == null)
         {
            return false;
         }
         if(Boolean(m_foePlayer1Xydzjs.getPet()) && m_foePlayer1Xydzjs.getPet().getAnimalEntity().getIsReady() == false)
         {
            return false;
         }
         if(m_isTwoMode)
         {
            if(m_foeUiPlayer2)
            {
               if(m_foePlayer2Xydzjs == null)
               {
                  return false;
               }
               if(m_foePlayer2Xydzjs.getAnimalEntity() == null)
               {
                  return false;
               }
               if(m_foePlayer2Xydzjs.getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
               if(Boolean(m_foePlayer2Xydzjs.getPet()) && m_foePlayer2Xydzjs.getPet().getAnimalEntity() == null)
               {
                  return false;
               }
               if(Boolean(m_foePlayer2Xydzjs.getPet()) && m_foePlayer2Xydzjs.getPet().getAnimalEntity().getIsReady() == false)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      private function addGameEntityListener() : void
      {
         m_myPlayer1Xydzjs.removeGameEntityListener(m_gameEntityListener);
         if(m_myPlayer1Xydzjs.getPet())
         {
            m_myPlayer1Xydzjs.getPet().removeGameEntityListener(m_gameEntityListener);
         }
         if(m_myPlayer1Xydzjs.getAutoAttackPet())
         {
            m_myPlayer1Xydzjs.getAutoAttackPet().removeGameEntityListener(m_gameEntityListener);
         }
         m_foePlayer1Xydzjs.removeGameEntityListener(m_gameEntityListener);
         if(m_foePlayer1Xydzjs.getPet())
         {
            m_foePlayer1Xydzjs.getPet().removeGameEntityListener(m_gameEntityListener);
         }
         if(m_foePlayer1Xydzjs.getAutoAttackPet())
         {
            m_foePlayer1Xydzjs.getAutoAttackPet().removeGameEntityListener(m_gameEntityListener);
         }
         if(m_isTwoMode)
         {
            if(m_myPlayer2Xydzjs)
            {
               m_myPlayer2Xydzjs.removeGameEntityListener(m_gameEntityListener);
               if(m_myPlayer2Xydzjs.getPet())
               {
                  m_myPlayer2Xydzjs.getPet().removeGameEntityListener(m_gameEntityListener);
               }
               if(m_myPlayer2Xydzjs.getAutoAttackPet())
               {
                  m_myPlayer2Xydzjs.getAutoAttackPet().removeGameEntityListener(m_gameEntityListener);
               }
            }
            if(m_foePlayer2Xydzjs)
            {
               m_foePlayer2Xydzjs.removeGameEntityListener(m_gameEntityListener);
               if(m_foePlayer2Xydzjs.getPet())
               {
                  m_foePlayer2Xydzjs.getPet().removeGameEntityListener(m_gameEntityListener);
               }
               if(m_foePlayer2Xydzjs.getAutoAttackPet())
               {
                  m_foePlayer2Xydzjs.getAutoAttackPet().removeGameEntityListener(m_gameEntityListener);
               }
            }
         }
         m_myPlayer1Xydzjs.addGameEntityListener(m_gameEntityListener);
         if(m_myPlayer1Xydzjs.getPet())
         {
            m_myPlayer1Xydzjs.getPet().addGameEntityListener(m_gameEntityListener);
         }
         if(m_myPlayer1Xydzjs.getAutoAttackPet())
         {
            m_myPlayer1Xydzjs.getAutoAttackPet().addGameEntityListener(m_gameEntityListener);
         }
         m_foePlayer1Xydzjs.addGameEntityListener(m_gameEntityListener);
         if(m_foePlayer1Xydzjs.getPet())
         {
            m_foePlayer1Xydzjs.getPet().addGameEntityListener(m_gameEntityListener);
         }
         if(m_foePlayer1Xydzjs.getAutoAttackPet())
         {
            m_foePlayer1Xydzjs.getAutoAttackPet().addGameEntityListener(m_gameEntityListener);
         }
         if(m_isTwoMode)
         {
            if(m_myPlayer2Xydzjs)
            {
               m_myPlayer2Xydzjs.addGameEntityListener(m_gameEntityListener);
               if(m_myPlayer2Xydzjs.getPet())
               {
                  m_myPlayer2Xydzjs.getPet().addGameEntityListener(m_gameEntityListener);
               }
               if(m_myPlayer2Xydzjs.getAutoAttackPet())
               {
                  m_myPlayer2Xydzjs.getAutoAttackPet().addGameEntityListener(m_gameEntityListener);
               }
            }
            if(m_foePlayer2Xydzjs)
            {
               m_foePlayer2Xydzjs.addGameEntityListener(m_gameEntityListener);
               if(m_foePlayer2Xydzjs.getPet())
               {
                  m_foePlayer2Xydzjs.getPet().addGameEntityListener(m_gameEntityListener);
               }
               if(m_foePlayer2Xydzjs.getAutoAttackPet())
               {
                  m_foePlayer2Xydzjs.getAutoAttackPet().addGameEntityListener(m_gameEntityListener);
               }
            }
         }
      }
      
      private function gameEntityAttackSuccess(param1:GameEntity, param2:IEntity, param3:ISkill, param4:IEntity) : void
      {
         var _loc5_:AttackData = null;
         var _loc7_:* = m_getOwnerOfSubsidinaryEntity.getOwnerOfSubAttackEntity(param4);
         if(_loc7_ == null)
         {
            _loc7_ = param4;
         }
         var _loc6_:IAttackDataCalculateVOXydzjs = param4.getExtra() is IAttackDataCalculateVOXydzjs ? param4.getExtra() as IAttackDataCalculateVOXydzjs : _loc7_.getExtra() as IAttackDataCalculateVOXydzjs;
         if(isFoeForAttackTarget(param2,_loc7_) && (!(param2.getExtra() is IAbleInvincibleGameEntity) || param2.getExtra() is PlayerXydzjs && (param2.getExtra() as PlayerXydzjs).getIsInvincible2() == false || (param2.getExtra() as IAbleInvincibleGameEntity).getIsInvincible() == false))
         {
            _loc5_ = m_attackDataManager.getOneAttackData();
            m_xydzjsAttackDataCalculate.caculateAttackData(_loc5_,_loc6_,param2.getExtra() as IAttackDataCalculateVOXydzjs,param3);
            (_loc6_ as GameEntity).getAnimalEntity().setAttackData(_loc5_);
            if(_loc7_.getExtra() is PlayerXydzjs && (_loc7_ as AnimalEntity_AbleToSuper).getIsSuper() == false)
            {
               (_loc7_.getExtra() as PlayerXydzjs).getUiPlayer().addNuQiValue();
            }
            if(_loc5_.getHurt())
            {
               m_valueAnimationsManager.addValueAnimationToTarget(param2,_loc5_);
            }
            m_attackDataManager.wasteOneAttackData(_loc5_);
         }
         else
         {
            (_loc6_ as GameEntity).getAnimalEntity().setAttackData(_loc5_);
         }
      }
      
      private function gameEntityBeAttack(param1:GameEntity, param2:IEntity, param3:AttackData, param4:ISkill, param5:IEntity) : void
      {
         var _loc6_:IAttackDataCalculateVOXydzjs = param5.getExtra() as IAttackDataCalculateVOXydzjs;
         _loc6_.decCurrentHp(param3.getHurt());
         if(!(param4 is IStunSkill))
         {
            param3.resetData(param3.getHurt(),param3.getIsCritical(),param3.getIsBeDodge(),0);
         }
         if(param3.getHurt())
         {
            m_hurtChangeColorManager.changeColor((param5 as AnimalEntity).getBodyShowContainer());
         }
         updateHpBarDataShow();
         if(_loc6_.getCurrentHp() <= 0)
         {
            (param5 as IAnimalEntity).die();
         }
         if((param5 as IAnimalEntity).isInSkill())
         {
            (param5 as IAnimalEntity).setIsAbleToHurtWhenBeAttacked(false);
         }
         else
         {
            (param5 as IAnimalEntity).setIsAbleToHurtWhenBeAttacked(true);
         }
      }
      
      private function gameEntityPreJudgeIsFoe(param1:GameEntity, param2:IEntity) : void
      {
         param1.setIsFoe(isFoeForAttackTarget(param2,param1.getAnimalEntity()));
      }
      
      private function isFoeForAttackTarget(param1:IEntity, param2:IEntity) : Boolean
      {
         var _loc3_:Boolean = false;
         var _loc4_:Boolean = false;
         if(param2 == null)
         {
            throw new Error("出错了");
         }
         if(param1 == null)
         {
            throw new Error("出错了");
         }
         if(param1.getExtra() == null)
         {
            return false;
         }
         if(param2.getExtra() == null)
         {
            return false;
         }
         if(param1.getExtra() is PetXydzjs)
         {
            return false;
         }
         if(entityIsPKEntity(param1) == false)
         {
            return false;
         }
         if(entityIsPKEntity(param2) == false)
         {
            return false;
         }
         if(param2.getExtra() == m_myPlayer1Xydzjs || param2.getExtra() == m_myPlayer1Xydzjs.getPet() || param2.getExtra() == m_myPlayer1Xydzjs.getAutoAttackPet() || param2.getExtra() == m_myPlayer2Xydzjs || param2.getExtra() == (m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getPet() : null) || param2.getExtra() == (m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getAutoAttackPet() : null))
         {
            _loc3_ = true;
         }
         if(param1.getExtra() == m_myPlayer1Xydzjs || param1.getExtra() == m_myPlayer1Xydzjs.getPet() || param1.getExtra() == m_myPlayer1Xydzjs.getAutoAttackPet() || param1.getExtra() == m_myPlayer2Xydzjs || param1.getExtra() == (m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getPet() : null) || param1.getExtra() == (m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getAutoAttackPet() : null))
         {
            _loc4_ = true;
         }
         return _loc3_ != _loc4_;
      }
      
      private function entityIsPKEntity(param1:IEntity) : Boolean
      {
         var _loc2_:Boolean = false;
         if(param1.getExtra() == null)
         {
            return false;
         }
         var _loc3_:* = param1.getExtra();
         loop0:
         switch(_loc3_)
         {
            case m_myPlayer1Xydzjs:
            case m_myPlayer1Xydzjs.getPet():
            case m_myPlayer1Xydzjs.getAutoAttackPet():
            case m_myPlayer2Xydzjs:
               addr25:
               _loc2_ = true;
               break;
               addr20:
               addr21:
               addr22:
               addr23:
               addr24:
               addr18:
               addr19:
            default:
               if((m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getPet() : null) !== _loc3_)
               {
                  switch(_loc3_)
                  {
                     case m_myPlayer2Xydzjs ? m_myPlayer2Xydzjs.getAutoAttackPet() : null:
                     case m_foePlayer1Xydzjs:
                        §§goto(addr20);
                     case m_foePlayer1Xydzjs.getPet():
                        §§goto(addr21);
                     case m_foePlayer1Xydzjs.getAutoAttackPet():
                        §§goto(addr22);
                     case m_foePlayer2Xydzjs:
                        §§goto(addr23);
                     default:
                        if((m_foePlayer2Xydzjs ? m_foePlayer2Xydzjs.getPet() : null) === _loc3_)
                        {
                           break;
                        }
                        if((m_foePlayer2Xydzjs ? m_foePlayer2Xydzjs.getAutoAttackPet() : null) !== _loc3_)
                        {
                           _loc2_ = false;
                           break loop0;
                        }
                        §§goto(addr25);
                  }
                  §§goto(addr24);
               }
               else
               {
                  §§goto(addr18);
               }
         }
         return _loc2_;
      }
      
      private function recoverPkPlayerPostion() : void
      {
         m_myPlayer1Xydzjs.getAnimalEntity().setNewPosition(m_myPlayer1InitPosition.getX(),m_myPlayer1InitPosition.getY(),m_myPlayer1InitPosition.getZ());
         m_myPlayer1Xydzjs.getAnimalEntity().setDirection(1,0);
         if(m_myUiPlayer2 && m_isTwoMode)
         {
            m_myPlayer2Xydzjs.getAnimalEntity().setNewPosition(m_myPlayer2InitPosition.getX(),m_myPlayer2InitPosition.getY(),m_myPlayer2InitPosition.getZ());
            m_myPlayer2Xydzjs.getAnimalEntity().setDirection(1,0);
         }
         m_foePlayer1Xydzjs.getAnimalEntity().setNewPosition(m_foePlayer1InitPosition.getX(),m_foePlayer1InitPosition.getY(),m_foePlayer1InitPosition.getZ());
         m_foePlayer1Xydzjs.getAnimalEntity().setDirection(-1,0);
         if(m_foePlayerAIEntity2)
         {
            m_foePlayer2Xydzjs.getAnimalEntity().setNewPosition(m_foePlayer2InitPosition.getX(),m_foePlayer2InitPosition.getY(),m_foePlayer2InitPosition.getZ());
            m_foePlayer2Xydzjs.getAnimalEntity().setDirection(-1,0);
         }
      }
      
      public function getPlayer1() : YJFY.GameEntity.PlayerAndPet.Player
      {
         return m_myPlayer1Xydzjs;
      }
      
      public function getPlayer2() : YJFY.GameEntity.PlayerAndPet.Player
      {
         return m_myPlayer2Xydzjs;
      }
      
      public function getWorld() : World
      {
         return m_world;
      }
      
      override protected function mapInitSuccess() : void
      {
         super.mapInitSuccess();
         initPlayerOne(m_myUiPlayer1);
         if(m_myUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_myUiPlayer2);
         }
         initPlayerOne(m_foeUiPlayer1);
         if(m_foeUiPlayer2 && m_isTwoMode)
         {
            initPlayerOne(m_foeUiPlayer2);
         }
      }
      
      private function initPlayerOne(param1:UI.Players.Player) : void
      {
         switch(param1.playerVO.playerType)
         {
            case "SunWuKong":
               m_thisload.getXML("NewGameFolder/Players/Monkey.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "BaiLongMa":
               m_thisload.getXML("NewGameFolder/Players/Dragon.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ErLangShen":
               m_thisload.getXML("NewGameFolder/Players/Dog.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ChangE":
               m_thisload.getXML("NewGameFolder/Players/Rabbit.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "Fox":
               m_thisload.getXML("NewGameFolder/Players/Fox.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "TieShan":
               m_thisload.getXML("NewGameFolder/Players/TieShan.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            case "ZiXia":
               m_thisload.getXML("NewGameFolder/Players/ZiXia.xml",getPlayerXMLSuccess,getFail,null,null,[param1]);
               break;
            default:
               throw new Error();
         }
         m_thisload.load();
      }
      
      private function getPlayerXMLSuccess(param1:YJFYLoaderData, param2:UI.Players.Player) : void
      {
         var _loc3_:YJFY.GameEntity.PlayerAndPet.Player = null;
         var _loc4_:PlayerAIEntity = null;
         if(param1.xmlPath == "NewGameFolder/Players/Monkey.xml")
         {
            _loc3_ = new Monkey();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dragon.xml")
         {
            _loc3_ = new Dragon();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Dog.xml")
         {
            _loc3_ = new Dog();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Rabbit.xml")
         {
            _loc3_ = new Rabbit();
         }
         else if(param1.xmlPath == "NewGameFolder/Players/Fox.xml")
         {
            _loc3_ = new Fox();
         }
         else
         {
            if(param1.xmlPath != "NewGameFolder/Players/TieShan.xml")
            {
               throw new Error();
            }
            _loc3_ = new TieShan();
            (_loc3_ as TieShan).setStatus("Huo");
         }
         _loc3_.setMyLoader(m_thisload);
         _loc3_.setEnterFrameTime(m_enterFrameTime);
         _loc3_.setAnimationDefinitionManager(m_animationDefinitionManager);
         _loc3_.setSoundMananger(m_soundManager);
         _loc3_.initByXML(param1.resultXML);
         switch(param2)
         {
            case m_myUiPlayer1:
               m_myPlayer1Xydzjs = _loc3_ as PlayerXydzjs;
               _loc3_.getAnimalEntity().setNewPosition(m_myPlayer1InitPosition.getX(),m_myPlayer1InitPosition.getY(),m_myPlayer1InitPosition.getZ());
               break;
            case m_myUiPlayer2:
               m_myPlayer2Xydzjs = _loc3_ as PlayerXydzjs;
               _loc3_.getAnimalEntity().setNewPosition(m_myPlayer2InitPosition.getX(),m_myPlayer2InitPosition.getY(),m_myPlayer2InitPosition.getZ());
               break;
            case m_foeUiPlayer1:
               _loc4_ = m_foePlayerAIEntity1;
               m_foePlayer1Xydzjs = _loc3_ as PlayerXydzjs;
               _loc4_.setPlayerXydzjs(m_foePlayer1Xydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_foePlayer1InitPosition.getX(),m_foePlayer1InitPosition.getY(),m_foePlayer1InitPosition.getZ());
               break;
            case m_foeUiPlayer2:
               _loc4_ = m_foePlayerAIEntity2;
               m_foePlayer2Xydzjs = _loc3_ as PlayerXydzjs;
               _loc4_.setPlayerXydzjs(m_foePlayer2Xydzjs);
               _loc3_.getAnimalEntity().setNewPosition(m_foePlayer2InitPosition.getX(),m_foePlayer2InitPosition.getY(),m_foePlayer2InitPosition.getZ());
               break;
            default:
               throw new Error();
         }
         (_loc3_ as PlayerXydzjs).setIsCreateAutoAttackPet(false);
         (_loc3_ as PlayerXydzjs).setUiPlayer(param2);
         m_world.addEntity(_loc3_.getAnimalEntity());
         initCompletePlayer(_loc3_);
      }
      
      protected function initCompletePlayer(param1:YJFY.GameEntity.PlayerAndPet.Player) : void
      {
         (param1 as PlayerXydzjs).setMedalIsDynamic(false);
         if(Boolean(m_myPlayer1Xydzjs) && (m_myUiPlayer2 == null || m_isTwoMode == false || Boolean(m_myPlayer2Xydzjs)) && Boolean(m_foePlayer1Xydzjs) && (m_foePlayerAIEntity2 == null || Boolean(m_foePlayer2Xydzjs)))
         {
            recoverPkPlayerPostion();
            addGameEntityListener();
            m_thisload.getClass("NewGameFolder/PKMode2/PKSource.swf",null,getHpBarShowSuccess,null);
            m_thisload.load();
         }
      }
      
      private function getHpBarShowSuccess(param1:YJFYLoaderData) : void
      {
         initHpBar();
      }
      
      private function initHpBar() : void
      {
         m_myPlayer1HpBarShow = MyFunction2.returnShowByClassName("PKHpBar1") as MovieClip;
         m_uiLayer2.addChild(m_myPlayer1HpBarShow);
         m_myPlayer1HpBarLG = new DoubleShowChangeBarLogicShell();
         m_myPlayer1HpBarLG.init(m_myPlayer1HpBarShow,m_enterFrameTime,500);
         m_foePlayer1HpBarShow = MyFunction2.returnShowByClassName("PKHpBar2") as MovieClip;
         m_uiLayer2.addChild(m_foePlayer1HpBarShow);
         m_foePlayer1HpBarLG = new DoubleShowChangeBarLogicShell();
         m_foePlayer1HpBarLG.init(m_foePlayer1HpBarShow,m_enterFrameTime,500);
         if(m_isTwoMode && Boolean(m_myPlayer2Xydzjs))
         {
            m_myPlayer2HpBarShow = MyFunction2.returnShowByClassName("PKHpBar1") as MovieClip;
            m_uiLayer2.addChild(m_myPlayer2HpBarShow);
            m_myPlayer2HpBarLG = new DoubleShowChangeBarLogicShell();
            m_myPlayer2HpBarLG.init(m_myPlayer2HpBarShow,m_enterFrameTime,500);
         }
         if(m_isTwoMode && Boolean(m_foePlayer2Xydzjs))
         {
            m_foePlayer2HpBarShow = MyFunction2.returnShowByClassName("PKHpBar2") as MovieClip;
            m_uiLayer2.addChild(m_foePlayer2HpBarShow);
            m_foePlayer2HpBarLG = new DoubleShowChangeBarLogicShell();
            m_foePlayer2HpBarLG.init(m_foePlayer2HpBarShow,m_enterFrameTime,500);
         }
      }
      
      private function renderHpBar() : void
      {
         var _loc1_:Point = null;
         if(m_myPlayer1HpBarLG)
         {
            m_myPlayer1HpBarLG.render();
            _loc1_ = m_myPlayer1Xydzjs.getAnimalEntity().getShow().localToGlobal(new Point(0,0));
            m_myPlayer1HpBarShow.x = _loc1_.x - m_myPlayer1HpBarShow.width / 2;
            m_myPlayer1HpBarShow.y = _loc1_.y - m_myPlayer1Xydzjs.getAnimalEntity().getBodyZRange();
         }
         if(m_myPlayer2HpBarLG)
         {
            m_myPlayer2HpBarLG.render();
            _loc1_ = m_myPlayer2Xydzjs.getAnimalEntity().getShow().localToGlobal(new Point(0,0));
            m_myPlayer2HpBarShow.x = _loc1_.x - m_myPlayer2HpBarShow.width / 2;
            m_myPlayer2HpBarShow.y = _loc1_.y - m_myPlayer2Xydzjs.getAnimalEntity().getBodyZRange();
         }
         if(m_foePlayer1HpBarLG)
         {
            m_foePlayer1HpBarLG.render();
            _loc1_ = m_foePlayer1Xydzjs.getAnimalEntity().getShow().localToGlobal(new Point(0,0));
            m_foePlayer1HpBarShow.x = _loc1_.x - m_foePlayer1HpBarShow.width / 2;
            m_foePlayer1HpBarShow.y = _loc1_.y - m_foePlayer1Xydzjs.getAnimalEntity().getBodyZRange();
         }
         if(m_foePlayer2HpBarLG)
         {
            m_foePlayer2HpBarLG.render();
            _loc1_ = m_foePlayer2Xydzjs.getAnimalEntity().getShow().localToGlobal(new Point(0,0));
            m_foePlayer2HpBarShow.x = 0 - m_foePlayer2HpBarShow.width / 2;
            m_foePlayer2HpBarShow.y = 0 - m_foePlayer2Xydzjs.getAnimalEntity().getBodyZRange();
         }
      }
      
      private function updateHpBarDataShow() : void
      {
         if(m_myPlayer1HpBarLG)
         {
            m_myPlayer1HpBarLG.change(m_myUiPlayer1.playerVO.bloodPercent);
            if(m_myUiPlayer1.playerVO.bloodPercent <= 0)
            {
               m_myPlayer1HpBarShow.visible = false;
            }
         }
         if(m_myPlayer2HpBarLG)
         {
            m_myPlayer2HpBarLG.change(m_myUiPlayer2.playerVO.bloodPercent);
            if(m_myUiPlayer2.playerVO.bloodPercent <= 0)
            {
               m_myPlayer2HpBarShow.visible = false;
            }
         }
         if(m_foePlayer1HpBarLG)
         {
            m_foePlayer1HpBarLG.change(m_foeUiPlayer1.playerVO.bloodPercent);
            if(m_foeUiPlayer1.playerVO.bloodPercent <= 0)
            {
               m_foePlayer1HpBarShow.visible = false;
            }
         }
         if(m_foePlayer2HpBarLG)
         {
            m_foePlayer2HpBarLG.change(m_foeUiPlayer2.playerVO.bloodPercent);
            if(m_foeUiPlayer2.playerVO.bloodPercent <= 0)
            {
               m_foePlayer2HpBarShow.visible = false;
            }
         }
      }
      
      public function forceEnd() : void
      {
         m_isPKing = false;
         m_isMyWin = false;
         end();
      }
      
      private function judgeIsEnd() : Boolean
      {
         if(m_isPKing == false)
         {
            return true;
         }
         if(m_myPlayer1Xydzjs.getAnimalEntity().isInDie() && (Boolean(m_myPlayer2Xydzjs) == false || m_myPlayer2Xydzjs.getAnimalEntity().isInDie()))
         {
            m_isPKing = false;
            m_isMyWin = false;
            end();
            return true;
         }
         if(m_foePlayer1Xydzjs.getAnimalEntity().isInDie() && (Boolean(m_foePlayer2Xydzjs) == false || m_foePlayer2Xydzjs.getAnimalEntity().isInDie()))
         {
            m_isPKing = false;
            m_isMyWin = true;
            end();
            return true;
         }
         return false;
      }
      
      private function end() : void
      {
         m_gameKeyControlLogic.lockKeyAction();
         m_pk.addPKDataAndSubmitToRankList(m_isMyWin);
         m_isPKing = false;
         m_pkingInforPanel.playGameEndAnimation(m_isMyWin,null);
      }
   }
}

