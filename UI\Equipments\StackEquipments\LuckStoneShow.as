package UI.Equipments.StackEquipments
{
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import flash.display.DisplayObject;
   
   public class LuckStoneShow extends MySprite
   {
      
      private var _wanLoadSource:Array;
      
      public function LuckStoneShow()
      {
         var loadFinishListener:LoadFinishListener1;
         _wanLoadSource = ["equipmentUI"];
         super();
         loadFinishListener = new LoadFinishListener1(function():void
         {
            var _loc1_:DisplayObject = MyFunction2.returnShowByClassName("UI.Equipments.StackEquipments.LuckStone");
            addChild(_loc1_);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSource,loadFinishListener);
      }
   }
}

