package YJFY.Skill.TieShanSkills
{
   import UI.GamingUI;
   import UI.Players.PlayerVO;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.RealityEntity;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.TieShan;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.PlayerAISkillVO;
   
   public class Skill_TieShanSkillFeng2 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      public var isStart:Boolean = false;
      
      private var m_effectAddtoTargetId:String;
      
      private var costMp:Number = 0;
      
      private var costMpIndex:int = 0;
      
      private var effectAnimationShowPlay:AnimationShowPlayLogicShell;
      
      public function Skill_TieShanSkillFeng2()
      {
         super();
         effectAnimationShowPlay = new AnimationShowPlayLogicShell();
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearObject(effectAnimationShowPlay);
         effectAnimationShowPlay = null;
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         if(isStart)
         {
            endSkill2();
            endBuff();
            return false;
         }
         (m_owner.getExtra() as TieShan).removeSkillBuff();
         releaseSkill2(param1);
         return true;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         costMp = int(param1.@costMp) / 100;
         m_effectAddtoTargetId = String(param1.@effectAddtoTargetId);
      }
      
      override public function setExtra(param1:Object) : void
      {
         super.setExtra(param1);
         if(param1 && (param1 as PlayerAISkillVO).getSkillVO())
         {
            (param1 as PlayerAISkillVO).getSkillVO().isUntileUse = false;
            GamingUI.getInstance().refresh(64);
         }
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
      }
      
      public function renderFromPlayer() : void
      {
         var _loc2_:PlayerVO = null;
         if(!isStart)
         {
            return;
         }
         if(!m_world)
         {
            return;
         }
         var _loc1_:Number = 0;
         if(m_isOutWorldTime)
         {
            _loc1_ = m_world.getWorldExistTime();
         }
         else
         {
            _loc1_ = m_world.getWorldTime();
         }
         if(!isNaN(m_nextAttackReachTime) && Boolean(m_attackInterval) && m_nextAttackReachTime < _loc1_)
         {
            getCuboidRangeToWorld();
            attackReach(m_world);
         }
         costMpIndex++;
         if(costMpIndex >= 50)
         {
            costMpIndex = 0;
            _loc2_ = (this.m_owner.getExtra() as PlayerXydzjs).getUiPlayer().playerVO;
            if(_loc2_.magicPercent > costMp)
            {
               _loc2_.magicPercent -= costMp;
            }
            else
            {
               endBuff();
            }
         }
      }
      
      public function endBuff() : void
      {
         if(!isStart)
         {
            return;
         }
         isStart = false;
         (getExtra() as PlayerAISkillVO).getSkillVO().isUntileUse = false;
         this.m_owner.removeOtherAnimation(effectAnimationShowPlay);
         GamingUI.getInstance().refresh(64);
      }
      
      override protected function attackSuccess2(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         super.attackSuccess2(param1,param2,param3);
         if(Boolean(m_attackData) && m_attackData.getIsAttack() && param1 is IAnimalEntity)
         {
            if(!(param1 is RealityEntity) || (param1 as RealityEntity).canNotBePushed)
            {
               param1.applyImpulse(new P3DVector3D(m_owner.getShowDirection() * 500000,0,500000));
            }
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
         if(!isStart)
         {
            addBuff();
            m_nextAttackReachTime = 0;
         }
      }
      
      private function addBuff() : void
      {
         this.isStart = true;
         (getExtra() as PlayerAISkillVO).getSkillVO().isUntileUse = true;
         GamingUI.getInstance().refresh(64);
         effectAnimationShowPlay.setShow(m_owner.getAnimationByDefId(m_effectAddtoTargetId),true);
         effectAnimationShowPlay.gotoAndPlay("start");
         this.m_owner.addOtherAniamtion(effectAnimationShowPlay);
      }
   }
}

