package YJFY.XydzjsData
{
   import UI.Skills.SkillVO;
   import YJFY.Logic.IActiveSkillVO_HaveAiNeedSkillInfor;
   
   public class PlayerAISkillVO implements IActiveSkillVO_HaveAiNeedSkillInfor
   {
      
      private var m_isAbleRunInHurt:Boolean;
      
      private var m_isInvincibleInRun:Boolean;
      
      private var m_priorityForRunInHurt:uint;
      
      private var m_priorityForRun:uint;
      
      private var m_isAbleRun:Boolean;
      
      private var m_skillVO:SkillVO;
      
      public function PlayerAISkillVO()
      {
         super();
         m_isAbleRun = true;
      }
      
      public function clear() : void
      {
         m_skillVO = null;
      }
      
      public function initFromXML(param1:XML, param2:SkillVO) : void
      {
         m_skillVO = param2;
         this.isAbleRunInHurt = Boolean(int(param1.@isAbleRunInHurt));
         this.isInvincibleInRun = Boolean(int(param1.@isInvincibleInRun));
         this.priorityForRunInHurt = uint(param1.@priorityForRunInHurt);
         this.priorityForRun = uint(param1.@priorityForRun);
         var _loc3_:String = String(param1.@isAbleRun);
         if(_loc3_)
         {
            this.isAbleRun = Boolean(uint(_loc3_));
         }
      }
      
      public function getSkillVO() : SkillVO
      {
         return m_skillVO;
      }
      
      public function getIsAbleRunInHurt() : Boolean
      {
         return isAbleRunInHurt;
      }
      
      public function getIsInvincibleInRun() : Boolean
      {
         return isInvincibleInRun;
      }
      
      public function getPriorityForRunInHurt() : uint
      {
         return priorityForRunInHurt;
      }
      
      public function getPriorityForRun() : uint
      {
         return priorityForRun;
      }
      
      public function getIsAbleRun() : Boolean
      {
         return isAbleRun;
      }
      
      protected function get isAbleRunInHurt() : Boolean
      {
         return m_isAbleRunInHurt;
      }
      
      protected function set isAbleRunInHurt(param1:Boolean) : void
      {
         m_isAbleRunInHurt = param1;
      }
      
      protected function get isInvincibleInRun() : Boolean
      {
         return m_isInvincibleInRun;
      }
      
      protected function set isInvincibleInRun(param1:Boolean) : void
      {
         m_isInvincibleInRun = param1;
      }
      
      protected function get priorityForRunInHurt() : uint
      {
         return m_priorityForRunInHurt;
      }
      
      protected function set priorityForRunInHurt(param1:uint) : void
      {
         m_priorityForRunInHurt = param1;
      }
      
      protected function get priorityForRun() : uint
      {
         return m_priorityForRun;
      }
      
      protected function set priorityForRun(param1:uint) : void
      {
         m_priorityForRun = param1;
      }
      
      protected function get isAbleRun() : Boolean
      {
         return m_isAbleRun;
      }
      
      protected function set isAbleRun(param1:Boolean) : void
      {
         m_isAbleRun = param1;
      }
   }
}

