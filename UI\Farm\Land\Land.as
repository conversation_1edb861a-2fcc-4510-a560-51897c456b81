package UI.Farm.Land
{
   import UI.Event.UIPassiveEvent;
   import UI.Farm.CoordGrid.SceneCoordGrid;
   import UI.Farm.CoordGrid.SceneCoordGrids;
   import UI.Farm.FarmFunction;
   import UI.Farm.FarmShowObjectVO;
   import UI.Farm.IFarmShowObject;
   import UI.Farm.MouseManager.MouseManager;
   import UI.MyFunction2;
   import UI.MySprite;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.utils.getTimer;
   
   public class Land extends MySprite implements IFarmShowObject
   {
      
      public var landShow:Sprite;
      
      private var _landDrive:LandDrive;
      
      private var _container:Sprite;
      
      private var _listenerList:Array = [];
      
      private var _intervalTime:uint;
      
      private const _TIME:uint = 200;
      
      public function Land(param1:LandVO, param2:String, param3:XML, param4:XML)
      {
         super();
         init(param1,param2,param3,param4);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         super.clear();
         destory();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_container)
         {
            while(_container.numChildren > 0)
            {
               _container.removeChildAt(0);
            }
         }
         if(landShow)
         {
            while(landShow.numChildren > 0)
            {
               landShow.removeChildAt(0);
            }
         }
         landShow = null;
         _container = null;
         if(_landDrive)
         {
            _landDrive.clear();
         }
         _landDrive = null;
         var _loc3_:int = 0;
         if(_listenerList)
         {
            _loc2_ = int(_listenerList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_listenerList[_loc3_])
               {
                  for(var _loc1_ in _listenerList[_loc3_])
                  {
                     _listenerList[_loc3_][_loc1_] = null;
                  }
               }
               _listenerList[_loc3_] = null;
               _loc3_++;
            }
         }
         _listenerList = null;
      }
      
      public function get farmShowObjectVO() : FarmShowObjectVO
      {
         return _landDrive.landVO;
      }
      
      public function set farmShowObjectVO(param1:FarmShowObjectVO) : void
      {
         _landDrive.landVO = param1 as LandVO;
      }
      
      public function get hI() : int
      {
         return _landDrive.landVO.hI;
      }
      
      public function get vJ() : int
      {
         return _landDrive.landVO.vJ;
      }
      
      public function sethv(param1:int, param2:int) : void
      {
         _landDrive.landVO.vJ = param2;
         _landDrive.landVO.hI = param1;
         var _loc3_:SceneCoordGrid = SceneCoordGrids.getInstance().returnSceneCoordByHV(_landDrive.landVO.hI,_landDrive.landVO.vJ);
         this.x = _loc3_.x;
         this.y = _loc3_.y;
         dispatchEvent(new UIPassiveEvent("changeBuffRemaineTime"));
      }
      
      public function set framShowState(param1:int) : void
      {
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = int(_listenerList.length);
         _loc5_ = 0;
         while(_loc5_ < _listenerList.length)
         {
            if(_listenerList[_loc5_].type == param1 && _listenerList[_loc5_].listener == param2)
            {
               _listenerList.splice(_loc5_,1);
               super.removeEventListener(param1,param2,param3);
               _loc5_--;
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
      
      public function get landDrive() : LandDrive
      {
         return _landDrive;
      }
      
      public function set landDrive(param1:LandDrive) : void
      {
         _landDrive = param1;
      }
      
      public function addLandPartShow(param1:int, param2:XML) : void
      {
         while(landShow.numChildren > 0)
         {
            landShow.removeChildAt(0);
         }
         if(!param1)
         {
            landShow.addChild(MyFunction2.returnShowByClassName("undevelopedLand"));
            return;
         }
         if(param1 == -1)
         {
            landShow.addChild(MyFunction2.returnShowByClassName("WasteLand"));
            return;
         }
         var _loc3_:DisplayObject = MyFunction2.returnShowByClassName(String(param2.land.(@id == param1)[0].@className));
         if(_loc3_)
         {
            landShow.addChild(_loc3_);
         }
      }
      
      public function addLandUpPartShow(param1:DisplayObject) : void
      {
         while(_container.numChildren > 0)
         {
            _container.removeChildAt(0);
         }
         if(param1)
         {
            _container.addChild(param1);
         }
      }
      
      private function init(param1:LandVO, param2:String, param3:XML, param4:XML) : void
      {
         landShow = new Sprite();
         _container = new Sprite();
         addChild(_container);
         var _loc6_:int = int(param4.landData[0].@H_width);
         var _loc5_:int = int(param4.landData[0].@V_height);
         _container.graphics.lineStyle(1,0,0);
         _container.graphics.beginFill(16777215,0);
         _container.graphics.moveTo(0,-30 * _loc5_ / 2);
         _container.graphics.lineTo(60 * _loc6_ / 2,0);
         _container.graphics.lineTo(0,30 * _loc5_ / 2);
         _container.graphics.lineTo(-60 * _loc6_ / 2,0);
         _container.graphics.lineTo(0,-30 * _loc5_ / 2);
         _container.graphics.endFill();
         addEventListener("rollOver",onOver,false,0,true);
         addEventListener("rollOut",onOut,false,0,true);
         addEventListener("mouseDown",onMouseDown,false,0,true);
         addEventListener("mouseUp",onMouseUp,false,0,true);
         _landDrive = new LandDrive(param1,param2,param3,param4);
         addLandPartShow(_landDrive.landVO.id,param4);
         FarmFunction.getInstance().setLandStateByLand(this);
         if(_landDrive)
         {
            _landDrive.addEventListener("changeState",changeShowState,false,0,true);
         }
      }
      
      private function changeShowState(param1:Event) : void
      {
         FarmFunction.getInstance().setLandStateByLand(this);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showLandProgressBar",this));
         filters = [new GlowFilter(16777215,1,255,255,0.2,1,true)];
         if(landShow)
         {
            landShow.filters = [new GlowFilter(16777215,1,255,255,0.5,1,true)];
         }
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideLandProgressBar"));
         filters = [];
         if(landShow)
         {
            landShow.filters = [];
         }
      }
      
      private function onMouseDown(param1:MouseEvent) : void
      {
         _intervalTime = getTimer();
      }
      
      private function onMouseUp(param1:MouseEvent) : void
      {
         _intervalTime = getTimer() - _intervalTime;
         if(MouseManager.getInstance().state != "normal")
         {
            return;
         }
         if(_intervalTime < 200)
         {
            dispatchEvent(new UIPassiveEvent("clickLand"));
         }
      }
   }
}

