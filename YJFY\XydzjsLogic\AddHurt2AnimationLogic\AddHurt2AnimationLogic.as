package YJFY.XydzjsLogic.AddHurt2AnimationLogic
{
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AttackData;
   import YJFY.Entity.CustomAnimationData;
   import YJFY.Entity.IEntity;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   
   public class AddHurt2AnimationLogic
   {
      
      private const m_const_hurtDurationForHurt2:uint = 5000;
      
      private var m_listeners:Vector.<IAddHurt2AnimationLogicListener>;
      
      private var m_hurtAnimationData:CustomAnimationData;
      
      private var m_recoverToIdleFrameLabel:String;
      
      private var m_isInHurt2:Boolean;
      
      private var m_animalEntity:AnimalEntity;
      
      public function AddHurt2AnimationLogic()
      {
         super();
         m_listeners = new Vector.<IAddHurt2AnimationLogicListener>();
      }
      
      public function setAnimalEntity(param1:AnimalEntity) : void
      {
         m_animalEntity = param1;
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_listeners,false,false,false);
         m_listeners = null;
         ClearUtil.clearObject(m_hurtAnimationData);
         m_hurtAnimationData = null;
         m_recoverToIdleFrameLabel = null;
      }
      
      public function initByXML(param1:XML) : void
      {
         ClearUtil.clearObject(m_hurtAnimationData);
         var _loc2_:String = String(param1.@playFrameLabel);
         if(Boolean(_loc2_) == false)
         {
            _loc2_ = "1";
         }
         m_hurtAnimationData = null;
         m_hurtAnimationData = new CustomAnimationData(param1.@defId,_loc2_);
         m_recoverToIdleFrameLabel = String(param1.@recoverFrameLabel);
      }
      
      public function addAddHurt2AnimationLogicListener(param1:IAddHurt2AnimationLogicListener) : void
      {
         ListenerUtil.addListener(m_listeners,param1);
      }
      
      public function removeAddHurt2AnimationLogicListener(param1:IAddHurt2AnimationLogicListener) : void
      {
         ListenerUtil.removeListener(m_listeners,param1);
      }
      
      public function beAttack(param1:IEntity, param2:AttackData, param3:ISkill, param4:IEntity) : void
      {
         if(m_animalEntity == null)
         {
            return;
         }
         if(m_animalEntity.getBody().getVelocity().getLength() && m_animalEntity.isInDie() == false && m_animalEntity.isInHurt() == false)
         {
            m_animalEntity.setCustomAnimationData(m_hurtAnimationData);
            m_isInHurt2 = true;
            param2.resetData(param2.getHurt(),param2.getIsCritical(),param2.getIsBeDodge(),5000);
         }
      }
      
      public function entityAnimationPlayReachFrameLabel(param1:String) : void
      {
         if(param1 == m_recoverToIdleFrameLabel)
         {
            m_animalEntity.setCustomAnimationData(null);
            m_isInHurt2 = false;
            hurt2AnimationEnd(this);
            if(m_animalEntity.isInDie() == false)
            {
               m_animalEntity.idle();
            }
            else
            {
               m_animalEntity.die();
            }
         }
      }
      
      public function getIsInHurt2() : Boolean
      {
         return m_isInHurt2;
      }
      
      private function hurt2AnimationEnd(param1:AddHurt2AnimationLogic) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IAddHurt2AnimationLogicListener> = m_listeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_[_loc4_].hurt2AnimationEnd(param1);
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
   }
}

