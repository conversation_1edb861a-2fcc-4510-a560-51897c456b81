package YJFY.ShowLogicShell.MovieClipPlayLogicShell
{
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.events.Event;
   
   public class MovieClipPlayLogicShell
   {
      
      protected var m_show:MovieClip;
      
      private var _isStop:Boolean;
      
      private var _nextStopFun:Function;
      
      private var _nextStopFunParams:Array;
      
      private var _extra:Object;
      
      public function MovieClipPlayLogicShell()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("addedToStage",addToStage,false);
            if(m_show.stage)
            {
               m_show.stage.removeEventListener("enterFrame",render,false);
            }
         }
         this.m_show = param1;
         this.m_show.stop();
         _isStop = true;
         if(param1.stage)
         {
            param1.stage.addEventListener("enterFrame",render,false,0,true);
         }
         else
         {
            param1.addEventListener("addedToStage",addToStage,false,0,true);
         }
      }
      
      public function getShow() : MovieClip
      {
         return m_show;
      }
      
      public function setExtra(param1:Object) : void
      {
         _extra = param1;
      }
      
      public function getExtra() : Object
      {
         return _extra;
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("addedToStage",addToStage,false);
            if(m_show.stage)
            {
               m_show.stage.removeEventListener("enterFrame",render,false);
            }
         }
         _nextStopFun = null;
         ClearUtil.nullArr(_nextStopFunParams,false,false,false);
         _nextStopFunParams = null;
         m_show = null;
         _extra = null;
      }
      
      protected function addToStage(param1:Event) : void
      {
         m_show.stage.addEventListener("removedFromStage",removeFromStage,false,0,true);
         m_show.stage.addEventListener("enterFrame",render,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         m_show.stage.removeEventListener("removedFromStage",removeFromStage,false);
         m_show.stage.removeEventListener("enterFrame",render,false);
      }
      
      protected function render(param1:Event) : void
      {
         if(_isStop)
         {
            return;
         }
         if(m_show == null)
         {
            return;
         }
         m_show.nextFrame();
         if(m_show.currentFrameLabel)
         {
            _isStop = true;
            if(Boolean(_nextStopFun))
            {
               _nextStopFun.apply(null,_nextStopFunParams);
            }
            _nextStopFun = null;
            ClearUtil.nullArr(_nextStopFunParams,false,false,false);
            _nextStopFunParams = null;
         }
      }
      
      public function gotoAndPlay(param1:String, param2:Function = null, param3:Array = null, param4:Function = null, param5:Array = null) : void
      {
         try
         {
            m_show.gotoAndStop(param1);
            _isStop = false;
         }
         catch(error:Error)
         {
            m_show.gotoAndStop(1);
            if(Boolean(_nextStopFun))
            {
               _nextStopFun.apply(null,_nextStopFunParams);
            }
            _nextStopFun = null;
            ClearUtil.nullArr(_nextStopFunParams,false,false,false);
            _nextStopFunParams = null;
         }
         if(Boolean(param2))
         {
            param2.apply(null,param3);
         }
         _nextStopFun = param4;
         _nextStopFunParams = param5;
      }
      
      public function gotoAndStop(param1:String, param2:Function = null, param3:Array = null) : void
      {
         try
         {
            m_show.gotoAndStop(param1);
         }
         catch(e:Error)
         {
            m_show.gotoAndStop(1);
         }
         _isStop = true;
         if(Boolean(param2))
         {
            param2.apply(null,param3);
         }
      }
   }
}

