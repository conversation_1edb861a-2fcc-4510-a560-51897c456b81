package UI.SocietySystem.SeverLink.Test.InformationBodyDetailTest
{
   import UI.SocietySystem.SeverLink.InformationBodyDetailUtil;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_GetChatInfor;
   
   public class TEST_DOWN_GetChatInfor extends DOWN_GetChatInfor
   {
      
      public function TEST_DOWN_GetChatInfor()
      {
         super();
      }
      
      public function initData(param1:Number, param2:int, param3:String, param4:String, param5:int) : void
      {
         m_uid = param1;
         m_idx = param2;
         m_name = param3;
         m_chatStr = param4;
         m_chatStrLength = new InformationBodyDetailUtil().getStringLength(m_chatStr);
         m_extraData = param5;
      }
   }
}

