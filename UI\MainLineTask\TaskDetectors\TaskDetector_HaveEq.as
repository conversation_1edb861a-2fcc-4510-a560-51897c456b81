package UI.MainLineTask.TaskDetectors
{
   import UI.Equipments.AbleEquipments.AbleEquipmentVO;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.Players.Player;
   import YJFY.Utils.ClearUtil;
   
   public class TaskDetector_HaveEq extends TaskDetector
   {
      
      private var m_detectXML:XML;
      
      public function TaskDetector_HaveEq()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_detectXML = param1;
      }
      
      override public function detect() : void
      {
         var _loc17_:* = false;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc10_:int = 0;
         var _loc5_:int = 0;
         var _loc13_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:String = null;
         var _loc11_:String = null;
         var _loc9_:String = null;
         var _loc1_:String = null;
         var _loc12_:String = null;
         var _loc8_:Boolean = false;
         var _loc14_:Boolean = true;
         var _loc16_:Vector.<EquipmentVO> = createAllEquipmentVOs();
         var _loc3_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc15_:XMLList = m_detectXML.equipment;
         _loc13_ = int(_loc15_ ? _loc15_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc13_)
         {
            _loc2_ = String(_loc15_[_loc7_].@equipmentType);
            _loc11_ = String(_loc15_[_loc7_].@suitName);
            _loc9_ = String(_loc15_[_loc7_].@equipmentName);
            _loc1_ = String(_loc15_[_loc7_].@equipmentLevel);
            _loc12_ = String(_loc15_[_loc7_].@holeNum);
            _loc5_ = int(_loc16_.length);
            _loc8_ = false;
            _loc4_ = 0;
            for(; _loc4_ < _loc5_; _loc4_++)
            {
               if(_loc16_[_loc4_] != null)
               {
                  _loc6_ = int(_loc3_.length);
                  _loc10_ = 0;
                  while(_loc10_ < _loc6_)
                  {
                     if(_loc16_[_loc4_] != _loc3_[_loc10_])
                     {
                     }
                     _loc10_++;
                  }
                  if(_loc2_)
                  {
                     if(_loc16_[_loc4_].equipmentType != _loc2_)
                     {
                        continue;
                     }
                  }
                  if(_loc11_)
                  {
                     if(!(_loc16_[_loc4_] is AbleEquipmentVO) || (_loc16_[_loc4_] as AbleEquipmentVO).suitName != _loc11_)
                     {
                        continue;
                     }
                  }
                  if(_loc9_)
                  {
                     if(_loc16_[_loc4_].name != _loc9_)
                     {
                        continue;
                     }
                  }
                  if(_loc1_)
                  {
                     if(_loc16_[_loc4_].level < int(_loc1_))
                     {
                        continue;
                     }
                  }
                  if(_loc12_)
                  {
                     if(!(_loc16_[_loc4_] is AbleEquipmentVO) || (_loc16_[_loc4_] as AbleEquipmentVO).getHoleNum() < int(_loc12_))
                     {
                        continue;
                     }
                  }
                  _loc8_ = true;
                  _loc3_.push(_loc16_[_loc4_]);
                  break;
               }
            }
            if(_loc8_ == false)
            {
               _loc14_ = false;
               break;
            }
            _loc7_++;
         }
         _loc17_ = _loc14_;
         ClearUtil.nullArr(_loc3_,false,false,false);
         _loc3_ = null;
         if(_loc17_)
         {
            GamingUI.getInstance().taskEvent(m_eventStr);
         }
      }
      
      private function createAllEquipmentVOs() : Vector.<EquipmentVO>
      {
         var _loc4_:* = undefined;
         var _loc1_:* = undefined;
         var _loc2_:Player = GamingUI.getInstance().player1;
         var _loc3_:Player = GamingUI.getInstance().player2;
         _loc4_ = _loc2_.playerVO.inforEquipmentVOs.slice(0);
         _loc1_ = _loc4_.concat(_loc2_.playerVO.packageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         _loc4_ = _loc1_;
         _loc1_ = _loc4_.concat(_loc2_.playerVO.storageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         _loc4_ = _loc1_;
         if(Boolean(_loc2_.playerVO.pet) && _loc2_.playerVO.pet.petEquipmentVO)
         {
            _loc4_.push(_loc2_.playerVO.pet.petEquipmentVO);
         }
         if(_loc3_)
         {
            _loc1_ = _loc4_.concat(_loc3_.playerVO.inforEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            _loc1_ = _loc4_.concat(_loc3_.playerVO.packageEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            _loc1_ = _loc4_.concat(_loc3_.playerVO.storageEquipmentVOs);
            ClearUtil.nullArr(_loc4_,false,false,false);
            _loc4_ = _loc1_;
            if(_loc3_.playerVO.pet != null && _loc3_.playerVO.pet.petEquipmentVO)
            {
               _loc4_.push(_loc3_.playerVO.pet.petEquipmentVO);
            }
         }
         _loc1_ = _loc4_.concat(GamingUI.getInstance().publicStorageEquipmentVOs);
         ClearUtil.nullArr(_loc4_,false,false,false);
         return _loc1_;
      }
   }
}

