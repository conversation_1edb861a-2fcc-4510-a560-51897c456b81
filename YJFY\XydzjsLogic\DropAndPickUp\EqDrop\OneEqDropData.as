package YJFY.XydzjsLogic.DropAndPickUp.EqDrop
{
   public class OneEqDropData
   {
      
      private var m_dropClassName:String;
      
      private var m_proWeight:Number;
      
      private var m_upPro:Number;
      
      private var m_downPro:Number;
      
      public function OneEqDropData(param1:String, param2:Number, param3:Number, param4:Number)
      {
         super();
         m_dropClassName = param1;
         m_proWeight = param2;
         m_upPro = param3;
         m_downPro = param4;
      }
      
      public function clear() : void
      {
         m_dropClassName = null;
      }
      
      public function getDropClassName() : String
      {
         return m_dropClassName;
      }
      
      public function getProWeight() : Number
      {
         return m_proWeight;
      }
      
      public function getUpPro() : Number
      {
         return m_upPro;
      }
      
      public function getDownPro() : Number
      {
         return m_downPro;
      }
   }
}

