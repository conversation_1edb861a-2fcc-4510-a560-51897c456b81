package UI.Shop
{
   import UI.EquipmentCells.ShopEquipmentCell;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.GamingUI;
   import UI.InitUI;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.XMLSingle;
   import YJFY.ToolTip.SmallToolTip;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class EquipmentSlot extends MySprite
   {
      
      public var equipmentName:TextField;
      
      public var equipmentPrice:TextField;
      
      public var border:Sprite;
      
      private var _moneyOrPKSprite:Sprite;
      
      private var _equipmentVO:EquipmentVO;
      
      protected var m_tipString:String;
      
      protected var m_smallToolTip:SmallToolTip;
      
      private var equipmentCell:IEquipmentCell;
      
      public function EquipmentSlot(param1:EquipmentVO, param2:String)
      {
         super();
         _equipmentVO = param1;
         initSlot(param2);
         addEventListener("addedToStage",addToStage,false,0,true);
         setTipString("点击购买该物品");
         buttonMode = true;
      }
      
      override public function clear() : void
      {
         super.clear();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _equipmentVO = null;
         equipmentName = null;
         if(equipmentCell)
         {
            equipmentCell.clear();
         }
         equipmentCell = null;
         equipmentPrice = null;
         border = null;
         equipmentCell = null;
         ClearUtil.clearObject(m_smallToolTip);
         m_smallToolTip = null;
      }
      
      public function setTipString(param1:String) : void
      {
         m_tipString = param1;
         if(m_tipString)
         {
            if(m_smallToolTip == null)
            {
               m_smallToolTip = new SmallToolTip();
               m_smallToolTip.setFont(new FangZhengKaTongJianTi());
               m_smallToolTip.setTipStr(m_tipString);
               m_smallToolTip.init();
            }
         }
      }
      
      protected function initSlot(param1:String) : void
      {
         equipmentCell = new ShopEquipmentCell();
         equipmentCell.x = 0 + equipmentCell.width / 2;
         equipmentCell.y = 2.5 + equipmentCell.height / 2;
         addChild(equipmentCell as DisplayObject);
         equipmentCell.addEquipmentVO(_equipmentVO);
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         equipmentName.defaultTextFormat = new TextFormat(_loc2_.fontName,20,16777215);
         equipmentName.mouseEnabled = false;
         equipmentPrice.defaultTextFormat = new TextFormat(_loc2_.fontName,25,16776960);
         equipmentPrice.mouseEnabled = false;
         equipmentName.embedFonts = true;
         equipmentPrice.embedFonts = true;
         equipmentName.text = _equipmentVO.name;
         switch(param1)
         {
            case "moneyShop":
               _moneyOrPKSprite = new MoneySprite();
               equipmentPrice.text = _equipmentVO.price.toString();
               break;
            case "pk点商店":
               _moneyOrPKSprite = new PKSprite();
               equipmentPrice.text = _equipmentVO.pkPrice.toString();
               break;
            default:
               throw new Error("选项错误！");
         }
         _moneyOrPKSprite.y = (height - _moneyOrPKSprite.height) / 2 + 0;
         addChild(_moneyOrPKSprite);
         _moneyOrPKSprite.x = equipmentPrice.x + equipmentPrice.width - equipmentPrice.textWidth - _moneyOrPKSprite.width;
         border.visible = false;
      }
      
      protected function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",mouseOverSlot,false,0,true);
         addEventListener("rollOut",mouseOutSlot,false,0,true);
         addEventListener("click",click,false,0,true);
      }
      
      protected function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage);
         removeEventListener("rollOver",mouseOverSlot);
         removeEventListener("rollOut",mouseOutSlot);
         removeEventListener("click",click,false);
      }
      
      protected function mouseOverSlot(param1:MouseEvent) : void
      {
         border.visible = true;
         if(m_smallToolTip)
         {
            if(stage)
            {
               stage.addChild(m_smallToolTip);
               if(stage.mouseX + 10 + m_smallToolTip.width > stage.stageWidth)
               {
                  m_smallToolTip.x = stage.mouseX - 10 - m_smallToolTip.width;
               }
               else
               {
                  m_smallToolTip.x = stage.mouseX + 10;
               }
               if(stage.mouseY + 10 + m_smallToolTip.height > stage.stageHeight)
               {
                  m_smallToolTip.y = stage.mouseY - 10 - m_smallToolTip.height;
               }
               else
               {
                  m_smallToolTip.y = stage.mouseY + 10;
               }
            }
         }
      }
      
      protected function mouseOutSlot(param1:MouseEvent) : void
      {
         border.visible = false;
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
      }
      
      protected function click(param1:MouseEvent) : void
      {
         if(Boolean(m_smallToolTip) && m_smallToolTip.parent)
         {
            m_smallToolTip.parent.removeChild(m_smallToolTip);
         }
         if(_equipmentVO.isShopItem)
         {
            _equipmentVO = InitUI.getInstance().getShopItemVO(_equipmentVO,XMLSingle.getInstance().equipmentXML,XMLSingle.getInstance().skillXML,XMLSingle.getInstance().talentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
         }
         dispatchEvent(new UIDataEvent("showBox",{"equipmentVO":_equipmentVO.clone()}));
      }
   }
}

