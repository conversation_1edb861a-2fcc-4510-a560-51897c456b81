package UI.WorldBoss.AnimationQueueData.AnimationData
{
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   
   public class BossUpgradeListener implements IPlayEndListener
   {
      
      public var animationData:AnimationData;
      
      public var level:int;
      
      public var mc:AnimationShowPlayLogicShell;
      
      public function BossUpgradeListener()
      {
         super();
      }
      
      public function playEnd() : void
      {
         if(mc != null && mc.getShow() != null && mc.getShow()["card"] != null && mc.getShow()["card"]["levelText"])
         {
            mc.getShow()["card"]["levelText"].text = level.toString();
         }
         animationData.removePlayEndListener(this);
         clear();
      }
      
      public function clear() : void
      {
         animationData = null;
         mc = null;
      }
   }
}

