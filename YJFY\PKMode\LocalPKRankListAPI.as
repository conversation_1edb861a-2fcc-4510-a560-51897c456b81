package YJFY.PKMode
{
   import UI.PKUI.PKFunction;
   import YJFY.API_4399.RankListAPI.ILocalRankListAPI;
   import YJFY.API_4399.RankListAPI.SubmitData;
   import YJFY.GameData;
   import YJFY.Part1;
   import flash.utils.setTimeout;
   import unit4399.events.RankListEvent;
   
   public class LocalPKRankListAPI implements ILocalRankListAPI
   {
      
      private var rankArr:Array = [{
         "userName":"#天生一对#",
         "score":100,
         "rank":1,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":2,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":3,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":4,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":5,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":6,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":7,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":8,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":9,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":10,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":11,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":12,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":13,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":14,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":15,
         "index":1
      },{
         "userName":"#天生一对#",
         "score":100,
         "rank":16,
         "index":1
      }];
      
      public function LocalPKRankListAPI()
      {
         super();
      }
      
      public function getOneRankInfo(param1:uint, param2:String) : void
      {
         Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
            "data":[{
               "userName":"test",
               "uId":"test123",
               "score":100,
               "rank":3,
               "index":3
            }],
            "apiName":"1"
         }));
      }
      
      public function getRankListByOwn(param1:uint, param2:uint, param3:uint) : void
      {
      }
      
      public function getRankListsData(param1:uint, param2:uint, param3:uint) : void
      {
         var object:Object;
         var rankListId:uint = param1;
         var pageSize:uint = param2;
         var pageNum:uint = param3;
         var arr:Array = [];
         var length:int = int(pageSize);
         length = int(pageSize);
         var i:int = 0;
         while(i < length)
         {
            object = rankArr[Math.max(0,Math.min((pageNum - 1) * pageSize + i,rankArr.length - 1))];
            if(object)
            {
               arr.push({
                  "userName":object.userName,
                  "score":object.score,
                  "rank":object.rank,
                  "index":object.index,
                  "uId":"test123"
               });
               object = null;
            }
            i++;
         }
         setTimeout(function():void
         {
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "data":arr,
               "apiName":"4"
            }));
         },500);
      }
      
      public function submitScoreToRankLists(param1:uint, param2:Vector.<SubmitData>) : void
      {
      }
      
      public function getSaveData(param1:String, param2:uint) : void
      {
         if(param1 == GameData.getInstance().getLoginReturnData().getUid() && param2 == GameData.getInstance().getSaveFileData().index)
         {
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "apiName":"5",
               "data":{"data":GameData.getInstance().getSaveFileData().saveXML}
            }));
         }
         else
         {
            Part1.getInstance().stage.dispatchEvent(new RankListEvent("rankListSuccess",{
               "apiName":"5",
               "data":{"data":PKFunction.getInstance().testXML.copy()}
            }));
         }
      }
   }
}

