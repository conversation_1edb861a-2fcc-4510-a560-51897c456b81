package YJFY.ShowLogicShell
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class AbleDragSpriteLogicShell
   {
      
      protected var m_show:Sprite;
      
      protected var m_dragShow:Sprite;
      
      public function AbleDragSpriteLogicShell()
      {
         super();
      }
      
      public function setShow(param1:Sprite) : void
      {
         m_show = param1;
         m_dragShow = m_show["dragShow"];
         if(m_dragShow)
         {
            m_dragShow.addEventListener("mouseDown",mouseDownBox,false,0,true);
         }
         m_show.addEventListener("mouseUp",mouseUpBox,false,0,true);
      }
      
      public function getShow() : Sprite
      {
         return m_show;
      }
      
      public function clear() : void
      {
         m_show.stopDrag();
         if(m_dragShow)
         {
            m_dragShow.removeEventListener("mouseDown",mouseDownBox,false);
         }
         m_show.removeEventListener("mouseUp",mouseUpBox,false);
         m_show.removeEventListener("enterFrame",test,false);
         m_show = null;
      }
      
      protected function mouseDownBox(param1:MouseEvent) : void
      {
         if(m_show.parent)
         {
            m_show.parent.setChildIndex(m_show,m_show.parent.numChildren - 1);
         }
         m_show.startDrag();
         m_show.addEventListener("enterFrame",test,false,0,true);
      }
      
      protected function mouseUpBox(param1:MouseEvent) : void
      {
         m_show.stopDrag();
         m_show.removeEventListener("enterFrame",test,false);
      }
      
      protected function test(param1:Event) : void
      {
         var _loc2_:Point = null;
         if(m_show.stage)
         {
            _loc2_ = m_show.localToGlobal(new Point(m_show.mouseX,m_show.mouseY));
            if(_loc2_.x > m_show.stage.stageWidth)
            {
               m_show.dispatchEvent(new MouseEvent("mouseUp"));
               m_show.x -= 10;
            }
            if(_loc2_.x < 0)
            {
               m_show.dispatchEvent(new MouseEvent("mouseUp"));
               m_show.x += 10;
            }
            if(_loc2_.y > m_show.stage.stageHeight)
            {
               m_show.dispatchEvent(new MouseEvent("mouseUp"));
               m_show.y -= 10;
            }
            if(_loc2_.y < 0)
            {
               m_show.dispatchEvent(new MouseEvent("mouseUp"));
               m_show.y += 10;
            }
         }
      }
   }
}

