package YJFY.LevelMode5
{
   import UI.AnalogServiceHoldFunction;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.NewPrecious.PreDataInfo;
   import YJFY.GameData;
   import YJFY.GameEvent;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   import flash.text.TextField;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   
   public class EndDancerZhenhunView extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_ok:ButtonLogicShell;
      
      private var m_returnCityBtn:ButtonLogicShell;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_look:TextField;
      
      private var m_freetime:TextField;
      
      private var m_newfail:LevelEndDancerFail;
      
      private var m_timeid:int;
      
      public function EndDancerZhenhunView(param1:LevelEndDancerFail)
      {
         super();
         m_newfail = param1;
         m_ok = new ButtonLogicShell();
         m_returnCityBtn = new ButtonLogicShell();
         m_myLoader = new YJFYLoader();
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
         GameEvent.eventDispacher.addEventListener("ticketChange",onResetTicketTextHandler);
         init();
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         GameEvent.eventDispacher.removeEventListener("ticketChange",onResetTicketTextHandler);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("warningBox",sureOrCancel,true);
         clearTimeout(m_timeid);
         ClearUtil.clearObject(m_ok);
         m_ok = null;
         ClearUtil.clearObject(m_returnCityBtn);
         m_returnCityBtn = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_show);
         m_show = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_myLoader.getClass("NewGameFolder/LevelMode3/newfail.swf","zhenhunpanel",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         m_show.x = -480;
         m_show.y = -280;
         addChild(m_show);
         initshow();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         Part1.getInstance().hideGameWaitShow();
      }
      
      public function initshow() : void
      {
         m_ok.setShow(m_show["zhenhunokbtn"]);
         m_ok.setTipString("确定");
         m_returnCityBtn.setShow(m_show["zhenhunnobtn"]);
         m_returnCityBtn.setTipString("放弃");
         m_look = m_show["vipinfo"] as TextField;
         m_freetime = m_show["freetime"] as TextField;
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_look);
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_freetime);
         m_look.text = "VIP用户有使用特权(vip1-5拥有1次，vip6-7拥有2次，vip8拥有3次)";
         zhenhun();
      }
      
      private function onResetTicketTextHandler(param1:GameEvent) : void
      {
         m_timeid = setTimeout(zhenhun,1000);
      }
      
      private function zhenhun() : void
      {
         var _loc1_:int = GamingUI.getInstance().player1.vipVO.freeZhenhunNum;
         var _loc2_:int = PreDataInfo.getInstance().getUseNum();
         if(_loc1_ - _loc2_ > 0)
         {
            m_freetime.text = "您今天拥有" + (_loc1_ - _loc2_) + "次免费使用机会";
         }
         else
         {
            m_freetime.text = "您今天拥有0次免费使用机会";
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_returnCityBtn:
               m_newfail.closeZhenhun();
               break;
            case m_ok:
               usecall();
         }
      }
      
      private function usecall() : void
      {
         var _loc1_:int = GamingUI.getInstance().player1.vipVO.freeZhenhunNum;
         var _loc2_:int = PreDataInfo.getInstance().getUseNum();
         if(_loc1_ - _loc2_ > 0)
         {
            m_freetime.text = "您今天拥有" + (_loc1_ - _loc2_) + "次免费使用机会";
            m_newfail.playZhenhun();
            closeUI();
         }
         else
         {
            m_freetime.text = "您今天拥有0次免费使用机会";
            showWarningBox("是否花费" + PreDataInfo.getInstance().getMoney() + "点券购买次数",1 | 2,{
               "type":"buyzhenhun",
               "okFunction":buy2
            });
         }
      }
      
      private function buy2() : void
      {
         var price:uint = uint(PreDataInfo.getInstance().getMoney());
         var ticketId:String = String(PreDataInfo.getInstance().getTicketId());
         var dataObj:Object = {};
         dataObj["propId"] = ticketId;
         dataObj["count"] = 1;
         dataObj["price"] = price;
         dataObj["idx"] = GameData.getInstance().getSaveFileData().index;
         dataObj["tag"] = "购买仙乐镇魂";
         AnalogServiceHoldFunction.getInstance().buyByPayDataFun(dataObj,function(param1:Object):void
         {
            if(param1["propId"] != ticketId)
            {
               showWarningBox("购买物品id前后端不相同！",0);
               throw new Error("购买物品id前后端不相同！");
            }
            m_newfail.playZhenhun();
            var _loc2_:SaveTaskInfo = new SaveTaskInfo();
            _loc2_.type = "4399";
            _loc2_.isHaveData = false;
            SaveTaskList.getInstance().addData(_loc2_);
            MyFunction2.saveGame2();
            closeUI();
         },showWarningBox,WarningBoxSingle.getInstance().getTextFontSize());
      }
      
      private function closeUI() : void
      {
         clear();
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         if(stage == null)
         {
            return;
         }
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         WarningBoxSingle.getInstance().x = -110;
         WarningBoxSingle.getInstance().y = -40;
         addChild(WarningBoxSingle.getInstance());
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         else if(param1.data.detail == 2)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.cancelFunction))
            {
               (param1.data.task.cancelFunction as Function).apply(null,param1.data.task.cancelFunctionParams);
               ClearUtil.nullArr(param1.data.task.cancelFunctionParams,false,false);
               param1.data.task.cancelFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
   }
}

