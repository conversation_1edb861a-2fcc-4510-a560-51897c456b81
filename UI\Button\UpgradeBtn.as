package UI.Button
{
   import UI.Event.UIBtnEvent;
   import UI.MyFunction;
   import flash.events.MouseEvent;
   
   public class UpgradeBtn extends Btn
   {
      
      private var _isActive:Boolean;
      
      public function UpgradeBtn()
      {
         super();
         setTipString("升级技能");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new UIBtnEvent("clickUpgradeBtn"));
      }
      
      public function setBtnActive(param1:int) : void
      {
         if(param1 == 0)
         {
            setNoActive();
            mouseChildren = false;
            mouseEnabled = false;
         }
         else
         {
            setActive();
            mouseChildren = true;
            mouseEnabled = true;
         }
      }
      
      protected function setNoActive() : void
      {
         _isActive = false;
         MyFunction.getInstance().changeSaturation(this,-100);
      }
      
      protected function setActive() : void
      {
         _isActive = true;
         MyFunction.getInstance().changeSaturation(this,0);
      }
   }
}

