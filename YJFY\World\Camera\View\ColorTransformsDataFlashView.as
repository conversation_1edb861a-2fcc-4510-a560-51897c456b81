package YJFY.World.Camera.View
{
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfColorTransforms;
   import flash.display.MovieClip;
   import flash.geom.ColorTransform;
   
   public class ColorTransformsDataFlashView
   {
      
      protected var m_isMyFlash:Boolean;
      
      protected var m_view:View;
      
      protected var m_viewOriginalColorTransform:ColorTransform;
      
      protected var m_dataOfColorTransforms:DataOfColorTransforms;
      
      protected var m_currentPointFrame:int;
      
      public function ColorTransformsDataFlashView()
      {
         super();
         m_viewOriginalColorTransform;
         m_dataOfColorTransforms = new DataOfColorTransforms();
      }
      
      public function clear() : void
      {
         recover();
         m_view = null;
         m_viewOriginalColorTransform = null;
         ClearUtil.clearObject(m_dataOfColorTransforms);
         m_dataOfColorTransforms = null;
      }
      
      public function initByMovieClip(param1:MovieClip) : void
      {
         ClearUtil.clearObject(m_dataOfColorTransforms);
         m_dataOfColorTransforms = new DataOfColorTransforms();
         m_dataOfColorTransforms.initByMovieClip(param1);
      }
      
      public function initByDataOfColorTransforms(param1:DataOfColorTransforms) : void
      {
         m_dataOfColorTransforms.copy(param1);
      }
      
      public function flashView(param1:View) : void
      {
         if(m_isMyFlash)
         {
            return;
         }
         m_view = param1;
         if(m_view.getIsFlash())
         {
            return;
         }
         if(m_dataOfColorTransforms == null)
         {
            return;
         }
         m_viewOriginalColorTransform = m_view.transform.colorTransform;
         m_isMyFlash = true;
         m_view.setIsFlash(true);
         m_currentPointFrame = 1;
      }
      
      public function render() : void
      {
         if(m_isMyFlash == false)
         {
            return;
         }
         if(m_currentPointFrame <= m_dataOfColorTransforms.getColorTransformNum())
         {
            m_view.transform.colorTransform = m_dataOfColorTransforms.getColorTransformByIndex(m_currentPointFrame - 1);
            ++m_currentPointFrame;
         }
         else
         {
            recover();
         }
      }
      
      protected function recover() : void
      {
         if(m_isMyFlash)
         {
            m_view.setIsFlash(false);
         }
         m_isMyFlash = false;
         m_currentPointFrame = 1;
      }
      
      public function getIsMyFlash() : Boolean
      {
         return m_isMyFlash;
      }
   }
}

