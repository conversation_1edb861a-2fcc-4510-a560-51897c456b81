package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.BulletEntity.BulletEntity;
   import YJFY.Entity.BulletEntity.BulletEntityData;
   import YJFY.Entity.BulletEntity.BulletEntityListener;
   import YJFY.Entity.EntityFactory;
   import YJFY.Entity.IEntity;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_DragonSkill1 extends AttackSkill
   {
      
      private var m_skillMoYuYingBulletEntityData:BulletEntityData;
      
      private var m_SkillMoYuYingBulletEntitys:Vector.<BulletEntity>;
      
      private var m_skillMoYuYingBulletEntityListener:BulletEntityListener;
      
      private var m_pushEnityListener:MoYuYingBulletEntityListener;
      
      private var m_isAblePushEntity:Boolean;
      
      private var m_dragonSkill1Listeners:Vector.<IDragonSkill1Listener>;
      
      public function Skill_DragonSkill1()
      {
         super();
         m_isAblePushEntity = true;
         m_SkillMoYuYingBulletEntitys = new Vector.<BulletEntity>();
         m_pushEnityListener = new MoYuYingBulletEntityListener();
         m_pushEnityListener.pushEntityFun = listeneredPushEntity;
         m_skillMoYuYingBulletEntityListener = new BulletEntityListener();
         m_skillMoYuYingBulletEntityListener.attackSuccessFun = listenerBulletAttackSuccess;
         m_dragonSkill1Listeners = new Vector.<IDragonSkill1Listener>();
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         ClearUtil.clearObject(m_skillMoYuYingBulletEntityData);
         m_skillMoYuYingBulletEntityData = null;
         if(m_SkillMoYuYingBulletEntitys)
         {
            _loc1_ = int(m_SkillMoYuYingBulletEntitys.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_SkillMoYuYingBulletEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_SkillMoYuYingBulletEntitys[_loc2_]);
               m_SkillMoYuYingBulletEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_SkillMoYuYingBulletEntitys[_loc2_] = null;
            m_SkillMoYuYingBulletEntitys.length = 0;
            m_SkillMoYuYingBulletEntitys = null;
         }
         ClearUtil.clearObject(m_skillMoYuYingBulletEntityListener);
         m_skillMoYuYingBulletEntityListener = null;
         ClearUtil.clearObject(m_pushEnityListener);
         m_pushEnityListener = null;
         ClearUtil.nullArr(m_dragonSkill1Listeners,false,false,false);
         m_dragonSkill1Listeners = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillMoYuYingBulletEntityData = new BulletEntityData(param1.bullet[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      public function setIsAblePushEntity(param1:Boolean) : void
      {
         m_isAblePushEntity = param1;
      }
      
      public function addDragonSkill1Listener(param1:IDragonSkill1Listener) : void
      {
         m_dragonSkill1Listeners.push(param1);
      }
      
      public function removeDragonSkill1Listener(param1:IDragonSkill1Listener) : void
      {
         var _loc2_:int = int(m_dragonSkill1Listeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_dragonSkill1Listeners.splice(_loc2_,1);
            _loc2_ = int(m_dragonSkill1Listeners.indexOf(param1));
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         createMoYuBulletEntity();
         endSkill1();
      }
      
      private function createMoYuBulletEntity() : void
      {
         var _loc1_:EntityFactory = new EntityFactory();
         var _loc2_:MoYuYingBulletEntity = _loc1_.createEntity(m_skillMoYuYingBulletEntityData.getBulletEntityXML().@animalType) as MoYuYingBulletEntity;
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.addBulletEntityListener(m_skillMoYuYingBulletEntityListener);
         _loc2_.setPushEntityListener(m_pushEnityListener);
         _loc2_.initByXML(m_skillMoYuYingBulletEntityData.getBulletEntityXML());
         m_SkillMoYuYingBulletEntitys.push(_loc2_);
         ClearUtil.clearObject(_loc1_);
         _loc2_.setNewPosition(m_owner.getX(),m_owner.getY() - 10,m_owner.getZ());
         m_world.addEntity(_loc2_);
         _loc2_.moveBullet(m_owner.getShowDirection(),0);
      }
      
      protected function listeneredPushEntity(param1:MoYuYingBulletEntity, param2:IEntity) : void
      {
         var _loc5_:int = 0;
         var _loc4_:Vector.<IDragonSkill1Listener> = m_dragonSkill1Listeners.slice(0);
         var _loc3_:int = int(_loc4_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_[_loc5_].pushEntity(this,param1,param2);
            _loc4_[_loc5_] = null;
            _loc5_++;
         }
         _loc4_.length = 0;
         _loc4_ = null;
         param1.setIsAblePushEntity(m_isAblePushEntity);
      }
      
      protected function listenerBulletAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as MoYuYingBulletEntity).setAttackData(m_attackData);
      }
   }
}

