package YJFY.XydzjsLogic
{
   import YJFY.Entity.IEntity;
   import YJFY.Entity.ISubAttackEntity;
   import YJFY.GameEntity.PlayerAndPet.Player;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.XydzjsData.IEnemyXydzjs;
   
   public class AddDropProLogic
   {
      
      public function AddDropProLogic()
      {
         super();
      }
      
      public function getAddDropPro(param1:IEntity, param2:IEnemyXydzjs, param3:Player, param4:Player) : Number
      {
         var _loc5_:Number = NaN;
         if(!param1 && !param1.getExtra() && !param1.getId())
         {
            return 0;
         }
         if(param3.getAnimalEntity() == param1 || (param3 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && (param3 as PlayerXydzjs).getPet().getAnimalEntity() == param1 || Boolean((param3 as PlayerXydzjs).getUiPlayer().playerVO.automaticPetVO) && (param3 as PlayerXydzjs).getAutoAttackPet().getAnimalEntity() == param1)
         {
            _loc5_ = (param3 as PlayerXydzjs).getUiPlayer().playerVO.renPin / 1000;
         }
         else if(Boolean(param4) && (param4.getAnimalEntity() == param1 || (param4 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && (param4 as PlayerXydzjs).getPet().getAnimalEntity() == param1 || Boolean((param4 as PlayerXydzjs).getUiPlayer().playerVO.automaticPetVO) && (param4 as PlayerXydzjs).getAutoAttackPet().getAnimalEntity() == param1))
         {
            _loc5_ = (param4 as PlayerXydzjs).getUiPlayer().playerVO.renPin / 1000;
         }
         else if((param3 as PlayerXydzjs).getUiPlayer().playerVO.isHavePet() && param1.getId() == "pet18" && (param3 as PlayerXydzjs).getPet().getAnimalEntity().getId() == param1.getId())
         {
            _loc5_ = (param3 as PlayerXydzjs).getUiPlayer().playerVO.renPin / 1000;
         }
         else if(param1 is ISubAttackEntity)
         {
            _loc5_ = getAddDropPro((param1 as ISubAttackEntity).getOwner(),param2,param3,param4);
         }
         else
         {
            if(!(Boolean(param1) && param1.getExtra() is ISubAttackEntity))
            {
               throw new Error("出错了");
            }
            _loc5_ = getAddDropPro((param1.getExtra() as ISubAttackEntity).getOwner(),param2,param3,param4);
         }
         return _loc5_;
      }
   }
}

