package UI.MessageBox
{
   import UI.MiragePanel.EssenceIcon;
   
   public class ShowEssenceIconMessage
   {
      
      public function ShowEssenceIconMessage()
      {
         super();
      }
      
      public function showMessage(param1:EssenceIcon, param2:MessageBox) : void
      {
         if(param1)
         {
            param2.htmlText = MessageBoxFunction.getInstance().toHTMLText("精气：" + param1.essenceValue + "点",20);
         }
      }
   }
}

