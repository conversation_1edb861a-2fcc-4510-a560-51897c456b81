package YJFY.Skill.BossSkills
{
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.ActiveSkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class Skill_BossInvincibleSkill extends ActiveSkill
   {
      
      private const m_const_invincibleDurationMinTime:uint = 5000;
      
      private const m_const_invincibleDurationMaxTime:uint = 8000;
      
      private var m_superRotateId:String;
      
      private var m_superRotateDefinitionData:AnimationDefinitionData;
      
      private var m_superRotateAnimationPlay:AnimationShowPlayLogicShell;
      
      public var bClear:Boolean = false;
      
      public function Skill_BossInvincibleSkill()
      {
         super();
         m_superRotateAnimationPlay = new AnimationShowPlayLogicShell();
      }
      
      override public function clear() : void
      {
         m_superRotateId = null;
         ClearUtil.clearObject(m_superRotateDefinitionData);
         m_superRotateDefinitionData = null;
         if(m_owner)
         {
            m_owner.removeOtherAnimation(m_superRotateAnimationPlay);
         }
         ClearUtil.clearObject(m_superRotateAnimationPlay.getShow());
         ClearUtil.clearObject(m_superRotateAnimationPlay);
         m_superRotateAnimationPlay = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_superRotateId = String(param1.@superRotateId);
         m_superRotateDefinitionData = new AnimationDefinitionData();
         m_superRotateDefinitionData.initByXML(param1.animationDefinition.(@id == m_superRotateId)[0]);
         m_timeOfDuration = 5000 + Math.random() * (8000 - 5000);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         var _loc2_:AnimationShow = null;
         if(m_superRotateAnimationPlay.getShow() == null)
         {
            _loc2_ = new AnimationShow();
            _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_superRotateDefinitionData));
            m_superRotateAnimationPlay.setShow(_loc2_,true);
         }
         m_superRotateAnimationPlay.gotoAndPlay("start");
         m_owner.addOtherAniamtion(m_superRotateAnimationPlay,true);
         super.releaseSkill2(param1);
      }
      
      override protected function endSkill2() : void
      {
         m_owner.removeOtherAnimation(m_superRotateAnimationPlay);
         super.endSkill2();
      }
   }
}

