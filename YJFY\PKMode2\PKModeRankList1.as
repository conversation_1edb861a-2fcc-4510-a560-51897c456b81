package YJFY.PKMode2
{
   import UI.MyFunction2;
   import UI.PKUI.RankListPanel;
   import flash.display.MovieClip;
   
   public class PKModeRankList1 extends RankListPanel
   {
      
      public function PKModeRankList1()
      {
         super();
         _isTwoMode = false;
      }
      
      override public function init(param1:int, param2:Number) : void
      {
         _rankId = param1;
         m_show = MyFunction2.returnShowByClassName("PkModeRankList1") as MovieClip;
         addChild(m_show);
         super.init(param1,param2);
      }
   }
}

