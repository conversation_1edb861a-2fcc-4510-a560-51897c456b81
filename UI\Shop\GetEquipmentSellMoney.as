package UI.Shop
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI2.ProgramStartData.ProgramStartData;
   
   public class GetEquipmentSellMoney
   {
      
      public function GetEquipmentSellMoney()
      {
         super();
      }
      
      public function getEquipmentSellMoney(param1:EquipmentVO) : int
      {
         var _loc2_:int = 0;
         if(param1 is StackEquipmentVO)
         {
            _loc2_ = param1.price * ProgramStartData.getInstance().getDiscount() * (param1 as StackEquipmentVO).num;
         }
         else
         {
            _loc2_ = param1.price * ProgramStartData.getInstance().getDiscount();
         }
         return _loc2_;
      }
   }
}

