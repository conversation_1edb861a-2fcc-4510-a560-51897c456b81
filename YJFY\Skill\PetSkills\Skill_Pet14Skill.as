package YJFY.Skill.PetSkills
{
   import YJFY.World.World;
   
   public class Skill_Pet14Skill extends Skill_Pet13_14Skill
   {
      
      public function Skill_Pet14Skill()
      {
         super();
      }
      
      override public function startSkill(param1:World) : Bo<PERSON>an
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

