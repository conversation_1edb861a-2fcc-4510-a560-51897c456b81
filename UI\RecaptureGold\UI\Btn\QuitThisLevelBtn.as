package UI.RecaptureGold.UI.Btn
{
   import UI.Button.Btn;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class QuitThisLevelBtn extends Btn
   {
      
      public function QuitThisLevelBtn()
      {
         super();
         setTipString("退出本关");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("recaptureGoldGameEnd"));
      }
   }
}

