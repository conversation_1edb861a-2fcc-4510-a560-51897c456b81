package YJFY.Skill.HouyiSkills
{
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_NiaoSkill;
   import YJFY.World.World;
   
   public class Skill_HouyiSkill4 extends CuboidAreaAttackSkill_NiaoSkill
   {
      
      public function Skill_HouyiSkill4()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
   }
}

