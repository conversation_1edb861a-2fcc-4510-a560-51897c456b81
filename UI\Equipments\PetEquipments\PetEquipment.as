package UI.Equipments.PetEquipments
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.Pets.Pet;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import flash.display.Sprite;
   
   public class PetEquipment extends Equipment
   {
      
      private static const path:String = "UI.Equipments.PetEquipments.";
      
      private var _isActive:Boolean;
      
      public function PetEquipment(param1:PetEquipmentVO)
      {
         super(param1);
      }
      
      protected function setEquipmentActive(param1:Number) : void
      {
         if(param1 == 0)
         {
            setNoActive();
         }
         else
         {
            setActive();
         }
      }
      
      protected function setNoActive() : void
      {
         _isActive = false;
         MyFunction.getInstance().changeSaturation(this,-100);
      }
      
      protected function setActive() : void
      {
         _isActive = true;
         MyFunction.getInstance().changeSaturation(this,0);
      }
      
      override protected function setShow(param1:EquipmentVO) : void
      {
         var loadListener:LoadFinishListener1;
         var equipmentVO:EquipmentVO = param1;
         if(equipmentVO == null)
         {
            return;
         }
         loadListener = new LoadFinishListener1(function():void
         {
            if(_equipmentVO == null)
            {
               return;
            }
            setmg(equipmentVO.className);
            setEquipmentActive((equipmentVO as PetEquipmentVO).essentialPercent);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSources,loadListener);
      }
      
      override protected function setmg(param1:String) : void
      {
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _equipmentSprite = MyFunction2.returnShowByClassName("UI.Equipments.PetEquipments." + param1) as Sprite;
         if(_equipmentSprite == null)
         {
            trace("没有装备显示！");
            return;
         }
         addChild(_equipmentSprite);
      }
      
      override public function set equipmentVO(param1:EquipmentVO) : void
      {
         _equipmentVO = param1;
         if(param1)
         {
            setShow(_equipmentVO);
         }
         else
         {
            setmg("");
            setEquipmentActive(0);
         }
      }
      
      override public function get equipmentVO() : EquipmentVO
      {
         return _equipmentVO;
      }
      
      override public function imperfectClone() : Equipment
      {
         return new PetEquipment(equipmentVO as PetEquipmentVO);
      }
      
      override public function clone() : Equipment
      {
         var _loc2_:Pet = null;
         if((equipmentVO as PetEquipmentVO).pet)
         {
            _loc2_ = (equipmentVO as PetEquipmentVO).pet;
            _loc2_.petEquipmentVO = null;
         }
         var _loc1_:Equipment = new PetEquipment((equipmentVO as PetEquipmentVO).clone() as PetEquipmentVO);
         if(_loc2_)
         {
            _loc2_.petEquipmentVO = equipmentVO as PetEquipmentVO;
         }
         return _loc1_;
      }
   }
}

