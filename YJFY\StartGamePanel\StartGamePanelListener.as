package YJFY.StartGamePanel
{
   public class StartGamePanelListener implements IStartGamePanelListener
   {
      
      public var startGameFun:Function;
      
      public function StartGamePanelListener()
      {
         super();
      }
      
      public function clear() : void
      {
         startGameFun = null;
      }
      
      public function startGame(param1:StartGamePanel, param2:String, param3:String, param4:XML, param5:int, param6:Boolean) : void
      {
         if(Boolean(startGameFun))
         {
            startGameFun(param1,param2,param3,param4,param5,param6);
         }
      }
   }
}

