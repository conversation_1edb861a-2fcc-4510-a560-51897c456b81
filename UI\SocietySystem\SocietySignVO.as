package UI.SocietySystem
{
   import UI.DataManagerParent;
   
   public class SocietySignVO extends DataManagerParent
   {
      
      private var m_signTime:String;
      
      private var m_currentSignNum:int;
      
      private var m_maxSignNum:int;
      
      private var m_getRewardTime:String;
      
      public function SocietySignVO()
      {
         super();
         signTime = "";
         getRewardTime = "";
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      public function initFromSaveXML(param1:XML) : void
      {
         if(param1 == null)
         {
            return;
         }
         signTime = String(param1.@sT);
         currentSignNum = int(param1.@sN);
         getRewardTime = String(param1.@gT);
      }
      
      public function exportSaveXML() : XML
      {
         var _loc1_:XML = <sign />;
         var _loc2_:String = signTime ? signTime : "";
         _loc1_.@sT = _loc2_;
         _loc1_.@sN = currentSignNum;
         _loc1_.@gT = getRewardTime;
         return _loc1_;
      }
      
      public function getsignTime() : String
      {
         return signTime;
      }
      
      public function setsignTime(param1:String) : void
      {
         signTime = param1;
      }
      
      public function getCurrentSignNum() : int
      {
         return currentSignNum;
      }
      
      public function setCurrentSignNum(param1:int) : void
      {
         currentSignNum = param1;
      }
      
      public function getGetRewardTime() : String
      {
         return getRewardTime;
      }
      
      public function setGetRewardTime(param1:String) : void
      {
         getRewardTime = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.signTime = m_signTime;
         _antiwear.currentSignNum = m_currentSignNum;
         _antiwear.maxSignNum = m_maxSignNum;
         _antiwear.getRewardTime = m_getRewardTime;
      }
      
      private function get signTime() : String
      {
         return _antiwear.signTime;
      }
      
      private function set signTime(param1:String) : void
      {
         _antiwear.signTime = param1;
      }
      
      private function get currentSignNum() : int
      {
         return _antiwear.currentSignNum;
      }
      
      private function set currentSignNum(param1:int) : void
      {
         _antiwear.currentSignNum = param1;
      }
      
      private function get maxSignNum() : int
      {
         return _antiwear.maxSignNum;
      }
      
      private function set maxSignNum(param1:int) : void
      {
         _antiwear.maxSignNum = param1;
      }
      
      private function get getRewardTime() : String
      {
         return _antiwear.getRewardTime;
      }
      
      private function set getRewardTime(param1:String) : void
      {
         _antiwear.getRewardTime = param1;
      }
   }
}

