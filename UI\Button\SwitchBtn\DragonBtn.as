package UI.Button.SwitchBtn
{
   import UI.Event.UIBtnEvent;
   
   public class DragonBtn extends SwitchBtn
   {
      
      public var btnName:String;
      
      public function DragonBtn()
      {
         super();
         setTipString("白龙马");
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchToDragon"));
      }
   }
}

