package UI.Equipments.PetEquipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Pets.Pet;
   import UI.Pets.Talents.TalentVO;
   import UI.Skills.SkillVO;
   
   public class PetEquipmentVO extends EquipmentVO
   {
      
      public static const TYRANNOSAURS:String = "tyrannosaurs";
      
      public static const XIAOPIKAQIU:String = "xiaoPiKaQiu";
      
      public static const SHITOUREN:String = "shiTouRen";
      
      public static const HUOREN:String = "huoRen";
      
      public static const JIQIREN:String = "jiQiRen";
      
      public static const PROMOTE_XML_0:int = 0;
      
      public static const PROMOTE_XML_1:int = 1;
      
      public var activeSkillVO:SkillVO;
      
      public var talentVO:TalentVO;
      
      public var maxLevel:int;
      
      public var promoteXMLId:int;
      
      public var petSeries:String;
      
      public var petType:String;
      
      public var initPassiveSkillNum:int;
      
      public var passiveSkillVOs:Vector.<SkillVO> = new Vector.<SkillVO>();
      
      private var _petLevel:int;
      
      private var _addHitPet:Number = 10;
      
      private var _upgradeLevelNum:int;
      
      private var _experienceVolume:int;
      
      private var _essentialVolume:int;
      
      private var _experiencePercent:Number = 0;
      
      private var _essentialPercent:Number = 0;
      
      protected var _pet:Pet;
      
      public function PetEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
         _antiwear.petLevel = _petLevel;
         _antiwear.addHitPet = _addHitPet;
         _antiwear.upgradeLevelNum = _upgradeLevelNum;
         _antiwear.experienceVolume = _experienceVolume;
         _antiwear.essentialVolume = _essentialVolume;
         _antiwear.experiencePercent = _experiencePercent;
         _antiwear.essentialPercent = _essentialPercent;
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         if(activeSkillVO)
         {
            activeSkillVO.clear();
         }
         if(passiveSkillVOs)
         {
            _loc1_ = int(passiveSkillVOs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               passiveSkillVOs[_loc2_] = null;
               _loc2_++;
            }
            passiveSkillVOs = null;
         }
         activeSkillVO = null;
         _pet = null;
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:PetEquipmentVO = new PetEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         var _loc2_:* = undefined;
         if(_pet)
         {
            throw new Error("存在连接宠物！主动技能可能已经被提升， 现在不能clone， 要先将对应的pet的petEquipmentVO清空!");
         }
         super.cloneAttribute(param1);
         (param1 as PetEquipmentVO).upgradeLevelNum = this.upgradeLevelNum;
         (param1 as PetEquipmentVO).petLevel = this.petLevel;
         (param1 as PetEquipmentVO).addHitPet = this.addHitPet;
         (param1 as PetEquipmentVO).maxLevel = this.maxLevel;
         (param1 as PetEquipmentVO).activeSkillVO = this.activeSkillVO.clone();
         if(this.talentVO)
         {
            (param1 as PetEquipmentVO).talentVO = this.talentVO.clone();
         }
         (param1 as PetEquipmentVO).experienceVolume = this.experienceVolume;
         (param1 as PetEquipmentVO).essentialVolume = this.essentialVolume;
         (param1 as PetEquipmentVO).experiencePercent = this.experiencePercent;
         (param1 as PetEquipmentVO).petSeries = this.petSeries;
         if(this.passiveSkillVOs)
         {
            _loc2_ = new Vector.<SkillVO>();
            for each(var _loc3_ in this.passiveSkillVOs)
            {
               _loc2_.push(_loc3_.clone());
            }
            (param1 as PetEquipmentVO).passiveSkillVOs = _loc2_;
         }
         (param1 as PetEquipmentVO).essentialPercent = this.essentialPercent;
         (param1 as PetEquipmentVO).promoteXMLId = this.promoteXMLId;
         (param1 as PetEquipmentVO).petType = this.petType;
      }
      
      public function get addHitPet() : Number
      {
         return _antiwear.addHitPet;
      }
      
      public function set addHitPet(param1:Number) : void
      {
         _antiwear.addHitPet = param1;
      }
      
      public function get petLevel() : int
      {
         return _antiwear.petLevel;
      }
      
      public function set petLevel(param1:int) : void
      {
         if(Boolean(upgradeLevelNum) && param1 > upgradeLevelNum)
         {
            param1 = upgradeLevelNum;
         }
         _antiwear.petLevel = param1;
      }
      
      public function get upgradeLevelNum() : int
      {
         return _antiwear.upgradeLevelNum;
      }
      
      public function set upgradeLevelNum(param1:int) : void
      {
         _antiwear.upgradeLevelNum = param1;
      }
      
      public function get experienceVolume() : int
      {
         return _antiwear.experienceVolume;
      }
      
      public function set experienceVolume(param1:int) : void
      {
         _antiwear.experienceVolume = param1;
      }
      
      public function get essentialVolume() : int
      {
         return _antiwear.essentialVolume;
      }
      
      public function set essentialVolume(param1:int) : void
      {
         _antiwear.essentialVolume = param1;
      }
      
      public function get experiencePercent() : Number
      {
         return _antiwear.experiencePercent;
      }
      
      public function set experiencePercent(param1:Number) : void
      {
         if(param1 > 1)
         {
            _antiwear.experiencePercent = 1;
         }
         if(param1 < 0)
         {
            _antiwear.experiencePercent = 0;
         }
         else
         {
            _antiwear.experiencePercent = param1;
         }
      }
      
      public function get essentialPercent() : Number
      {
         return _antiwear.essentialPercent;
      }
      
      public function set essentialPercent(param1:Number) : void
      {
         var _loc2_:Boolean = false;
         if(param1 < 1e-7)
         {
            param1 = 0;
         }
         if(essentialPercent == 0 && param1 != 0 || essentialPercent != 0 && param1 == 0)
         {
            _loc2_ = true;
         }
         if(param1 < 0)
         {
            _antiwear.essentialPercent = 0;
         }
         else if(param1 > 1)
         {
            _antiwear.essentialPercent = 1;
         }
         else
         {
            _antiwear.essentialPercent = param1;
         }
         if(_loc2_)
         {
            if(pet)
            {
               pet.petEquipmentVO = pet.petEquipmentVO;
            }
         }
      }
      
      public function get pet() : Pet
      {
         return _pet;
      }
      
      public function set pet(param1:Pet) : void
      {
         _pet = param1;
      }
      
      public function set(param1:String, param2:String) : void
      {
         _antiwear[param1] = param2;
      }
      
      public function get(param1:String) : String
      {
         var _loc2_:String = null;
         try
         {
            _loc2_ = _antiwear[param1];
         }
         catch(e:Error)
         {
            _loc2_ = "";
         }
         return _loc2_ ? _loc2_ : "";
      }
   }
}

