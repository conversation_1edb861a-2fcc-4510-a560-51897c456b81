package UI.WorldBoss.ShakeView
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class ShakeViewFactory
   {
      
      private var _shakeViews:Array;
      
      public function ShakeViewFactory()
      {
         super();
         _shakeViews = [ShakeView,ShakeView1,ShakeViewUpAndDown,ShakeViewUpAndDown2];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(_shakeViews);
         _shakeViews = null;
      }
      
      public function createShakeView(param1:String) : ShakeView
      {
         var _loc3_:* = null;
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
      
      public function createShakeViewByXML(param1:XML) : ShakeView
      {
         var _loc6_:int = 0;
         var _loc4_:String = String(param1.@className);
         var _loc5_:ShakeView = createShakeView(_loc4_);
         var _loc2_:XMLList = param1.att;
         var _loc3_:int = int(_loc2_ ? _loc2_.length() : 0);
         _loc6_ = 0;
         while(_loc6_ < _loc3_)
         {
            _loc5_[String(_loc2_[_loc6_].@attName)](_loc2_[_loc6_].@attValue);
            _loc6_++;
         }
         return _loc5_;
      }
   }
}

