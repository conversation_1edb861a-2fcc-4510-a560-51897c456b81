package YJFY.Skill.RabbitSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow2;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.World;
   
   public class Skill_RabbitSkill4 extends CuboidAreaAttackSkill_OneSkillShow2
   {
      
      private const m_const_pushFrameLabel:String = "push";
      
      private var m_isPush:Boolean;
      
      private var m_rabbitSkill4Listeners:Vector.<IRabbitSkill4Listener>;
      
      private var m_pushImpluse:P3DVector3D;
      
      private var m_pushImpluse2:P3DVector3D;
      
      public function Skill_RabbitSkill4()
      {
         super();
         m_pushImpluse = new P3DVector3D(1500000,0,0);
         m_pushImpluse2 = new P3DVector3D();
         m_rabbitSkill4Listeners = new Vector.<IRabbitSkill4Listener>();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_pushImpluse);
         m_pushImpluse = null;
         ClearUtil.clearObject(m_pushImpluse2);
         m_pushImpluse2 = null;
         ClearUtil.nullArr(m_rabbitSkill4Listeners,false,false,false);
         m_rabbitSkill4Listeners = null;
         super.clear();
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill2(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      public function addRabbitSkill4Listener(param1:IRabbitSkill4Listener) : void
      {
         ListenerUtil.addListener(m_rabbitSkill4Listeners,param1);
      }
      
      public function removeRabbitSkill4Listener(param1:IRabbitSkill4Listener) : void
      {
         ListenerUtil.removeListener(m_rabbitSkill4Listeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPush = param1;
      }
      
      private function pushEntity() : void
      {
         var _loc3_:int = 0;
         var _loc1_:IEntity = null;
         if(m_world == null)
         {
            return;
         }
         getCuboidRangeToWorld2();
         var _loc2_:int = int(m_world.getAllEntityNum());
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ = m_world.getAllEnttiyByIndex(_loc3_);
            if(m_world.cuboidRangeIsContainsEntity(m_cuboidRangeToWorld,_loc1_))
            {
               pushEntity2(_loc1_);
            }
            _loc3_++;
         }
      }
      
      private function pushEntity2(param1:IEntity) : void
      {
         pushEntity3(param1);
         if(m_isPush)
         {
            m_pushImpluse2.multi2(m_owner.getShowDirection(),m_pushImpluse);
            param1.getBody().applyImpulse(m_pushImpluse2);
         }
      }
      
      private function pushEntity3(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IRabbitSkill4Listener> = m_rabbitSkill4Listeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         var _loc2_:* = param1;
         if("push" === _loc2_)
         {
            pushEntity();
         }
      }
   }
}

