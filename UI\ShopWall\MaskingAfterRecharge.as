package UI.ShopWall
{
   import UI.AnalogServiceHoldFunction;
   import UI.GamingUI;
   import UI.MySprite;
   import YJFY.GameEvent;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class MaskingAfterRecharge extends MySprite
   {
      
      public var sureBtn:MovieClip;
      
      public function MaskingAfterRecharge()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         sureBtn = null;
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         sureBtn.addEventListener("click",sure,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         sureBtn.removeEventListener("click",sure,false);
      }
      
      private function sure(param1:MouseEvent) : void
      {
         GameEvent.eventDispacher.dispatchEvent(new GameEvent("rechargeCallBack"));
         parent.removeChild(this);
         clear();
         AnalogServiceHoldFunction.getInstance().getBalance();
         AnalogServiceHoldFunction.getInstance().getTotalRechargedFun(null,null,null,null);
         GamingUI.getInstance().closeRechargePanel();
      }
   }
}

