package UI.newGuide.GuideMainLine
{
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MainLineTask.MainLineTaskVO;
   import UI.newGuide.NewGuidePanel;
   import YJFY.Part1;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class GuideMainBtnShell
   {
      
      public var m_newguidepanel:NewGuidePanel;
      
      private var m_guidemainpanel:GuideMainPanel;
      
      private var m_show:MovieClip;
      
      private var m_mc:MovieClip;
      
      private var m_DoneBtn:ButtonLogicShell2;
      
      private var m_GetBtn:ButtonLogicShell2;
      
      private var m_GoBtn:ButtonLogicShell2;
      
      private var m_data:MainLineTaskVO;
      
      public function GuideMainBtnShell()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_DoneBtn);
         m_DoneBtn = null;
         ClearUtil.clearObject(m_GetBtn);
         m_GetBtn = null;
         ClearUtil.clearObject(m_GoBtn);
         m_GoBtn = null;
      }
      
      public function init(param1:NewGuidePanel, param2:GuideMainPanel, param3:MovieClip) : void
      {
         m_newguidepanel = param1;
         m_guidemainpanel = param2;
         m_show = param3;
         m_mc = m_show["listpanel"];
         initParams();
      }
      
      private function initParams() : void
      {
         m_DoneBtn = new ButtonLogicShell2();
         m_GetBtn = new ButtonLogicShell2();
         m_GoBtn = new ButtonLogicShell2();
         m_DoneBtn.setShow(m_mc["lookinfo"]);
         m_GetBtn.setShow(m_mc["getbtn"]);
         m_GoBtn.setShow(m_mc["gobtn"]);
         m_DoneBtn.getShow().visible = false;
         m_GetBtn.getShow().visible = false;
         m_GoBtn.getShow().visible = false;
      }
      
      public function show() : void
      {
         m_mc.addEventListener("clickButton",clickButton,true,0,true);
         if(m_DoneBtn == null)
         {
            m_DoneBtn = new ButtonLogicShell2();
            m_DoneBtn.setShow(m_mc["donebtn"]);
            m_DoneBtn.getShow().visible = false;
         }
         if(m_GetBtn == null)
         {
            m_GetBtn = new ButtonLogicShell2();
            m_GetBtn.setShow(m_mc["getbtn"]);
            m_GetBtn.getShow().visible = false;
         }
         if(m_GoBtn == null)
         {
            m_GoBtn = new ButtonLogicShell2();
            m_GoBtn.setShow(m_mc["gobtn"]);
            m_GoBtn.getShow().visible = false;
         }
      }
      
      public function hide() : void
      {
         m_mc.removeEventListener("clickButton",clickButton,true);
         m_data = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_GetBtn:
               if(m_data)
               {
                  m_newguidepanel.refreshState();
                  GamingUI.getInstance().openNewTask(m_newguidepanel.currentType,m_data.id);
               }
               break;
            case m_GoBtn:
               if(m_data && m_data.gotoInfo)
               {
                  if(m_data.gotoInfo.type == "1" || m_data.gotoInfo.type == "2")
                  {
                     Part1.getInstance().closeCityMap();
                     Part1.getInstance().openRouteMapByTask(m_data.gotoInfo.swfpath,m_data.gotoInfo.showClassName,m_data.gotoInfo.xmlpath,m_data.gotoInfo.gotobtnname);
                  }
                  else if(m_data.gotoInfo.type == "3" || m_data.gotoInfo.type == "4")
                  {
                     GamingUI.getInstance().openWeaponMake();
                  }
                  else if(m_data.gotoInfo.type == "5")
                  {
                     GamingUI.getInstance().openHatchPanel();
                  }
                  else if(m_data.gotoInfo.type == "6")
                  {
                     GamingUI.getInstance().enterToNewSign();
                  }
                  else if(m_data.gotoInfo.type == "7")
                  {
                     GamingUI.getInstance().openMiragePanel();
                  }
                  else if(m_data.gotoInfo.type == "8")
                  {
                     GamingUI.getInstance().openAdvancePetPanel();
                  }
                  else if(m_data.gotoInfo.type == "9")
                  {
                     Part1.getInstance().openPK("onePK");
                  }
                  m_newguidepanel.refreshState();
               }
               break;
            case m_DoneBtn:
               if(m_data)
               {
                  m_newguidepanel.refreshState();
                  GamingUI.getInstance().openNewTask(m_newguidepanel.currentType,m_data.id);
               }
         }
      }
      
      public function refreshScript(param1:MainLineTaskVO) : void
      {
         m_data = param1;
         allhide();
         if(m_data)
         {
            if(m_data.isFinish)
            {
               showGet();
            }
            else
            {
               showGO();
            }
         }
      }
      
      private function allhide() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = false;
            m_DoneBtn.lock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showDone() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
      
      private function showGet() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = false;
            m_GoBtn.lock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = true;
            m_GetBtn.unLock();
         }
      }
      
      private function showGO() : void
      {
         if(m_DoneBtn)
         {
            m_DoneBtn.getShow().visible = true;
            m_DoneBtn.unLock();
         }
         if(m_GoBtn)
         {
            m_GoBtn.getShow().visible = true;
            m_GoBtn.unLock();
         }
         if(m_GetBtn)
         {
            m_GetBtn.getShow().visible = false;
            m_GetBtn.lock();
         }
      }
   }
}

