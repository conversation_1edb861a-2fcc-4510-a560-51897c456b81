package UI.SocietySystem.SocietyShop
{
   import UI.Equipments.Equipment;
   import UI.Event.UIPassiveEvent;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.Utils.ClearHelper;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SocietyShopColume extends ButtonLogicShell2
   {
      
      private var m_clear:ClearHelper;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_equipment:Equipment;
      
      private var m_eqContainer:Sprite;
      
      private var m_eqNameText:TextField;
      
      private var m_remainNumText:TextField;
      
      private var m_conIcon:Sprite;
      
      private var m_conValueText:TextField;
      
      private var m_goodData:GoodData;
      
      private var m_oneBuyEqData:OneEqSaveDataInSocietyShop;
      
      public function SocietyShopColume()
      {
         super();
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_clear);
         m_clear = null;
         super.clear();
         m_font = null;
         ClearUtil.clearObject(m_equipment);
         m_equipment = null;
         ClearUtil.clearObject(m_eqContainer);
         m_eqContainer = null;
         m_eqNameText = null;
         m_remainNumText = null;
         ClearUtil.clearObject(m_conIcon);
         m_conIcon = null;
         m_conValueText = null;
         m_goodData = null;
         m_oneBuyEqData = null;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         initShow();
      }
      
      public function setData(param1:GoodData, param2:OneEqSaveDataInSocietyShop) : void
      {
         m_goodData = param1;
         m_oneBuyEqData = param2;
         ClearUtil.clearObject(m_equipment);
         m_equipment = MyFunction2.sheatheEquipmentShell(m_goodData.getEquipmentVO());
         m_equipment.addEventListener("rollOver",onOver2,false,0,true);
         m_equipment.addEventListener("rollOut",onOut2,false,0,true);
         initShow2();
      }
      
      public function getGoodData() : GoodData
      {
         return m_goodData;
      }
      
      public function getOnebuyEqData() : OneEqSaveDataInSocietyShop
      {
         return m_oneBuyEqData;
      }
      
      private function initShow() : void
      {
         m_eqContainer = m_show["eqContainer"];
         m_eqNameText = m_show["equipmentNameText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_eqNameText);
         m_eqNameText.mouseEnabled = false;
         m_remainNumText = m_show["remainNumText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remainNumText);
         m_remainNumText.mouseEnabled = false;
         m_conIcon = m_show["conIcon"];
         m_conValueText = m_show["conValueText"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_conValueText);
         m_conValueText.mouseEnabled = false;
         initShow2();
      }
      
      private function initShow2() : void
      {
         if(m_goodData == null || m_show == null)
         {
            return;
         }
         m_eqContainer.addChild(m_equipment);
         m_eqNameText.text = m_equipment.equipmentVO.name;
         m_remainNumText.text = m_oneBuyEqData ? (m_goodData.getMaxNum() - m_oneBuyEqData.getNum()).toString() : m_goodData.getMaxNum().toString();
         m_conValueText.text = m_goodData.getConValue().toString();
         m_conValueText.width = m_conValueText.textWidth + 5;
      }
      
      override protected function onDown(param1:MouseEvent) : void
      {
         super.onDown(param1);
         changeTextField();
      }
      
      override protected function onUp(param1:MouseEvent) : void
      {
         super.onUp(param1);
         changeTextField();
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         super.onOut(param1);
         changeTextField();
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         super.onOver(param1);
         changeTextField();
      }
      
      private function changeTextField() : void
      {
      }
      
      private function onOver2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{
            "equipment":param1.currentTarget,
            "messageBoxMode":1
         }));
      }
      
      private function onOut2(param1:MouseEvent) : void
      {
         m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
   }
}

