package UI
{
   import UI.DanMedicinePanel.DanMedicineFunction;
   import UI.DanMedicinePanel.DanMedicinePanel;
   import UI.EquipmentCells.UsedEquipmentCell;
   import UI.EquipmentMakeAndUpgrade.EquipMagicInherit;
   import UI.EquipmentMakeAndUpgrade.EquipmentMagicCreat;
   import UI.EquipmentMakeAndUpgrade.IsCanPutInfo;
   import UI.EquipmentMakeAndUpgrade.MakeAndUpgradePanel;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Equipments.StackEquipments.StackEquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Event.UIPassiveEvent;
   import UI.InformationPanel.PlayerInformationPanel;
   import UI.PackageAndStorage.StorageArea;
   import UI.Pets.PetPanel;
   import UI.Shop.GetEquipmentSellMoney;
   import UI.Shop.Shop;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import UI.WarningBox.WarningBoxSingle;
   import YJFY.Part1;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class DragEquipmentEngine
   {
      
      private var _equipmentArray:Vector.<IEquipmentCell>;
      
      private var _equipmentVOs:Vector.<EquipmentVO>;
      
      private var _displayObjectContainer:Sprite;
      
      private var _targetEquipmentCell:IEquipmentCell;
      
      private var _equipment:Equipment = null;
      
      private var _isHaveChild:Boolean;
      
      private var _isHitPanel:Boolean;
      
      private var _storageArea:StorageArea;
      
      private var _storageAreas:Array;
      
      public function DragEquipmentEngine()
      {
         super();
      }
      
      public function init(param1:Vector.<IEquipmentCell>, param2:Sprite, param3:StorageArea) : void
      {
         _storageAreas = [];
         _equipmentArray = param1;
         _displayObjectContainer = param2;
         _storageArea = param3;
         _isHaveChild = false;
         _isHitPanel = false;
         addCellEventListener(param1);
      }
      
      public function addMoveTarget(param1:*) : void
      {
         _storageAreas.push(param1);
      }
      
      public function removeMoveTarget(param1:*) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < _storageAreas.length)
         {
            if(_storageAreas[_loc2_] == param1)
            {
               _storageAreas.splice(_loc2_,1);
               _loc2_--;
            }
            _loc2_++;
         }
      }
      
      public function addCellEventListener(param1:Vector.<IEquipmentCell>) : void
      {
         _equipmentArray = param1;
         for each(var _loc2_ in param1)
         {
            if(_loc2_.isHaveChild)
            {
               _loc2_.equipment.addEventListener("mouseDown",mouseDown,false,0,true);
            }
         }
         Part1.getInstance().stage.addEventListener("mouseUp",mouseUp,false,0,true);
         _displayObjectContainer.addEventListener("enterFrame",test,false,0,true);
      }
      
      public function clear() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         _equipmentArray = null;
         _displayObjectContainer = null;
         _targetEquipmentCell = null;
         _equipment = null;
         if(_storageAreas)
         {
            _loc1_ = int(_storageAreas.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _storageAreas[_loc2_] = null;
               _loc2_++;
            }
            _storageAreas = null;
         }
         _equipmentVOs = null;
      }
      
      public function deleteEventListener(param1:Vector.<IEquipmentCell>) : void
      {
         for each(var _loc2_ in param1)
         {
            if(_loc2_.isHaveChild)
            {
               _loc2_.equipment.removeEventListener("mouseDown",mouseDown,false);
            }
         }
         Part1.getInstance().stage.removeEventListener("mouseUp",mouseUp,false);
         _displayObjectContainer.removeEventListener("enterFrame",test,false);
      }
      
      public function set equipmentVOs(param1:Vector.<EquipmentVO>) : void
      {
         _equipmentVOs = param1;
      }
      
      protected function mouseDown(param1:MouseEvent) : void
      {
         var _loc3_:Point = null;
         var _loc2_:Rectangle = null;
         try
         {
            _targetEquipmentCell = param1.currentTarget.parent;
         }
         catch(error:Error)
         {
            trace(error.message);
            return;
         }
         if(WarningBoxSingle.getInstance().parent)
         {
            return;
         }
         if(_targetEquipmentCell.isHaveChild && !_isHaveChild)
         {
            _equipment = _targetEquipmentCell.equipment;
            _targetEquipmentCell.removeEquipmentVO();
            _loc3_ = new Point(_equipment.x,_equipment.y);
            _equipment.x = (_targetEquipmentCell as Cell).localToGlobal(_loc3_).x;
            _equipment.y = (_targetEquipmentCell as Cell).localToGlobal(_loc3_).y;
            _displayObjectContainer.addChild(_equipment as DisplayObject);
            _isHaveChild = true;
            _loc2_ = new Rectangle(0,0,800,500);
            _equipment.startDrag(false);
            _equipment.addEventListener("mouseMove",moveMouse,false,0,true);
         }
      }
      
      protected function mouseUp(param1:MouseEvent) : void
      {
         var _loc35_:int = 0;
         var _loc23_:int = 0;
         var _loc40_:Boolean = false;
         var _loc7_:Boolean = false;
         var _loc11_:Equipment = null;
         var _loc33_:Array = null;
         var _loc16_:DanMedicinePanel = null;
         var _loc32_:Array = null;
         var _loc9_:PlayerInformationPanel = null;
         var _loc30_:int = 0;
         var _loc8_:String = null;
         var _loc20_:EquipmentVO = null;
         var _loc4_:EquipmentVO = null;
         var _loc5_:Shop = null;
         var _loc17_:int = 0;
         var _loc15_:PetPanel = null;
         var _loc21_:PetEquipmentVO = null;
         var _loc42_:StorageArea = null;
         var _loc34_:EquipmentVO = null;
         var _loc27_:int = 0;
         var _loc19_:EquipmentVO = null;
         var _loc24_:EquipmentMagicCreat = null;
         var _loc39_:IsCanPutInfo = null;
         var _loc12_:EquipmentVO = null;
         var _loc13_:* = undefined;
         var _loc31_:EquipMagicInherit = null;
         var _loc41_:IsCanPutInfo = null;
         var _loc26_:EquipmentVO = null;
         var _loc29_:* = undefined;
         var _loc14_:MakeAndUpgradePanel = null;
         var _loc36_:IsCanPutInfo = null;
         var _loc10_:EquipmentVO = null;
         var _loc18_:* = undefined;
         var _loc38_:int = 0;
         var _loc28_:int = 0;
         var _loc6_:IEquipmentCell = null;
         var _loc2_:EquipmentVO = null;
         var _loc22_:int = int(_equipmentArray.indexOf(_targetEquipmentCell));
         if(_isHaveChild)
         {
            _equipment.removeEventListener("mouseMove",moveMouse,false);
            _loc40_ = false;
            _loc7_ = false;
            _loc11_ = _equipment;
            _displayObjectContainer.removeChild(_equipment as DisplayObject);
            _loc33_ = [];
            _loc23_ = int(_storageAreas.length);
            _loc35_ = 0;
            while(_loc35_ < _loc23_)
            {
               if((_storageAreas[_loc35_] as DisplayObject).visible && (_storageAreas[_loc35_] as DisplayObject).hitTestPoint(_equipment.x,_equipment.y))
               {
                  _loc33_.push(_storageAreas[_loc35_]);
                  _loc33_.sort(sortHitStorageArea);
               }
               _loc35_++;
            }
            if(_loc33_.length)
            {
               if(_loc33_[0] is DanMedicinePanel)
               {
                  _loc16_ = _loc33_[0] as DanMedicinePanel;
                  _loc32_ = DanMedicineFunction.getInstance().addEquipmentVOToDanMedicineEquipmentGrid(_equipment.equipmentVO.clone(),(_loc16_.parent as MyControlPanel).currentPlayer);
                  if(_loc32_[0])
                  {
                     _isHaveChild = false;
                     _equipment.stopDrag();
                     _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                     _storageArea.removeEquipmentVO(_loc22_);
                     _loc16_.dispatchEvent(new UIPassiveEvent("refreshAtt",0x020000 | 1 | 2));
                     _equipment = null;
                     return;
                  }
                  _loc16_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                     "text":_loc32_[1],
                     "flag":0
                  }));
               }
               if(_loc33_[0] is PlayerInformationPanel)
               {
                  _loc9_ = _loc33_[0] as PlayerInformationPanel;
                  for each(var _loc37_ in _loc9_.useEquipmentCellContainer.equipmentCells)
                  {
                     _loc30_ = int(_loc9_.useEquipmentCellContainer.equipmentCells.indexOf(_loc37_));
                     if(_loc11_.equipmentVO.equipmentType.match(_loc37_.cellType))
                     {
                        if(!(_loc37_.owner != _loc11_.equipmentVO.owner && _loc11_.equipmentVO.owner != ""))
                        {
                           if(_loc37_.isHaveChild)
                           {
                              _loc4_ = _loc37_.child;
                              _loc4_.playerVO = null;
                              _loc9_.removeEquipmentVO(_loc30_);
                              _loc20_ = _loc11_.equipmentVO.clone();
                              _loc20_.playerVO = _loc37_.player.playerVO;
                              _loc9_.addEquipmentVO(_loc20_,_loc30_);
                              _storageArea.removeEquipmentVO(_loc22_);
                              _storageArea.addEquipmentVO(_loc4_,_loc22_);
                              _isHaveChild = false;
                              _equipment.stopDrag();
                              _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                              GamingUI.getInstance().refresh(2);
                              _equipment = null;
                              return;
                           }
                           _loc20_ = _loc11_.equipmentVO.clone();
                           _loc20_.playerVO = _loc37_.player.playerVO;
                           _loc9_.addEquipmentVO(_loc20_,_loc30_);
                           _isHaveChild = false;
                           _equipment.stopDrag();
                           _storageArea.removeEquipmentVO(_loc22_);
                           _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                           _loc9_.dispatchEvent(new UIPassiveEvent("refreshAtt",0x0200 | 1 | 0x80));
                           _equipment = null;
                           return;
                        }
                        _loc8_ = "";
                        switch(_loc37_.owner)
                        {
                           case "SunWuKong":
                              _loc8_ = "孙悟空";
                              break;
                           case "BaiLongMa":
                              _loc8_ = "白龙马";
                              break;
                           case "ErLangShen":
                              _loc8_ = "二郎神";
                              break;
                           case "ChangE":
                              _loc8_ = "嫦娥";
                              break;
                           case "Fox":
                              _loc8_ = "灵狐";
                              break;
                           case "TieShan":
                              _loc8_ = "铁扇公主";
                              break;
                           case "Houyi":
                              _loc8_ = "后羿";
                              break;
                           case "ZiXia":
                              _loc8_ = "紫霞仙子";
                              break;
                           default:
                              _loc8_ = "";
                        }
                        _loc9_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                           "text":_loc8_ + "不能用该装备！",
                           "flag":0
                        }));
                     }
                  }
               }
            }
            if(_loc33_[0] is Shop)
            {
               _loc5_ = _loc33_[0] as Shop;
               if(_loc5_.hitTestPoint(_equipment.x,_equipment.y))
               {
                  _loc17_ = new GetEquipmentSellMoney().getEquipmentSellMoney(_equipment.equipmentVO);
                  if(_equipment.equipmentVO.isAbleSell)
                  {
                     _isHaveChild = false;
                     _equipment.stopDrag();
                     _storageArea.removeEquipmentVO(_loc22_);
                     _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                     _targetEquipmentCell.dispatchEvent(new UIDataEvent("dragSellEquipment",{"equipment":_equipment.equipmentVO}));
                     _targetEquipmentCell.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
                     _loc5_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"已卖出一个物品！",
                        "flag":1
                     }));
                     _equipment = null;
                     return;
                  }
                  _loc5_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                     "text":"出售可获得<font size=\'20\' color=\'#ff0000\'>" + _loc17_ + "</font>元宝。" + "确定要出售<font size=\'20\' color=\'#ff0000\'>" + _equipment.equipmentVO.name + "</font> 吗？",
                     "flag":1 | 2,
                     "task":{
                        "type":"sellValuableEquipment",
                        "equipment":_equipment.equipmentVO,
                        "player":GamingUI.getInstance().internalPanel.currentPlayer
                     }
                  }));
               }
            }
            if(_loc33_[0] is PetPanel)
            {
               _loc15_ = _loc33_[0] as PetPanel;
               if(_loc15_.hitTestPoint(_equipment.x,_equipment.y))
               {
                  if(_equipment.equipmentVO.equipmentType == "pet")
                  {
                     if(!_loc15_.isHavePet)
                     {
                        _loc15_.changePetVO(_equipment.equipmentVO as PetEquipmentVO);
                        _isHaveChild = false;
                        _equipment.stopDrag();
                        _storageArea.removeEquipmentVO(_loc22_);
                        _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                        _loc15_.dispatchEvent(new UIPassiveEvent("refreshAtt",0x10 | 0x40 | 0x80 | 2 | 1 | 8 | 0x0200));
                        _equipment = null;
                        return;
                     }
                     if(!(_loc15_.pet.petEquipmentVO.activeSkillVO as PetActiveSkillVO).isCD)
                     {
                        _loc21_ = _loc15_.changePetVO(_equipment.equipmentVO as PetEquipmentVO);
                        _storageArea.removeEquipmentVO(_loc22_);
                        _equipment.equipmentVO = _loc21_;
                        _storageArea.addEquipmentVO(_loc21_,_loc22_);
                        _isHaveChild = false;
                        _equipment.stopDrag();
                        _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                        _loc15_.dispatchEvent(new UIPassiveEvent("refreshAtt",0x10 | 0x40 | 0x80 | 2 | 1 | 8 | 0x0200));
                        _equipment = null;
                        return;
                     }
                     _loc15_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":"已装备的宠物技能正在CD中，暂不能装备新的宠物！",
                        "flag":0
                     }));
                  }
               }
            }
            if(_loc33_[0] is StorageArea)
            {
               _loc42_ = _loc33_[0] as StorageArea;
               if(_loc11_.equipmentVO is StackEquipmentVO)
               {
                  if(_loc42_.hitTestPoint(_loc11_.x,_loc11_.y))
                  {
                     for each(var _loc25_ in _loc42_.area.equipmentCells)
                     {
                        if(_loc25_.isHaveChild)
                        {
                           if(_loc25_.child.id == _loc11_.equipmentVO.id)
                           {
                              if(_loc42_.addEquipmentVOs(_loc11_.equipmentVO,1))
                              {
                                 _isHaveChild = false;
                                 _equipment.stopDrag();
                                 _storageArea.removeEquipmentVO(_loc22_);
                                 _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                                 _equipment = null;
                                 return;
                              }
                           }
                        }
                     }
                  }
               }
               _loc34_ = _loc11_.equipmentVO;
               for each(var _loc3_ in _loc42_.area.equipmentCells)
               {
                  if(_loc3_.hitTestPoint(_loc11_.x,_loc11_.y))
                  {
                     _loc27_ = int(_loc42_.area.equipmentCells.indexOf(_loc3_));
                     if(_loc3_.isHaveChild)
                     {
                        _loc19_ = _loc42_.removeEquipmentVO(_loc27_);
                        _loc42_.addEquipmentVO(_loc34_,_loc27_);
                        _storageArea.removeEquipmentVO(_loc22_);
                        _storageArea.addEquipmentVO(_loc19_,_loc22_);
                        _isHaveChild = false;
                        _equipment.stopDrag();
                        _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                        _loc42_.refreshEquipments();
                        _storageArea.refreshEquipments();
                        _equipment = null;
                        return;
                     }
                     _loc42_.addEquipmentVO(_loc34_,_loc27_);
                     _storageArea.removeEquipmentVO(_loc22_);
                     _isHaveChild = false;
                     _equipment.stopDrag();
                     _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                     _loc42_.refreshEquipments();
                     _storageArea.refreshEquipments();
                     _equipment = null;
                     return;
                  }
               }
            }
            if(_loc33_[0] is EquipmentMagicCreat)
            {
               _loc24_ = _loc33_[0] as EquipmentMagicCreat;
               if(_loc24_.hitTestArea && _loc24_.hitTestArea.hitTestPoint(_loc11_.x,_loc11_.y))
               {
                  _loc39_ = _loc24_.isCanPutEquipmentVO(_loc11_.equipmentVO);
                  if(_loc39_.isCanPut)
                  {
                     if(_loc11_.equipmentVO is StackEquipmentVO)
                     {
                        _loc12_ = _loc11_.equipmentVO.clone();
                        (_loc12_ as StackEquipmentVO).num = 1;
                        (_loc11_.equipmentVO as StackEquipmentVO).num -= 1;
                     }
                     else
                     {
                        _loc12_ = _loc11_.equipmentVO.clone();
                     }
                     if(_loc11_.equipmentVO is StackEquipmentVO && (_loc11_.equipmentVO as StackEquipmentVO).num)
                     {
                        MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc24_.player.playerVO.packageEquipmentVOs,_loc11_.equipmentVO);
                     }
                     else
                     {
                        MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc24_.player.playerVO.packageEquipmentVOs);
                     }
                     _loc11_.equipmentVO.isPutInOperate = true;
                     _loc13_ = _loc24_.putInEquipmentVO(_loc12_);
                     _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                     if(_loc13_)
                     {
                        ClearUtil.nullArr(_loc13_,false,false,false);
                     }
                     _isHaveChild = false;
                     _equipment.stopDrag();
                     _loc24_.dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     _equipment = null;
                     return;
                  }
                  if(_loc39_.message)
                  {
                     _loc24_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":_loc39_.message,
                        "flag":0
                     }));
                  }
               }
            }
            if(_loc33_[0] is EquipMagicInherit)
            {
               _loc31_ = _loc33_[0] as EquipMagicInherit;
               _loc41_ = _loc31_.isCanPutEquipmentVO(_loc11_.equipmentVO);
               if(_loc41_.isCanPut)
               {
                  if(_loc11_.equipmentVO is StackEquipmentVO)
                  {
                     _loc26_ = _loc11_.equipmentVO.clone();
                     (_loc26_ as StackEquipmentVO).num = 1;
                     (_loc11_.equipmentVO as StackEquipmentVO).num -= 1;
                  }
                  else
                  {
                     _loc26_ = _loc11_.equipmentVO.clone();
                  }
                  if(_loc11_.equipmentVO is StackEquipmentVO && (_loc11_.equipmentVO as StackEquipmentVO).num)
                  {
                     MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc31_.player.playerVO.packageEquipmentVOs,_loc11_.equipmentVO);
                  }
                  else
                  {
                     MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc31_.player.playerVO.packageEquipmentVOs);
                  }
                  _loc11_.equipmentVO.isPutInOperate = true;
                  _loc29_ = _loc31_.putInEquipmentVO(_loc26_);
                  _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                  if(_loc29_)
                  {
                     ClearUtil.nullArr(_loc29_,false,false,false);
                  }
                  _isHaveChild = false;
                  _equipment.stopDrag();
                  _loc31_.dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                  _equipment = null;
                  return;
               }
               if(_loc41_.message)
               {
                  _loc31_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                     "text":_loc39_.message,
                     "flag":0
                  }));
               }
            }
            if(_loc33_[0] is MakeAndUpgradePanel)
            {
               _loc14_ = _loc33_[0] as MakeAndUpgradePanel;
               if(_loc14_.hitTestArea.hitTestPoint(_loc11_.x,_loc11_.y))
               {
                  _loc36_ = _loc14_.isCanPutEquipmentVO(_loc11_.equipmentVO);
                  if(_loc36_.isCanPut)
                  {
                     if(_loc11_.equipmentVO is StackEquipmentVO)
                     {
                        _loc10_ = _loc11_.equipmentVO.clone();
                        (_loc10_ as StackEquipmentVO).num = 1;
                     }
                     else
                     {
                        _loc10_ = _loc11_.equipmentVO.clone();
                     }
                     if(_loc11_.equipmentVO is StackEquipmentVO && (_loc11_.equipmentVO as StackEquipmentVO).num)
                     {
                        MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc14_.player.playerVO.packageEquipmentVOs,_loc11_.equipmentVO);
                     }
                     else
                     {
                        MyFunction.getInstance().resethEquipmentVOVectorToPutOut(_loc14_.player.playerVO.packageEquipmentVOs);
                     }
                     _loc11_.equipmentVO.isPutInOperate = true;
                     _loc18_ = _loc14_.putInEquipmentVO(_loc10_);
                     _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                     if(_loc18_)
                     {
                        ClearUtil.nullArr(_loc18_,false,false,false);
                     }
                     _isHaveChild = false;
                     _equipment.stopDrag();
                     _loc14_.dispatchEvent(new UIPassiveEvent("refreshAtt",2));
                     _equipment = null;
                     return;
                  }
                  if(_loc36_.message)
                  {
                     _loc14_.dispatchEvent(new UIPassiveEvent("showWarningBox",{
                        "text":_loc36_.message,
                        "flag":0
                     }));
                  }
               }
            }
            _loc28_ = int(_equipmentArray.length);
            _loc38_ = 0;
            while(_loc38_ < _loc28_)
            {
               _loc6_ = _equipmentArray[_loc38_];
               if(_loc6_.hitTestPoint(_loc11_.x,_loc11_.y))
               {
                  _loc40_ = true;
                  if(_loc6_ != _targetEquipmentCell)
                  {
                     if(!_loc6_.isHaveChild)
                     {
                        _storageArea.removeEquipmentVO(_loc22_);
                        _storageArea.addEquipmentVO(_loc11_.equipmentVO,_loc38_);
                        _loc7_ = true;
                        _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                        _loc6_.dispatchEvent(new MouseEvent("rollOver"));
                        _storageArea.refreshEquipments();
                     }
                     else
                     {
                        _loc7_ = true;
                        _loc2_ = _loc6_.child;
                        _storageArea.removeEquipmentVO(_loc22_);
                        _storageArea.removeEquipmentVO(_loc38_);
                        _storageArea.addEquipmentVO(_loc11_.equipmentVO,_loc38_);
                        _storageArea.addEquipmentVO(_loc2_,_loc22_);
                        _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
                        _loc6_.dispatchEvent(new MouseEvent("rollOver"));
                        _storageArea.refreshEquipments();
                     }
                     break;
                  }
               }
               _loc38_++;
            }
            if(!_loc40_)
            {
               if(!_targetEquipmentCell.isFocus)
               {
                  _targetEquipmentCell.dispatchEvent(new MouseEvent("rollOut"));
               }
            }
            if(!_loc7_)
            {
               _targetEquipmentCell.addEquipment(_loc11_);
            }
            _isHaveChild = false;
            _equipment.stopDrag();
            _loc23_ = int(_loc33_.length);
            _loc35_ = 0;
            while(_loc35_ < _loc23_)
            {
               _loc33_[_loc35_] = null;
               _loc35_++;
            }
            _loc33_ = null;
         }
      }
      
      protected function test(param1:Event) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(_isHaveChild)
         {
            if(_displayObjectContainer.mouseX > Part1.getInstance().stage.stageWidth || _displayObjectContainer.mouseX < 0 || _displayObjectContainer.mouseY > Part1.getInstance().stage.stageHeight || _displayObjectContainer.mouseY < 0)
            {
               _displayObjectContainer.dispatchEvent(new MouseEvent("mouseUp"));
            }
            _loc2_ = int(_storageAreas.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if(_storageAreas[_loc3_] is DanMedicinePanel)
               {
                  (_storageAreas[_loc3_] as DanMedicinePanel).hideBorder();
               }
               else if(_storageAreas[_loc3_] is PlayerInformationPanel)
               {
                  (_storageAreas[_loc3_] as PlayerInformationPanel).hideBorders();
               }
               _loc3_++;
            }
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               if((_storageAreas[_loc3_] as DisplayObject).visible && (_storageAreas[_loc3_] as DisplayObject).hitTestPoint(_equipment.x,_equipment.y))
               {
                  if(_storageAreas[_loc3_] is DanMedicinePanel)
                  {
                     (_storageAreas[_loc3_] as DanMedicinePanel).showWantAddCellBorder();
                     _isHitPanel = true;
                  }
                  else if(_storageAreas[_loc3_] is PlayerInformationPanel)
                  {
                     (_storageAreas[_loc3_] as PlayerInformationPanel).showCorrespondingCell(_equipment.equipmentVO);
                     _isHitPanel = true;
                  }
               }
               _loc3_++;
            }
         }
      }
      
      private function moveMouse(param1:MouseEvent) : void
      {
         param1.currentTarget.x = _displayObjectContainer.mouseX;
         param1.currentTarget.y = _displayObjectContainer.mouseY;
      }
      
      private function sortHitStorageArea(param1:DisplayObject, param2:DisplayObject) : int
      {
         var _loc3_:DisplayObjectContainer = param1.parent;
         return _loc3_.getChildIndex(param2) - _loc3_.getChildIndex(param1);
      }
   }
}

