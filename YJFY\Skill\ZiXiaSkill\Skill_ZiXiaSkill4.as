package YJFY.Skill.ZiXiaSkill
{
   import YJFY.Entity.AnimalEntityListener;
   import YJFY.Entity.IEntity;
   import YJFY.Entity.TimeLimitEntity.TimeLimitEntity;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.AttackSkill;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OneSkillShow;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Skill.MonkeySkills.TimeLimitEntity_MonkeySkill5;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import YJFY.World.WorldLogic.GetAllEntityAndRunFun;
   
   public class Skill_ZiXiaSkill4 extends CuboidAreaAttackSkill_OneSkillShow implements IPushSkill
   {
      
      private var m_skillStartFrameLabel:String;
      
      private var m_skillAttackReachFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_isPush:Boolean;
      
      private var m_isPushEntity:Boolean;
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_originalCoordOfOwner:Coordinate;
      
      private var m_getAllEntityAndRunFun:GetAllEntityAndRunFun;
      
      private const m_const_pushStartFrameLabel:String = "pushStart";
      
      private const m_const_pushEndFrameLabel:String = "pushEnd";
      
      private const m_const_distanceToPlayer:int = 170;
      
      private const m_const_width:int = 240;
      
      private const m_const_height:int = 130;
      
      protected var m_circleDurationTime:int;
      
      protected var m_circleXML:XML;
      
      private var m_createdImageEntitys:Vector.<TimeLimitEntity>;
      
      protected var m_animalEntityListener:AnimalEntityListener;
      
      private var m_zixiaSkill4Listeners:Vector.<IZiXiaSkill4Listener>;
      
      private var m_isAbleCreateImage:Boolean;
      
      public function Skill_ZiXiaSkill4()
      {
         super();
         m_isAttackReachWhenRelease = false;
         m_isAbleCreateImage = true;
         m_createdImageEntitys = new Vector.<TimeLimitEntity>();
         m_animalEntityListener = new AnimalEntityListener();
         m_animalEntityListener.attackSuccessFun = listeneredImageAttackSuccess;
         m_zixiaSkill4Listeners = new Vector.<IZiXiaSkill4Listener>();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
         m_getAllEntityAndRunFun = new GetAllEntityAndRunFun();
         m_getAllEntityAndRunFun.fun = pushEntity;
      }
      
      override public function clear() : void
      {
         ClearUtil.nullArr(m_zixiaSkill4Listeners,false,false,false);
         m_zixiaSkill4Listeners = null;
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         ClearUtil.clearObject(m_originalCoordOfOwner);
         m_originalCoordOfOwner = null;
         ClearUtil.clearObject(m_getAllEntityAndRunFun);
         m_getAllEntityAndRunFun = null;
         ClearUtil.clearObject(m_animalEntityListener);
         m_animalEntityListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_skillStartFrameLabel = String(param1.@skillStartFrameLabel);
         m_skillAttackReachFrameLabel = String(param1.@skillAttackReachFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_circleXML = param1.image[0];
         m_circleDurationTime = int(param1.@circleDurationTime);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(m_isRun && m_isPush)
         {
            renderPushEntity();
         }
      }
      
      private function renderPushEntity() : void
      {
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         m_getAllEntityAndRunFun.getAllEntityAndRunFun(m_world,m_owner,m_cuboidRangeToWorld);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "pushStart":
               m_isPush = true;
               break;
            case "pushEnd":
               m_isPush = false;
               createImages();
               break;
            case m_skillAttackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
      
      protected function createImages() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:TimeLimitEntity_MonkeySkill5 = null;
         _loc2_ = new TimeLimitEntity_MonkeySkill5();
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.setDisappearTime(m_circleDurationTime,"^stop^");
         _loc2_.initByXML(m_circleXML);
         _loc2_.m_nPushPosition = m_owner.getShowDirection();
         if(m_owner.getShowDirection() > 0)
         {
            if(m_owner.getX() >= m_world.getCamera().getMaxX() - 300)
            {
               _loc2_.setNewPosition(m_world.getCamera().getMaxX() - 240 / 2,m_owner.getY() + 130,0);
            }
            else
            {
               _loc2_.setNewPosition(m_owner.getX() + 170,m_owner.getY() + 130,0);
            }
         }
         else if(m_owner.getX() <= m_world.getCamera().getMinX() + 300)
         {
            _loc2_.setNewPosition(m_world.getCamera().getMinX() - 240 / 2,m_owner.getY() + 130,0);
         }
         else
         {
            _loc2_.setNewPosition(m_owner.getX() - 170 - 240,m_owner.getY() + 130,0);
         }
         _loc2_.attack();
         m_world.addEntity(_loc2_);
         m_createdImageEntitys.push(_loc2_);
         _loc2_.addAnimalEntityListener(m_animalEntityListener);
      }
      
      public function setIsAbleCreateImage(param1:Boolean) : void
      {
         m_isAbleCreateImage = param1;
      }
      
      public function addZiXiaSkill4Listener(param1:IZiXiaSkill4Listener) : void
      {
         m_zixiaSkill4Listeners.push(param1);
      }
      
      public function removeZiXiaSkill4Listener(param1:IZiXiaSkill4Listener) : void
      {
         var _loc2_:int = int(m_zixiaSkill4Listeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_zixiaSkill4Listeners.splice(_loc2_,1);
            _loc2_ = int(m_zixiaSkill4Listeners.indexOf(param1));
         }
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      private function pushEntity(param1:IEntity, param2:IEntity = null) : void
      {
         pushEntity2(param1);
         if(m_isPushEntity)
         {
            if(m_isPush)
            {
               param1.applyImpulse(new P3DVector3D(m_owner.getShowDirection() * 100000,0,0));
            }
            else if(param2 is TimeLimitEntity_MonkeySkill5)
            {
               param1.applyImpulse(new P3DVector3D((param2 as TimeLimitEntity_MonkeySkill5).m_nPushPosition * 150000,0,0));
            }
         }
      }
      
      private function pushEntity2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
      
      protected function listeneredImageAttackSuccess(param1:IEntity, param2:AttackSkill, param3:IEntity) : void
      {
         pushEntity(param1,param3);
         attackSuccess2(param1,this,param3);
         (param3 as TimeLimitEntity).setAttackData(m_attackData);
      }
   }
}

