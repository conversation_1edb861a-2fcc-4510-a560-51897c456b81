package UI.RecaptureGold.RewardPanel.Btn
{
   import UI.Button.Btn;
   import UI.RecaptureGold.RecaptureGoldEvent;
   import flash.events.MouseEvent;
   
   public class GetRewardBtn extends Btn
   {
      
      public function GetRewardBtn()
      {
         super();
         setTipString("点击获取奖励");
      }
      
      override protected function clickBtn(param1:MouseEvent) : void
      {
         dispatchEvent(new RecaptureGoldEvent("clickGetRewardBtn"));
      }
   }
}

