package UI.UIInterface.OldInterface
{
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   
   public interface IEquipmentCell extends ICell
   {
      
      function addEquipmentVO(param1:EquipmentVO) : void;
      
      function addEquipment(param1:Equipment) : void;
      
      function removeEquipmentVO(param1:EquipmentVO = null) : void;
      
      function get equipmentCellBackground() : IEquipmentCellBackground;
      
      function get child() : EquipmentVO;
      
      function get equipment() : Equipment;
      
      function get id() : int;
      
      function set id(param1:int) : void;
   }
}

