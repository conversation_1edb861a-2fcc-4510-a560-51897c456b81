package UI.SignPanel.Bar
{
   import UI.ChangeBar;
   import UI.UIInterface.ISegmentedBar;
   
   public class SignProgressBar extends ChangeBar implements ISegmentedBar
   {
      
      public var signProgressBarUpPart:SignProgressBarUpPart;
      
      public var signProgressBarMask:SignProgressBarMask;
      
      public function SignProgressBar()
      {
         super();
         _width = signProgressBarUpPart.width;
         _heidth = signProgressBarUpPart.height;
      }
      
      override public function clear() : void
      {
         if(signProgressBarUpPart)
         {
            signProgressBarUpPart.clear();
         }
         if(signProgressBarMask)
         {
            signProgressBarMask.clear();
         }
         signProgressBarUpPart = null;
         signProgressBarMask = null;
      }
      
      public function change(param1:Number, param2:Boolean = false) : void
      {
         changebar(signProgressBarUpPart,signProgressBarMask,param1,0,param2);
      }
   }
}

