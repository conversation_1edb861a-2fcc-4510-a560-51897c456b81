package YJFY.Skill.BossSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OnlyPlayBody2;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_NanTaSkill extends CuboidAreaAttackSkill_OnlyPlayBody2
   {
      
      protected var m_randomPlaceXRange:int;
      
      protected var m_randomPlaceYRange:int;
      
      public function Skill_NanTaSkill()
      {
         super();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_randomPlaceXRange = int(param1.@randomPlaceXRange);
         m_randomPlaceYRange = int(param1.@randomPlaceYRange);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function addAttackEffect(param1:IEntity) : void
      {
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:IAnimationShow = null;
         var _loc3_:AnimationShowPlayLogicShell = null;
         if(m_skillAttackEffectDefinitionData)
         {
            _loc5_ = Math.random() * m_randomPlaceXRange;
            _loc2_ = Math.random() * m_randomPlaceYRange;
            _loc4_ = new AnimationShow();
            (_loc4_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_skillAttackEffectDefinitionData));
            _loc3_ = new AnimationShowPlayLogicShell();
            _loc3_.addNextStopListener(m_effectAnimationStopListener);
            _loc3_.extra = param1;
            m_effectAnimationShowPlays.push(_loc3_);
            _loc3_.setShow(_loc4_,true);
            _loc3_.gotoAndPlay("start");
            (_loc4_ as DisplayObject).x += _loc5_;
            (_loc4_ as DisplayObject).y += _loc2_;
            param1.addOtherAniamtion(_loc3_);
         }
      }
   }
}

