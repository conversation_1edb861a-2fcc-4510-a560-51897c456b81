package YJFY.Loader
{
   import YJFY.VersionControl.IVersionControl;
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.XMLLoader;
   import flash.events.Event;
   import flash.net.URLRequest;
   import flash.utils.Timer;
   import flash.utils.getTimer;
   
   public class YJFYXMLLoader extends XMLLoader
   {
      
      protected var _progressShow:IProgressShow;
      
      protected var _autoRetry:Boolean = true;
      
      protected var _retryTimes:int = 2147483647;
      
      protected var _retriedTimes:int = 0;
      
      protected var _lastProgressTime:Number = 0;
      
      protected var _timeOutTimer:Timer;
      
      public function YJFYXMLLoader(param1:*, param2:Object = null, param3:IProgressShow = null, param4:IVersionControl = null)
      {
         var _loc7_:String = null;
         _progressShow = param3;
         if(param1 is URLRequest)
         {
            _loc7_ = (param1 as URLRequest).url;
         }
         else
         {
            _loc7_ = param1;
         }
         var _loc5_:String = param4 ? param4.getLineMode().getLineMode() : "offLine";
         var _loc6_:String = param4 ? param4.getCodeMode().getCodeMode() : "false";
         if(param4.getCodeMode().getCodeMode() == "true")
         {
            _loc7_ = "CodeXML" + param4.getXMLVersionStr() + "/" + _loc7_ + "?v=" + Math.random() * 999999;
         }
         else
         {
            _loc7_ = "XML" + param4.getXMLVersionStr() + "/" + _loc7_ + "?v=" + Math.random() * 999999;
         }
         if(param1 is URLRequest)
         {
            (param1 as URLRequest).url = _loc7_;
            trace("[XML] " + URLRequest(param1).url);
         }
         else
         {
            param1 = _loc7_;
            trace("[XML] " + param1);
         }
         super(param1,param2);
      }
      
      override protected function _progressHandler(param1:Event) : void
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(_dispatchProgress)
         {
            _loc2_ = _cachedBytesLoaded;
            _loc3_ = _cachedBytesTotal;
            _calculateProgress();
            if(_loc2_ != _cachedBytesLoaded || _loc3_ != _cachedBytesTotal)
            {
               dispatchEvent(new LoaderEvent("progress",this));
               if(_progressShow)
               {
                  _progressShow.setProgress(progress,bytesLoaded,bytesTotal,loadTime,0,0);
               }
               _lastProgressTime = getTimer();
            }
         }
         else
         {
            _cacheIsDirty = true;
         }
      }
      
      public function clear(param1:Boolean = false) : void
      {
         dispose(param1);
      }
      
      override public function dispose(param1:Boolean = false) : void
      {
         super.dispose(param1);
         _progressShow = null;
      }
      
      public function setAutoRetry(param1:Boolean) : void
      {
         _autoRetry = param1;
      }
      
      override public function load(param1:Boolean = false) : void
      {
         super.load(param1);
         if(_autoRetry)
         {
            startTimeOutCheck();
         }
         else
         {
            stopTimeOutCheck();
         }
      }
      
      override protected function _errorHandler(param1:Event) : void
      {
         if(_autoRetry == false || checkToReload() == false)
         {
            super._errorHandler(param1);
         }
      }
      
      private function checkToReload() : Boolean
      {
         var _loc2_:int = 0;
         if(_retriedTimes >= _retryTimes)
         {
            stopTimeOutCheck();
            return false;
         }
         var _loc1_:int = int(_request.url.indexOf("?rN="));
         if(_loc1_ != -1)
         {
            _loc2_ = int(_request.url.substring(_loc1_ + "?rN=".length));
         }
         _loc2_++;
         if(_loc1_ != -1)
         {
            _request.url = _request.url.substring(0,_loc1_ + "?rN=".length) + _loc2_;
         }
         else
         {
            _request.url = _request.url + "?rN=" + _loc2_;
         }
         unload();
         load(true);
         ++_retriedTimes;
         return true;
      }
      
      private function onTimeOutTimer(param1:Event) : void
      {
         if(getTimer() - _lastProgressTime > 7000)
         {
            checkToReload();
         }
      }
      
      private function stopTimeOutCheck() : void
      {
         if(_timeOutTimer)
         {
            _timeOutTimer.stop();
            _timeOutTimer.removeEventListener("timer",onTimeOutTimer);
            _timeOutTimer = null;
         }
         _lastProgressTime = 0;
      }
      
      private function startTimeOutCheck() : void
      {
         if(_timeOutTimer == null)
         {
            _timeOutTimer = new Timer(2000);
            _timeOutTimer.addEventListener("timer",onTimeOutTimer);
         }
         else
         {
            _timeOutTimer.reset();
         }
         _timeOutTimer.start();
      }
      
      override protected function _completeHandler(param1:Event = null) : void
      {
         super._completeHandler(param1);
         stopTimeOutCheck();
      }
   }
}

