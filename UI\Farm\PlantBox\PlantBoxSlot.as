package UI.Farm.PlantBox
{
   import UI.EquipmentCell;
   import UI.Equipments.Equipment;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIDataEvent;
   import UI.Farm.FarmData;
   import UI.Farm.FarmEquipmentCell.FarmEquipmentCell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.UIInterface.OldInterface.IEquipmentCell;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PlantBoxSlot extends MySprite
   {
      
      public var nameText:TextField;
      
      public var priceText:TextField;
      
      public var border:Sprite;
      
      private var _equipment:Equipment;
      
      private var _equipmentCell:IEquipmentCell;
      
      private var _equipmentCellLayer:Sprite;
      
      private var _isEnbleClick:Boolean;
      
      public function PlantBoxSlot()
      {
         super();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc1_:DisplayObject = null;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            _loc1_ = getChildAt(0);
            removeChildAt(0);
            if(_loc1_.hasOwnProperty("clear"))
            {
               _loc1_["clear"]();
            }
         }
         if(_equipmentCellLayer)
         {
            while(_equipmentCellLayer.numChildren > 0)
            {
               _loc1_ = _equipmentCellLayer.getChildAt(0);
               _equipmentCellLayer.removeChildAt(0);
               if(_loc1_.hasOwnProperty("clear"))
               {
                  _loc1_["clear"]();
               }
            }
         }
         nameText = null;
         priceText = null;
         border = null;
         _equipment = null;
         if(_equipmentCell)
         {
            (_equipmentCell as EquipmentCell).clear();
         }
         _equipmentCell = null;
         _equipmentCellLayer = null;
      }
      
      public function init(param1:EquipmentVO, param2:XML, param3:XML) : void
      {
         var _loc4_:int = 0;
         var _loc6_:Class = null;
         var _loc5_:Bitmap = null;
         var _loc7_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         nameText.defaultTextFormat = new TextFormat(_loc7_.fontName,16,16777215);
         priceText.defaultTextFormat = new TextFormat(_loc7_.fontName,16,16776960);
         nameText.embedFonts = true;
         priceText.embedFonts = true;
         nameText.selectable = false;
         priceText.selectable = false;
         buttonMode = true;
         border.visible = false;
         border.mouseChildren = false;
         border.mouseEnabled = false;
         _equipmentCellLayer = new Sprite();
         addChildAt(_equipmentCellLayer,0);
         _equipment = MyFunction2.sheatheEquipmentShell(param1);
         _equipmentCell = new FarmEquipmentCell();
         _equipmentCell.x = 20 + _equipmentCell.width / 2;
         _equipmentCell.y = 10 + _equipmentCell.height / 2;
         _equipmentCellLayer.addChild(_equipmentCell as DisplayObject);
         _equipmentCell.addEquipmentVO(param1);
         nameText.text = _equipment.equipmentVO.name;
         priceText.text = _equipment.equipmentVO.price.toString();
         var _loc8_:int = int(param2.item.(@id == _equipment.equipmentVO.id)[0].@needSunLevel);
         if(_loc8_)
         {
            _loc4_ = int(param3.farmLevel[0].farmLevel.(@level == _loc8_)[0].@sunValue);
         }
         if(_loc4_ > FarmData.getInstance().currentSunValue)
         {
            _isEnbleClick = false;
            _loc6_ = MyFunction2.returnClassByClassName("PlantSunLevelLimitBitmapData_" + _loc8_);
            _loc5_ = new Bitmap(new _loc6_());
            _loc5_.x = (width - _loc5_.width) / 2 - 2;
            _loc5_.y = (height - _loc5_.height) / 2 - 32;
            addChild(_loc5_);
         }
         else
         {
            _isEnbleClick = true;
         }
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         addEventListener("rollOver",mouseOverSlot,false,0,true);
         addEventListener("rollOut",mouseOutSlot,false,0,true);
         addEventListener("click",click,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         removeEventListener("rollOver",mouseOverSlot,false);
         removeEventListener("rollOut",mouseOutSlot,false);
         removeEventListener("click",click,false);
      }
      
      protected function mouseOverSlot(param1:MouseEvent) : void
      {
         border.visible = true;
      }
      
      protected function mouseOutSlot(param1:MouseEvent) : void
      {
         border.visible = false;
      }
      
      protected function click(param1:MouseEvent) : void
      {
         if(_isEnbleClick)
         {
            dispatchEvent(new UIDataEvent("buyEquipment",{"equipment":_equipment.clone()}));
         }
      }
   }
}

