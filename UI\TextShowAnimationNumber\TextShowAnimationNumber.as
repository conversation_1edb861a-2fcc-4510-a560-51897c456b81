package UI.TextShowAnimationNumber
{
   import UI.MyFunction2;
   import flash.text.TextField;
   import flash.utils.clearInterval;
   import flash.utils.setInterval;
   
   public class TextShowAnimationNumber
   {
      
      private var _textField:TextField;
      
      private var _startValue:Number;
      
      private var _endValue:Number;
      
      private var _valueToShowStr:IValueToShowStr;
      
      private var _interval:uint;
      
      private var _value:Number;
      
      private var _peed:Number;
      
      private var _tV:Number;
      
      public function TextShowAnimationNumber(param1:TextField, param2:Number, param3:Number, param4:IValueToShowStr)
      {
         super();
         _textField = param1;
         _startValue = param2;
         _endValue = param3;
         _value = _startValue;
         _valueToShowStr = param4;
      }
      
      public function play(param1:Number, param2:Number) : void
      {
         _peed = param1;
         _tV = param2;
         _interval = setInterval(go,param1,param2);
      }
      
      public function stop() : void
      {
         if(_textField)
         {
            _textField.text = _valueToShowStr.valueToShowStrFun(_endValue);
         }
         clearInterval(_interval);
      }
      
      public function clear() : void
      {
         stop();
         _textField = null;
         _valueToShowStr = null;
      }
      
      private function go(param1:Number) : void
      {
         _value += param1;
         if(MyFunction2.judgmentValueInNormal(_value,_endValue,Math.abs(param1 / 2)))
         {
            clear();
            return;
         }
         _textField.text = _valueToShowStr.valueToShowStrFun(_value);
      }
   }
}

