package UI.ActivityPanel
{
   import YJFY.Utils.ClearUtil;
   
   public class ActivityData
   {
      
      private var m_activityOneDataObj:Object;
      
      public function ActivityData()
      {
         super();
         m_activityOneDataObj = {};
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_activityOneDataObj);
         m_activityOneDataObj = null;
      }
      
      public function initByXML(param1:XML, param2:String) : void
      {
         var _loc7_:int = 0;
         var _loc4_:String = null;
         var _loc3_:ActivityOneData = null;
         var _loc6_:XMLList = param1.children();
         var _loc5_:int = int(_loc6_ ? _loc6_.length() : 0);
         _loc7_ = 0;
         while(_loc7_ < _loc5_)
         {
            _loc3_ = new ActivityOneData();
            _loc3_.initByXML(_loc6_[_loc7_],param2);
            _loc4_ = (_loc6_[_loc7_] as XML).localName() as String;
            m_activityOneDataObj[_loc4_] = _loc3_;
            _loc7_++;
         }
      }
      
      public function getActivityOneDataByName(param1:String) : ActivityOneData
      {
         return m_activityOneDataObj[param1];
      }
   }
}

