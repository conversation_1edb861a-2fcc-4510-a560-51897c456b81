package UI.Task
{
   import UI.MyFunction2;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class TaskGoalVO
   {
      
      private var _id:int;
      
      public var name:String;
      
      public var str:String;
      
      public var owerTaskIDs:Vector.<int> = new Vector.<int>();
      
      private var _num:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function TaskGoalVO()
      {
         super();
         init();
      }
      
      private function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.id = _id;
         _antiwear.num = _num;
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         _binaryEn = null;
         _antiwear = null;
         if(owerTaskIDs)
         {
            _loc2_ = 0;
            _loc1_ = int(owerTaskIDs.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               owerTaskIDs[_loc2_] = null;
               _loc2_++;
            }
         }
         owerTaskIDs = null;
      }
      
      public function get id() : int
      {
         return _antiwear.id;
      }
      
      public function set id(param1:int) : void
      {
         _antiwear.id = param1;
      }
      
      public function get num() : int
      {
         return _antiwear.num;
      }
      
      public function set num(param1:int) : void
      {
         _antiwear.num = param1;
      }
   }
}

