package UI.Equipments.EquipmentVO
{
   import UI.GamingUI;
   import UI.MyFunction;
   import UI.XMLSingle;
   import YJFY.Utils.ClearUtil;
   
   public class EggEquipmentVO extends EquipmentVO
   {
      
      public var targetPetIdsStr:String;
      
      public function EggEquipmentVO()
      {
         super();
      }
      
      override public function init() : void
      {
         super.init();
      }
      
      override public function clone() : EquipmentVO
      {
         var _loc1_:EggEquipmentVO = new EggEquipmentVO();
         cloneAttribute(_loc1_);
         return _loc1_;
      }
      
      override protected function cloneAttribute(param1:EquipmentVO) : void
      {
         super.cloneAttribute(param1);
         (param1 as EggEquipmentVO).targetPetIdsStr = this.targetPetIdsStr;
      }
      
      public function getTargetPetVOs() : Vector.<EquipmentVO>
      {
         var _loc5_:int = 0;
         var _loc1_:EquipmentVO = null;
         var _loc2_:Vector.<int> = MyFunction.getInstance().excreteString(targetPetIdsStr);
         var _loc4_:Vector.<EquipmentVO> = new Vector.<EquipmentVO>();
         var _loc3_:int = int(_loc2_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = XMLSingle.getEquipmentVOByID(_loc2_[_loc5_],XMLSingle.getInstance().equipmentXML,GamingUI.getInstance().getNewestTimeStrFromSever());
            _loc1_.isBinding = this.isBinding;
            _loc4_.push(_loc1_);
            _loc5_++;
         }
         ClearUtil.nullArr(_loc2_);
         _loc2_ = null;
         return _loc4_;
      }
   }
}

