package UI
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Equipments.PetEquipments.PetEquipmentVO;
   import UI.Players.Player;
   import UI.Skills.PetSkills.PetActiveSkillVO;
   
   public class MainGameCall
   {
      
      public static var _instance:MainGameCall = null;
      
      private var _addNuQiValue:int;
      
      public var linXmlData1:XML = <root>
  <Data>
	<Dragon name="BaiLongMa" experiencePercent="0.2799999999999727" bloodPercent="1.0000" magicPercent="0.4167" level="3" money="49001">
	  <Package>
		<item id="10400104" petLevel="1" experiencePercent="0" essentialPercent="1.0000" talent="10103100" passiveSkills="10203101_10203103">
		  <activeSkill id="10103104" currentCDTime="0"/>
		</item>
		<item id="11000007" num="1"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </Package>
	  <Storage>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </Storage>
	  <InformationPanel>
		<item id="0"/>
		<item id="0"/>
		<item id="10200001" maxMagic="135" renPin="21"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </InformationPanel>
	  <PetPanel>
		<item id="10400103" petLevel="3" experiencePercent="0.06500000000005457" essentialPercent="1.0000" talent="10303100" passiveSkills="10203106">
		  <activeSkill id="10103103" currentCDTime="0"/>
		</item>
	  </PetPanel>
	  <Skill>
		<item id="10102100" currentCDTime="0"/>
		<item id="10102101" currentCDTime="0"/>
		<item id="10102002" currentCDTime="0"/>
		<item id="10102003" currentCDTime="0"/>
		<item id="10102004" currentCDTime="0"/>
	  </Skill>
	</Dragon>
	<Monkey name="SunWuKong" experiencePercent="0.29333333333335077" bloodPercent="1.0000" magicPercent="1.0000" level="3" money="0">
	  <Package>
		<item id="10400102" petLevel="1" experiencePercent="0" essentialPercent="1.0000" talent="10103100" passiveSkills="">
		  <activeSkill id="10103102" currentCDTime="0"/>
		</item>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </Package>
	  <Storage>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </Storage>
	  <InformationPanel>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
		<item id="0"/>
	  </InformationPanel>
	  <PetPanel/>
	  <Skill>
		<item id="10101101" currentCDTime="0"/>
		<item id="10101102" currentCDTime="0"/>
		<item id="10101004" currentCDTime="0"/>
		<item id="10101003" currentCDTime="0"/>
		<item id="10101000" currentCDTime="0"/>
	  </Skill>
	</Monkey>
	<PublicStorage>
	  <item id="0"/>
	  <item id="0"/>
	  <item id="0"/>
	  <item id="0"/>
	  <item id="0"/>
	  <item id="0"/>
	</PublicStorage>
	<TaskGoals/>
	<Task/>
	<Key>
	  <Key>
		<item key=" J"/>
		<item key="K"/>
		<item key="L"/>
		<item key="U"/>
		<item key="I"/>
		<item key="O"/>
	  </Key>
	  <Key>
		<item key=" 1"/>
		<item key="2"/>
		<item key="3"/>
		<item key="4"/>
		<item key="5"/>
		<item key="6"/>
	  </Key>
	</Key>
	<VIP oldDateGetGift=""/>
  </Data>
  <uiWord>
	<p2Name>孙悟空</p2Name>
	<SunWuKong>2</SunWuKong>
	<p1Name>白龙马</p1Name>
	<BaiLongMa>1</BaiLongMa>
	<word>白龙马-等级:3
孙悟空-等级:3</word>
	<endLessWaveNum>0</endLessWaveNum>
	<mapLevel>1</mapLevel>
  </uiWord>
  <playerData>
	<P_1>
	  <hatchTime>0</hatchTime>
	  <hatchStartTime/>
	  <hatchEnd>true</hatchEnd>
	</P_1>
	<P_2>
	  <hatchTime>0</hatchTime>
	  <hatchStartTime/>
	  <hatchEnd>true</hatchEnd>
	</P_2>
  </playerData>
  <ignoreComments>true</ignoreComments>
</root>
		;
      
      public var linXmlData2:XML = <root>
<Data>
<Dragon name="BaiLongMa" experiencePercent="0.2799999999999727" bloodPercent="1.0000" magicPercent="0.4167" level="3" money="49001">
  <Package>
	<item id="10400104" petLevel="1" experiencePercent="0" essentialPercent="1.0000" talent="10103100" passiveSkills="10203101_10203103">
	  <activeSkill id="10103104" currentCDTime="0"/>
	</item>
	<item id="11000007" num="1"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </Package>
  <Storage>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </Storage>
  <InformationPanel>
	<item id="0"/>
	<item id="0"/>
	<item id="10200001" maxMagic="135" renPin="21"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </InformationPanel>
  <PetPanel>
	<item id="10400103" petLevel="3" experiencePercent="0.06500000000005457" essentialPercent="1.0000" talent="10303100" passiveSkills="10203106">
	  <activeSkill id="10103103" currentCDTime="0"/>
	</item>
  </PetPanel>
  <Skill>
	<item id="10102100" currentCDTime="0"/>
	<item id="10102101" currentCDTime="0"/>
	<item id="10102002" currentCDTime="0"/>
	<item id="10102003" currentCDTime="0"/>
	<item id="10102004" currentCDTime="0"/>
  </Skill>
</Dragon>
<Monkey name="SunWuKong" experiencePercent="0.29333333333335077" bloodPercent="1.0000" magicPercent="1.0000" level="3" money="0">
  <Package>
	<item id="10400102" petLevel="1" experiencePercent="0" essentialPercent="1.0000" talent="10103100" passiveSkills="">
	  <activeSkill id="10103102" currentCDTime="0"/>
	</item>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </Package>
  <Storage>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </Storage>
  <InformationPanel>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
	<item id="0"/>
  </InformationPanel>
  <PetPanel/>
  <Skill>
	<item id="10101101" currentCDTime="0"/>
	<item id="10101102" currentCDTime="0"/>
	<item id="10101004" currentCDTime="0"/>
	<item id="10101003" currentCDTime="0"/>
	<item id="10101000" currentCDTime="0"/>
  </Skill>
</Monkey>
<PublicStorage>
  <item id="0"/>
  <item id="0"/>
  <item id="0"/>
  <item id="0"/>
  <item id="0"/>
  <item id="0"/>
</PublicStorage>
<TaskGoals/>
<Task/>
<Key>
  <Key>
	<item key=" J"/>
	<item key="K"/>
	<item key="L"/>
	<item key="U"/>
	<item key="I"/>
	<item key="O"/>
  </Key>
  <Key>
	<item key=" 1"/>
	<item key="2"/>
	<item key="3"/>
	<item key="4"/>
	<item key="5"/>
	<item key="6"/>
  </Key>
</Key>
<VIP oldDateGetGift=""/>
</Data>
<uiWord>
<p2Name>孙悟空</p2Name>
<SunWuKong>2</SunWuKong>
<p1Name>白龙马</p1Name>
<BaiLongMa>1</BaiLongMa>
<word>白龙马-等级:3
孙悟空-等级:3</word>
<endLessWaveNum>0</endLessWaveNum>
<mapLevel>1</mapLevel>
</uiWord>
<playerData>
<P_1>
  <hatchTime>0</hatchTime>
  <hatchStartTime/>
  <hatchEnd>true</hatchEnd>
</P_1>
<P_2>
  <hatchTime>0</hatchTime>
  <hatchStartTime/>
  <hatchEnd>true</hatchEnd>
</P_2>
</playerData>
<ignoreComments>true</ignoreComments>
</root>
	;
      
      public function MainGameCall()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗？！");
      }
      
      public static function getInstance() : MainGameCall
      {
         if(!_instance)
         {
            _instance = new MainGameCall();
         }
         return _instance;
      }
      
      public function addHatchPet(param1:PetEquipmentVO, param2:String) : EquipmentVO
      {
         var _loc5_:PetActiveSkillVO = null;
         var _loc4_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc3_:EquipmentVO = XMLSingle.getEquipment(param1.id,_loc4_);
         _loc3_.isBinding = param1.isBinding;
         var _loc6_:* = _loc3_.equipmentType;
         if("pet" !== _loc6_)
         {
            throw new Error("增加的只能是宠物。");
         }
         (_loc3_ as PetEquipmentVO).petLevel = 1;
         _loc5_ = (_loc3_ as PetEquipmentVO).activeSkillVO as PetActiveSkillVO;
         _loc5_.originalHurt = (_loc3_ as PetEquipmentVO).petLevel * _loc5_.hurCoefficient + _loc5_.additionHurt;
         _loc5_.originalPkHurt = (_loc3_ as PetEquipmentVO).petLevel * _loc5_.pkHurtCoefficient + _loc5_.additionPkHurt;
         XMLSingle.getInstance().setPetData(param1.id,(_loc3_ as PetEquipmentVO).petLevel,_loc3_ as PetEquipmentVO);
         if(param2)
         {
            if(param2 == "normal")
            {
               (_loc3_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(1);
            }
            else if(param2 == "super")
            {
               (_loc3_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(2);
            }
            else
            {
               (_loc3_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(0);
            }
         }
         else
         {
            (_loc3_ as PetEquipmentVO).talentVO = XMLSingle.getInstance().randomGetTalentVO(0);
         }
         if(PetEquipmentVO(_loc3_).initPassiveSkillNum)
         {
            (_loc3_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomFixedNumPassiveSkillVO(_loc3_ as PetEquipmentVO,(_loc3_ as PetEquipmentVO).initPassiveSkillNum);
         }
         else
         {
            (_loc3_ as PetEquipmentVO).passiveSkillVOs = XMLSingle.getInstance().randomOnePassiveSkillVOInCreatePet(_loc3_ as PetEquipmentVO);
         }
         (_loc3_ as PetEquipmentVO).essentialPercent = 1;
         MyFunction.getInstance().refreshPet(_loc3_ as PetEquipmentVO);
         (_loc3_ as PetEquipmentVO).experiencePercent = 0;
         MyFunction.getInstance().putInEquipmentVOToEquipmentVOVector(GamingUI.getInstance().internalPanel.currentPlayer.playerVO.packageEquipmentVOs,_loc3_,2);
         return _loc3_;
      }
      
      public function showWave(param1:uint, param2:uint) : void
      {
         GamingUI.getInstance().externalPanel.middlePanel.setAllWave(param2);
         GamingUI.getInstance().externalPanel.middlePanel.setWave(param1);
      }
      
      public function hideUI() : void
      {
         GamingUI.getInstance().externalPanel.visible = false;
      }
      
      public function showUI() : void
      {
         GamingUI.getInstance().externalPanel.visible = true;
      }
      
      public function uiTranToFightState() : void
      {
         GamingUI.getInstance().externalPanel.middlePanel.tranToFightState();
      }
      
      public function uiTranToNormalState() : void
      {
         GamingUI.getInstance().externalPanel.middlePanel.tranToNormalState();
      }
      
      public function get linXMLDatas() : Array
      {
         return [linXmlData1,linXmlData2];
      }
      
      public function useFastUseEq(param1:int, param2:Player) : void
      {
         switch(param2)
         {
            case GamingUI.getInstance().player1:
               if(GamingUI.getInstance().externalPanel.player1Panel)
               {
                  GamingUI.getInstance().externalPanel.player1Panel.useFastUseEq(param1);
               }
               break;
            case GamingUI.getInstance().player2:
               if(GamingUI.getInstance().externalPanel.player2Panel)
               {
                  GamingUI.getInstance().externalPanel.player2Panel.useFastUseEq(param1);
               }
         }
      }
      
      public function addNuQiValue(param1:Player) : void
      {
         param1.addNuQiValue();
      }
      
      public function addMainLineTaskGoalGameEventStr(param1:String) : void
      {
         GamingUI.getInstance().addMainLineTaskGoalGameEventStr(param1);
      }
      
      public function addEquipments(param1:Array, param2:Array, param3:Function, param4:Function) : void
      {
         var _loc11_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:* = null;
         var _loc10_:int = int(param1.length);
         var _loc7_:XML = XMLSingle.getInstance().equipmentXML;
         var _loc9_:Vector.<int> = new Vector.<int>();
         var _loc8_:Vector.<int> = new Vector.<int>();
         _loc11_ = 0;
         while(_loc11_ < _loc10_)
         {
            _loc5_ = XMLSingle.getIDByClassName(MyFunction.getInstance().unweaveClassNameString(param1[_loc11_]),_loc7_);
            _loc9_.push(_loc5_);
            _loc8_.push(param2[_loc11_]);
            _loc11_++;
         }
         MyFunction2.addEquipmentVOs(XMLSingle.getEquipmentVOsIDs(_loc9_,_loc7_,_loc8_,false),GamingUI.getInstance().player1,param3,param4,null,null);
      }
   }
}

