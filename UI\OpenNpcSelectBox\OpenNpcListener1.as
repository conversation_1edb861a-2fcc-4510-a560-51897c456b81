package UI.OpenNpcSelectBox
{
   public class OpenNpcListener1 implements IOpenNpcListener
   {
      
      public var openNpcFun:Function;
      
      public function OpenNpcListener1()
      {
         super();
      }
      
      public function clear() : void
      {
         openNpcFun = null;
      }
      
      public function openNpc(param1:String) : void
      {
         if(<PERSON><PERSON>an(openNpcFun))
         {
            openNpcFun(param1);
         }
      }
   }
}

