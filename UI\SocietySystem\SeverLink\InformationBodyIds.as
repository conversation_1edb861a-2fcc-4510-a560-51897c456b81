package UI.SocietySystem.SeverLink
{
   public class InformationBodyIds
   {
      
      public static const UP_GET_LOGIC_SERVER:int = 3044;
      
      public static const UP_VERIFY:int = 2000;
      
      public static const UP_REGISTER:int = 3000;
      
      public static const UP_LOG_OUT:int = 3002;
      
      public static const UP_HEART_BEAT_BAG:int = 3003;
      
      public static const UP_SHOW_SOCIETY_LIST:int = 3004;
      
      public static const UP_SHOW_PLAYER_THE_SOCIETY_DATA:int = 3006;
      
      public static const UP_SHOW_THE_SOCIETY_MEMBER_LIST:int = 3008;
      
      public static const UP_LEAVE_THE_SOCIETY:int = 3010;
      
      public static const UP_APPLY_FOR_JOIN:int = 3012;
      
      public static const UP_CANCEL_APPLY_FOR_JOIN:int = 3017;
      
      public static const UP_AGREE_OR_REFUSE_JOIN:int = 3015;
      
      public static const UP_SHOW_APPLY_PLAYER_LIST:int = 3019;
      
      public static const UP_CREATE_SOCIETY:int = 3022;
      
      public static const UP_DISSOLVE_THE_SOCIETY:int = 3024;
      
      public static const UP_CHANGE_ANNOUNCEMENT:int = 3031;
      
      public static const UP_REMOVE_MEMBER:int = 3033;
      
      public static const UP_SHOW_SOCIETY_LIST_OF_APPLY:int = 3036;
      
      public static const UP_CANCEL_DISSOLVE_SOCIETY:int = 3038;
      
      public static const UP_UPDATE_PLAYER_DATA:int = 3040;
      
      public static const UP_ADD_CON_VALUE:int = 3027;
      
      public static const UP_DEC_CON_VALUE:int = 3029;
      
      public static const UP_CHAT_INFOR_UP:int = 3042;
      
      public static const DOWN_GET_LOGIC_SERVER_RETURN:int = 3045;
      
      public static const DOWN_VERIFY_RETURN:int = 2001;
      
      public static const DOWN_REGISTER_SUCCESS:int = 3001;
      
      public static const DOWN_LOG_OUT:int = 3108;
      
      public static const DOWN_THE_SOCIETY_LIST:int = 3005;
      
      public static const DOWN_PLAYER_THE_SOCIETY_DATA:int = 3007;
      
      public static const DOWN_THE_SOCIETY_MEMBER_LIST:int = 3009;
      
      public static const DOWN_LEAVE_THE_SOCIETY_SUCCESS:int = 3011;
      
      public static const DOWN_HAVE_PLAYER_APPLY_FOR_JOIN:int = 3014;
      
      public static const DOWN_IS_SUCCESS_JOIN:int = 3016;
      
      public static const DOWN_PLAYER_LIST_OF_APPLY:int = 3020;
      
      public static const DOWN_CREATE_SOCIETY_SUCCESS:int = 3023;
      
      public static const DOWN_DISSOLVE_THE_SOCIETY:int = 3026;
      
      public static const DOWN_HAVE_NEW_MEMBER:int = 3021;
      
      public static const DOWN_CHANGE_ANNOUNCEMENT_RETURN:int = 3032;
      
      public static const DOWN_REMOVE_MEMBER_RETURN_TO_LEADER:int = 3034;
      
      public static const DOWN_REMOVE_MEMBER_RETURN_TO_MEMBER:int = 3035;
      
      public static const DOWN_APPLY_JOIN_RETURN:int = 3013;
      
      public static const DOWN_CANCEL_APPLY_JOIN_RETURN:int = 3018;
      
      public static const DOWN_SOCIETY_LIST_OF_APPLY:int = 3037;
      
      public static const DOWN_DISSOLVE_THE_SOCIETY_Return:int = 3025;
      
      public static const DOWN_CANCEL_DISSOLVE_SOCIETY_RETURN:int = 3039;
      
      public static const DOWN_UPDATE_PLAYER_DATA_RETURN:int = 3041;
      
      public static const DOWN_ADD_CON_VALUE_RETURN:int = 3028;
      
      public static const DOWN_DEC_CON_VALUE_RETURN:int = 3030;
      
      public static const DOWN_GET_CHAT_INFOR:int = 3043;
      
      private var m_upAndDownRalation:Object;
      
      public function InformationBodyIds()
      {
         super();
      }
   }
}

