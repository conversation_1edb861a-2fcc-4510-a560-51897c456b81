package YJFY.Utils
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class RandomUnit
   {
      
      private static var s_helpPoint:Point = new Point();
      
      public function RandomUnit()
      {
         super();
      }
      
      public static function getRandom(param1:int, param2:int) : int
      {
         var _loc4_:* = 0;
         if(param2 < param1)
         {
            _loc4_ = param1;
            param1 = param2;
            param2 = _loc4_;
         }
         return int(Math.random() * (param2 - param1) + param1);
      }
      
      public static function randomRect(param1:Rectangle, param2:Point = null) : Point
      {
         if(param2 == null)
         {
            param2 = s_helpPoint;
         }
         if(param1 == null)
         {
            throw new ArgumentError("rect is null!!!");
         }
         param2.x = getRandom(0,param1.width) + param1.x;
         param2.y = getRandom(0,param1.height) + param1.y;
         return param2;
      }
   }
}

