package YJFY.Skill.BossSkills
{
   import YJFY.Entity.IEntity;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.Point3DPhysics.P3DMath.P3DVector3D;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill_OnlyPlayBody2;
   import YJFY.Skill.ActiveSkill.IPushSkill;
   import YJFY.Skill.ActiveSkill.IPushSkillListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.DataOfPoints;
   import YJFY.Utils.ListenerUtil;
   import YJFY.World.Coordinate;
   import YJFY.World.World;
   import flash.geom.Point;
   
   public class Skill_MoveAndPushSkill extends CuboidAreaAttackSkill_OnlyPlayBody2 implements IPushSkill
   {
      
      protected var m_isPushEntity:Boolean;
      
      private var m_pushSkillListeners:Vector.<IPushSkillListener>;
      
      private var m_skillDirectionX:int;
      
      protected var m_moveDataOfPoints:DataOfPoints;
      
      protected var m_originalCoordOfOwner:Coordinate;
      
      protected var m_pushImpluse:P3DVector3D;
      
      private var m_pushImpluse2:P3DVector3D;
      
      public function Skill_MoveAndPushSkill()
      {
         super();
         m_moveDataOfPoints = new DataOfPoints();
         m_originalCoordOfOwner = new Coordinate();
         m_pushImpluse = new P3DVector3D();
         m_pushImpluse2 = new P3DVector3D();
         m_pushSkillListeners = new Vector.<IPushSkillListener>();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_moveDataOfPoints);
         m_moveDataOfPoints = null;
         ClearUtil.clearObject(m_originalCoordOfOwner);
         m_originalCoordOfOwner = null;
         ClearUtil.clearObject(m_pushImpluse);
         m_pushImpluse = null;
         ClearUtil.clearObject(m_pushImpluse2);
         m_pushImpluse2 = null;
         ClearUtil.nullArr(m_pushSkillListeners,false,false,false);
         m_pushSkillListeners = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         var _loc2_:String = String(param1.moveData[0].@swfPath);
         var _loc3_:String = String(param1.moveData[0].@className);
         m_myLoader.getClass(_loc2_,_loc3_,getMoveDataOfPointsShowSuccess,getMoveDataOfPointsShowFail);
         m_myLoader.load();
      }
      
      public function setPushData(param1:P3DVector3D) : void
      {
         if(param1)
         {
            m_pushImpluse.copy(param1);
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_originalCoordOfOwner.setTo(m_owner.getX(),m_owner.getY(),m_owner.getZ());
         m_skillDirectionX = m_owner.getShowDirection();
         m_owner.forceSetDirection(m_owner.getShowDirection(),0);
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         runAndPush();
      }
      
      protected function runAndPush() : void
      {
         var _loc1_:Point = null;
         if(m_isRun)
         {
            m_owner.setMoveSpeed(0);
            if(m_owner.getAnimationShow(m_bodyDefId).currentFrame < m_moveDataOfPoints.getPointNum())
            {
               _loc1_ = m_moveDataOfPoints.getPointByIndex(m_owner.getAnimationShow(m_bodyDefId).currentFrame);
               m_owner.setNewPosition(m_originalCoordOfOwner.getX() + m_owner.getShowDirection() * _loc1_.x,m_originalCoordOfOwner.getY() + _loc1_.y,m_originalCoordOfOwner.getZ());
            }
            renderPushEntity();
         }
      }
      
      override protected function endSkill2() : void
      {
         super.endSkill2();
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         super.attackSuccess(param1);
      }
      
      private function getMoveDataOfPointsShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_moveDataOfPoints.initByMovieClip(new _loc2_());
      }
      
      private function getMoveDataOfPointsShowFail(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      override protected function ownerSetDirection(param1:IEntity, param2:Number, param3:Number, param4:int) : void
      {
         if(m_isRun)
         {
            if(m_owner)
            {
               m_owner.forceSetDirection(m_skillDirectionX,0);
            }
         }
      }
      
      public function addPushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.addListener(m_pushSkillListeners,param1);
      }
      
      public function removePushSkillListener(param1:IPushSkillListener) : void
      {
         ListenerUtil.removeListener(m_pushSkillListeners,param1);
      }
      
      public function setIsPushEntity(param1:Boolean) : void
      {
         m_isPushEntity = param1;
      }
      
      protected function renderPushEntity() : void
      {
         var _loc3_:int = 0;
         var _loc1_:IEntity = null;
         if(m_world == null)
         {
            return;
         }
         if(m_isRun == false)
         {
            return;
         }
         getCuboidRangeToWorld2();
         var _loc2_:int = int(m_world.getAllEntityNum());
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_ = m_world.getAllEnttiyByIndex(_loc3_);
            if(m_world.cuboidRangeIsContainsEntity(m_cuboidRangeToWorld,_loc1_))
            {
               pushEntity(_loc1_);
            }
            _loc3_++;
         }
      }
      
      protected function pushEntity(param1:IEntity) : void
      {
         pushEntity2(param1);
         if(m_isPushEntity)
         {
            m_pushImpluse2.multi2(m_owner.getShowDirection(),m_pushImpluse);
            param1.getBody().applyImpulse(m_pushImpluse2);
         }
      }
      
      protected function pushEntity2(param1:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Vector.<IPushSkillListener> = m_pushSkillListeners.slice(0);
         _loc3_ = int(_loc2_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(_loc2_[_loc4_])
            {
               _loc2_[_loc4_].pushEntity(this,param1);
            }
            _loc2_[_loc4_] = null;
            _loc4_++;
         }
         _loc2_.length = 0;
         _loc2_ = null;
      }
   }
}

