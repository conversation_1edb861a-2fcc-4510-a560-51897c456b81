package UI.Farm
{
   import UI.Farm.Land.Land;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import flash.events.Event;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class InformationBoard extends MySprite
   {
      
      private static var _instance:InformationBoard = null;
      
      public var nameText:TextField;
      
      public var descriptionText:TextField;
      
      private var _wantShowTarget:IFarmShowObject;
      
      private var _equipmentXML:XML;
      
      private var _farmXML:XML;
      
      public function InformationBoard()
      {
         super();
         if(!_instance)
         {
            init();
            _instance = this;
            return;
         }
         throw new Error("实例已经存在!");
      }
      
      public static function getInstance() : InformationBoard
      {
         if(!_instance)
         {
            _instance = new InformationBoard();
         }
         return _instance;
      }
      
      public function showInformation(param1:IFarmShowObject) : void
      {
         _wantShowTarget = param1;
         addEventListener("enterFrame",refreshShow,false,0,true);
      }
      
      public function hideInformation() : void
      {
         if(hasEventListener("enterFrame"))
         {
            removeEventListener("enterFrame",refreshShow,false);
         }
         _wantShowTarget = null;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(hasEventListener("enterFrame"))
         {
            removeEventListener("enterFrame",refreshShow,false);
         }
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         nameText = null;
         descriptionText = null;
         _wantShowTarget = null;
         _equipmentXML = null;
         _farmXML = null;
         _instance = null;
      }
      
      private function init() : void
      {
         nameText.defaultTextFormat = new TextFormat(null,20,16761451);
         nameText.filters = [new GlowFilter(0,1,5,5,3,1)];
         descriptionText.embedFonts = true;
         descriptionText.defaultTextFormat = new TextFormat(new FangZhengKaTongJianTi().fontName,15,16777215);
         _equipmentXML = XMLSingle.getInstance().equipmentXML;
         _farmXML = XMLSingle.getInstance().farmXML;
      }
      
      private function refreshShow(param1:Event) : void
      {
         var _loc3_:String = null;
         var _loc2_:String = null;
         var _loc4_:Land = null;
         var _loc5_:XML = null;
         descriptionText.y = -44;
         if(_wantShowTarget is Land)
         {
            _loc4_ = _wantShowTarget as Land;
            if(_loc4_.landDrive.landVO.id)
            {
               switch(_loc4_.landDrive.landVO.state)
               {
                  case 0:
                     _loc3_ = "";
                     _loc2_ = "土地闲置中...";
                     break;
                  case 1:
                     _loc3_ = String(_equipmentXML.item.(@id == _loc4_.landDrive.landVO.equipmentIDInLand)[0].@name);
                     switch(_loc4_.landDrive.landData.plantState)
                     {
                        case "seedState":
                           _loc2_ = "种子";
                           break;
                        case "sproutState":
                           _loc2_ = "发芽";
                           break;
                        case "growthState":
                           _loc2_ = "成长期";
                           break;
                        default:
                           _loc2_ = "";
                     }
                     _loc2_ += "   " + MyFunction2.transformTimeToString(_loc4_.landDrive.landData.remainTime);
                     break;
                  case 2:
                     _loc3_ = String(_equipmentXML.item.(@id == _loc4_.landDrive.landVO.equipmentIDInLand)[0].@name);
                     _loc2_ = "已成熟！";
                     break;
                  case 3:
                     _loc3_ = String(_equipmentXML.item.(@id == _loc4_.landDrive.landVO.equipmentIDInLand)[0].@name);
                     _loc2_ = "枯萎了！";
                     break;
                  case 4:
                     _loc3_ = "";
                     _loc2_ = "土地正在恢复中...   " + MyFunction2.transformTimeToString(_loc4_.landDrive.landData.remainTime);
                     if(_loc4_.landDrive.landData.vipAccLandRecoverTime)
                     {
                        _loc2_ += "\n（VIP特权为您加速了" + (_loc4_.landDrive.landData.vipAccLandRecoverTime / 60).toFixed(2) + "分钟。）";
                        descriptionText.y = -54;
                     }
                     break;
                  default:
                     throw new Error();
               }
            }
            else
            {
               _loc3_ = "";
               _loc2_ = "土地等待您的开发...";
            }
         }
         else
         {
            _loc5_ = _farmXML.otherItem.(@id == _wantShowTarget.farmShowObjectVO.id)[0];
            _loc3_ = String(_loc5_.@name);
            _loc2_ = String(_loc5_.@description);
         }
         nameText.text = _loc3_;
         descriptionText.text = _loc2_;
      }
   }
}

