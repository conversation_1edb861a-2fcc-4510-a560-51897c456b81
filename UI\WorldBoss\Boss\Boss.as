package UI.WorldBoss.Boss
{
   import UI.Players.PlayerVO;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.AttackAnimationData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.CriticalAndDodgeData;
   import UI.WorldBoss.AnimationQueueData.AnimationData.DaZhaoAnimationData;
   import UI.WorldBoss.DaZhao.ChangeHurtDaZhao.ChangeHurtDaZhao;
   import UI.WorldBoss.DaZhao.DaZhao;
   import UI.WorldBoss.DaZhao.DaZhaoFactory;
   import UI.WorldBoss.DaZhao.NotAttackDaZhao.NotAttackDaZhao;
   import UI.WorldBoss.EnemySideEntity;
   import UI.WorldBoss.Entity;
   import UI.WorldBoss.FlashView.FlashView;
   import UI.WorldBoss.FlashView.FlashViewFactory;
   import UI.WorldBoss.PetEntity;
   import UI.WorldBoss.PlayerEntity;
   import UI.WorldBoss.ReachAttackFrameLabelListener;
   import UI.WorldBoss.ShakeView.ShakeView;
   import UI.WorldBoss.ShakeView.ShakeViewFactory;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.ShowLogicShell.PlayEndListener;
   import YJFY.StepAttackGame.IEntity;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.system.System;
   
   public class Boss extends EnemySideEntity
   {
      
      protected var m_id:String;
      
      protected var m_level:int;
      
      protected var m_attack:int;
      
      protected var m_defence:int;
      
      protected var m_criticalRate:Number;
      
      protected var m_criticalMuti:Number;
      
      protected var m_riotValue:Number;
      
      protected var m_hit:Number;
      
      protected var m_dodge:Number;
      
      protected var m_hurtVolume:int;
      
      protected var m_maxLevel:int;
      
      protected var m_xmlPath:String;
      
      protected var m_xml:XML;
      
      protected var m_daZhaos:Vector.<DaZhao>;
      
      protected var m_levelAniShow:AnimationShowPlayLogicShell;
      
      protected var m_levelNum:MultiPlaceNumLogicShell;
      
      protected var m_shakeView_daZhao:ShakeView;
      
      protected var m_flashView_daZhao:FlashView;
      
      public function Boss()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
         System.disposeXML(m_xml);
         m_xml = null;
         ClearUtil.clearObject(m_mc);
         m_mc = null;
         ClearUtil.nullArr(m_daZhaos);
         m_daZhaos = null;
         ClearUtil.clearObject(m_levelAniShow);
         m_levelAniShow = null;
         ClearUtil.clearObject(m_levelNum);
         m_levelNum = null;
      }
      
      public function initByXMLPath(param1:String) : void
      {
         m_xmlPath = param1;
         m_myLoader.getXML(param1,getXMLSuccess,getFailFun);
         m_myLoader.load();
      }
      
      public function initByXML(param1:XML) : void
      {
         m_level = 1;
         m_xml = param1;
         m_id = String(param1.@id);
         m_maxLevel = int(param1.@maxLevel);
         m_hurtVolume = int(m_xml.children()[0].@hurtVolume);
         setByLevel(m_level);
         m_myLoader.getClass(m_xml.@showSwf,m_xml.@showClassName,getShowSuccess,getFailFun);
         m_myLoader.load();
         param1 = m_xml.attackViewData[0].shakeView[0];
         var _loc2_:ShakeViewFactory = new ShakeViewFactory();
         m_shakeView_attack = _loc2_.createShakeViewByXML(param1);
         param1 = m_xml.attackViewData[0].flashView[0];
         var _loc3_:FlashViewFactory = new FlashViewFactory();
         m_flashView_attack = _loc3_.createFlashViewByXML(param1);
         param1 = m_xml.daZhaoViewData[0].shakeView[0];
         m_shakeView_daZhao = _loc2_.createShakeViewByXML(param1);
         ClearUtil.clearObject(_loc2_);
         param1 = m_xml.daZhaoViewData[0].flashView[0];
         m_flashView_daZhao = _loc3_.createFlashViewByXML(param1);
         ClearUtil.clearObject(_loc3_);
      }
      
      public function setLevel(param1:int) : void
      {
         m_level = param1;
         setByLevel(m_level);
      }
      
      override public function attack(param1:Vector.<IEntity>) : void
      {
         var _loc8_:int = 0;
         var _loc16_:int = 0;
         var _loc4_:PlayerEntity = null;
         var _loc10_:int = 0;
         var _loc5_:* = null;
         var _loc7_:AttackAnimationData = null;
         var _loc2_:DaZhao = null;
         var _loc15_:* = null;
         var _loc11_:Boolean = false;
         var _loc14_:int = 0;
         var _loc17_:CriticalAndDodgeData = null;
         var _loc12_:Vector.<int> = new Vector.<int>();
         var _loc18_:Vector.<int> = new Vector.<int>();
         var _loc3_:Vector.<int> = new Vector.<int>();
         var _loc6_:Vector.<int> = new Vector.<int>();
         var _loc13_:Vector.<Number> = new Vector.<Number>();
         var _loc9_:Vector.<CriticalAndDodgeData> = new Vector.<CriticalAndDodgeData>();
         _loc16_ = m_daZhaos ? m_daZhaos.length : 0;
         _loc8_ = 0;
         while(_loc8_ < _loc16_)
         {
            _loc2_ = m_daZhaos[_loc8_];
            if(_loc2_)
            {
               _loc11_ = _loc2_ ? _loc2_.isRunDaZhao() : false;
               if(_loc11_)
               {
                  if(_loc2_ is NotAttackDaZhao)
                  {
                     runNotAttackDaZhao(_loc2_ as NotAttackDaZhao);
                  }
                  else
                  {
                     _loc14_++;
                     if(_loc14_ > 1)
                     {
                        throw new Error("攻击性大招不能超过1个");
                     }
                     _loc15_ = _loc2_;
                     _loc7_ = new DaZhaoAnimationData();
                  }
               }
               else if(!(_loc2_ is NotAttackDaZhao))
               {
                  _loc14_++;
                  if(_loc14_ > 1)
                  {
                     throw new Error("攻击性大招不能超过1个");
                  }
                  _loc7_ = new AttackAnimationData();
               }
            }
            _loc8_++;
         }
         if(_loc7_ == null)
         {
            _loc7_ = new AttackAnimationData();
         }
         _loc16_ = int(param1.length);
         _loc8_ = 0;
         while(_loc8_ < _loc16_)
         {
            if(param1[_loc8_] is PetEntity)
            {
               throw new Error("攻击目标不能是宠物！");
            }
            _loc4_ = param1[_loc8_] as PlayerEntity;
            _loc17_ = new CriticalAndDodgeData();
            _loc12_.push(_loc4_.getPlayerVO().level);
            _loc10_ = caculateHurt(_loc4_.getPlayerVO(),_loc17_);
            if(_loc2_ is ChangeHurtDaZhao)
            {
               _loc10_ = int((_loc2_ as ChangeHurtDaZhao).changeHurt(_loc10_));
            }
            _loc4_.getPlayerVO().bloodPercent = (_loc4_.getPlayerVO().bloodPercent * _loc4_.getPlayerVO().bloodVolume - _loc10_) / _loc4_.getPlayerVO().bloodVolume;
            _loc18_.push(_loc10_);
            _loc13_.push(_loc4_.getPlayerVO().bloodPercent);
            _loc3_.push(Math.round(_loc4_.getPlayerVO().bloodPercent * _loc4_.getPlayerVO().bloodVolume));
            _loc6_.push(Math.round(_loc4_.getPlayerVO().bloodVolume));
            _loc9_.push(_loc17_);
            _loc8_++;
         }
         _loc7_.attackEntity = this;
         _loc7_.attackEntityLevel = m_level;
         _loc7_.roundNum = m_world.getGetNextStepActionEnities().getRoundNum();
         _loc7_.beAttackedEntities = param1.slice(0);
         _loc7_.beAttackedEntityLevels = _loc12_;
         _loc7_.hurts = _loc18_;
         _loc7_.criticalAndDodgeDatas = _loc9_;
         _loc7_.currentBloods = _loc3_;
         _loc7_.totalBloods = _loc6_;
         _loc7_.bloodPercents = _loc13_;
         m_animationQueueData.addAnimationData(_loc7_);
         runAfterAttack(_loc7_);
      }
      
      protected function runAfterAttack(param1:AnimationData) : void
      {
      }
      
      protected function runNotAttackDaZhao(param1:NotAttackDaZhao) : void
      {
      }
      
      protected function caculateHurt(param1:PlayerVO, param2:CriticalAndDodgeData) : int
      {
         var _loc4_:int = 0;
         var _loc3_:Number = Math.min(1,Math.max(0,m_criticalRate - param1.riotValue));
         var _loc6_:Number = Math.min(1,Math.max(0,param1.dodge - m_hit));
         var _loc5_:Number = Math.random();
         if(_loc5_ < _loc6_)
         {
            param2.isBeDodge = true;
            return 0;
         }
         _loc5_ = Math.random();
         if(_loc5_ < _loc3_)
         {
            _loc4_ = Math.round(Math.max(1,m_attack * (1 + m_criticalMuti) - param1.defence));
            param2.isCritical = true;
         }
         else
         {
            _loc4_ = Math.max(m_attack - param1.defence,1);
         }
         return _loc4_;
      }
      
      protected function setByLevel(param1:int) : void
      {
         var _loc2_:XML = m_xml.data.(@level == m_level)[0];
         m_attack = int(_loc2_.@attack);
         m_defence = int(_loc2_.@defence);
         m_criticalRate = Number(_loc2_.@criticalRate);
         m_criticalMuti = Number(_loc2_.@criticalMuti);
         m_riotValue = Number(_loc2_.@riotValue);
         m_hit = Number(_loc2_.@hit);
         m_dodge = Number(_loc2_.@dodge);
         m_hurtVolume = int(_loc2_.@hurtVolume);
         var _loc3_:DaZhaoFactory = new DaZhaoFactory();
         m_daZhaos = _loc3_.createDaZhaosByXML(_loc2_.daZhao,m_world,this);
         _loc3_.clear();
      }
      
      protected function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_xml = param1.resultXML;
         initByXML(m_xml);
         m_isReadyFight = true;
      }
      
      protected function getShowSuccess(param1:YJFYLoaderData) : void
      {
         if(Boolean(m_mc) && m_mc.getShow().parent)
         {
            m_mc.getShow().parent.removeChild(m_mc.getShow());
         }
         ClearUtil.clearObject(m_mc);
         m_mc = null;
         var _loc2_:Class = param1.resultClass;
         m_mc = new AnimationShowPlayLogicShell();
         m_mc.addNextStopListener(this);
         m_mc.setShow(new _loc2_());
         addChild(m_mc.getShow() as MovieClip);
         setLevelStr("1");
         m_isReadyPlay = true;
      }
      
      protected function getFailFun(param1:YJFYLoaderData) : void
      {
         throw new Error();
      }
      
      public function getId() : String
      {
         return m_id;
      }
      
      public function getLevel() : int
      {
         return m_level;
      }
      
      public function getAttack() : int
      {
         return m_attack;
      }
      
      public function getDefence() : int
      {
         return m_defence;
      }
      
      public function getCriticalRate() : Number
      {
         return m_criticalRate;
      }
      
      public function getCriticalMuti() : Number
      {
         return m_criticalMuti;
      }
      
      public function getRiotValue() : Number
      {
         return m_riotValue;
      }
      
      public function getHit() : Number
      {
         return m_hit;
      }
      
      public function getDodge() : Number
      {
         return m_dodge;
      }
      
      public function getHurtVolume() : Number
      {
         return m_hurtVolume;
      }
      
      public function getMaxLevel() : int
      {
         return m_maxLevel;
      }
      
      override public function isDie() : Boolean
      {
         return false;
      }
      
      override public function Die() : void
      {
      }
      
      public function playDaZhaoAnimation(param1:DaZhaoAnimationData, param2:Function) : void
      {
         var playEndListener2:PlayEndListener;
         var entity:Entity;
         var beEntities:Vector.<IEntity>;
         var i:int;
         var length:int;
         var reachAttackFrameLabelListener:ReachAttackFrameLabelListener;
         var str:String;
         var daZhaoAnimationData:DaZhaoAnimationData = param1;
         var playEndFun:Function = param2;
         var playEndListener:PlayEndListener = new PlayEndListener();
         playEndListener.mc = m_mc;
         playEndListener.playEndFun = playEndFun;
         m_mc.addNextStopListener(playEndListener);
         playEndListener2 = new PlayEndListener();
         playEndListener2.mc = m_mc;
         entity = this;
         playEndListener2.playEndFun = function():void
         {
            if(m_position)
            {
               m_position.container.addChild(entity);
            }
         };
         m_mc.addNextStopListener(playEndListener2);
         beEntities = daZhaoAnimationData.beAttackedEntities;
         length = int(beEntities.length);
         i = 0;
         while(i < length)
         {
            reachAttackFrameLabelListener = new ReachAttackFrameLabelListener();
            reachAttackFrameLabelListener.fightStage = m_fightStage;
            reachAttackFrameLabelListener.attackEntity = this;
            reachAttackFrameLabelListener.beAttackedEnttity = beEntities[i] as Entity;
            reachAttackFrameLabelListener.m_myLoader = m_myLoader;
            reachAttackFrameLabelListener.mc = m_mc;
            reachAttackFrameLabelListener.level = daZhaoAnimationData.beAttackedEntityLevels[i];
            reachAttackFrameLabelListener.hurt = daZhaoAnimationData.hurts[i];
            reachAttackFrameLabelListener.bloodPercent = daZhaoAnimationData.bloodPercents[i];
            reachAttackFrameLabelListener.bloodShowIndex = daZhaoAnimationData.bloodShowIndexs ? daZhaoAnimationData.bloodShowIndexs[i] : 0;
            reachAttackFrameLabelListener.currentBlood = daZhaoAnimationData.currentBloods[i];
            reachAttackFrameLabelListener.totalBlood = daZhaoAnimationData.totalBloods ? daZhaoAnimationData.totalBloods[i] : 0;
            reachAttackFrameLabelListener.criticalAndDodgeData = daZhaoAnimationData.criticalAndDodgeDatas[i];
            m_mc.addFrameLabelListener(reachAttackFrameLabelListener);
            i++;
         }
         if(m_fightPosition)
         {
            m_fightPosition.addChild(this);
         }
         setLevelStr(daZhaoAnimationData.attackEntityLevel);
         str = getLevelStr();
         m_mc.gotoAndPlay("daZhao");
         setLevelStr(str);
      }
      
      override public function setWorld(param1:StepAttackGameWorld) : void
      {
         var _loc3_:int = 0;
         super.setWorld(param1);
         var _loc2_:int = m_daZhaos ? m_daZhaos.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(m_daZhaos[_loc3_])
            {
               m_daZhaos[_loc3_].setWorld(param1);
            }
            _loc3_++;
         }
      }
      
      override protected function getLevelStr() : String
      {
         var _loc1_:String = "";
         if(m_levelNum)
         {
            _loc1_ = m_levelNum.getNum().toString();
         }
         return _loc1_;
      }
      
      override public function setLevelStr(param1:*) : void
      {
         var listener:PlayEndListener;
         var levelStr:* = param1;
         var oldStr:String = getLevelStr();
         if(oldStr == levelStr)
         {
            return;
         }
         if(Boolean(m_mc) && m_mc.getShow() && m_mc.getShow()["card"])
         {
            if(m_levelAniShow)
            {
               listener = new PlayEndListener();
               listener.mc = m_levelAniShow;
               listener.playEndFun = function():void
               {
                  ClearUtil.clearObject(m_levelAniShow);
                  ClearUtil.clearObject(m_levelNum);
                  if(m_mc.getShow()["card"].hasOwnProperty("levelAniShow"))
                  {
                     m_levelAniShow = new AnimationShowPlayLogicShell();
                     m_levelAniShow.setShow(m_mc.getShow()["card"]["levelAniShow"]);
                     m_levelNum = new MultiPlaceNumLogicShell();
                     m_levelNum.setShow(m_levelAniShow.getShow()["num"]);
                     m_levelNum.showNum(levelStr);
                     m_levelAniShow.gotoAndPlay("in");
                  }
               };
               m_levelAniShow.addNextStopListener(listener);
               m_levelAniShow.gotoAndPlay("out");
            }
            else
            {
               ClearUtil.clearObject(m_levelAniShow);
               ClearUtil.clearObject(m_levelNum);
               if(m_mc.getShow()["card"].hasOwnProperty("levelAniShow"))
               {
                  m_levelAniShow = new AnimationShowPlayLogicShell();
                  m_levelAniShow.setShow(m_mc.getShow()["card"]["levelAniShow"]);
                  m_levelNum = new MultiPlaceNumLogicShell();
                  m_levelNum.setShow(m_levelAniShow.getShow()["num"]);
                  m_levelNum.showNum(levelStr);
                  m_levelAniShow.gotoAndPlay("in");
               }
            }
         }
      }
      
      public function playDaZhaoViewChange() : void
      {
         if(m_view == null)
         {
            return;
         }
         if(m_shakeView_daZhao)
         {
            m_shakeView_daZhao.shakeView(m_view);
         }
         if(m_flashView_daZhao)
         {
            m_flashView_daZhao.flashView(m_view);
         }
      }
   }
}

