package UI.Utils.LoadQueue
{
   import GM_UI.GMData;
   import UI.Utils.StringUtil;
   import YJFY.LoadPart;
   import YJFY.Utils.ClearUtil;
   
   public class LoadQueue
   {
      
      private var m_wantLoadArrs:Vector.<Array>;
      
      private var m_loadFinishLiteners:Vector.<ILoadFinishListener>;
      
      private var m_loadedArr:Vector.<String>;
      
      private var m_loadWay:String;
      
      public function LoadQueue()
      {
         super();
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_wantLoadArrs);
         m_wantLoadArrs = null;
         ClearUtil.nullArr(m_loadFinishLiteners);
         m_loadFinishLiteners = null;
         ClearUtil.nullArr(m_loadedArr);
         m_loadedArr = null;
      }
      
      public function load(param1:Array, param2:ILoadFinishListener, param3:String = "") : void
      {
         if(param3 != "allShade")
         {
            param3 = "notAllShade";
         }
         m_loadWay = param3;
         var _loc4_:StringUtil = new StringUtil();
         param1 = _loc4_.cloneStrings(param1);
         if(m_wantLoadArrs == null || m_wantLoadArrs.length == 0)
         {
            m_wantLoadArrs = new Vector.<Array>();
            m_loadFinishLiteners = new Vector.<ILoadFinishListener>();
            m_wantLoadArrs.push(param1);
            m_loadFinishLiteners.push(param2);
            if(isLoadedStrs(m_wantLoadArrs[0]) == false && GMData.getInstance().isGMApplication == false)
            {
               if(m_loadWay == "notAllShade")
               {
                  LoadPart.getInstance().loadParts2(loadOneFinish,m_wantLoadArrs[0]);
               }
               else
               {
                  LoadPart.getInstance().loadParts1(loadOneFinish,m_wantLoadArrs[0]);
               }
            }
            else
            {
               loadOneFinish();
            }
         }
         else
         {
            m_wantLoadArrs.push(param1);
            m_loadFinishLiteners.push(param2);
         }
      }
      
      public function unLoad(param1:Array) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = param1 ? param1.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            LoadPart.getInstance().unLoadOnePart(param1[_loc3_]);
            removeLoadedStr(param1[_loc3_]);
            _loc3_++;
         }
      }
      
      private function loadOneFinish() : void
      {
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:* = undefined;
         if(m_wantLoadArrs == null || m_wantLoadArrs.length == 0)
         {
            throw new Error();
         }
         if(m_loadFinishLiteners == null || m_loadFinishLiteners.length == 0)
         {
            throw new Error();
         }
         var _loc2_:Vector.<Array> = m_wantLoadArrs.splice(0,1);
         addLoadedStrs(_loc2_[0]);
         ClearUtil.nullArr(_loc2_);
         if(m_wantLoadArrs.length == 0)
         {
            _loc1_ = m_loadFinishLiteners;
            m_loadFinishLiteners = null;
            m_wantLoadArrs = null;
            _loc3_ = _loc1_ ? _loc1_.length : 0;
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               if(_loc1_[_loc4_])
               {
                  _loc1_[_loc4_].loadFinish();
               }
               _loc4_++;
            }
            ClearUtil.nullArr(_loc1_,false,false,false);
            _loc1_ = null;
         }
         else if(isLoadedStrs(m_wantLoadArrs[0]) == false && GMData.getInstance().isGMApplication == false)
         {
            if(m_loadWay == "notAllShade")
            {
               LoadPart.getInstance().loadParts2(loadOneFinish,m_wantLoadArrs[0]);
            }
            else
            {
               LoadPart.getInstance().loadParts1(loadOneFinish,m_wantLoadArrs[0]);
            }
         }
         else
         {
            loadOneFinish();
         }
      }
      
      private function isLoadedStrs(param1:Array) : Boolean
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = param1 ? param1.length : 0;
         var _loc3_:* = true;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            if(param1[_loc4_] != null)
            {
               if(_loc3_)
               {
                  _loc3_ = isLoadedStr(param1[_loc4_]) != -1;
               }
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      private function addLoadedStrs(param1:Array) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc2_ = param1 ? param1.length : 0;
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            addLoadedStr(param1[_loc3_]);
            _loc3_++;
         }
      }
      
      private function addLoadedStr(param1:String) : void
      {
         if(isLoadedStr(param1) == -1)
         {
            if(m_loadedArr == null)
            {
               m_loadedArr = new Vector.<String>();
            }
            m_loadedArr.push(param1);
         }
      }
      
      private function removeLoadedStr(param1:String) : void
      {
         var _loc2_:int = isLoadedStr(param1);
         while(_loc2_ != -1)
         {
            m_loadedArr.splice(_loc2_,1);
            _loc2_ = isLoadedStr(param1);
         }
      }
      
      private function isLoadedStr(param1:String) : int
      {
         if(m_loadedArr == null)
         {
            return -1;
         }
         return int(m_loadedArr.indexOf(param1));
      }
   }
}

