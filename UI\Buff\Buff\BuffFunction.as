package UI.Buff.Buff
{
   import GM_UI.GMData;
   import UI.Buff.Buff.AllTimeBuff.AllTimeBuffVO;
   import UI.Buff.Buff.OnlyOnLineBuff.OnlyOnLineBuffVO;
   import UI.Buff.BuffData;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.Privilege.PrivilegeVO;
   import UI.Protect.ProtectData;
   import UI.XMLSingle;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class BuffFunction
   {
      
      private static var _instance:BuffFunction = null;
      
      public function BuffFunction()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : BuffFunction
      {
         if(!_instance)
         {
            _instance = new BuffFunction();
         }
         return _instance;
      }
      
      public static function addBuffVOToBuffs(param1:BuffDrive, param2:Vector.<BuffDrive>) : void
      {
         var _loc3_:int = 0;
         _loc3_ = 0;
         while(_loc3_ < param2.length)
         {
            if(param2[_loc3_].buffVO.className == param1.buffVO.className)
            {
               switch(param1.buffVO.type)
               {
                  case "allTimeBuff":
                     (param2[_loc3_].buffVO as AllTimeBuffVO).totalTime += (param1.buffVO as AllTimeBuffVO).totalTime;
                     param2[_loc3_].buffVO.remainTime += (param1.buffVO as AllTimeBuffVO).totalTime * 3600;
                     break;
                  case "onlyOnLineBuff":
                     (param2[_loc3_].buffVO as OnlyOnLineBuffVO).saveRemainTime += (param1.buffVO as OnlyOnLineBuffVO).saveRemainTime;
                     param2[_loc3_].buffVO.remainTime += (param1.buffVO as OnlyOnLineBuffVO).saveRemainTime;
                     break;
                  case "noTimeBuff":
                     break;
                  default:
                     throw new Error("类型错误！");
               }
               return;
            }
            if(param2[_loc3_].buffVO.field == param1.buffVO.field)
            {
               param2.splice(_loc3_,1);
               _loc3_--;
            }
            _loc3_++;
         }
         param2.push(param1);
         param1.start();
         switch(param2)
         {
            case BuffData.getInstance().buffDrives:
               GamingUI.getInstance().refresh(16384);
               break;
            case BuffData.getInstance().buffDrives_playerOne:
               GamingUI.getInstance().refresh(32768);
               break;
            case BuffData.getInstance().buffDrives_playerTwo:
               GamingUI.getInstance().refresh(65536);
               break;
            default:
               throw new Error();
         }
      }
      
      public static function getBuffRemainTimeById(param1:int, param2:Vector.<BuffDrive>) : String
      {
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         _loc6_ = 0;
         while(_loc6_ < param2.length)
         {
            if(param2[_loc6_].buffVO.id == param1)
            {
               if(param2[_loc6_].buffVO.type != "noTimeBuff")
               {
                  _loc5_ = param2[_loc6_].buffVO.remainTime / 3600;
                  _loc4_ = param2[_loc6_].buffVO.remainTime - _loc5_ * 3600;
                  _loc3_ = _loc4_ / 60;
                  if(_loc5_)
                  {
                     return _loc5_ + "h";
                  }
                  if(_loc3_)
                  {
                     return _loc3_ + "m";
                  }
                  return param2[_loc6_].buffVO.remainTime + "s";
               }
            }
            _loc6_++;
         }
         return "0s";
      }
      
      public static function removeBuffDriveFromBuffDrivesByBuffVO(param1:BuffVO, param2:Vector.<BuffDrive>) : void
      {
         var _loc4_:int = 0;
         var _loc3_:Boolean = false;
         _loc4_ = 0;
         while(_loc4_ < param2.length)
         {
            if(param2[_loc4_].buffVO == param1)
            {
               _loc3_ = true;
               param2[_loc4_].stop();
               param2.splice(_loc4_,1);
               _loc4_--;
            }
            _loc4_++;
         }
         if(_loc3_)
         {
            switch(param2)
            {
               case BuffData.getInstance().buffDrives:
                  GamingUI.getInstance().refresh(16384);
                  break;
               case BuffData.getInstance().buffDrives_playerOne:
                  GamingUI.getInstance().refresh(32768);
                  break;
               case BuffData.getInstance().buffDrives_playerTwo:
                  GamingUI.getInstance().refresh(65536);
                  break;
               default:
                  throw new Error();
            }
         }
      }
      
      public static function removeBuffFromBuffsByField(param1:String, param2:Vector.<BuffDrive>) : void
      {
         if(param2 == null)
         {
            return;
         }
         var _loc4_:int = 0;
         var _loc3_:int = param2 ? param2.length : 0;
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            if(param2[_loc4_].buffVO.field == param1)
            {
               param2[_loc4_].stop();
               param2.splice(_loc4_,1);
               _loc4_--;
               _loc3_--;
            }
            _loc4_++;
         }
         switch(param2)
         {
            case BuffData.getInstance().buffDrives:
               GamingUI.getInstance().refresh(16384);
               break;
            case BuffData.getInstance().buffDrives_playerOne:
               GamingUI.getInstance().refresh(32768);
               break;
            case BuffData.getInstance().buffDrives_playerTwo:
               GamingUI.getInstance().refresh(65536);
               break;
            default:
               throw new Error();
         }
      }
      
      public function decRemainTime(param1:BuffVO) : void
      {
         param1.remainTime--;
         if(param1.remainTime <= 0)
         {
            detectionPassTime(param1);
         }
      }
      
      public function detectionPassTime(param1:BuffVO) : void
      {
         var buffVO:BuffVO = param1;
         MyFunction2.getServerTimeFunction(function(param1:String):void
         {
            var _loc2_:Number = NaN;
            if(buffVO is OnlyOnLineBuffVO)
            {
               if(param1)
               {
                  _loc2_ = new TimeUtil().timeInterval((buffVO as OnlyOnLineBuffVO).initTime,param1) * 3600;
               }
               else
               {
                  _loc2_ = 0;
               }
               buffVO.remainTime = (buffVO as OnlyOnLineBuffVO).saveRemainTime - _loc2_;
            }
            else
            {
               if(!(buffVO is AllTimeBuffVO))
               {
                  throw new Error("buffVO错误");
               }
               if(param1)
               {
                  _loc2_ = new TimeUtil().timeInterval((buffVO as AllTimeBuffVO).startDate,param1);
               }
               else
               {
                  _loc2_ = 0;
               }
               (buffVO as AllTimeBuffVO).remainTime = ((buffVO as AllTimeBuffVO).totalTime - _loc2_) * 3600;
            }
            if(buffVO.remainTime <= 0)
            {
               removeBuffDriveFromBuffDrivesByBuffVO(buffVO,BuffData.getInstance().buffDrives);
               removeBuffDriveFromBuffDrivesByBuffVO(buffVO,BuffData.getInstance().buffDrives_playerOne);
               removeBuffDriveFromBuffDrivesByBuffVO(buffVO,BuffData.getInstance().buffDrives_playerTwo);
            }
         },null,false);
      }
      
      public function startProtectBuffFun(param1:BuffVO) : void
      {
         var _loc2_:XML = null;
         if(GMData.getInstance().isGMApplication)
         {
            return;
         }
         if(GamingUI.getInstance().protectPanel)
         {
            GamingUI.getInstance().protectPanel.startProtect();
         }
         if(!ProtectData.getInstance().privilegeVOs || !ProtectData.getInstance().privilegeVOs.length)
         {
            _loc2_ = XMLSingle.getInstance().buffXML;
            ProtectData.getInstance().privilegeVOs = XMLSingle.getPrivilegeVOs(String(_loc2_.item.(@className == "Buff_ProtectPrivileges")[0].@privileges),XMLSingle.getInstance().privilegeXML);
         }
      }
      
      public function endProtectBuffFun() : void
      {
         ProtectData.getInstance().privilegeVOs = new Vector.<PrivilegeVO>();
         if(GamingUI.getInstance().protectPanel)
         {
            GamingUI.getInstance().protectPanel.endProtect();
         }
      }
      
      public function processProtect(param1:BuffVO) : void
      {
         if(param1.remainTime > 0)
         {
            if(GamingUI.getInstance().protectPanel)
            {
               GamingUI.getInstance().protectPanel.setTimeText(param1.remainTime);
            }
         }
      }
   }
}

