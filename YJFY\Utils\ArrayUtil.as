package YJFY.Utils
{
   public class ArrayUtil
   {
      
      public function ArrayUtil()
      {
         super();
      }
      
      public static function removeItemFromArrays(param1:*, ... rest) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         _loc4_ = int(rest.length);
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc3_ = int(rest[_loc5_].indexOf(param1));
            while(_loc3_ != -1)
            {
               rest[_loc5_].splice(_loc3_,1);
               _loc3_ = int(rest[_loc5_].indexOf(param1));
            }
            _loc5_++;
         }
      }
   }
}

