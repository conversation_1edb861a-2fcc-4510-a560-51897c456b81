package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.BulletEntity.BeatBackEntity;
   import YJFY.Entity.BulletEntity.BeatBackEntityListener;
   import YJFY.Entity.BulletEntity.BulletEntity;
   import YJFY.Entity.BulletEntity.BulletEntityData;
   import YJFY.Entity.BulletEntity.BulletEntityListener;
   import YJFY.Entity.EntityFactory;
   import YJFY.Entity.IEntity;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.Skill.ISkill;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   
   public class CuboidAreaAttackSkill_OneBullet extends CuboidAreaAttackSkill_OnlyPlayBody
   {
      
      private var m_createBulletFrameLabel:String;
      
      private var m_skillEndFrameLabel:String;
      
      private var m_bulletData:BulletEntityData;
      
      private var m_useBulletEntitys:Vector.<BulletEntity>;
      
      private var m_wasteBulletEntitys:Vector.<BulletEntity>;
      
      private var m_bulletEntityPool:ObjectsPool;
      
      private var m_bulletEntityListener:BulletEntityListener;
      
      private var m_beatBackulletEntityListener:BeatBackEntityListener;
      
      private var m_isAbleBeatBackEntity:Boolean;
      
      private var m_oneBulletSkillListeners:Vector.<ICuboidAreaAttackSkill_OneBulletListener>;
      
      public function CuboidAreaAttackSkill_OneBullet()
      {
         super();
         m_isAbleBeatBackEntity = true;
         m_useBulletEntitys = new Vector.<BulletEntity>();
         m_wasteBulletEntitys = new Vector.<BulletEntity>();
         m_bulletEntityPool = new ObjectsPool(m_useBulletEntitys,m_wasteBulletEntitys,createBulletEntity2,null);
         m_beatBackulletEntityListener = new BeatBackEntityListener();
         m_beatBackulletEntityListener.beatBackEntityFun = listeneredBeatBackEntity;
         m_bulletEntityListener = new BulletEntityListener();
         m_bulletEntityListener.attackSuccessFun = listenerBulletAttackSuccess;
         m_oneBulletSkillListeners = new Vector.<ICuboidAreaAttackSkill_OneBulletListener>();
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         m_createBulletFrameLabel = null;
         m_skillEndFrameLabel = null;
         ClearUtil.clearObject(m_bulletData);
         m_bulletData = null;
         if(m_useBulletEntitys)
         {
            _loc1_ = m_useBulletEntitys ? m_useBulletEntitys.length : 0;
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               if(m_world)
               {
                  m_world.removeEntity(m_useBulletEntitys[_loc2_]);
               }
               ClearUtil.clearObject(m_useBulletEntitys[_loc2_]);
               m_useBulletEntitys[_loc2_] = null;
               _loc2_++;
            }
            m_useBulletEntitys.length = 0;
            m_useBulletEntitys = null;
         }
         ClearUtil.clearObject(m_wasteBulletEntitys);
         m_wasteBulletEntitys = null;
         ClearUtil.clearObject(m_bulletEntityPool);
         m_bulletEntityPool = null;
         ClearUtil.clearObject(m_bulletEntityListener);
         m_bulletEntityListener = null;
         ClearUtil.clearObject(m_beatBackulletEntityListener);
         m_beatBackulletEntityListener = null;
         ClearUtil.nullArr(m_oneBulletSkillListeners,false,false,false);
         m_oneBulletSkillListeners = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_createBulletFrameLabel = String(param1.@createBulletFrameLabel);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_bulletData = new BulletEntityData(param1.bullet[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         return true;
      }
      
      public function setIsAbleBeatBackEntity(param1:Boolean) : void
      {
         m_isAbleBeatBackEntity = param1;
      }
      
      public function addOneBulletSkillListener(param1:ICuboidAreaAttackSkill_OneBulletListener) : void
      {
         m_oneBulletSkillListeners.push(param1);
      }
      
      public function removeDragonSkill1Listener(param1:ICuboidAreaAttackSkill_OneBulletListener) : void
      {
         var _loc2_:int = int(m_oneBulletSkillListeners.indexOf(param1));
         while(_loc2_ != -1)
         {
            m_oneBulletSkillListeners.splice(_loc2_,1);
            _loc2_ = int(m_oneBulletSkillListeners.indexOf(param1));
         }
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
      }
      
      public function createBulletEntity() : void
      {
         var _loc1_:BeatBackEntity = m_bulletEntityPool.getOneOrCreateOneObj() as BeatBackEntity;
         _loc1_.setNewPosition(m_owner.getX(),m_owner.getY(),m_owner.getZ());
         m_world.addEntity(_loc1_);
         _loc1_.moveBullet(m_owner.getShowDirection(),0);
      }
      
      private function createBulletEntity2() : BeatBackEntity
      {
         var _loc1_:EntityFactory = new EntityFactory();
         var _loc2_:BeatBackEntity = _loc1_.createEntity(m_bulletData.getBulletEntityXML().@animalType) as BeatBackEntity;
         _loc2_.setOwner(m_owner);
         _loc2_.setMyLoader(m_myLoader);
         _loc2_.setAnimationDefinitionManager(m_world.getAnimationDefinitionManager());
         _loc2_.setSoundManager(m_world.getSoundManager());
         _loc2_.addBulletEntityListener(m_bulletEntityListener);
         _loc2_.setBeatBackEntityListener(m_beatBackulletEntityListener);
         _loc2_.initByXML(m_bulletData.getBulletEntityXML());
         ClearUtil.clearObject(_loc1_);
         return _loc2_;
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_createBulletFrameLabel:
               createBulletEntity();
               break;
            case m_skillEndFrameLabel:
               endSkill2();
         }
      }
      
      protected function listeneredBeatBackEntity(param1:BeatBackEntity, param2:IEntity) : void
      {
         var _loc4_:int = 0;
         var _loc5_:Vector.<ICuboidAreaAttackSkill_OneBulletListener> = m_oneBulletSkillListeners.slice(0);
         var _loc3_:int = int(_loc5_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc5_[_loc4_].beatBackEntity(this,param1,param2);
            _loc5_[_loc4_] = null;
            _loc4_++;
         }
         _loc5_.length = 0;
         _loc5_ = null;
         param1.setIsAbleBeatBackEntity(m_isAbleBeatBackEntity);
      }
      
      protected function listenerBulletAttackSuccess(param1:IEntity, param2:ISkill, param3:IEntity) : void
      {
         attackSuccess2(param1,this,param3);
         (param3 as BeatBackEntity).setAttackData(m_attackData);
      }
   }
}

