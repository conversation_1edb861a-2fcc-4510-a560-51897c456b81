package UI.ExchangeGiftBag
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import YJFY.optimize.SaveTaskInfo;
   import YJFY.optimize.SaveTaskList;
   import flash.display.MovieClip;
   
   public class GiftBagChoicePanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_oneGiftBagChoiceItems:Vector.<OneGiftBagChoiceItem>;
      
      private var m_exchangeGiftBagPanel:ExchangeGiftBagPanel;
      
      public function GiftBagChoicePanel()
      {
         super();
         m_oneGiftBagChoiceItems = new Vector.<OneGiftBagChoiceItem>();
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_oneGiftBagChoiceItems);
         m_oneGiftBagChoiceItems = null;
         m_exchangeGiftBagPanel = null;
         super.clear();
      }
      
      public function init(param1:ExchangeGiftBagPanel) : void
      {
         m_exchangeGiftBagPanel = param1;
         if(m_show)
         {
            throw new Error("不能重复初始化");
         }
         m_show = MyFunction2.returnShowByClassName("GiftBagChoicePanel") as MovieClip;
         addChild(m_show);
         MyFunction2.loadXMLFunction("yaYaLeGiftBag",getXMLSuccess,null,true);
         initShow();
      }
      
      private function initShow() : void
      {
         var _loc2_:int = 0;
         var _loc1_:OneGiftBagChoiceItem = null;
         _loc2_ = 0;
         while(_loc2_ < 5)
         {
            _loc1_ = new OneGiftBagChoiceItem();
            _loc1_.setShow(m_show["oneGiftChoiceItem" + (_loc2_ + 1)],this);
            m_oneGiftBagChoiceItems.push(_loc1_);
            _loc2_++;
         }
      }
      
      private function getXMLSuccess(param1:XML) : void
      {
         var _loc5_:int = 0;
         var _loc3_:XMLList = param1.giftBag;
         var _loc2_:int = int(_loc3_.length());
         var _loc4_:int = int(m_oneGiftBagChoiceItems.length);
         _loc5_ = 0;
         while(_loc5_ < _loc2_ && _loc5_ < _loc4_)
         {
            m_oneGiftBagChoiceItems[_loc5_].setEquipmentVOsXML(_loc3_[_loc5_]);
            _loc5_++;
         }
      }
      
      public function getOneGiftBag(param1:Vector.<EquipmentVO>) : void
      {
         MyFunction2.trueAddEquipmentVOs(param1,GamingUI.getInstance().player1,m_exchangeGiftBagPanel.showWarningBox,["礼包领取成功！",0]);
         var _loc2_:SaveTaskInfo = new SaveTaskInfo();
         _loc2_.type = "4399";
         _loc2_.isHaveData = false;
         SaveTaskList.getInstance().addData(_loc2_);
         MyFunction2.saveGame();
         m_exchangeGiftBagPanel.closeGiftBagChoicePanel();
      }
      
      protected function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      protected function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
   }
}

