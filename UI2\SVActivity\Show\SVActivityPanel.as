package UI2.SVActivity.Show
{
   import UI.EnterFrameTime;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.LogicShell.MySwitchBtnLogicShell;
   import UI.MessageBox.MessageBoxEngine;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.WarningBox.WarningBoxSingle;
   import UI2.SVActivity.Data.SVActivityData;
   import UI2.SVActivity.Data.SVActivitySaveData;
   import YJFY.Loader.IProgressShow;
   import YJFY.Loader.YJFYLoader;
   import YJFY.Loader.YJFYLoaderData;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnGroupLogicShell;
   import YJFY.ShowLogicShell.SwitchBtn.SwitchBtnLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.VersionControl.IVersionControl;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.system.System;
   
   public class SVActivityPanel extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_quitBtn:ButtonLogicShell2;
      
      private var m_multiShowMC:MovieClipPlayLogicShell;
      
      private var m_switchBtnGroup:SwitchBtnGroupLogicShell;
      
      private var m_chengZhangJiHuaBtn:MySwitchBtnLogicShell;
      
      private var m_leiJiHaoLiBtn:MySwitchBtnLogicShell;
      
      private var m_newCharacterShowBtn:MySwitchBtnLogicShell;
      
      private var m_jinHouSonBaoShowBtn:MySwitchBtnLogicShell;
      
      private var m_newHuanleZhuanPanBtn:MySwitchBtnLogicShell;
      
      private var m_svActivityXML:XML;
      
      private var m_myLoader:YJFYLoader;
      
      private var m_svActivityData:SVActivityData;
      
      private var m_chengZhangJiHuaPanel:ChengZhangJiHuaPanel;
      
      private var m_jiLeiHaoLiPanel:LeiJiHaoLiPanel;
      
      private var m_xinJueSeLibBaoPanel:XinJueSeLiBaoPanel;
      
      private var m_huanLeZhuanPanPa:HuanLeZhuanPanPanel;
      
      private var m_zhengdianPanel:ZhengdianmiaoshaPanel;
      
      private var m_jinHouSonLiPanel:JinHouSonBaoPanel;
      
      private var m_goodsTip:GetGoodsTip;
      
      private var m_svActivitySaveData:SVActivitySaveData;
      
      private var m_loadUI:IProgressShow;
      
      private var m_versionControl:IVersionControl;
      
      private var m_gamingUI:GamingUI;
      
      private var m_currentServeTime:String;
      
      private var m_buttonEnable:Boolean = true;
      
      public function SVActivityPanel()
      {
         super();
         m_quitBtn = new ButtonLogicShell2();
         m_multiShowMC = new MovieClipPlayLogicShell();
         m_switchBtnGroup = new SwitchBtnGroupLogicShell(SwitchBtnLogicShell);
         m_chengZhangJiHuaBtn = new MySwitchBtnLogicShell();
         m_leiJiHaoLiBtn = new MySwitchBtnLogicShell();
         m_newCharacterShowBtn = new MySwitchBtnLogicShell();
         m_newHuanleZhuanPanBtn = new MySwitchBtnLogicShell();
         m_jinHouSonBaoShowBtn = new MySwitchBtnLogicShell();
         m_switchBtnGroup.addSwitchBtn(m_chengZhangJiHuaBtn);
         m_switchBtnGroup.addSwitchBtn(m_leiJiHaoLiBtn);
         m_switchBtnGroup.addSwitchBtn(m_newCharacterShowBtn);
         m_switchBtnGroup.addSwitchBtn(m_newHuanleZhuanPanBtn);
         m_switchBtnGroup.addSwitchBtn(m_jinHouSonBaoShowBtn);
         addEventListener("clickButton",clickButton,true,0,true);
         addEventListener("showMessageBox",showMessageBox,true,0,true);
         addEventListener("hideMessageBox",hideMessageBox,true,0,true);
         addEventListener("showMessageBox",showMessageBox,false,0,true);
         addEventListener("hideMessageBox",hideMessageBox,false,0,true);
         addEventListener("warningBox",sureOrCancel,true,0,true);
      }
      
      override public function clear() : void
      {
         removeEventListener("clickButton",clickButton,true);
         removeEventListener("showMessageBox",showMessageBox,true);
         removeEventListener("hideMessageBox",hideMessageBox,true);
         removeEventListener("showMessageBox",showMessageBox,false);
         removeEventListener("hideMessageBox",hideMessageBox,false);
         removeEventListener("warningBox",sureOrCancel,true);
         ClearUtil.clearObject(m_show);
         m_show = null;
         ClearUtil.clearObject(m_quitBtn);
         m_quitBtn = null;
         ClearUtil.clearObject(m_multiShowMC);
         m_multiShowMC = null;
         ClearUtil.clearObject(m_switchBtnGroup);
         m_switchBtnGroup = null;
         ClearUtil.clearObject(m_chengZhangJiHuaBtn);
         m_chengZhangJiHuaBtn = null;
         ClearUtil.clearObject(m_leiJiHaoLiBtn);
         m_leiJiHaoLiBtn = null;
         ClearUtil.clearObject(m_newCharacterShowBtn);
         m_newCharacterShowBtn = null;
         ClearUtil.clearObject(m_newHuanleZhuanPanBtn);
         m_newHuanleZhuanPanBtn = null;
         ClearUtil.clearObject(m_jinHouSonBaoShowBtn);
         m_jinHouSonBaoShowBtn = null;
         if(m_svActivityXML)
         {
            System.disposeXML(m_svActivityXML);
         }
         m_svActivityXML = null;
         ClearUtil.clearObject(m_myLoader);
         m_myLoader = null;
         ClearUtil.clearObject(m_svActivityData);
         m_svActivityData = null;
         ClearUtil.clearObject(m_chengZhangJiHuaPanel);
         m_chengZhangJiHuaPanel = null;
         ClearUtil.clearObject(m_jiLeiHaoLiPanel);
         m_jiLeiHaoLiPanel = null;
         ClearUtil.clearObject(m_xinJueSeLibBaoPanel);
         m_xinJueSeLibBaoPanel = null;
         ClearUtil.clearObject(m_huanLeZhuanPanPa);
         m_huanLeZhuanPanPa = null;
         ClearUtil.clearObject(m_jinHouSonLiPanel);
         m_jinHouSonLiPanel = null;
         m_svActivitySaveData = null;
         m_loadUI = null;
         m_versionControl = null;
         m_gamingUI = null;
         super.clear();
      }
      
      public function init(param1:SVActivitySaveData, param2:IProgressShow, param3:IVersionControl, param4:GamingUI) : void
      {
         m_svActivitySaveData = param1;
         m_loadUI = param2;
         m_versionControl = param3;
         m_gamingUI = param4;
         if(m_show)
         {
            throw new Error("出错了");
         }
         MyFunction2.getServerTimeFunction(getServerTimeComplete,getServerTimeFilter);
      }
      
      private function getServerTimeComplete(param1:String) : void
      {
         setServerTime(param1);
         if(m_myLoader)
         {
            throw new Error("出错了");
         }
         m_myLoader = new YJFYLoader();
         m_myLoader.setProgressShow(m_loadUI);
         m_myLoader.setVersionControl(m_versionControl);
         m_myLoader.getXML("UIData/SVActivity/svActivity.xml",getXMLSuccess,getFail);
         m_myLoader.getClass("UISprite2/NewYearActivityPanel.swf","SVActivityPanel",getShowSuccess,getFail);
         m_myLoader.load();
      }
      
      private function getServerTimeFilter() : void
      {
      }
      
      private function initShow() : void
      {
         m_multiShowMC.setShow(m_show["multiShowMC"]);
         m_quitBtn.setShow(m_show["quitBtn"]);
         m_chengZhangJiHuaBtn.setShow(m_show["chengZhangJiHuaBtn"]);
         m_leiJiHaoLiBtn.setShow(m_show["leiJiHaoLiBtn"]);
         m_newCharacterShowBtn.setShow(m_show["newCharacterShowBtn"]);
         m_newHuanleZhuanPanBtn.setShow(m_show["huanleBtn"]);
         m_jinHouSonBaoShowBtn.setShow(m_show["jinhousonbaoBtn"]);
         m_jinHouSonBaoShowBtn.setClickEnble(true);
         m_chengZhangJiHuaBtn.setClickEnble(true);
         m_newCharacterShowBtn.setClickEnble(true);
         m_leiJiHaoLiBtn.setClickEnble(true);
         (m_leiJiHaoLiBtn.getShow() as MovieClip).gotoAndStop(1);
         m_leiJiHaoLiBtn.turnActivate();
         (m_jinHouSonBaoShowBtn.getShow() as MovieClip).gotoAndStop(1);
         m_jinHouSonBaoShowBtn.turnUnActivate();
         (m_chengZhangJiHuaBtn.getShow() as MovieClip).gotoAndStop(1);
         m_chengZhangJiHuaBtn.turnUnActivate();
         (m_newCharacterShowBtn.getShow() as MovieClip).gotoAndStop(1);
         m_newCharacterShowBtn.turnUnActivate();
         m_newCharacterShowBtn.turnActivate();
         initMultiShow_newCharacterShow();
      }
      
      private function initShow2() : void
      {
      }
      
      private function initSVActivityData() : void
      {
         ClearUtil.clearObject(m_svActivityData);
         m_svActivityData = new SVActivityData();
         m_svActivityData.initByXML(m_svActivityXML);
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         if(m_huanLeZhuanPanPa)
         {
            m_huanLeZhuanPanPa.render(param1);
         }
         if(m_jinHouSonLiPanel)
         {
            m_jinHouSonLiPanel.render(param1);
         }
      }
      
      private function getShowSuccess(param1:YJFYLoaderData) : void
      {
         var _loc2_:Class = param1.resultClass;
         m_show = new _loc2_();
         addChild(m_show);
         initShow();
         initShow2();
      }
      
      private function getXMLSuccess(param1:YJFYLoaderData) : void
      {
         m_svActivityXML = param1.resultXML;
         initSVActivityData();
         initShow2();
      }
      
      private function getFail(param1:YJFYLoaderData) : void
      {
         m_gamingUI.closeSvActivityPanel();
      }
      
      public function setServerTime(param1:String) : void
      {
         m_currentServeTime = param1;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         if(m_buttonEnable == false)
         {
            return;
         }
         switch(param1.button)
         {
            case m_quitBtn:
               m_gamingUI.closeSvActivityPanel();
               break;
            case m_chengZhangJiHuaBtn:
               initMultiShow_ChengZhangJiHua();
               break;
            case m_leiJiHaoLiBtn:
               initMultiShow_LeiJiHaoLi();
               break;
            case m_newCharacterShowBtn:
               initMultiShow_newCharacterShow();
               break;
            case m_newHuanleZhuanPanBtn:
               initMuitishow_newHuanLeZhuanPanPanel();
               break;
            case m_jinHouSonBaoShowBtn:
               initMuitishow_newJinHouSonBaoPanel();
         }
      }
      
      private function multiShowClear() : void
      {
         ClearUtil.clearObject(m_chengZhangJiHuaPanel);
         m_chengZhangJiHuaPanel = null;
         ClearUtil.clearObject(m_jiLeiHaoLiPanel);
         m_jiLeiHaoLiPanel = null;
         ClearUtil.clearObject(m_xinJueSeLibBaoPanel);
         m_xinJueSeLibBaoPanel = null;
         ClearUtil.clearObject(m_huanLeZhuanPanPa);
         m_huanLeZhuanPanPa = null;
         ClearUtil.clearObject(m_jinHouSonLiPanel);
         m_jinHouSonLiPanel = null;
         ClearUtil.clearObject(m_zhengdianPanel);
         m_zhengdianPanel = null;
      }
      
      private function initMultiShow_ChengZhangJiHua() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("chengZhangJiHua");
         m_chengZhangJiHuaPanel = new ChengZhangJiHuaPanel();
         m_chengZhangJiHuaPanel.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,m_svActivityData.getChengZhangJiHuaData(),this);
      }
      
      public function initMultiShow_chongZhiFanLi() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("chongZhiFanLi");
         (m_multiShowMC.getShow()["show"]["leijiBtn"] as MovieClip).gotoAndStop("2");
         (m_multiShowMC.getShow()["show"]["czflBtn"] as MovieClip).gotoAndStop("1");
         m_multiShowMC.getShow()["show"]["leijiBtn"].addEventListener("click",clickLeiji);
      }
      
      private function clickLeiji(param1:MouseEvent) : void
      {
         (m_multiShowMC.getShow()["show"]["leijiBtn"] as MovieClip).gotoAndStop("1");
         (m_multiShowMC.getShow()["show"]["czflBtn"] as MovieClip).gotoAndStop("2");
         m_multiShowMC.getShow()["show"]["leijiBtn"].removeEventListener("click",clickLeiji);
         initMultiShow_LeiJiHaoLi();
      }
      
      private function initMultiShow_LeiJiHaoLi() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("leiJiHaoLi");
         (m_multiShowMC.getShow()["show"]["leijiBtn"] as MovieClip).gotoAndStop("1");
         (m_multiShowMC.getShow()["show"]["czflBtn"] as MovieClip).gotoAndStop("2");
         m_jiLeiHaoLiPanel = new LeiJiHaoLiPanel();
         m_jiLeiHaoLiPanel.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,m_svActivityData.getJiLeiHaoLiData(),this);
      }
      
      private function initMultiShow_newCharacterShow() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("newCharacterShow");
         m_xinJueSeLibBaoPanel = new XinJueSeLiBaoPanel();
         m_xinJueSeLibBaoPanel.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,m_svActivityData.getXinJueSeLiBao(),this,m_currentServeTime);
      }
      
      private function initMultiShow_zhengdianShow() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("xianliangqianggou");
         m_zhengdianPanel = new ZhengdianmiaoshaPanel();
         m_zhengdianPanel.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,m_svActivityData.getZhengdianmiaosha(),this);
      }
      
      private function initMuitishow_newHuanLeZhuanPanPanel() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("newHuanleShow");
         m_huanLeZhuanPanPa = new HuanLeZhuanPanPanel();
         m_huanLeZhuanPanPa.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,this,m_versionControl);
      }
      
      private function initMuitishow_newJinHouSonBaoPanel() : void
      {
         multiShowClear();
         m_multiShowMC.gotoAndStop("jinhousonbao");
         m_jinHouSonLiPanel = new JinHouSonBaoPanel();
         m_jinHouSonLiPanel.init(m_multiShowMC.getShow()["show"],m_svActivitySaveData,m_svActivityData.getJinHouSonLi(),this,m_versionControl);
      }
      
      public function closeCurrPanel() : void
      {
         m_gamingUI.closeSvActivityPanel();
      }
      
      public function showWarningBox(param1:String, param2:int, param3:Object = null) : void
      {
         WarningBoxSingle.getInstance().x = 400;
         WarningBoxSingle.getInstance().y = 560;
         WarningBoxSingle.getInstance().initBox(param1,param2);
         WarningBoxSingle.getInstance().setBoxPosition(stage);
         addChildAt(WarningBoxSingle.getInstance(),numChildren);
         if(param3)
         {
            WarningBoxSingle.getInstance().task = param3;
         }
      }
      
      private function showMessageBox(param1:UIPassiveEvent) : void
      {
         addChildAt(MessageBoxEngine.getInstance(),numChildren);
         MessageBoxEngine.getInstance().showMessageBox(param1);
      }
      
      private function hideMessageBox(param1:UIPassiveEvent) : void
      {
         MessageBoxEngine.getInstance().hideMessageBox(param1);
      }
      
      protected function sureOrCancel(param1:UIPassiveEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.data.detail == 1)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.okFunction))
            {
               (param1.data.task.okFunction as Function).apply(null,param1.data.task.okFunctionParams);
               ClearUtil.nullArr(param1.data.task.okFunctionParams,false,false);
               param1.data.task.okFunction = null;
            }
         }
         else if(param1.data.detail == 2)
         {
            if(param1.data && Boolean(param1.data.task) && Boolean(param1.data.task.cancelFunction))
            {
               (param1.data.task.cancelFunction as Function).apply(null,param1.data.task.cancelFunctionParams);
               ClearUtil.nullArr(param1.data.task.cancelFunctionParams,false,false);
               param1.data.task.cancelFunction = null;
            }
         }
         ClearUtil.nullObject(param1.data);
      }
      
      public function setButtonEnable(param1:Boolean) : void
      {
         m_buttonEnable = param1;
      }
      
      public function showGoodSTip(param1:Vector.<EquipmentVO>, param2:Function, param3:int = 0) : void
      {
         m_goodsTip = new GetGoodsTip();
         m_goodsTip.init(param1,param2,this,m_versionControl,param3);
         this.addChild(m_goodsTip);
      }
      
      public function hidGoodsTip() : void
      {
         if(m_goodsTip)
         {
            ClearUtil.clearObject(m_goodsTip);
            m_goodsTip = null;
         }
      }
   }
}

