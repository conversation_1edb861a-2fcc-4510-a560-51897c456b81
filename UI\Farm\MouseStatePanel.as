package UI.Farm
{
   import UI.Farm.MouseManager.MouseManager;
   import UI.MyFunction;
   import UI.MySprite;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class MouseStatePanel extends MySprite
   {
      
      public var normalMouse:Sprite;
      
      public var movingMouse:Sprite;
      
      private var _selectMouseStates:Array;
      
      public function MouseStatePanel()
      {
         super();
         init();
         addEventListener("addedToStage",addToStage,false,0,true);
      }
      
      override public function clear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         super.clear();
         removeEventListener("addedToStage",addToStage,false);
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         normalMouse = null;
         movingMouse = null;
         if(_selectMouseStates)
         {
            _loc1_ = int(_selectMouseStates.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               _selectMouseStates[_loc2_] = null;
               _loc2_++;
            }
         }
         _selectMouseStates = null;
      }
      
      private function init() : void
      {
         _selectMouseStates = [];
         _selectMouseStates.push(normalMouse);
         _selectMouseStates.push(movingMouse);
      }
      
      private function addToStage(param1:Event) : void
      {
         addEventListener("removedFromStage",removeFromStage,false,0,true);
         normalMouse.addEventListener("click",onClick,false,0,true);
         movingMouse.addEventListener("click",onClick,false,0,true);
      }
      
      private function removeFromStage(param1:Event) : void
      {
         removeEventListener("removedFromStage",removeFromStage,false);
         normalMouse.removeEventListener("click",onClick,false);
         movingMouse.removeEventListener("click",onClick,false);
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(_selectMouseStates.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(_selectMouseStates[_loc3_] == param1.currentTarget)
            {
               (_selectMouseStates[_loc3_] as Sprite).mouseChildren = false;
               (_selectMouseStates[_loc3_] as Sprite).mouseEnabled = false;
               MyFunction.getInstance().changeSaturation(_selectMouseStates[_loc3_],-100);
            }
            else
            {
               (_selectMouseStates[_loc3_] as Sprite).mouseChildren = true;
               (_selectMouseStates[_loc3_] as Sprite).mouseEnabled = true;
               MyFunction.getInstance().changeSaturation(_selectMouseStates[_loc3_],0);
            }
            _loc3_++;
         }
         switch(param1.currentTarget)
         {
            case normalMouse:
               MouseManager.getInstance().setMouseState("normal");
               break;
            case movingMouse:
               MouseManager.getInstance().setMouseState("move");
         }
      }
   }
}

