package UI.Utils.CreateAdvancePet
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Pets.Pet;
   import YJFY.Utils.ClearUtil;
   
   public class CreateAdvancePetReturn
   {
      
      private var m_advancePetEquipmentVO:EquipmentVO;
      
      private var m_advancePet:Pet;
      
      public function CreateAdvancePetReturn(param1:EquipmentVO, param2:Pet)
      {
         super();
         m_advancePet = param2;
         m_advancePetEquipmentVO = param1;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_advancePetEquipmentVO);
         m_advancePetEquipmentVO = null;
         ClearUtil.clearObject(m_advancePet);
         m_advancePet = null;
      }
      
      public function nullQuotes() : void
      {
         m_advancePet = null;
         m_advancePetEquipmentVO = null;
      }
      
      public function getAdvancePetEquipmentVO() : EquipmentVO
      {
         return m_advancePetEquipmentVO;
      }
      
      public function getAdvancePet() : Pet
      {
         return m_advancePet;
      }
   }
}

