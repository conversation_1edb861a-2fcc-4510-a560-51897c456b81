package YJFY.Skill.DragonSkills
{
   import YJFY.Entity.AnimationEntityLogicShell.AnimationEntityLogicShell;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.EntityListener;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class Skill_DragonSkill5 extends CuboidAreaAttackSkill2
   {
      
      private const m_const_skillEndFrameLabel:String = "skill^stop^";
      
      private const m_const_skillAttackReachFrameLabel:String = "skillAttackReach";
      
      private var m_bodyDefId:String;
      
      private var m_longDefId:String;
      
      private var m_longDefinitionData:AnimationDefinitionData;
      
      private var m_groundEffectDefId:String;
      
      private var m_groundEffectDefinitionData:AnimationDefinitionData;
      
      private var m_longAnimationEntity1:AnimationEntityLogicShell;
      
      private var m_longAnimationEntity1AniamtionListener:AnimationPlayFrameLabelListener;
      
      private var m_longAnimationEntityListener:EntityListener;
      
      private var m_longAnimationEntity2:AnimationEntityLogicShell;
      
      private var m_groundEffectAnimation1:AnimationShowPlayLogicShell;
      
      private var m_groundEffectAnimation2:AnimationShowPlayLogicShell;
      
      public function Skill_DragonSkill5()
      {
         super();
         m_longAnimationEntity1 = new AnimationEntityLogicShell();
         m_longAnimationEntity1.init(0,0,0);
         m_longAnimationEntity1AniamtionListener = new AnimationPlayFrameLabelListener();
         m_longAnimationEntity1AniamtionListener.reachFrameLabelFun = reachFrameLabel;
         m_longAnimationEntity1.getAniamtionShowPlay().addFrameLabelListener(m_longAnimationEntity1AniamtionListener);
         m_longAnimationEntity2 = new AnimationEntityLogicShell();
         m_longAnimationEntity2.init(0,0,0);
         m_longAnimationEntityListener = new EntityListener();
         m_longAnimationEntityListener.addToWorldFun = longAddToWorld;
         m_longAnimationEntity1.addEntityListener(m_longAnimationEntityListener);
         m_longAnimationEntity2.addEntityListener(m_longAnimationEntityListener);
         m_groundEffectAnimation1 = new AnimationShowPlayLogicShell();
         m_groundEffectAnimation2 = new AnimationShowPlayLogicShell();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_longAnimationEntity1.getAnimationShow());
         ClearUtil.clearObject(m_longAnimationEntity1);
         m_longAnimationEntity1 = null;
         ClearUtil.clearObject(m_longAnimationEntity2.getAnimationShow());
         ClearUtil.clearObject(m_longAnimationEntity2);
         m_longAnimationEntity2 = null;
         ClearUtil.clearObject(m_groundEffectAnimation1.getShow());
         ClearUtil.clearObject(m_groundEffectAnimation1);
         m_groundEffectAnimation1 = null;
         ClearUtil.clearObject(m_groundEffectAnimation2.getShow());
         ClearUtil.clearObject(m_groundEffectAnimation2);
         m_groundEffectAnimation2 = null;
         m_bodyDefId = null;
         m_longDefId = null;
         ClearUtil.clearObject(m_longDefinitionData);
         m_longDefinitionData = null;
         m_groundEffectDefId = null;
         ClearUtil.clearObject(m_groundEffectDefinitionData);
         m_groundEffectDefinitionData = null;
         ClearUtil.clearObject(m_longAnimationEntity1AniamtionListener);
         m_longAnimationEntity1AniamtionListener = null;
         ClearUtil.clearObject(m_longAnimationEntityListener);
         m_longAnimationEntityListener = null;
         super.clear();
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_longDefId = String(param1.@longDefId);
         m_longDefinitionData = new AnimationDefinitionData();
         m_longDefinitionData.initByXML(param1.animationDefinition.(@id == m_longDefId)[0]);
         m_groundEffectDefId = String(param1.@groundEffectDefId);
         m_groundEffectDefinitionData = new AnimationDefinitionData();
         m_groundEffectDefinitionData.initByXML(param1.animationDefinition.(@id == m_groundEffectDefId)[0]);
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         var _loc2_:AnimationShow = null;
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         if(m_longAnimationEntity1.getAnimationShow() == null)
         {
            _loc2_ = new AnimationShow();
            _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_longDefinitionData));
            m_longAnimationEntity1.setShow(_loc2_);
         }
         if(m_longAnimationEntity2.getAnimationShow() == null)
         {
            _loc2_ = new AnimationShow();
            _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_longDefinitionData));
            m_longAnimationEntity2.setShow(_loc2_);
         }
         if(m_groundEffectAnimation1.getShow() == null)
         {
            _loc2_ = new AnimationShow();
            _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_groundEffectDefinitionData));
            m_groundEffectAnimation1.setShow(_loc2_);
         }
         if(m_groundEffectAnimation2.getShow() == null)
         {
            _loc2_ = new AnimationShow();
            _loc2_.setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_groundEffectDefinitionData));
            m_groundEffectAnimation2.setShow(_loc2_);
         }
         m_longAnimationEntity1.setNewPosition(m_cuboidRangeToWorld.getMinX() + 0.3333333333333333 * m_cuboidRangeToWorld.getXRange(),m_cuboidRangeToWorld.getMinY() + 0.6666666666666666 * m_cuboidRangeToWorld.getYRange(),0);
         m_longAnimationEntity2.setNewPosition(m_cuboidRangeToWorld.getMinX() + 0.6666666666666666 * m_cuboidRangeToWorld.getXRange(),m_cuboidRangeToWorld.getMinY() + 0.3333333333333333 * m_cuboidRangeToWorld.getYRange(),0);
         m_longAnimationEntity1.getShow().scaleX = 1;
         m_longAnimationEntity2.getShow().scaleX = -1;
         m_longAnimationEntity1.getAniamtionShowPlay().gotoAndPlay("1");
         m_longAnimationEntity2.getAniamtionShowPlay().gotoAndPlay("1");
         m_world.addEntity(m_longAnimationEntity1);
         m_world.addEntity(m_longAnimationEntity2);
      }
      
      private function longAddToWorld(param1:IEntity, param2:World) : void
      {
         var _loc3_:AnimationShowPlayLogicShell = null;
         if(param1 == m_longAnimationEntity1)
         {
            _loc3_ = m_groundEffectAnimation1;
            m_groundEffectAnimation1.getShow().scaleX = 1;
         }
         else
         {
            if(param1 != m_longAnimationEntity2)
            {
               return;
            }
            _loc3_ = m_groundEffectAnimation2;
            m_groundEffectAnimation2.getShow().scaleX = -1;
         }
         (_loc3_.getShow() as DisplayObject).x = param1.getScreenX();
         (_loc3_.getShow() as DisplayObject).y = param1.getScreenY();
         _loc3_.gotoAndPlay("1");
         m_world.addAnimationInGround(_loc3_);
      }
      
      override protected function endSkill2() : void
      {
         m_world.removeEntity(m_longAnimationEntity1);
         m_world.removeEntity(m_longAnimationEntity2);
         m_world.removeAnimationInGround(m_groundEffectAnimation1);
         m_world.removeAnimationInGround(m_groundEffectAnimation2);
         super.endSkill2();
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "skillAttackReach":
               if(isNaN(m_nextAttackReachTime))
               {
                  attackReach(m_world);
               }
               break;
            case "skill^stop^":
               endSkill2();
         }
      }
   }
}

