package YJFY.XydzjsLogic.GetOneTargetLogic
{
   import YJFY.Entity.IEntity;
   import YJFY.EntityAI.AILogic.IsOneEffectiveTarget;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerImage;
   import YJFY.Utils.ClearUtil;
   import YJFY.XydzjsData.IAttackDataCalculateVOXydzjs;
   
   public class GetOneTargetLogic
   {
      
      private var m_attackNum:Number = 0;
      
      private var m_attackNum1:Number = 0;
      
      private var m_attackNum2:Number = 0;
      
      private var m_isOneEffectiveTarget:IsOneEffectiveTarget;
      
      public function GetOneTargetLogic()
      {
         super();
         m_isOneEffectiveTarget = new IsOneEffectiveTarget();
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_isOneEffectiveTarget);
         m_isOneEffectiveTarget = null;
      }
      
      public function getOneMaxKillEntity(param1:IEntity, param2:IEntity, param3:IEntity) : IEntity
      {
         if(param1 == null || param1.getWorld() == null || !(param1.getExtra() is IAttackDataCalculateVOXydzjs))
         {
            throw new Error("出错了");
         }
         if(m_isOneEffectiveTarget.isOneEffectiveTarget(param3) == false || !(param3.getExtra() is IAttackDataCalculateVOXydzjs))
         {
            return param2;
         }
         if(m_isOneEffectiveTarget.isOneEffectiveTarget(param2) == false || !(param2.getExtra() is IAttackDataCalculateVOXydzjs))
         {
            return param3;
         }
         if(param2.getExtra() is PlayerImage)
         {
            return param2;
         }
         m_attackNum1 = calculateNeedAvargeAttackNum(param1.getExtra() as IAttackDataCalculateVOXydzjs,param2.getExtra() as IAttackDataCalculateVOXydzjs);
         m_attackNum2 = calculateNeedAvargeAttackNum(param1.getExtra() as IAttackDataCalculateVOXydzjs,param3.getExtra() as IAttackDataCalculateVOXydzjs);
         if(m_attackNum1 > m_attackNum2)
         {
            return param3;
         }
         return param2;
      }
      
      private function calculateNeedAvargeAttackNum(param1:IAttackDataCalculateVOXydzjs, param2:IAttackDataCalculateVOXydzjs) : Number
      {
         m_attackNum = param2.getCurrentHp() / (Math.max(1,param1.getAttack() * (1 + param1.getCriticalMulti() * Math.max(0,param1.getCriticalRate() - param2.getDeCriticalRate())) - param2.getDefence()) * Math.min(1,Math.max(0.0001,1 - param2.getDogdeRate() + param1.getHitRate())));
         return m_attackNum;
      }
   }
}

