package YJFY.Utils.MovieClipToBitmapDatas
{
   import YJFY.Utils.ClearUtil;
   import flash.display.BitmapData;
   
   public class DataOfBitmapData
   {
      
      private var m_bitmapData:BitmapData;
      
      private var m_bitmapDataXOffset:Number;
      
      private var m_bitmapDataYOffset:Number;
      
      private var m_frameLabel:String;
      
      public function DataOfBitmapData(param1:BitmapData, param2:Number, param3:Number, param4:String)
      {
         super();
         resetData(param1,param2,param3,param4);
      }
      
      public function resetData(param1:BitmapData, param2:Number, param3:Number, param4:String) : void
      {
         m_bitmapData = param1;
         m_bitmapDataXOffset = param2;
         m_bitmapDataYOffset = param3;
         m_frameLabel = param4;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_bitmapData);
         m_bitmapData = null;
      }
      
      public function nullBitmapData() : void
      {
         m_bitmapData = null;
      }
      
      public function getBitmapData() : BitmapData
      {
         return m_bitmapData;
      }
      
      public function getBitmapDataXOffset() : Number
      {
         return m_bitmapDataXOffset;
      }
      
      public function getBitmapDataYOffset() : Number
      {
         return m_bitmapDataYOffset;
      }
      
      public function getFrameLabel() : String
      {
         return m_frameLabel;
      }
   }
}

