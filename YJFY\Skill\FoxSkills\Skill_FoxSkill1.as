package YJFY.Skill.FoxSkills
{
   import YJFY.Skill.DogSkills.Skill_DogSkill1;
   import YJFY.World.World;
   
   public class Skill_FoxSkill1 extends Skill_DogSkill1
   {
      
      private const m_const_startMoveFrameLabel:String = "startMove";
      
      private const m_const_stopMoveFrameLabel:String = "stopMove";
      
      public function Skill_FoxSkill1()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         m_owner.setMoveSpeed(0);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case "startMove":
               m_owner.setMoveSpeed(m_skillMoveSpeed);
               break;
            case "stopMove":
               m_owner.setMoveSpeed(0);
         }
      }
   }
}

