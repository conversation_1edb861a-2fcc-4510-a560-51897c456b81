package YJFY.PKMode2.PKUI
{
   import UI.EnterFrameTime;
   import UI.ExternalUI.SkillCells.UsedSkillCellLogicShell;
   import UI.LogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShell;
   import UI.Players.Player;
   import YJFY.ShowLogicShell.ChangeBarLogicShell.CMSXChangeBarLogicShellType;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.ShowLogicShell.NumShowLogicShell.MultiPlaceNumLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   
   public class PKPlayerPanel
   {
      
      private var m_headShow:MovieClipPlayLogicShell;
      
      private var m_bloodBar:CMSXChangeBarLogicShell;
      
      private var m_magicBar:CMSXChangeBarLogicShell;
      
      private var m_expBar:CMSXChangeBarLogicShell;
      
      private var m_nuQiBarMC:MovieClipPlayLogicShell;
      
      private var m_nuQiBar:CMSXChangeBarLogicShell;
      
      private var m_skillCells:Vector.<UsedSkillCellLogicShell>;
      
      private var m_levelShow:MultiPlaceNumLogicShell;
      
      private var m_hpPercent:Number;
      
      private var m_mpPercent:Number;
      
      private var m_currentNuQi:int;
      
      private var m_expPercent:Number;
      
      private var m_show:MovieClip;
      
      private var m_player:Player;
      
      public function PKPlayerPanel()
      {
         super();
         m_skillCells = new Vector.<UsedSkillCellLogicShell>();
         m_expBar = new CMSXChangeBarLogicShell();
         m_magicBar = new CMSXChangeBarLogicShell();
         m_bloodBar = new CMSXChangeBarLogicShell();
         m_nuQiBarMC = new MovieClipPlayLogicShell();
         m_nuQiBar = new CMSXChangeBarLogicShell();
         m_levelShow = new MultiPlaceNumLogicShell();
         m_headShow = new MovieClipPlayLogicShell();
         m_currentNuQi = -1;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(m_headShow);
         m_headShow = null;
         ClearUtil.clearObject(m_bloodBar);
         m_bloodBar = null;
         ClearUtil.clearObject(m_magicBar);
         m_magicBar = null;
         ClearUtil.clearObject(m_expBar);
         m_expBar = null;
         ClearUtil.clearObject(m_nuQiBarMC);
         m_nuQiBarMC = null;
         ClearUtil.clearObject(m_nuQiBar);
         m_nuQiBar = null;
         ClearUtil.clearObject(m_skillCells);
         m_skillCells = null;
         ClearUtil.clearObject(m_levelShow);
         m_levelShow = null;
         m_player = null;
         m_show = null;
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         initShow();
         initShow2();
      }
      
      public function setPlayer(param1:Player) : void
      {
         m_player = param1;
         initShow2();
      }
      
      public function render(param1:EnterFrameTime) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         refreshSomeDataShow();
         _loc2_ = int(m_skillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_skillCells[_loc3_].render(param1);
            _loc3_++;
         }
      }
      
      private function initShow2() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         if(m_player == null || m_show == null)
         {
            return;
         }
         m_headShow.gotoAndStop(m_player.playerVO.playerType);
         m_levelShow.showNum(m_player.playerVO.level);
         refreshSomeDataShow();
         _loc1_ = int(m_skillCells.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            m_skillCells[_loc3_].setPlayerVO(m_player.playerVO);
            _loc3_++;
         }
         _loc1_ = m_skillCells.length - 1;
         var _loc2_:int = int(m_player.playerVO.skillVOs.length);
         _loc3_ = 0;
         while(_loc3_ < _loc1_ && _loc3_ < _loc2_)
         {
            m_skillCells[_loc3_].removeSkillVO();
            m_skillCells[_loc3_].addSkillVO(m_player.playerVO.skillVOs[_loc3_]);
            _loc3_++;
         }
         if(Boolean(m_player.playerVO.pet) && Boolean(m_player.playerVO.pet.petEquipmentVO))
         {
            if(m_player.playerVO.pet.petEquipmentVO.activeSkillVO)
            {
               m_skillCells[5].removeSkillVO();
               m_skillCells[5].addSkillVO(m_player.playerVO.pet.petEquipmentVO.activeSkillVO);
            }
         }
      }
      
      private function initShow() : void
      {
         var _loc4_:int = 0;
         var _loc1_:UsedSkillCellLogicShell = null;
         ClearUtil.clearObject(m_skillCells);
         m_skillCells.length = 0;
         var _loc2_:int = 6;
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            _loc1_ = new UsedSkillCellLogicShell();
            _loc1_.setShow(m_show["skillCell_" + (_loc4_ + 1)],m_show["timeShow" + (_loc4_ + 1)]);
            m_skillCells.push(_loc1_);
            _loc4_++;
         }
         m_expBar.setShow(m_show["experienceBar"]);
         var _loc3_:CMSXChangeBarLogicShellType = new CMSXChangeBarLogicShellType();
         _loc3_.type = "vertical";
         m_expBar.setType(_loc3_);
         m_magicBar.setShow(m_show["magicBar"]);
         m_bloodBar.setShow(m_show["bloodBar"]);
         m_nuQiBarMC.setShow(m_show["nuQiBar"]);
         m_nuQiBar.setShow(m_nuQiBarMC.getShow()["bar"]);
         m_levelShow.setShow(m_show["levelShow"]);
         m_headShow.setShow(m_show["headShow"]);
      }
      
      private function refreshSomeDataShow() : void
      {
         if(m_hpPercent != m_player.playerVO.bloodPercent)
         {
            m_hpPercent = m_player.playerVO.bloodPercent;
            m_bloodBar.change(m_hpPercent);
            m_bloodBar.setDataShow("" + Math.round(m_hpPercent * m_player.playerVO.bloodVolume) + "/" + m_player.playerVO.bloodVolume);
         }
         if(m_mpPercent != m_player.playerVO.magicPercent)
         {
            m_mpPercent = m_player.playerVO.magicPercent;
            m_magicBar.change(m_mpPercent);
            m_magicBar.setDataShow("" + Math.round(m_mpPercent * m_player.playerVO.maxMagic) + "/" + m_player.playerVO.maxMagic);
         }
         if(m_currentNuQi != m_player.playerVO.nuQiCurrentValue)
         {
            if(m_player.playerVO.nuQiCurrentValue == m_player.playerVO.nuQiAllValue)
            {
               m_nuQiBarMC.gotoAndStop("full");
            }
            else
            {
               m_nuQiBarMC.gotoAndStop("normal");
            }
            m_currentNuQi = m_player.playerVO.nuQiCurrentValue;
            m_nuQiBar.change(m_currentNuQi / m_player.playerVO.nuQiAllValue);
            m_nuQiBar.setDataShow("" + m_currentNuQi + "/" + m_player.playerVO.nuQiAllValue);
         }
         if(m_expPercent != m_player.playerVO.experiencePercent)
         {
            m_expPercent = m_player.playerVO.experiencePercent;
            m_expBar.change(m_expPercent);
            m_expBar.setDataShow("" + m_expPercent * m_player.playerVO.experienceVolume + "/" + m_player.playerVO.experienceVolume);
         }
      }
   }
}

