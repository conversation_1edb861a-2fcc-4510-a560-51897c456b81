package YJFY.Skill.ActiveSkill.AttackSkill
{
   import YJFY.Entity.AnimalEntity;
   
   public class Jian<PERSON>killListener implements IJianSkillListener
   {
      
      public var replaceFun:Function;
      
      public var recoverFun:Function;
      
      public function JianSkillListener()
      {
         super();
      }
      
      public function clear() : void
      {
         recoverFun = null;
         replaceFun = null;
      }
      
      public function replace(param1:CuboidAreaAttackSkill_JianSkill, param2:AnimalEntity) : void
      {
         if(Boolean(replaceFun))
         {
            replaceFun(param1,param2);
         }
      }
      
      public function recover(param1:CuboidAreaAttackSkill_JianSkill, param2:AnimalEntity) : void
      {
         if(<PERSON><PERSON><PERSON>(recoverFun))
         {
            recoverFun(param1,param2);
         }
      }
   }
}

