package UI.ShopWall
{
   import UI.Button.QuitBtn3;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIBtnEvent;
   import UI.Event.UIDataEvent;
   import UI.Shop.BuyPopUpBox;
   import flash.events.Event;
   
   public class ShopWall_BuyPopUpBox extends BuyPopUpBox
   {
      
      public static const ONE_PLAYER:int = 1;
      
      public static const TWO_PLAYER:int = 2;
      
      private var _quitBtn:QuitBtn3;
      
      private var _state:int;
      
      public function ShopWall_BuyPopUpBox(param1:EquipmentVO, param2:String)
      {
         super(param1,param2);
         _quitBtn = new QuitBtn3();
         _quitBtn.x = 200;
         _quitBtn.y = 0;
         addChild(_quitBtn);
      }
      
      override public function initBox(param1:EquipmentVO, param2:String) : void
      {
         super.initBox(param1,param2);
         _sureBtn.showText("P1购买");
         _cancelBtn.showText("P2购买");
         state = _state;
      }
      
      override public function clear() : void
      {
         super.clear();
         if(_quitBtn)
         {
            _quitBtn.clear();
         }
         _quitBtn = null;
      }
      
      public function set state(param1:int) : void
      {
         _state = param1;
         switch(param1 - 1)
         {
            case 0:
               if(Boolean(_cancelBtn) && _cancelBtn.parent)
               {
                  _cancelBtn.parent.removeChild(_cancelBtn);
               }
               if(_sureBtn)
               {
                  _sureBtn.x = (width - _sureBtn.width) / 2 - 140;
               }
               break;
            case 1:
               if(_cancelBtn)
               {
                  addChild(_cancelBtn);
                  _cancelBtn.x = 135.8;
               }
               if(_sureBtn)
               {
                  _sureBtn.x = 52.8;
                  break;
               }
         }
      }
      
      override protected function addToStage(param1:Event) : void
      {
         super.addToStage(param1);
         addEventListener("clickQuitBtn",quit,true,0,true);
      }
      
      override protected function removeFromStage(param1:Event) : void
      {
         super.removeFromStage(param1);
         removeEventListener("clickQuitBtn",quit,true);
      }
      
      override protected function clickBtn(param1:UIBtnEvent) : void
      {
         if(param1.target == _sureBtn)
         {
            dispatchEvent(new UIDataEvent("buyEquipment",{
               "player":1,
               "equipmentVO":equipmentCell.child,
               "num":_numBtnGroup.num
            }));
         }
         else
         {
            dispatchEvent(new UIDataEvent("buyEquipment",{
               "player":2,
               "equipmentVO":equipmentCell.child,
               "num":_numBtnGroup.num
            }));
         }
      }
      
      public function quit(param1:UIBtnEvent) : void
      {
         parent.removeChild(this);
      }
   }
}

