package UI.RefineFactory.LianDanFurnace
{
   import UI.Animation.AnimationObject;
   import UI.Animation.M2B;
   import UI.Event.UIPassiveEvent;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.XMLSingle;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   
   public class LianDanFurnace extends MySprite
   {
      
      private var _showSprite:Sprite;
      
      private var _lianDanFurnaceDrive:LianDanFurnaceDrive;
      
      private var listenerList:Array = [];
      
      public function LianDanFurnace(param1:LianDanFurnaceVO, param2:String, param3:XML, param4:XML)
      {
         super();
         init(param1,param2,param3,param4);
      }
      
      override public function clear() : void
      {
         var _loc2_:DisplayObject = null;
         var _loc1_:int = 0;
         super.clear();
         destory();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         if(_showSprite)
         {
            while(_showSprite.numChildren > 0)
            {
               _loc2_ = _showSprite.getChildAt(0);
               _showSprite.removeChildAt(0);
               if(_loc2_.hasOwnProperty("clear"))
               {
                  _loc2_["clear"]();
               }
            }
         }
         _showSprite = null;
         if(_lianDanFurnaceDrive)
         {
            _lianDanFurnaceDrive.clear();
         }
         _lianDanFurnaceDrive = null;
         var _loc3_:int = 0;
         if(listenerList)
         {
            _loc1_ = int(listenerList.length);
            _loc3_ = 0;
            while(_loc3_ < _loc1_)
            {
               listenerList[_loc3_] = null;
               _loc3_++;
            }
            listenerList = null;
         }
      }
      
      public function get lianDanFurnaceDrive() : LianDanFurnaceDrive
      {
         return _lianDanFurnaceDrive;
      }
      
      public function set lianDanFurnaceDrive(param1:LianDanFurnaceDrive) : void
      {
         _lianDanFurnaceDrive = param1;
      }
      
      public function showOrdinayToRefineAnimation(param1:Function) : void
      {
         var _loc5_:String = null;
         var _loc4_:XML = XMLSingle.getInstance().farmXML.lianDanFurnace.(@id == _lianDanFurnaceDrive.lianDanFurnaceVO.id)[0];
         _loc5_ = String(_loc4_.@showNameOneToTwo);
         var _loc3_:MovieClip = MyFunction2.returnShowByClassName(_loc5_) as MovieClip;
         var _loc2_:AnimationObject = new AnimationObject();
         _loc2_.x = -143;
         _loc2_.y = -381;
         _loc2_.imgList = M2B.transformM2B(_loc3_,false,50,150);
         _loc2_.repeatCount = 1;
         _loc2_.delay = 50;
         _loc2_.play();
         _loc2_.addEventListener("play over event",param1,false,0,true);
         if(_loc3_)
         {
            _showSprite.addChild(_loc2_);
            _showSprite.getChildAt(0).visible = false;
         }
      }
      
      private function init(param1:LianDanFurnaceVO, param2:String, param3:XML, param4:XML) : void
      {
         _showSprite = new Sprite();
         addChild(_showSprite);
         addEventListener("rollOver",onOver,false,0,true);
         addEventListener("rollOut",onOut,false,0,true);
         addEventListener("click",onClick,false,0,true);
         _lianDanFurnaceDrive = new LianDanFurnaceDrive(param1,param2,param3,param4);
         initShow(_lianDanFurnaceDrive.lianDanFurnaceVO,param4);
         _lianDanFurnaceDrive.addEventListener("changeState",changeShowState,false,0,true);
      }
      
      private function changeShowState(param1:Event) : void
      {
         initShow(_lianDanFurnaceDrive.lianDanFurnaceVO,XMLSingle.getInstance().farmXML);
      }
      
      private function initShow(param1:LianDanFurnaceVO, param2:XML) : void
      {
         var _loc5_:String = null;
         var _loc6_:DisplayObject = null;
         var _loc4_:XML = param2.lianDanFurnace.(@id == param1.id)[0];
         switch(param1.state)
         {
            case 0:
               _loc5_ = String(_loc4_.@showNameOne);
               break;
            case 1:
               _loc5_ = String(_loc4_.@showNameTwo);
               break;
            case 2:
               _loc5_ = String(_loc4_.@showNameThree);
         }
         var _loc3_:DisplayObject = MyFunction2.returnShowByClassName(_loc5_);
         if(_loc3_)
         {
            while(_showSprite.numChildren > 0)
            {
               _loc6_ = _showSprite.getChildAt(0);
               _showSprite.removeChildAt(0);
               if(_loc6_.hasOwnProperty("clear"))
               {
                  _loc6_["clear"]();
               }
            }
            _showSprite.addChild(_loc3_);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         filters = [new GlowFilter(16777215,1,255,255,0.2,1,true)];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         filters = [];
      }
      
      private function onClick(param1:Event) : void
      {
         dispatchEvent(new UIPassiveEvent("clickLianDanFurnace"));
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
      }
   }
}

