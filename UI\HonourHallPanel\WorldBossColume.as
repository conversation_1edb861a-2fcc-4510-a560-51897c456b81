package UI.HonourHallPanel
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.Event.UIPassiveEvent;
   import UI.GamingUI;
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.Players.Player;
   import UI.XMLSingle;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import YJFY.Utils.TimeUtil.TimeUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class WorldBossColume
   {
      
      public static const NAME:String = "name";
      
      public static const DESCRIPTION:String = "description";
      
      public static const MEDAL_ID:String = "medalID";
      
      private var m_show:MovieClip;
      
      private var m_showMc:MovieClipPlayLogicShell;
      
      private var m_getAwardBtn:ButtonLogicShell;
      
      private var m_icon:Sprite;
      
      private var _dataObject:Object;
      
      private var _medalEquipmentVO:EquipmentVO;
      
      private var m_honourHallPanel:HonourHallPanel;
      
      private var m_doublePlayer:Boolean;
      
      public function WorldBossColume()
      {
         super();
      }
      
      public function setShow(param1:MovieClip) : void
      {
         m_show = param1;
         m_showMc = new MovieClipPlayLogicShell();
         var _loc2_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_showMc.setShow(m_show);
         m_icon = m_show["icon"];
         m_show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function setHonourHallPanel(param1:HonourHallPanel, param2:Boolean = false) : void
      {
         m_honourHallPanel = param1;
         m_doublePlayer = param2;
      }
      
      public function initData(param1:Object, param2:String, param3:XML) : void
      {
         _dataObject = param1;
         _medalEquipmentVO = XMLSingle.getEquipmentVOByID(param1["medalID"],param3,param2);
         m_icon.addEventListener("rollOver",equipmentInfor,false,0,true);
         m_icon.addEventListener("rollOut",equipmentInfor,false,0,true);
         m_showMc.gotoAndStop("1");
      }
      
      public function active() : void
      {
         m_showMc.gotoAndStop("2");
         m_getAwardBtn = new ButtonLogicShell();
         m_getAwardBtn.setShow(m_show["getAwardBtn"]);
         m_getAwardBtn.setTipString("点击获取奖励");
      }
      
      public function clear() : void
      {
         if(m_icon)
         {
            m_icon.removeEventListener("rollOver",equipmentInfor,false);
            m_icon.removeEventListener("rollOut",equipmentInfor,false);
         }
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = null;
         ClearUtil.clearObject(m_showMc);
         m_showMc = null;
         ClearUtil.clearObject(m_getAwardBtn);
         m_getAwardBtn = null;
         m_icon = null;
         if(_medalEquipmentVO)
         {
            _medalEquipmentVO.clear();
         }
         _medalEquipmentVO = null;
         for(var _loc1_ in _dataObject)
         {
            _dataObject[_loc1_] = null;
         }
         _dataObject = null;
         m_honourHallPanel = null;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc2_:* = param1.button;
         if(m_getAwardBtn === _loc2_)
         {
            getAward();
         }
      }
      
      private function getAward() : void
      {
         var _loc11_:int = 0;
         var _loc5_:int = 0;
         var _loc10_:* = undefined;
         var _loc3_:EquipmentVO = null;
         var _loc8_:* = undefined;
         var _loc9_:EquipmentVO = null;
         var _loc1_:* = undefined;
         var _loc4_:Player = GamingUI.getInstance().player1;
         var _loc6_:Player = GamingUI.getInstance().player2;
         var _loc2_:Vector.<EquipmentVO> = GamingUI.getInstance().publicStorageEquipmentVOs;
         var _loc7_:Boolean = false;
         if(_loc6_)
         {
            _loc10_ = MyFunction2.concatEquipmentVOVector(_loc4_.playerVO.inforEquipmentVOs,_loc4_.playerVO.packageEquipmentVOs,_loc4_.playerVO.storageEquipmentVOs,_loc6_.playerVO.inforEquipmentVOs,_loc6_.playerVO.packageEquipmentVOs,_loc6_.playerVO.storageEquipmentVOs,_loc4_.playerVO.medalEquipmentVOs,_loc6_.playerVO.medalEquipmentVOs,_loc2_);
         }
         else
         {
            _loc10_ = MyFunction2.concatEquipmentVOVector(_loc4_.playerVO.inforEquipmentVOs,_loc4_.playerVO.packageEquipmentVOs,_loc4_.playerVO.storageEquipmentVOs,_loc4_.playerVO.medalEquipmentVOs,_loc2_);
         }
         _loc5_ = int(_loc10_.length);
         _loc11_ = 0;
         while(_loc11_ < _loc5_)
         {
            if(Boolean(_loc10_[_loc11_]) && _loc10_[_loc11_].id == _dataObject["medalID"])
            {
               _loc7_ = true;
               break;
            }
            _loc11_++;
         }
         if(!_loc7_)
         {
            _loc3_ = XMLSingle.getEquipmentVOByID(_dataObject["medalID"],XMLSingle.getInstance().equipmentXML,TimeUtil.timeStr,true);
            _loc8_ = new Vector.<EquipmentVO>();
            _loc8_.push(_loc3_);
            MyFunction2.addEquipmentVOs(_loc8_,_loc4_,m_honourHallPanel.showWarningBox,m_honourHallPanel.showWarningBox,["背包已满！",0],["获取成功！",0],2);
            if(m_doublePlayer && _loc6_)
            {
               _loc9_ = XMLSingle.getEquipmentVOByID(_dataObject["medalID"],XMLSingle.getInstance().equipmentXML,TimeUtil.timeStr,true);
               _loc1_ = new Vector.<EquipmentVO>();
               _loc1_.push(_loc9_);
               MyFunction2.addEquipmentVOs(_loc1_,_loc6_,m_honourHallPanel.showWarningBox,m_honourHallPanel.showWarningBox,["背包已满！",0],["获取成功！",0],2);
            }
            _loc5_ = int(_loc8_.length);
            _loc11_ = 0;
            while(_loc11_ < _loc5_)
            {
               if(_loc8_[_loc11_])
               {
                  _loc8_[_loc11_] = null;
               }
               _loc11_++;
            }
            _loc8_ = null;
         }
         else
         {
            m_honourHallPanel.showWarningBox("您已经有这个勋章了！",0);
         }
      }
      
      private function equipmentInfor(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case "rollOver":
               m_show.dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":_medalEquipmentVO}));
               break;
            case "rollOut":
               m_show.dispatchEvent(new UIPassiveEvent("hideMessageBox"));
         }
      }
   }
}

