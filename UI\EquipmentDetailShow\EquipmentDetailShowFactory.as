package UI.EquipmentDetailShow
{
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class EquipmentDetailShowFactory
   {
      
      private var m_classes:Array;
      
      public function EquipmentDetailShowFactory()
      {
         super();
         m_classes = [EquipmentDetailShow,ScrollDetailShow,EggDetailShow,PetDetailShow];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_classes);
         m_classes = null;
      }
      
      public function createByClassName(param1:String) : EquipmentDetailShow
      {
         var _loc2_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc2_();
      }
   }
}

