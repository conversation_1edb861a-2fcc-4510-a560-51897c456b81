package UI.Pets.Talents
{
   import UI.Event.UIPassiveEvent;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class Talent extends MySprite
   {
      
      private static const path:String = "";
      
      private var _talentVO:TalentVO;
      
      private var _talentSprite:Sprite;
      
      private var _width:Number;
      
      private var _height:Number;
      
      private var _listenerList:Array = [];
      
      public function Talent(param1:TalentVO)
      {
         super();
         setImg(param1.className);
         this.talentVO = param1;
         addEventListener("mouseOver",onOver,false,0,true);
         addEventListener("mouseOut",onOut,false,0,true);
         buttonMode = true;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function clear() : void
      {
         _talentVO = null;
         super.clear();
         destory();
         if(_talentVO)
         {
            _talentVO.clear();
         }
         ClearUtil.clearDisplayObjectInContainer(_talentSprite);
         _talentSprite = null;
         ClearUtil.nullArr(_listenerList);
         _listenerList = null;
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in _listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
      }
      
      private function setImg(param1:String) : void
      {
         _talentSprite = MyFunction2.returnShowByClassName("" + param1) as Sprite;
         addChild(_talentSprite);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("showMessageBox",{"equipment":this}));
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         dispatchEvent(new UIPassiveEvent("hideMessageBox"));
      }
      
      override public function set width(param1:Number) : void
      {
         _width = param1;
      }
      
      override public function get width() : Number
      {
         return _width;
      }
      
      override public function set height(param1:Number) : void
      {
         _height = param1;
      }
      
      override public function get height() : Number
      {
         return _height;
      }
      
      public function get talentVO() : TalentVO
      {
         return _talentVO;
      }
      
      public function set talentVO(param1:TalentVO) : void
      {
         _talentVO = param1;
         setImg(param1.className);
      }
      
      public function clone() : Talent
      {
         return new Talent(talentVO.clone());
      }
   }
}

