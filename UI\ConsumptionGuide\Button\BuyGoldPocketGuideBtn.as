package UI.ConsumptionGuide.Button
{
   import UI.ConsumptionGuide.GuideUseEquipment;
   
   public class BuyGoldPocketGuideBtn extends GuideBtn
   {
      
      public function BuyGoldPocketGuideBtn(param1:int, param2:Function = null, param3:int = 0)
      {
         super(param1,param2,param3);
         var _loc4_:GuideUseEquipment = new GuideUseEquipment();
         _loc4_.setShowWarningFun(_showWarningFun);
         _loc4_.setUseAfterFun(param2);
         _loc4_.addUseEqId("11100000");
         setGuideEquipment(_loc4_);
      }
      
      public function IsBtnLaterOn(param1:Boolean) : void
      {
         IsLaterOn = param1;
      }
   }
}

