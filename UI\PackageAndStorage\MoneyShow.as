package UI.PackageAndStorage
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class MoneyShow extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_showBtn:ButtonLogicShell2;
      
      private var m_moneyText:TextField;
      
      public function MoneyShow()
      {
         super();
         init();
      }
      
      override public function clear() : void
      {
         super.clear();
         m_show = null;
         ClearUtil.clearObject(m_showBtn);
         m_showBtn = null;
         m_moneyText = null;
      }
      
      private function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("MoneyShow") as MovieClip;
         addChild(m_show);
         m_showBtn = new ButtonLogicShell2();
         m_showBtn.setShow(m_show);
         m_moneyText = m_show["moneyText"];
      }
      
      public function getMoneyText() : TextField
      {
         return m_moneyText;
      }
      
      public function getShowBtn() : ButtonLogicShell2
      {
         return m_showBtn;
      }
   }
}

