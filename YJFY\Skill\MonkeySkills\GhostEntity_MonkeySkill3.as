package YJFY.Skill.MonkeySkills
{
   import YJFY.Entity.GhostEntity.GhostEntity;
   import YJFY.Entity.IEntityContainer;
   import YJFY.Entity.IFriend;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.IAnimationDefintion;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.NextStopListener;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import flash.display.DisplayObject;
   
   public class GhostEntity_MonkeySkill3 extends GhostEntity implements IFriend
   {
      
      protected var m_durationTime:int;
      
      protected var m_disappearTime:Number;
      
      protected var m_disappearAnimation:AnimationShow;
      
      protected var m_disappearAnimationPlay:AnimationShowPlayLogicShell;
      
      protected var m_currentAnimation:AnimationShowPlayLogicShell;
      
      public function GhostEntity_MonkeySkill3()
      {
         super();
         m_disappearAnimation = new AnimationShow();
         m_disappearAnimationPlay = new AnimationShowPlayLogicShell();
         m_disappearAnimationPlay.setShow(m_disappearAnimation,true);
      }
      
      override protected function fullyClear() : void
      {
         ClearUtil.clearObject(m_disappearAnimationPlay);
         m_disappearAnimationPlay = null;
         ClearUtil.clearObject(m_disappearAnimation);
         m_disappearAnimation = null;
         m_currentAnimation = null;
         super.fullyClear();
      }
      
      public function setDisappearTime(param1:int) : void
      {
         m_durationTime = param1;
      }
      
      public function setDisappearAnimationDef(param1:IAnimationDefintion) : void
      {
         m_disappearAnimation.setDurrentDefinition(param1);
      }
      
      override public function setParent(param1:IEntityContainer, param2:World) : void
      {
         super.setParent(param1,param2);
         if(m_world)
         {
            m_disappearTime = m_world.getWorldTime() + m_durationTime;
         }
      }
      
      override public function render(param1:World) : void
      {
         super.render(param1);
         if(!isNaN(m_disappearTime) && m_disappearTime < param1.getWorldTime())
         {
            die();
            m_disappearTime = NaN;
         }
      }
      
      override public function showRender() : void
      {
         super.showRender();
         if(m_currentAnimation)
         {
            m_currentAnimation.render();
         }
      }
      
      public function die() : void
      {
         if(m_show.contains(m_bitmap))
         {
            m_show.removeChild(m_bitmap);
         }
         m_currentAnimation = m_disappearAnimationPlay;
         m_show.addChild(m_currentAnimation.getShow() as DisplayObject);
         var _loc1_:NextStopListener = new NextStopListener();
         _loc1_.target = m_currentAnimation;
         _loc1_.stopFun = disappear;
         m_disappearAnimationPlay.addNextStopListener(_loc1_);
         m_currentAnimation.gotoAndPlay("1");
      }
      
      private function disappear() : void
      {
         m_dieRemoveTime = m_world.getWorldTime() + m_delayDisappearTime;
      }
   }
}

