package YJFY.Skill.PetSkills
{
   import UI.Skills.PetSkills.PetActiveSkillVO;
   import UI.Skills.SkillVO;
   import YJFY.Entity.AnimalEntity;
   import YJFY.Entity.AnimationPlayFrameLabelListener;
   import YJFY.Entity.IAnimalEntity;
   import YJFY.Entity.IEntity;
   import YJFY.EntityAnimation.AnimationShow;
   import YJFY.EntityAnimation.EntityAnimationDefinitionData.AnimationDefinitionData;
   import YJFY.GameEntity.EnemyXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PetXydzjs;
   import YJFY.GameEntity.XydzjsPlayerAndPet.PlayerXydzjs;
   import YJFY.ObjectPool.ObjectsPool;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.AnimationShowPlayLogicShell;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.IAnimationShow;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.StopListener;
   import YJFY.Skill.ActiveSkill.AttackSkill.CuboidAreaAttackSkill2;
   import YJFY.Utils.ClearUtil;
   import YJFY.World.World;
   import YJFY.XydzjsData.IEnemyXydzjs;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.utils.Timer;
   
   public class Skill_Pet18Skill extends CuboidAreaAttackSkill2
   {
      
      private const m_const_changeStartId:String = "changeStart";
      
      private const m_const_startEffectFrameLabel:String = "start";
      
      private const m_const_endEffectFrameLabel:String = "end^stop^";
      
      private const m_const_startPlayEffectFrameLabel:String = "changeTarget";
      
      private const m_const_effectShowDefId:String = "petSkillEffect";
      
      protected var m_frontSkillShowDefId:String;
      
      protected var m_releaseSkillFrameLabel:String;
      
      protected var m_skillEndFrameLabel:String;
      
      protected var m_appearBodyShowDefId:String;
      
      private var m_RecoverTimer:Timer;
      
      protected var m_bodyDefId:String;
      
      protected var m_bodyAttackReachFrameLabel:String;
      
      protected var m_bodySkillEndFrameLabel:String;
      
      protected var m_everyEntityAddShowDefId:String;
      
      protected var m_everyEntityAddShowDefinitionData:AnimationDefinitionData;
      
      protected var m_everyEntityAddShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_everyEntityAddShowPlayEndListener:StopListener;
      
      protected var m_everyEntityAddShowPlayReachFrameListener:AnimationPlayFrameLabelListener;
      
      protected var m_wasteEveryEntityAddShowPlays:Vector.<AnimationShowPlayLogicShell>;
      
      protected var m_everyEntityAddShowPlaysPool:ObjectsPool;
      
      protected var m_frontSkillShowPlay:AnimationShowPlayLogicShell;
      
      protected var m_frontSkillShowPlayListener:AnimationPlayFrameLabelListener;
      
      public function Skill_Pet18Skill()
      {
         super();
         m_frontSkillShowPlay = new AnimationShowPlayLogicShell();
         m_frontSkillShowPlayListener = new AnimationPlayFrameLabelListener();
         m_frontSkillShowPlayListener.reachFrameLabelFun = reachFrameLabel;
         m_frontSkillShowPlay.addFrameLabelListener(m_frontSkillShowPlayListener);
         m_everyEntityAddShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_everyEntityAddShowPlayEndListener = new StopListener();
         m_everyEntityAddShowPlayEndListener.stop2Fun = AnimationStop;
         m_everyEntityAddShowPlayReachFrameListener = new AnimationPlayFrameLabelListener();
         m_everyEntityAddShowPlayReachFrameListener.reachFrameLabelFun2 = everyEntityAddShowPlayReachFrame;
         m_wasteEveryEntityAddShowPlays = new Vector.<AnimationShowPlayLogicShell>();
         m_everyEntityAddShowPlaysPool = new ObjectsPool(m_everyEntityAddShowPlays,m_wasteEveryEntityAddShowPlays,createEntityShow,null);
      }
      
      protected function createEntityShow() : AnimationShowPlayLogicShell
      {
         var _loc2_:IAnimationShow = new AnimationShow();
         (_loc2_ as AnimationShow).setDurrentDefinition(m_world.getAnimationDefinitionManager().getOrAddDefinitionById(m_everyEntityAddShowDefinitionData));
         var _loc1_:AnimationShowPlayLogicShell = new AnimationShowPlayLogicShell();
         _loc1_.setShow(_loc2_);
         return _loc1_;
      }
      
      protected function AnimationStop(param1:Event) : void
      {
         var _loc5_:int = 0;
         var _loc4_:AnimationShowPlayLogicShell = null;
         var _loc3_:IEntity = null;
         if(m_RecoverTimer)
         {
            m_RecoverTimer.stop();
         }
         m_RecoverTimer = null;
         var _loc2_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = m_everyEntityAddShowPlays[_loc5_];
            _loc3_ = _loc4_.extra as IEntity;
            if(_loc3_ && _loc3_ is AnimalEntity)
            {
               if(!(_loc3_ && _loc3_.getExtra() is PlayerXydzjs))
               {
                  (_loc3_ as AnimalEntity).recoverWalkSpeed();
               }
            }
            if(_loc3_ && _loc3_.getExtra() is IEnemyXydzjs)
            {
               (_loc3_.getExtra() as EnemyXydzjs).RecoverDefence();
            }
            (_loc4_.extra as IEntity).removeOtherAnimation(_loc4_);
            m_everyEntityAddShowPlaysPool.wasteOneObj(_loc4_);
            _loc5_--;
            _loc2_--;
            _loc5_++;
         }
      }
      
      protected function everyEntityAddShowPlayReachFrame(param1:AnimationShowPlayLogicShell, param2:String) : void
      {
      }
      
      override public function clear() : void
      {
         clearEveryEntityAddShowAnimationPlays();
         clearWasteEveryEntityAddShowAnimationPlays();
         m_appearBodyShowDefId = null;
         m_skillEndFrameLabel = null;
         m_frontSkillShowDefId = null;
         m_releaseSkillFrameLabel = null;
         m_bodyDefId = null;
         m_bodyAttackReachFrameLabel = null;
         m_bodySkillEndFrameLabel = null;
         m_everyEntityAddShowDefId = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlays);
         m_everyEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayEndListener);
         m_everyEntityAddShowPlayEndListener = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlayReachFrameListener);
         m_everyEntityAddShowPlayReachFrameListener = null;
         ClearUtil.clearObject(m_wasteEveryEntityAddShowPlays);
         m_wasteEveryEntityAddShowPlays = null;
         ClearUtil.clearObject(m_everyEntityAddShowPlaysPool);
         m_everyEntityAddShowPlaysPool = null;
         ClearUtil.clearObject(m_everyEntityAddShowDefinitionData);
         m_everyEntityAddShowDefinitionData = null;
         super.clear();
         if(m_RecoverTimer)
         {
            m_RecoverTimer.stop();
         }
         m_RecoverTimer = null;
         ClearUtil.clearObject(m_frontSkillShowPlay);
         m_frontSkillShowPlay = null;
         ClearUtil.clearObject(m_frontSkillShowPlayListener);
         m_frontSkillShowPlayListener = null;
      }
      
      protected function clearEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = m_everyEntityAddShowPlays ? m_everyEntityAddShowPlays.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            if(m_everyEntityAddShowPlays[_loc2_].extra)
            {
               (m_everyEntityAddShowPlays[_loc2_].extra as IEntity).removeOtherAnimation(m_everyEntityAddShowPlays[_loc2_]);
            }
            ClearUtil.clearObject(m_everyEntityAddShowPlays[_loc2_].getShow());
            ClearUtil.clearObject(m_everyEntityAddShowPlays[_loc2_]);
            m_everyEntityAddShowPlays[_loc2_] = null;
            _loc2_++;
         }
         m_everyEntityAddShowPlays.length = 0;
      }
      
      protected function clearWasteEveryEntityAddShowAnimationPlays() : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         var _loc1_:int = m_wasteEveryEntityAddShowPlays ? m_wasteEveryEntityAddShowPlays.length : 0;
         while(_loc1_)
         {
            _loc2_ = m_wasteEveryEntityAddShowPlays[0];
            m_wasteEveryEntityAddShowPlays.splice(0,1);
            _loc1_--;
            ClearUtil.clearObject(_loc2_);
            _loc2_ = null;
         }
         m_wasteEveryEntityAddShowPlays = null;
      }
      
      override public function initByXML(param1:XML) : void
      {
         super.initByXML(param1);
         m_bodyDefId = String(param1.@bodyDefId);
         m_bodyAttackReachFrameLabel = String(param1.@bodyAttackReachFrameLabel);
         m_bodySkillEndFrameLabel = String(param1.@bodySkillEndFrameLabel);
         m_everyEntityAddShowDefId = String(param1.@everyEntityAddShowDefId);
         m_releaseSkillFrameLabel = String(param1.@releaseSkillFrameLabel);
         m_frontSkillShowDefId = String(param1.@frontSkillShowDefId);
         m_skillEndFrameLabel = String(param1.@skillEndFrameLabel);
         m_appearBodyShowDefId = String(param1.@appearBodyShowDefId);
         if(m_everyEntityAddShowDefId)
         {
            m_everyEntityAddShowDefinitionData = new AnimationDefinitionData();
            m_everyEntityAddShowDefinitionData.initByXML(param1.animationDefinition.(@id == m_everyEntityAddShowDefId)[0]);
         }
      }
      
      override public function startSkill(param1:World) : Boolean
      {
         if(super.startSkill(param1) == false)
         {
            return false;
         }
         releaseSkill2(param1);
         return true;
      }
      
      override protected function releaseSkill2(param1:World) : void
      {
         super.releaseSkill2(param1);
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setNewPosition(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         m_owner.setMoveSpeed(0);
         m_owner.changeAnimationShow(m_bodyDefId);
         m_owner.currentAnimationGotoAndPlay("1");
         m_RecoverTimer = new Timer(18000);
         m_RecoverTimer.addEventListener("timer",AnimationStop,false,0,true);
         m_RecoverTimer.start();
      }
      
      override public function attackSuccess(param1:IEntity) : void
      {
         var _loc3_:* = 0;
         var _loc5_:* = 0;
         var _loc4_:SkillVO = null;
         var _loc2_:* = 0;
         super.attackSuccess(param1);
         if(Boolean(m_attackData) && m_attackData.getIsAttack())
         {
            addEntityShow(param1);
            if(m_owner && m_owner.getExtra() is PetXydzjs && (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO())
            {
               _loc4_ = (m_owner.getExtra() as PetXydzjs).getPetEquipmentVO().activeSkillVO;
               if(_loc4_)
               {
                  if(_loc4_.className == "PetSkill_ChongMing")
                  {
                     _loc3_ = uint((_loc4_ as PetActiveSkillVO).subWalkSpeed);
                     _loc5_ = uint((_loc4_ as PetActiveSkillVO).subDefence);
                  }
               }
            }
            if(param1 && param1 is AnimalEntity)
            {
               if(!(param1 && param1.getExtra() is PlayerXydzjs))
               {
                  (param1 as AnimalEntity).addPersentWalkSpeed(Math.max(100 - _loc3_,0));
               }
            }
            if(param1 && param1.getExtra() is IEnemyXydzjs)
            {
               _loc2_ = (param1.getExtra() as EnemyXydzjs).getDefence();
               _loc2_ *= (100 - _loc5_) / 100;
               (param1.getExtra() as EnemyXydzjs).addDefence(Math.max(0,_loc2_));
            }
         }
      }
      
      protected function addEntityShow(param1:IEntity) : void
      {
         var _loc2_:AnimationShowPlayLogicShell = null;
         if(m_world == null)
         {
            return;
         }
         if(param1.getIsShowBody() == false || param1 is IAnimalEntity && (param1 as IAnimalEntity).isInDie())
         {
            return;
         }
         if(m_everyEntityAddShowDefinitionData)
         {
            _loc2_ = m_everyEntityAddShowPlaysPool.getOneOrCreateOneObj() as AnimationShowPlayLogicShell;
            _loc2_.extra = param1;
            _loc2_.gotoAndPlay("start");
            _loc2_.getDisplayShow().scaleX = _loc2_.getDisplayShow().scaleY = 0.8;
            _loc2_.getDisplayShow().x = _loc2_.getDisplayShow().x - 5;
            _loc2_.getDisplayShow().y = 0 - param1.getBodyZRange();
            param1.addOtherAniamtion(_loc2_);
         }
      }
      
      protected function releaseSkill3() : void
      {
         m_world.stopWorldTime();
         if(m_frontSkillShowPlay.getShow() == null)
         {
            m_frontSkillShowPlay.setShow(m_owner.getAnimationByDefId(m_frontSkillShowDefId),true);
         }
         (m_frontSkillShowPlay.getShow() as DisplayObject).x = m_world.getCamera().getX() + 960 / 2;
         (m_frontSkillShowPlay.getShow() as DisplayObject).y = m_world.getWorldArea().getMinY() - m_world.getWorldArea().getYRange() / 3;
         m_frontSkillShowPlay.gotoAndPlay("1");
         m_world.addAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
      }
      
      override protected function reachFrameLabel(param1:String) : void
      {
         super.reachFrameLabel(param1);
         if(m_isRun == false)
         {
            return;
         }
         switch(param1)
         {
            case m_releaseSkillFrameLabel:
               releaseSkill3();
               break;
            case m_bodyAttackReachFrameLabel:
               attackReach(m_world);
               break;
            case m_bodySkillEndFrameLabel:
               m_world.continueWorldTime();
               m_world.removeAnimationOfOutWorldTimeInFront(m_frontSkillShowPlay);
               endSkill1();
               break;
            case m_skillEndFrameLabel:
         }
      }
      
      override public function isAbleAttackOnePosition(param1:Number, param2:Number, param3:Number) : Boolean
      {
         setAttackPoint_in(m_world.getCamera().getX() + 960 / 2,m_world.getWorldArea().getMinY() + m_world.getWorldArea().getYRange() / 2,0);
         return super.isAbleAttackOnePosition(param1,param2,param3);
      }
   }
}

