package UI.Equipments
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import UI.MyFunction2;
   import UI.MySprite;
   import UI.Utils.LoadQueue.LoadFinishListener1;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class Equipment extends MySprite
   {
      
      protected var m_path:String = "UI.Equipments.";
      
      protected var _equipmentVO:EquipmentVO;
      
      protected var _equipmentSprite:Sprite;
      
      private var listenerList:Array = [];
      
      private const CELL_WIDTH:Number = 44;
      
      private const CELL_HEIGHT:Number = 44;
      
      private var _width:Number;
      
      private var _height:Number;
      
      protected var _wanLoadSources:Array = ["equipmentUI"];
      
      private var _extra:Object;
      
      public function Equipment(param1:EquipmentVO)
      {
         super();
         _width = 44;
         _height = 44;
         drawMouseArea();
         _equipmentVO = param1;
         setShow(_equipmentVO);
      }
      
      override public function clear() : void
      {
         var _loc1_:int = 0;
         super.clear();
         destory();
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         _equipmentVO = null;
         _equipmentSprite = null;
         var _loc2_:int = 0;
         if(listenerList)
         {
            _loc1_ = int(listenerList.length);
            _loc2_ = 0;
            while(_loc2_ < _loc1_)
            {
               listenerList[_loc2_] = null;
               _loc2_++;
            }
         }
         listenerList = null;
         _equipmentSprite = null;
         ClearUtil.nullArr(_wanLoadSources);
         _wanLoadSources = null;
         _extra = null;
      }
      
      public function getExtra() : Object
      {
         return _extra;
      }
      
      public function setExtra(param1:Object) : void
      {
         _extra = param1;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         if(listenerList == null)
         {
            listenerList = [];
         }
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = listenerList ? listenerList.length : 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            if(listenerList[_loc5_] != null)
            {
               if(listenerList[_loc5_].type == param1 && listenerList[_loc5_].listener == param2)
               {
                  listenerList.splice(_loc5_,1);
                  _loc5_--;
                  super.removeEventListener(param1,param2,param3);
               }
            }
            _loc5_++;
         }
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener);
         }
      }
      
      protected function setShow(param1:EquipmentVO) : void
      {
         var loadListener:LoadFinishListener1;
         var equipmentVO:EquipmentVO = param1;
         if(equipmentVO == null)
         {
            return;
         }
         loadListener = new LoadFinishListener1(function():void
         {
            if(_equipmentVO == null)
            {
               return;
            }
            setmg(equipmentVO.className);
         },null);
         GamingUI.getInstance().loadQueue.load(_wanLoadSources,loadListener);
      }
      
      protected function setmg(param1:String) : void
      {
         _equipmentSprite = MyFunction2.returnShowByClassName(m_path + param1) as Sprite;
         if(_equipmentSprite == null)
         {
            return;
         }
         while(numChildren > 1)
         {
            removeChildAt(numChildren - 1);
         }
         addChild(_equipmentSprite);
      }
      
      protected function drawMouseArea() : void
      {
         var _loc1_:Sprite = new Sprite();
         _loc1_.graphics.beginFill(16777215,0);
         _loc1_.graphics.drawRect(-22,-22,44,44);
         _loc1_.graphics.endFill();
         addChildAt(_loc1_,0);
      }
      
      override public function get width() : Number
      {
         return _width;
      }
      
      override public function get height() : Number
      {
         return _height;
      }
      
      public function get equipmentVO() : EquipmentVO
      {
         return _equipmentVO;
      }
      
      public function get equipmentSprite() : Sprite
      {
         return _equipmentSprite;
      }
      
      public function set equipmentVO(param1:EquipmentVO) : void
      {
         _equipmentVO = param1;
         setShow(_equipmentVO);
      }
      
      public function imperfectClone() : Equipment
      {
         return new Equipment(equipmentVO);
      }
      
      public function clone() : Equipment
      {
         return new Equipment(equipmentVO.clone());
      }
   }
}

