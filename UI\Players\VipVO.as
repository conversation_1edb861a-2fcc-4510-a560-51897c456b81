package UI.Players
{
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.MyFunction2;
   import UI.Privilege.PrivilegeVO;
   import YJFY.GameDataEncrypt.Antiwear;
   import YJFY.GameDataEncrypt.encrypt.binaryEncrypt;
   
   public class VipVO
   {
      
      private var _totalPointTicketValue:Number = 0;
      
      private var _vipLevel:int;
      
      private var _isOpenStorage:Boolean;
      
      private var _isOpenShop:Boolean;
      
      private var _isLostEquipmentInMake:Boolean;
      
      private var _isLostEquipmentInUpgrade:Boolean;
      
      private var _addExperienceValue:int;
      
      private var _freeZhenhunNum:int;
      
      private var _oldDateGetGift:String;
      
      private var _giftBagVOs:Vector.<EquipmentVO>;
      
      private var _privilegeVOs:Vector.<PrivilegeVO>;
      
      private var _accLandRecoverValue:Number;
      
      private var _freePKResetOppNum:int;
      
      private var _freeOneKeyPkOppNum:int;
      
      private var _freeGuanYinProtectNum:int;
      
      private var _addMirageNum:int;
      
      private var _isHaveFreeRedNickname:Boolean;
      
      private var _isFreeExchangeEquipment:Boolean;
      
      private var _addXiuLianUpperLimit:int;
      
      private var _addWorldBossPowerValue:int;
      
      protected var _binaryEn:binaryEncrypt;
      
      protected var _antiwear:Antiwear;
      
      public function VipVO()
      {
         super();
         init();
      }
      
      public function init() : void
      {
         _binaryEn = new binaryEncrypt();
         _antiwear = new Antiwear(_binaryEn,MyFunction2.doIsCheat);
         _antiwear.totalPointTicketValue = _totalPointTicketValue;
         _antiwear.vipLevel = _vipLevel;
         _antiwear.isOpenStorage = _isOpenStorage;
         _antiwear.isOpenShop = _isOpenShop;
         _antiwear.oldDateGetGift = _oldDateGetGift;
         _antiwear.isLostEquipmentInMake = _isLostEquipmentInMake;
         _antiwear.isLostEquipmentInUpgrade = _isLostEquipmentInUpgrade;
         _antiwear.addExperienceValue = _addExperienceValue;
         _antiwear.freeZhenhunNum = _freeZhenhunNum;
         _antiwear.accLandRecoverValue = _accLandRecoverValue = 0;
         _antiwear.freePKResetOppNum = _freePKResetOppNum;
         _antiwear.freeOneKeyPkOppNum = _freeOneKeyPkOppNum;
         _antiwear.freeGuanYinProtectNum = _freeGuanYinProtectNum;
         _antiwear.addMirageNum = _addMirageNum;
         _antiwear.isHaveFreeRedNickname = _isHaveFreeRedNickname;
         _antiwear.isFreeExchangeEquipment = _isFreeExchangeEquipment;
         _antiwear.addXiuLianUpperLimit = _addXiuLianUpperLimit;
         _antiwear.addWorldBossPowerValue = _addWorldBossPowerValue;
         _antiwear.addEndlessPowerValue = 0;
      }
      
      public function set addWorldBossPowerValue(param1:int) : void
      {
         _antiwear.addWorldBossPowerValue = param1;
      }
      
      public function get addWorldBossPowerValue() : int
      {
         return _antiwear.addWorldBossPowerValue;
      }
      
      public function set addEndlessPowerValue(param1:int) : void
      {
         _antiwear.addEndlessPowerValue = param1;
      }
      
      public function get addEndlessPowerValue() : int
      {
         return _antiwear.addEndlessPowerValue;
      }
      
      public function get addXiuLianUpperLimit() : int
      {
         return _antiwear.addXiuLianUpperLimit;
      }
      
      public function set addXiuLianUpperLimit(param1:int) : void
      {
         _antiwear.addXiuLianUpperLimit = param1;
      }
      
      public function get isFreeExchangeEquipment() : Boolean
      {
         return _antiwear.isFreeExchangeEquipment;
      }
      
      public function set isFreeExchangeEquipment(param1:Boolean) : void
      {
         _antiwear.isFreeExchangeEquipment = param1;
      }
      
      public function get isHaveFreeRedNickname() : Boolean
      {
         return _antiwear.isHaveFreeRedNickname;
      }
      
      public function set isHaveFreeRedNickname(param1:Boolean) : void
      {
         _antiwear.isHaveFreeRedNickname = param1;
      }
      
      public function get addMirageNum() : int
      {
         return _antiwear.addMirageNum;
      }
      
      public function set addMirageNum(param1:int) : void
      {
         _antiwear.addMirageNum = param1;
      }
      
      public function get freeGuanYinProtectNum() : int
      {
         return _antiwear.freeGuanYinProtectNum;
      }
      
      public function set freeGuanYinProtectNum(param1:int) : void
      {
         _antiwear.freeGuanYinProtectNum = param1;
      }
      
      public function get freePKResetOppNum() : int
      {
         return _antiwear.freePKResetOppNum;
      }
      
      public function set freePKResetOppNum(param1:int) : void
      {
         _antiwear.freePKResetOppNum = param1;
      }
      
      public function get freeOneKeyPkOppNum() : int
      {
         return _antiwear.freeOneKeyPkOppNum;
      }
      
      public function set freeOneKeyPkOppNum(param1:int) : void
      {
         _antiwear.freeOneKeyPkOppNum = param1;
      }
      
      public function get accLandRecoverValue() : Number
      {
         return _antiwear.accLandRecoverValue;
      }
      
      public function set accLandRecoverValue(param1:Number) : void
      {
         _antiwear.accLandRecoverValue = param1;
      }
      
      public function get totalPointTicketValue() : Number
      {
         return _antiwear.totalPointTicketValue;
      }
      
      public function set totalPointTicketValue(param1:Number) : void
      {
         _antiwear.totalPointTicketValue = param1;
      }
      
      public function get vipLevel() : int
      {
         return _antiwear.vipLevel;
      }
      
      public function set vipLevel(param1:int) : void
      {
         _antiwear.vipLevel = param1;
      }
      
      public function get isOpenStorage() : Boolean
      {
         return _antiwear.isOpenStorage;
      }
      
      public function set isOpenStorage(param1:Boolean) : void
      {
         _antiwear.isOpenStorage = param1;
      }
      
      public function get isOpenShop() : Boolean
      {
         return _antiwear.isOpenShop;
      }
      
      public function set isOpenShop(param1:Boolean) : void
      {
         _antiwear.isOpenShop = param1;
      }
      
      public function get oldDateGetGift() : String
      {
         return _antiwear.oldDateGetGift;
      }
      
      public function set oldDateGetGift(param1:String) : void
      {
         _antiwear.oldDateGetGift = param1;
      }
      
      public function get isLostEquipmentInMake() : Boolean
      {
         return _antiwear.isLostEquipmentInMake;
      }
      
      public function set isLostEquipmentInMake(param1:Boolean) : void
      {
         _antiwear.isLostEquipmentInMake = param1;
      }
      
      public function get isLostEquipmentInUpgrade() : Boolean
      {
         return _antiwear.isLostEquipmentInUpgrade;
      }
      
      public function set isLostEquipmentInUpgrade(param1:Boolean) : void
      {
         _antiwear.isLostEquipmentInUpgrade = param1;
      }
      
      public function get addExperienceValue() : int
      {
         return _antiwear.addExperienceValue;
      }
      
      public function set addExperienceValue(param1:int) : void
      {
         _antiwear.addExperienceValue = param1;
      }
      
      public function get freeZhenhunNum() : int
      {
         return _antiwear.freeZhenhunNum;
      }
      
      public function set freeZhenhunNum(param1:int) : void
      {
         _antiwear.freeZhenhunNum = param1;
      }
      
      public function get giftBagVOs() : Vector.<EquipmentVO>
      {
         return _giftBagVOs;
      }
      
      public function set giftBagVOs(param1:Vector.<EquipmentVO>) : void
      {
         _giftBagVOs = param1;
      }
      
      public function get privilegeVOs() : Vector.<PrivilegeVO>
      {
         return _privilegeVOs;
      }
      
      public function set privilegeVOs(param1:Vector.<PrivilegeVO>) : void
      {
         var _loc4_:int = 0;
         var _loc2_:String = null;
         _privilegeVOs = param1;
         var _loc3_:int = int(_privilegeVOs.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = _privilegeVOs[_loc4_].className;
            switch(_loc2_.substr(0,_loc2_.length - 2))
            {
               case "Privilege_Shop":
                  isOpenShop = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_ZhenHunProtect":
                  freeZhenhunNum = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_Storage":
                  isOpenStorage = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Make":
                  isLostEquipmentInMake = !Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Upgrade":
                  isLostEquipmentInUpgrade = !Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_Experience":
                  addExperienceValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_VIPLogo":
                  break;
               case "Privilege_AccLandRecover":
                  accLandRecoverValue = _privilegeVOs[_loc4_].value / 100;
                  break;
               case "Privilege_PKResetOpp":
                  freePKResetOppNum = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_OneKeyPKOpp":
                  freeOneKeyPkOppNum = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_GuanYinProtect":
                  freeGuanYinProtectNum = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_MirageNum":
                  addMirageNum = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_RedNickname":
                  isHaveFreeRedNickname = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_FreeExchangeEquipment":
                  isFreeExchangeEquipment = Boolean(_privilegeVOs[_loc4_].value);
                  break;
               case "Privilege_AddXiuLianUpperLimit":
                  addXiuLianUpperLimit = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_AddWorldBossPowerUpperLimit":
                  addWorldBossPowerValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_AddEndlessPowerUpperLimit":
                  addEndlessPowerValue = _privilegeVOs[_loc4_].value;
                  break;
               case "Privilege_ReserveOne":
                  break;
               case "Privilege_huanhuaBaoji":
                  break;
               case "Privilege_AddEndlessResurrection":
                  break;
               case "Privilege_ReserveTwo":
                  break;
               case "Privilege_wuxianchongnengi":
                  break;
               default:
                  throw new Error("没有符合条件的特权！");
            }
            _loc4_++;
         }
      }
      
      public function clear() : void
      {
         var _loc2_:int = 0;
         _binaryEn = null;
         _antiwear = null;
         var _loc1_:int = _giftBagVOs ? _giftBagVOs.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _giftBagVOs[_loc2_].clear();
            _giftBagVOs[_loc2_] = null;
            _loc2_++;
         }
         _giftBagVOs = null;
         _loc1_ = _privilegeVOs ? _privilegeVOs.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            _privilegeVOs[_loc2_].clear();
            _privilegeVOs[_loc2_] = null;
            _loc2_++;
         }
         _privilegeVOs = null;
      }
   }
}

