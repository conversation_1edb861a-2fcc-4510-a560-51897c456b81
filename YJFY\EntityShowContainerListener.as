package YJFY
{
   public class EntityShowContainerListener implements IEntityShowContainerListener
   {
      
      public var loadCompleteFun:Function;
      
      public function EntityShowContainerListener()
      {
         super();
      }
      
      public function clear() : void
      {
         loadCompleteFun = null;
      }
      
      public function loadComplete(param1:EntityShowContainer) : void
      {
         if(Boolean(loadCompleteFun))
         {
            loadCompleteFun(param1);
         }
      }
   }
}

