package UI.Skills
{
   import UI.MyFunction;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.Sprite;
   
   public class Skill extends MySprite
   {
      
      private static const path:String = "UI.Skills.";
      
      private var _skillVO:SkillVO;
      
      private var _skillSprite:Sprite;
      
      private var listenerList:Array = [];
      
      public function Skill(param1:SkillVO)
      {
         super();
         if(param1)
         {
            setImg(param1.className);
            setSkillActive(param1.level,param1.isActive);
         }
         this.skillVO = param1;
      }
      
      override public function clear() : void
      {
         super.clear();
         ClearUtil.clearDisplayObjectInContainer(_skillSprite);
         ClearUtil.clearDisplayObjectInContainer(this);
         _skillVO = null;
         _skillSprite = null;
         destory();
         var _loc2_:int = 0;
         var _loc1_:int = listenerList ? listenerList.length : 0;
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            listenerList[_loc2_] = null;
            _loc2_++;
         }
         listenerList = null;
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Object = {};
         _loc6_.type = param1;
         _loc6_.listener = param2;
         _loc6_.useCapture = param3;
         listenerList.push(_loc6_);
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      public function destory() : void
      {
         for each(var _loc1_ in listenerList)
         {
            removeEventListener(_loc1_.type,_loc1_.listener,_loc1_.useCapture);
         }
      }
      
      private function setImg(param1:String) : void
      {
         _skillSprite = MyFunction2.returnShowByClassName("UI.Skills." + param1) as Sprite;
         if(_skillSprite == null)
         {
            trace("没有装备显示！");
            return;
         }
         while(numChildren > 1)
         {
            removeChildAt(numChildren - 1);
         }
         addChild(_skillSprite);
      }
      
      public function get skillVO() : SkillVO
      {
         return _skillVO;
      }
      
      public function set skillVO(param1:SkillVO) : void
      {
         _skillVO = param1;
         if(param1)
         {
            setImg(param1.className);
            setSkillActive(_skillVO.level,_skillVO.isActive);
         }
      }
      
      public function get skillSprite() : Sprite
      {
         return _skillSprite;
      }
      
      public function imperfectClone() : Skill
      {
         return new Skill(skillVO);
      }
      
      public function clone() : Skill
      {
         return new Skill(skillVO.clone());
      }
      
      public function setSkillActive2(param1:int) : void
      {
         _skillVO.isActive = Boolean(param1);
         setSkillActive(_skillVO.level,_skillVO.isActive);
      }
      
      protected function setSkillActive(param1:int, param2:Boolean) : void
      {
         if(!param1)
         {
            setNoActive();
         }
         else
         {
            setActive();
         }
         if(param1)
         {
            if(!param2)
            {
               setNoActive();
            }
            else
            {
               setActive();
            }
         }
      }
      
      protected function setNoActive() : void
      {
         MyFunction.getInstance().changeSaturation(this,-100);
      }
      
      protected function setActive() : void
      {
         MyFunction.getInstance().changeSaturation(this,0);
      }
   }
}

