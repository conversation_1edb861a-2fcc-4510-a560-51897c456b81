package UI.SocietySystem.SocietyListPanel
{
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.DOWN_TheSocietyListOfApplyReturn;
   import UI.SocietySystem.SeverLink.InformationBodyDetails.SocietyDataInList;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.ShowLogicShell.MovieClipPlayLogicShell.MovieClipPlayLogicShell;
   import YJFY.Utils.ClearUtil;
   import flash.display.InteractiveObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SocietyColume extends ButtonLogicShell2
   {
      
      public static const s_const_applyAndLookUpBtn:String = "applyAndLookUpBtn";
      
      public static const s_const_cancelApplyAndLookUpBtn:String = "cancelApplyAndLookUpBtn";
      
      private var m_rank:int;
      
      private var m_rankOfSocietyText:TextField;
      
      private var m_nameOfSocietyText:TextField;
      
      private var m_leaderNameOfSocietyText:TextField;
      
      private var m_levelOfSocietyText:TextField;
      
      private var m_memberNumOfSocietyText:TextField;
      
      private var m_btnGroup:MovieClipPlayLogicShell;
      
      private var m_lookUpBtn:ButtonLogicShell2;
      
      private var m_applyBtn:ButtonLogicShell2;
      
      private var m_cancelApplyBtn:ButtonLogicShell2;
      
      private var m_type1:String;
      
      private var m_type2:String;
      
      private var m_font:FangZhengKaTongJianTi;
      
      private var m_rankIconShow:MovieClipPlayLogicShell;
      
      private var m_societyDataInList:SocietyDataInList;
      
      public function SocietyColume()
      {
         super();
         m_font = new FangZhengKaTongJianTi();
      }
      
      override public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         super.clear();
         m_rankOfSocietyText = null;
         m_nameOfSocietyText = null;
         m_leaderNameOfSocietyText = null;
         m_levelOfSocietyText = null;
         m_memberNumOfSocietyText = null;
         ClearUtil.clearObject(m_btnGroup);
         m_btnGroup = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
         ClearUtil.clearObject(m_applyBtn);
         m_applyBtn = null;
         ClearUtil.clearObject(m_cancelApplyBtn);
         m_cancelApplyBtn = null;
         m_font = null;
         ClearUtil.clearObject(m_rankIconShow);
         m_rankIconShow = null;
         m_societyDataInList = null;
      }
      
      override public function setShow(param1:Sprite) : void
      {
         super.setShow(param1);
         m_show.addEventListener("clickButton",clickButton,true,0,true);
      }
      
      public function setType1(param1:String) : void
      {
         if(param1 != "applyJoinSociety" && param1 != "createSociety" && param1 != "lookUpSocietyList")
         {
            throw new Error();
         }
         m_type1 = param1;
      }
      
      public function setType2(param1:String) : void
      {
         if(m_type1 != "applyJoinSociety")
         {
            return;
         }
         if(param1 != "applyAndLookUpBtn" && param1 != "cancelApplyAndLookUpBtn")
         {
            throw new Error();
         }
         m_type2 = param1;
         initShow();
         initShow2();
      }
      
      public function setSocietyDataInList(param1:SocietyDataInList, param2:int, param3:DOWN_TheSocietyListOfApplyReturn) : void
      {
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         m_societyDataInList = param1;
         m_rank = param2;
         if(m_type1 == "applyJoinSociety")
         {
            m_type2 = "applyAndLookUpBtn";
            _loc4_ = param3 ? param3.getSocietyNumOfApply() : 0;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               if(param3.getSocietyIdByIndex(_loc5_) == param1.getSocietyId())
               {
                  m_type2 = "cancelApplyAndLookUpBtn";
               }
               _loc5_++;
            }
         }
         initShow();
         initShow2();
      }
      
      private function initShow() : void
      {
         m_rankOfSocietyText = m_show["rankOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_rankOfSocietyText);
         m_rankOfSocietyText.mouseEnabled = false;
         m_nameOfSocietyText = m_show["nameOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_nameOfSocietyText);
         m_nameOfSocietyText.mouseEnabled = false;
         m_leaderNameOfSocietyText = m_show["leaderNameOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_leaderNameOfSocietyText);
         m_leaderNameOfSocietyText.mouseEnabled = false;
         m_levelOfSocietyText = m_show["levelOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_levelOfSocietyText);
         m_levelOfSocietyText.mouseEnabled = false;
         m_memberNumOfSocietyText = m_show["memberNumOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_memberNumOfSocietyText);
         m_memberNumOfSocietyText.mouseEnabled = false;
         m_btnGroup = new MovieClipPlayLogicShell();
         m_btnGroup.setShow(m_show["btnGroup"]);
         m_rankIconShow = new MovieClipPlayLogicShell();
         m_rankIconShow.setShow(m_show["rankIconShow"]);
         clearBtn();
         if(m_type2 == "applyAndLookUpBtn")
         {
            m_btnGroup.gotoAndStop("applyAndLookUpBtn");
            m_applyBtn = new ButtonLogicShell2();
            m_applyBtn.setShow(m_btnGroup.getShow()["applyBtn"]);
            m_applyBtn.setTipString("申请加入该帮会");
         }
         else if(m_type2 == "cancelApplyAndLookUpBtn")
         {
            m_btnGroup.gotoAndStop("cancelApplyAndLookUpBtn");
            m_cancelApplyBtn = new ButtonLogicShell2();
            m_cancelApplyBtn.setShow(m_btnGroup.getShow()["cancelApplyBtn"]);
            m_cancelApplyBtn.setTipString("取消申请加入");
         }
         else
         {
            m_btnGroup.gotoAndStop("onlyLookUpBtn");
         }
         m_lookUpBtn = new ButtonLogicShell2();
         m_lookUpBtn.setShow(m_btnGroup.getShow()["lookUpBtn"]);
         m_lookUpBtn.setTipString("点击查看该帮会成员信息");
      }
      
      private function clearBtn() : void
      {
         ClearUtil.clearObject(m_applyBtn);
         m_applyBtn = null;
         ClearUtil.clearObject(m_cancelApplyBtn);
         m_cancelApplyBtn = null;
         ClearUtil.clearObject(m_lookUpBtn);
         m_lookUpBtn = null;
      }
      
      private function initShow2() : void
      {
         if(m_societyDataInList == null)
         {
            return;
         }
         m_rankOfSocietyText.text = m_rank.toString();
         m_nameOfSocietyText.text = m_societyDataInList.getSocietyName() ? m_societyDataInList.getSocietyName() : "";
         m_leaderNameOfSocietyText.text = m_societyDataInList.getName_Leader() ? m_societyDataInList.getName_Leader() : m_societyDataInList.getUid_Leader().toString();
         m_levelOfSocietyText.text = m_societyDataInList.getSocietyLevel().toString();
         m_memberNumOfSocietyText.text = m_societyDataInList.getPlayerNumInSociety() + "/" + m_societyDataInList.getMaxPlayerNumInSociety();
         if(m_rank > 3)
         {
            m_rankIconShow.gotoAndStop("no");
         }
         else if(m_rank == 1)
         {
            m_rankIconShow.gotoAndStop("one");
         }
         else if(m_rank == 2)
         {
            m_rankIconShow.gotoAndStop("two");
         }
         else
         {
            if(m_rank != 3)
            {
               throw new Error();
            }
            m_rankIconShow.gotoAndStop("three");
         }
      }
      
      override public function getShow() : InteractiveObject
      {
         return m_show;
      }
      
      public function getSocietyData() : SocietyDataInList
      {
         return m_societyDataInList;
      }
      
      public function getApplyBtn() : ButtonLogicShell2
      {
         return m_applyBtn;
      }
      
      public function getLookUpBtn() : ButtonLogicShell2
      {
         return m_lookUpBtn;
      }
      
      public function getCancelApplyBtn() : ButtonLogicShell2
      {
         return m_cancelApplyBtn;
      }
      
      override protected function onDown(param1:MouseEvent) : void
      {
         super.onDown(param1);
         changeTextField();
      }
      
      override protected function onUp(param1:MouseEvent) : void
      {
         super.onUp(param1);
         changeTextField();
      }
      
      override protected function onOut(param1:MouseEvent) : void
      {
         super.onOut(param1);
         changeTextField();
      }
      
      override protected function onOver(param1:MouseEvent) : void
      {
         super.onOver(param1);
         changeTextField();
      }
      
      private function changeTextField() : void
      {
         var _loc1_:String = m_rankOfSocietyText.text;
         m_rankOfSocietyText = m_show["rankOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_rankOfSocietyText);
         m_rankOfSocietyText.mouseEnabled = false;
         m_rankOfSocietyText.text = _loc1_;
         _loc1_ = m_nameOfSocietyText.text;
         m_nameOfSocietyText = m_show["nameOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_nameOfSocietyText);
         m_nameOfSocietyText.mouseEnabled = false;
         m_nameOfSocietyText.text = _loc1_;
         _loc1_ = m_leaderNameOfSocietyText.text;
         m_leaderNameOfSocietyText = m_show["leaderNameOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_leaderNameOfSocietyText);
         m_leaderNameOfSocietyText.mouseEnabled = false;
         m_leaderNameOfSocietyText.text = _loc1_;
         _loc1_ = m_levelOfSocietyText.text;
         m_levelOfSocietyText = m_show["levelOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_levelOfSocietyText);
         m_levelOfSocietyText.mouseEnabled = false;
         m_levelOfSocietyText.text = _loc1_;
         _loc1_ = m_memberNumOfSocietyText.text;
         m_memberNumOfSocietyText = m_show["memberNumOfSocietyText"];
         MyFunction2.changeTextFieldFont(m_font.fontName,m_memberNumOfSocietyText);
         m_memberNumOfSocietyText.mouseEnabled = false;
         m_memberNumOfSocietyText.text = _loc1_;
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
      }
   }
}

