package YJFY.PKMode2.PKUI.PKPanel
{
   import YJFY.PKMode2.PK2;
   import YJFY.PKMode2.PKMode2VO;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class PKHistoryShow
   {
      
      private var m_historyRecordRows:Vector.<HistoryRecordRow>;
      
      private var m_show:MovieClip;
      
      private var m_pk:PK2;
      
      private var m_pkMode2VO:PKMode2VO;
      
      public function PKHistoryShow()
      {
         super();
         m_historyRecordRows = new Vector.<HistoryRecordRow>();
      }
      
      public function clear() : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_historyRecordRows);
         m_historyRecordRows = null;
         m_show = null;
         m_pk = null;
         m_pkMode2VO = null;
      }
      
      public function init(param1:MovieClip, param2:PK2) : void
      {
         m_pk = param2;
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true);
         }
         initShow();
      }
      
      public function setPKTargetPlayerDatas(param1:PKMode2VO) : void
      {
         var _loc3_:int = 0;
         historyRecordRowsClear();
         m_pkMode2VO = param1;
         var _loc2_:int = int(m_pkMode2VO.getPKTargetPlayerDataNum());
         _loc2_ = Math.min(_loc2_,m_historyRecordRows.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            m_historyRecordRows[_loc3_].setPKTargetPlayerData(m_pkMode2VO.getPKTargetPlayerDataByIndex(_loc3_));
            m_historyRecordRows[_loc3_].getShow().visible = true;
            _loc3_++;
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = int(m_historyRecordRows.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.button == m_historyRecordRows[_loc3_].getLookUpBtn())
            {
               m_pk.lookUpPlayerInfor(m_historyRecordRows[_loc3_].getPKTargetPlayerData().getUid(),m_historyRecordRows[_loc3_].getPKTargetPlayerData().getIndexOfSaveXML());
               return;
            }
            _loc3_++;
         }
      }
      
      private function initShow() : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:DisplayObject = null;
         var _loc4_:int = 0;
         var _loc2_:HistoryRecordRow = null;
         _loc3_ = m_show.numChildren;
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc1_ = m_show.getChildAt(_loc5_);
            if(_loc1_.name.substr(0,"historyRecordRow".length) == "historyRecordRow")
            {
               _loc4_++;
            }
            _loc5_++;
         }
         ClearUtil.clearObject(m_historyRecordRows);
         m_historyRecordRows.length = 0;
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            _loc2_ = new HistoryRecordRow();
            _loc2_.setShow(m_show["historyRecordRow" + (_loc5_ + 1)]);
            m_historyRecordRows.push(_loc2_);
            _loc5_++;
         }
      }
      
      private function historyRecordRowsClear() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(m_historyRecordRows.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            m_historyRecordRows[_loc2_].setPKTargetPlayerData(null);
            m_historyRecordRows[_loc2_].getShow().visible = false;
            _loc2_++;
         }
      }
   }
}

