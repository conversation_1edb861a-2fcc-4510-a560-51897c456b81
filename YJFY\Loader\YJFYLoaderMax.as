package YJFY.Loader
{
   import YJFY.Utils.ClearUtil;
   import com.greensock.events.LoaderEvent;
   import com.greensock.loading.LoaderMax;
   import flash.events.Event;
   
   public class YJFYLoaderMax extends LoaderMax
   {
      
      protected var _progressShow:IProgressShow;
      
      public function YJFYLoaderMax(param1:Object = null, param2:IProgressShow = null)
      {
         _progressShow = param2;
         super(param1);
      }
      
      override protected function _progressHandler(param1:Event) : void
      {
         var _loc2_:int = 0;
         var _loc3_:* = 0;
         var _loc4_:* = 0;
         if(_dispatchProgress && _status != 5)
         {
            _loc3_ = _cachedBytesLoaded;
            _loc4_ = _cachedBytesTotal;
            _calculateProgress();
            if(!(_loc3_ == 0 && _cachedBytesLoaded == 0))
            {
               if(_loc3_ != _cachedBytesLoaded || _loc4_ != _cachedBytesTotal)
               {
                  dispatchEvent(new LoaderEvent("progress",this));
                  _loc2_ = Math.round(rawProgress * numChildren);
                  if(_progressShow)
                  {
                     _progressShow.setProgress(progress,bytesLoaded,bytesTotal,loadTime,_loc2_,numChildren);
                  }
               }
            }
         }
         else
         {
            _cacheIsDirty = true;
         }
         if(_dispatchChildProgress && param1 != null)
         {
            dispatchEvent(new LoaderEvent("childProgress",param1.target));
         }
      }
      
      override protected function _errorHandler(param1:Event) : void
      {
         super._errorHandler(param1);
         if(_progressShow)
         {
            _progressShow.error();
         }
      }
      
      override public function load(param1:Boolean = false) : void
      {
         super.load(param1);
         if(_status == 1)
         {
            if(_progressShow)
            {
               _progressShow.startLoad();
            }
         }
      }
      
      override protected function _completeHandler(param1:Event = null) : void
      {
         super._completeHandler(param1);
         if(_progressShow)
         {
            _progressShow.completeLoad();
         }
      }
      
      public function clear(param1:Boolean = true, param2:Boolean = true) : void
      {
         dispose(true);
         empty(param1,param2);
         ClearUtil.nullArr(_loaders);
         _loaders = null;
         ClearUtil.nullObject(_activeLoaders);
         _rootLoader = null;
      }
      
      override public function empty(param1:Boolean = true, param2:Boolean = false) : void
      {
         super.empty(param1,param2);
         _progressShow = null;
      }
      
      override public function dispose(param1:Boolean = false) : void
      {
         super.dispose(param1);
         _progressShow = null;
      }
      
      public function set progressShow(param1:IProgressShow) : void
      {
         _progressShow = param1;
      }
      
      public function get progressShow() : IProgressShow
      {
         return _progressShow;
      }
   }
}

