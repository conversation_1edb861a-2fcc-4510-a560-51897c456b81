package UI.WorldBoss.<PERSON><PERSON>hao
{
   import UI.WorldBoss.Boss.Boss;
   import UI.WorldBoss.DaZhao.ChangeHurtDaZhao.RoundHurtMultiDaZhao;
   import UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao;
   import YJFY.StepAttackGame.StepAttackGameWorld;
   import YJFY.Utils.ClearUtil;
   import flash.system.ApplicationDomain;
   
   public class DaZhaoFactory
   {
      
      private var m_classes:Array;
      
      public function DaZhaoFactory()
      {
         super();
         m_classes = [DaZ<PERSON>,DaZhaoFactory,RoundHurtMultiDaZhao,CreateOwnImageDaZhao];
      }
      
      public function clear() : void
      {
         ClearUtil.nullArr(m_classes);
         m_classes = null;
      }
      
      public function create(param1:String) : <PERSON><PERSON><PERSON>
      {
         var _loc3_:Class = ApplicationDomain.currentDomain.getDefinition(param1) as Class;
         return new _loc3_();
      }
      
      public function createByXML(param1:XML) : <PERSON><PERSON><PERSON>
      {
         var _loc3_:String = String(param1.@className);
         var _loc2_:Da<PERSON><PERSON> = create(_loc3_);
         _loc2_.initByXML(param1);
         return _loc2_;
      }
      
      public function createDaZhaosByXML(param1:XMLList, param2:StepAttackGameWorld, param3:Boss) : Vector.<DaZhao>
      {
         var _loc7_:int = 0;
         var _loc4_:DaZhao = null;
         var _loc6_:int = int(param1 ? param1.length() : 0);
         var _loc5_:Vector.<DaZhao> = new Vector.<DaZhao>();
         _loc7_ = 0;
         while(_loc7_ < _loc6_)
         {
            _loc4_ = createByXML(param1[_loc7_]);
            _loc4_.setBoss(param3);
            _loc4_.setWorld(param2);
            _loc5_.push(_loc4_);
            _loc7_++;
         }
         return _loc5_;
      }
   }
}

