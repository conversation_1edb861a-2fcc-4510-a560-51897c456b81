package YJFY.PKMode2.PKUI.PKPanel
{
   import UI.GamingUI;
   import UI.InitPlayerData.GetPlayerDataListener;
   import UI.InitPlayerData.InitPlayersData;
   import UI.InitPlayerData.PlayerDatas;
   import UI.LogicShell.ButtonLogicShell2;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import YJFY.API_4399.RankListAPI.RankListAPIListener;
   import YJFY.API_4399.RankListAPI.UserDataInRankList;
   import YJFY.EntityShowContainer;
   import YJFY.PKMode2.PK2;
   import YJFY.ShowLogicShell.ButtonEvent;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class TopPlayersShow
   {
      
      private const m_const_playerNum:uint = 3;
      
      private var m_FirstPlayerDatas:PlayerDatas;
      
      private var m_secondPlayerDatas:PlayerDatas;
      
      private var m_thirdPlayerDatas:PlayerDatas;
      
      private var m_firstPlayerData:InitPlayersData;
      
      private var m_secondPlayerData:InitPlayersData;
      
      private var m_thirdPlayerData:InitPlayersData;
      
      private var m_firstPlayer1Show:EntityShowContainer;
      
      private var m_firstPlayer2Show:EntityShowContainer;
      
      private var m_secondPlayer1Show:EntityShowContainer;
      
      private var m_secondPlayer2Show:EntityShowContainer;
      
      private var m_thirdPlayer1Show:EntityShowContainer;
      
      private var m_thirdPlayer2Show:EntityShowContainer;
      
      private var m_firstPlayerNameText:TextField;
      
      private var m_secondPlayerNameText:TextField;
      
      private var m_thirdPlayerNameText:TextField;
      
      private var m_firstPlayerLookUpBtn:ButtonLogicShell2;
      
      private var m_secondPlayerLookUpBtn:ButtonLogicShell2;
      
      private var m_thirdPlayerLookUpBtn:ButtonLogicShell2;
      
      private var m_getPlayerDataListener:GetPlayerDataListener;
      
      private var m_rankListAPIListener:RankListAPIListener;
      
      private var m_userDataInRankList1:UserDataInRankList;
      
      private var m_userDataInRankList2:UserDataInRankList;
      
      private var m_userDataInRankList3:UserDataInRankList;
      
      private var m_rankListId:uint;
      
      private var m_firstPlayerShowContainer:MovieClip;
      
      private var m_secondPlayerShowContainer:MovieClip;
      
      private var m_thirdPlayerShowContainer:MovieClip;
      
      private var m_show:MovieClip;
      
      private var m_PK:PK2;
      
      private var m_pkType:String;
      
      private var m_isInit:Boolean;
      
      public function TopPlayersShow()
      {
         super();
         m_FirstPlayerDatas = new PlayerDatas();
         m_secondPlayerDatas = new PlayerDatas();
         m_thirdPlayerDatas = new PlayerDatas();
         m_getPlayerDataListener = new GetPlayerDataListener();
         m_getPlayerDataListener.getPlayerDataFun2 = getPlayerData;
         m_FirstPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_secondPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_thirdPlayerDatas.addGetPlayerDataListener(m_getPlayerDataListener);
         m_FirstPlayerDatas.init();
         m_secondPlayerDatas.init();
         m_thirdPlayerDatas.init();
         m_rankListAPIListener = new RankListAPIListener();
         m_rankListAPIListener.rankListErrorFun = ranklistError;
         m_rankListAPIListener.getRankListsDataFun = getRankListsData;
         m_firstPlayer1Show = new EntityShowContainer();
         m_firstPlayer1Show.init();
         m_firstPlayer1Show.getShow().scaleX = m_firstPlayer1Show.getShow().scaleY = 1.8;
         m_firstPlayer2Show = new EntityShowContainer();
         m_firstPlayer2Show.init();
         m_firstPlayer2Show.getShow().scaleX = m_firstPlayer2Show.getShow().scaleY = 1.8;
         m_secondPlayer1Show = new EntityShowContainer();
         m_secondPlayer1Show.init();
         m_secondPlayer1Show.getShow().scaleX = m_secondPlayer1Show.getShow().scaleY = 1.8;
         m_secondPlayer2Show = new EntityShowContainer();
         m_secondPlayer2Show.init();
         m_secondPlayer2Show.getShow().scaleX = m_secondPlayer2Show.getShow().scaleY = 1.8;
         m_thirdPlayer1Show = new EntityShowContainer();
         m_thirdPlayer1Show.init();
         m_thirdPlayer1Show.getShow().scaleX = m_thirdPlayer1Show.getShow().scaleY = 1.8;
         m_thirdPlayer2Show = new EntityShowContainer();
         m_thirdPlayer2Show.init();
         m_thirdPlayer2Show.getShow().scaleX = m_thirdPlayer2Show.getShow().scaleY = 1.8;
      }
      
      public function clear() : void
      {
         if(m_rankListAPIListener)
         {
            GamingUI.getInstance().getAPI4399().rankListAPI.removeRankListAPIListener(m_rankListAPIListener);
         }
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         ClearUtil.clearObject(m_FirstPlayerDatas);
         m_FirstPlayerDatas = null;
         ClearUtil.clearObject(m_secondPlayerDatas);
         m_secondPlayerDatas = null;
         ClearUtil.clearObject(m_thirdPlayerDatas);
         m_thirdPlayerDatas = null;
         ClearUtil.clearObject(m_firstPlayerData);
         m_firstPlayerData = null;
         ClearUtil.clearObject(m_secondPlayerData);
         m_secondPlayerData = null;
         ClearUtil.clearObject(m_thirdPlayerData);
         m_thirdPlayerData = null;
         ClearUtil.clearObject(m_firstPlayer1Show);
         m_firstPlayer1Show = null;
         ClearUtil.clearObject(m_firstPlayer2Show);
         m_firstPlayer2Show = null;
         ClearUtil.clearObject(m_secondPlayer1Show);
         m_secondPlayer1Show = null;
         ClearUtil.clearObject(m_secondPlayer2Show);
         m_secondPlayer2Show = null;
         ClearUtil.clearObject(m_thirdPlayer1Show);
         m_thirdPlayer1Show = null;
         ClearUtil.clearObject(m_thirdPlayer2Show);
         m_thirdPlayer2Show = null;
         m_firstPlayerNameText = null;
         m_secondPlayerNameText = null;
         m_thirdPlayerNameText = null;
         ClearUtil.clearObject(m_firstPlayerLookUpBtn);
         m_firstPlayerLookUpBtn = null;
         ClearUtil.clearObject(m_secondPlayerLookUpBtn);
         m_secondPlayerLookUpBtn = null;
         ClearUtil.clearObject(m_thirdPlayerLookUpBtn);
         m_thirdPlayerLookUpBtn = null;
         ClearUtil.clearObject(m_getPlayerDataListener);
         m_getPlayerDataListener = null;
         ClearUtil.clearObject(m_rankListAPIListener);
         m_rankListAPIListener = null;
         ClearUtil.clearObject(m_userDataInRankList1);
         m_userDataInRankList1 = null;
         ClearUtil.clearObject(m_userDataInRankList2);
         m_userDataInRankList2 = null;
         ClearUtil.clearObject(m_userDataInRankList3);
         m_userDataInRankList3 = null;
         m_firstPlayerShowContainer = null;
         m_secondPlayerShowContainer = null;
         m_thirdPlayerShowContainer = null;
         m_show = null;
         m_PK = null;
         m_pkType = null;
      }
      
      public function init(param1:MovieClip, param2:PK2, param3:uint, param4:String) : void
      {
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = param1;
         if(m_show)
         {
            m_show.addEventListener("clickButton",clickButton,true,0,true);
         }
         m_PK = param2;
         m_rankListId = param3;
         m_pkType = param4;
         initShow();
         initShow2();
      }
      
      public function clearPlayerShow() : void
      {
         if(m_firstPlayer1Show.getShow().parent)
         {
            m_firstPlayer1Show.getShow().parent.removeChild(m_firstPlayer1Show.getShow());
         }
         if(m_firstPlayer2Show.getShow().parent)
         {
            m_firstPlayer2Show.getShow().parent.removeChild(m_firstPlayer2Show.getShow());
         }
         if(m_secondPlayer1Show.getShow().parent)
         {
            m_secondPlayer1Show.getShow().parent.removeChild(m_secondPlayer1Show.getShow());
         }
         if(m_secondPlayer2Show.getShow().parent)
         {
            m_secondPlayer2Show.getShow().parent.removeChild(m_secondPlayer2Show.getShow());
         }
         if(m_thirdPlayer1Show.getShow().parent)
         {
            m_thirdPlayer1Show.getShow().parent.removeChild(m_thirdPlayer1Show.getShow());
         }
         if(m_thirdPlayer2Show.getShow().parent)
         {
            m_thirdPlayer2Show.getShow().parent.removeChild(m_thirdPlayer2Show.getShow());
         }
         m_firstPlayerShowContainer = null;
         m_secondPlayerShowContainer = null;
         m_thirdPlayerShowContainer = null;
         ClearUtil.clearObject(m_firstPlayerLookUpBtn);
         m_firstPlayerLookUpBtn = null;
         ClearUtil.clearObject(m_secondPlayerLookUpBtn);
         m_secondPlayerLookUpBtn = null;
         ClearUtil.clearObject(m_thirdPlayerLookUpBtn);
         m_thirdPlayerLookUpBtn = null;
         m_firstPlayerNameText = null;
         m_secondPlayerNameText = null;
         m_thirdPlayerNameText = null;
         if(m_show)
         {
            m_show.removeEventListener("clickButton",clickButton,true);
         }
         m_show = null;
      }
      
      private function initShow() : void
      {
         m_firstPlayerShowContainer = m_show["showContainer1"];
         m_secondPlayerShowContainer = m_show["showContainer2"];
         m_thirdPlayerShowContainer = m_show["showContainer3"];
         var _loc1_:FangZhengKaTongJianTi = new FangZhengKaTongJianTi();
         m_firstPlayerNameText = m_show["nameText1"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_firstPlayerNameText);
         m_secondPlayerNameText = m_show["nameText2"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_secondPlayerNameText);
         m_thirdPlayerNameText = m_show["nameText3"];
         MyFunction2.changeTextFieldFont(_loc1_.fontName,m_thirdPlayerNameText);
         m_firstPlayerLookUpBtn = new ButtonLogicShell2();
         m_secondPlayerLookUpBtn = new ButtonLogicShell2();
         m_thirdPlayerLookUpBtn = new ButtonLogicShell2();
         m_firstPlayerLookUpBtn.setShow(m_show["lookUpBtn1"]);
         m_secondPlayerLookUpBtn.setShow(m_show["lookUpBtn2"]);
         m_thirdPlayerLookUpBtn.setShow(m_show["lookUpBtn3"]);
      }
      
      private function initShow2() : void
      {
         if(m_isInit == false)
         {
            m_firstPlayerData = null;
            m_secondPlayerData = null;
            m_thirdPlayerData = null;
            getTopPlayersDataFromRankList();
            m_isInit = true;
         }
         else
         {
            initPlayerShow(m_firstPlayerData);
            initPlayerShow(m_secondPlayerData);
            initPlayerShow(m_thirdPlayerData);
         }
      }
      
      private function getTopPlayersDataFromRankList() : void
      {
         m_PK.showGameWaitShow();
         GamingUI.getInstance().getAPI4399().rankListAPI.addRankListAPIListener(m_rankListAPIListener);
         GamingUI.getInstance().getAPI4399().rankListAPI.getRankListsData(m_rankListId,3,1);
         trace("获取前三名玩家数据， ranListId：",m_rankListId,"人数:",3);
      }
      
      private function getRankListsData(param1:Vector.<UserDataInRankList>) : void
      {
         if(m_show == null)
         {
            return;
         }
         ClearUtil.clearObject(m_userDataInRankList1);
         m_userDataInRankList1 = null;
         ClearUtil.clearObject(m_userDataInRankList2);
         m_userDataInRankList2 = null;
         ClearUtil.clearObject(m_userDataInRankList3);
         m_userDataInRankList3 = null;
         if(Boolean(param1.length) && param1[0])
         {
            m_userDataInRankList1 = param1[0];
         }
         if(param1.length > 0 && param1[1])
         {
            m_userDataInRankList2 = param1[1];
         }
         if(param1.length > 1 && param1[2])
         {
            m_userDataInRankList3 = param1[2];
         }
         ClearUtil.nullArr(param1,false,false,false);
         param1 = null;
         m_PK.hideGameWaitShow();
         if(m_userDataInRankList1)
         {
            m_FirstPlayerDatas.getPlayerData(m_userDataInRankList1.getUid(),m_userDataInRankList1.getIndex(),1 | 8 | 0x10 | 0x40 | 0x0200 | 0x80 | 0x0800);
            m_PK.showGameWaitShow();
         }
      }
      
      private function getPlayerData(param1:InitPlayersData, param2:PlayerDatas) : void
      {
         if(m_show == null)
         {
            return;
         }
         m_PK.hideGameWaitShow();
         if(param2 == m_FirstPlayerDatas)
         {
            m_firstPlayerData = param1;
            if(m_userDataInRankList2)
            {
               m_PK.showGameWaitShow();
               m_secondPlayerDatas.getPlayerData(m_userDataInRankList2.getUid(),m_userDataInRankList2.getIndex(),1 | 8 | 0x10 | 0x40 | 0x0200 | 0x80 | 0x0800);
            }
         }
         else if(param2 == m_secondPlayerDatas)
         {
            m_secondPlayerData = param1;
            if(m_userDataInRankList3)
            {
               m_PK.showGameWaitShow();
               m_thirdPlayerDatas.getPlayerData(m_userDataInRankList3.getUid(),m_userDataInRankList3.getIndex(),1 | 8 | 0x10 | 0x40 | 0x0200 | 0x80 | 0x0800);
            }
         }
         else
         {
            if(param2 != m_thirdPlayerDatas)
            {
               return;
            }
            m_thirdPlayerData = param1;
         }
         initPlayerShow(param1);
      }
      
      private function initPlayerShow(param1:InitPlayersData) : void
      {
         var _loc3_:EntityShowContainer = null;
         var _loc5_:EntityShowContainer = null;
         var _loc2_:Sprite = null;
         var _loc4_:TextField = null;
         if(param1 == null)
         {
            return;
         }
         switch(param1)
         {
            case m_firstPlayerData:
               _loc3_ = m_firstPlayer1Show;
               _loc5_ = m_firstPlayer2Show;
               _loc2_ = m_firstPlayerShowContainer;
               _loc4_ = m_firstPlayerNameText;
               break;
            case m_secondPlayerData:
               _loc3_ = m_secondPlayer1Show;
               _loc5_ = m_secondPlayer2Show;
               _loc2_ = m_secondPlayerShowContainer;
               _loc4_ = m_secondPlayerNameText;
               break;
            case m_thirdPlayerData:
               _loc3_ = m_thirdPlayer1Show;
               _loc5_ = m_thirdPlayer2Show;
               _loc2_ = m_thirdPlayerShowContainer;
               _loc4_ = m_thirdPlayerNameText;
         }
         _loc3_.refreshPlayerShow(param1.player1.playerVO);
         _loc2_.addChild(_loc3_.getShow());
         _loc4_.text = Boolean(param1.nickNameData) && Boolean(param1.nickNameData.extra) ? String(param1.nickNameData.extra) : param1.uid;
         if(m_pkType == "twoPK")
         {
            _loc5_.refreshPlayerShow(param1.player2.playerVO);
            _loc2_.addChild(_loc5_.getShow());
         }
         else if(_loc5_.getShow().parent)
         {
            _loc5_.getShow().parent.removeChild(_loc5_.getShow());
         }
      }
      
      private function clickButton(param1:ButtonEvent) : void
      {
         switch(param1.button)
         {
            case m_firstPlayerLookUpBtn:
               if(m_firstPlayerData)
               {
                  m_PK.lookUpPlayerInfor(m_firstPlayerData.uid,m_firstPlayerData.idx);
               }
               break;
            case m_secondPlayerLookUpBtn:
               if(m_secondPlayerData)
               {
                  m_PK.lookUpPlayerInfor(m_secondPlayerData.uid,m_secondPlayerData.idx);
               }
               break;
            case m_thirdPlayerLookUpBtn:
               if(m_thirdPlayerData)
               {
                  m_PK.lookUpPlayerInfor(m_thirdPlayerData.uid,m_thirdPlayerData.idx);
               }
         }
      }
      
      private function ranklistError(param1:String) : void
      {
      }
   }
}

