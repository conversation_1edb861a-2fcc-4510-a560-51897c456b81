package YJFY.PKMode
{
   import UI.LogicShell.ButtonLogicShell;
   import UI.MyFont.FangZhengKaTongJianTi;
   import UI.MyFunction2;
   import UI.MySprite;
   import YJFY.Utils.ClearUtil;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class VipResetPopBox extends MySprite
   {
      
      private var m_show:MovieClip;
      
      private var m_remaineResetNumText:TextField;
      
      private var m_sureBtn:ButtonLogicShell;
      
      private var m_cancelBtn:ButtonLogicShell;
      
      public function VipResetPopBox()
      {
         super();
         m_sureBtn = new ButtonLogicShell();
         m_cancelBtn = new ButtonLogicShell();
      }
      
      override public function clear() : void
      {
         ClearUtil.clearObject(m_show);
         m_show = null;
         m_remaineResetNumText = null;
         ClearUtil.clearObject(m_sureBtn);
         m_sureBtn = null;
         ClearUtil.clearObject(m_cancelBtn);
         m_cancelBtn = null;
         super.clear();
      }
      
      public function init() : void
      {
         m_show = MyFunction2.returnShowByClassName("ResetPKTipVip") as MovieClip;
         addChild(m_show);
         m_remaineResetNumText = m_show["reSetTime"];
         MyFunction2.changeTextFieldFont(new FangZhengKaTongJianTi().fontName,m_remaineResetNumText);
         m_sureBtn.setShow(m_show["yesBtn"]);
         m_cancelBtn.setShow(m_show["noBtn"]);
      }
      
      public function setData(param1:uint) : void
      {
         m_remaineResetNumText.text = param1.toString();
      }
      
      public function getSureBtn() : ButtonLogicShell
      {
         return m_sureBtn;
      }
      
      public function getCancelBtn() : ButtonLogicShell
      {
         return m_cancelBtn;
      }
   }
}

