package UI.SmallPackage.Button
{
   import UI.Button.SwitchBtn.SwitchBtn;
   import UI.Event.UIBtnEvent;
   
   public class SmallPackageBtn_SwitchToPlayer2 extends SwitchBtn
   {
      
      public function SmallPackageBtn_SwitchToPlayer2()
      {
         super();
      }
      
      override protected function dispatchUIBtnEvent() : void
      {
         dispatchEvent(new UIBtnEvent("switchPlayerInSmallPackage"));
      }
   }
}

