package UI.RecaptureGold.Parent
{
   import UI.MyFunction2;
   import UI.MyMovieClip;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.geom.Point;
   import flash.utils.getQualifiedClassName;
   
   public class PutItem extends MyMovieClip implements IPutItem
   {
      
      public function PutItem()
      {
         super();
      }
      
      override public function clear() : void
      {
         stop();
         super.clear();
      }
      
      public function playExplodedAnimtion(param1:XML, param2:DisplayObjectContainer, param3:ICatchTarget) : void
      {
         var _loc7_:Point = param2.globalToLocal((param3 as DisplayObject).localToGlobal(new Point(this.x,this.y)));
         var _loc4_:XML = param1.TargetData[0].item.(@classNameForPut == getQualifiedClassName(this))[0];
         var _loc6_:Class = MyFunction2.returnClassByClassName(_loc4_.@explodedAnimation);
         var _loc5_:MovieClip = new _loc6_();
         _loc5_.x = _loc7_.x;
         _loc5_.y = _loc7_.y;
         param2.addChild(_loc5_);
      }
   }
}

