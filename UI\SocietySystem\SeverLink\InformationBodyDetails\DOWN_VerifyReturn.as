package UI.SocietySystem.SeverLink.InformationBodyDetails
{
   import flash.utils.ByteArray;
   
   public class DOWN_VerifyReturn extends InformationBodyDetail
   {
      
      protected var m_isSuccess:int;
      
      protected var m_session:int;
      
      protected var m_encryValue:int;
      
      public function DOWN_VerifyReturn()
      {
         super();
         m_informationBodyId = 2001;
      }
      
      override public function initFromByteArray(param1:ByteArray) : void
      {
         super.initFromByteArray(param1);
         m_isSuccess = param1.readInt();
         if(m_isSuccess)
         {
            m_session = param1.readInt();
            m_encryValue = param1.readInt();
            trace("收到验证成功的信息：","session:",m_session,"encryValue:",m_encryValue);
         }
      }
      
      public function getIsSuccess() : int
      {
         return m_isSuccess;
      }
      
      public function getSession() : int
      {
         return m_session;
      }
      
      public function getEncryValue() : int
      {
         return m_encryValue;
      }
   }
}

