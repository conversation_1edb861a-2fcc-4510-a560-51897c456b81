package UI.SocietySystem
{
   import UI.DataManagerParent;
   import YJFY.Utils.TimeUtil.TimeUtil;
   
   public class SocietyContriData extends DataManagerParent
   {
      
      private var m_societyContriVO:SocietyContriVO;
      
      private var m_maxNum_contriOfExp:int;
      
      private var m_maxNum_contriOfMoney:int;
      
      private var m_maxNum_contriOFTicket:int;
      
      public function SocietyContriData()
      {
         super();
      }
      
      override public function clear() : void
      {
         super.clear();
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      public function initData(param1:SocietyContriVO, param2:String, param3:XML) : void
      {
         m_societyContriVO = param1;
         if(new TimeUtil().newDateIsNewDay(m_societyContriVO.getTime(),param2))
         {
            m_societyContriVO.reSetData(param2);
         }
         var _loc4_:XML = param3.Contribution[0];
         m_maxNum_contriOfExp = int(_loc4_.contriOfExp[0].@numOfOneDay);
         m_maxNum_contriOfMoney = int(_loc4_.contriOfMoney[0].@numOfOneDay);
         m_maxNum_contriOFTicket = int(_loc4_.contriOfTicket[0].@numOfOneDay);
      }
      
      public function addOneNum_contriOfExp() : void
      {
         if(m_societyContriVO.getNum_contriOfExp() < m_maxNum_contriOfExp)
         {
            m_societyContriVO.addOneNum_contriOfExp();
         }
      }
      
      public function addOneNum_contriOfMoney() : void
      {
         if(m_societyContriVO.getNum_contriOfMoney() < m_maxNum_contriOfMoney)
         {
            m_societyContriVO.addOneNum_contriOfMoney();
         }
      }
      
      public function addOneNum_contriOfTicket() : void
      {
         if(m_societyContriVO.getNum_contriOfTicket() < m_maxNum_contriOFTicket)
         {
            m_societyContriVO.addOneNum_contriOfTicket();
         }
      }
      
      public function decOneNum_contriOfExp() : void
      {
         if(m_societyContriVO.getNum_contriOfExp() > 0)
         {
            m_societyContriVO.decOneNum_contriOfExp();
         }
      }
      
      public function decOneNum_contriOfMoney() : void
      {
         if(m_societyContriVO.getNum_contriOfMoney() > 0)
         {
            m_societyContriVO.decOneNum_contriOfMoney();
         }
      }
      
      public function decOneNum_contriOfTicket() : void
      {
         if(m_societyContriVO.getNum_contriOfTicket() > 0)
         {
            m_societyContriVO.decOneNum_contriOfTicket();
         }
      }
      
      public function getrNum_contriOfExp() : int
      {
         return m_maxNum_contriOfExp - m_societyContriVO.getNum_contriOfExp();
      }
      
      public function getrNum_contriOfMoney() : int
      {
         return m_maxNum_contriOfMoney - m_societyContriVO.getNum_contriOfMoney();
      }
      
      public function getrNum_contriOfTicket() : int
      {
         return m_maxNum_contriOFTicket - m_societyContriVO.getNum_contriOfTicket();
      }
      
      public function getTime() : String
      {
         return m_societyContriVO.getTime();
      }
   }
}

