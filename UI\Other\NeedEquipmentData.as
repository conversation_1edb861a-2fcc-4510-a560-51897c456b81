package UI.Other
{
   import UI.ActivityPanel.EquipmentData;
   
   public class NeedEquipmentData extends EquipmentData
   {
      
      private var m_currNum:uint;
      
      public function NeedEquipmentData(param1:uint, param2:uint, param3:String, param4:uint = 0)
      {
         super(param1,param2,param3);
         this.currentNum = param4;
      }
      
      override protected function init() : void
      {
         super.init();
         _antiwear.currNum = m_currNum;
      }
      
      public function getCurrentNum() : uint
      {
         return currentNum;
      }
      
      public function setCurrentNum(param1:uint) : void
      {
         currentNum = param1;
      }
      
      private function set currentNum(param1:uint) : void
      {
         _antiwear.currNum = param1;
      }
      
      private function get currentNum() : uint
      {
         return _antiwear.currNum;
      }
   }
}

